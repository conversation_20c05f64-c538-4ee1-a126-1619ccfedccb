package ingester

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"

	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	mockUssStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog/mocks"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vgCatalogMocks "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise/mocks"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	daoMocks "github.com/epifi/gamma/securities/catalog/dao/mocks"
	securitiesconf "github.com/epifi/gamma/securities/config"
	secconfig "github.com/epifi/gamma/securities/config/genconf"
)

type mockStruct struct {
	securityDao         *daoMocks.MockSecuritiesDao
	securityListingsDao *daoMocks.MockSecurityListingsDao
	vgCatalogClient     *vgCatalogMocks.MockCatalogClient
	ussCatalogClient    *mockUssStocksCatalogPb.MockCatalogManagerClient
}

func getMockSecurity(time2 time.Time) *securitiesPb.Security {
	return &securitiesPb.Security{
		FinancialInfo: &securitiesPb.FinancialInfo{
			FinancialParameters: &securitiesPb.FinancialParameters{
				FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
					StockFinancialInfo: &securitiesPb.StockFinancialInfo{
						QuarterlyLastUpdatedAt: timestamppb.New(time2),
						YearlyLastUpdatedAt:    timestamppb.New(time2),
					},
				},
			},
		},
	}
}

func TestStockDataIngesterImpl_IngestByPage(t *testing.T) {
	logger.Init(cfg.TestEnv)

	genConf, err := dynconf.LoadConfig(securitiesconf.Load, secconfig.NewConfig, cfg.SECURITIES_SERVICE)
	if err != nil {
		t.Errorf("failed to load securities conf: %v", err)
	}

	tests := []struct {
		name        string
		pageNum     int32
		setup       func(*mockStruct)
		expectedErr error
	}{
		{
			name:    "success_with_valid_page_new_security",
			pageNum: 1,
			setup: func(mocks *mockStruct) {
				// Mock successful GetCompanies response
				companies := &vendorPb.CompaniesData{
					Data: []*vendorPb.CompanyDetails{
						{
							CompanyId:                123,
							CompanyName:              "Test Company",
							CompanyNameShort:         "TEST",
							Website:                  "https://test.com",
							RegionName:               "US",
							IncorporationCountryName: "United States",
							GicsSectorName:           "Information Technology",
							GicsIndustryGroupName:    "Software & Services",
							GicsIndustryName:         "Software",
						},
					},
				}
				resp := &vgCatalogPb.GetCompaniesResponse{
					Status:    rpc.StatusOk(),
					Companies: companies,
				}
				mocks.vgCatalogClient.EXPECT().GetCompanies(gomock.Any(), gomock.Any()).Return(resp, nil)

				// Mock security dao - company doesn't exist, so create new
				mocks.securityDao.EXPECT().GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "123", gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)

				createdSecurity := &securitiesPb.Security{
					Id:               "sec_123",
					VendorSecurityId: "123",
				}
				mocks.securityDao.EXPECT().Create(gomock.Any(), gomock.Any()).Return(createdSecurity, nil)

				// Mock trading items response
				tradingItemsResp := &vgCatalogPb.GetCompanyTradingItemsResponse{
					Status: rpc.StatusOk(),
					AssetDetails: []*vendorPb.AssetDetails{
						{
							TradingItemId:  456,
							TickerSymbol:   "TEST",
							ExchangeSymbol: "NYSE",
							PrimaryFlag:    true,
						},
					},
				}
				mocks.vgCatalogClient.EXPECT().GetCompanyTradingItems(gomock.Any(), gomock.Any()).Return(tradingItemsResp, nil)

				// Mock USS catalog client for US stocks
				ussResp := &ussCatalogPb.GetStocksResponse{
					Status: rpc.StatusRecordNotFound(),
				}
				mocks.ussCatalogClient.EXPECT().GetStocks(gomock.Any(), gomock.Any()).Return(ussResp, nil)

				// Mock batch upsert
				mocks.securityListingsDao.EXPECT().BatchUpsert(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
		{
			name:        "error_max_pages_exceeded",
			pageNum:     1500,
			setup:       func(mocks *mockStruct) {}, // No mocks needed as it fails early
			expectedErr: ErrMaxPagesExceeded,
		},
		{
			name:    "success_with_existing_security",
			pageNum: 1,
			setup: func(mocks *mockStruct) {
				// Mock successful GetCompanies response
				companies := &vendorPb.CompaniesData{
					Data: []*vendorPb.CompanyDetails{
						{
							CompanyId:   123,
							CompanyName: "Existing Company",
						},
					},
				}
				resp := &vgCatalogPb.GetCompaniesResponse{
					Status:    rpc.StatusOk(),
					Companies: companies,
				}
				mocks.vgCatalogClient.EXPECT().GetCompanies(gomock.Any(), gomock.Any()).Return(resp, nil)

				// Mock security dao - company exists
				existingSecurity := &securitiesPb.Security{
					Id:               "sec_123",
					VendorSecurityId: "123",
				}
				mocks.securityDao.EXPECT().GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "123", gomock.Any()).
					Return(existingSecurity, nil)

				// Mock trading items response with Indian exchange
				tradingItemsResp := &vgCatalogPb.GetCompanyTradingItemsResponse{
					Status: rpc.StatusOk(),
					AssetDetails: []*vendorPb.AssetDetails{
						{
							TradingItemId:  456,
							TickerSymbol:   "TESTINDIA",
							ExchangeSymbol: "NSEI",
							PrimaryFlag:    true,
						},
					},
				}
				mocks.vgCatalogClient.EXPECT().GetCompanyTradingItems(gomock.Any(), gomock.Any()).Return(tradingItemsResp, nil)

				// Mock batch upsert
				mocks.securityListingsDao.EXPECT().BatchUpsert(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mocks
			mocks := &mockStruct{
				securityDao:         daoMocks.NewMockSecuritiesDao(ctrl),
				securityListingsDao: daoMocks.NewMockSecurityListingsDao(ctrl),
				vgCatalogClient:     vgCatalogMocks.NewMockCatalogClient(ctrl),
				ussCatalogClient:    mockUssStocksCatalogPb.NewMockCatalogManagerClient(ctrl),
			}

			// Setup mocks
			tt.setup(mocks)

			// Create ingester
			ingester := &StockDataIngesterImpl{
				securityDao:         mocks.securityDao,
				securityListingsDao: mocks.securityListingsDao,
				vgCatalogClient:     mocks.vgCatalogClient,
				securityDataHandler: &SecurityDataHandlerImpl{
					ussCatalogClient: mocks.ussCatalogClient,
					conf:             genConf,
				},
				conf: genConf,
			}

			// Execute test
			err := ingester.IngestByPage(context.Background(), &IngestByPageRequest{
				PageNum:      tt.pageNum,
				SecurityType: securitiesPb.SecurityType_SECURITY_TYPE_STOCK,
			})

			// Assertions
			if tt.expectedErr != nil {
				require.Error(t, err)
				require.Equal(t, tt.expectedErr.Error(), err.Error())
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestStockDataIngesterImpl_populateHistoricalFinancialInfo(t *testing.T) {
	logger.Init(cfg.TestEnv)

	tests := []struct {
		name              string
		vendorSecurityId  string
		setupMocks        func(*daoMocks.MockSecuritiesDao, *vgCatalogMocks.MockCatalogClient)
		want              *securitiesPb.FinancialInfo
		wantErr           bool
		expectedErrorType error
	}{
		{
			name:             "should return error when getting security from db fails",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(nil, epifierrors.ErrRecordNotFound)
			},
			want:              nil,
			wantErr:           true,
			expectedErrorType: epifierrors.ErrPermanent,
		},
		{
			name:             "should return existing financial info when data is recent",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with recent financial info (less than 3 months old, same year)
				recentTime := time.Now() // Current time (guaranteed same quarter/year)
				expectedFinancialInfo := &securitiesPb.FinancialInfo{
					FinancialParameters: &securitiesPb.FinancialParameters{
						FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
							StockFinancialInfo: &securitiesPb.StockFinancialInfo{
								QuarterlyLastUpdatedAt: timestamppb.New(recentTime),
								YearlyLastUpdatedAt:    timestamppb.New(recentTime),
							},
						},
					},
				}
				mockSecurity := &securitiesPb.Security{
					FinancialInfo: expectedFinancialInfo,
				}
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(mockSecurity, nil)
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
		{
			name:             "should fill latest financial info when data is more than 3 months old",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with old financial info (more than 3 months old)
				oldTime := time.Now().AddDate(0, -4, 0) // 4 months ago
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(getMockSecurity(oldTime), nil)

				// Mock the calls to fillLatestFinancialInfo
				_, lastQuarter := getLastYearAndQuarter()

				// Mock yearly data call if it's year-end quarter
				if lastQuarter == yearEndQuarter {
					mockVgCatalogClient.EXPECT().
						GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
						Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
							Status: rpc.StatusRecordNotFound(),
						}, nil)
				}

				// Mock quarterly data call
				mockVgCatalogClient.EXPECT().
					GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
					Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
		{
			name:             "should fill latest financial info when year has changed",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with financial info from previous year
				lastYear := time.Now().AddDate(-1, 0, 0)
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(getMockSecurity(lastYear), nil)

				// Mock the calls to fillLatestFinancialInfo
				_, lastQuarter := getLastYearAndQuarter()

				// Mock yearly data call if it's year-end quarter
				if lastQuarter == yearEndQuarter {
					mockVgCatalogClient.EXPECT().
						GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
						Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
							Status: rpc.StatusRecordNotFound(),
						}, nil)
				}

				// Mock quarterly data call
				mockVgCatalogClient.EXPECT().
					GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
					Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
						Status: rpc.StatusRecordNotFound(),
					}, nil)
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
		{
			name:             "should fill financial info since start when no historical data exists",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with no financial info (nil timestamps)
				mockSecurity := &securitiesPb.Security{
					FinancialInfo: &securitiesPb.FinancialInfo{
						FinancialParameters: &securitiesPb.FinancialParameters{
							FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
								StockFinancialInfo: &securitiesPb.StockFinancialInfo{
									QuarterlyLastUpdatedAt: nil,
									YearlyLastUpdatedAt:    nil,
								},
							},
						},
					},
				}
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(mockSecurity, nil)

				// Mock the calls to fillQuarterlyFinancialInfoSinceStart and fillYearlyFinancialInfoSinceStart
				lastYear, lastQuarter := getLastYearAndQuarter()

				// Mock quarterly data calls
				for year := quarterlyDataStartYear; year <= lastYear; year++ {
					quarterStart := yearStartQuarter
					quarterEnd := yearEndQuarter
					if year == quarterlyDataStartYear {
						quarterStart = quarterlyDataStartQuarter
					}
					if year == lastYear {
						quarterEnd = lastQuarter
					}
					for quarter := quarterStart; quarter <= quarterEnd; quarter++ {
						mockVgCatalogClient.EXPECT().
							GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
							Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
								Status: rpc.StatusRecordNotFound(),
							}, nil)
					}
				}

				// Mock yearly data calls
				for year := yearlyDataStartYear; year <= lastYear; year++ {
					mockVgCatalogClient.EXPECT().
						GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
						Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
							Status: rpc.StatusRecordNotFound(),
						}, nil)
				}
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
		{
			name:             "should fill financial info since start when only quarterly data is missing",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with only yearly data (nil quarterly timestamp)
				recentTime := time.Now()
				mockSecurity := &securitiesPb.Security{
					FinancialInfo: &securitiesPb.FinancialInfo{
						FinancialParameters: &securitiesPb.FinancialParameters{
							FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
								StockFinancialInfo: &securitiesPb.StockFinancialInfo{
									QuarterlyLastUpdatedAt: nil,
									YearlyLastUpdatedAt:    timestamppb.New(recentTime),
								},
							},
						},
					},
				}
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(mockSecurity, nil)

				// Mock the calls to fillQuarterlyFinancialInfoSinceStart and fillYearlyFinancialInfoSinceStart
				lastYear, lastQuarter := getLastYearAndQuarter()

				// Mock quarterly data calls
				for year := quarterlyDataStartYear; year <= lastYear; year++ {
					quarterStart := yearStartQuarter
					quarterEnd := yearEndQuarter
					if year == quarterlyDataStartYear {
						quarterStart = quarterlyDataStartQuarter
					}
					if year == lastYear {
						quarterEnd = lastQuarter
					}
					for quarter := quarterStart; quarter <= quarterEnd; quarter++ {
						mockVgCatalogClient.EXPECT().
							GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
							Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
								Status: rpc.StatusRecordNotFound(),
							}, nil)
					}
				}

				// Mock yearly data calls
				for year := yearlyDataStartYear; year <= lastYear; year++ {
					mockVgCatalogClient.EXPECT().
						GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
						Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
							Status: rpc.StatusRecordNotFound(),
						}, nil)
				}
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
		{
			name:             "should fill financial info since start when only yearly data is missing",
			vendorSecurityId: "test_vendor_id",
			setupMocks: func(mockSecurityDao *daoMocks.MockSecuritiesDao, mockVgCatalogClient *vgCatalogMocks.MockCatalogClient) {
				// Mock security with only quarterly data (nil yearly timestamp)
				recentTime := time.Now()
				mockSecurity := &securitiesPb.Security{
					FinancialInfo: &securitiesPb.FinancialInfo{
						FinancialParameters: &securitiesPb.FinancialParameters{
							FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
								StockFinancialInfo: &securitiesPb.StockFinancialInfo{
									QuarterlyLastUpdatedAt: timestamppb.New(recentTime),
									YearlyLastUpdatedAt:    nil,
								},
							},
						},
					},
				}
				mockSecurityDao.EXPECT().
					GetByVendorSecurityId(gomock.Any(), vendorgateway.Vendor_BRIDGEWISE, "test_vendor_id", gomock.Any()).
					Return(mockSecurity, nil)

				// Mock the calls to fillQuarterlyFinancialInfoSinceStart and fillYearlyFinancialInfoSinceStart
				lastYear, lastQuarter := getLastYearAndQuarter()

				// Mock quarterly data calls
				for year := quarterlyDataStartYear; year <= lastYear; year++ {
					quarterStart := yearStartQuarter
					quarterEnd := yearEndQuarter
					if year == quarterlyDataStartYear {
						quarterStart = quarterlyDataStartQuarter
					}
					if year == lastYear {
						quarterEnd = lastQuarter
					}
					for quarter := quarterStart; quarter <= quarterEnd; quarter++ {
						mockVgCatalogClient.EXPECT().
							GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
							Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
								Status: rpc.StatusRecordNotFound(),
							}, nil)
					}
				}

				// Mock yearly data calls
				for year := yearlyDataStartYear; year <= lastYear; year++ {
					mockVgCatalogClient.EXPECT().
						GetCompanyFundamentalParameters(gomock.Any(), gomock.Any()).
						Return(&vgCatalogPb.GetCompanyFundamentalParametersResponse{
							Status: rpc.StatusRecordNotFound(),
						}, nil)
				}
			},
			want:    nil, // Will be filled by the method
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Create mocks - only mock what's needed for this test
			mockSecurityDao := daoMocks.NewMockSecuritiesDao(ctrl)
			mockVgCatalogClient := vgCatalogMocks.NewMockCatalogClient(ctrl)

			// Setup mocks
			tt.setupMocks(mockSecurityDao, mockVgCatalogClient)

			// Create ingester with only the mocked dependencies
			ingester := &StockDataIngesterImpl{
				securityDao:     mockSecurityDao,
				vgCatalogClient: mockVgCatalogClient,
				// Set other dependencies to nil as they're not used in this method
				securityListingsDao:                nil,
				conf:                               nil,
				stockCatalogRefreshPublisher:       nil,
				securitiesHistoricalPricePublisher: nil,
				securityDataHandler:                nil,
			}

			// Execute test
			result, err := ingester.populateHistoricalFinancialInfo(context.Background(), tt.vendorSecurityId)

			// Assertions
			if tt.wantErr {
				require.Error(t, err)
				if tt.expectedErrorType != nil {
					require.ErrorIs(t, err, tt.expectedErrorType)
				}
			} else {
				require.NoError(t, err)
				require.NotNil(t, result)
			}
		})
	}
}
