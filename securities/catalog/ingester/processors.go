// nolint:gosec
package ingester

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	moneyPb "github.com/epifi/be-common/pkg/money"
	catalogPb "github.com/epifi/gamma/api/securities/catalog"
	"github.com/epifi/gamma/api/typesv2"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
)

// This file is for processing Bridgewise specific data to BE security and security listing pb

type parsedCompanyParameters struct {
	incomeStatement      *catalogPb.IncomeStatement
	balanceSheet         *catalogPb.BalanceSheet
	cashFlowStatement    *catalogPb.CashFlowStatement
	profitabilityRatio   *catalogPb.ProfitabilityRatio
	efficiencyRatio      *catalogPb.EfficiencyRatio
	financialHealthRatio *catalogPb.FinancialHealthRatio
	growthRatio          *catalogPb.GrowthRatio
	valuationRatio       *catalogPb.ValuationRatio
}

func processCompanyDescriptionFromCompanyDetails(companyDetails *vendorPb.CompanyDetails, companyFundamentalParagraphs []*vgCatalogPb.CompanyFundamentalParagraph) (*catalogPb.StockDetails, error) {
	var stockDescriptionStr string
	gicsSectorType, ok := MapGICSSectorTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsSectorName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS sector type to enum: %s", companyDetails.GetGicsSectorName())
	}
	gicsIndustryGroupType, ok := MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry group type to enum: %s", companyDetails.GetGicsIndustryGroupName())
	}
	gicsIndustryType, ok := MapGICSIndustryTypeToEnum[strings.ReplaceAll(companyDetails.GetGicsIndustryName(), "and", "&")]
	if !ok {
		return nil, fmt.Errorf("error converting GICS industry type to enum: %s", companyDetails.GetGicsIndustryName())
	}
	for _, paragraph := range companyFundamentalParagraphs {
		if paragraph.GetParagraphType() == vgCatalogPb.SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION {
			if paragraph.GetParagraph() == "" {
				return nil, fmt.Errorf("company description is empty")
			}
			stockDescriptionStr = paragraph.GetParagraph()
		}
	}
	return &catalogPb.StockDetails{
		StockName:                companyDetails.GetCompanyName(),
		StockShortName:           companyDetails.GetCompanyNameShort(),
		WebsiteUrl:               companyDetails.GetWebsite(),
		RegionName:               companyDetails.GetRegionName(),
		IncorporationCountryName: companyDetails.GetIncorporationCountryName(),
		GicsSectorType:           gicsSectorType,
		GicsIndustryGroupType:    gicsIndustryGroupType,
		GicsIndustryType:         gicsIndustryType,
		StockDescription:         stockDescriptionStr,
	}, nil
}

func processBEFundDetails(fundDetails *vendorPb.FundDetails, fundParagraphs []*vgCatalogPb.FundParagraphs,
	fundSegments []*vendorPb.SegmentDetails, fundHoldings []*vendorPb.HoldingDetails) (*catalogPb.FundDetails, error) {
	var (
		fundDescriptionStr string
		holdingsMap        map[string]float64
		totalWeightage     float64
	)
	holdingsMap = make(map[string]float64)
	for _, paragraph := range fundParagraphs {
		if paragraph.GetParagraphType() == vgCatalogPb.SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION {
			if paragraph.GetParagraph() == "" {
				return nil, fmt.Errorf("fund description is empty")
			}
			fundDescriptionStr = paragraph.GetParagraph()
		}
	}

	sectorHoldingsMap := lo.SliceToMap(fundSegments, func(segment *vendorPb.SegmentDetails) (string, float64) {
		return segment.GetSegmentTypeName(), segment.GetSegmentWeight() * 100
	})

	sort.Slice(fundHoldings, func(i, j int) bool {
		return fundHoldings[i].GetWeight() > fundHoldings[j].GetWeight()
	})
	for _, holding := range fundHoldings {
		// We only show 10 stocks with the highest weightage
		if len(holdingsMap) >= 10 {
			holdingsMap["Others"] = 100 - totalWeightage
			break
		}
		holdingWeight := holding.GetWeight() * 100
		holdingsMap[holding.GetCompanyName()] = holdingWeight
		totalWeightage += holdingWeight
	}

	return &catalogPb.FundDetails{
		FundName:           fundDetails.GetFundName(),
		FundNameShort:      fundDetails.GetFundNameShort(),
		CountryName:        fundDetails.GetDomicileCountryName(),
		BenchmarkName:      fundDetails.GetBenchmarkName(),
		BenchmarkNameShort: fundDetails.GetBenchmarkNameShort(),
		FundDescription:    fundDescriptionStr,
		EtfHoldings: &catalogPb.Holdings{
			Holdings: holdingsMap,
		},
		EquitySectorHoldings: &catalogPb.EquitySectorHoldings{
			EquitySectors: sectorHoldingsMap,
		},
	}, nil
}

func processBEEtfFinancialDetails(fundParameters []*vgCatalogPb.FundParameters, fundMarketData []*vgCatalogPb.MarketData) *catalogPb.FinancialInfo {
	var dividendYield, sharpeRatio, trackingErrorPercentage, expenseRatioPercentage float64
	for _, parameter := range fundParameters {
		// Bridgewise Ref: https://docs.google.com/document/d/1awDL9m1xfR0azidSC1kvJBOydwEFQYgOc7rVIHeGMAA/edit?usp=sharing
		switch parameter.GetSecurityParameterType() {
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD:
			dividendYield = parameter.GetParameterValue() * 100
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARPE_RATIO:
			sharpeRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE:
			trackingErrorPercentage = parameter.GetParameterValue() * 100
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE:
			expenseRatioPercentage = parameter.GetParameterValue() * 100
		}
	}

	return &catalogPb.FinancialInfo{
		MarketCap: processMarketCapFromMarketData(fundMarketData),
		TtmFundamentalParameters: &catalogPb.FundamentalParameters{
			DividendYield:           dividendYield,
			SharpeRatio:             sharpeRatio,
			TrackingErrorPercentage: trackingErrorPercentage,
			ExpenseRatioPercentage:  expenseRatioPercentage,
		},
	}
}

// market data is a slice which contains data for each day, we require the latest day's market cap
func processMarketCapFromMarketData(fundMarketData []*vgCatalogPb.MarketData) *money.Money {
	if len(fundMarketData) == 0 {
		return nil
	}
	lastReportedMarketData := fundMarketData[len(fundMarketData)-1]
	return moneyPb.ParseFloat(lastReportedMarketData.GetMarketCap(), lastReportedMarketData.GetPriceCurrencyIso3())
}

func convertAssetDetailsToSecurityPb(ctx context.Context, securityData *vendorPb.CompanyDetails) *catalogPb.Security {
	// This parsing logic will be specific to bridgewise
	// Ex: If Bridgewise provides us Trading Companies and Distributors, replace all converts it to Trading Companies & Distributors
	gicsSectorType, ok := MapGICSSectorTypeToEnum[strings.ReplaceAll(securityData.GetGicsSectorName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Sector Type unavailable", zap.String("GICS Sector Type", securityData.GetGicsSectorName()))
		gicsSectorType = catalogPb.GICSSectorType_GICS_SECTOR_TYPE_UNSPECIFIED
	}
	gicsIndustryGroupType, ok := MapGICSIndustryGroupTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryGroupName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Group Type unavailable", zap.String("GICS Industry Group Type", securityData.GetGicsIndustryGroupName()))
		gicsIndustryGroupType = catalogPb.GICSIndustryGroupType_GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED
	}
	gicsIndustryType, ok := MapGICSIndustryTypeToEnum[strings.ReplaceAll(securityData.GetGicsIndustryName(), "and", "&")]
	if !ok {
		logger.Info(ctx, "GICS Industry Type unavailable", zap.String("GICS Industry Type", securityData.GetGicsIndustryName()))
		gicsIndustryType = catalogPb.GICSIndustryType_GICS_INDUSTRY_TYPE_UNSPECIFIED
	}
	return &catalogPb.Security{
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_STOCK,
		SecurityName:     securityData.GetCompanyName(),
		Vendor:           vendor,
		VendorSecurityId: strconv.Itoa(int(securityData.GetCompanyId())),
		SecurityDetails: &catalogPb.SecurityDetails{
			SecurityType: &catalogPb.SecurityDetails_StockDetails{
				StockDetails: &catalogPb.StockDetails{
					StockName:                securityData.GetCompanyName(),
					StockShortName:           securityData.GetCompanyNameShort(),
					WebsiteUrl:               securityData.GetWebsite(),
					RegionName:               securityData.GetRegionName(),
					IncorporationCountryName: securityData.GetIncorporationCountryName(),
					GicsSectorType:           gicsSectorType,
					GicsIndustryType:         gicsIndustryType,
					GicsIndustryGroupType:    gicsIndustryGroupType,
				},
			},
		},
	}
}

func convertTradingItemToSecurityListing(tradingItem *vendorPb.AssetDetails, securityId, externalId string, isinMapping *IsinVendorIdMapping) *catalogPb.SecurityListing {
	if isinMapping != nil && isinMapping.Isin != "" && isinMapping.VendorListingId == strconv.Itoa(int(tradingItem.GetTradingItemId())) {
		return &catalogPb.SecurityListing{
			ExternalId:       externalId,
			SecurityId:       securityId,
			Exchange:         bridgewiseToInternalExchangeMap[strings.ToUpper(tradingItem.GetExchangeSymbol())],
			Symbol:           tradingItem.GetTickerSymbol(),
			IsPrimaryListing: tradingItem.GetPrimaryFlag(),
			Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
			Vendor:           vendor,
			VendorListingId:  strconv.Itoa(int(tradingItem.GetTradingItemId())),
			Isin:             isinMapping.Isin,
		}
	}
	return &catalogPb.SecurityListing{
		ExternalId:       externalId,
		SecurityId:       securityId,
		Exchange:         bridgewiseToInternalExchangeMap[strings.ToUpper(tradingItem.GetExchangeSymbol())],
		Symbol:           tradingItem.GetTickerSymbol(),
		IsPrimaryListing: tradingItem.GetPrimaryFlag(),
		Status:           catalogPb.ListingStatus_LISTING_STATUS_ACTIVE,
		Vendor:           vendor,
		VendorListingId:  strconv.Itoa(int(tradingItem.GetTradingItemId())),
	}
}

func convertAssetDetailsToSecurityPbEtf(securityData *vendorPb.FundDetails) *catalogPb.Security {
	return &catalogPb.Security{
		SecurityType:     catalogPb.SecurityType_SECURITY_TYPE_ETF,
		SecurityName:     securityData.GetFundName(),
		Vendor:           vendorgateway.Vendor_BRIDGEWISE,
		VendorSecurityId: strconv.Itoa(int(securityData.GetFundId())),
		SecurityDetails: &catalogPb.SecurityDetails{
			SecurityType: &catalogPb.SecurityDetails_FundDetails{
				FundDetails: &catalogPb.FundDetails{
					FundName:           securityData.GetFundName(),
					FundNameShort:      securityData.GetFundNameShort(),
					CountryName:        securityData.GetDomicileCountryName(),
					BenchmarkName:      securityData.GetBenchmarkName(),
					BenchmarkNameShort: securityData.GetBenchmarkNameShort(),
				},
			},
		},
	}
}

// nolint:dupl
func fillQuarterlyCompanyParameters(companyParameters []*vgCatalogPb.CompanyFundamentalParameters, financialInfo *catalogPb.StockFinancialInfo) *catalogPb.StockFinancialInfo {
	if len(companyParameters) == 0 {
		return financialInfo
	}
	parsedParameters := parseCompanyParameters(companyParameters, true)

	if financialInfo == nil {
		financialInfo = &catalogPb.StockFinancialInfo{}
	}
	financialInfo.QuarterlyIncomeStatements = append(financialInfo.QuarterlyIncomeStatements, parsedParameters.incomeStatement)
	financialInfo.QuarterlyBalanceSheets = append(financialInfo.QuarterlyBalanceSheets, parsedParameters.balanceSheet)
	financialInfo.QuarterlyCashFlowStatements = append(financialInfo.QuarterlyCashFlowStatements, parsedParameters.cashFlowStatement)
	financialInfo.QuarterlyProfitabilityRatios = append(financialInfo.QuarterlyProfitabilityRatios, parsedParameters.profitabilityRatio)
	financialInfo.QuarterlyEfficiencyRatios = append(financialInfo.QuarterlyEfficiencyRatios, parsedParameters.efficiencyRatio)
	financialInfo.QuarterlyFinancialHealthRatios = append(financialInfo.QuarterlyFinancialHealthRatios, parsedParameters.financialHealthRatio)
	financialInfo.QuarterlyGrowthRatios = append(financialInfo.QuarterlyGrowthRatios, parsedParameters.growthRatio)
	financialInfo.QuarterlyValuationRatios = append(financialInfo.QuarterlyValuationRatios, parsedParameters.valuationRatio)

	return financialInfo
}

// nolint:dupl
func fillYearlyCompanyParameters(companyParameters []*vgCatalogPb.CompanyFundamentalParameters, financialInfo *catalogPb.StockFinancialInfo) *catalogPb.StockFinancialInfo {
	if len(companyParameters) == 0 {
		return financialInfo
	}
	parsedParameters := parseCompanyParameters(companyParameters, false)

	if financialInfo == nil {
		financialInfo = &catalogPb.StockFinancialInfo{}
	}
	financialInfo.YearlyIncomeStatements = append(financialInfo.YearlyIncomeStatements, parsedParameters.incomeStatement)
	financialInfo.YearlyBalanceSheets = append(financialInfo.YearlyBalanceSheets, parsedParameters.balanceSheet)
	financialInfo.YearlyCashFlowStatements = append(financialInfo.YearlyCashFlowStatements, parsedParameters.cashFlowStatement)
	financialInfo.YearlyProfitabilityRatios = append(financialInfo.YearlyProfitabilityRatios, parsedParameters.profitabilityRatio)
	financialInfo.YearlyEfficiencyRatios = append(financialInfo.YearlyEfficiencyRatios, parsedParameters.efficiencyRatio)
	financialInfo.YearlyFinancialHealthRatios = append(financialInfo.YearlyFinancialHealthRatios, parsedParameters.financialHealthRatio)
	financialInfo.YearlyGrowthRatios = append(financialInfo.YearlyGrowthRatios, parsedParameters.growthRatio)
	financialInfo.YearlyValuationRatios = append(financialInfo.YearlyValuationRatios, parsedParameters.valuationRatio)
	return financialInfo
}

func parseCompanyParameters(companyParameters []*vgCatalogPb.CompanyFundamentalParameters, isQuarterly bool) *parsedCompanyParameters {
	// this is done to avoid panics
	parameters := &parsedCompanyParameters{
		incomeStatement:      &catalogPb.IncomeStatement{},
		balanceSheet:         &catalogPb.BalanceSheet{},
		cashFlowStatement:    &catalogPb.CashFlowStatement{},
		profitabilityRatio:   &catalogPb.ProfitabilityRatio{},
		efficiencyRatio:      &catalogPb.EfficiencyRatio{},
		financialHealthRatio: &catalogPb.FinancialHealthRatio{},
		growthRatio:          &catalogPb.GrowthRatio{},
		valuationRatio:       &catalogPb.ValuationRatio{},
	}
	var reportDate *typesv2.Date
	var calendarYear, calendarQuarter int32
	if len(companyParameters) > 0 {
		parsedDate, _ := time.Parse("2006-01-02", companyParameters[0].GetFilingDate())
		reportDate = &typesv2.Date{
			Year:  int32(parsedDate.Year()),
			Month: int32(parsedDate.Month()),
			Day:   int32(parsedDate.Day()),
		}
		calendarYear = int32(companyParameters[0].GetCalendarYear())
		calendarQuarter = int32(companyParameters[0].GetCalendarQuarter())
	}

	for _, parameter := range companyParameters {
		if parameter.GetParameterValue() == 0 {
			continue
		}
		// Bridgewise Ref: https://docs.google.com/document/d/1awDL9m1xfR0azidSC1kvJBOydwEFQYgOc7rVIHeGMAA/edit?usp=sharing
		switch parameter.GetSecurityParameterType() {
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_REVENUE:
			parameters.incomeStatement.TotalRevenue = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_EXPENSES:
			parameters.incomeStatement.TotalExpenses = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_PROFIT:
			parameters.incomeStatement.GrossProfit = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_INCOME:
			parameters.incomeStatement.NetIncome = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA:
			parameters.incomeStatement.Ebitda = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_ASSETS:
			parameters.balanceSheet.TotalAssets = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_LIABILITIES:
			parameters.balanceSheet.TotalLiabilities = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_CURRENT_RATIO:
			parameters.balanceSheet.CurrentRatio = parameter.GetParameterValue()

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_FREE_CASH_FLOW:
			parameters.cashFlowStatement.FreeCashFlow = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_OPERATING_CASH_FLOW:
			parameters.cashFlowStatement.OperatingCashFlow = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_INVESTING_CASH_FLOW:
			parameters.cashFlowStatement.InvestingCashFlow = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_FINANCING_CASH_FLOW:
			parameters.cashFlowStatement.FinancingCashFlow = typesv2.GetFromBeMoney(moneyPb.ParseFloat(parameter.GetParameterValue(), parameter.GetParameterCurrencyIso3()))

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_MARGIN:
			parameters.profitabilityRatio.GrossMargin = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA_MARGIN:
			parameters.profitabilityRatio.EbitdaMargin = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_MARGIN:
			parameters.profitabilityRatio.NetMargin = parameter.GetParameterValue()

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY:
			parameters.efficiencyRatio.Roe = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_TOTAL_CAPITAL:
			parameters.efficiencyRatio.Rotc = parameter.GetParameterValue()

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_DEBT_TO_EQUITY:
			parameters.financialHealthRatio.TotalDebtToEquity = parameter.GetParameterValue()

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DILUTED_EPS_GROWTH:
			parameters.growthRatio.DilutedEpsGrowth = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_REVENUE_GROWTH:
			parameters.growthRatio.RevenueGrowth = parameter.GetParameterValue()

		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PE_RATIO:
			parameters.valuationRatio.PeRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PB_RATIO:
			parameters.valuationRatio.PbRatio = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD:
			parameters.valuationRatio.DividendYieldPercentage = parameter.GetParameterValue()
		case vgCatalogPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EV_TO_EBITDA:
			parameters.valuationRatio.EvToEbitda = parameter.GetParameterValue()
		}
	}

	parameters.incomeStatement.CalendarYear = calendarYear
	parameters.incomeStatement.ReportDate = reportDate

	parameters.balanceSheet.CalendarYear = calendarYear
	parameters.balanceSheet.ReportDate = reportDate

	parameters.cashFlowStatement.CalendarYear = calendarYear
	parameters.cashFlowStatement.ReportDate = reportDate

	parameters.profitabilityRatio.CalendarYear = calendarYear
	parameters.profitabilityRatio.ReportDate = reportDate

	parameters.efficiencyRatio.CalendarYear = calendarYear
	parameters.efficiencyRatio.ReportDate = reportDate

	parameters.financialHealthRatio.CalendarYear = calendarYear
	parameters.financialHealthRatio.ReportDate = reportDate

	parameters.growthRatio.CalendarYear = calendarYear
	parameters.growthRatio.ReportDate = reportDate

	parameters.valuationRatio.CalendarYear = calendarYear
	parameters.valuationRatio.ReportDate = reportDate

	if isQuarterly {
		parameters.incomeStatement.CalendarQuarter = calendarQuarter
		parameters.balanceSheet.CalendarQuarter = calendarQuarter
		parameters.cashFlowStatement.CalendarQuarter = calendarQuarter
		parameters.profitabilityRatio.CalendarQuarter = calendarQuarter
		parameters.efficiencyRatio.CalendarQuarter = calendarQuarter
		parameters.financialHealthRatio.CalendarQuarter = calendarQuarter
		parameters.growthRatio.CalendarQuarter = calendarQuarter
		parameters.valuationRatio.CalendarQuarter = calendarQuarter
	}

	return parameters
}
