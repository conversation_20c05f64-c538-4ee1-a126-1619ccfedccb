package ingester

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	ussCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
	wireTypes "github.com/epifi/gamma/securities/wire/types"
)

const (
	maxGoRoutines = 10
)

var EtfDataIngesterWireSet = wire.NewSet(NewEtfDataIngester)

type EtfDataIngesterImpl struct {
	securityDao                        dao.SecuritiesDao
	securityListingsDao                dao.SecurityListingsDao
	vgCatalogClient                    vgCatalogPb.CatalogClient
	ussCatalogClient                   ussCatalogPb.CatalogManagerClient
	conf                               *genConf.Config
	stockCatalogRefreshPublisher       wireTypes.StockCatalogRefreshPublisher
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher
	securityDataHandler                *SecurityDataHandlerImpl
}

func NewEtfDataIngester(
	conf *genConf.Config,
	securityDao dao.SecuritiesDao,
	securityListingsDao dao.SecurityListingsDao,
	vgCatalogClient vgCatalogPb.CatalogClient,
	ussCatalogClient ussCatalogPb.CatalogManagerClient,
	stockCatalogRefreshPublisher wireTypes.StockCatalogRefreshPublisher,
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher,
	securityDataHandler *SecurityDataHandlerImpl,
) *EtfDataIngesterImpl {
	return &EtfDataIngesterImpl{
		conf:                               conf,
		securityDao:                        securityDao,
		securityListingsDao:                securityListingsDao,
		vgCatalogClient:                    vgCatalogClient,
		ussCatalogClient:                   ussCatalogClient,
		stockCatalogRefreshPublisher:       stockCatalogRefreshPublisher,
		securitiesHistoricalPricePublisher: securitiesHistoricalPricePublisher,
		securityDataHandler:                securityDataHandler,
	}
}

func (s *EtfDataIngesterImpl) IngestByPage(ctx context.Context, req *IngestByPageRequest) error {
	if int(req.PageNum) > s.conf.AddNewSecuritiesConfig().MaximumPageNum() {
		return ErrMaxPagesExceeded
	}

	logger.Info(ctx, "processing add new securities", zap.Any("pageNum", req.PageNum),
		zap.Any("exchanges", s.securityDataHandler.GetExchanges()))

	//nolint: gosec
	resp, err := s.vgCatalogClient.GetFunds(ctx, &vgCatalogPb.GetFundsRequest{
		Header:            &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		Exchanges:         s.securityDataHandler.GetExchanges(),
		Page:              req.PageNum,
		PageSize:          int32(s.conf.AddNewSecuritiesConfig().PageSize()),
		FilterByAnalytics: req.FilterByAnalytics.ToBool(),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "Page number is out of bounds", zap.Any("page number", req.PageNum))
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error while calling GetFunds vg", zap.Error(rpcErr))
		return rpcErr
	}

	err = s.ingestFunds(ctx, resp.GetFunds())
	if err != nil {
		logger.Error(ctx, "error while ingesting funds", zap.Error(err))
		return err
	}

	return nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) ingestFunds(ctx context.Context, fund *vendorPb.FundsData) error {
	grp, grpCtx := errgroup.WithContext(ctx)
	fundIngestionChan := make(chan *vendorPb.FundDetails, 5)

	grp.Go(func() error {
		defer close(fundIngestionChan)
		for _, fund := range fund.GetData() {
			select {
			case fundIngestionChan <- fund:
			case <-grpCtx.Done():
				return grpCtx.Err()
			}
		}
		return nil
	})

	for i := 0; i < maxGoRoutines; i++ {
		grp.Go(func() error {
			for currentFund := range fundIngestionChan {
				ingestionErr := s.ingestFundAndListings(ctx, currentFund, nil)
				if ingestionErr != nil {
					logger.Error(ctx, "error while ingesting fund and listings",
						zap.Error(ingestionErr), zap.Any(logger.REQUEST_ID, currentFund.GetFundId()))
				}
			}
			return nil
		})
	}

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in fund ingestion", zap.Error(err))
		return err
	}

	return nil
}

func (s *EtfDataIngesterImpl) IngestByISINs(ctx context.Context, req *IngestByISINRequest) *IngestByISINResponse {
	isinMappings := lo.Uniq(req.IsinMappings)
	var failedISINs, notFoundISINs []string
	var isinSecurityListingPairs []*securitiesPb.IsinSecurityListingPair
	for _, isinMapping := range isinMappings {
		resp, err := s.vgCatalogClient.GetFund(ctx, &vgCatalogPb.GetFundRequest{
			Header: &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
			FundId: isinMapping.VendorSecurityId,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil || len(resp.GetFunds()) == 0 {
			if !resp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "Error fetching company with vendorSecurityId", zap.Error(rpcErr),
					zap.String("vendorSecurityId", isinMapping.VendorSecurityId), zap.String("isin", isinMapping.Isin))
				failedISINs = append(failedISINs, isinMapping.Isin)
				continue
			}
			notFoundISINs = append(notFoundISINs, isinMapping.Isin)
			continue
		}

		err = s.ingestFundAndListings(ctx, resp.GetFunds()[0], isinMapping)
		if err != nil {
			logger.Error(ctx, "Error ingesting fund and listings", zap.Error(err))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		securityListing, daoErr := s.securityListingsDao.GetByVendorListingId(ctx, vendorgateway.Vendor_BRIDGEWISE, isinMapping.VendorListingId, []securitiesPb.SecurityListingFieldMask{
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security listing with vendorListingId", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		security, daoErr := s.securityDao.GetById(ctx, securityListing.GetSecurityId(), []securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security with security id", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		s.securityDataHandler.PublishListingAndHistoricalPrices(ctx, &PublishListingAndHistoricalPricesRequest{
			SecurityId:       securityListing.GetSecurityId(),
			ExternalId:       securityListing.GetExternalId(),
			VendorSecurityId: security.GetVendorSecurityId(),
			VendorListingId:  securityListing.GetVendorListingId(),
		})

		isinSecurityListingPairs = append(isinSecurityListingPairs, &securitiesPb.IsinSecurityListingPair{
			Isin:              securityListing.GetIsin(),
			SecurityListingId: securityListing.GetExternalId(),
		})
	}
	return &IngestByISINResponse{
		IngestedIsinSecurityListingPairs: isinSecurityListingPairs,
		FailedISINs:                      failedISINs,
		NotFoundISINs:                    notFoundISINs,
	}
}

func (s *EtfDataIngesterImpl) RefreshSecurityListing(ctx context.Context, req *RefreshSecurityListingRequest) error {
	var (
		fundDetails    *vendorPb.FundDetails
		fundParagraphs []*vgCatalogPb.FundParagraphs
		fundParameters []*vgCatalogPb.FundParameters
		fundMarketData []*vgCatalogPb.MarketData
		fundSegments   []*vendorPb.SegmentDetails
		fundHoldings   []*vendorPb.HoldingDetails
	)
	g, gCtx := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		fundDetails, err = s.getFundDetails(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "fund not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting company details for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		fundParagraphs, err = s.getFundParagraphs(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "company fundamental paragraphs not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fundamental paragraphs for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		fundParameters, err = s.getFundParameters(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "fund parameters not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fund parameters for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		fundSegments, err = s.getSegments(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "fund segments not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fund segments for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		fundHoldings, err = s.getHoldings(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "fund holdings not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fund holdings for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		fundMarketData, err = s.getFundMarketData(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "fund market data not found", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fund market data for vendorSecurityId: %s", req.VendorSecurityId))
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		logger.Error(ctx, "error refreshing security listing details", zap.Error(err), zap.Any("securityListing", req.SecurityId))
		return epifierrors.ErrTransient
	}

	fundFinancialDetails := processBEEtfFinancialDetails(fundParameters, fundMarketData)
	fundSecurityDetails, err := processBEFundDetails(fundDetails, fundParagraphs, fundSegments, fundHoldings)
	if err != nil {
		logger.Error(ctx, "error filling fund details", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
		return epifierrors.ErrPermanent
	}
	err = s.updateSecurity(ctx, req.SecurityId, fundSecurityDetails, fundFinancialDetails)
	if err != nil {
		logger.Error(ctx, "error updating fund details", zap.Error(err))
		return epifierrors.ErrTransient
	}

	return nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) ingestFundAndListings(ctx context.Context, fundData *vendorPb.FundDetails, isinMapping *IsinVendorIdMapping) error {
	security, err := s.upsertFund(ctx, fundData)
	if err != nil {
		logger.Error(ctx, "error while get or create fund data", zap.Error(err), zap.Any(logger.REQUEST_ID, fundData.GetFundId()))
		return err
	}

	securityListingsVg, vgErr := s.getSecurityListingsFromVg(ctx, security.GetVendorSecurityId())
	if vgErr != nil {
		logger.Error(ctx, "error while fetching security listings from vg", zap.Error(vgErr), zap.Any(logger.REQUEST_ID, fundData.GetFundId()))
		return vgErr
	}

	var securityListings []*securitiesPb.SecurityListing

	for _, securityListingVg := range securityListingsVg.GetAssetDetails() {
		if _, ok := bridgewiseToInternalExchangeMap[strings.ToUpper(securityListingVg.GetExchangeSymbol())]; !ok {
			// if exchange is not handled internally skip parsing, currently only Indian and US stocks are ingested
			continue
		}
		externalId, generateErr := s.securityDataHandler.GenerateExternalId(ctx, securityListingVg.GetTickerSymbol(), securityListingVg.GetExchangeSymbol())
		if generateErr != nil {
			logger.Error(ctx, "error while calling GenerateExternalId", zap.Error(generateErr),
				zap.Any("symbol", securityListingVg.GetTickerSymbol()), zap.Any("exchange", securityListingVg.GetExchangeSymbol()),
				zap.Any(logger.REQUEST_ID, fundData.GetFundId()), zap.Any("tradingItemId", securityListingVg.GetTradingItemId()))
			return generateErr
		}
		securityListing := convertTradingItemToSecurityListing(securityListingVg, security.GetId(), externalId, isinMapping)
		securityListings = append(securityListings, securityListing)
	}

	upsertErr := s.securityListingsDao.BatchUpsert(ctx, securityListings, nil)
	if upsertErr != nil {
		var vendorListingIds []int64
		for _, securityListing := range securityListingsVg.GetAssetDetails() {
			vendorListingIds = append(vendorListingIds, securityListing.GetTradingItemId())
		}
		logger.Error(ctx, "error while upserting security listings", zap.Error(upsertErr),
			zap.Any(logger.REQUEST_ID, fundData.GetFundId()), zap.Any("vendorListingIds", vendorListingIds))
		return upsertErr
	}
	return nil
}

func (s *EtfDataIngesterImpl) upsertFund(ctx context.Context, fundData *vendorPb.FundDetails) (*securitiesPb.Security, error) {
	security, getErr := s.securityDao.GetByVendorSecurityId(ctx, vendorgateway.Vendor_BRIDGEWISE, strconv.Itoa(int(fundData.GetFundId())),
		[]securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
	if getErr != nil && errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		securityObj := convertAssetDetailsToSecurityPbEtf(fundData)
		logger.Info(ctx, "creating new security", zap.Any("securityObj", securityObj))

		var createErr error
		security, createErr = s.securityDao.Create(ctx, securityObj)
		if createErr != nil {
			logger.Error(ctx, "error while calling Create", zap.Error(createErr), zap.Any(logger.REQUEST_ID, fundData.GetFundId()))
			return nil, createErr
		}
	} else if getErr != nil {
		logger.Error(ctx, "error while calling GetByVendorSecurityId", zap.Error(getErr), zap.Any(logger.REQUEST_ID, fundData.GetFundId()))
		return nil, getErr
	}
	return security, nil
}

func (s *EtfDataIngesterImpl) getSecurityListingsFromVg(ctx context.Context, fundId string) (*vgCatalogPb.GetCompanyTradingItemsResponse, error) {
	// Get security listings for a security
	// There is no fund trading items api for bridgewise, GetCompanyTradingItems allows fund ids as well
	tradingItems, tradingItemsErr := s.vgCatalogClient.GetCompanyTradingItems(ctx, &vgCatalogPb.GetCompanyTradingItemsRequest{
		Header:    &vendorgateway.RequestHeader{Vendor: vendorgateway.Vendor_BRIDGEWISE},
		CompanyId: fundId,
	})
	if rpcErr := epifigrpc.RPCError(tradingItems, tradingItemsErr); rpcErr != nil {
		logger.Error(ctx, "error while calling GetCompanyTradingItems vg", zap.Error(rpcErr), zap.String(logger.REQUEST_ID, fundId))
		return nil, rpcErr
	}
	return tradingItems, nil
}

// nolint:unparam
func (s *EtfDataIngesterImpl) updateSecurity(ctx context.Context, securityId string, fundDetails *securitiesPb.FundDetails, financialDetails *securitiesPb.FinancialInfo) error {
	security := &securitiesPb.Security{
		Id: securityId,
		SecurityDetails: &securitiesPb.SecurityDetails{
			SecurityType: &securitiesPb.SecurityDetails_FundDetails{
				FundDetails: fundDetails,
			},
		},
		FinancialInfo: financialDetails,
	}
	_, updateErr := s.securityDao.Update(ctx, security, []securitiesPb.SecurityFieldMask{
		securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS,
		securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO,
	})
	if updateErr != nil {
		logger.Error(ctx, "error updating security details", zap.Error(updateErr), zap.String("securityId", security.GetId()))
		return fmt.Errorf("error updating security details: %w", updateErr)
	}
	return nil
}

func (s *EtfDataIngesterImpl) getFundDetails(ctx context.Context, vendor vendorgateway.Vendor, fundId string) (*vendorPb.FundDetails, error) {
	resp, err := s.vgCatalogClient.GetFund(ctx, &vgCatalogPb.GetFundRequest{
		Header: &vendorgateway.RequestHeader{Vendor: vendor},
		FundId: fundId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting fund details", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetFund rpc")
	}
	if len(resp.GetFunds()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetFunds()[0], nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) getFundParagraphs(ctx context.Context, vendor vendorgateway.Vendor, fundId string) ([]*vgCatalogPb.FundParagraphs, error) {
	resp, err := s.vgCatalogClient.GetFundParagraphs(ctx, &vgCatalogPb.GetFundParagraphsRequest{
		Header: &vendorgateway.RequestHeader{Vendor: vendor},
		FundId: fundId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting fund paragraphs", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetFundParagraphs rpc")
	}
	if len(resp.GetParagraphs()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetParagraphs(), nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) getFundParameters(ctx context.Context, vendor vendorgateway.Vendor, fundId string) ([]*vgCatalogPb.FundParameters, error) {
	resp, err := s.vgCatalogClient.GetFundParameters(ctx, &vgCatalogPb.GetFundParametersRequest{
		Header: &vendorgateway.RequestHeader{Vendor: vendor},
		FundId: fundId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting fund parameters", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetFundParameters rpc")
	}
	if len(resp.GetParameters()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetParameters(), nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) getSegments(ctx context.Context, vendor vendorgateway.Vendor, fundId string) ([]*vendorPb.SegmentDetails, error) {
	resp, err := s.vgCatalogClient.GetSegments(ctx, &vgCatalogPb.GetSegmentsRequest{
		Header: &vendorgateway.RequestHeader{Vendor: vendor},
		FundId: fundId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting segment details", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetSegments rpc")
	}
	if len(resp.GetSegmentDetails()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetSegmentDetails(), nil
}

// nolint:dupl
func (s *EtfDataIngesterImpl) getHoldings(ctx context.Context, vendor vendorgateway.Vendor, fundId string) ([]*vendorPb.HoldingDetails, error) {
	resp, err := s.vgCatalogClient.GetHoldings(ctx, &vgCatalogPb.GetHoldingsRequest{
		Header: &vendorgateway.RequestHeader{Vendor: vendor},
		FundId: fundId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting holding details", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetHoldings rpc")
	}
	if len(resp.GetHoldingDetails()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetHoldingDetails(), nil
}

func (s *EtfDataIngesterImpl) getFundMarketData(ctx context.Context, vendor vendorgateway.Vendor, fundId string) ([]*vgCatalogPb.MarketData, error) {
	curTime := time.Now().In(datetime.IST)
	curDate := time.Date(curTime.Year(), curTime.Month(), curTime.Day(), 0, 0, 0, 0, datetime.IST)
	resp, err := s.vgCatalogClient.GetFundMarketData(ctx, &vgCatalogPb.GetFundMarketDataRequest{
		Header:   &vendorgateway.RequestHeader{Vendor: vendor},
		FundId:   fundId,
		FromDate: datetime.TimeToDateInLoc(curDate, datetime.IST),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error getting fund market data", zap.Error(err))
		return nil, errors.Wrap(err, "error while invoking GetFundMarketData rpc")
	}
	if len(resp.GetMarketData()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetMarketData(), nil
}
