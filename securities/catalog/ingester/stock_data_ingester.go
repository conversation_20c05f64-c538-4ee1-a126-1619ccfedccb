package ingester

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	securitiesPb "github.com/epifi/gamma/api/securities/catalog"
	vgCatalogPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorPb "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/securities/catalog/dao"
	genConf "github.com/epifi/gamma/securities/config/genconf"
	wireTypes "github.com/epifi/gamma/securities/wire/types"
)

var (
	bridgewiseToInternalExchangeMap = map[string]securitiesPb.Exchange{
		"NSEI":     securitiesPb.Exchange_EXCHANGE_INDIA_NSE,
		"BSE":      securitiesPb.Exchange_EXCHANGE_INDIA_BSE,
		"NYSE":     securitiesPb.Exchange_EXCHANGE_USA_NYSE,
		"NASDAQGS": securitiesPb.Exchange_EXCHANGE_USA_NASDAQ,
		"NASDAQGM": securitiesPb.Exchange_EXCHANGE_USA_NASDAQ,
		"NYSEAM":   securitiesPb.Exchange_EXCHANGE_USA_ASE,
		"ARCA":     securitiesPb.Exchange_EXCHANGE_USA_ARCA,
		"BATS":     securitiesPb.Exchange_EXCHANGE_USA_BATS,
		"OTCPK":    securitiesPb.Exchange_EXCHANGE_USA_OTC,
	}

	ErrMaxPagesExceeded = errors.New("Max pages exceeded")
)

const (
	vendor = commonvgpb.Vendor_BRIDGEWISE
)

var StockDataIngesterWireSet = wire.NewSet(NewStockDataIngester)

type StockDataIngesterImpl struct {
	securityDao                        dao.SecuritiesDao
	securityListingsDao                dao.SecurityListingsDao
	vgCatalogClient                    vgCatalogPb.CatalogClient
	conf                               *genConf.Config
	stockCatalogRefreshPublisher       wireTypes.StockCatalogRefreshPublisher
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher
	securityDataHandler                *SecurityDataHandlerImpl
}

func NewStockDataIngester(
	conf *genConf.Config,
	securityDao dao.SecuritiesDao,
	securityListingsDao dao.SecurityListingsDao,
	vgCatalogClient vgCatalogPb.CatalogClient,
	stockCatalogRefreshPublisher wireTypes.StockCatalogRefreshPublisher,
	securitiesHistoricalPricePublisher wireTypes.SecuritiesHistoricalPricePublisher,
	securityDataHandler *SecurityDataHandlerImpl,
) *StockDataIngesterImpl {
	return &StockDataIngesterImpl{
		conf:                               conf,
		securityDao:                        securityDao,
		securityListingsDao:                securityListingsDao,
		vgCatalogClient:                    vgCatalogClient,
		stockCatalogRefreshPublisher:       stockCatalogRefreshPublisher,
		securitiesHistoricalPricePublisher: securitiesHistoricalPricePublisher,
		securityDataHandler:                securityDataHandler,
	}
}

type IsinVendorIdMapping struct {
	Isin             string
	VendorSecurityId string
	VendorListingId  string
}

func (s *StockDataIngesterImpl) IngestByPage(ctx context.Context, req *IngestByPageRequest) error {
	if int(req.PageNum) > s.conf.AddNewSecuritiesConfig().MaximumPageNum() {
		return ErrMaxPagesExceeded
	}

	logger.Info(ctx, "processing add new securities", zap.Any("pageNum", req.PageNum), zap.Any("exchanges", s.securityDataHandler.GetExchanges()))

	//nolint: gosec
	resp, err := s.vgCatalogClient.GetCompanies(ctx, &vgCatalogPb.GetCompaniesRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: vendor},
		Exchanges: s.securityDataHandler.GetExchanges(),
		Page:      req.PageNum,
		PageSize:  int32(s.conf.AddNewSecuritiesConfig().PageSize()),
	})
	if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
		if resp.GetStatus().IsRecordNotFound() {
			logger.Info(ctx, "Page number is out of bounds", zap.Any("page number", req.PageNum))
			return epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, "error while calling GetCompanies vg", zap.Error(rpcErr))
		return rpcErr
	}

	err = s.ingestCompanies(ctx, resp.GetCompanies())
	if err != nil {
		logger.Error(ctx, "error while ingesting companies", zap.Error(err))
		return err
	}

	return nil
}

// nolint:dupl
func (s *StockDataIngesterImpl) ingestCompanies(ctx context.Context, companies *vendorPb.CompaniesData) error {
	grp, grpCtx := errgroup.WithContext(ctx)
	companyIngestionChan := make(chan *vendorPb.CompanyDetails, 5)

	grp.Go(func() error {
		defer close(companyIngestionChan)
		for _, company := range companies.GetData() {
			select {
			case companyIngestionChan <- company:
			case <-grpCtx.Done():
				return grpCtx.Err()
			}
		}
		return nil
	})

	for i := 0; i < maxGoRoutines; i++ {
		grp.Go(func() error {
			for currentCompany := range companyIngestionChan {
				ingestionErr := s.ingestCompanyAndListings(ctx, currentCompany, nil)
				if ingestionErr != nil {
					logger.Error(ctx, "error while ingesting company and listings",
						zap.Error(ingestionErr), zap.Any(logger.REQUEST_ID, currentCompany.GetCompanyId()))
				}
			}
			return nil
		})
	}

	if err := grp.Wait(); err != nil {
		logger.Error(ctx, "error in company ingestion", zap.Error(err))
		return err
	}

	return nil
}

func (s *StockDataIngesterImpl) IngestByISINs(ctx context.Context, req *IngestByISINRequest) *IngestByISINResponse {
	isinMappings := lo.Uniq(req.IsinMappings)
	var failedISINs, notFoundISINs []string
	var isinSecurityListingPairs []*securitiesPb.IsinSecurityListingPair
	for _, isinMapping := range isinMappings {
		resp, err := s.vgCatalogClient.GetCompany(ctx, &vgCatalogPb.GetCompanyRequest{
			Header:    &commonvgpb.RequestHeader{Vendor: vendor},
			CompanyId: isinMapping.VendorSecurityId,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil || len(resp.GetCompanies()) == 0 {
			if !resp.GetStatus().IsRecordNotFound() {
				logger.Error(ctx, "Error fetching company with vendorSecurityId", zap.Error(rpcErr),
					zap.String("vendorSecurityId", isinMapping.VendorSecurityId), zap.String("isin", isinMapping.Isin))
				failedISINs = append(failedISINs, isinMapping.Isin)
				continue
			}
			notFoundISINs = append(notFoundISINs, isinMapping.Isin)
			continue
		}

		err = s.ingestCompanyAndListings(ctx, resp.GetCompanies()[0], isinMapping)
		if err != nil {
			logger.Error(ctx, "Error ingesting company and listings", zap.Error(err))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		securityListing, daoErr := s.securityListingsDao.GetByVendorListingId(ctx, vendor, isinMapping.VendorListingId, []securitiesPb.SecurityListingFieldMask{
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_ISIN,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID,
			securitiesPb.SecurityListingFieldMask_SECURITY_LISTING_FIELD_MASK_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security listing with vendorListingId", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		security, daoErr := s.securityDao.GetById(ctx, securityListing.GetSecurityId(), []securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
		if daoErr != nil {
			logger.Error(ctx, "Error fetching security with security id", zap.Error(daoErr))
			failedISINs = append(failedISINs, isinMapping.Isin)
			continue
		}

		s.securityDataHandler.PublishListingAndHistoricalPrices(ctx, &PublishListingAndHistoricalPricesRequest{
			SecurityId:       securityListing.GetSecurityId(),
			ExternalId:       securityListing.GetExternalId(),
			VendorSecurityId: security.GetVendorSecurityId(),
			VendorListingId:  securityListing.GetVendorListingId(),
		})

		isinSecurityListingPairs = append(isinSecurityListingPairs, &securitiesPb.IsinSecurityListingPair{
			Isin:              securityListing.GetIsin(),
			SecurityListingId: securityListing.GetExternalId(),
		})
	}
	return &IngestByISINResponse{
		IngestedIsinSecurityListingPairs: isinSecurityListingPairs,
		FailedISINs:                      failedISINs,
		NotFoundISINs:                    notFoundISINs,
	}
}

func (s *StockDataIngesterImpl) RefreshSecurityListing(ctx context.Context, req *RefreshSecurityListingRequest) error {
	var (
		companyLogoUrl               string
		companyDetails               *vendorPb.CompanyDetails
		companyFundamentalParagraphs []*vgCatalogPb.CompanyFundamentalParagraph
		companyMarketData            []*vgCatalogPb.MarketData
		financialInfo                *securitiesPb.FinancialInfo
	)
	g, gCtx := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		companyDetails, err = s.getCompanyDetails(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "company not found", zap.Error(err), zap.String("company id", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting company details for companyId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		companyLogoUrl, err = s.getCompanyLogoUrl(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "company logo not found", zap.Error(err), zap.String("company id", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting company logo for companyId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		companyFundamentalParagraphs, err = s.getCompanyFundamentalParagraphs(gCtx, vendor, req.VendorSecurityId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "company fundamental paragraphs not found", zap.Error(err), zap.String("company id", req.VendorSecurityId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting fundamental paragraphs for companyId: %s", req.VendorSecurityId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		// for now only fetching primary stock+exchange traded security
		companyMarketData, err = s.getCompanyMarketData(gCtx, vendor, req.VendorSecurityId, req.VendorListingId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Info(ctx, "company market data not found", zap.Error(err), zap.String("company id", req.VendorSecurityId),
					zap.String("trading item id", req.VendorListingId))
				return nil
			}
			return errors.Wrap(err, fmt.Sprintf("error getting market data for companyId: %s, tradingItemId: %s", req.VendorSecurityId, req.VendorListingId))
		}
		return nil
	})
	g.Go(func() error {
		var err error
		financialInfo, err = s.populateHistoricalFinancialInfo(ctx, req.VendorSecurityId)
		if err != nil {
			logger.Error(ctx, "error populating historical financial info", zap.Error(err), zap.String("company id", req.VendorSecurityId))
			return err
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		logger.Error(ctx, "error refreshing security listing details", zap.Error(err), zap.Any("securityListing", req.SecurityId))
		return epifierrors.ErrTransient
	}

	financialInfo.MarketCap = processMarketCapFromMarketData(companyMarketData)

	companyDescription, err := processCompanyDescriptionFromCompanyDetails(companyDetails, companyFundamentalParagraphs)
	if err != nil {
		logger.Error(ctx, "error processing company description from company details", zap.Error(err))
		return epifierrors.ErrPermanent
	}

	err = s.updateSecurity(ctx, req.SecurityId, companyLogoUrl, companyDescription, financialInfo)
	if err != nil {
		logger.Error(ctx, "error updating security details", zap.Error(err), zap.String("vendorSecurityId", req.VendorSecurityId))
		return epifierrors.ErrTransient
	}

	return nil
}

func (s *StockDataIngesterImpl) populateHistoricalFinancialInfo(ctx context.Context, vendorSecurityId string) (*securitiesPb.FinancialInfo, error) {
	security, err := s.securityDao.GetByVendorSecurityId(ctx, vendor, vendorSecurityId, []securitiesPb.SecurityFieldMask{securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO})
	if err != nil {
		logger.Error(ctx, "error getting security from db", zap.Error(err), zap.String("vendorSecurityId", vendorSecurityId))
		return nil, epifierrors.ErrPermanent
	}

	financialInfo := security.GetFinancialInfo()
	quarterlyLastUpdatedAt := security.GetFinancialInfo().GetFinancialParameters().GetStockFinancialInfo().GetQuarterlyLastUpdatedAt()
	yearlyLastUpdatedAt := security.GetFinancialInfo().GetFinancialParameters().GetStockFinancialInfo().GetYearlyLastUpdatedAt()
	switch {
	// if there are no historical ratios for the security, fetch and populate them
	case quarterlyLastUpdatedAt == nil || yearlyLastUpdatedAt == nil:
		financialInfo, err = s.fillQuarterlyFinancialInfoSinceStart(ctx, vendorSecurityId, financialInfo)
		if err != nil {
			logger.Error(ctx, "error when ingesting quarterly financial info", zap.Error(err))
			return nil, err
		}
		financialInfo, err = s.fillYearlyFinancialInfoSinceStart(ctx, vendorSecurityId, financialInfo)
		if err != nil {
			logger.Error(ctx, "error when ingesting yearly financial info", zap.Error(err))
			return nil, err
		}
		return financialInfo, nil

	// if the last quarterly update is more than 3 months ago or if the year has changed, fetch and update the latest financial details
	case quarterlyLastUpdatedAt.AsTime().Before(time.Now().AddDate(0, -3, 0)) || yearlyLastUpdatedAt.AsTime().Year() != time.Now().Year():
		return s.fillLatestFinancialInfo(ctx, vendorSecurityId, financialInfo)
	default:
		return security.GetFinancialInfo(), nil
	}
}

func (s *StockDataIngesterImpl) fillLatestFinancialInfo(ctx context.Context, vendorSecurityId string, financialInfo *securitiesPb.FinancialInfo) (*securitiesPb.FinancialInfo, error) {
	lastYear, lastQuarter := getLastYearAndQuarter()

	stockFinancialInfo := financialInfo.GetFinancialParameters().GetStockFinancialInfo()
	if lastQuarter == yearEndQuarter && financialInfo.GetFinancialParameters().GetStockFinancialInfo().GetYearlyLastUpdatedAt().AsTime().Year() != time.Now().Year() {
		yearlyCompanyParameters, err := s.getCompanyFundamentalParameters(ctx, vendor, vendorSecurityId, lastYear, 0, vgCatalogPb.PeriodType_PERIOD_TYPE_ANNUAL)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting company fundamental parameters", zap.Error(err), zap.String("vendorSecurityId", vendorSecurityId),
				zap.Int("year", lastYear))
			return nil, err
		}
		if err == nil {
			stockFinancialInfo = fillYearlyCompanyParameters(yearlyCompanyParameters, stockFinancialInfo)
			stockFinancialInfo.YearlyLastUpdatedAt = timestamppb.Now()
		}
	}

	if financialInfo.GetFinancialParameters().GetStockFinancialInfo().GetQuarterlyLastUpdatedAt().AsTime().Before(time.Now().AddDate(0, -3, 0)) {
		quarterlyCompanyParameters, err := s.getCompanyFundamentalParameters(ctx, vendor, vendorSecurityId, lastYear, lastQuarter, vgCatalogPb.PeriodType_PERIOD_TYPE_QUARTER)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting company fundamental parameters", zap.Error(err), zap.String("vendorSecurityId", vendorSecurityId),
				zap.Int("year", lastYear), zap.Int("quarter", lastQuarter))
			return nil, err
		}
		if err == nil {
			stockFinancialInfo = fillQuarterlyCompanyParameters(quarterlyCompanyParameters, stockFinancialInfo)
			stockFinancialInfo.QuarterlyLastUpdatedAt = timestamppb.Now()
		}
	}

	financialInfo.FinancialParameters = &securitiesPb.FinancialParameters{
		FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
			StockFinancialInfo: stockFinancialInfo,
		},
	}
	return financialInfo, nil
}

func (s *StockDataIngesterImpl) fillQuarterlyFinancialInfoSinceStart(ctx context.Context, vendorSecurityId string, financialInfo *securitiesPb.FinancialInfo) (*securitiesPb.FinancialInfo, error) {
	if financialInfo.GetFinancialParameters().GetStockFinancialInfo().GetQuarterlyLastUpdatedAt() != nil {
		// return if quarterly data is present
		return financialInfo, nil
	}

	lastYear, lastQuarter := getLastYearAndQuarter()
	startingYear, startingQuarter := quarterlyDataStartYear, quarterlyDataStartQuarter
	stockFinancialInfo := financialInfo.GetFinancialParameters().GetStockFinancialInfo()

	for year := startingYear; year <= lastYear; year++ {
		quarterStart := yearStartQuarter
		quarterEnd := yearEndQuarter

		// For the starting year, start at the starting quarter
		if year == startingYear {
			quarterStart = startingQuarter
		}

		// For the current year, end at the current quarter
		if year == lastYear {
			quarterEnd = lastQuarter
		}
		for quarter := quarterStart; quarter <= quarterEnd; quarter++ {
			quarterlyCompanyParameters, err := s.getCompanyFundamentalParameters(ctx, vendor, vendorSecurityId, year, quarter, vgCatalogPb.PeriodType_PERIOD_TYPE_QUARTER)
			if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
				logger.Error(ctx, "error getting company fundamental parameters", zap.Error(err), zap.String("vendorSecurityId", vendorSecurityId),
					zap.Int("quarter", quarter), zap.Int("year", year))
				return nil, err
			}
			// Skip adding financial parameters if not found as there will be companies which have not reported earnings as they were not public at the time
			if err == nil {
				stockFinancialInfo = fillQuarterlyCompanyParameters(quarterlyCompanyParameters, stockFinancialInfo)
			}
		}
	}

	if stockFinancialInfo == nil {
		logger.Info(ctx, "financial info not found", zap.String("vendorSecurityId", vendorSecurityId))
		return financialInfo, nil
	}
	stockFinancialInfo.QuarterlyLastUpdatedAt = timestamppb.Now()
	return &securitiesPb.FinancialInfo{
		FinancialParameters: &securitiesPb.FinancialParameters{
			FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
				StockFinancialInfo: stockFinancialInfo,
			},
		},
	}, nil
}

func (s *StockDataIngesterImpl) fillYearlyFinancialInfoSinceStart(ctx context.Context, vendorSecurityId string, financialInfo *securitiesPb.FinancialInfo) (*securitiesPb.FinancialInfo, error) {
	if financialInfo.GetFinancialParameters().GetStockFinancialInfo().GetYearlyLastUpdatedAt() != nil {
		// return if yearly data is present
		return financialInfo, nil
	}

	lastYear, _ := getLastYearAndQuarter()
	stockFinancialInfo := financialInfo.GetFinancialParameters().GetStockFinancialInfo()
	for year := yearlyDataStartYear; year <= lastYear; year++ {
		yearlyCompanyParameters, err := s.getCompanyFundamentalParameters(ctx, vendor, vendorSecurityId, year, 0, vgCatalogPb.PeriodType_PERIOD_TYPE_ANNUAL)
		if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error getting company fundamental parameters", zap.Error(err), zap.String("vendorSecurityId", vendorSecurityId),
				zap.Int("year", year))
			return nil, err
		}
		// Skip adding financial parameters if not found as there will be companies which have not reported earnings as they were not public at the time
		if err == nil {
			stockFinancialInfo = fillYearlyCompanyParameters(yearlyCompanyParameters, stockFinancialInfo)
		}
	}

	if stockFinancialInfo == nil {
		logger.Info(ctx, "financial info not found", zap.String("vendorSecurityId", vendorSecurityId))
		return financialInfo, nil
	}
	stockFinancialInfo.YearlyLastUpdatedAt = timestamppb.Now()
	return &securitiesPb.FinancialInfo{
		FinancialParameters: &securitiesPb.FinancialParameters{
			FinancialInfo: &securitiesPb.FinancialParameters_StockFinancialInfo{
				StockFinancialInfo: stockFinancialInfo,
			},
		},
	}, nil
}

func getLastYearAndQuarter() (int, int) {
	curTime := time.Now()
	var lastYear, lastQuarter int
	currentYear, currentQuarter := curTime.Year(), (int(curTime.Month())-1)/3+1
	// For any quarter, we only have data till the previous quarter
	// So if we're in Q2, we can only get data till Q1
	// If we're in Q1, we need to go to previous year's Q4
	if currentQuarter == 1 {
		lastYear = currentYear - 1
		lastQuarter = 4
	} else {
		lastQuarter = currentQuarter - 1
	}

	return lastYear, lastQuarter
}

func (s *StockDataIngesterImpl) ingestCompanyAndListings(ctx context.Context, companyData *vendorPb.CompanyDetails, isinMapping *IsinVendorIdMapping) error {
	security, err := s.upsertCompany(ctx, companyData)
	if err != nil {
		logger.Error(ctx, "error while get or create company data", zap.Error(err), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return err
	}

	securityListingsVg, vgErr := s.getSecurityListingsFromVg(ctx, security.GetVendorSecurityId())
	if vgErr != nil {
		logger.Error(ctx, "error while fetching security listings from vg", zap.Error(vgErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return vgErr
	}

	var securityListings []*securitiesPb.SecurityListing

	for _, securityListingVg := range securityListingsVg.GetAssetDetails() {
		if _, ok := bridgewiseToInternalExchangeMap[strings.ToUpper(securityListingVg.GetExchangeSymbol())]; !ok {
			// if exchange is not handled internally skip parsing, currently only Indian and US stocks are ingested
			continue
		}
		externalId, generateErr := s.securityDataHandler.GenerateExternalId(ctx, securityListingVg.GetTickerSymbol(), securityListingVg.GetExchangeSymbol())
		if generateErr != nil {
			logger.Error(ctx, "error while calling GenerateExternalId", zap.Error(generateErr),
				zap.Any("symbol", securityListingVg.GetTickerSymbol()), zap.Any("exchange", securityListingVg.GetExchangeSymbol()),
				zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("tradingItemId", securityListingVg.GetTradingItemId()))
			return generateErr
		}
		securityListing := convertTradingItemToSecurityListing(securityListingVg, security.GetId(), externalId, isinMapping)
		securityListings = append(securityListings, securityListing)
	}

	upsertErr := s.securityListingsDao.BatchUpsert(ctx, securityListings, nil)
	if upsertErr != nil {
		var vendorListingIds []int64
		for _, securityListing := range securityListingsVg.GetAssetDetails() {
			vendorListingIds = append(vendorListingIds, securityListing.GetTradingItemId())
		}
		logger.Error(ctx, "error while upserting security listings", zap.Error(upsertErr),
			zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()), zap.Any("vendorListingIds", vendorListingIds))
		return upsertErr
	}
	return nil
}

func (s *StockDataIngesterImpl) upsertCompany(ctx context.Context, companyData *vendorPb.CompanyDetails) (*securitiesPb.Security, error) {
	security, getErr := s.securityDao.GetByVendorSecurityId(ctx, vendor, strconv.Itoa(int(companyData.GetCompanyId())),
		[]securitiesPb.SecurityFieldMask{
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_ID,
			securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID,
		})
	if getErr != nil && errors.Is(getErr, epifierrors.ErrRecordNotFound) {
		securityObj := convertAssetDetailsToSecurityPb(ctx, companyData)
		logger.Info(ctx, "creating new security", zap.Any("securityObj", securityObj))

		var createErr error
		security, createErr = s.securityDao.Create(ctx, securityObj)
		if createErr != nil {
			logger.Error(ctx, "error while calling Create", zap.Error(createErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
			return nil, createErr
		}
	} else if getErr != nil {
		logger.Error(ctx, "error while calling GetByVendorSecurityId", zap.Error(getErr), zap.Any(logger.REQUEST_ID, companyData.GetCompanyId()))
		return nil, getErr
	}
	return security, nil
}

// nolint:unparam
func (s *StockDataIngesterImpl) updateSecurity(ctx context.Context, securityId, companyLogoUrl string, companyDescription *securitiesPb.StockDetails,
	financialInfo *securitiesPb.FinancialInfo) error {
	security := &securitiesPb.Security{
		Id: securityId,
		SecurityDetails: &securitiesPb.SecurityDetails{
			SecurityType: &securitiesPb.SecurityDetails_StockDetails{
				StockDetails: companyDescription,
			},
		},
		FinancialInfo: financialInfo,
	}
	fieldMasks := []securitiesPb.SecurityFieldMask{
		securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS,
		securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO,
	}
	if companyLogoUrl != "" {
		security.LogoUrl = companyLogoUrl
		fieldMasks = append(fieldMasks, securitiesPb.SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL)
	}
	_, updateErr := s.securityDao.Update(ctx, security, fieldMasks)
	if updateErr != nil {
		logger.Error(ctx, "error updating security details", zap.Error(updateErr), zap.String("securityId", security.GetId()))
		return updateErr
	}
	return nil
}

func (s *StockDataIngesterImpl) getSecurityListingsFromVg(ctx context.Context, companyId string) (*vgCatalogPb.GetCompanyTradingItemsResponse, error) {
	// Get security listings for a security
	tradingItems, tradingItemsErr := s.vgCatalogClient.GetCompanyTradingItems(ctx, &vgCatalogPb.GetCompanyTradingItemsRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: vendor},
		CompanyId: companyId,
	})
	if rpcErr := epifigrpc.RPCError(tradingItems, tradingItemsErr); rpcErr != nil {
		logger.Error(ctx, "error while calling GetCompanyTradingItems vg", zap.Error(rpcErr), zap.String(logger.REQUEST_ID, companyId))
		return nil, rpcErr
	}
	return tradingItems, nil
}

func (s *StockDataIngesterImpl) getCompanyDetails(ctx context.Context, vendor commonvgpb.Vendor, companyId string) (*vendorPb.CompanyDetails, error) {
	resp, err := s.vgCatalogClient.GetCompany(ctx, &vgCatalogPb.GetCompanyRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: vendor},
		CompanyId: companyId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while invoking GetCompany rpc")
	}
	if len(resp.GetCompanies()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetCompanies()[0], nil
}

// nolint:dupl,unparam
func (s *StockDataIngesterImpl) getCompanyFundamentalParameters(ctx context.Context, vendor commonvgpb.Vendor, companyId string,
	year, quarter int, periodType vgCatalogPb.PeriodType) ([]*vgCatalogPb.CompanyFundamentalParameters, error) {
	resp, err := s.vgCatalogClient.GetCompanyFundamentalParameters(ctx, &vgCatalogPb.GetCompanyFundamentalParametersRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: vendor},
		CompanyId:       companyId,
		CalendarYear:    int64(year),
		CalendarQuarter: int64(quarter),
		PeriodType:      periodType,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while invoking GetCompanyFundamentalParameters rpc")
	}
	if len(resp.GetParameters()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetParameters(), nil
}

//nolint:dupl
func (s *StockDataIngesterImpl) getCompanyFundamentalParagraphs(ctx context.Context, vendor commonvgpb.Vendor, companyId string) ([]*vgCatalogPb.CompanyFundamentalParagraph, error) {
	resp, err := s.vgCatalogClient.GetCompanyFundamentalParagraphs(ctx, &vgCatalogPb.GetCompanyFundamentalParagraphsRequest{
		Header:    &commonvgpb.RequestHeader{Vendor: vendor},
		CompanyId: companyId,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while invoking GetCompanyFundamentalParagraphs rpc")
	}
	if len(resp.GetParagraphs()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetParagraphs(), nil
}

func (s *StockDataIngesterImpl) getCompanyMarketData(ctx context.Context, vendor commonvgpb.Vendor, companyId string, bridgewiseTradingItemId string) ([]*vgCatalogPb.MarketData, error) {
	resp, err := s.vgCatalogClient.GetCompanyMarketData(ctx, &vgCatalogPb.GetCompanyMarketDataRequest{
		Header:        &commonvgpb.RequestHeader{Vendor: vendor},
		TradingItemId: bridgewiseTradingItemId,
		CompanyId:     companyId,
		FromDate:      nil,
		ToDate:        nil,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		if resp.GetStatus().IsRecordNotFound() {
			return nil, epifierrors.ErrRecordNotFound
		}
		return nil, errors.Wrap(err, "error while invoking GetCompanyMarketData rpc")
	}
	if len(resp.GetMarketData()) == 0 {
		return nil, epifierrors.ErrRecordNotFound
	}
	return resp.GetMarketData(), nil
}

func (s *StockDataIngesterImpl) getCompanyLogoUrl(ctx context.Context, vendor commonvgpb.Vendor, companyId string) (string, error) {
	resp, err := s.vgCatalogClient.GetCompanyLogos(ctx, &vgCatalogPb.GetCompanyLogosRequest{
		Header:     &commonvgpb.RequestHeader{Vendor: vendor},
		CompanyIds: []string{companyId},
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return "", errors.Wrap(err, "error while invoking GetCompanyLogos rpc")
	}
	if len(resp.GetLogos()) == 0 || len(resp.GetLogos()[0].GetLinks()) == 0 || resp.GetLogos()[0].GetLinks()[0].GetUrl() == "" {
		return "", epifierrors.ErrRecordNotFound
	}
	return resp.GetLogos()[0].GetLinks()[0].GetUrl(), nil
}
