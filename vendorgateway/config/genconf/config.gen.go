// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gendynconf "github.com/epifi/be-common/pkg/cfg/dynconf/genconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/vendorgateway/config"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_MaxAllowedTimeIntervalForMiniStatement int64
	_DowntimeConfig                         *syncmap.Map[string, *DowntimeConfig]
	_Application                            *Application
	_Flags                                  *Flags
	_RpcRateLimitConfig                     *gencfg.RateLimitConfig
	_Server                                 *config.Server
	_Logging                                *cfg.Logging
	_RedisOptions                           *cfg.RedisOptions
	_Aws                                    *config.Aws
	_Secrets                                *cfg.Secrets
	_DisputeSFTP                            *config.DisputeSFTP
	_SecureLogging                          *config.SecureLogging
	_HttpClientConfig                       *cfg.HttpClient
	_KarzaEPFPassbookHttpClientConfig       *cfg.HttpClient
	_DisputeHTTPClientConfig                *cfg.HttpClient
	_Freshdesk                              *cfg.Freshdesk
	_FcmAnalyticsLabel                      string
	_AwsSes                                 *config.AwsSes
	_HystrixConfig                          *config.HystrixConfig
	_XMLDigitalSignatureSigner              *cfg.XMLDigitalSignatureSigner
	_TimeoutConfig                          *config.TimeoutConfig
	_Tracing                                *cfg.Tracing
	_Profiling                              *cfg.Profiling
	_Crm                                    *cfg.Crm
	_DisputeConfig                          *config.DisputeConfig
	_VendorAddresses                        *config.VendorAddresses
	_FederalLien                            *config.FederalLien
	_PGPInMemoryEntityStoreParams           *cfg.PGPInMemoryEntityStoreParams
	_GrpcRatelimiterParams                  *cfg.GrpcRateLimiterParams
	_GrpcAttributeRatelimiterParams         *cfg.AttributeRateLimiterParams
	_RedactedRawRequestLogExceptionList     []string
	_RedactedRawResponseLogExceptionList    []string
	_VideoSdk                               *config.VideoSdk
	_Uqudo                                  *config.Uqudo
	_DCIssuance                             *config.DCIssuance
	_FederalAPICreds                        map[string]*config.FederalAPICreds
	_SkipServerCertVerification             bool
}

func (obj *Config) MaxAllowedTimeIntervalForMiniStatement() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._MaxAllowedTimeIntervalForMiniStatement))
}
func (obj *Config) DowntimeConfig() *syncmap.Map[string, *DowntimeConfig] {
	return obj._DowntimeConfig
}
func (obj *Config) Application() *Application {
	return obj._Application
}
func (obj *Config) Flags() *Flags {
	return obj._Flags
}
func (obj *Config) RpcRateLimitConfig() *gencfg.RateLimitConfig {
	return obj._RpcRateLimitConfig
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) Logging() *cfg.Logging {
	return obj._Logging
}
func (obj *Config) RedisOptions() *cfg.RedisOptions {
	return obj._RedisOptions
}
func (obj *Config) Aws() *config.Aws {
	return obj._Aws
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) DisputeSFTP() *config.DisputeSFTP {
	return obj._DisputeSFTP
}
func (obj *Config) SecureLogging() *config.SecureLogging {
	return obj._SecureLogging
}
func (obj *Config) HttpClientConfig() *cfg.HttpClient {
	return obj._HttpClientConfig
}
func (obj *Config) KarzaEPFPassbookHttpClientConfig() *cfg.HttpClient {
	return obj._KarzaEPFPassbookHttpClientConfig
}
func (obj *Config) DisputeHTTPClientConfig() *cfg.HttpClient {
	return obj._DisputeHTTPClientConfig
}
func (obj *Config) Freshdesk() *cfg.Freshdesk {
	return obj._Freshdesk
}
func (obj *Config) FcmAnalyticsLabel() string {
	return obj._FcmAnalyticsLabel
}
func (obj *Config) AwsSes() *config.AwsSes {
	return obj._AwsSes
}
func (obj *Config) HystrixConfig() *config.HystrixConfig {
	return obj._HystrixConfig
}
func (obj *Config) XMLDigitalSignatureSigner() *cfg.XMLDigitalSignatureSigner {
	return obj._XMLDigitalSignatureSigner
}
func (obj *Config) TimeoutConfig() *config.TimeoutConfig {
	return obj._TimeoutConfig
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) Profiling() *cfg.Profiling {
	return obj._Profiling
}
func (obj *Config) Crm() *cfg.Crm {
	return obj._Crm
}
func (obj *Config) DisputeConfig() *config.DisputeConfig {
	return obj._DisputeConfig
}
func (obj *Config) VendorAddresses() *config.VendorAddresses {
	return obj._VendorAddresses
}
func (obj *Config) FederalLien() *config.FederalLien {
	return obj._FederalLien
}
func (obj *Config) PGPInMemoryEntityStoreParams() *cfg.PGPInMemoryEntityStoreParams {
	return obj._PGPInMemoryEntityStoreParams
}
func (obj *Config) GrpcRatelimiterParams() *cfg.GrpcRateLimiterParams {
	return obj._GrpcRatelimiterParams
}
func (obj *Config) GrpcAttributeRatelimiterParams() *cfg.AttributeRateLimiterParams {
	return obj._GrpcAttributeRatelimiterParams
}
func (obj *Config) RedactedRawRequestLogExceptionList() []string {
	return obj._RedactedRawRequestLogExceptionList
}
func (obj *Config) RedactedRawResponseLogExceptionList() []string {
	return obj._RedactedRawResponseLogExceptionList
}
func (obj *Config) VideoSdk() *config.VideoSdk {
	return obj._VideoSdk
}
func (obj *Config) Uqudo() *config.Uqudo {
	return obj._Uqudo
}
func (obj *Config) DCIssuance() *config.DCIssuance {
	return obj._DCIssuance
}
func (obj *Config) FederalAPICreds() map[string]*config.FederalAPICreds {
	return obj._FederalAPICreds
}
func (obj *Config) SkipServerCertVerification() bool {
	return obj._SkipServerCertVerification
}

type Application struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InhouseMatchFaceRequestURL             string
	_InhouseMatchFaceRequestURLMutex        *sync.RWMutex
	_InhouseMatchFaceRequestURLV2           string
	_InhouseMatchFaceRequestURLV2Mutex      *sync.RWMutex
	_InhouseNameCheckUrl                    string
	_InhouseNameCheckUrlMutex               *sync.RWMutex
	_GoogleReverseGeocodingUrl              string
	_GoogleReverseGeocodingUrlMutex         *sync.RWMutex
	_GoogleGeocodingUrl                     string
	_GoogleGeocodingUrlMutex                *sync.RWMutex
	_InhouseRiskServiceURL                  string
	_InhouseRiskServiceURLMutex             *sync.RWMutex
	_InhouseRiskServiceURLV1                string
	_InhouseRiskServiceURLV1Mutex           *sync.RWMutex
	_InhouseReonboardingRiskServiceURL      string
	_InhouseReonboardingRiskServiceURLMutex *sync.RWMutex
	// Zenduty integration URL for sending alerts
	_ZendutyWebhookUrl                          string
	_ZendutyWebhookUrlMutex                     *sync.RWMutex
	_SmallCase                                  *SmallCase
	_Karza                                      *Karza
	_AA                                         *AA
	_FennelFeatureStore                         *FennelFeatureStore
	_Scienaptic                                 *Scienaptic
	_InhouseOCR                                 *InhouseOCR
	_Experian                                   *Experian
	_Alpaca                                     *Alpaca
	_AclSftp                                    *AclSftp
	_Lending                                    *Lending
	_LocationModel                              *LocationModelConfig
	_CasePrioritisationModel                    *CasePrioritisationModelConfig
	_IncomeEstimatorConf                        *IncomeEstimatorConfig
	_Saven                                      *Saven
	_FederalEscalation                          *FederalEscalation
	_Environment                                string
	_Name                                       string
	_IsSecureRedis                              bool
	_SyncWrapperTimeout                         int
	_VGAuthSvcSyncWrapperTimeout                int
	_IsStatementAPIEnabled                      bool
	_IsListKeysSimulated                        bool
	_CreateCustomerURL                          string
	_CreateLoanCustomerURL                      string
	_LoanCustomerCreationStatusURL              string
	_CheckCustomerStatusURL                     string
	_DedupeCheckURL                             string
	_FetchCustomerDetailsUrl                    string
	_EnquireVKYCStatusUrl                       string
	_CreateAccountURL                           string
	_CheckAccountStatusURL                      string
	_EnquireBalanceURL                          string
	_CkycSearchURL                              string
	_GetKycDataURL                              string
	_VerifyCkycOtpURL                           string
	_CreateDisputeURL                           string
	_DisputeStatusCheckUrl                      string
	_BulkDisputeStatusCheckUrl                  string
	_UploadDocumentUrl                          string
	_SendCorrespondenceUrl                      string
	_ChannelQuestionnaireUrl                    string
	_AccountTransactionsUrl                     string
	_PanProfileURL                              string
	_Federal                                    *config.Federal
	_LeadSquared                                *config.LeadSquared
	_M2P                                        *config.M2p
	_AAConsentRequestURL                        string
	_AAConsentHandleURL                         string
	_AAGetConsentStatusURL                      string
	_AARequestDataURL                           string
	_AAFetchDataURL                             string
	_CreateVirtualIdURL                         string
	_GetTokenURL                                string
	_DeviceRegistrationURL                      string
	_SetPINURL                                  string
	_GenerateUpiOtpURL                          string
	_ValidateAddressURL                         string
	_RespAuthDetailsURL                         string
	_RespAuthValCustURL                         string
	_RespAuthMandateURL                         string
	_RespMandateConfirmationURL                 string
	_ReqPayURL                                  string
	_ReqMandateURL                              string
	_RegisterMobileURL                          string
	_ListUpiKeyUrl                              string
	_ListAccountURL                             string
	_ListAccountProviderURL                     string
	_UPIBalanceEnquiryURL                       string
	_ReqComplaintURL                            string
	_ReqCheckComplaintStatusUrl                 string
	_ReqActivationUrl                           string
	_ListPspURL                                 string
	_RegMapperURL                               string
	_GetMapperInfoURL                           string
	_ReqValQRUrl                                string
	_RespMapperConfirmationURL                  string
	_PspHandleToUpiOrgIdMap                     *config.PspHandleToUpiOrgIdMap
	_FreshdeskAgentURL                          string
	_FreshdeskTicketURL                         string
	_FreshdeskFilterTicketURL                   string
	_FreshdeskSolutionsURL                      string
	_FreshdeskContactsURL                       string
	_FreshdeskTicketFieldURL                    string
	_RespTxnConfirmationURL                     string
	_RespValidateAddressURL                     string
	_ReqCheckTxnStatusURL                       string
	_FreshchatConversationURL                   string
	_FreshchatUserURL                           string
	_FreshchatAgentURL                          string
	_SenseforthEventURL                         string
	_ListVaeURL                                 string
	_GetUpiLiteURL                              string
	_SyncUpiLiteInfoURL                         string
	_Exotel                                     *config.Exotel
	_Twilio                                     *config.Twilio
	_GPlace                                     *config.Gplace
	_Loylty                                     *config.Loylty
	_Qwikcilver                                 *config.Qwikcilver
	_Thriwe                                     *config.Thriwe
	_Dreamfolks                                 *config.Dreamfolks
	_Poshvine                                   *config.Poshvine
	_Riskcovry                                  *config.Riskcovry
	_Onsurity                                   *config.Onsurity
	_AclEpifi                                   *config.AclEpifi
	_AclFederal                                 *config.AclFederal
	_AclEpifiOtp                                *config.AclEpifiOtp
	_KaleyraFederal                             *config.KaleyraFederal
	_KaleyraEpifi                               *config.KaleyraEpifi
	_KaleyraFederalCreditCard                   *config.KaleyraFederalCreditCard
	_KaleyraEpifiNR                             *config.KaleyraEpifiNR
	_KaleyraSmsCallbackURL                      string
	_NetCoreEpifi                               *config.NetCoreEpifi
	_AirtelFedSMS                               *config.AirtelFedSMS
	_AirtelEpifiSMS                             *config.AirtelEpifiSMS
	_AclWhatsapp                                *config.AclWhatsapp
	_Employment                                 *config.Employment
	_Roanuz                                     *config.Roanuz
	_IPStack                                    *config.IPStack
	_Shipway                                    *config.Shipway
	_CAMS                                       *config.Cams
	_Karvy                                      *config.Karvy
	_MFCentral                                  *config.MFCentral
	_InHouseAAParserURL                         string
	_InHouseAABulkParserURL                     string
	_FederalInternationalFundTransfer           *config.FederalInternationalFundTransfer
	_Veri5CheckLivenessRequestURL               string
	_Veri5MatchFaceRequestURL                   string
	_KarzaCheckLivenessRequestURL               string
	_KarzaLivenessCallbackURL                   string
	_KarzaMatchFaceRequestURL                   string
	_KarzaCheckPassiveLivenessRequestURL        string
	_KarzaCheckLivenessStatusURL                string
	_GetEPANKarzaStatusURL                      string
	_InhouseGetAndValidateEPANURL               string
	_InhouseCheckLivenessRequestURL             string
	_UseFormMarshalForKarza                     bool
	_UseFormMarshalForKarzaFM                   bool
	_SendAgentDataURL                           string
	_SendAuditorDataURL                         string
	_InhouseVerifyAndGetITRIntimationDetailsURL string
	_OzonetelManualDialUrl                      string
	_OzonetelUserName                           string
	_OzonetelCampaignName                       string
	_PayAckStatusCodeJson                       string
	_PayFundTransferStatusCodeJson              string
	_PayUpiStatusCodeJson                       string
	_EnachTransactionStatusCodeJson             string
	_DepositAckStatusCodeFilePath               string
	_DepositResponseStatusCodeFilePath          string
	_CreateCustomerCallBackUrl                  string
	_CreateAccountCallBackUrl                   string
	_CardResponseStatusCodeFilePath             string
	_Tiering                                    *config.Tiering
	_BouncyCastle                               *config.BouncyCastle
	_CvlKra                                     *config.CvlKra
	_NsdlKra                                    *config.NsdlKra
	_Ckyc                                       *config.Ckyc
	_FederalDeposit                             *config.FederalDeposit
	_Manch                                      *config.Manch
	_Digio                                      *config.Digio
	_WealthKarza                                *config.WealthKarza
	_Digilocker                                 *config.Digilocker
	_SIResponseStatusCodeFilePath               string
	_InhouseEmployerNameMatchUrl                string
	_InhouseEmployerNameCategoriserUrl          string
	_Cibil                                      *config.Cibil
	_DrivingLicenseValidationUrl                string
	_VoterIdValidationUrl                       string
	_Seon                                       *config.Seon
	_InhousePopularFAQUrl                       string
	_Liquiloans                                 *config.Liquiloans
	_Esign                                      *config.Esign
	_ProfileValidation                          *config.ProfileValidation
	_CvlSecrets                                 *config.CvlSecrets
	_BankAccountVerificationUrl                 string
	_MaxmindIp2CityUrlPrefix                    string
	_MaxmindSecrets                             *config.MaxmindSecrets
	_PAYUAffluenceURL                           string
	_BureauPhoneNumberDetailsUrl                string
	_BureauSecrets                              *config.BureauSecrets
	_DronapayHostURL                            string
	_SignzySecrets                              *config.SignzySecrets
	_Aml                                        *config.Aml
	_MorningStar                                *config.MorningStar
	_InhouseMerchantResolutionServiceUrl        string
	_FreshdeskAccountConfig                     map[string]map[string]*config.FreshdeskAccountConfig
	_FreshdeskURI                               *config.FreshdeskURI
	_Vistara                                    *config.Vistara
	_SlackTokens                                *config.SlackTokens
	_InhouseLocationServiceUrl                  string
	_VKYCAgentDashCred                          *config.VKYCAgentDashCred
	_Credgenics                                 *config.Credgenics
	_Vkyc                                       *config.Vkyc
	_EnachConfig                                *config.EnachConfig
	_CxFreshdeskTicketAttachmentsBucketName     string
	_InhouseBreForCCUrl                         string
	_EpanConfig                                 *config.EpanConfig
	_Razorpay                                   *config.Razorpay
	_PanValidationSecrets                       *config.PanValidationSecrets
	_BureauIdUrl                                string
	_BureauIdSecrets                            *config.BureauIdSecrets
	_Visa                                       *config.Visa
	_MoEngage                                   *config.MoEngage
	_PerfiosDigiLocker                          *config.PerfiosDigiLocker
	_SetU                                       *config.SetU
	_Bridgewise                                 *config.Bridgewise
	_Nps                                        *config.Nps
	_Nugget                                     *config.Nugget
}

func (obj *Application) InhouseMatchFaceRequestURL() string {
	obj._InhouseMatchFaceRequestURLMutex.RLock()
	defer obj._InhouseMatchFaceRequestURLMutex.RUnlock()
	return obj._InhouseMatchFaceRequestURL
}
func (obj *Application) InhouseMatchFaceRequestURLV2() string {
	obj._InhouseMatchFaceRequestURLV2Mutex.RLock()
	defer obj._InhouseMatchFaceRequestURLV2Mutex.RUnlock()
	return obj._InhouseMatchFaceRequestURLV2
}
func (obj *Application) InhouseNameCheckUrl() string {
	obj._InhouseNameCheckUrlMutex.RLock()
	defer obj._InhouseNameCheckUrlMutex.RUnlock()
	return obj._InhouseNameCheckUrl
}
func (obj *Application) GoogleReverseGeocodingUrl() string {
	obj._GoogleReverseGeocodingUrlMutex.RLock()
	defer obj._GoogleReverseGeocodingUrlMutex.RUnlock()
	return obj._GoogleReverseGeocodingUrl
}
func (obj *Application) GoogleGeocodingUrl() string {
	obj._GoogleGeocodingUrlMutex.RLock()
	defer obj._GoogleGeocodingUrlMutex.RUnlock()
	return obj._GoogleGeocodingUrl
}
func (obj *Application) InhouseRiskServiceURL() string {
	obj._InhouseRiskServiceURLMutex.RLock()
	defer obj._InhouseRiskServiceURLMutex.RUnlock()
	return obj._InhouseRiskServiceURL
}
func (obj *Application) InhouseRiskServiceURLV1() string {
	obj._InhouseRiskServiceURLV1Mutex.RLock()
	defer obj._InhouseRiskServiceURLV1Mutex.RUnlock()
	return obj._InhouseRiskServiceURLV1
}
func (obj *Application) InhouseReonboardingRiskServiceURL() string {
	obj._InhouseReonboardingRiskServiceURLMutex.RLock()
	defer obj._InhouseReonboardingRiskServiceURLMutex.RUnlock()
	return obj._InhouseReonboardingRiskServiceURL
}

// Zenduty integration URL for sending alerts
func (obj *Application) ZendutyWebhookUrl() string {
	obj._ZendutyWebhookUrlMutex.RLock()
	defer obj._ZendutyWebhookUrlMutex.RUnlock()
	return obj._ZendutyWebhookUrl
}
func (obj *Application) SmallCase() *SmallCase {
	return obj._SmallCase
}
func (obj *Application) Karza() *Karza {
	return obj._Karza
}
func (obj *Application) AA() *AA {
	return obj._AA
}
func (obj *Application) FennelFeatureStore() *FennelFeatureStore {
	return obj._FennelFeatureStore
}
func (obj *Application) Scienaptic() *Scienaptic {
	return obj._Scienaptic
}
func (obj *Application) InhouseOCR() *InhouseOCR {
	return obj._InhouseOCR
}
func (obj *Application) Experian() *Experian {
	return obj._Experian
}
func (obj *Application) Alpaca() *Alpaca {
	return obj._Alpaca
}
func (obj *Application) AclSftp() *AclSftp {
	return obj._AclSftp
}
func (obj *Application) Lending() *Lending {
	return obj._Lending
}
func (obj *Application) LocationModel() *LocationModelConfig {
	return obj._LocationModel
}
func (obj *Application) CasePrioritisationModel() *CasePrioritisationModelConfig {
	return obj._CasePrioritisationModel
}
func (obj *Application) IncomeEstimatorConf() *IncomeEstimatorConfig {
	return obj._IncomeEstimatorConf
}
func (obj *Application) Saven() *Saven {
	return obj._Saven
}
func (obj *Application) FederalEscalation() *FederalEscalation {
	return obj._FederalEscalation
}
func (obj *Application) Environment() string {
	return obj._Environment
}
func (obj *Application) Name() string {
	return obj._Name
}
func (obj *Application) IsSecureRedis() bool {
	return obj._IsSecureRedis
}
func (obj *Application) SyncWrapperTimeout() int {
	return obj._SyncWrapperTimeout
}
func (obj *Application) VGAuthSvcSyncWrapperTimeout() int {
	return obj._VGAuthSvcSyncWrapperTimeout
}
func (obj *Application) IsStatementAPIEnabled() bool {
	return obj._IsStatementAPIEnabled
}
func (obj *Application) IsListKeysSimulated() bool {
	return obj._IsListKeysSimulated
}
func (obj *Application) CreateCustomerURL() string {
	return obj._CreateCustomerURL
}
func (obj *Application) CreateLoanCustomerURL() string {
	return obj._CreateLoanCustomerURL
}
func (obj *Application) LoanCustomerCreationStatusURL() string {
	return obj._LoanCustomerCreationStatusURL
}
func (obj *Application) CheckCustomerStatusURL() string {
	return obj._CheckCustomerStatusURL
}
func (obj *Application) DedupeCheckURL() string {
	return obj._DedupeCheckURL
}
func (obj *Application) FetchCustomerDetailsUrl() string {
	return obj._FetchCustomerDetailsUrl
}
func (obj *Application) EnquireVKYCStatusUrl() string {
	return obj._EnquireVKYCStatusUrl
}
func (obj *Application) CreateAccountURL() string {
	return obj._CreateAccountURL
}
func (obj *Application) CheckAccountStatusURL() string {
	return obj._CheckAccountStatusURL
}
func (obj *Application) EnquireBalanceURL() string {
	return obj._EnquireBalanceURL
}
func (obj *Application) CkycSearchURL() string {
	return obj._CkycSearchURL
}
func (obj *Application) GetKycDataURL() string {
	return obj._GetKycDataURL
}
func (obj *Application) VerifyCkycOtpURL() string {
	return obj._VerifyCkycOtpURL
}
func (obj *Application) CreateDisputeURL() string {
	return obj._CreateDisputeURL
}
func (obj *Application) DisputeStatusCheckUrl() string {
	return obj._DisputeStatusCheckUrl
}
func (obj *Application) BulkDisputeStatusCheckUrl() string {
	return obj._BulkDisputeStatusCheckUrl
}
func (obj *Application) UploadDocumentUrl() string {
	return obj._UploadDocumentUrl
}
func (obj *Application) SendCorrespondenceUrl() string {
	return obj._SendCorrespondenceUrl
}
func (obj *Application) ChannelQuestionnaireUrl() string {
	return obj._ChannelQuestionnaireUrl
}
func (obj *Application) AccountTransactionsUrl() string {
	return obj._AccountTransactionsUrl
}
func (obj *Application) PanProfileURL() string {
	return obj._PanProfileURL
}
func (obj *Application) Federal() *config.Federal {
	return obj._Federal
}
func (obj *Application) LeadSquared() *config.LeadSquared {
	return obj._LeadSquared
}
func (obj *Application) M2P() *config.M2p {
	return obj._M2P
}
func (obj *Application) AAConsentRequestURL() string {
	return obj._AAConsentRequestURL
}
func (obj *Application) AAConsentHandleURL() string {
	return obj._AAConsentHandleURL
}
func (obj *Application) AAGetConsentStatusURL() string {
	return obj._AAGetConsentStatusURL
}
func (obj *Application) AARequestDataURL() string {
	return obj._AARequestDataURL
}
func (obj *Application) AAFetchDataURL() string {
	return obj._AAFetchDataURL
}
func (obj *Application) CreateVirtualIdURL() string {
	return obj._CreateVirtualIdURL
}
func (obj *Application) GetTokenURL() string {
	return obj._GetTokenURL
}
func (obj *Application) DeviceRegistrationURL() string {
	return obj._DeviceRegistrationURL
}
func (obj *Application) SetPINURL() string {
	return obj._SetPINURL
}
func (obj *Application) GenerateUpiOtpURL() string {
	return obj._GenerateUpiOtpURL
}
func (obj *Application) ValidateAddressURL() string {
	return obj._ValidateAddressURL
}
func (obj *Application) RespAuthDetailsURL() string {
	return obj._RespAuthDetailsURL
}
func (obj *Application) RespAuthValCustURL() string {
	return obj._RespAuthValCustURL
}
func (obj *Application) RespAuthMandateURL() string {
	return obj._RespAuthMandateURL
}
func (obj *Application) RespMandateConfirmationURL() string {
	return obj._RespMandateConfirmationURL
}
func (obj *Application) ReqPayURL() string {
	return obj._ReqPayURL
}
func (obj *Application) ReqMandateURL() string {
	return obj._ReqMandateURL
}
func (obj *Application) RegisterMobileURL() string {
	return obj._RegisterMobileURL
}
func (obj *Application) ListUpiKeyUrl() string {
	return obj._ListUpiKeyUrl
}
func (obj *Application) ListAccountURL() string {
	return obj._ListAccountURL
}
func (obj *Application) ListAccountProviderURL() string {
	return obj._ListAccountProviderURL
}
func (obj *Application) UPIBalanceEnquiryURL() string {
	return obj._UPIBalanceEnquiryURL
}
func (obj *Application) ReqComplaintURL() string {
	return obj._ReqComplaintURL
}
func (obj *Application) ReqCheckComplaintStatusUrl() string {
	return obj._ReqCheckComplaintStatusUrl
}
func (obj *Application) ReqActivationUrl() string {
	return obj._ReqActivationUrl
}
func (obj *Application) ListPspURL() string {
	return obj._ListPspURL
}
func (obj *Application) RegMapperURL() string {
	return obj._RegMapperURL
}
func (obj *Application) GetMapperInfoURL() string {
	return obj._GetMapperInfoURL
}
func (obj *Application) ReqValQRUrl() string {
	return obj._ReqValQRUrl
}
func (obj *Application) RespMapperConfirmationURL() string {
	return obj._RespMapperConfirmationURL
}
func (obj *Application) PspHandleToUpiOrgIdMap() *config.PspHandleToUpiOrgIdMap {
	return obj._PspHandleToUpiOrgIdMap
}
func (obj *Application) FreshdeskAgentURL() string {
	return obj._FreshdeskAgentURL
}
func (obj *Application) FreshdeskTicketURL() string {
	return obj._FreshdeskTicketURL
}
func (obj *Application) FreshdeskFilterTicketURL() string {
	return obj._FreshdeskFilterTicketURL
}
func (obj *Application) FreshdeskSolutionsURL() string {
	return obj._FreshdeskSolutionsURL
}
func (obj *Application) FreshdeskContactsURL() string {
	return obj._FreshdeskContactsURL
}
func (obj *Application) FreshdeskTicketFieldURL() string {
	return obj._FreshdeskTicketFieldURL
}
func (obj *Application) RespTxnConfirmationURL() string {
	return obj._RespTxnConfirmationURL
}
func (obj *Application) RespValidateAddressURL() string {
	return obj._RespValidateAddressURL
}
func (obj *Application) ReqCheckTxnStatusURL() string {
	return obj._ReqCheckTxnStatusURL
}
func (obj *Application) FreshchatConversationURL() string {
	return obj._FreshchatConversationURL
}
func (obj *Application) FreshchatUserURL() string {
	return obj._FreshchatUserURL
}
func (obj *Application) FreshchatAgentURL() string {
	return obj._FreshchatAgentURL
}
func (obj *Application) SenseforthEventURL() string {
	return obj._SenseforthEventURL
}
func (obj *Application) ListVaeURL() string {
	return obj._ListVaeURL
}
func (obj *Application) GetUpiLiteURL() string {
	return obj._GetUpiLiteURL
}
func (obj *Application) SyncUpiLiteInfoURL() string {
	return obj._SyncUpiLiteInfoURL
}
func (obj *Application) Exotel() *config.Exotel {
	return obj._Exotel
}
func (obj *Application) Twilio() *config.Twilio {
	return obj._Twilio
}
func (obj *Application) GPlace() *config.Gplace {
	return obj._GPlace
}
func (obj *Application) Loylty() *config.Loylty {
	return obj._Loylty
}
func (obj *Application) Qwikcilver() *config.Qwikcilver {
	return obj._Qwikcilver
}
func (obj *Application) Thriwe() *config.Thriwe {
	return obj._Thriwe
}
func (obj *Application) Dreamfolks() *config.Dreamfolks {
	return obj._Dreamfolks
}
func (obj *Application) Poshvine() *config.Poshvine {
	return obj._Poshvine
}
func (obj *Application) Riskcovry() *config.Riskcovry {
	return obj._Riskcovry
}
func (obj *Application) Onsurity() *config.Onsurity {
	return obj._Onsurity
}
func (obj *Application) AclEpifi() *config.AclEpifi {
	return obj._AclEpifi
}
func (obj *Application) AclFederal() *config.AclFederal {
	return obj._AclFederal
}
func (obj *Application) AclEpifiOtp() *config.AclEpifiOtp {
	return obj._AclEpifiOtp
}
func (obj *Application) KaleyraFederal() *config.KaleyraFederal {
	return obj._KaleyraFederal
}
func (obj *Application) KaleyraEpifi() *config.KaleyraEpifi {
	return obj._KaleyraEpifi
}
func (obj *Application) KaleyraFederalCreditCard() *config.KaleyraFederalCreditCard {
	return obj._KaleyraFederalCreditCard
}
func (obj *Application) KaleyraEpifiNR() *config.KaleyraEpifiNR {
	return obj._KaleyraEpifiNR
}
func (obj *Application) KaleyraSmsCallbackURL() string {
	return obj._KaleyraSmsCallbackURL
}
func (obj *Application) NetCoreEpifi() *config.NetCoreEpifi {
	return obj._NetCoreEpifi
}
func (obj *Application) AirtelFedSMS() *config.AirtelFedSMS {
	return obj._AirtelFedSMS
}
func (obj *Application) AirtelEpifiSMS() *config.AirtelEpifiSMS {
	return obj._AirtelEpifiSMS
}
func (obj *Application) AclWhatsapp() *config.AclWhatsapp {
	return obj._AclWhatsapp
}
func (obj *Application) Employment() *config.Employment {
	return obj._Employment
}
func (obj *Application) Roanuz() *config.Roanuz {
	return obj._Roanuz
}
func (obj *Application) IPStack() *config.IPStack {
	return obj._IPStack
}
func (obj *Application) Shipway() *config.Shipway {
	return obj._Shipway
}
func (obj *Application) CAMS() *config.Cams {
	return obj._CAMS
}
func (obj *Application) Karvy() *config.Karvy {
	return obj._Karvy
}
func (obj *Application) MFCentral() *config.MFCentral {
	return obj._MFCentral
}
func (obj *Application) InHouseAAParserURL() string {
	return obj._InHouseAAParserURL
}
func (obj *Application) InHouseAABulkParserURL() string {
	return obj._InHouseAABulkParserURL
}
func (obj *Application) FederalInternationalFundTransfer() *config.FederalInternationalFundTransfer {
	return obj._FederalInternationalFundTransfer
}
func (obj *Application) Veri5CheckLivenessRequestURL() string {
	return obj._Veri5CheckLivenessRequestURL
}
func (obj *Application) Veri5MatchFaceRequestURL() string {
	return obj._Veri5MatchFaceRequestURL
}
func (obj *Application) KarzaCheckLivenessRequestURL() string {
	return obj._KarzaCheckLivenessRequestURL
}
func (obj *Application) KarzaLivenessCallbackURL() string {
	return obj._KarzaLivenessCallbackURL
}
func (obj *Application) KarzaMatchFaceRequestURL() string {
	return obj._KarzaMatchFaceRequestURL
}
func (obj *Application) KarzaCheckPassiveLivenessRequestURL() string {
	return obj._KarzaCheckPassiveLivenessRequestURL
}
func (obj *Application) KarzaCheckLivenessStatusURL() string {
	return obj._KarzaCheckLivenessStatusURL
}
func (obj *Application) GetEPANKarzaStatusURL() string {
	return obj._GetEPANKarzaStatusURL
}
func (obj *Application) InhouseGetAndValidateEPANURL() string {
	return obj._InhouseGetAndValidateEPANURL
}
func (obj *Application) InhouseCheckLivenessRequestURL() string {
	return obj._InhouseCheckLivenessRequestURL
}
func (obj *Application) UseFormMarshalForKarza() bool {
	return obj._UseFormMarshalForKarza
}
func (obj *Application) UseFormMarshalForKarzaFM() bool {
	return obj._UseFormMarshalForKarzaFM
}
func (obj *Application) SendAgentDataURL() string {
	return obj._SendAgentDataURL
}
func (obj *Application) SendAuditorDataURL() string {
	return obj._SendAuditorDataURL
}
func (obj *Application) InhouseVerifyAndGetITRIntimationDetailsURL() string {
	return obj._InhouseVerifyAndGetITRIntimationDetailsURL
}
func (obj *Application) OzonetelManualDialUrl() string {
	return obj._OzonetelManualDialUrl
}
func (obj *Application) OzonetelUserName() string {
	return obj._OzonetelUserName
}
func (obj *Application) OzonetelCampaignName() string {
	return obj._OzonetelCampaignName
}
func (obj *Application) PayAckStatusCodeJson() string {
	return obj._PayAckStatusCodeJson
}
func (obj *Application) PayFundTransferStatusCodeJson() string {
	return obj._PayFundTransferStatusCodeJson
}
func (obj *Application) PayUpiStatusCodeJson() string {
	return obj._PayUpiStatusCodeJson
}
func (obj *Application) EnachTransactionStatusCodeJson() string {
	return obj._EnachTransactionStatusCodeJson
}
func (obj *Application) DepositAckStatusCodeFilePath() string {
	return obj._DepositAckStatusCodeFilePath
}
func (obj *Application) DepositResponseStatusCodeFilePath() string {
	return obj._DepositResponseStatusCodeFilePath
}
func (obj *Application) CreateCustomerCallBackUrl() string {
	return obj._CreateCustomerCallBackUrl
}
func (obj *Application) CreateAccountCallBackUrl() string {
	return obj._CreateAccountCallBackUrl
}
func (obj *Application) CardResponseStatusCodeFilePath() string {
	return obj._CardResponseStatusCodeFilePath
}
func (obj *Application) Tiering() *config.Tiering {
	return obj._Tiering
}
func (obj *Application) BouncyCastle() *config.BouncyCastle {
	return obj._BouncyCastle
}
func (obj *Application) CvlKra() *config.CvlKra {
	return obj._CvlKra
}
func (obj *Application) NsdlKra() *config.NsdlKra {
	return obj._NsdlKra
}
func (obj *Application) Ckyc() *config.Ckyc {
	return obj._Ckyc
}
func (obj *Application) FederalDeposit() *config.FederalDeposit {
	return obj._FederalDeposit
}
func (obj *Application) Manch() *config.Manch {
	return obj._Manch
}
func (obj *Application) Digio() *config.Digio {
	return obj._Digio
}
func (obj *Application) WealthKarza() *config.WealthKarza {
	return obj._WealthKarza
}
func (obj *Application) Digilocker() *config.Digilocker {
	return obj._Digilocker
}
func (obj *Application) SIResponseStatusCodeFilePath() string {
	return obj._SIResponseStatusCodeFilePath
}
func (obj *Application) InhouseEmployerNameMatchUrl() string {
	return obj._InhouseEmployerNameMatchUrl
}
func (obj *Application) InhouseEmployerNameCategoriserUrl() string {
	return obj._InhouseEmployerNameCategoriserUrl
}
func (obj *Application) Cibil() *config.Cibil {
	return obj._Cibil
}
func (obj *Application) DrivingLicenseValidationUrl() string {
	return obj._DrivingLicenseValidationUrl
}
func (obj *Application) VoterIdValidationUrl() string {
	return obj._VoterIdValidationUrl
}
func (obj *Application) Seon() *config.Seon {
	return obj._Seon
}
func (obj *Application) InhousePopularFAQUrl() string {
	return obj._InhousePopularFAQUrl
}
func (obj *Application) Liquiloans() *config.Liquiloans {
	return obj._Liquiloans
}
func (obj *Application) Esign() *config.Esign {
	return obj._Esign
}
func (obj *Application) ProfileValidation() *config.ProfileValidation {
	return obj._ProfileValidation
}
func (obj *Application) CvlSecrets() *config.CvlSecrets {
	return obj._CvlSecrets
}
func (obj *Application) BankAccountVerificationUrl() string {
	return obj._BankAccountVerificationUrl
}
func (obj *Application) MaxmindIp2CityUrlPrefix() string {
	return obj._MaxmindIp2CityUrlPrefix
}
func (obj *Application) MaxmindSecrets() *config.MaxmindSecrets {
	return obj._MaxmindSecrets
}
func (obj *Application) PAYUAffluenceURL() string {
	return obj._PAYUAffluenceURL
}
func (obj *Application) BureauPhoneNumberDetailsUrl() string {
	return obj._BureauPhoneNumberDetailsUrl
}
func (obj *Application) BureauSecrets() *config.BureauSecrets {
	return obj._BureauSecrets
}
func (obj *Application) DronapayHostURL() string {
	return obj._DronapayHostURL
}
func (obj *Application) SignzySecrets() *config.SignzySecrets {
	return obj._SignzySecrets
}
func (obj *Application) Aml() *config.Aml {
	return obj._Aml
}
func (obj *Application) MorningStar() *config.MorningStar {
	return obj._MorningStar
}
func (obj *Application) InhouseMerchantResolutionServiceUrl() string {
	return obj._InhouseMerchantResolutionServiceUrl
}
func (obj *Application) FreshdeskAccountConfig() map[string]map[string]*config.FreshdeskAccountConfig {
	return obj._FreshdeskAccountConfig
}
func (obj *Application) FreshdeskURI() *config.FreshdeskURI {
	return obj._FreshdeskURI
}
func (obj *Application) Vistara() *config.Vistara {
	return obj._Vistara
}
func (obj *Application) SlackTokens() *config.SlackTokens {
	return obj._SlackTokens
}
func (obj *Application) InhouseLocationServiceUrl() string {
	return obj._InhouseLocationServiceUrl
}
func (obj *Application) VKYCAgentDashCred() *config.VKYCAgentDashCred {
	return obj._VKYCAgentDashCred
}
func (obj *Application) Credgenics() *config.Credgenics {
	return obj._Credgenics
}
func (obj *Application) Vkyc() *config.Vkyc {
	return obj._Vkyc
}
func (obj *Application) EnachConfig() *config.EnachConfig {
	return obj._EnachConfig
}
func (obj *Application) CxFreshdeskTicketAttachmentsBucketName() string {
	return obj._CxFreshdeskTicketAttachmentsBucketName
}
func (obj *Application) InhouseBreForCCUrl() string {
	return obj._InhouseBreForCCUrl
}
func (obj *Application) EpanConfig() *config.EpanConfig {
	return obj._EpanConfig
}
func (obj *Application) Razorpay() *config.Razorpay {
	return obj._Razorpay
}
func (obj *Application) PanValidationSecrets() *config.PanValidationSecrets {
	return obj._PanValidationSecrets
}
func (obj *Application) BureauIdUrl() string {
	return obj._BureauIdUrl
}
func (obj *Application) BureauIdSecrets() *config.BureauIdSecrets {
	return obj._BureauIdSecrets
}
func (obj *Application) Visa() *config.Visa {
	return obj._Visa
}
func (obj *Application) MoEngage() *config.MoEngage {
	return obj._MoEngage
}
func (obj *Application) PerfiosDigiLocker() *config.PerfiosDigiLocker {
	return obj._PerfiosDigiLocker
}
func (obj *Application) SetU() *config.SetU {
	return obj._SetU
}
func (obj *Application) Bridgewise() *config.Bridgewise {
	return obj._Bridgewise
}
func (obj *Application) Nps() *config.Nps {
	return obj._Nps
}
func (obj *Application) Nugget() *config.Nugget {
	return obj._Nugget
}

type SmallCase struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// If true, the new NO_HOLDINGS_FOUND will be returned by the InitiateHoldingsImport API
	// TODO: clean this up once deployment is stable
	_EnableNotFoundInInitHoldingsImport uint32
	_CreateTransactionURL               string
	_InitiateHoldingsImportURL          string
	_TriggerHoldingsImportFetchURL      string
	_MFAnalyticsURL                     string
	_SmallCaseGateway                   string
}

// If true, the new NO_HOLDINGS_FOUND will be returned by the InitiateHoldingsImport API
// TODO: clean this up once deployment is stable
func (obj *SmallCase) EnableNotFoundInInitHoldingsImport() bool {
	if atomic.LoadUint32(&obj._EnableNotFoundInInitHoldingsImport) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *SmallCase) CreateTransactionURL() string {
	return obj._CreateTransactionURL
}
func (obj *SmallCase) InitiateHoldingsImportURL() string {
	return obj._InitiateHoldingsImportURL
}
func (obj *SmallCase) TriggerHoldingsImportFetchURL() string {
	return obj._TriggerHoldingsImportFetchURL
}
func (obj *SmallCase) MFAnalyticsURL() string {
	return obj._MFAnalyticsURL
}
func (obj *SmallCase) SmallCaseGateway() string {
	return obj._SmallCaseGateway
}

type Karza struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_KycOcrUrl                         string
	_KycOcrUrlMutex                    *sync.RWMutex
	_GenerateSessionTokenUrl           string
	_AddNewCustomerUrl                 string
	_AddNewCustomerV3Url               string
	_UpdateCustomerV3Url               string
	_GenerateCustomerTokenUrl          string
	_GetSlotUrl                        string
	_BookSlotUrl                       string
	_GenerateWebLinkUrl                string
	_SlotAgentsUrl                     string
	_TransactionStatusEnquiryUrl       string
	_ReScheduleSlotUrl                 string
	_TriggerCallback                   string
	_AgentDashboardUrl                 string
	_AgentDashboardAuthUrl             string
	_UpdateCustomerV3UATUrl            string
	_AddNewCustomerV3UATUrl            string
	_GenerateCustomerTokenUATUrl       string
	_GenerateWebLinkUATUrl             string
	_TransactionStatusEnquiryUATUrl    string
	_GenerateSessionTokenUATUrl        string
	_EmploymentVerificationAdvancedUrl string
	_PassportVerificationURL           string
	_Secrets                           *config.KarzaSecrets
}

func (obj *Karza) KycOcrUrl() string {
	obj._KycOcrUrlMutex.RLock()
	defer obj._KycOcrUrlMutex.RUnlock()
	return obj._KycOcrUrl
}
func (obj *Karza) GenerateSessionTokenUrl() string {
	return obj._GenerateSessionTokenUrl
}
func (obj *Karza) AddNewCustomerUrl() string {
	return obj._AddNewCustomerUrl
}
func (obj *Karza) AddNewCustomerV3Url() string {
	return obj._AddNewCustomerV3Url
}
func (obj *Karza) UpdateCustomerV3Url() string {
	return obj._UpdateCustomerV3Url
}
func (obj *Karza) GenerateCustomerTokenUrl() string {
	return obj._GenerateCustomerTokenUrl
}
func (obj *Karza) GetSlotUrl() string {
	return obj._GetSlotUrl
}
func (obj *Karza) BookSlotUrl() string {
	return obj._BookSlotUrl
}
func (obj *Karza) GenerateWebLinkUrl() string {
	return obj._GenerateWebLinkUrl
}
func (obj *Karza) SlotAgentsUrl() string {
	return obj._SlotAgentsUrl
}
func (obj *Karza) TransactionStatusEnquiryUrl() string {
	return obj._TransactionStatusEnquiryUrl
}
func (obj *Karza) ReScheduleSlotUrl() string {
	return obj._ReScheduleSlotUrl
}
func (obj *Karza) TriggerCallback() string {
	return obj._TriggerCallback
}
func (obj *Karza) AgentDashboardUrl() string {
	return obj._AgentDashboardUrl
}
func (obj *Karza) AgentDashboardAuthUrl() string {
	return obj._AgentDashboardAuthUrl
}
func (obj *Karza) UpdateCustomerV3UATUrl() string {
	return obj._UpdateCustomerV3UATUrl
}
func (obj *Karza) AddNewCustomerV3UATUrl() string {
	return obj._AddNewCustomerV3UATUrl
}
func (obj *Karza) GenerateCustomerTokenUATUrl() string {
	return obj._GenerateCustomerTokenUATUrl
}
func (obj *Karza) GenerateWebLinkUATUrl() string {
	return obj._GenerateWebLinkUATUrl
}
func (obj *Karza) TransactionStatusEnquiryUATUrl() string {
	return obj._TransactionStatusEnquiryUATUrl
}
func (obj *Karza) GenerateSessionTokenUATUrl() string {
	return obj._GenerateSessionTokenUATUrl
}
func (obj *Karza) EmploymentVerificationAdvancedUrl() string {
	return obj._EmploymentVerificationAdvancedUrl
}
func (obj *Karza) PassportVerificationURL() string {
	return obj._PassportVerificationURL
}
func (obj *Karza) Secrets() *config.KarzaSecrets {
	return obj._Secrets
}

type AA struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_UseSahamatiCrAndToken            uint32
	_IsOnemoneyV2Enabled              uint32
	_IsFinvuV2Enabled                 uint32
	_BaseURL                          string
	_BaseURLMutex                     *sync.RWMutex
	_PostConsentURL                   string
	_PostConsentURLMutex              *sync.RWMutex
	_ConsentStatusURL                 string
	_ConsentStatusURLMutex            *sync.RWMutex
	_ConsentArtefactURL               string
	_ConsentArtefactURLMutex          *sync.RWMutex
	_ConsentArtefactV2URL             string
	_ConsentArtefactV2URLMutex        *sync.RWMutex
	_RequestDataURL                   string
	_RequestDataURLMutex              *sync.RWMutex
	_FetchDataURL                     string
	_FetchDataURLMutex                *sync.RWMutex
	_GenerateAccessTokenURL           string
	_GenerateAccessTokenURLMutex      *sync.RWMutex
	_FetchCrEntityDetailURL           string
	_FetchCrEntityDetailURLMutex      *sync.RWMutex
	_FetchCrEntityDetailURLV2         string
	_FetchCrEntityDetailURLV2Mutex    *sync.RWMutex
	_GetAccountLinkStatusURL          string
	_GetAccountLinkStatusURLMutex     *sync.RWMutex
	_AccountDeLinkURL                 string
	_AccountDeLinkURLMutex            *sync.RWMutex
	_ConsentUpdateURL                 string
	_ConsentUpdateURLMutex            *sync.RWMutex
	_OneMoneyCrId                     string
	_OneMoneyCrIdMutex                *sync.RWMutex
	_FinvuCrId                        string
	_FinvuCrIdMutex                   *sync.RWMutex
	_GetAccountLinkStatusBulkURL      string
	_GetAccountLinkStatusBulkURLMutex *sync.RWMutex
	_GetHeartbeatStatusURL            string
	_GetHeartbeatStatusURLMutex       *sync.RWMutex
	_GetBulkConsentRequestURL         string
	_GetBulkConsentRequestURLMutex    *sync.RWMutex
	_GenerateFinvuJwtTokenURL         string
	_GenerateFinvuJwtTokenURLMutex    *sync.RWMutex
	_AAClientApiKey                   string
	_AAClientApiKeyMutex              *sync.RWMutex
	_SahamatiClientId                 string
	_SahamatiClientIdMutex            *sync.RWMutex
	_EpifiAaKid                       string
	_EpifiAaKidMutex                  *sync.RWMutex
	_AaSecretsVersionToUse            string
	_AaSecretsVersionToUseMutex       *sync.RWMutex
	_Ignosis                          *Ignosis
	_AaVgSecretsV1                    *config.AaVgSecrets
	_AaVgVnSecretsV1                  *config.AaVgVnSecrets
	_AaVgSecretsV2                    *config.AaVgSecrets
	_AaVgVnSecretsV2                  *config.AaVgVnSecrets
	_FinvuFipMetricsURL               string
}

func (obj *AA) UseSahamatiCrAndToken() bool {
	if atomic.LoadUint32(&obj._UseSahamatiCrAndToken) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AA) IsOnemoneyV2Enabled() bool {
	if atomic.LoadUint32(&obj._IsOnemoneyV2Enabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AA) IsFinvuV2Enabled() bool {
	if atomic.LoadUint32(&obj._IsFinvuV2Enabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *AA) BaseURL() string {
	obj._BaseURLMutex.RLock()
	defer obj._BaseURLMutex.RUnlock()
	return obj._BaseURL
}
func (obj *AA) PostConsentURL() string {
	obj._PostConsentURLMutex.RLock()
	defer obj._PostConsentURLMutex.RUnlock()
	return obj._PostConsentURL
}
func (obj *AA) ConsentStatusURL() string {
	obj._ConsentStatusURLMutex.RLock()
	defer obj._ConsentStatusURLMutex.RUnlock()
	return obj._ConsentStatusURL
}
func (obj *AA) ConsentArtefactURL() string {
	obj._ConsentArtefactURLMutex.RLock()
	defer obj._ConsentArtefactURLMutex.RUnlock()
	return obj._ConsentArtefactURL
}
func (obj *AA) ConsentArtefactV2URL() string {
	obj._ConsentArtefactV2URLMutex.RLock()
	defer obj._ConsentArtefactV2URLMutex.RUnlock()
	return obj._ConsentArtefactV2URL
}
func (obj *AA) RequestDataURL() string {
	obj._RequestDataURLMutex.RLock()
	defer obj._RequestDataURLMutex.RUnlock()
	return obj._RequestDataURL
}
func (obj *AA) FetchDataURL() string {
	obj._FetchDataURLMutex.RLock()
	defer obj._FetchDataURLMutex.RUnlock()
	return obj._FetchDataURL
}
func (obj *AA) GenerateAccessTokenURL() string {
	obj._GenerateAccessTokenURLMutex.RLock()
	defer obj._GenerateAccessTokenURLMutex.RUnlock()
	return obj._GenerateAccessTokenURL
}
func (obj *AA) FetchCrEntityDetailURL() string {
	obj._FetchCrEntityDetailURLMutex.RLock()
	defer obj._FetchCrEntityDetailURLMutex.RUnlock()
	return obj._FetchCrEntityDetailURL
}
func (obj *AA) FetchCrEntityDetailURLV2() string {
	obj._FetchCrEntityDetailURLV2Mutex.RLock()
	defer obj._FetchCrEntityDetailURLV2Mutex.RUnlock()
	return obj._FetchCrEntityDetailURLV2
}
func (obj *AA) GetAccountLinkStatusURL() string {
	obj._GetAccountLinkStatusURLMutex.RLock()
	defer obj._GetAccountLinkStatusURLMutex.RUnlock()
	return obj._GetAccountLinkStatusURL
}
func (obj *AA) AccountDeLinkURL() string {
	obj._AccountDeLinkURLMutex.RLock()
	defer obj._AccountDeLinkURLMutex.RUnlock()
	return obj._AccountDeLinkURL
}
func (obj *AA) ConsentUpdateURL() string {
	obj._ConsentUpdateURLMutex.RLock()
	defer obj._ConsentUpdateURLMutex.RUnlock()
	return obj._ConsentUpdateURL
}
func (obj *AA) OneMoneyCrId() string {
	obj._OneMoneyCrIdMutex.RLock()
	defer obj._OneMoneyCrIdMutex.RUnlock()
	return obj._OneMoneyCrId
}
func (obj *AA) FinvuCrId() string {
	obj._FinvuCrIdMutex.RLock()
	defer obj._FinvuCrIdMutex.RUnlock()
	return obj._FinvuCrId
}
func (obj *AA) GetAccountLinkStatusBulkURL() string {
	obj._GetAccountLinkStatusBulkURLMutex.RLock()
	defer obj._GetAccountLinkStatusBulkURLMutex.RUnlock()
	return obj._GetAccountLinkStatusBulkURL
}
func (obj *AA) GetHeartbeatStatusURL() string {
	obj._GetHeartbeatStatusURLMutex.RLock()
	defer obj._GetHeartbeatStatusURLMutex.RUnlock()
	return obj._GetHeartbeatStatusURL
}
func (obj *AA) GetBulkConsentRequestURL() string {
	obj._GetBulkConsentRequestURLMutex.RLock()
	defer obj._GetBulkConsentRequestURLMutex.RUnlock()
	return obj._GetBulkConsentRequestURL
}
func (obj *AA) GenerateFinvuJwtTokenURL() string {
	obj._GenerateFinvuJwtTokenURLMutex.RLock()
	defer obj._GenerateFinvuJwtTokenURLMutex.RUnlock()
	return obj._GenerateFinvuJwtTokenURL
}
func (obj *AA) AAClientApiKey() string {
	obj._AAClientApiKeyMutex.RLock()
	defer obj._AAClientApiKeyMutex.RUnlock()
	return obj._AAClientApiKey
}
func (obj *AA) SahamatiClientId() string {
	obj._SahamatiClientIdMutex.RLock()
	defer obj._SahamatiClientIdMutex.RUnlock()
	return obj._SahamatiClientId
}
func (obj *AA) EpifiAaKid() string {
	obj._EpifiAaKidMutex.RLock()
	defer obj._EpifiAaKidMutex.RUnlock()
	return obj._EpifiAaKid
}
func (obj *AA) AaSecretsVersionToUse() string {
	obj._AaSecretsVersionToUseMutex.RLock()
	defer obj._AaSecretsVersionToUseMutex.RUnlock()
	return obj._AaSecretsVersionToUse
}
func (obj *AA) Ignosis() *Ignosis {
	return obj._Ignosis
}
func (obj *AA) AaVgSecretsV1() *config.AaVgSecrets {
	return obj._AaVgSecretsV1
}
func (obj *AA) AaVgVnSecretsV1() *config.AaVgVnSecrets {
	return obj._AaVgVnSecretsV1
}
func (obj *AA) AaVgSecretsV2() *config.AaVgSecrets {
	return obj._AaVgSecretsV2
}
func (obj *AA) AaVgVnSecretsV2() *config.AaVgVnSecrets {
	return obj._AaVgVnSecretsV2
}
func (obj *AA) FinvuFipMetricsURL() string {
	return obj._FinvuFipMetricsURL
}

type Ignosis struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Url      string
	_UrlMutex *sync.RWMutex
}

func (obj *Ignosis) Url() string {
	obj._UrlMutex.RLock()
	defer obj._UrlMutex.RUnlock()
	return obj._Url
}

type FennelFeatureStore struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PdScoreURL               string
	_PdScoreURLMutex          *sync.RWMutex
	_Simulator                *FennelSimulatorConfig
	_ExtractFeatureSetsURLV2  string
	_ExtractFeatureSetsURLV3  string
	_LogDatasetsURL           string
	_LogDatasetsURLV2         string
	_LogDatasetsURLV3         string
	_FennelFeatureStoreSecret *config.FennelFeatureStoreSecret
}

func (obj *FennelFeatureStore) PdScoreURL() string {
	obj._PdScoreURLMutex.RLock()
	defer obj._PdScoreURLMutex.RUnlock()
	return obj._PdScoreURL
}
func (obj *FennelFeatureStore) Simulator() *FennelSimulatorConfig {
	return obj._Simulator
}
func (obj *FennelFeatureStore) ExtractFeatureSetsURLV2() string {
	return obj._ExtractFeatureSetsURLV2
}
func (obj *FennelFeatureStore) ExtractFeatureSetsURLV3() string {
	return obj._ExtractFeatureSetsURLV3
}
func (obj *FennelFeatureStore) LogDatasetsURL() string {
	return obj._LogDatasetsURL
}
func (obj *FennelFeatureStore) LogDatasetsURLV2() string {
	return obj._LogDatasetsURLV2
}
func (obj *FennelFeatureStore) LogDatasetsURLV3() string {
	return obj._LogDatasetsURLV3
}
func (obj *FennelFeatureStore) FennelFeatureStoreSecret() *config.FennelFeatureStoreSecret {
	return obj._FennelFeatureStoreSecret
}

type FennelSimulatorConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// flag to enable simulator for fennel
	_Enable                        uint32
	_ExtractFeatureSetsURL         string
	_LogDatasetsURL                string
	_AllowedWorkflowsForSimulation []string
}

// flag to enable simulator for fennel
func (obj *FennelSimulatorConfig) Enable() bool {
	if atomic.LoadUint32(&obj._Enable) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *FennelSimulatorConfig) ExtractFeatureSetsURL() string {
	return obj._ExtractFeatureSetsURL
}
func (obj *FennelSimulatorConfig) LogDatasetsURL() string {
	return obj._LogDatasetsURL
}
func (obj *FennelSimulatorConfig) AllowedWorkflowsForSimulation() []string {
	return obj._AllowedWorkflowsForSimulation
}

type Scienaptic struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_WhitelistedFeatures    *syncmap.Map[string, bool]
	_GenerateSmsFeaturesURL string
	_ScienapticSecrets      *config.ScienapticSecrets
}

func (obj *Scienaptic) WhitelistedFeatures() *syncmap.Map[string, bool] {
	return obj._WhitelistedFeatures
}
func (obj *Scienaptic) GenerateSmsFeaturesURL() string {
	return obj._GenerateSmsFeaturesURL
}
func (obj *Scienaptic) ScienapticSecrets() *config.ScienapticSecrets {
	return obj._ScienapticSecrets
}

type InhouseOCR struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_ExtractFieldsURLV2      string
	_ExtractFieldsURLV2Mutex *sync.RWMutex
	_DetectDocumentURL       string
	_DetectDocumentURLMutex  *sync.RWMutex
	_MaskDocURL              string
	_ExtractFieldsURL        string
	_ConfidenceThreshold     float64
}

func (obj *InhouseOCR) ExtractFieldsURLV2() string {
	obj._ExtractFieldsURLV2Mutex.RLock()
	defer obj._ExtractFieldsURLV2Mutex.RUnlock()
	return obj._ExtractFieldsURLV2
}
func (obj *InhouseOCR) DetectDocumentURL() string {
	obj._DetectDocumentURLMutex.RLock()
	defer obj._DetectDocumentURLMutex.RUnlock()
	return obj._DetectDocumentURL
}
func (obj *InhouseOCR) MaskDocURL() string {
	return obj._MaskDocURL
}
func (obj *InhouseOCR) ExtractFieldsURL() string {
	return obj._ExtractFieldsURL
}
func (obj *InhouseOCR) ConfidenceThreshold() float64 {
	return obj._ConfidenceThreshold
}

type Experian struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_V1VersionFlag                         uint32
	_CheckCreditReportPresenceURL          string
	_FetchCreditReportURL                  string
	_FetchCreditReportForExistingUserURL   string
	_FetchExtendSubscriptionURL            string
	_FetchAccessTokenUrl                   string
	_FetchCreditReportURLV1                string
	_FetchCreditReportForExistingUserURLV1 string
	_FetchExtendSubscriptionURLV1          string
	_ExperianSecrets                       *config.ExperianSecrets
}

func (obj *Experian) V1VersionFlag() bool {
	if atomic.LoadUint32(&obj._V1VersionFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Experian) CheckCreditReportPresenceURL() string {
	return obj._CheckCreditReportPresenceURL
}
func (obj *Experian) FetchCreditReportURL() string {
	return obj._FetchCreditReportURL
}
func (obj *Experian) FetchCreditReportForExistingUserURL() string {
	return obj._FetchCreditReportForExistingUserURL
}
func (obj *Experian) FetchExtendSubscriptionURL() string {
	return obj._FetchExtendSubscriptionURL
}
func (obj *Experian) FetchAccessTokenUrl() string {
	return obj._FetchAccessTokenUrl
}
func (obj *Experian) FetchCreditReportURLV1() string {
	return obj._FetchCreditReportURLV1
}
func (obj *Experian) FetchCreditReportForExistingUserURLV1() string {
	return obj._FetchCreditReportForExistingUserURLV1
}
func (obj *Experian) FetchExtendSubscriptionURLV1() string {
	return obj._FetchExtendSubscriptionURLV1
}
func (obj *Experian) ExperianSecrets() *config.ExperianSecrets {
	return obj._ExperianSecrets
}

type Alpaca struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// help in decide whether to use simulator in non prod env or not for websocket
	_ShouldUseSimulatedEnvForWs uint32
	// help in decide whether to use simulator in non prod env or not for event (stream)
	_ShouldUseSimulatedEnvForEvents uint32
	_Secret                         *config.AlpacaSecret
	_BrokerApiHost                  string
	_BrokerApiVersion               string
	_MarketApiHost                  string
	_MarketApiVersion               string
	_StreamApiHost                  string
	_StreamApiPath                  string
	_StreamApiScheme                string
	_OrderEventsApiPath             string
	_AccountEventsApiPath           string
	_BrokerEventsApiHost            string
	_BrokerEventsApiScheme          string
	_FundingConfig                  *config.FundingConfig
	_JournalEventsPath              string
	_FundTransferEventsPath         string
	_MarketDataBetaAPIPrefix        string
}

// help in decide whether to use simulator in non prod env or not for websocket
func (obj *Alpaca) ShouldUseSimulatedEnvForWs() bool {
	if atomic.LoadUint32(&obj._ShouldUseSimulatedEnvForWs) == 0 {
		return false
	} else {
		return true
	}
}

// help in decide whether to use simulator in non prod env or not for event (stream)
func (obj *Alpaca) ShouldUseSimulatedEnvForEvents() bool {
	if atomic.LoadUint32(&obj._ShouldUseSimulatedEnvForEvents) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Alpaca) Secret() *config.AlpacaSecret {
	return obj._Secret
}
func (obj *Alpaca) BrokerApiHost() string {
	return obj._BrokerApiHost
}
func (obj *Alpaca) BrokerApiVersion() string {
	return obj._BrokerApiVersion
}
func (obj *Alpaca) MarketApiHost() string {
	return obj._MarketApiHost
}
func (obj *Alpaca) MarketApiVersion() string {
	return obj._MarketApiVersion
}
func (obj *Alpaca) StreamApiHost() string {
	return obj._StreamApiHost
}
func (obj *Alpaca) StreamApiPath() string {
	return obj._StreamApiPath
}
func (obj *Alpaca) StreamApiScheme() string {
	return obj._StreamApiScheme
}
func (obj *Alpaca) OrderEventsApiPath() string {
	return obj._OrderEventsApiPath
}
func (obj *Alpaca) AccountEventsApiPath() string {
	return obj._AccountEventsApiPath
}
func (obj *Alpaca) BrokerEventsApiHost() string {
	return obj._BrokerEventsApiHost
}
func (obj *Alpaca) BrokerEventsApiScheme() string {
	return obj._BrokerEventsApiScheme
}
func (obj *Alpaca) FundingConfig() *config.FundingConfig {
	return obj._FundingConfig
}
func (obj *Alpaca) JournalEventsPath() string {
	return obj._JournalEventsPath
}
func (obj *Alpaca) FundTransferEventsPath() string {
	return obj._FundTransferEventsPath
}
func (obj *Alpaca) MarketDataBetaAPIPrefix() string {
	return obj._MarketDataBetaAPIPrefix
}

type AclSftp struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Port          int64
	_User          string
	_UserMutex     *sync.RWMutex
	_Host          string
	_HostMutex     *sync.RWMutex
	_Password      string
	_PasswordMutex *sync.RWMutex
	_SshKey        string
	_SshKeyMutex   *sync.RWMutex
}

func (obj *AclSftp) Port() int {
	return int(atomic.LoadInt64(&obj._Port))
}
func (obj *AclSftp) User() string {
	obj._UserMutex.RLock()
	defer obj._UserMutex.RUnlock()
	return obj._User
}
func (obj *AclSftp) Host() string {
	obj._HostMutex.RLock()
	defer obj._HostMutex.RUnlock()
	return obj._Host
}
func (obj *AclSftp) Password() string {
	obj._PasswordMutex.RLock()
	defer obj._PasswordMutex.RUnlock()
	return obj._Password
}
func (obj *AclSftp) SshKey() string {
	obj._SshKeyMutex.RLock()
	defer obj._SshKeyMutex.RUnlock()
	return obj._SshKey
}

type Lending struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_PreApprovedLoan *PreApprovedLoan
	_CreditCard      *config.CreditCard
	_CreditLine      *config.CreditLine
	_Collateral      *config.Collateral
	_SecuredLoans    *config.SecuredLoans
}

func (obj *Lending) PreApprovedLoan() *PreApprovedLoan {
	return obj._PreApprovedLoan
}
func (obj *Lending) CreditCard() *config.CreditCard {
	return obj._CreditCard
}
func (obj *Lending) CreditLine() *config.CreditLine {
	return obj._CreditLine
}
func (obj *Lending) Collateral() *config.Collateral {
	return obj._Collateral
}
func (obj *Lending) SecuredLoans() *config.SecuredLoans {
	return obj._SecuredLoans
}

type PreApprovedLoan struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Idfc       *Idfc
	_Federal    *config.FederalPreapprovedLoan
	_FederalNTB *config.FederalNTBLoanCredentials
	_Liquiloans *config.LiquiloansPreapprovedLoan
	_Lentra     *config.Lentra
	_Abfl       *config.Abfl
	_Moneyview  *config.Moneyview
	_Finflux    *config.Finflux
	_Setu       *config.Setu
	_Digitap    *config.Digitap
	_Lenden     *config.Lenden
}

func (obj *PreApprovedLoan) Idfc() *Idfc {
	return obj._Idfc
}
func (obj *PreApprovedLoan) Federal() *config.FederalPreapprovedLoan {
	return obj._Federal
}
func (obj *PreApprovedLoan) FederalNTB() *config.FederalNTBLoanCredentials {
	return obj._FederalNTB
}
func (obj *PreApprovedLoan) Liquiloans() *config.LiquiloansPreapprovedLoan {
	return obj._Liquiloans
}
func (obj *PreApprovedLoan) Lentra() *config.Lentra {
	return obj._Lentra
}
func (obj *PreApprovedLoan) Abfl() *config.Abfl {
	return obj._Abfl
}
func (obj *PreApprovedLoan) Moneyview() *config.Moneyview {
	return obj._Moneyview
}
func (obj *PreApprovedLoan) Finflux() *config.Finflux {
	return obj._Finflux
}
func (obj *PreApprovedLoan) Setu() *config.Setu {
	return obj._Setu
}
func (obj *PreApprovedLoan) Digitap() *config.Digitap {
	return obj._Digitap
}
func (obj *PreApprovedLoan) Lenden() *config.Lenden {
	return obj._Lenden
}

type Idfc struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Url                          string
	_UrlMutex                     *sync.RWMutex
	_GetAccessTokenUrl            string
	_GetAccessTokenUrlMutex       *sync.RWMutex
	_MandatePageUrl               string
	_MandatePageUrlMutex          *sync.RWMutex
	_EnableEncryption             bool
	_SecretKey                    string
	_Iv                           string
	_PrivateKey                   string
	_ClientId                     string
	_Kid                          string
	_CorrelationId                string
	_Source                       string
	_MerchantCode                 string
	_MandateRegistrationIv        string
	_MandateRegistrationSecretKey string
	_BankCode                     string
	_SimulatorHttpURL             string
}

func (obj *Idfc) Url() string {
	obj._UrlMutex.RLock()
	defer obj._UrlMutex.RUnlock()
	return obj._Url
}
func (obj *Idfc) GetAccessTokenUrl() string {
	obj._GetAccessTokenUrlMutex.RLock()
	defer obj._GetAccessTokenUrlMutex.RUnlock()
	return obj._GetAccessTokenUrl
}
func (obj *Idfc) MandatePageUrl() string {
	obj._MandatePageUrlMutex.RLock()
	defer obj._MandatePageUrlMutex.RUnlock()
	return obj._MandatePageUrl
}
func (obj *Idfc) EnableEncryption() bool {
	return obj._EnableEncryption
}
func (obj *Idfc) SecretKey() string {
	return obj._SecretKey
}
func (obj *Idfc) Iv() string {
	return obj._Iv
}
func (obj *Idfc) PrivateKey() string {
	return obj._PrivateKey
}
func (obj *Idfc) ClientId() string {
	return obj._ClientId
}
func (obj *Idfc) Kid() string {
	return obj._Kid
}
func (obj *Idfc) CorrelationId() string {
	return obj._CorrelationId
}
func (obj *Idfc) Source() string {
	return obj._Source
}
func (obj *Idfc) MerchantCode() string {
	return obj._MerchantCode
}
func (obj *Idfc) MandateRegistrationIv() string {
	return obj._MandateRegistrationIv
}
func (obj *Idfc) MandateRegistrationSecretKey() string {
	return obj._MandateRegistrationSecretKey
}
func (obj *Idfc) BankCode() string {
	return obj._BankCode
}
func (obj *Idfc) SimulatorHttpURL() string {
	return obj._SimulatorHttpURL
}

type LocationModelConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InHouseUrl                   string
	_InHouseUrlMutex              *sync.RWMutex
	_RiskSeverityValToEnumMapping map[string]string
}

func (obj *LocationModelConfig) InHouseUrl() string {
	obj._InHouseUrlMutex.RLock()
	defer obj._InHouseUrlMutex.RUnlock()
	return obj._InHouseUrl
}
func (obj *LocationModelConfig) RiskSeverityValToEnumMapping() map[string]string {
	return obj._RiskSeverityValToEnumMapping
}

type CasePrioritisationModelConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InHouseUrl      string
	_InHouseUrlMutex *sync.RWMutex
}

func (obj *CasePrioritisationModelConfig) InHouseUrl() string {
	obj._InHouseUrlMutex.RLock()
	defer obj._InHouseUrlMutex.RUnlock()
	return obj._InHouseUrl
}

type IncomeEstimatorConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_InhouseIncomeEstimatorURL      string
	_InhouseIncomeEstimatorURLMutex *sync.RWMutex
}

func (obj *IncomeEstimatorConfig) InhouseIncomeEstimatorURL() string {
	obj._InhouseIncomeEstimatorURLMutex.RLock()
	defer obj._InhouseIncomeEstimatorURLMutex.RUnlock()
	return obj._InhouseIncomeEstimatorURL
}

type Saven struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_JwtExpiry         int64
	_SavenSecrets      *config.SavenSecrets
	_CreditCardBaseUrl string
}

func (obj *Saven) JwtExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._JwtExpiry))
}
func (obj *Saven) SavenSecrets() *config.SavenSecrets {
	return obj._SavenSecrets
}
func (obj *Saven) CreditCardBaseUrl() string {
	return obj._CreditCardBaseUrl
}

type FederalEscalation struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_BaseURL                  string
	_BaseURLMutex             *sync.RWMutex
	_CreateEscalationURL      string
	_CreateEscalationURLMutex *sync.RWMutex
	_BulkFetchURL             string
	_BulkFetchURLMutex        *sync.RWMutex
	_FederalEscalationSecrets *config.FederalEscalationSecret
	_S3BucketName             string
}

func (obj *FederalEscalation) BaseURL() string {
	obj._BaseURLMutex.RLock()
	defer obj._BaseURLMutex.RUnlock()
	return obj._BaseURL
}
func (obj *FederalEscalation) CreateEscalationURL() string {
	obj._CreateEscalationURLMutex.RLock()
	defer obj._CreateEscalationURLMutex.RUnlock()
	return obj._CreateEscalationURL
}
func (obj *FederalEscalation) BulkFetchURL() string {
	obj._BulkFetchURLMutex.RLock()
	defer obj._BulkFetchURLMutex.RUnlock()
	return obj._BulkFetchURL
}
func (obj *FederalEscalation) FederalEscalationSecrets() *config.FederalEscalationSecret {
	return obj._FederalEscalationSecrets
}
func (obj *FederalEscalation) S3BucketName() string {
	return obj._S3BucketName
}

type Flags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Log the raw request and response of all the APIs including the final proto message after unmarshalling.
	// The logs are enabled only in non prod envs
	_LogAllAPIParams uint32
	// Flag to use newly captured employmentData.OccupationType in CIF creation
	_UseNewOccupationInCifCreation uint32
	// Flag to use new fields like CustomerStatus, Category, Pep and SubCategoryOccupation in CIF creation
	_UseNewFieldsInCifCreation uint32
	// Flag to use new fields like SourceOfFunds, AnnualTxnVolume in account creation
	_UseNewFieldsInAccountCreation uint32
	// flag to use new transaction status enquiry api
	_EnableTransactionEnquiryNewApi     uint32
	_EnableUATForVKYC                   uint32
	_EnableInstrumentBillingInterceptor uint32
	// Disable Gst reporting for ift
	_DisableGstReportingForIFT uint32
	_EnableFennelClusterV3     uint32
	// Enable Cibil V2 secrets
	_EnableCibilV2Secrets                     uint32
	_TrimDebugMessageFromStatus               bool
	_TokenValidation                          bool
	_UseAsyncNotificationCallback             bool
	_UseCustomTrustedCertPool                 bool
	_SkipCertVerifyPANValidation              bool
	_AllowSpecialCharactersInAddress          bool
	_EnableFederalCardDecryptionByFallbackKey bool
	_UseNewSolID                              bool
}

// Log the raw request and response of all the APIs including the final proto message after unmarshalling.
// The logs are enabled only in non prod envs
func (obj *Flags) LogAllAPIParams() bool {
	if atomic.LoadUint32(&obj._LogAllAPIParams) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to use newly captured employmentData.OccupationType in CIF creation
func (obj *Flags) UseNewOccupationInCifCreation() bool {
	if atomic.LoadUint32(&obj._UseNewOccupationInCifCreation) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to use new fields like CustomerStatus, Category, Pep and SubCategoryOccupation in CIF creation
func (obj *Flags) UseNewFieldsInCifCreation() bool {
	if atomic.LoadUint32(&obj._UseNewFieldsInCifCreation) == 0 {
		return false
	} else {
		return true
	}
}

// Flag to use new fields like SourceOfFunds, AnnualTxnVolume in account creation
func (obj *Flags) UseNewFieldsInAccountCreation() bool {
	if atomic.LoadUint32(&obj._UseNewFieldsInAccountCreation) == 0 {
		return false
	} else {
		return true
	}
}

// flag to use new transaction status enquiry api
func (obj *Flags) EnableTransactionEnquiryNewApi() bool {
	if atomic.LoadUint32(&obj._EnableTransactionEnquiryNewApi) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableUATForVKYC() bool {
	if atomic.LoadUint32(&obj._EnableUATForVKYC) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableInstrumentBillingInterceptor() bool {
	if atomic.LoadUint32(&obj._EnableInstrumentBillingInterceptor) == 0 {
		return false
	} else {
		return true
	}
}

// Disable Gst reporting for ift
func (obj *Flags) DisableGstReportingForIFT() bool {
	if atomic.LoadUint32(&obj._DisableGstReportingForIFT) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) EnableFennelClusterV3() bool {
	if atomic.LoadUint32(&obj._EnableFennelClusterV3) == 0 {
		return false
	} else {
		return true
	}
}

// Enable Cibil V2 secrets
func (obj *Flags) EnableCibilV2Secrets() bool {
	if atomic.LoadUint32(&obj._EnableCibilV2Secrets) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Flags) TrimDebugMessageFromStatus() bool {
	return obj._TrimDebugMessageFromStatus
}
func (obj *Flags) TokenValidation() bool {
	return obj._TokenValidation
}
func (obj *Flags) UseAsyncNotificationCallback() bool {
	return obj._UseAsyncNotificationCallback
}
func (obj *Flags) UseCustomTrustedCertPool() bool {
	return obj._UseCustomTrustedCertPool
}
func (obj *Flags) SkipCertVerifyPANValidation() bool {
	return obj._SkipCertVerifyPANValidation
}
func (obj *Flags) AllowSpecialCharactersInAddress() bool {
	return obj._AllowSpecialCharactersInAddress
}
func (obj *Flags) EnableFederalCardDecryptionByFallbackKey() bool {
	return obj._EnableFederalCardDecryptionByFallbackKey
}
func (obj *Flags) UseNewSolID() bool {
	return obj._UseNewSolID
}

type DowntimeConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Downtime *Downtime
}

func (obj *DowntimeConfig) Downtime() *Downtime {
	return obj._Downtime
}

type Downtime struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_DailyDowntime          *gendynconf.RecurringDowntimePeriod
	_TimestampBasedDowntime *gendynconf.TimestampBasedDowntime
}

func (obj *Downtime) DailyDowntime() *gendynconf.RecurringDowntimePeriod {
	return obj._DailyDowntime
}
func (obj *Downtime) TimestampBasedDowntime() *gendynconf.TimestampBasedDowntime {
	return obj._TimestampBasedDowntime
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxallowedtimeintervalforministatement"] = _obj.SetMaxAllowedTimeIntervalForMiniStatement

	_obj._DowntimeConfig = &syncmap.Map[string, *DowntimeConfig]{}
	_setters["downtimeconfig"] = _obj.SetDowntimeConfig
	_Application, _fieldSetters := NewApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_RpcRateLimitConfig, _fieldSetters := gencfg.NewRateLimitConfig()
	_obj._RpcRateLimitConfig = _RpcRateLimitConfig
	helper.AddFieldSetters("rpcratelimitconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["maxallowedtimeintervalforministatement"] = _obj.SetMaxAllowedTimeIntervalForMiniStatement

	_obj._DowntimeConfig = &syncmap.Map[string, *DowntimeConfig]{}
	_setters["downtimeconfig"] = _obj.SetDowntimeConfig
	_Application, _fieldSetters := NewApplication()
	_obj._Application = _Application
	helper.AddFieldSetters("application", _fieldSetters, _setters)
	_Flags, _fieldSetters := NewFlags()
	_obj._Flags = _Flags
	helper.AddFieldSetters("flags", _fieldSetters, _setters)
	_RpcRateLimitConfig, _fieldSetters := gencfg.NewRateLimitConfig()
	_obj._RpcRateLimitConfig = _RpcRateLimitConfig
	helper.AddFieldSetters("rpcratelimitconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "maxallowedtimeintervalforministatement":
		return obj.SetMaxAllowedTimeIntervalForMiniStatement(v.MaxAllowedTimeIntervalForMiniStatement, true, nil)
	case "downtimeconfig":
		return obj.SetDowntimeConfig(v.DowntimeConfig, true, path)
	case "application":
		return obj._Application.Set(v.Application, true, path)
	case "flags":
		return obj._Flags.Set(v.Flags, true, path)
	case "rpcratelimitconfig":
		return obj._RpcRateLimitConfig.Set(v.RpcRateLimitConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetMaxAllowedTimeIntervalForMiniStatement(v.MaxAllowedTimeIntervalForMiniStatement, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDowntimeConfig(v.DowntimeConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Application.Set(v.Application, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Flags.Set(v.Flags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RpcRateLimitConfig.Set(v.RpcRateLimitConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Server = v.Server
	obj._Logging = v.Logging
	obj._RedisOptions = v.RedisOptions
	obj._Aws = v.Aws
	obj._Secrets = v.Secrets
	obj._DisputeSFTP = v.DisputeSFTP
	obj._SecureLogging = v.SecureLogging
	obj._HttpClientConfig = v.HttpClientConfig
	obj._KarzaEPFPassbookHttpClientConfig = v.KarzaEPFPassbookHttpClientConfig
	obj._DisputeHTTPClientConfig = v.DisputeHTTPClientConfig
	obj._Freshdesk = v.Freshdesk
	obj._FcmAnalyticsLabel = v.FcmAnalyticsLabel
	obj._AwsSes = v.AwsSes
	obj._HystrixConfig = v.HystrixConfig
	obj._XMLDigitalSignatureSigner = v.XMLDigitalSignatureSigner
	obj._TimeoutConfig = v.TimeoutConfig
	obj._Tracing = v.Tracing
	obj._Profiling = v.Profiling
	obj._Crm = v.Crm
	obj._DisputeConfig = v.DisputeConfig
	obj._VendorAddresses = v.VendorAddresses
	obj._FederalLien = v.FederalLien
	obj._PGPInMemoryEntityStoreParams = v.PGPInMemoryEntityStoreParams
	obj._GrpcRatelimiterParams = v.GrpcRatelimiterParams
	obj._GrpcAttributeRatelimiterParams = v.GrpcAttributeRatelimiterParams
	obj._RedactedRawRequestLogExceptionList = v.RedactedRawRequestLogExceptionList
	obj._RedactedRawResponseLogExceptionList = v.RedactedRawResponseLogExceptionList
	obj._VideoSdk = v.VideoSdk
	obj._Uqudo = v.Uqudo
	obj._DCIssuance = v.DCIssuance
	obj._FederalAPICreds = v.FederalAPICreds
	obj._SkipServerCertVerification = v.SkipServerCertVerification
	return nil
}

func (obj *Config) SetMaxAllowedTimeIntervalForMiniStatement(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.MaxAllowedTimeIntervalForMiniStatement", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._MaxAllowedTimeIntervalForMiniStatement, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MaxAllowedTimeIntervalForMiniStatement")
	}
	return nil
}
func (obj *Config) SetDowntimeConfig(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.DowntimeConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.DowntimeConfig", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._DowntimeConfig, v, dynamic, path)

}

func NewApplication() (_obj *Application, _setters map[string]dynconf.SetFunc) {
	_obj = &Application{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["inhousematchfacerequesturl"] = _obj.SetInhouseMatchFaceRequestURL
	_obj._InhouseMatchFaceRequestURLMutex = &sync.RWMutex{}
	_setters["inhousematchfacerequesturlv2"] = _obj.SetInhouseMatchFaceRequestURLV2
	_obj._InhouseMatchFaceRequestURLV2Mutex = &sync.RWMutex{}
	_setters["inhousenamecheckurl"] = _obj.SetInhouseNameCheckUrl
	_obj._InhouseNameCheckUrlMutex = &sync.RWMutex{}
	_setters["googlereversegeocodingurl"] = _obj.SetGoogleReverseGeocodingUrl
	_obj._GoogleReverseGeocodingUrlMutex = &sync.RWMutex{}
	_setters["googlegeocodingurl"] = _obj.SetGoogleGeocodingUrl
	_obj._GoogleGeocodingUrlMutex = &sync.RWMutex{}
	_setters["inhouseriskserviceurl"] = _obj.SetInhouseRiskServiceURL
	_obj._InhouseRiskServiceURLMutex = &sync.RWMutex{}
	_setters["inhouseriskserviceurlv1"] = _obj.SetInhouseRiskServiceURLV1
	_obj._InhouseRiskServiceURLV1Mutex = &sync.RWMutex{}
	_setters["inhousereonboardingriskserviceurl"] = _obj.SetInhouseReonboardingRiskServiceURL
	_obj._InhouseReonboardingRiskServiceURLMutex = &sync.RWMutex{}
	_setters["zendutywebhookurl"] = _obj.SetZendutyWebhookUrl
	_obj._ZendutyWebhookUrlMutex = &sync.RWMutex{}
	_SmallCase, _fieldSetters := NewSmallCase()
	_obj._SmallCase = _SmallCase
	helper.AddFieldSetters("smallcase", _fieldSetters, _setters)
	_Karza, _fieldSetters := NewKarza()
	_obj._Karza = _Karza
	helper.AddFieldSetters("karza", _fieldSetters, _setters)
	_AA, _fieldSetters := NewAA()
	_obj._AA = _AA
	helper.AddFieldSetters("aa", _fieldSetters, _setters)
	_FennelFeatureStore, _fieldSetters := NewFennelFeatureStore()
	_obj._FennelFeatureStore = _FennelFeatureStore
	helper.AddFieldSetters("fennelfeaturestore", _fieldSetters, _setters)
	_Scienaptic, _fieldSetters := NewScienaptic()
	_obj._Scienaptic = _Scienaptic
	helper.AddFieldSetters("scienaptic", _fieldSetters, _setters)
	_InhouseOCR, _fieldSetters := NewInhouseOCR()
	_obj._InhouseOCR = _InhouseOCR
	helper.AddFieldSetters("inhouseocr", _fieldSetters, _setters)
	_Experian, _fieldSetters := NewExperian()
	_obj._Experian = _Experian
	helper.AddFieldSetters("experian", _fieldSetters, _setters)
	_Alpaca, _fieldSetters := NewAlpaca()
	_obj._Alpaca = _Alpaca
	helper.AddFieldSetters("alpaca", _fieldSetters, _setters)
	_AclSftp, _fieldSetters := NewAclSftp()
	_obj._AclSftp = _AclSftp
	helper.AddFieldSetters("aclsftp", _fieldSetters, _setters)
	_Lending, _fieldSetters := NewLending()
	_obj._Lending = _Lending
	helper.AddFieldSetters("lending", _fieldSetters, _setters)
	_LocationModel, _fieldSetters := NewLocationModelConfig()
	_obj._LocationModel = _LocationModel
	helper.AddFieldSetters("locationmodel", _fieldSetters, _setters)
	_CasePrioritisationModel, _fieldSetters := NewCasePrioritisationModelConfig()
	_obj._CasePrioritisationModel = _CasePrioritisationModel
	helper.AddFieldSetters("caseprioritisationmodel", _fieldSetters, _setters)
	_IncomeEstimatorConf, _fieldSetters := NewIncomeEstimatorConfig()
	_obj._IncomeEstimatorConf = _IncomeEstimatorConf
	helper.AddFieldSetters("incomeestimatorconf", _fieldSetters, _setters)
	_Saven, _fieldSetters := NewSaven()
	_obj._Saven = _Saven
	helper.AddFieldSetters("saven", _fieldSetters, _setters)
	_FederalEscalation, _fieldSetters := NewFederalEscalation()
	_obj._FederalEscalation = _FederalEscalation
	helper.AddFieldSetters("federalescalation", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Application) Init() {
	newObj, _ := NewApplication()
	*obj = *newObj
}

func (obj *Application) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Application) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Application)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Application) setDynamicField(v *config.Application, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "inhousematchfacerequesturl":
		return obj.SetInhouseMatchFaceRequestURL(v.InhouseMatchFaceRequestURL, true, nil)
	case "inhousematchfacerequesturlv2":
		return obj.SetInhouseMatchFaceRequestURLV2(v.InhouseMatchFaceRequestURLV2, true, nil)
	case "inhousenamecheckurl":
		return obj.SetInhouseNameCheckUrl(v.InhouseNameCheckUrl, true, nil)
	case "googlereversegeocodingurl":
		return obj.SetGoogleReverseGeocodingUrl(v.GoogleReverseGeocodingUrl, true, nil)
	case "googlegeocodingurl":
		return obj.SetGoogleGeocodingUrl(v.GoogleGeocodingUrl, true, nil)
	case "inhouseriskserviceurl":
		return obj.SetInhouseRiskServiceURL(v.InhouseRiskServiceURL, true, nil)
	case "inhouseriskserviceurlv1":
		return obj.SetInhouseRiskServiceURLV1(v.InhouseRiskServiceURLV1, true, nil)
	case "inhousereonboardingriskserviceurl":
		return obj.SetInhouseReonboardingRiskServiceURL(v.InhouseReonboardingRiskServiceURL, true, nil)
	case "zendutywebhookurl":
		return obj.SetZendutyWebhookUrl(v.ZendutyWebhookUrl, true, nil)
	case "smallcase":
		return obj._SmallCase.Set(v.SmallCase, true, path)
	case "karza":
		return obj._Karza.Set(v.Karza, true, path)
	case "aa":
		return obj._AA.Set(v.AA, true, path)
	case "fennelfeaturestore":
		return obj._FennelFeatureStore.Set(v.FennelFeatureStore, true, path)
	case "scienaptic":
		return obj._Scienaptic.Set(v.Scienaptic, true, path)
	case "inhouseocr":
		return obj._InhouseOCR.Set(v.InhouseOCR, true, path)
	case "experian":
		return obj._Experian.Set(v.Experian, true, path)
	case "alpaca":
		return obj._Alpaca.Set(v.Alpaca, true, path)
	case "aclsftp":
		return obj._AclSftp.Set(v.AclSftp, true, path)
	case "lending":
		return obj._Lending.Set(v.Lending, true, path)
	case "locationmodel":
		return obj._LocationModel.Set(v.LocationModel, true, path)
	case "caseprioritisationmodel":
		return obj._CasePrioritisationModel.Set(v.CasePrioritisationModel, true, path)
	case "incomeestimatorconf":
		return obj._IncomeEstimatorConf.Set(v.IncomeEstimatorConf, true, path)
	case "saven":
		return obj._Saven.Set(v.Saven, true, path)
	case "federalescalation":
		return obj._FederalEscalation.Set(v.FederalEscalation, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Application) setDynamicFields(v *config.Application, dynamic bool, path []string) (err error) {

	err = obj.SetInhouseMatchFaceRequestURL(v.InhouseMatchFaceRequestURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInhouseMatchFaceRequestURLV2(v.InhouseMatchFaceRequestURLV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInhouseNameCheckUrl(v.InhouseNameCheckUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGoogleReverseGeocodingUrl(v.GoogleReverseGeocodingUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGoogleGeocodingUrl(v.GoogleGeocodingUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInhouseRiskServiceURL(v.InhouseRiskServiceURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInhouseRiskServiceURLV1(v.InhouseRiskServiceURLV1, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetInhouseReonboardingRiskServiceURL(v.InhouseReonboardingRiskServiceURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetZendutyWebhookUrl(v.ZendutyWebhookUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._SmallCase.Set(v.SmallCase, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Karza.Set(v.Karza, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AA.Set(v.AA, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FennelFeatureStore.Set(v.FennelFeatureStore, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Scienaptic.Set(v.Scienaptic, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InhouseOCR.Set(v.InhouseOCR, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Experian.Set(v.Experian, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Alpaca.Set(v.Alpaca, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AclSftp.Set(v.AclSftp, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Lending.Set(v.Lending, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LocationModel.Set(v.LocationModel, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CasePrioritisationModel.Set(v.CasePrioritisationModel, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IncomeEstimatorConf.Set(v.IncomeEstimatorConf, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Saven.Set(v.Saven, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FederalEscalation.Set(v.FederalEscalation, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Application) setStaticFields(v *config.Application) error {

	obj._Environment = v.Environment
	obj._Name = v.Name
	obj._IsSecureRedis = v.IsSecureRedis
	obj._SyncWrapperTimeout = v.SyncWrapperTimeout
	obj._VGAuthSvcSyncWrapperTimeout = v.VGAuthSvcSyncWrapperTimeout
	obj._IsStatementAPIEnabled = v.IsStatementAPIEnabled
	obj._IsListKeysSimulated = v.IsListKeysSimulated
	obj._CreateCustomerURL = v.CreateCustomerURL
	obj._CreateLoanCustomerURL = v.CreateLoanCustomerURL
	obj._LoanCustomerCreationStatusURL = v.LoanCustomerCreationStatusURL
	obj._CheckCustomerStatusURL = v.CheckCustomerStatusURL
	obj._DedupeCheckURL = v.DedupeCheckURL
	obj._FetchCustomerDetailsUrl = v.FetchCustomerDetailsUrl
	obj._EnquireVKYCStatusUrl = v.EnquireVKYCStatusUrl
	obj._CreateAccountURL = v.CreateAccountURL
	obj._CheckAccountStatusURL = v.CheckAccountStatusURL
	obj._EnquireBalanceURL = v.EnquireBalanceURL
	obj._CkycSearchURL = v.CkycSearchURL
	obj._GetKycDataURL = v.GetKycDataURL
	obj._VerifyCkycOtpURL = v.VerifyCkycOtpURL
	obj._CreateDisputeURL = v.CreateDisputeURL
	obj._DisputeStatusCheckUrl = v.DisputeStatusCheckUrl
	obj._BulkDisputeStatusCheckUrl = v.BulkDisputeStatusCheckUrl
	obj._UploadDocumentUrl = v.UploadDocumentUrl
	obj._SendCorrespondenceUrl = v.SendCorrespondenceUrl
	obj._ChannelQuestionnaireUrl = v.ChannelQuestionnaireUrl
	obj._AccountTransactionsUrl = v.AccountTransactionsUrl
	obj._PanProfileURL = v.PanProfileURL
	obj._Federal = v.Federal
	obj._LeadSquared = v.LeadSquared
	obj._M2P = v.M2P
	obj._AAConsentRequestURL = v.AAConsentRequestURL
	obj._AAConsentHandleURL = v.AAConsentHandleURL
	obj._AAGetConsentStatusURL = v.AAGetConsentStatusURL
	obj._AARequestDataURL = v.AARequestDataURL
	obj._AAFetchDataURL = v.AAFetchDataURL
	obj._CreateVirtualIdURL = v.CreateVirtualIdURL
	obj._GetTokenURL = v.GetTokenURL
	obj._DeviceRegistrationURL = v.DeviceRegistrationURL
	obj._SetPINURL = v.SetPINURL
	obj._GenerateUpiOtpURL = v.GenerateUpiOtpURL
	obj._ValidateAddressURL = v.ValidateAddressURL
	obj._RespAuthDetailsURL = v.RespAuthDetailsURL
	obj._RespAuthValCustURL = v.RespAuthValCustURL
	obj._RespAuthMandateURL = v.RespAuthMandateURL
	obj._RespMandateConfirmationURL = v.RespMandateConfirmationURL
	obj._ReqPayURL = v.ReqPayURL
	obj._ReqMandateURL = v.ReqMandateURL
	obj._RegisterMobileURL = v.RegisterMobileURL
	obj._ListUpiKeyUrl = v.ListUpiKeyUrl
	obj._ListAccountURL = v.ListAccountURL
	obj._ListAccountProviderURL = v.ListAccountProviderURL
	obj._UPIBalanceEnquiryURL = v.UPIBalanceEnquiryURL
	obj._ReqComplaintURL = v.ReqComplaintURL
	obj._ReqCheckComplaintStatusUrl = v.ReqCheckComplaintStatusUrl
	obj._ReqActivationUrl = v.ReqActivationUrl
	obj._ListPspURL = v.ListPspURL
	obj._RegMapperURL = v.RegMapperURL
	obj._GetMapperInfoURL = v.GetMapperInfoURL
	obj._ReqValQRUrl = v.ReqValQRUrl
	obj._RespMapperConfirmationURL = v.RespMapperConfirmationURL
	obj._PspHandleToUpiOrgIdMap = v.PspHandleToUpiOrgIdMap
	obj._FreshdeskAgentURL = v.FreshdeskAgentURL
	obj._FreshdeskTicketURL = v.FreshdeskTicketURL
	obj._FreshdeskFilterTicketURL = v.FreshdeskFilterTicketURL
	obj._FreshdeskSolutionsURL = v.FreshdeskSolutionsURL
	obj._FreshdeskContactsURL = v.FreshdeskContactsURL
	obj._FreshdeskTicketFieldURL = v.FreshdeskTicketFieldURL
	obj._RespTxnConfirmationURL = v.RespTxnConfirmationURL
	obj._RespValidateAddressURL = v.RespValidateAddressURL
	obj._ReqCheckTxnStatusURL = v.ReqCheckTxnStatusURL
	obj._FreshchatConversationURL = v.FreshchatConversationURL
	obj._FreshchatUserURL = v.FreshchatUserURL
	obj._FreshchatAgentURL = v.FreshchatAgentURL
	obj._SenseforthEventURL = v.SenseforthEventURL
	obj._ListVaeURL = v.ListVaeURL
	obj._GetUpiLiteURL = v.GetUpiLiteURL
	obj._SyncUpiLiteInfoURL = v.SyncUpiLiteInfoURL
	obj._Exotel = v.Exotel
	obj._Twilio = v.Twilio
	obj._GPlace = v.GPlace
	obj._Loylty = v.Loylty
	obj._Qwikcilver = v.Qwikcilver
	obj._Thriwe = v.Thriwe
	obj._Dreamfolks = v.Dreamfolks
	obj._Poshvine = v.Poshvine
	obj._Riskcovry = v.Riskcovry
	obj._Onsurity = v.Onsurity
	obj._AclEpifi = v.AclEpifi
	obj._AclFederal = v.AclFederal
	obj._AclEpifiOtp = v.AclEpifiOtp
	obj._KaleyraFederal = v.KaleyraFederal
	obj._KaleyraEpifi = v.KaleyraEpifi
	obj._KaleyraFederalCreditCard = v.KaleyraFederalCreditCard
	obj._KaleyraEpifiNR = v.KaleyraEpifiNR
	obj._KaleyraSmsCallbackURL = v.KaleyraSmsCallbackURL
	obj._NetCoreEpifi = v.NetCoreEpifi
	obj._AirtelFedSMS = v.AirtelFedSMS
	obj._AirtelEpifiSMS = v.AirtelEpifiSMS
	obj._AclWhatsapp = v.AclWhatsapp
	obj._Employment = v.Employment
	obj._Roanuz = v.Roanuz
	obj._IPStack = v.IPStack
	obj._Shipway = v.Shipway
	obj._CAMS = v.CAMS
	obj._Karvy = v.Karvy
	obj._MFCentral = v.MFCentral
	obj._InHouseAAParserURL = v.InHouseAAParserURL
	obj._InHouseAABulkParserURL = v.InHouseAABulkParserURL
	obj._FederalInternationalFundTransfer = v.FederalInternationalFundTransfer
	obj._Veri5CheckLivenessRequestURL = v.Veri5CheckLivenessRequestURL
	obj._Veri5MatchFaceRequestURL = v.Veri5MatchFaceRequestURL
	obj._KarzaCheckLivenessRequestURL = v.KarzaCheckLivenessRequestURL
	obj._KarzaLivenessCallbackURL = v.KarzaLivenessCallbackURL
	obj._KarzaMatchFaceRequestURL = v.KarzaMatchFaceRequestURL
	obj._KarzaCheckPassiveLivenessRequestURL = v.KarzaCheckPassiveLivenessRequestURL
	obj._KarzaCheckLivenessStatusURL = v.KarzaCheckLivenessStatusURL
	obj._GetEPANKarzaStatusURL = v.GetEPANKarzaStatusURL
	obj._InhouseGetAndValidateEPANURL = v.InhouseGetAndValidateEPANURL
	obj._InhouseCheckLivenessRequestURL = v.InhouseCheckLivenessRequestURL
	obj._UseFormMarshalForKarza = v.UseFormMarshalForKarza
	obj._UseFormMarshalForKarzaFM = v.UseFormMarshalForKarzaFM
	obj._SendAgentDataURL = v.SendAgentDataURL
	obj._SendAuditorDataURL = v.SendAuditorDataURL
	obj._InhouseVerifyAndGetITRIntimationDetailsURL = v.InhouseVerifyAndGetITRIntimationDetailsURL
	obj._OzonetelManualDialUrl = v.OzonetelManualDialUrl
	obj._OzonetelUserName = v.OzonetelUserName
	obj._OzonetelCampaignName = v.OzonetelCampaignName
	obj._PayAckStatusCodeJson = v.PayAckStatusCodeJson
	obj._PayFundTransferStatusCodeJson = v.PayFundTransferStatusCodeJson
	obj._PayUpiStatusCodeJson = v.PayUpiStatusCodeJson
	obj._EnachTransactionStatusCodeJson = v.EnachTransactionStatusCodeJson
	obj._DepositAckStatusCodeFilePath = v.DepositAckStatusCodeFilePath
	obj._DepositResponseStatusCodeFilePath = v.DepositResponseStatusCodeFilePath
	obj._CreateCustomerCallBackUrl = v.CreateCustomerCallBackUrl
	obj._CreateAccountCallBackUrl = v.CreateAccountCallBackUrl
	obj._CardResponseStatusCodeFilePath = v.CardResponseStatusCodeFilePath
	obj._Tiering = v.Tiering
	obj._BouncyCastle = v.BouncyCastle
	obj._CvlKra = v.CvlKra
	obj._NsdlKra = v.NsdlKra
	obj._Ckyc = v.Ckyc
	obj._FederalDeposit = v.FederalDeposit
	obj._Manch = v.Manch
	obj._Digio = v.Digio
	obj._WealthKarza = v.WealthKarza
	obj._Digilocker = v.Digilocker
	obj._SIResponseStatusCodeFilePath = v.SIResponseStatusCodeFilePath
	obj._InhouseEmployerNameMatchUrl = v.InhouseEmployerNameMatchUrl
	obj._InhouseEmployerNameCategoriserUrl = v.InhouseEmployerNameCategoriserUrl
	obj._Cibil = v.Cibil
	obj._DrivingLicenseValidationUrl = v.DrivingLicenseValidationUrl
	obj._VoterIdValidationUrl = v.VoterIdValidationUrl
	obj._Seon = v.Seon
	obj._InhousePopularFAQUrl = v.InhousePopularFAQUrl
	obj._Liquiloans = v.Liquiloans
	obj._Esign = v.Esign
	obj._ProfileValidation = v.ProfileValidation
	obj._CvlSecrets = v.CvlSecrets
	obj._BankAccountVerificationUrl = v.BankAccountVerificationUrl
	obj._MaxmindIp2CityUrlPrefix = v.MaxmindIp2CityUrlPrefix
	obj._MaxmindSecrets = v.MaxmindSecrets
	obj._PAYUAffluenceURL = v.PAYUAffluenceURL
	obj._BureauPhoneNumberDetailsUrl = v.BureauPhoneNumberDetailsUrl
	obj._BureauSecrets = v.BureauSecrets
	obj._DronapayHostURL = v.DronapayHostURL
	obj._SignzySecrets = v.SignzySecrets
	obj._Aml = v.Aml
	obj._MorningStar = v.MorningStar
	obj._InhouseMerchantResolutionServiceUrl = v.InhouseMerchantResolutionServiceUrl
	obj._FreshdeskAccountConfig = v.FreshdeskAccountConfig
	obj._FreshdeskURI = v.FreshdeskURI
	obj._Vistara = v.Vistara
	obj._SlackTokens = v.SlackTokens
	obj._InhouseLocationServiceUrl = v.InhouseLocationServiceUrl
	obj._VKYCAgentDashCred = v.VKYCAgentDashCred
	obj._Credgenics = v.Credgenics
	obj._Vkyc = v.Vkyc
	obj._EnachConfig = v.EnachConfig
	obj._CxFreshdeskTicketAttachmentsBucketName = v.CxFreshdeskTicketAttachmentsBucketName
	obj._InhouseBreForCCUrl = v.InhouseBreForCCUrl
	obj._EpanConfig = v.EpanConfig
	obj._Razorpay = v.Razorpay
	obj._PanValidationSecrets = v.PanValidationSecrets
	obj._BureauIdUrl = v.BureauIdUrl
	obj._BureauIdSecrets = v.BureauIdSecrets
	obj._Visa = v.Visa
	obj._MoEngage = v.MoEngage
	obj._PerfiosDigiLocker = v.PerfiosDigiLocker
	obj._SetU = v.SetU
	obj._Bridgewise = v.Bridgewise
	obj._Nps = v.Nps
	obj._Nugget = v.Nugget
	return nil
}

func (obj *Application) SetInhouseMatchFaceRequestURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseMatchFaceRequestURL", reflect.TypeOf(val))
	}
	obj._InhouseMatchFaceRequestURLMutex.Lock()
	defer obj._InhouseMatchFaceRequestURLMutex.Unlock()
	obj._InhouseMatchFaceRequestURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseMatchFaceRequestURL")
	}
	return nil
}
func (obj *Application) SetInhouseMatchFaceRequestURLV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseMatchFaceRequestURLV2", reflect.TypeOf(val))
	}
	obj._InhouseMatchFaceRequestURLV2Mutex.Lock()
	defer obj._InhouseMatchFaceRequestURLV2Mutex.Unlock()
	obj._InhouseMatchFaceRequestURLV2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseMatchFaceRequestURLV2")
	}
	return nil
}
func (obj *Application) SetInhouseNameCheckUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseNameCheckUrl", reflect.TypeOf(val))
	}
	obj._InhouseNameCheckUrlMutex.Lock()
	defer obj._InhouseNameCheckUrlMutex.Unlock()
	obj._InhouseNameCheckUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseNameCheckUrl")
	}
	return nil
}
func (obj *Application) SetGoogleReverseGeocodingUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.GoogleReverseGeocodingUrl", reflect.TypeOf(val))
	}
	obj._GoogleReverseGeocodingUrlMutex.Lock()
	defer obj._GoogleReverseGeocodingUrlMutex.Unlock()
	obj._GoogleReverseGeocodingUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GoogleReverseGeocodingUrl")
	}
	return nil
}
func (obj *Application) SetGoogleGeocodingUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.GoogleGeocodingUrl", reflect.TypeOf(val))
	}
	obj._GoogleGeocodingUrlMutex.Lock()
	defer obj._GoogleGeocodingUrlMutex.Unlock()
	obj._GoogleGeocodingUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GoogleGeocodingUrl")
	}
	return nil
}
func (obj *Application) SetInhouseRiskServiceURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseRiskServiceURL", reflect.TypeOf(val))
	}
	obj._InhouseRiskServiceURLMutex.Lock()
	defer obj._InhouseRiskServiceURLMutex.Unlock()
	obj._InhouseRiskServiceURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseRiskServiceURL")
	}
	return nil
}
func (obj *Application) SetInhouseRiskServiceURLV1(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseRiskServiceURLV1", reflect.TypeOf(val))
	}
	obj._InhouseRiskServiceURLV1Mutex.Lock()
	defer obj._InhouseRiskServiceURLV1Mutex.Unlock()
	obj._InhouseRiskServiceURLV1 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseRiskServiceURLV1")
	}
	return nil
}
func (obj *Application) SetInhouseReonboardingRiskServiceURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.InhouseReonboardingRiskServiceURL", reflect.TypeOf(val))
	}
	obj._InhouseReonboardingRiskServiceURLMutex.Lock()
	defer obj._InhouseReonboardingRiskServiceURLMutex.Unlock()
	obj._InhouseReonboardingRiskServiceURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseReonboardingRiskServiceURL")
	}
	return nil
}
func (obj *Application) SetZendutyWebhookUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Application.ZendutyWebhookUrl", reflect.TypeOf(val))
	}
	obj._ZendutyWebhookUrlMutex.Lock()
	defer obj._ZendutyWebhookUrlMutex.Unlock()
	obj._ZendutyWebhookUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ZendutyWebhookUrl")
	}
	return nil
}

func NewSmallCase() (_obj *SmallCase, _setters map[string]dynconf.SetFunc) {
	_obj = &SmallCase{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablenotfoundininitholdingsimport"] = _obj.SetEnableNotFoundInInitHoldingsImport
	return _obj, _setters
}

func (obj *SmallCase) Init() {
	newObj, _ := NewSmallCase()
	*obj = *newObj
}

func (obj *SmallCase) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SmallCase) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SmallCase)
	if !ok {
		return fmt.Errorf("invalid data type %v *SmallCase", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SmallCase) setDynamicField(v *config.SmallCase, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablenotfoundininitholdingsimport":
		return obj.SetEnableNotFoundInInitHoldingsImport(v.EnableNotFoundInInitHoldingsImport, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SmallCase) setDynamicFields(v *config.SmallCase, dynamic bool, path []string) (err error) {

	err = obj.SetEnableNotFoundInInitHoldingsImport(v.EnableNotFoundInInitHoldingsImport, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SmallCase) setStaticFields(v *config.SmallCase) error {

	obj._CreateTransactionURL = v.CreateTransactionURL
	obj._InitiateHoldingsImportURL = v.InitiateHoldingsImportURL
	obj._TriggerHoldingsImportFetchURL = v.TriggerHoldingsImportFetchURL
	obj._MFAnalyticsURL = v.MFAnalyticsURL
	obj._SmallCaseGateway = v.SmallCaseGateway
	return nil
}

func (obj *SmallCase) SetEnableNotFoundInInitHoldingsImport(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SmallCase.EnableNotFoundInInitHoldingsImport", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableNotFoundInInitHoldingsImport, 1)
	} else {
		atomic.StoreUint32(&obj._EnableNotFoundInInitHoldingsImport, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableNotFoundInInitHoldingsImport")
	}
	return nil
}

func NewKarza() (_obj *Karza, _setters map[string]dynconf.SetFunc) {
	_obj = &Karza{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["kycocrurl"] = _obj.SetKycOcrUrl
	_obj._KycOcrUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Karza) Init() {
	newObj, _ := NewKarza()
	*obj = *newObj
}

func (obj *Karza) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Karza) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Karza)
	if !ok {
		return fmt.Errorf("invalid data type %v *Karza", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Karza) setDynamicField(v *config.Karza, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "kycocrurl":
		return obj.SetKycOcrUrl(v.KycOcrUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Karza) setDynamicFields(v *config.Karza, dynamic bool, path []string) (err error) {

	err = obj.SetKycOcrUrl(v.KycOcrUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Karza) setStaticFields(v *config.Karza) error {

	obj._GenerateSessionTokenUrl = v.GenerateSessionTokenUrl
	obj._AddNewCustomerUrl = v.AddNewCustomerUrl
	obj._AddNewCustomerV3Url = v.AddNewCustomerV3Url
	obj._UpdateCustomerV3Url = v.UpdateCustomerV3Url
	obj._GenerateCustomerTokenUrl = v.GenerateCustomerTokenUrl
	obj._GetSlotUrl = v.GetSlotUrl
	obj._BookSlotUrl = v.BookSlotUrl
	obj._GenerateWebLinkUrl = v.GenerateWebLinkUrl
	obj._SlotAgentsUrl = v.SlotAgentsUrl
	obj._TransactionStatusEnquiryUrl = v.TransactionStatusEnquiryUrl
	obj._ReScheduleSlotUrl = v.ReScheduleSlotUrl
	obj._TriggerCallback = v.TriggerCallback
	obj._AgentDashboardUrl = v.AgentDashboardUrl
	obj._AgentDashboardAuthUrl = v.AgentDashboardAuthUrl
	obj._UpdateCustomerV3UATUrl = v.UpdateCustomerV3UATUrl
	obj._AddNewCustomerV3UATUrl = v.AddNewCustomerV3UATUrl
	obj._GenerateCustomerTokenUATUrl = v.GenerateCustomerTokenUATUrl
	obj._GenerateWebLinkUATUrl = v.GenerateWebLinkUATUrl
	obj._TransactionStatusEnquiryUATUrl = v.TransactionStatusEnquiryUATUrl
	obj._GenerateSessionTokenUATUrl = v.GenerateSessionTokenUATUrl
	obj._EmploymentVerificationAdvancedUrl = v.EmploymentVerificationAdvancedUrl
	obj._PassportVerificationURL = v.PassportVerificationURL
	obj._Secrets = v.Secrets
	return nil
}

func (obj *Karza) SetKycOcrUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Karza.KycOcrUrl", reflect.TypeOf(val))
	}
	obj._KycOcrUrlMutex.Lock()
	defer obj._KycOcrUrlMutex.Unlock()
	obj._KycOcrUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "KycOcrUrl")
	}
	return nil
}

func NewAA() (_obj *AA, _setters map[string]dynconf.SetFunc) {
	_obj = &AA{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["usesahamaticrandtoken"] = _obj.SetUseSahamatiCrAndToken
	_setters["isonemoneyv2enabled"] = _obj.SetIsOnemoneyV2Enabled
	_setters["isfinvuv2enabled"] = _obj.SetIsFinvuV2Enabled
	_setters["baseurl"] = _obj.SetBaseURL
	_obj._BaseURLMutex = &sync.RWMutex{}
	_setters["postconsenturl"] = _obj.SetPostConsentURL
	_obj._PostConsentURLMutex = &sync.RWMutex{}
	_setters["consentstatusurl"] = _obj.SetConsentStatusURL
	_obj._ConsentStatusURLMutex = &sync.RWMutex{}
	_setters["consentartefacturl"] = _obj.SetConsentArtefactURL
	_obj._ConsentArtefactURLMutex = &sync.RWMutex{}
	_setters["consentartefactv2url"] = _obj.SetConsentArtefactV2URL
	_obj._ConsentArtefactV2URLMutex = &sync.RWMutex{}
	_setters["requestdataurl"] = _obj.SetRequestDataURL
	_obj._RequestDataURLMutex = &sync.RWMutex{}
	_setters["fetchdataurl"] = _obj.SetFetchDataURL
	_obj._FetchDataURLMutex = &sync.RWMutex{}
	_setters["generateaccesstokenurl"] = _obj.SetGenerateAccessTokenURL
	_obj._GenerateAccessTokenURLMutex = &sync.RWMutex{}
	_setters["fetchcrentitydetailurl"] = _obj.SetFetchCrEntityDetailURL
	_obj._FetchCrEntityDetailURLMutex = &sync.RWMutex{}
	_setters["fetchcrentitydetailurlv2"] = _obj.SetFetchCrEntityDetailURLV2
	_obj._FetchCrEntityDetailURLV2Mutex = &sync.RWMutex{}
	_setters["getaccountlinkstatusurl"] = _obj.SetGetAccountLinkStatusURL
	_obj._GetAccountLinkStatusURLMutex = &sync.RWMutex{}
	_setters["accountdelinkurl"] = _obj.SetAccountDeLinkURL
	_obj._AccountDeLinkURLMutex = &sync.RWMutex{}
	_setters["consentupdateurl"] = _obj.SetConsentUpdateURL
	_obj._ConsentUpdateURLMutex = &sync.RWMutex{}
	_setters["onemoneycrid"] = _obj.SetOneMoneyCrId
	_obj._OneMoneyCrIdMutex = &sync.RWMutex{}
	_setters["finvucrid"] = _obj.SetFinvuCrId
	_obj._FinvuCrIdMutex = &sync.RWMutex{}
	_setters["getaccountlinkstatusbulkurl"] = _obj.SetGetAccountLinkStatusBulkURL
	_obj._GetAccountLinkStatusBulkURLMutex = &sync.RWMutex{}
	_setters["getheartbeatstatusurl"] = _obj.SetGetHeartbeatStatusURL
	_obj._GetHeartbeatStatusURLMutex = &sync.RWMutex{}
	_setters["getbulkconsentrequesturl"] = _obj.SetGetBulkConsentRequestURL
	_obj._GetBulkConsentRequestURLMutex = &sync.RWMutex{}
	_setters["generatefinvujwttokenurl"] = _obj.SetGenerateFinvuJwtTokenURL
	_obj._GenerateFinvuJwtTokenURLMutex = &sync.RWMutex{}
	_setters["aaclientapikey"] = _obj.SetAAClientApiKey
	_obj._AAClientApiKeyMutex = &sync.RWMutex{}
	_setters["sahamaticlientid"] = _obj.SetSahamatiClientId
	_obj._SahamatiClientIdMutex = &sync.RWMutex{}
	_setters["epifiaakid"] = _obj.SetEpifiAaKid
	_obj._EpifiAaKidMutex = &sync.RWMutex{}
	_setters["aasecretsversiontouse"] = _obj.SetAaSecretsVersionToUse
	_obj._AaSecretsVersionToUseMutex = &sync.RWMutex{}
	_Ignosis, _fieldSetters := NewIgnosis()
	_obj._Ignosis = _Ignosis
	helper.AddFieldSetters("ignosis", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *AA) Init() {
	newObj, _ := NewAA()
	*obj = *newObj
}

func (obj *AA) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AA) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AA)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AA) setDynamicField(v *config.AA, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "usesahamaticrandtoken":
		return obj.SetUseSahamatiCrAndToken(v.UseSahamatiCrAndToken, true, nil)
	case "isonemoneyv2enabled":
		return obj.SetIsOnemoneyV2Enabled(v.IsOnemoneyV2Enabled, true, nil)
	case "isfinvuv2enabled":
		return obj.SetIsFinvuV2Enabled(v.IsFinvuV2Enabled, true, nil)
	case "baseurl":
		return obj.SetBaseURL(v.BaseURL, true, nil)
	case "postconsenturl":
		return obj.SetPostConsentURL(v.PostConsentURL, true, nil)
	case "consentstatusurl":
		return obj.SetConsentStatusURL(v.ConsentStatusURL, true, nil)
	case "consentartefacturl":
		return obj.SetConsentArtefactURL(v.ConsentArtefactURL, true, nil)
	case "consentartefactv2url":
		return obj.SetConsentArtefactV2URL(v.ConsentArtefactV2URL, true, nil)
	case "requestdataurl":
		return obj.SetRequestDataURL(v.RequestDataURL, true, nil)
	case "fetchdataurl":
		return obj.SetFetchDataURL(v.FetchDataURL, true, nil)
	case "generateaccesstokenurl":
		return obj.SetGenerateAccessTokenURL(v.GenerateAccessTokenURL, true, nil)
	case "fetchcrentitydetailurl":
		return obj.SetFetchCrEntityDetailURL(v.FetchCrEntityDetailURL, true, nil)
	case "fetchcrentitydetailurlv2":
		return obj.SetFetchCrEntityDetailURLV2(v.FetchCrEntityDetailURLV2, true, nil)
	case "getaccountlinkstatusurl":
		return obj.SetGetAccountLinkStatusURL(v.GetAccountLinkStatusURL, true, nil)
	case "accountdelinkurl":
		return obj.SetAccountDeLinkURL(v.AccountDeLinkURL, true, nil)
	case "consentupdateurl":
		return obj.SetConsentUpdateURL(v.ConsentUpdateURL, true, nil)
	case "onemoneycrid":
		return obj.SetOneMoneyCrId(v.OneMoneyCrId, true, nil)
	case "finvucrid":
		return obj.SetFinvuCrId(v.FinvuCrId, true, nil)
	case "getaccountlinkstatusbulkurl":
		return obj.SetGetAccountLinkStatusBulkURL(v.GetAccountLinkStatusBulkURL, true, nil)
	case "getheartbeatstatusurl":
		return obj.SetGetHeartbeatStatusURL(v.GetHeartbeatStatusURL, true, nil)
	case "getbulkconsentrequesturl":
		return obj.SetGetBulkConsentRequestURL(v.GetBulkConsentRequestURL, true, nil)
	case "generatefinvujwttokenurl":
		return obj.SetGenerateFinvuJwtTokenURL(v.GenerateFinvuJwtTokenURL, true, nil)
	case "aaclientapikey":
		return obj.SetAAClientApiKey(v.AAClientApiKey, true, nil)
	case "sahamaticlientid":
		return obj.SetSahamatiClientId(v.SahamatiClientId, true, nil)
	case "epifiaakid":
		return obj.SetEpifiAaKid(v.EpifiAaKid, true, nil)
	case "aasecretsversiontouse":
		return obj.SetAaSecretsVersionToUse(v.AaSecretsVersionToUse, true, nil)
	case "ignosis":
		return obj._Ignosis.Set(v.Ignosis, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AA) setDynamicFields(v *config.AA, dynamic bool, path []string) (err error) {

	err = obj.SetUseSahamatiCrAndToken(v.UseSahamatiCrAndToken, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsOnemoneyV2Enabled(v.IsOnemoneyV2Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsFinvuV2Enabled(v.IsFinvuV2Enabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBaseURL(v.BaseURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPostConsentURL(v.PostConsentURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConsentStatusURL(v.ConsentStatusURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConsentArtefactURL(v.ConsentArtefactURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConsentArtefactV2URL(v.ConsentArtefactV2URL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRequestDataURL(v.RequestDataURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFetchDataURL(v.FetchDataURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGenerateAccessTokenURL(v.GenerateAccessTokenURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFetchCrEntityDetailURL(v.FetchCrEntityDetailURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFetchCrEntityDetailURLV2(v.FetchCrEntityDetailURLV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetAccountLinkStatusURL(v.GetAccountLinkStatusURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAccountDeLinkURL(v.AccountDeLinkURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetConsentUpdateURL(v.ConsentUpdateURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetOneMoneyCrId(v.OneMoneyCrId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFinvuCrId(v.FinvuCrId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetAccountLinkStatusBulkURL(v.GetAccountLinkStatusBulkURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetHeartbeatStatusURL(v.GetHeartbeatStatusURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetBulkConsentRequestURL(v.GetBulkConsentRequestURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGenerateFinvuJwtTokenURL(v.GenerateFinvuJwtTokenURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAAClientApiKey(v.AAClientApiKey, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSahamatiClientId(v.SahamatiClientId, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEpifiAaKid(v.EpifiAaKid, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAaSecretsVersionToUse(v.AaSecretsVersionToUse, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Ignosis.Set(v.Ignosis, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AA) setStaticFields(v *config.AA) error {

	obj._AaVgSecretsV1 = v.AaVgSecretsV1
	obj._AaVgVnSecretsV1 = v.AaVgVnSecretsV1
	obj._AaVgSecretsV2 = v.AaVgSecretsV2
	obj._AaVgVnSecretsV2 = v.AaVgVnSecretsV2
	obj._FinvuFipMetricsURL = v.FinvuFipMetricsURL
	return nil
}

func (obj *AA) SetUseSahamatiCrAndToken(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.UseSahamatiCrAndToken", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseSahamatiCrAndToken, 1)
	} else {
		atomic.StoreUint32(&obj._UseSahamatiCrAndToken, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseSahamatiCrAndToken")
	}
	return nil
}
func (obj *AA) SetIsOnemoneyV2Enabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.IsOnemoneyV2Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsOnemoneyV2Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsOnemoneyV2Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsOnemoneyV2Enabled")
	}
	return nil
}
func (obj *AA) SetIsFinvuV2Enabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.IsFinvuV2Enabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsFinvuV2Enabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsFinvuV2Enabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsFinvuV2Enabled")
	}
	return nil
}
func (obj *AA) SetBaseURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.BaseURL", reflect.TypeOf(val))
	}
	obj._BaseURLMutex.Lock()
	defer obj._BaseURLMutex.Unlock()
	obj._BaseURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BaseURL")
	}
	return nil
}
func (obj *AA) SetPostConsentURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.PostConsentURL", reflect.TypeOf(val))
	}
	obj._PostConsentURLMutex.Lock()
	defer obj._PostConsentURLMutex.Unlock()
	obj._PostConsentURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PostConsentURL")
	}
	return nil
}
func (obj *AA) SetConsentStatusURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.ConsentStatusURL", reflect.TypeOf(val))
	}
	obj._ConsentStatusURLMutex.Lock()
	defer obj._ConsentStatusURLMutex.Unlock()
	obj._ConsentStatusURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentStatusURL")
	}
	return nil
}
func (obj *AA) SetConsentArtefactURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.ConsentArtefactURL", reflect.TypeOf(val))
	}
	obj._ConsentArtefactURLMutex.Lock()
	defer obj._ConsentArtefactURLMutex.Unlock()
	obj._ConsentArtefactURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentArtefactURL")
	}
	return nil
}
func (obj *AA) SetConsentArtefactV2URL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.ConsentArtefactV2URL", reflect.TypeOf(val))
	}
	obj._ConsentArtefactV2URLMutex.Lock()
	defer obj._ConsentArtefactV2URLMutex.Unlock()
	obj._ConsentArtefactV2URL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentArtefactV2URL")
	}
	return nil
}
func (obj *AA) SetRequestDataURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.RequestDataURL", reflect.TypeOf(val))
	}
	obj._RequestDataURLMutex.Lock()
	defer obj._RequestDataURLMutex.Unlock()
	obj._RequestDataURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "RequestDataURL")
	}
	return nil
}
func (obj *AA) SetFetchDataURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.FetchDataURL", reflect.TypeOf(val))
	}
	obj._FetchDataURLMutex.Lock()
	defer obj._FetchDataURLMutex.Unlock()
	obj._FetchDataURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FetchDataURL")
	}
	return nil
}
func (obj *AA) SetGenerateAccessTokenURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GenerateAccessTokenURL", reflect.TypeOf(val))
	}
	obj._GenerateAccessTokenURLMutex.Lock()
	defer obj._GenerateAccessTokenURLMutex.Unlock()
	obj._GenerateAccessTokenURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GenerateAccessTokenURL")
	}
	return nil
}
func (obj *AA) SetFetchCrEntityDetailURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.FetchCrEntityDetailURL", reflect.TypeOf(val))
	}
	obj._FetchCrEntityDetailURLMutex.Lock()
	defer obj._FetchCrEntityDetailURLMutex.Unlock()
	obj._FetchCrEntityDetailURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FetchCrEntityDetailURL")
	}
	return nil
}
func (obj *AA) SetFetchCrEntityDetailURLV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.FetchCrEntityDetailURLV2", reflect.TypeOf(val))
	}
	obj._FetchCrEntityDetailURLV2Mutex.Lock()
	defer obj._FetchCrEntityDetailURLV2Mutex.Unlock()
	obj._FetchCrEntityDetailURLV2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FetchCrEntityDetailURLV2")
	}
	return nil
}
func (obj *AA) SetGetAccountLinkStatusURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GetAccountLinkStatusURL", reflect.TypeOf(val))
	}
	obj._GetAccountLinkStatusURLMutex.Lock()
	defer obj._GetAccountLinkStatusURLMutex.Unlock()
	obj._GetAccountLinkStatusURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetAccountLinkStatusURL")
	}
	return nil
}
func (obj *AA) SetAccountDeLinkURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.AccountDeLinkURL", reflect.TypeOf(val))
	}
	obj._AccountDeLinkURLMutex.Lock()
	defer obj._AccountDeLinkURLMutex.Unlock()
	obj._AccountDeLinkURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AccountDeLinkURL")
	}
	return nil
}
func (obj *AA) SetConsentUpdateURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.ConsentUpdateURL", reflect.TypeOf(val))
	}
	obj._ConsentUpdateURLMutex.Lock()
	defer obj._ConsentUpdateURLMutex.Unlock()
	obj._ConsentUpdateURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ConsentUpdateURL")
	}
	return nil
}
func (obj *AA) SetOneMoneyCrId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.OneMoneyCrId", reflect.TypeOf(val))
	}
	obj._OneMoneyCrIdMutex.Lock()
	defer obj._OneMoneyCrIdMutex.Unlock()
	obj._OneMoneyCrId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "OneMoneyCrId")
	}
	return nil
}
func (obj *AA) SetFinvuCrId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.FinvuCrId", reflect.TypeOf(val))
	}
	obj._FinvuCrIdMutex.Lock()
	defer obj._FinvuCrIdMutex.Unlock()
	obj._FinvuCrId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FinvuCrId")
	}
	return nil
}
func (obj *AA) SetGetAccountLinkStatusBulkURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GetAccountLinkStatusBulkURL", reflect.TypeOf(val))
	}
	obj._GetAccountLinkStatusBulkURLMutex.Lock()
	defer obj._GetAccountLinkStatusBulkURLMutex.Unlock()
	obj._GetAccountLinkStatusBulkURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetAccountLinkStatusBulkURL")
	}
	return nil
}
func (obj *AA) SetGetHeartbeatStatusURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GetHeartbeatStatusURL", reflect.TypeOf(val))
	}
	obj._GetHeartbeatStatusURLMutex.Lock()
	defer obj._GetHeartbeatStatusURLMutex.Unlock()
	obj._GetHeartbeatStatusURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetHeartbeatStatusURL")
	}
	return nil
}
func (obj *AA) SetGetBulkConsentRequestURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GetBulkConsentRequestURL", reflect.TypeOf(val))
	}
	obj._GetBulkConsentRequestURLMutex.Lock()
	defer obj._GetBulkConsentRequestURLMutex.Unlock()
	obj._GetBulkConsentRequestURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetBulkConsentRequestURL")
	}
	return nil
}
func (obj *AA) SetGenerateFinvuJwtTokenURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.GenerateFinvuJwtTokenURL", reflect.TypeOf(val))
	}
	obj._GenerateFinvuJwtTokenURLMutex.Lock()
	defer obj._GenerateFinvuJwtTokenURLMutex.Unlock()
	obj._GenerateFinvuJwtTokenURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GenerateFinvuJwtTokenURL")
	}
	return nil
}
func (obj *AA) SetAAClientApiKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.AAClientApiKey", reflect.TypeOf(val))
	}
	obj._AAClientApiKeyMutex.Lock()
	defer obj._AAClientApiKeyMutex.Unlock()
	obj._AAClientApiKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AAClientApiKey")
	}
	return nil
}
func (obj *AA) SetSahamatiClientId(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.SahamatiClientId", reflect.TypeOf(val))
	}
	obj._SahamatiClientIdMutex.Lock()
	defer obj._SahamatiClientIdMutex.Unlock()
	obj._SahamatiClientId = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SahamatiClientId")
	}
	return nil
}
func (obj *AA) SetEpifiAaKid(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.EpifiAaKid", reflect.TypeOf(val))
	}
	obj._EpifiAaKidMutex.Lock()
	defer obj._EpifiAaKidMutex.Unlock()
	obj._EpifiAaKid = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "EpifiAaKid")
	}
	return nil
}
func (obj *AA) SetAaSecretsVersionToUse(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AA.AaSecretsVersionToUse", reflect.TypeOf(val))
	}
	obj._AaSecretsVersionToUseMutex.Lock()
	defer obj._AaSecretsVersionToUseMutex.Unlock()
	obj._AaSecretsVersionToUse = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "AaSecretsVersionToUse")
	}
	return nil
}

func NewIgnosis() (_obj *Ignosis, _setters map[string]dynconf.SetFunc) {
	_obj = &Ignosis{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["url"] = _obj.SetUrl
	_obj._UrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Ignosis) Init() {
	newObj, _ := NewIgnosis()
	*obj = *newObj
}

func (obj *Ignosis) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Ignosis) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Ignosis)
	if !ok {
		return fmt.Errorf("invalid data type %v *Ignosis", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Ignosis) setDynamicField(v *config.Ignosis, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "url":
		return obj.SetUrl(v.Url, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Ignosis) setDynamicFields(v *config.Ignosis, dynamic bool, path []string) (err error) {

	err = obj.SetUrl(v.Url, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Ignosis) setStaticFields(v *config.Ignosis) error {

	return nil
}

func (obj *Ignosis) SetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Ignosis.Url", reflect.TypeOf(val))
	}
	obj._UrlMutex.Lock()
	defer obj._UrlMutex.Unlock()
	obj._Url = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Url")
	}
	return nil
}

func NewFennelFeatureStore() (_obj *FennelFeatureStore, _setters map[string]dynconf.SetFunc) {
	_obj = &FennelFeatureStore{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["pdscoreurl"] = _obj.SetPdScoreURL
	_obj._PdScoreURLMutex = &sync.RWMutex{}
	_Simulator, _fieldSetters := NewFennelSimulatorConfig()
	_obj._Simulator = _Simulator
	helper.AddFieldSetters("simulator", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *FennelFeatureStore) Init() {
	newObj, _ := NewFennelFeatureStore()
	*obj = *newObj
}

func (obj *FennelFeatureStore) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FennelFeatureStore) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FennelFeatureStore)
	if !ok {
		return fmt.Errorf("invalid data type %v *FennelFeatureStore", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FennelFeatureStore) setDynamicField(v *config.FennelFeatureStore, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "pdscoreurl":
		return obj.SetPdScoreURL(v.PdScoreURL, true, nil)
	case "simulator":
		return obj._Simulator.Set(v.Simulator, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FennelFeatureStore) setDynamicFields(v *config.FennelFeatureStore, dynamic bool, path []string) (err error) {

	err = obj.SetPdScoreURL(v.PdScoreURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._Simulator.Set(v.Simulator, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FennelFeatureStore) setStaticFields(v *config.FennelFeatureStore) error {

	obj._ExtractFeatureSetsURLV2 = v.ExtractFeatureSetsURLV2
	obj._ExtractFeatureSetsURLV3 = v.ExtractFeatureSetsURLV3
	obj._LogDatasetsURL = v.LogDatasetsURL
	obj._LogDatasetsURLV2 = v.LogDatasetsURLV2
	obj._LogDatasetsURLV3 = v.LogDatasetsURLV3
	obj._FennelFeatureStoreSecret = v.FennelFeatureStoreSecret
	return nil
}

func (obj *FennelFeatureStore) SetPdScoreURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FennelFeatureStore.PdScoreURL", reflect.TypeOf(val))
	}
	obj._PdScoreURLMutex.Lock()
	defer obj._PdScoreURLMutex.Unlock()
	obj._PdScoreURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "PdScoreURL")
	}
	return nil
}

func NewFennelSimulatorConfig() (_obj *FennelSimulatorConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &FennelSimulatorConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enable"] = _obj.SetEnable
	return _obj, _setters
}

func (obj *FennelSimulatorConfig) Init() {
	newObj, _ := NewFennelSimulatorConfig()
	*obj = *newObj
}

func (obj *FennelSimulatorConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FennelSimulatorConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FennelSimulatorConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *FennelSimulatorConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FennelSimulatorConfig) setDynamicField(v *config.FennelSimulatorConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enable":
		return obj.SetEnable(v.Enable, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FennelSimulatorConfig) setDynamicFields(v *config.FennelSimulatorConfig, dynamic bool, path []string) (err error) {

	err = obj.SetEnable(v.Enable, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FennelSimulatorConfig) setStaticFields(v *config.FennelSimulatorConfig) error {

	obj._ExtractFeatureSetsURL = v.ExtractFeatureSetsURL
	obj._LogDatasetsURL = v.LogDatasetsURL
	obj._AllowedWorkflowsForSimulation = v.AllowedWorkflowsForSimulation
	return nil
}

func (obj *FennelSimulatorConfig) SetEnable(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FennelSimulatorConfig.Enable", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._Enable, 1)
	} else {
		atomic.StoreUint32(&obj._Enable, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "Enable")
	}
	return nil
}

func NewScienaptic() (_obj *Scienaptic, _setters map[string]dynconf.SetFunc) {
	_obj = &Scienaptic{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._WhitelistedFeatures = &syncmap.Map[string, bool]{}
	_setters["whitelistedfeatures"] = _obj.SetWhitelistedFeatures
	return _obj, _setters
}

func (obj *Scienaptic) Init() {
	newObj, _ := NewScienaptic()
	*obj = *newObj
}

func (obj *Scienaptic) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Scienaptic) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Scienaptic)
	if !ok {
		return fmt.Errorf("invalid data type %v *Scienaptic", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Scienaptic) setDynamicField(v *config.Scienaptic, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "whitelistedfeatures":
		return obj.SetWhitelistedFeatures(v.WhitelistedFeatures, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Scienaptic) setDynamicFields(v *config.Scienaptic, dynamic bool, path []string) (err error) {

	err = obj.SetWhitelistedFeatures(v.WhitelistedFeatures, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Scienaptic) setStaticFields(v *config.Scienaptic) error {

	obj._GenerateSmsFeaturesURL = v.GenerateSmsFeaturesURL
	obj._ScienapticSecrets = v.ScienapticSecrets
	return nil
}

func (obj *Scienaptic) SetWhitelistedFeatures(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Scienaptic.WhitelistedFeatures", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._WhitelistedFeatures, v, path)
}

func NewInhouseOCR() (_obj *InhouseOCR, _setters map[string]dynconf.SetFunc) {
	_obj = &InhouseOCR{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["extractfieldsurlv2"] = _obj.SetExtractFieldsURLV2
	_obj._ExtractFieldsURLV2Mutex = &sync.RWMutex{}
	_setters["detectdocumenturl"] = _obj.SetDetectDocumentURL
	_obj._DetectDocumentURLMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *InhouseOCR) Init() {
	newObj, _ := NewInhouseOCR()
	*obj = *newObj
}

func (obj *InhouseOCR) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InhouseOCR) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InhouseOCR)
	if !ok {
		return fmt.Errorf("invalid data type %v *InhouseOCR", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InhouseOCR) setDynamicField(v *config.InhouseOCR, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "extractfieldsurlv2":
		return obj.SetExtractFieldsURLV2(v.ExtractFieldsURLV2, true, nil)
	case "detectdocumenturl":
		return obj.SetDetectDocumentURL(v.DetectDocumentURL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InhouseOCR) setDynamicFields(v *config.InhouseOCR, dynamic bool, path []string) (err error) {

	err = obj.SetExtractFieldsURLV2(v.ExtractFieldsURLV2, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDetectDocumentURL(v.DetectDocumentURL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InhouseOCR) setStaticFields(v *config.InhouseOCR) error {

	obj._MaskDocURL = v.MaskDocURL
	obj._ExtractFieldsURL = v.ExtractFieldsURL
	obj._ConfidenceThreshold = v.ConfidenceThreshold
	return nil
}

func (obj *InhouseOCR) SetExtractFieldsURLV2(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InhouseOCR.ExtractFieldsURLV2", reflect.TypeOf(val))
	}
	obj._ExtractFieldsURLV2Mutex.Lock()
	defer obj._ExtractFieldsURLV2Mutex.Unlock()
	obj._ExtractFieldsURLV2 = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "ExtractFieldsURLV2")
	}
	return nil
}
func (obj *InhouseOCR) SetDetectDocumentURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InhouseOCR.DetectDocumentURL", reflect.TypeOf(val))
	}
	obj._DetectDocumentURLMutex.Lock()
	defer obj._DetectDocumentURLMutex.Unlock()
	obj._DetectDocumentURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "DetectDocumentURL")
	}
	return nil
}

func NewExperian() (_obj *Experian, _setters map[string]dynconf.SetFunc) {
	_obj = &Experian{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["v1versionflag"] = _obj.SetV1VersionFlag
	return _obj, _setters
}

func (obj *Experian) Init() {
	newObj, _ := NewExperian()
	*obj = *newObj
}

func (obj *Experian) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Experian) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Experian)
	if !ok {
		return fmt.Errorf("invalid data type %v *Experian", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Experian) setDynamicField(v *config.Experian, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "v1versionflag":
		return obj.SetV1VersionFlag(v.V1VersionFlag, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Experian) setDynamicFields(v *config.Experian, dynamic bool, path []string) (err error) {

	err = obj.SetV1VersionFlag(v.V1VersionFlag, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Experian) setStaticFields(v *config.Experian) error {

	obj._CheckCreditReportPresenceURL = v.CheckCreditReportPresenceURL
	obj._FetchCreditReportURL = v.FetchCreditReportURL
	obj._FetchCreditReportForExistingUserURL = v.FetchCreditReportForExistingUserURL
	obj._FetchExtendSubscriptionURL = v.FetchExtendSubscriptionURL
	obj._FetchAccessTokenUrl = v.FetchAccessTokenUrl
	obj._FetchCreditReportURLV1 = v.FetchCreditReportURLV1
	obj._FetchCreditReportForExistingUserURLV1 = v.FetchCreditReportForExistingUserURLV1
	obj._FetchExtendSubscriptionURLV1 = v.FetchExtendSubscriptionURLV1
	obj._ExperianSecrets = v.ExperianSecrets
	return nil
}

func (obj *Experian) SetV1VersionFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Experian.V1VersionFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._V1VersionFlag, 1)
	} else {
		atomic.StoreUint32(&obj._V1VersionFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "V1VersionFlag")
	}
	return nil
}

func NewAlpaca() (_obj *Alpaca, _setters map[string]dynconf.SetFunc) {
	_obj = &Alpaca{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["shouldusesimulatedenvforws"] = _obj.SetShouldUseSimulatedEnvForWs
	_setters["shouldusesimulatedenvforevents"] = _obj.SetShouldUseSimulatedEnvForEvents
	return _obj, _setters
}

func (obj *Alpaca) Init() {
	newObj, _ := NewAlpaca()
	*obj = *newObj
}

func (obj *Alpaca) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Alpaca) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Alpaca)
	if !ok {
		return fmt.Errorf("invalid data type %v *Alpaca", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Alpaca) setDynamicField(v *config.Alpaca, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "shouldusesimulatedenvforws":
		return obj.SetShouldUseSimulatedEnvForWs(v.ShouldUseSimulatedEnvForWs, true, nil)
	case "shouldusesimulatedenvforevents":
		return obj.SetShouldUseSimulatedEnvForEvents(v.ShouldUseSimulatedEnvForEvents, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Alpaca) setDynamicFields(v *config.Alpaca, dynamic bool, path []string) (err error) {

	err = obj.SetShouldUseSimulatedEnvForWs(v.ShouldUseSimulatedEnvForWs, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetShouldUseSimulatedEnvForEvents(v.ShouldUseSimulatedEnvForEvents, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Alpaca) setStaticFields(v *config.Alpaca) error {

	obj._Secret = v.Secret
	obj._BrokerApiHost = v.BrokerApiHost
	obj._BrokerApiVersion = v.BrokerApiVersion
	obj._MarketApiHost = v.MarketApiHost
	obj._MarketApiVersion = v.MarketApiVersion
	obj._StreamApiHost = v.StreamApiHost
	obj._StreamApiPath = v.StreamApiPath
	obj._StreamApiScheme = v.StreamApiScheme
	obj._OrderEventsApiPath = v.OrderEventsApiPath
	obj._AccountEventsApiPath = v.AccountEventsApiPath
	obj._BrokerEventsApiHost = v.BrokerEventsApiHost
	obj._BrokerEventsApiScheme = v.BrokerEventsApiScheme
	obj._FundingConfig = v.FundingConfig
	obj._JournalEventsPath = v.JournalEventsPath
	obj._FundTransferEventsPath = v.FundTransferEventsPath
	obj._MarketDataBetaAPIPrefix = v.MarketDataBetaAPIPrefix
	return nil
}

func (obj *Alpaca) SetShouldUseSimulatedEnvForWs(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Alpaca.ShouldUseSimulatedEnvForWs", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShouldUseSimulatedEnvForWs, 1)
	} else {
		atomic.StoreUint32(&obj._ShouldUseSimulatedEnvForWs, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShouldUseSimulatedEnvForWs")
	}
	return nil
}
func (obj *Alpaca) SetShouldUseSimulatedEnvForEvents(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Alpaca.ShouldUseSimulatedEnvForEvents", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ShouldUseSimulatedEnvForEvents, 1)
	} else {
		atomic.StoreUint32(&obj._ShouldUseSimulatedEnvForEvents, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ShouldUseSimulatedEnvForEvents")
	}
	return nil
}

func NewAclSftp() (_obj *AclSftp, _setters map[string]dynconf.SetFunc) {
	_obj = &AclSftp{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["port"] = _obj.SetPort
	_setters["user"] = _obj.SetUser
	_obj._UserMutex = &sync.RWMutex{}
	_setters["host"] = _obj.SetHost
	_obj._HostMutex = &sync.RWMutex{}
	_setters["password"] = _obj.SetPassword
	_obj._PasswordMutex = &sync.RWMutex{}
	_setters["sshkey"] = _obj.SetSshKey
	_obj._SshKeyMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *AclSftp) Init() {
	newObj, _ := NewAclSftp()
	*obj = *newObj
}

func (obj *AclSftp) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AclSftp) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AclSftp)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AclSftp) setDynamicField(v *config.AclSftp, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "port":
		return obj.SetPort(v.Port, true, nil)
	case "user":
		return obj.SetUser(v.User, true, nil)
	case "host":
		return obj.SetHost(v.Host, true, nil)
	case "password":
		return obj.SetPassword(v.Password, true, nil)
	case "sshkey":
		return obj.SetSshKey(v.SshKey, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AclSftp) setDynamicFields(v *config.AclSftp, dynamic bool, path []string) (err error) {

	err = obj.SetPort(v.Port, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUser(v.User, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHost(v.Host, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetPassword(v.Password, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetSshKey(v.SshKey, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AclSftp) setStaticFields(v *config.AclSftp) error {

	return nil
}

func (obj *AclSftp) SetPort(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp.Port", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Port, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Port")
	}
	return nil
}
func (obj *AclSftp) SetUser(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp.User", reflect.TypeOf(val))
	}
	obj._UserMutex.Lock()
	defer obj._UserMutex.Unlock()
	obj._User = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "User")
	}
	return nil
}
func (obj *AclSftp) SetHost(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp.Host", reflect.TypeOf(val))
	}
	obj._HostMutex.Lock()
	defer obj._HostMutex.Unlock()
	obj._Host = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Host")
	}
	return nil
}
func (obj *AclSftp) SetPassword(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp.Password", reflect.TypeOf(val))
	}
	obj._PasswordMutex.Lock()
	defer obj._PasswordMutex.Unlock()
	obj._Password = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Password")
	}
	return nil
}
func (obj *AclSftp) SetSshKey(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *AclSftp.SshKey", reflect.TypeOf(val))
	}
	obj._SshKeyMutex.Lock()
	defer obj._SshKeyMutex.Unlock()
	obj._SshKey = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "SshKey")
	}
	return nil
}

func NewLending() (_obj *Lending, _setters map[string]dynconf.SetFunc) {
	_obj = &Lending{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_PreApprovedLoan, _fieldSetters := NewPreApprovedLoan()
	_obj._PreApprovedLoan = _PreApprovedLoan
	helper.AddFieldSetters("preapprovedloan", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Lending) Init() {
	newObj, _ := NewLending()
	*obj = *newObj
}

func (obj *Lending) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Lending) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Lending)
	if !ok {
		return fmt.Errorf("invalid data type %v *Lending", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Lending) setDynamicField(v *config.Lending, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "preapprovedloan":
		return obj._PreApprovedLoan.Set(v.PreApprovedLoan, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Lending) setDynamicFields(v *config.Lending, dynamic bool, path []string) (err error) {

	err = obj._PreApprovedLoan.Set(v.PreApprovedLoan, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Lending) setStaticFields(v *config.Lending) error {

	obj._CreditCard = v.CreditCard
	obj._CreditLine = v.CreditLine
	obj._Collateral = v.Collateral
	obj._SecuredLoans = v.SecuredLoans
	return nil
}

func NewPreApprovedLoan() (_obj *PreApprovedLoan, _setters map[string]dynconf.SetFunc) {
	_obj = &PreApprovedLoan{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_Idfc, _fieldSetters := NewIdfc()
	_obj._Idfc = _Idfc
	helper.AddFieldSetters("idfc", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *PreApprovedLoan) Init() {
	newObj, _ := NewPreApprovedLoan()
	*obj = *newObj
}

func (obj *PreApprovedLoan) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PreApprovedLoan) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PreApprovedLoan)
	if !ok {
		return fmt.Errorf("invalid data type %v *PreApprovedLoan", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PreApprovedLoan) setDynamicField(v *config.PreApprovedLoan, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "idfc":
		return obj._Idfc.Set(v.Idfc, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PreApprovedLoan) setDynamicFields(v *config.PreApprovedLoan, dynamic bool, path []string) (err error) {

	err = obj._Idfc.Set(v.Idfc, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PreApprovedLoan) setStaticFields(v *config.PreApprovedLoan) error {

	obj._Federal = v.Federal
	obj._FederalNTB = v.FederalNTB
	obj._Liquiloans = v.Liquiloans
	obj._Lentra = v.Lentra
	obj._Abfl = v.Abfl
	obj._Moneyview = v.Moneyview
	obj._Finflux = v.Finflux
	obj._Setu = v.Setu
	obj._Digitap = v.Digitap
	obj._Lenden = v.Lenden
	return nil
}

func NewIdfc() (_obj *Idfc, _setters map[string]dynconf.SetFunc) {
	_obj = &Idfc{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["url"] = _obj.SetUrl
	_obj._UrlMutex = &sync.RWMutex{}
	_setters["getaccesstokenurl"] = _obj.SetGetAccessTokenUrl
	_obj._GetAccessTokenUrlMutex = &sync.RWMutex{}
	_setters["mandatepageurl"] = _obj.SetMandatePageUrl
	_obj._MandatePageUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *Idfc) Init() {
	newObj, _ := NewIdfc()
	*obj = *newObj
}

func (obj *Idfc) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Idfc) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Idfc)
	if !ok {
		return fmt.Errorf("invalid data type %v *Idfc", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Idfc) setDynamicField(v *config.Idfc, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "url":
		return obj.SetUrl(v.Url, true, nil)
	case "getaccesstokenurl":
		return obj.SetGetAccessTokenUrl(v.GetAccessTokenUrl, true, nil)
	case "mandatepageurl":
		return obj.SetMandatePageUrl(v.MandatePageUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Idfc) setDynamicFields(v *config.Idfc, dynamic bool, path []string) (err error) {

	err = obj.SetUrl(v.Url, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetGetAccessTokenUrl(v.GetAccessTokenUrl, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMandatePageUrl(v.MandatePageUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Idfc) setStaticFields(v *config.Idfc) error {

	obj._EnableEncryption = v.EnableEncryption
	obj._SecretKey = v.SecretKey
	obj._Iv = v.Iv
	obj._PrivateKey = v.PrivateKey
	obj._ClientId = v.ClientId
	obj._Kid = v.Kid
	obj._CorrelationId = v.CorrelationId
	obj._Source = v.Source
	obj._MerchantCode = v.MerchantCode
	obj._MandateRegistrationIv = v.MandateRegistrationIv
	obj._MandateRegistrationSecretKey = v.MandateRegistrationSecretKey
	obj._BankCode = v.BankCode
	obj._SimulatorHttpURL = v.SimulatorHttpURL
	return nil
}

func (obj *Idfc) SetUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Idfc.Url", reflect.TypeOf(val))
	}
	obj._UrlMutex.Lock()
	defer obj._UrlMutex.Unlock()
	obj._Url = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "Url")
	}
	return nil
}
func (obj *Idfc) SetGetAccessTokenUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Idfc.GetAccessTokenUrl", reflect.TypeOf(val))
	}
	obj._GetAccessTokenUrlMutex.Lock()
	defer obj._GetAccessTokenUrlMutex.Unlock()
	obj._GetAccessTokenUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "GetAccessTokenUrl")
	}
	return nil
}
func (obj *Idfc) SetMandatePageUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Idfc.MandatePageUrl", reflect.TypeOf(val))
	}
	obj._MandatePageUrlMutex.Lock()
	defer obj._MandatePageUrlMutex.Unlock()
	obj._MandatePageUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "MandatePageUrl")
	}
	return nil
}

func NewLocationModelConfig() (_obj *LocationModelConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LocationModelConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["inhouseurl"] = _obj.SetInHouseUrl
	_obj._InHouseUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *LocationModelConfig) Init() {
	newObj, _ := NewLocationModelConfig()
	*obj = *newObj
}

func (obj *LocationModelConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LocationModelConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LocationModelConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LocationModelConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LocationModelConfig) setDynamicField(v *config.LocationModelConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "inhouseurl":
		return obj.SetInHouseUrl(v.InHouseUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LocationModelConfig) setDynamicFields(v *config.LocationModelConfig, dynamic bool, path []string) (err error) {

	err = obj.SetInHouseUrl(v.InHouseUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LocationModelConfig) setStaticFields(v *config.LocationModelConfig) error {

	obj._RiskSeverityValToEnumMapping = v.RiskSeverityValToEnumMapping
	return nil
}

func (obj *LocationModelConfig) SetInHouseUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *LocationModelConfig.InHouseUrl", reflect.TypeOf(val))
	}
	obj._InHouseUrlMutex.Lock()
	defer obj._InHouseUrlMutex.Unlock()
	obj._InHouseUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InHouseUrl")
	}
	return nil
}

func NewCasePrioritisationModelConfig() (_obj *CasePrioritisationModelConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CasePrioritisationModelConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["inhouseurl"] = _obj.SetInHouseUrl
	_obj._InHouseUrlMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *CasePrioritisationModelConfig) Init() {
	newObj, _ := NewCasePrioritisationModelConfig()
	*obj = *newObj
}

func (obj *CasePrioritisationModelConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CasePrioritisationModelConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CasePrioritisationModelConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePrioritisationModelConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CasePrioritisationModelConfig) setDynamicField(v *config.CasePrioritisationModelConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "inhouseurl":
		return obj.SetInHouseUrl(v.InHouseUrl, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CasePrioritisationModelConfig) setDynamicFields(v *config.CasePrioritisationModelConfig, dynamic bool, path []string) (err error) {

	err = obj.SetInHouseUrl(v.InHouseUrl, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CasePrioritisationModelConfig) setStaticFields(v *config.CasePrioritisationModelConfig) error {

	return nil
}

func (obj *CasePrioritisationModelConfig) SetInHouseUrl(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePrioritisationModelConfig.InHouseUrl", reflect.TypeOf(val))
	}
	obj._InHouseUrlMutex.Lock()
	defer obj._InHouseUrlMutex.Unlock()
	obj._InHouseUrl = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InHouseUrl")
	}
	return nil
}

func NewIncomeEstimatorConfig() (_obj *IncomeEstimatorConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IncomeEstimatorConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["inhouseincomeestimatorurl"] = _obj.SetInhouseIncomeEstimatorURL
	_obj._InhouseIncomeEstimatorURLMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *IncomeEstimatorConfig) Init() {
	newObj, _ := NewIncomeEstimatorConfig()
	*obj = *newObj
}

func (obj *IncomeEstimatorConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IncomeEstimatorConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IncomeEstimatorConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncomeEstimatorConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IncomeEstimatorConfig) setDynamicField(v *config.IncomeEstimatorConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "inhouseincomeestimatorurl":
		return obj.SetInhouseIncomeEstimatorURL(v.InhouseIncomeEstimatorURL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IncomeEstimatorConfig) setDynamicFields(v *config.IncomeEstimatorConfig, dynamic bool, path []string) (err error) {

	err = obj.SetInhouseIncomeEstimatorURL(v.InhouseIncomeEstimatorURL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IncomeEstimatorConfig) setStaticFields(v *config.IncomeEstimatorConfig) error {

	return nil
}

func (obj *IncomeEstimatorConfig) SetInhouseIncomeEstimatorURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *IncomeEstimatorConfig.InhouseIncomeEstimatorURL", reflect.TypeOf(val))
	}
	obj._InhouseIncomeEstimatorURLMutex.Lock()
	defer obj._InhouseIncomeEstimatorURLMutex.Unlock()
	obj._InhouseIncomeEstimatorURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "InhouseIncomeEstimatorURL")
	}
	return nil
}

func NewSaven() (_obj *Saven, _setters map[string]dynconf.SetFunc) {
	_obj = &Saven{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["jwtexpiry"] = _obj.SetJwtExpiry
	return _obj, _setters
}

func (obj *Saven) Init() {
	newObj, _ := NewSaven()
	*obj = *newObj
}

func (obj *Saven) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Saven) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Saven)
	if !ok {
		return fmt.Errorf("invalid data type %v *Saven", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Saven) setDynamicField(v *config.Saven, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "jwtexpiry":
		return obj.SetJwtExpiry(v.JwtExpiry, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Saven) setDynamicFields(v *config.Saven, dynamic bool, path []string) (err error) {

	err = obj.SetJwtExpiry(v.JwtExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Saven) setStaticFields(v *config.Saven) error {

	obj._SavenSecrets = v.SavenSecrets
	obj._CreditCardBaseUrl = v.CreditCardBaseUrl
	return nil
}

func (obj *Saven) SetJwtExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Saven.JwtExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._JwtExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "JwtExpiry")
	}
	return nil
}

func NewFederalEscalation() (_obj *FederalEscalation, _setters map[string]dynconf.SetFunc) {
	_obj = &FederalEscalation{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["baseurl"] = _obj.SetBaseURL
	_obj._BaseURLMutex = &sync.RWMutex{}
	_setters["createescalationurl"] = _obj.SetCreateEscalationURL
	_obj._CreateEscalationURLMutex = &sync.RWMutex{}
	_setters["bulkfetchurl"] = _obj.SetBulkFetchURL
	_obj._BulkFetchURLMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *FederalEscalation) Init() {
	newObj, _ := NewFederalEscalation()
	*obj = *newObj
}

func (obj *FederalEscalation) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FederalEscalation) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FederalEscalation)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalation", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FederalEscalation) setDynamicField(v *config.FederalEscalation, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "baseurl":
		return obj.SetBaseURL(v.BaseURL, true, nil)
	case "createescalationurl":
		return obj.SetCreateEscalationURL(v.CreateEscalationURL, true, nil)
	case "bulkfetchurl":
		return obj.SetBulkFetchURL(v.BulkFetchURL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FederalEscalation) setDynamicFields(v *config.FederalEscalation, dynamic bool, path []string) (err error) {

	err = obj.SetBaseURL(v.BaseURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCreateEscalationURL(v.CreateEscalationURL, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBulkFetchURL(v.BulkFetchURL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FederalEscalation) setStaticFields(v *config.FederalEscalation) error {

	obj._FederalEscalationSecrets = v.FederalEscalationSecrets
	obj._S3BucketName = v.S3BucketName
	return nil
}

func (obj *FederalEscalation) SetBaseURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalation.BaseURL", reflect.TypeOf(val))
	}
	obj._BaseURLMutex.Lock()
	defer obj._BaseURLMutex.Unlock()
	obj._BaseURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BaseURL")
	}
	return nil
}
func (obj *FederalEscalation) SetCreateEscalationURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalation.CreateEscalationURL", reflect.TypeOf(val))
	}
	obj._CreateEscalationURLMutex.Lock()
	defer obj._CreateEscalationURLMutex.Unlock()
	obj._CreateEscalationURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "CreateEscalationURL")
	}
	return nil
}
func (obj *FederalEscalation) SetBulkFetchURL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *FederalEscalation.BulkFetchURL", reflect.TypeOf(val))
	}
	obj._BulkFetchURLMutex.Lock()
	defer obj._BulkFetchURLMutex.Unlock()
	obj._BulkFetchURL = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "BulkFetchURL")
	}
	return nil
}

func NewFlags() (_obj *Flags, _setters map[string]dynconf.SetFunc) {
	_obj = &Flags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["logallapiparams"] = _obj.SetLogAllAPIParams
	_setters["usenewoccupationincifcreation"] = _obj.SetUseNewOccupationInCifCreation
	_setters["usenewfieldsincifcreation"] = _obj.SetUseNewFieldsInCifCreation
	_setters["usenewfieldsinaccountcreation"] = _obj.SetUseNewFieldsInAccountCreation
	_setters["enabletransactionenquirynewapi"] = _obj.SetEnableTransactionEnquiryNewApi
	_setters["enableuatforvkyc"] = _obj.SetEnableUATForVKYC
	_setters["enableinstrumentbillinginterceptor"] = _obj.SetEnableInstrumentBillingInterceptor
	_setters["disablegstreportingforift"] = _obj.SetDisableGstReportingForIFT
	_setters["enablefennelclusterv3"] = _obj.SetEnableFennelClusterV3
	_setters["enablecibilv2secrets"] = _obj.SetEnableCibilV2Secrets
	return _obj, _setters
}

func (obj *Flags) Init() {
	newObj, _ := NewFlags()
	*obj = *newObj
}

func (obj *Flags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Flags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Flags)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Flags) setDynamicField(v *config.Flags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "logallapiparams":
		return obj.SetLogAllAPIParams(v.LogAllAPIParams, true, nil)
	case "usenewoccupationincifcreation":
		return obj.SetUseNewOccupationInCifCreation(v.UseNewOccupationInCifCreation, true, nil)
	case "usenewfieldsincifcreation":
		return obj.SetUseNewFieldsInCifCreation(v.UseNewFieldsInCifCreation, true, nil)
	case "usenewfieldsinaccountcreation":
		return obj.SetUseNewFieldsInAccountCreation(v.UseNewFieldsInAccountCreation, true, nil)
	case "enabletransactionenquirynewapi":
		return obj.SetEnableTransactionEnquiryNewApi(v.EnableTransactionEnquiryNewApi, true, nil)
	case "enableuatforvkyc":
		return obj.SetEnableUATForVKYC(v.EnableUATForVKYC, true, nil)
	case "enableinstrumentbillinginterceptor":
		return obj.SetEnableInstrumentBillingInterceptor(v.EnableInstrumentBillingInterceptor, true, nil)
	case "disablegstreportingforift":
		return obj.SetDisableGstReportingForIFT(v.DisableGstReportingForIFT, true, nil)
	case "enablefennelclusterv3":
		return obj.SetEnableFennelClusterV3(v.EnableFennelClusterV3, true, nil)
	case "enablecibilv2secrets":
		return obj.SetEnableCibilV2Secrets(v.EnableCibilV2Secrets, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Flags) setDynamicFields(v *config.Flags, dynamic bool, path []string) (err error) {

	err = obj.SetLogAllAPIParams(v.LogAllAPIParams, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseNewOccupationInCifCreation(v.UseNewOccupationInCifCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseNewFieldsInCifCreation(v.UseNewFieldsInCifCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseNewFieldsInAccountCreation(v.UseNewFieldsInAccountCreation, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableTransactionEnquiryNewApi(v.EnableTransactionEnquiryNewApi, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableUATForVKYC(v.EnableUATForVKYC, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableInstrumentBillingInterceptor(v.EnableInstrumentBillingInterceptor, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetDisableGstReportingForIFT(v.DisableGstReportingForIFT, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableFennelClusterV3(v.EnableFennelClusterV3, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetEnableCibilV2Secrets(v.EnableCibilV2Secrets, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Flags) setStaticFields(v *config.Flags) error {

	obj._TrimDebugMessageFromStatus = v.TrimDebugMessageFromStatus
	obj._TokenValidation = v.TokenValidation
	obj._UseAsyncNotificationCallback = v.UseAsyncNotificationCallback
	obj._UseCustomTrustedCertPool = v.UseCustomTrustedCertPool
	obj._SkipCertVerifyPANValidation = v.SkipCertVerifyPANValidation
	obj._AllowSpecialCharactersInAddress = v.AllowSpecialCharactersInAddress
	obj._EnableFederalCardDecryptionByFallbackKey = v.EnableFederalCardDecryptionByFallbackKey
	obj._UseNewSolID = v.UseNewSolID
	return nil
}

func (obj *Flags) SetLogAllAPIParams(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.LogAllAPIParams", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._LogAllAPIParams, 1)
	} else {
		atomic.StoreUint32(&obj._LogAllAPIParams, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "LogAllAPIParams")
	}
	return nil
}
func (obj *Flags) SetUseNewOccupationInCifCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.UseNewOccupationInCifCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseNewOccupationInCifCreation, 1)
	} else {
		atomic.StoreUint32(&obj._UseNewOccupationInCifCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseNewOccupationInCifCreation")
	}
	return nil
}
func (obj *Flags) SetUseNewFieldsInCifCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.UseNewFieldsInCifCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseNewFieldsInCifCreation, 1)
	} else {
		atomic.StoreUint32(&obj._UseNewFieldsInCifCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseNewFieldsInCifCreation")
	}
	return nil
}
func (obj *Flags) SetUseNewFieldsInAccountCreation(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.UseNewFieldsInAccountCreation", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseNewFieldsInAccountCreation, 1)
	} else {
		atomic.StoreUint32(&obj._UseNewFieldsInAccountCreation, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseNewFieldsInAccountCreation")
	}
	return nil
}
func (obj *Flags) SetEnableTransactionEnquiryNewApi(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableTransactionEnquiryNewApi", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableTransactionEnquiryNewApi, 1)
	} else {
		atomic.StoreUint32(&obj._EnableTransactionEnquiryNewApi, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableTransactionEnquiryNewApi")
	}
	return nil
}
func (obj *Flags) SetEnableUATForVKYC(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableUATForVKYC", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableUATForVKYC, 1)
	} else {
		atomic.StoreUint32(&obj._EnableUATForVKYC, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableUATForVKYC")
	}
	return nil
}
func (obj *Flags) SetEnableInstrumentBillingInterceptor(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableInstrumentBillingInterceptor", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableInstrumentBillingInterceptor, 1)
	} else {
		atomic.StoreUint32(&obj._EnableInstrumentBillingInterceptor, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableInstrumentBillingInterceptor")
	}
	return nil
}
func (obj *Flags) SetDisableGstReportingForIFT(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.DisableGstReportingForIFT", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._DisableGstReportingForIFT, 1)
	} else {
		atomic.StoreUint32(&obj._DisableGstReportingForIFT, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "DisableGstReportingForIFT")
	}
	return nil
}
func (obj *Flags) SetEnableFennelClusterV3(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableFennelClusterV3", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableFennelClusterV3, 1)
	} else {
		atomic.StoreUint32(&obj._EnableFennelClusterV3, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableFennelClusterV3")
	}
	return nil
}
func (obj *Flags) SetEnableCibilV2Secrets(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Flags.EnableCibilV2Secrets", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnableCibilV2Secrets, 1)
	} else {
		atomic.StoreUint32(&obj._EnableCibilV2Secrets, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnableCibilV2Secrets")
	}
	return nil
}

func NewDowntimeConfig() (_obj *DowntimeConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DowntimeConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_Downtime, _fieldSetters := NewDowntime()
	_obj._Downtime = _Downtime
	helper.AddFieldSetters("downtime", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *DowntimeConfig) Init() {
	newObj, _ := NewDowntimeConfig()
	*obj = *newObj
}

func (obj *DowntimeConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DowntimeConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DowntimeConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DowntimeConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DowntimeConfig) setDynamicField(v *config.DowntimeConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "downtime":
		return obj._Downtime.Set(v.Downtime, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DowntimeConfig) setDynamicFields(v *config.DowntimeConfig, dynamic bool, path []string) (err error) {

	err = obj._Downtime.Set(v.Downtime, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DowntimeConfig) setStaticFields(v *config.DowntimeConfig) error {

	return nil
}

func NewDowntime() (_obj *Downtime, _setters map[string]dynconf.SetFunc) {
	_obj = &Downtime{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_DailyDowntime, _fieldSetters := gendynconf.NewRecurringDowntimePeriod()
	_obj._DailyDowntime = _DailyDowntime
	helper.AddFieldSetters("dailydowntime", _fieldSetters, _setters)
	_TimestampBasedDowntime, _fieldSetters := gendynconf.NewTimestampBasedDowntime()
	_obj._TimestampBasedDowntime = _TimestampBasedDowntime
	helper.AddFieldSetters("timestampbaseddowntime", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *Downtime) Init() {
	newObj, _ := NewDowntime()
	*obj = *newObj
}

func (obj *Downtime) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Downtime) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Downtime)
	if !ok {
		return fmt.Errorf("invalid data type %v *Downtime", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Downtime) setDynamicField(v *config.Downtime, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "dailydowntime":
		return obj._DailyDowntime.Set(v.DailyDowntime, true, path)
	case "timestampbaseddowntime":
		return obj._TimestampBasedDowntime.Set(v.TimestampBasedDowntime, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Downtime) setDynamicFields(v *config.Downtime, dynamic bool, path []string) (err error) {

	err = obj._DailyDowntime.Set(v.DailyDowntime, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._TimestampBasedDowntime.Set(v.TimestampBasedDowntime, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Downtime) setStaticFields(v *config.Downtime) error {

	return nil
}
