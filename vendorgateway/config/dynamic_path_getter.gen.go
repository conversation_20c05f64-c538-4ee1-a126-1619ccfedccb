// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "maxallowedtimeintervalforministatement":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MaxAllowedTimeIntervalForMiniStatement\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MaxAllowedTimeIntervalForMiniStatement, nil
	case "downtimeconfig":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.DowntimeConfig, nil
		case len(dynamicFieldPath) > 1:

			return obj.DowntimeConfig[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.DowntimeConfig, nil
	case "application":
		return obj.Application.Get(dynamicFieldPath[1:])
	case "flags":
		return obj.Flags.Get(dynamicFieldPath[1:])
	case "rpcratelimitconfig":
		return obj.RpcRateLimitConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Application) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "inhousematchfacerequesturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseMatchFaceRequestURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseMatchFaceRequestURL, nil
	case "inhousematchfacerequesturlv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseMatchFaceRequestURLV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseMatchFaceRequestURLV2, nil
	case "inhousenamecheckurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseNameCheckUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseNameCheckUrl, nil
	case "googlereversegeocodingurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GoogleReverseGeocodingUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GoogleReverseGeocodingUrl, nil
	case "googlegeocodingurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GoogleGeocodingUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GoogleGeocodingUrl, nil
	case "inhouseriskserviceurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseRiskServiceURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseRiskServiceURL, nil
	case "inhouseriskserviceurlv1":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseRiskServiceURLV1\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseRiskServiceURLV1, nil
	case "inhousereonboardingriskserviceurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseReonboardingRiskServiceURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseReonboardingRiskServiceURL, nil
	case "zendutywebhookurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ZendutyWebhookUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ZendutyWebhookUrl, nil
	case "smallcase":
		return obj.SmallCase.Get(dynamicFieldPath[1:])
	case "karza":
		return obj.Karza.Get(dynamicFieldPath[1:])
	case "aa":
		return obj.AA.Get(dynamicFieldPath[1:])
	case "fennelfeaturestore":
		return obj.FennelFeatureStore.Get(dynamicFieldPath[1:])
	case "scienaptic":
		return obj.Scienaptic.Get(dynamicFieldPath[1:])
	case "inhouseocr":
		return obj.InhouseOCR.Get(dynamicFieldPath[1:])
	case "experian":
		return obj.Experian.Get(dynamicFieldPath[1:])
	case "alpaca":
		return obj.Alpaca.Get(dynamicFieldPath[1:])
	case "aclsftp":
		return obj.AclSftp.Get(dynamicFieldPath[1:])
	case "lending":
		return obj.Lending.Get(dynamicFieldPath[1:])
	case "locationmodel":
		return obj.LocationModel.Get(dynamicFieldPath[1:])
	case "caseprioritisationmodel":
		return obj.CasePrioritisationModel.Get(dynamicFieldPath[1:])
	case "incomeestimatorconf":
		return obj.IncomeEstimatorConf.Get(dynamicFieldPath[1:])
	case "saven":
		return obj.Saven.Get(dynamicFieldPath[1:])
	case "federalescalation":
		return obj.FederalEscalation.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Application", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SmallCase) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablenotfoundininitholdingsimport":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableNotFoundInInitHoldingsImport\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableNotFoundInInitHoldingsImport, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SmallCase", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Karza) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "kycocrurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"KycOcrUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.KycOcrUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Karza", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AA) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "usesahamaticrandtoken":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseSahamatiCrAndToken\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseSahamatiCrAndToken, nil
	case "isonemoneyv2enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsOnemoneyV2Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsOnemoneyV2Enabled, nil
	case "isfinvuv2enabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsFinvuV2Enabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsFinvuV2Enabled, nil
	case "baseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BaseURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BaseURL, nil
	case "postconsenturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PostConsentURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PostConsentURL, nil
	case "consentstatusurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentStatusURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentStatusURL, nil
	case "consentartefacturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentArtefactURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentArtefactURL, nil
	case "consentartefactv2url":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentArtefactV2URL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentArtefactV2URL, nil
	case "requestdataurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RequestDataURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RequestDataURL, nil
	case "fetchdataurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FetchDataURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FetchDataURL, nil
	case "generateaccesstokenurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GenerateAccessTokenURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GenerateAccessTokenURL, nil
	case "fetchcrentitydetailurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FetchCrEntityDetailURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FetchCrEntityDetailURL, nil
	case "fetchcrentitydetailurlv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FetchCrEntityDetailURLV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FetchCrEntityDetailURLV2, nil
	case "getaccountlinkstatusurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetAccountLinkStatusURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetAccountLinkStatusURL, nil
	case "accountdelinkurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AccountDeLinkURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AccountDeLinkURL, nil
	case "consentupdateurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ConsentUpdateURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ConsentUpdateURL, nil
	case "onemoneycrid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OneMoneyCrId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OneMoneyCrId, nil
	case "finvucrid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FinvuCrId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FinvuCrId, nil
	case "getaccountlinkstatusbulkurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetAccountLinkStatusBulkURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetAccountLinkStatusBulkURL, nil
	case "getheartbeatstatusurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetHeartbeatStatusURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetHeartbeatStatusURL, nil
	case "getbulkconsentrequesturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetBulkConsentRequestURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetBulkConsentRequestURL, nil
	case "generatefinvujwttokenurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GenerateFinvuJwtTokenURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GenerateFinvuJwtTokenURL, nil
	case "aaclientapikey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AAClientApiKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AAClientApiKey, nil
	case "sahamaticlientid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SahamatiClientId\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SahamatiClientId, nil
	case "epifiaakid":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EpifiAaKid\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EpifiAaKid, nil
	case "aasecretsversiontouse":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AaSecretsVersionToUse\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AaSecretsVersionToUse, nil
	case "ignosis":
		return obj.Ignosis.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AA", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Ignosis) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "url":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Url\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Url, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Ignosis", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FennelFeatureStore) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "pdscoreurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PdScoreURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PdScoreURL, nil
	case "simulator":
		return obj.Simulator.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FennelFeatureStore", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FennelSimulatorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enable":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Enable\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Enable, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FennelSimulatorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Scienaptic) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "whitelistedfeatures":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.WhitelistedFeatures, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"WhitelistedFeatures\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.WhitelistedFeatures[dynamicFieldPath[1]], nil

		}
		return obj.WhitelistedFeatures, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Scienaptic", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InhouseOCR) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "extractfieldsurlv2":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ExtractFieldsURLV2\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ExtractFieldsURLV2, nil
	case "detectdocumenturl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DetectDocumentURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DetectDocumentURL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InhouseOCR", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Experian) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "v1versionflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"V1VersionFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.V1VersionFlag, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Experian", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Alpaca) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "shouldusesimulatedenvforws":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShouldUseSimulatedEnvForWs\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShouldUseSimulatedEnvForWs, nil
	case "shouldusesimulatedenvforevents":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ShouldUseSimulatedEnvForEvents\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ShouldUseSimulatedEnvForEvents, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Alpaca", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AclSftp) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "port":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Port\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Port, nil
	case "user":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"User\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.User, nil
	case "host":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Host\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Host, nil
	case "password":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Password\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Password, nil
	case "sshkey":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SshKey\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SshKey, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AclSftp", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Lending) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "preapprovedloan":
		return obj.PreApprovedLoan.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Lending", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PreApprovedLoan) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "idfc":
		return obj.Idfc.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PreApprovedLoan", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Idfc) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "url":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Url\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Url, nil
	case "getaccesstokenurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"GetAccessTokenUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.GetAccessTokenUrl, nil
	case "mandatepageurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MandatePageUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MandatePageUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Idfc", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LocationModelConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "inhouseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InHouseUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InHouseUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LocationModelConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CasePrioritisationModelConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "inhouseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InHouseUrl\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InHouseUrl, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CasePrioritisationModelConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IncomeEstimatorConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "inhouseincomeestimatorurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"InhouseIncomeEstimatorURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.InhouseIncomeEstimatorURL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IncomeEstimatorConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Saven) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "jwtexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"JwtExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.JwtExpiry, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Saven", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FederalEscalation) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "baseurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BaseURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BaseURL, nil
	case "createescalationurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CreateEscalationURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CreateEscalationURL, nil
	case "bulkfetchurl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BulkFetchURL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BulkFetchURL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FederalEscalation", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Flags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "logallapiparams":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LogAllAPIParams\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LogAllAPIParams, nil
	case "usenewoccupationincifcreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseNewOccupationInCifCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseNewOccupationInCifCreation, nil
	case "usenewfieldsincifcreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseNewFieldsInCifCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseNewFieldsInCifCreation, nil
	case "usenewfieldsinaccountcreation":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseNewFieldsInAccountCreation\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseNewFieldsInAccountCreation, nil
	case "enabletransactionenquirynewapi":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableTransactionEnquiryNewApi\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableTransactionEnquiryNewApi, nil
	case "enableuatforvkyc":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableUATForVKYC\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableUATForVKYC, nil
	case "enableinstrumentbillinginterceptor":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableInstrumentBillingInterceptor\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableInstrumentBillingInterceptor, nil
	case "disablegstreportingforift":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"DisableGstReportingForIFT\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.DisableGstReportingForIFT, nil
	case "enablefennelclusterv3":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableFennelClusterV3\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableFennelClusterV3, nil
	case "enablecibilv2secrets":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnableCibilV2Secrets\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnableCibilV2Secrets, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Flags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DowntimeConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "downtime":
		return obj.Downtime.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DowntimeConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Downtime) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "dailydowntime":
		return obj.DailyDowntime.Get(dynamicFieldPath[1:])
	case "timestampbaseddowntime":
		return obj.TimestampBasedDowntime.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Downtime", strings.Join(dynamicFieldPath, "."))
	}
}
