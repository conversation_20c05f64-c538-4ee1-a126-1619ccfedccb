//nolint:govet
//go:generate conf_gen github.com/epifi/gamma/vendorgateway/config Config

package config

import (
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strconv"
	"sync"
	"time"

	"github.com/knadh/koanf"
	"github.com/mitchellh/mapstructure"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cfg/dynconf"
	testtenantep "github.com/epifi/gamma/testing/test_tenant/endpoint"
)

var (
	once                sync.Once
	config              *Config
	err                 error
	unmarshallingConfig = func(o interface{}) koanf.UnmarshalConf {
		return koanf.UnmarshalConf{
			DecoderConfig: &mapstructure.DecoderConfig{
				DecodeHook: mapstructure.ComposeDecodeHookFunc(
					mapstructure.StringToTimeDurationHookFunc(),
					mapstructure.StringToSliceHookFunc(","),
					mapstructure.StringToTimeHookFunc(time.RFC822Z),
				),
				WeaklyTypedInput: true,
				Result:           o,
			},
		}
	}
)

var (
	_, b, _, _ = runtime.Caller(0)
)

func Load() (*Config, error) {
	once.Do(func() {
		config, err = loadConfig()
	})

	if err != nil {
		return nil, err
	}
	return config, err
}

// nolint:govet
func loadConfig() (*Config, error) {
	conf := &Config{}
	// will be used only for test environment
	testConfigDirPath := testEnvConfigDir()
	// loads config from file
	k, _, err := cfg.LoadConfigUsingKoanf(testConfigDirPath, cfg.VENDOR_GATEWAY_SERVICE)
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	err = k.UnmarshalWithConf("", conf, unmarshallingConfig(conf))
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	err = cfg.LoadAllSecretsV3(conf, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	keyToSecret, err := cfg.LoadSecrets(conf.Secrets.Ids, conf.Application.Environment, conf.Aws.Region)
	if err != nil {
		return nil, err
	}

	if err := updateDefaultConfig(conf, keyToSecret); err != nil {
		return nil, err
	}
	if cfg.IsTestTenantEnabled() {
		err := testtenantep.UpdateLocalhostEndpoint(&conf)
		if err != nil {
			return nil, err
		}
	}
	return conf, nil
}

// update the env variable
// update the default secret keys in the config with the secret values fetched from Secret manager
//
//nolint:funlen,govet
func updateDefaultConfig(c *Config, keyToSecret map[string]string) error {
	if err := readAndSetEnv(c); err != nil {
		return fmt.Errorf("failed to read and set env var: %w", err)
	}

	// updating service specific secrets
	cfg.UpdateSecretValues(&cfg.DB{GormV2: &cfg.GormV2Conf{}}, c.Secrets, keyToSecret)

	c.Application.Ckyc.FiCode = c.Secrets.Ids[_CkycFiCode]
	if val, ok := c.Secrets.Ids[_liquiloansSecretsKey]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), c.Application.Liquiloans)
			if err != nil {
				fmt.Printf("failed to unmarshal liquiloans secrets\n")
			}
		}
	}
	if val, ok := c.Secrets.Ids[AlpacaSecrets]; ok {
		if val != "" {
			c.Application.Alpaca.Secret = &AlpacaSecret{}
			err := json.Unmarshal([]byte(val), c.Application.Alpaca.Secret)
			if err != nil {
				fmt.Printf("failed to unmarshal Alpaca secrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[CredgenicsAuthenticationKeyV2]; ok {
		if val != "" {
			c.Application.Credgenics.Secret = &CredgenicsSecret{}
			err := json.Unmarshal([]byte(val), c.Application.Credgenics.Secret)
			if err != nil {
				fmt.Printf("failed to unmarshal Credgenics secrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[FederalInternationalFundTransferSecrets]; ok {
		if val != "" {
			c.Application.FederalInternationalFundTransfer.Secret = &FederalInternationalFundTransferSecret{}
			err := json.Unmarshal([]byte(val), c.Application.FederalInternationalFundTransfer.Secret)
			if err != nil {
				fmt.Printf("failed to unmarshal  InternationalFundTransferSecret secrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[cvlSecretsKey]; ok {
		cvlSecrets := &CvlSecrets{}
		err := json.Unmarshal([]byte(val), cvlSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal cvl secrets\n")
		}
		c.Application.CvlSecrets = cvlSecrets
	}

	if val, ok := c.Secrets.Ids[maxmindSecrets]; ok {
		maxmindSecrets := &MaxmindSecrets{}
		err := json.Unmarshal([]byte(val), maxmindSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal maxmind secrets \n")
		}
		c.Application.MaxmindSecrets = maxmindSecrets
	}

	if val, ok := c.Secrets.Ids[bureauSecrets]; ok {
		bureauSecrets := &BureauSecrets{}
		err := json.Unmarshal([]byte(val), bureauSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal bureau secrets \n")
		}
		c.Application.BureauSecrets = bureauSecrets
	}

	if val, ok := c.Secrets.Ids[_preApprovedLoanFederalSecrets]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.Lending.PreApprovedLoan.Federal)
			if err != nil {
				fmt.Printf("failed to unmarshal PreapprovedLoan secrets\n")
			}
		}
	}

	// TODO(@Shivansh) move this into a helper function in pkg/cfg
	if val, ok := c.Secrets.Ids[PreApprovedLoanSecrets]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.Lending.PreApprovedLoan)
			if err != nil {
				fmt.Printf("failed to unmarshal PreApprovedLoan secrets\n")
			}
		}
	}

	_, ok := c.Secrets.Ids[FiIdfcPreApprovedLoanPrivateKey]
	if c.Application.Lending.PreApprovedLoan.Idfc != nil && ok {
		c.Application.Lending.PreApprovedLoan.Idfc.PrivateKey = c.Secrets.Ids[FiIdfcPreApprovedLoanPrivateKey]
	}

	if val, ok := c.Secrets.Ids[LendingMFCentralSecrets]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.Lending.Collateral.LendingMFCentralConfig.Credentials)
			if err != nil {
				fmt.Println("failed to unmarshal Lamf secrets")
			}
		}
	}

	if val, ok := c.Secrets.Ids[LeegalitySecret]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.Esign.Leegality.LeegalitySecrets)
			if err != nil {
				fmt.Printf("failed to unmarshal leegality secrets\n")
			}
		}
	}
	if val, ok := c.Secrets.Ids[signzySecrets]; ok {
		signzySecrets := &SignzySecrets{}
		err := json.Unmarshal([]byte(val), signzySecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal bureau secrets \n")
		}
		c.Application.SignzySecrets = signzySecrets
	}

	if val, ok := c.Secrets.Ids[AAVgSecretsV1]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.AA.AaVgSecretsV1)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg secrets v1\n")
		}
	}

	if val, ok := c.Secrets.Ids[AAVgVnSecretsV1]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.AA.AaVgVnSecretsV1)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg Vn secrets v1\n")
		}
	}

	if val, ok := c.Secrets.Ids[AAVgSecretsV2]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.AA.AaVgSecretsV2)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg secrets v2\n")
		}
	}

	if val, ok := c.Secrets.Ids[AAVgVnSecretsV2]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.AA.AaVgVnSecretsV2)
		if err != nil {
			fmt.Printf("failed to unmarshal Aa Vg Vn secrets v2\n")
		}
	}

	if val, ok := c.Secrets.Ids[federalProfileValidationSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.ProfileValidation.Federal)
		if err != nil {
			fmt.Printf("failed to unmarshal Profile Validation Hunter secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[MorningStarSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.MorningStar.MorningStarSecret)
		if err != nil {
			fmt.Printf("failed to unmarshal morning star secrets\n")
		}
	}
	if val, ok := c.Secrets.Ids[TssApiToken]; ok {
		c.Application.Aml.Tss.Epifi.ApiToken = val
	}
	if val, ok := c.Secrets.Ids[TSSAPITokenForSG]; ok {
		c.Application.Aml.Tss.StockGuardian.ApiToken = val
	}

	tssCloudCredentials, ok := c.Secrets.Ids[TSSCloudCredentials]
	if ok {
		var secretTSSCloud *TSSCloud
		err = json.Unmarshal([]byte(tssCloudCredentials), &secretTSSCloud)
		if err != nil {
			return errors.Wrap(err, "error getting TSS cloud credentials")
		}
		if c.Application.Aml.TSSCloud == nil {
			c.Application.Aml.TSSCloud = &TSSCloud{}
		}
		if c.Application.Aml.TSSCloud.Common == nil {
			c.Application.Aml.TSSCloud.Common = &TSSCloudCommon{}
		}
		if c.Application.Aml.TSSCloud.Tenants == nil {
			c.Application.Aml.TSSCloud.Tenants = make(map[string]*TSSCloudTenant)
		}
		c.Application.Aml.TSSCloud.Common.PublicCertPEMBase64 = secretTSSCloud.Common.PublicCertPEMBase64
		for owner, tenantConf := range c.Application.Aml.TSSCloud.Tenants {
			for secretOwner, secretTenantConf := range secretTSSCloud.Tenants {
				if owner == secretOwner {
					tenantConf.APIToken = secretTenantConf.APIToken
					tenantConf.PrivateKeyPEMBase64 = secretTenantConf.PrivateKeyPEMBase64
				}
			}
		}
	} else {
		fmt.Println("TSS cloud credentials not found")
	}

	if val, ok := c.Secrets.Ids[MorningStarAccountSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.MorningStar.MorningStarAccountSecret)
		if err != nil {
			fmt.Printf("failed to unmarshal morning star account secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[VistaraSecrets]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.Vistara)
			if err != nil {
				fmt.Printf("failed to unmarshal Vistara secrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[SlackSecrets]; ok {
		if val != "" {
			err := json.Unmarshal([]byte(val), &c.Application.SlackTokens)
			if err != nil {
				fmt.Printf("failed to unmarshal Slack-Bot secrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[FennelFeatureStoreSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.FennelFeatureStore.FennelFeatureStoreSecret)
		if err != nil {
			fmt.Printf("failed to unmarshal fennel feature store secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[VKYCAgentDashboardSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.VKYCAgentDashCred)
		if err != nil {
			fmt.Printf("failed to unmarshal vkyc agent dashboard secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[LeadSquaredSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.LeadSquared)
		if err != nil {
			fmt.Printf("failed to unmarshal leadsquared secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[FederalDepositSecrets]; ok {
		if val != "" {
			if c.Application.FederalDeposit == nil {
				c.Application.FederalDeposit = &FederalDeposit{}
			}
			c.Application.FederalDeposit.Secret = &FederalDepositSecret{}
			err := json.Unmarshal([]byte(val), &c.Application.FederalDeposit.Secret)
			if err != nil {
				fmt.Printf("failed to unmarshal FederalDepositSecrets\n")
			}
		}
	}

	if val, ok := c.Secrets.Ids[DreamfolksSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.Dreamfolks)
		if err != nil {
			fmt.Printf("failed to unmarshal dreamfolks secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[PoshvineSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.Poshvine)
		if err != nil {
			fmt.Printf("failed to unmarshal poshvine secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[LendingFiftyFinLamfSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.Lending.SecuredLoans.FiftyFinLamfSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal fiftyfin lamf secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[NsdlUserId]; ok && val != "" {
		c.Application.NsdlKra.UserId = val
	}

	if val, ok := c.Secrets.Ids[CibilSecretsKey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Cibil.CibilSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal cibil secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[ExperianSecretsKey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Experian.ExperianSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal experian secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[RazorpaySecretsKey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Razorpay.Secrets)
		if err != nil {
			fmt.Printf("failed to unmarshal razorpay secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[PanValidationSecretskey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.PanValidationSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal pan validation secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[bureauIdSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.BureauIdSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal pan validation secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[NetCoreEpifiSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.NetCoreEpifi)
		if err != nil {
			fmt.Printf("failed to unmarshal netcore epifi secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[AirtelFedSMSSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.AirtelFedSMS)
		if err != nil {
			fmt.Printf("failed to unmarshal airtel fed-epifi secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[AirtelEpifiSMSSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.AirtelEpifiSMS)
		if err != nil {
			fmt.Printf("failed to unmarshal airtel epifi secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[visaSecretsKey]; ok {
		c.Application.Visa.VisaSecrets = &VisaSecrets{}
		err = json.Unmarshal([]byte(val), c.Application.Visa.VisaSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal visa secrets: %s\n", err.Error())
		}
		err = c.Application.Visa.VisaSecrets.DecodeSecretsFromBase64String()
		if err != nil {
			return fmt.Errorf("failed to decode base64 encoded visa secrets: %w", err)
		}
	}

	if val, ok := c.Secrets.Ids[moEngageSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.MoEngage.MoEngageSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal moengage secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[scienapticSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Scienaptic.ScienapticSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal scienaptic secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[perfiosDigilockerSecrets]; ok {
		c.Application.PerfiosDigiLocker.PerfiosDigilockerSecrets = val
	}

	if val, ok := c.Secrets.Ids[BridgewiseSecrets]; ok {
		err := json.Unmarshal([]byte(val), &c.Application.Bridgewise.BridgewiseSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal bridgewise secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[SavenSecretsKey]; ok {
		c.Application.Saven.SavenSecrets = &SavenSecrets{}
		err := json.Unmarshal([]byte(val), &c.Application.Saven.SavenSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal saven secrets\n")
		}
	}

	if val, ok := c.Secrets.Ids[FederalEscalationSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.FederalEscalation.FederalEscalationSecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal federal secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[SetuBillPaySecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.SetU.SetuBillPaySecrets)
		if err != nil {
			fmt.Printf("failed to unmarshal SetuBillPay secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[SetuMobileRechargeSecrets]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.SetU.SetuMobileRechargeSecret)
		if err != nil {
			fmt.Printf("failed to unmarshal SetuMobileRecharge secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[KarzaSecretsKey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Karza.Secrets)
		if err != nil {
			fmt.Printf("failed to unmarshal federal secrets: %s\n", err.Error())
		}
	}

	if val, ok := c.Secrets.Ids[NuggetSecretsKey]; ok {
		err = json.Unmarshal([]byte(val), &c.Application.Nugget.Secrets)
		if err != nil {
			fmt.Printf("failed to unmarshal nugget secrets: %s\n", err.Error())
		}
	}
	return nil
}

// readAndSetEnv reads and sets env vars to config
func readAndSetEnv(c *Config) error {
	if val, ok := os.LookupEnv("APPLICATION_NAME"); ok {
		c.Application.Name = val
	}

	if val, ok := os.LookupEnv("SERVER_PORT"); ok {
		intVal, err := strconv.Atoi(val)
		if err != nil {
			return fmt.Errorf("failed to convert port to int: %w", err)
		}

		c.Server.Ports.GrpcSecurePort = intVal
	}

	if val, ok := os.LookupEnv("REDIS_HOST"); ok {
		c.RedisOptions.Addr = val
	}

	return nil
}

func testEnvConfigDir() string {
	configPath := filepath.Join(b, "..", "values")
	return configPath
}

type DisputeSFTP struct {
	User         string
	Password     string
	Host         string
	Port         int
	S3BucketName string
}

// TODO(kunal): Add validate tags later and verify config struct post unmarshalling
//
//go:generate conf_gen github.com/epifi/gamma/vendorgateway/config Config
type Config struct {
	Application                            *Application `dynamic:"true"`
	Server                                 *Server
	Logging                                *cfg.Logging
	RedisOptions                           *cfg.RedisOptions
	Aws                                    *Aws
	Secrets                                *cfg.Secrets
	DisputeSFTP                            *DisputeSFTP
	Flags                                  *Flags `dynamic:"true"`
	SecureLogging                          *SecureLogging
	HttpClientConfig                       *cfg.HttpClient
	KarzaEPFPassbookHttpClientConfig       *cfg.HttpClient
	DisputeHTTPClientConfig                *cfg.HttpClient
	Freshdesk                              *cfg.Freshdesk
	FcmAnalyticsLabel                      string
	AwsSes                                 *AwsSes
	HystrixConfig                          *HystrixConfig
	RpcRateLimitConfig                     *cfg.RateLimitConfig `dynamic:"true"`
	XMLDigitalSignatureSigner              *cfg.XMLDigitalSignatureSigner
	TimeoutConfig                          *TimeoutConfig
	DowntimeConfig                         map[string]*DowntimeConfig `dynamic:"true"`
	Tracing                                *cfg.Tracing
	Profiling                              *cfg.Profiling
	MaxAllowedTimeIntervalForMiniStatement time.Duration `dynamic:"true"`
	Crm                                    *cfg.Crm
	DisputeConfig                          *DisputeConfig
	VendorAddresses                        *VendorAddresses
	FederalLien                            *FederalLien
	PGPInMemoryEntityStoreParams           *cfg.PGPInMemoryEntityStoreParams
	GrpcRatelimiterParams                  *cfg.GrpcRateLimiterParams
	GrpcAttributeRatelimiterParams         *cfg.AttributeRateLimiterParams
	// RedactedRawRequestLogExceptionList is the list of urls for which we want to not log the redacted raw request
	// This does not affect secure logging.
	// TODO: remove this after the following is done https://monorail.pointz.in/p/fi-app/issues/detail?id=61941
	RedactedRawRequestLogExceptionList []string
	// RedactedRawResponseLogExceptionList is the list of urls for which we want to not log the redacted raw response
	// This does not affect secure logging.
	RedactedRawResponseLogExceptionList []string
	VideoSdk                            *VideoSdk
	Uqudo                               *Uqudo
	DCIssuance                          *DCIssuance
	// FederalAPICreds contains the credentials for the Federal API for different request sources.
	// The keys of this map can take the enums (string) vendorgateway.RequestSource
	// Example: loans flow FederalAPICreds can be fetched using FederalAPICreds["REQUEST_SOURCE_LOANS"]
	FederalAPICreds map[string]*FederalAPICreds
	// Allows accessing GRPC-based simulator endpoints in remote-debug mode server by skipping TLS handshake.
	// Otherwise, TLS handshake results in certificate verification failure
	// as the simulator certs in remote-debug mode are valid for *.staging.pointz.in, not localhost.
	// Set this to true when accessing local simulator in remote-debug mode to avoid
	// "tls: failed to verify certificate: x509: certificate is valid for *.staging.pointz.in, not localhost" error.
	SkipServerCertVerification bool
}

type SecureLogging struct {
	EnableSecureLog bool
	SecureLogPath   string
	MaxSizeInMBs    int // megabytes
	MaxBackups      int // There will be MaxBackups + 1 total files
}

// SftpSecrets struct is used for unmarshalling sftp secrets in json format into an object for ease of usage.
type SftpSecrets struct {
	User     string `json:"User"`
	Password string `json:"Password"`
	SshKey   string `json:"SshKey"`
}

// a map b/w psp handle and upi orgId
// eg: "fede" <> "400021"
type PspHandleToUpiOrgIdMap map[string]string

type Application struct {
	Environment   string
	Name          string
	IsSecureRedis bool
	// Timeout period(in seconds) until which sync wrapper should wait for the response
	SyncWrapperTimeout          int
	VGAuthSvcSyncWrapperTimeout int
	IsStatementAPIEnabled       bool
	// Boolean flag to identify if list keys is pointing to
	// simulator or partner bank
	IsListKeysSimulated           bool
	CreateCustomerURL             string
	CreateLoanCustomerURL         string
	LoanCustomerCreationStatusURL string
	CheckCustomerStatusURL        string
	DedupeCheckURL                string
	FetchCustomerDetailsUrl       string
	EnquireVKYCStatusUrl          string
	CreateAccountURL              string
	CheckAccountStatusURL         string
	EnquireBalanceURL             string
	CkycSearchURL                 string
	GetKycDataURL                 string
	VerifyCkycOtpURL              string
	// url to raise dispute on Federal's DMP system
	CreateDisputeURL string
	// url to check status of raised dispute on Federal's DMP system
	DisputeStatusCheckUrl string
	// url to bulk check status of raised dispute on Federal's DMP system
	BulkDisputeStatusCheckUrl string
	// url to upload document against a raised dispute on Federal's DMP system
	UploadDocumentUrl string
	// url to attach correspondence against a raised dispute on Federal's DMP system
	SendCorrespondenceUrl string

	ChannelQuestionnaireUrl string

	AccountTransactionsUrl string

	PanProfileURL string

	Federal                    *Federal
	LeadSquared                *LeadSquared
	M2P                        *M2p
	AAConsentRequestURL        string
	AAConsentHandleURL         string
	AAGetConsentStatusURL      string
	AARequestDataURL           string
	AAFetchDataURL             string
	CreateVirtualIdURL         string
	GetTokenURL                string
	DeviceRegistrationURL      string
	SetPINURL                  string
	GenerateUpiOtpURL          string
	ValidateAddressURL         string
	RespAuthDetailsURL         string
	RespAuthValCustURL         string
	RespAuthMandateURL         string
	RespMandateConfirmationURL string
	ReqPayURL                  string
	ReqMandateURL              string
	RegisterMobileURL          string
	ListUpiKeyUrl              string
	ListAccountURL             string
	ListAccountProviderURL     string
	UPIBalanceEnquiryURL       string
	ReqComplaintURL            string
	ReqCheckComplaintStatusUrl string
	ReqActivationUrl           string
	ListPspURL                 string
	RegMapperURL               string
	GetMapperInfoURL           string
	ReqValQRUrl                string
	RespMapperConfirmationURL  string
	// Map of vpa handle suffix to Org ID to be used for NPCI APIs
	PspHandleToUpiOrgIdMap   *PspHandleToUpiOrgIdMap
	FreshdeskAgentURL        string
	FreshdeskTicketURL       string
	FreshdeskFilterTicketURL string
	FreshdeskSolutionsURL    string
	FreshdeskContactsURL     string
	FreshdeskTicketFieldURL  string
	RespTxnConfirmationURL   string
	RespValidateAddressURL   string
	ReqCheckTxnStatusURL     string
	FreshchatConversationURL string
	FreshchatUserURL         string
	FreshchatAgentURL        string
	SenseforthEventURL       string
	ListVaeURL               string
	GetUpiLiteURL            string
	SyncUpiLiteInfoURL       string
	Exotel                   *Exotel
	Twilio                   *Twilio
	GPlace                   *Gplace
	Loylty                   *Loylty
	Qwikcilver               *Qwikcilver
	Thriwe                   *Thriwe
	Dreamfolks               *Dreamfolks
	Poshvine                 *Poshvine
	Riskcovry                *Riskcovry
	Onsurity                 *Onsurity
	AclEpifi                 *AclEpifi
	AclFederal               *AclFederal
	AclEpifiOtp              *AclEpifiOtp
	KaleyraFederal           *KaleyraFederal
	KaleyraEpifi             *KaleyraEpifi
	KaleyraFederalCreditCard *KaleyraFederalCreditCard
	KaleyraEpifiNR           *KaleyraEpifiNR
	KaleyraSmsCallbackURL    string
	NetCoreEpifi             *NetCoreEpifi
	AirtelFedSMS             *AirtelFedSMS
	AirtelEpifiSMS           *AirtelEpifiSMS
	AclWhatsapp              *AclWhatsapp
	Employment               *Employment
	Roanuz                   *Roanuz
	IPStack                  *IPStack
	Shipway                  *Shipway
	CAMS                     *Cams
	Karvy                    *Karvy
	MFCentral                *MFCentral
	SmallCase                *SmallCase `dynamic:"true"`
	InHouseAAParserURL       string
	InHouseAABulkParserURL   string

	// international fund transfer
	FederalInternationalFundTransfer *FederalInternationalFundTransfer

	// Liveness
	Veri5CheckLivenessRequestURL        string
	Veri5MatchFaceRequestURL            string
	KarzaCheckLivenessRequestURL        string
	KarzaLivenessCallbackURL            string
	KarzaMatchFaceRequestURL            string
	KarzaCheckPassiveLivenessRequestURL string
	KarzaCheckLivenessStatusURL         string
	GetEPANKarzaStatusURL               string
	InhouseGetAndValidateEPANURL        string
	InhouseCheckLivenessRequestURL      string
	InhouseMatchFaceRequestURL          string `dynamic:"true"`
	InhouseMatchFaceRequestURLV2        string `dynamic:"true"`
	UseFormMarshalForKarza              bool
	UseFormMarshalForKarzaFM            bool
	SendAgentDataURL                    string
	SendAuditorDataURL                  string

	// ITR
	InhouseVerifyAndGetITRIntimationDetailsURL string

	// Ozonetel
	OzonetelManualDialUrl string
	OzonetelUserName      string
	OzonetelCampaignName  string

	PayAckStatusCodeJson string

	// pay status code json file path
	PayFundTransferStatusCodeJson  string
	PayUpiStatusCodeJson           string
	EnachTransactionStatusCodeJson string
	Karza                          *Karza `dynamic:"true"`

	DepositAckStatusCodeFilePath      string
	DepositResponseStatusCodeFilePath string

	CreateCustomerCallBackUrl string
	CreateAccountCallBackUrl  string

	CardResponseStatusCodeFilePath string
	AA                             *AA `dynamic:"true"`
	Tiering                        *Tiering
	BouncyCastle                   *BouncyCastle
	FennelFeatureStore             *FennelFeatureStore `dynamic:"true"`
	Scienaptic                     *Scienaptic         `dynamic:"true"`
	CvlKra                         *CvlKra
	NsdlKra                        *NsdlKra
	Ckyc                           *Ckyc
	FederalDeposit                 *FederalDeposit
	Manch                          *Manch
	Digio                          *Digio
	WealthKarza                    *WealthKarza
	InhouseOCR                     *InhouseOCR `dynamic:"true"`
	Digilocker                     *Digilocker

	SIResponseStatusCodeFilePath      string
	InhouseNameCheckUrl               string `dynamic:"true"`
	InhouseEmployerNameMatchUrl       string
	InhouseEmployerNameCategoriserUrl string
	Experian                          *Experian `dynamic:"true"`
	Cibil                             *Cibil

	// DL Validation
	DrivingLicenseValidationUrl string

	// Voter_Id Validation
	VoterIdValidationUrl string

	Seon *Seon

	InhousePopularFAQUrl string

	// liquiloans config for p2p investments
	Liquiloans *Liquiloans

	// Alpaca config for US Stock
	Alpaca *Alpaca `dynamic:"true"`

	AclSftp *AclSftp `dynamic:"true"`

	// lending config
	Lending *Lending `dynamic:"true"`

	// Esign config
	Esign *Esign

	// ProfileValidation Config
	ProfileValidation *ProfileValidation

	// cvl secrets read from sm as json
	CvlSecrets *CvlSecrets

	GoogleReverseGeocodingUrl string `dynamic:"true"`
	GoogleGeocodingUrl        string `dynamic:"true"`

	// bank account verification
	BankAccountVerificationUrl string

	// Maxmind's Ip -> Geo lookup API
	MaxmindIp2CityUrlPrefix string
	MaxmindSecrets          *MaxmindSecrets

	PAYUAffluenceURL string

	// Bureau's Phone Number Details API
	BureauPhoneNumberDetailsUrl string
	BureauSecrets               *BureauSecrets

	// TransactionMonitoring
	DronapayHostURL string

	InhouseRiskServiceURL             string                         `dynamic:"true"`
	InhouseRiskServiceURLV1           string                         `dynamic:"true"`
	InhouseReonboardingRiskServiceURL string                         `dynamic:"true"`
	LocationModel                     *LocationModelConfig           `dynamic:"true"`
	CasePrioritisationModel           *CasePrioritisationModelConfig `dynamic:"true"`
	SignzySecrets                     *SignzySecrets
	Aml                               *Aml
	// MorningStar config for catalog of us stock
	MorningStar *MorningStar

	InhouseMerchantResolutionServiceUrl string

	// We can end up using different freshdesk accounts based on
	// data ownership and use case for which we are using the CRM.
	// map[Ownership][UseCase] will contain account specific config as value
	FreshdeskAccountConfig map[string]map[string]*FreshdeskAccountConfig
	// Paths for freshdesk resources
	FreshdeskURI *FreshdeskURI

	Vistara *Vistara

	SlackTokens *SlackTokens

	InhouseLocationServiceUrl string
	IncomeEstimatorConf       *IncomeEstimatorConfig `dynamic:"true"`

	VKYCAgentDashCred *VKYCAgentDashCred

	// Config related to the vendor Credgenics
	// we use credgenics for debt collection and recovery of loans from customers
	Credgenics *Credgenics

	// Config related to Vkyc service
	Vkyc *Vkyc

	EnachConfig *EnachConfig

	// S3 bucket which contains ticket attachment files
	CxFreshdeskTicketAttachmentsBucketName string `iam:"s3-readwrite"`

	InhouseBreForCCUrl string

	EpanConfig *EpanConfig

	// Payment Gateways
	Razorpay *Razorpay

	PanValidationSecrets *PanValidationSecrets

	BureauIdUrl     string
	BureauIdSecrets *BureauIdSecrets

	// VISA
	Visa *Visa

	MoEngage *MoEngage

	PerfiosDigiLocker *PerfiosDigiLocker

	// SetU
	SetU *SetU

	Bridgewise *Bridgewise
	Nps        *Nps

	Saven *Saven `dynamic:"true"`

	FederalEscalation *FederalEscalation `dynamic:"true"`

	// Zenduty integration URL for sending alerts
	ZendutyWebhookUrl string `dynamic:"true"`

	Nugget *Nugget
}

type FederalEscalation struct {
	BaseURL                  string `dynamic:"true"`
	CreateEscalationURL      string `dynamic:"true"`
	BulkFetchURL             string `dynamic:"true"`
	FederalEscalationSecrets *FederalEscalationSecret
	S3BucketName             string `iam:"s3-readwrite"`
}

type FederalEscalationSecret struct {
	ClientSecret string `json:"ClientSecret"`
	ClientId     string `json:"ClientId"`
	Username     string `json:"Username"`
	Password     string `json:"Password"`
}

type Saven struct {
	SavenSecrets      *SavenSecrets
	CreditCardBaseUrl string
	JwtExpiry         time.Duration `dynamic:"true"`
}

type SavenSecrets struct {
	ApiKey                  string `json:"ApiKey"`
	PayloadSigningKeyBase64 string `json:"PayloadSigningKeyBase64"`
}

type MoEngage struct {
	BaseUrl         string
	MoEngageSecrets *MoEngageSecrets
}

type MoEngageSecrets struct {
	AppId  string `json:"AppId"`
	ApiKey string `json:"ApiKey"`
}

type Visa struct {
	VisaSecrets                              *VisaSecrets
	FindNearbyAtmTotalsUrl                   string
	FindNearbyAtmsUrl                        string
	FindGeocodesUrl                          string
	GetEnhancedForeignExchangeRatesUrl       string
	GetEnhancedMarkupForeignExchangeRatesUrl string
}

type VisaSecrets struct {
	Key      string `json:"key"`
	Cert     string `json:"cert"`
	UserId   string `json:"UserId"`
	Password string `json:"Password"`
}

type Finflux struct {
	BaseUrl            string
	Auth               *FinfluxAuth `json:"Auth"`
	Charges            *FinfluxCharges
	PreclosureReasonId int64
	// This is a map against loanStatusVendorId and Value is the stringified version of api.vendorgateway.lending.lms.finflux.types.LoanStatus
	LoanStatusVendorIdToEnumValue map[int32]string `yaml:"LoanStatusVendorIdToEnumValue"`
}

type FinfluxAuth struct {
	// populated from secrets (in prod)
	Username string `json:"Username"`
	// populated from secrets (in prod)
	Password              string `json:"Password"`
	IsPasswordEncrypted   bool
	TokenValidityDuration time.Duration
}

type FinfluxCharges struct {
	ProcessingFeeChargeId int32
}

type Razorpay struct {
	Secrets *RazorpaySecrets
	// refers to the host of razorpay. This will help us avoid adding API urls
	// for all the APIs. The base url will be constant and the entire url construction
	// will happen at the time of implementation
	BaseUrl string
}

type RazorpaySecrets struct {
	KeyId     string `json:"KeyId"`
	KeySecret string `json:"KeySecret"`
}

type CredgenicsAuthenticationSecrets struct {
	ClientId     string `json:"CLIENT_ID"`
	ClientSecret string `json:"CLIENT_SECRET"`
}

type CredgenicsSecret struct {
	EpifiAuthenticationSecrets CredgenicsAuthenticationSecrets `json:"EPIFI_AUTH_SECRETS"`
	SgAuthenticationSecrets    CredgenicsAuthenticationSecrets `json:"SG_AUTH_SECRETS"`
}

type Credgenics struct {
	Secret  *CredgenicsSecret
	BaseUrl string
}

type Employment struct {
	KarzaPFOTPURL                      string
	KarzaPFPassbookURL                 string
	KarzaEmploymentVerificationURL     string
	KarzaSearchCompanyNameURL          string
	KarzaUANLookupURL                  string
	KarzaEPFAuthURL                    string
	KarzaEmployeeNameSearchURL         string
	KarzaCompanyMasterLLPDataURL       string
	KarzaSearchGSTINBasisPAN           string
	KarzaGetForm16QuarterlyURL         string
	KarzaGetEmployerDetailsByGstinURL  string
	KarzaGetUANFromPan                 string
	SignzyLoginURL                     string
	SignzyDomainNameVerificationURL    string
	KarzaFindUanByPan                  string
	TartanUpdateEmployeeBankDetailsURL string
	TartanGetHrmsDetailsURL            string
	TartanGetEmployerDetailsURL        string
}

type Exotel struct {
	URL        string
	ApiToken   string
	AccountSid string
	ApiKey     string
	SenderId   string
}

type Twilio struct {
	URL        string
	AccountSid string
	ApiToken   string
	SenderId   string
}

type Gplace struct {
	GetPlaceDetailsReqURL string
	FindPlaceReqURL       string
}

type Cams struct {
	OrderFeedFileURL       string
	OrderFeedFileStatusURL string
	S3Bucket               string `iam:"s3-readwrite"`
	FATCAFileURL           string
	ElogFileURL            string
	OrderFeedFileSyncURL   string
	NFTFileURL             string
	GetFolioDetailsURL     string
	NomineeUpdateURL       string
	UserCode               string
	BrokerCode             string
}

type MFCentral struct {
	GenerateTokenURL        string
	EncryptAndSignURL       string
	VerifyAndDecryptURL     string
	UpdateFolioEmailURL     string
	UpdateFolioMobileURL    string
	InvestorConsentUrl      string
	SubmitCasSummaryUrl     string
	GetCasDocumentUrl       string
	GetTransactionStatusUrl string
}

type SmallCase struct {
	CreateTransactionURL          string
	InitiateHoldingsImportURL     string
	TriggerHoldingsImportFetchURL string
	MFAnalyticsURL                string
	SmallCaseGateway              string
	// If true, the new NO_HOLDINGS_FOUND will be returned by the InitiateHoldingsImport API
	// TODO: clean this up once deployment is stable
	EnableNotFoundInInitHoldingsImport bool `dynamic:"true"`
}

type AclFederal struct {
	FallbackURL string
	URL         string
	AppId       string
	UserId      string
	Password    string
	SenderId    string
}

type AclEpifi struct {
	FallbackURL string
	URL         string
	AppId       string
	UserId      string
	Password    string
	SenderId    string
}

type FederalInternationalFundTransfer struct {
	URL                          string
	CheckLRSEligibilityPrecision int32
	Secret                       *FederalInternationalFundTransferSecret
}

type FederalInternationalFundTransferSecret struct {
	Channel  string `json:"Channel"`
	ClientId string `json:"ClientId"`
	RateCode string `json:"RateCode"`
}

type FederalDeposit struct {
	Secret *FederalDepositSecret
}

type FederalDepositSecret struct {
	CalculateInterestDetailsSenderCode       string `json:"CalculateInterestDetailsSenderCode"`
	CalculateInterestDetailsSenderAccessId   string `json:"CalculateInterestDetailsSenderAccessId"`
	CalculateInterestDetailsSenderAccessCode string `json:"CalculateInterestDetailsSenderAccessCode"`
}

type AclEpifiOtp struct {
	FallbackURL string
	URL         string
	AppId       string
	UserId      string
	Password    string
	SenderId    string
}

type KaleyraFederal struct {
	URL      string
	SenderId string
}

type KaleyraEpifi struct {
	URL      string
	SenderId string
}

type KaleyraEpifiNR struct {
	URL               string
	SenderId          string
	CallbackProfileId string
}

type NetCoreEpifi struct {
	URL      string
	FeedId   string
	Username string `json:"username"`
	Password string `json:"password"`
}

type AirtelFedSMS struct {
	URL                     string
	SenderId                string
	CustomerId              string
	AuthToken               string `json:"authToken"`
	Username                string `json:"username"`
	Password                string `json:"password"`
	RedirectSMSDlrConfigMap map[string]*RedirectSMSDlrConfig
}

type AirtelEpifiSMS struct {
	URL        string
	SenderId   string
	CustomerId string
	AuthToken  string `json:"authToken"`
	Username   string `json:"username"`
	Password   string `json:"password"`
}

type AclWhatsapp struct {
	URL      string
	OptInURL string
}

type KaleyraFederalCreditCard struct {
	URL      string
	SenderId string
}

type RedirectSMSDlrConfig struct {
	DlrUrl string
}

type Loylty struct {
	AuthTokenURL             string
	GiftCardBookingURL       string
	CharityBookingURL        string
	GiftCardProductListURL   string
	GiftCardProductDetailURL string
	CreateOrderURL           string
	ConfirmOrderURL          string
	GetOrderDetailsURL       string
}

type Qwikcilver struct {
	GetAuthorizationCodeBaseUrl    string
	GetAccessTokenBaseUrl          string
	CreateOrderBaseUrl             string
	GetActivatedCardDetailsBaseUrl string
	GetCategoryDetailsBaseUrl      string
	GetOrderStatusBaseUrl          string
	GetProductDetailsBaseUrl       string
	GetProductListBaseUrl          string
	// validity of vendor access token.
	AccessTokenValidityDuration time.Duration
	// internal email id for receiving egv order details from vendor.
	MailOrderDetailsTo string
}

type Thriwe struct {
	BaseUrl string
}

type Dreamfolks struct {
	BaseUrl   string
	ProgramId string
	ServiceId string
	ApiKey    string `json:"ApiKey"`
	ApiSecret string `json:"ApiSecret"`
}

type Poshvine struct {
	/** Common configs **/
	BaseUrl        string
	SSOUrl         string
	UpdateUrl      string
	FiClientId     string
	ExpiresAt      time.Duration
	RedirectionUrl string
	/** User Registration configs **/
	SecretKey string `json:"key"`
	Iv        string `json:"iv"`
	Mode      string `json:"mode"`
	/** UpdateAPI configs **/
	UpdateApiSecretKey    string `json:"UpdateApiSecretKey"`
	UpdateApiIv           string `json:"UpdateApiIv"`
	UpdateApiClientApiKey string `json:"UpdateApiClientApiKey"`
}

type Riskcovry struct {
	BaseUrl string
}

type Onsurity struct {
	BaseUrl string
}

type Federal struct {
	// Pay Service
	PayIntraBankURL         string
	PayIntraBankCallbackURL string
	PayNEFTURL              string
	PayNEFTCallbackURL      string
	PayIMPSURL              string
	PayIMPSCallbackURL      string
	PayRTGSURL              string
	PayRTGSCallbackURL      string
	PayStatusURL            string
	PayIntraBankDepositURL  string

	// Shipping Preference Service
	ShippingAddressUpdateURL         string
	ShippingAddressUpdateCallbackURL string

	// B2C Pay Service
	PayB2CIntraBankURL         string
	PayB2CIntraBankCallbackURL string
	PayB2CStatusURL            string
	PayB2CBalanceURL           string
	PayB2CImpsURL              string
	PayB2CImpsCallbackURL      string

	// Card Service
	DebitCardCreateURL                   string
	DebitCardCreateCallbackURL           string
	DebitCardActivateURL                 string
	DebitCardEnquiryUrl                  string
	DebitCardPinSetUrl                   string
	DebitCardPinChangeUrl                string
	DebitCardPinResetUrl                 string
	DebitCardPinValidationUrl            string
	DebitCardValidationUrl               string
	DebitCardBlockUrl                    string
	DebitCardSuspendOnOffUrl             string
	DebitCardLocationOnOffUrl            string
	DebitCardECommerceOnOffUrl           string
	DebitCardCVVEnquiryUrl               string
	DebitCardLimitEnquiry                string
	DebitCardUpdateLimit                 string
	DebitCardDeliveryTracking            string
	DebitCardPhysicalDispatchUrl         string
	DebitCardPhysicalDispatchCallbackUrl string
	DebitCardConsolidatedCardControlUrl  string
	CheckDebitCardIssuanceFeeStatusUrl   string
	DebitCardCollectIssuanceFeeUrl       string

	// PAN Service
	PANValidationURL        string
	PANAadhaarValidationURL string

	EkycNameDobValidationURL  string
	AadharMobileValidationURL string
	// Name Check Service
	UNNameCheckURL        string
	ShareDocWithVendorURL string

	// Device Re-registration / Profile Update API
	DeviceReRegURL         string
	DeviceReRegCallbackUrl string

	// Device De-registration / Deactivate User
	DeviceDeRegURL string

	// Generate OTP Service
	GenerateOTPURL string

	DeviceReactivationURL string
	// deposit
	CreateDepositCallbackUrl     string
	PreCloseDepositCallbackUrl   string
	CreateFDURL                  string
	CreateSDURL                  string
	CreateRDURL                  string
	CloseDepositAccountURL       string
	GetDepositAccountDetailURL   string
	GetPreClosureDetailURL       string
	CheckDepositAccountStatusURL string
	DepositListAccountURL        string
	InterestRateInfoURL          string
	CalculateInterestDetailsURL  string
	AutoRenewFdURL               string
	AutoRenewFdCallbackUrl       string

	// standingInstruction
	CreateSIUrl   string
	ExecuteSIUrl  string
	SICallbackUrl string
	ModifySIUrl   string
	RevokeSIUrl   string

	CityCodesCsv string

	StateCodesCsv string

	CountryCodesCsv string

	// Account
	OpeningBalanceURL string

	ClosingBalanceURL string

	EnquireBalanceV1URL string

	// Account Statement
	AccountStatementURL string

	// Mini Statement
	MiniStatementURL string

	// Third party account collection
	ThirdPartyAccountCollectionURL string

	// Account statement By DR api url
	AccountStatementByDRApiUrl string

	// Partner SDK
	GetSessionParamsUrl string

	// Update Nominee
	UpdateNomineeUrl     string
	UpdateNomineeChannel string

	// Enquiry status url
	CustomerCreationEnquiryStatusURL            string
	AccountCreationEnquiryStatusURL             string
	CardCreationEnquiryStatusURL                string
	DeviceReRegistrationDetailsEnquiryStatusURL string
	MailingAddressModifyDetailsEnquiryStatusURL string
	ShippingAddressUpdateEnquiryStatusURL       string
	DeviceRegistrationDetailsEnquiryStatusURL   string
	PhysicalCardDispatchDetailsEnquiryStatus    string
	// Remitter Details url
	RemitterDetailsFetchUrl string
	// Remitter details url v1 to fetch remitter info for upi/intra/imps
	RemitterDetailsV1FetchUrl string

	// Federal beneficiary name lookup API URL.
	BeneficiaryNameLookupUrl string

	// order cheque book url
	OrderChequebookUrl string

	// track cheque book url
	TrackChequebookUrl string

	// issue digital cancelled cheque url
	IssueDigitalCancelledChequeUrl string

	ProfileUpdateUrl string

	ProfileUpdateEnquiryUrl string

	// Account status url
	AccountStatusURL string

	// CsisStatus fetch url
	GetCsisStatusUrl string

	// Lien marking url
	LienUrl string

	// e-nach service url
	ListEnachUrl string

	// API URL for fetching the transactions corresponding to an ENACH mandate
	FetchEnachTransactionsUrl string

	TcsCalculationURL string

	TcsCalculationChannelId string

	CustomerDetailsInsertURL string

	CreateCustomerForNonResidentURL string

	CheckCustomerStatusForNonResidentURL string

	PanValidationV2Url string

	InquireOrReportGSTCollectionURL string

	InquireOrReportGSTCollectionChannelId string
}

type LeadSquared struct {
	CreateOrUpdateLeadUrl string
	AccessKey             string
	SecretKey             string
}

type Server struct {
	Ports *cfg.ServerPorts
}

type Aws struct {
	Region string
}

type Karza struct {
	GenerateSessionTokenUrl string
	AddNewCustomerUrl       string
	// new vkyc registration API end point
	// this supports income occupation details in request
	AddNewCustomerV3Url               string
	UpdateCustomerV3Url               string
	GenerateCustomerTokenUrl          string
	GetSlotUrl                        string
	BookSlotUrl                       string
	GenerateWebLinkUrl                string
	SlotAgentsUrl                     string
	TransactionStatusEnquiryUrl       string
	ReScheduleSlotUrl                 string
	TriggerCallback                   string
	AgentDashboardUrl                 string
	AgentDashboardAuthUrl             string
	UpdateCustomerV3UATUrl            string
	AddNewCustomerV3UATUrl            string
	GenerateCustomerTokenUATUrl       string
	GenerateWebLinkUATUrl             string
	TransactionStatusEnquiryUATUrl    string
	GenerateSessionTokenUATUrl        string
	EmploymentVerificationAdvancedUrl string
	KycOcrUrl                         string `dynamic:"true"`
	PassportVerificationURL           string
	Secrets                           *KarzaSecrets
}

type Flags struct {
	// Log the raw request and response of all the APIs including the final proto message after unmarshalling.
	// The logs are enabled only in non prod envs
	LogAllAPIParams bool `dynamic:"true"`
	// A flag to determine if the debug message in Status is to be trimmed
	TrimDebugMessageFromStatus bool
	// A flag to determine if token is validated at federal's end for enabling card control instead of cred block
	// when token is not expired
	// This is a short term hack and we will stop using this flag all together when changes have been made at vendor's end.
	TokenValidation bool

	// A flag to identify vendor notification flow for asycn request handling for vendors request.
	UseAsyncNotificationCallback bool

	// A flag to determine if a custom cert pool is to be used of the system cert pool
	UseCustomTrustedCertPool bool

	// controls value of InsecureSkipVerify for PAN Validation API
	SkipCertVerifyPANValidation bool

	// Allows special character in address being sent to Federal except a subset of special characters: {'!', '^', '|'}
	// TODO (bhrigu) : To be removed post deployment after few days
	AllowSpecialCharactersInAddress bool

	// Flag to use fallback key for RSA cryptor
	// If true we will try decryption first using the existing key and in case of failures try with the fallback key
	EnableFederalCardDecryptionByFallbackKey bool

	// Federal has assigned a new SolID to Epifi, adding a flag to manage the value
	UseNewSolID bool

	// Flag to use newly captured employmentData.OccupationType in CIF creation
	UseNewOccupationInCifCreation bool `dynamic:"true"`

	// Flag to use new fields like CustomerStatus, Category, Pep and SubCategoryOccupation in CIF creation
	UseNewFieldsInCifCreation bool `dynamic:"true"`

	// Flag to use new fields like SourceOfFunds, AnnualTxnVolume in account creation
	UseNewFieldsInAccountCreation bool `dynamic:"true"`

	// flag to use new transaction status enquiry api
	EnableTransactionEnquiryNewApi bool `dynamic:"true"`

	EnableUATForVKYC bool `dynamic:"true"`

	EnableInstrumentBillingInterceptor bool `dynamic:"true"`

	// Disable Gst reporting for ift
	DisableGstReportingForIFT bool `dynamic:"true"`

	EnableFennelClusterV3 bool `dynamic:"true"`

	// Enable Cibil V2 secrets
	EnableCibilV2Secrets bool `dynamic:"true"`
}

type AwsSes struct {
	FiMoneyArn       string
	FiCareArn        string
	StockGuardianArn string
	ConfigSet        string
	// A contact list is a list of all contacts with details on subscription to a particular topic or topics.
	// Ref: https://docs.google.com/document/d/16iuVnvjzPF81mzpi5_yWfSyu-QSQ_g1MsF9HTCzpKm4/edit#
	ContactListName string
	// The text to be appended to the email for which an unsubscribe option should be given
	// This includes the placeholder for the aws unsubscribe link
	// Ref: https://docs.google.com/document/d/16iuVnvjzPF81mzpi5_yWfSyu-QSQ_g1MsF9HTCzpKm4/edit#
	UnsubscribeOptionPlaceholder string
}

type Roanuz struct {
	GenerateCricketAccessTokenUrl  string
	CricketURL                     string
	GenerateFootballAccessTokenUrl string
	FootballUrl                    string
	FootballAppId                  string
	FootballDeviceId               string
}

type Shipway struct {
	BulkUploadShipmentDataUrl string
	GetShipmentDetailsUrl     string
	AddOrUpdateWebhookUrl     string
	UploadShipmentDataUrl     string
}

type IPStack struct {
	GetLocationDetailFromIpUrl string
}

type AA struct {
	BaseURL                     string `dynamic:"true"`
	PostConsentURL              string `dynamic:"true"`
	ConsentStatusURL            string `dynamic:"true"`
	ConsentArtefactURL          string `dynamic:"true"`
	ConsentArtefactV2URL        string `dynamic:"true"`
	RequestDataURL              string `dynamic:"true"`
	FetchDataURL                string `dynamic:"true"`
	GenerateAccessTokenURL      string `dynamic:"true"`
	FetchCrEntityDetailURL      string `dynamic:"true"`
	FetchCrEntityDetailURLV2    string `dynamic:"true"`
	GetAccountLinkStatusURL     string `dynamic:"true"`
	AccountDeLinkURL            string `dynamic:"true"`
	ConsentUpdateURL            string `dynamic:"true"`
	UseSahamatiCrAndToken       bool   `dynamic:"true"`
	OneMoneyCrId                string `dynamic:"true"`
	FinvuCrId                   string `dynamic:"true"`
	GetAccountLinkStatusBulkURL string `dynamic:"true"`
	GetHeartbeatStatusURL       string `dynamic:"true"`
	GetBulkConsentRequestURL    string `dynamic:"true"`
	GenerateFinvuJwtTokenURL    string `dynamic:"true"`
	AAClientApiKey              string `dynamic:"true"`
	SahamatiClientId            string `dynamic:"true"`
	EpifiAaKid                  string `dynamic:"true"`
	AaVgSecretsV1               *AaVgSecrets
	AaVgVnSecretsV1             *AaVgVnSecrets
	AaVgSecretsV2               *AaVgSecrets
	AaVgVnSecretsV2             *AaVgVnSecrets
	AaSecretsVersionToUse       string `dynamic:"true"`
	FinvuFipMetricsURL          string
	IsOnemoneyV2Enabled         bool     `dynamic:"true"`
	IsFinvuV2Enabled            bool     `dynamic:"true"`
	Ignosis                     *Ignosis `dynamic:"true"`
}

type Ignosis struct {
	Url string `dynamic:"true"`
}

type Tiering struct {
	AddSchemeChangeURL     string
	EnquireSchemeChangeURL string
}

type AaVgSecrets struct {
	SahamatiClientSecret string `json:"sahamati_client_secret"`
	FinvuEntityKey       string `json:"finvu_entity_key"`
	IgnosisApiKey        string `json:"ignosis_api_key"`
}

type AaVgVnSecrets struct {
	EpifiAaPrivateKey string `json:"epifi_aa_private_key"`
}

type BouncyCastle struct {
	GenerateKeyPairURL      string
	GetSharedSecretURL      string
	DecryptDataURL          string
	CkycEncryptAndSignURL   string
	CkycVerifyAndDecryptURL string
}

type FennelFeatureStore struct {
	ExtractFeatureSetsURLV2  string
	ExtractFeatureSetsURLV3  string
	LogDatasetsURL           string
	LogDatasetsURLV2         string
	LogDatasetsURLV3         string
	FennelFeatureStoreSecret *FennelFeatureStoreSecret
	Simulator                *FennelSimulatorConfig `dynamic:"true"`
	PdScoreURL               string                 `dynamic:"true"`
}

type FennelSimulatorConfig struct {
	// flag to enable simulator for fennel
	Enable bool `dynamic:"true"`
	// Simulator URL for extract feature sets
	ExtractFeatureSetsURL string
	// Simulator URL for log datasets
	LogDatasetsURL string
	// workflows whose request is allowed to be routed to simulator
	AllowedWorkflowsForSimulation []string
}

type FennelFeatureStoreSecret struct {
	BearerToken   string `json:"bearer_token"`
	BearerTokenV2 string `json:"bearer_token_v2"`
}

type Scienaptic struct {
	GenerateSmsFeaturesURL string
	ScienapticSecrets      *ScienapticSecrets
	WhitelistedFeatures    map[string]bool `dynamic:"true"`
}

type ScienapticSecrets struct {
	AppAuthToken string `json:"AppAuthToken"`
}

type Experian struct {
	CheckCreditReportPresenceURL          string
	FetchCreditReportURL                  string
	FetchCreditReportForExistingUserURL   string
	FetchExtendSubscriptionURL            string
	FetchAccessTokenUrl                   string
	V1VersionFlag                         bool `dynamic:"true"`
	FetchCreditReportURLV1                string
	FetchCreditReportForExistingUserURLV1 string
	FetchExtendSubscriptionURLV1          string
	ExperianSecrets                       *ExperianSecrets
}

type ExperianSecrets struct {
	UserName     string `json:"UserName"`
	Password     string `json:"Password"`
	ClientId     string `json:"ClientId"`
	ClientSecret string `json:"ClientSecret"`
}

type Ckyc struct {
	SearchURL     string
	FiCode        string
	ApiVersion    string
	DownloadURL   string
	EnableCryptor bool
}

type CvlKra struct {
	SoapHost           string
	PanEnquiryURL      string
	InsertUpdateKycURL string
	// SFTP server params
	Host string
	Port int
}

type NsdlKra struct {
	// Epifi Wealth is a client of NSDL
	// This user ID is assigned by NSDL to Epifi Wealth
	UserId string

	PanInquiryURL        string
	GenerateSignatureURL string
	// endpoint for the v4 pan inquiry api (takes pan, dob and name and validates on their end)
	PerformPanInquiryV4URL string

	// If disabled, requests to NSDL will not be signed using Epifi Wealth unique digital certificate
	// Signing can be skipped when responses from NSDL are being simulated
	// as the signature verification by NSDL is not being simulated currently
	DisableSigning bool
}

type Manch struct {
	TransactionsURL string
	DocumentsURL    string
	OrgId           string
	ReturnUrl       string
}

type HystrixConfig struct {
	DefaultServiceTemplate cfg.HystrixTemplate
	Commands               map[string][]*cfg.HystrixCommand
}

// DowntimeConfig consists of all the downtime per vendor
type DowntimeConfig struct {
	Downtime *Downtime `dynamic:"true"`
}

type Downtime struct {
	// indicates daily downtime if any
	DailyDowntime *dynconf.RecurringDowntimePeriod `dynamic:"true"`
	// indicates timestamp based downtime if any
	TimestampBasedDowntime *dynconf.TimestampBasedDowntime `dynamic:"true"`
}

// TimeoutConfig consists all the API level timeouts per vendor
type TimeoutConfig struct {
	DefaultTimeout time.Duration
	// Vendors is a map where vendor is the key and value consists of a map
	// which consists of mappings between API name and context Config
	Vendors map[string]map[string]*ContextConfig
}

// ContextConfig consists of the API level timeout and a flag to use parent context
type ContextConfig struct {
	Timeout          time.Duration
	UseParentContext bool
}

type Digio struct {
	TransactionsURL string
	ExpiryInDays    int64
}

type WealthKarza struct {
	OcrURL             string
	MaskAadhaar        bool
	HideAadhaar        bool
	Confidence         bool
	CheckBlur          bool
	CheckBlackAndWhite bool
	CheckCutCard       bool
	CheckBrightness    bool
}

type Seon struct {
	GetUserSocialMediaInformationUrl string
}

type InhouseOCR struct {
	MaskDocURL          string
	ExtractFieldsURL    string
	ExtractFieldsURLV2  string `dynamic:"true"`
	DetectDocumentURL   string `dynamic:"true"`
	ConfidenceThreshold float64
}

type Digilocker struct {
	GetAuthorizationCodeUrl     string
	GetAccessTokenUrl           string
	ClientId                    string
	ClientSecret                string
	RedirectUri                 string
	GetListOfIssuedDocumentsUrl string
	GetRefreshTokenUrl          string
	GetFileFromUriUrl           string
	GetAadhaarInXmlUrl          string
	TokenCacheTtl               time.Duration
}

type Liquiloans struct {
	Mid                   string `json:"MID"`
	Key                   string `json:"KEY"`
	Host                  string
	SupplyIntegrationHost string

	// SFTP server params
	SftpHost                    string
	SftpPort                    int
	S3BucketP2PInvestmentLedger string
	SftpTimeoutInSeconds        int
}

type Alpaca struct {
	Secret           *AlpacaSecret
	BrokerApiHost    string
	BrokerApiVersion string
	MarketApiHost    string
	MarketApiVersion string
	// host for market data real time updates API
	StreamApiHost string
	// path for market data real time updates API
	StreamApiPath string
	// eg: wss, ws, http etc
	StreamApiScheme string
	// help in decide whether to use simulator in non prod env or not for websocket
	ShouldUseSimulatedEnvForWs bool `dynamic:"true"`
	// path for trade events (SSE)
	OrderEventsApiPath string
	// path for account update events (SSE)
	AccountEventsApiPath string
	// host for events (SSE)
	BrokerEventsApiHost string
	// scheme for events (SSE)
	BrokerEventsApiScheme string
	// help in decide whether to use simulator in non prod env or not for event (stream)
	ShouldUseSimulatedEnvForEvents bool `dynamic:"true"`
	// all the funding configurations
	FundingConfig *FundingConfig
	// path for journal update events
	JournalEventsPath string
	// path for fund transfer update events
	FundTransferEventsPath string
	//  URL sub-path of beta APIs for market data
	MarketDataBetaAPIPrefix string
}

type FundingConfig struct {
	FundTransferMode string
}

type AlpacaSecret struct {
	BrokerApiKey    string `json:"BROKER_API_KEY"`
	BrokerApiSecret string `json:"BROKER_API_SECRET"`
}
type Lending struct {
	PreApprovedLoan *PreApprovedLoan `dynamic:"true"`
	CreditCard      *CreditCard
	CreditLine      *CreditLine
	Collateral      *Collateral
	SecuredLoans    *SecuredLoans
}

type Collateral struct {
	LendingMFCentralConfig *LendingMFCentralConfig
}
type PreApprovedLoan struct {
	Federal    *FederalPreapprovedLoan
	FederalNTB *FederalNTBLoanCredentials `json:"FederalNTBLoanCredentials"`
	Liquiloans *LiquiloansPreapprovedLoan `json:"Liquiloans"`
	Idfc       *Idfc                      `json:"Idfc" ,dynamic:"true"`
	Lentra     *Lentra
	Abfl       *Abfl      `json:"Abfl"`
	Moneyview  *Moneyview `json:"Moneyview"`
	Finflux    *Finflux   `json:"Finflux"`
	Setu       *Setu      `json:"Setu"`
	Digitap    *Digitap   `json:"Digitap"`
	Lenden     *Lenden    `json:"Lenden"`
}

type Setu struct {
	BaseUrl         string
	PartnerId       string `json:"PartnerId"`
	PartnerSchemeId string `json:"PartnerSchemeId"`
	PartnerSecret   string `json:"PartnerSecret"`
}

type Moneyview struct {
	BaseUrl               string
	HttpUrl               string
	Username              string                `json:"Username"`
	Password              string                `json:"Password"`
	PartnerCode           int                   `json:"PartnerCode"`
	OpenMarketCredentials OpenMarketCredentials `json:"OpenMarketCredentials"`
}

type OpenMarketCredentials struct {
	PartnerCode int    `json:"PartnerCode"`
	Username    string `json:"Username"`
	Password    string `json:"Password"`
}

type CreditLine struct {
	Federal *FederalCreditLine
	M2P     *M2PCreditLine
}

type M2PCreditLine struct {
	Url string
}

type LendingMFCentralConfig struct {
	// Credentials are present in secret manager.
	// It is filled after fetching details from there during server init/config object creation.
	Credentials *LendingMFCentralCredentials
	// It contains API endpoints.
	HttpUrl *LendingMFCentralHttpUrl
}

type LendingMFCentralCredentials struct {
	ClientId     string `json:"ClientId"`
	ClientSecret string `json:"ClientSecret"`
	UserName     string `json:"UserName"`
	Password     string `json:"Password"`
	LenderCode   string `json:"LenderCode"`
}

type LendingMFCentralHttpUrl struct {
	AuthToken            string
	EncryptAndSign       string
	DecryptAndVerify     string
	SubmitCasSummary     string
	InvestorConsent      string
	GetCasDocument       string
	ValidateLien         string
	SubmitLien           string
	InvokeRevokeLien     string
	CheckStatus          string
	GetTransactionStatus string
}

type FederalPreapprovedLoan struct {
	UserName                  string `json:"UserName"`
	Password                  string `json:"Password"`
	UserNameSftp              string `json:"UserNameSftp"`
	PasswordSftp              string `json:"PasswordSftp"`
	Url                       string
	UrlLentra                 string
	HttpURL                   string
	FetchDetailsUrl           string
	RepaymentScheduleUrl      string
	RespUrl                   string
	SftpHost                  string
	SftpPort                  int
	SenderCode                string `json:"SenderCode"`
	SenderId                  string `json:"SenderId"`
	SenderAccessCode          string `json:"SenderAccessCode"`
	PartnerName               string `json:"PartnerName"`
	PartnerCode               string `json:"PartnerCode"`
	DisbursementApiSenderCode string `json:"DisbursementApiSenderCode"`
	UserAccessCode            string `json:"UserAccessCode"`
	UserAccessId              string `json:"UserAccessId"`
	SchemeCode                string `json:"SchemeCode"`
	// Loan Account Creation for NTB case http url endpoint
	PlAcntCrnNtbHttpURL string
	// Loan Account Creation Enquiry for NTB case http url endpoint
	PlAcntCrnEnqNtbHttpURL string
}

// These configurations will be used only for users who do not have a federal account (non-Fi users) and are involved in the loans flow.
type FederalNTBLoanCredentials struct {
	UserAccessCode          string `json:"UserAccessCode"` // can be used for password
	UserAccessId            string `json:"UserAccessId"`   // can be used for username
	SenderCode              string `json:"SenderCode"`     // can be used for merchant code
	ClientSecret            string `json:"ClientSecret"`
	ClientId                string `json:"ClientId"`
	PartnerCodeFundTransfer string `json:"PartnerCodeFundTransfer"`
	DisbSenderCode          string `json:"DisbSenderCode"`
	DisbPartnerName         string `json:"DisbPartnerName"`
}

type LiquiloansPreapprovedLoan struct {
	Url         string
	HttpUrl     string
	Sid         string       `json:"SID"`
	Key         string       `json:"Key"`
	EarlySalary *EarlySalary `json:"EarlySalary"`
	FLDG        *FLDG        `json:"FLDG"`
	STPL        *STPL        `json:"STPL"`
}

type EarlySalary struct {
	Sid        string `json:"SID"`
	Key        string `json:"Key"`
	SchemeCode string `json:"SchemeCode"`
	// V2 credentials are used for loan applications and accounts that are created with Fi's own LMS
	SidV2        string `json:"SIDV2"`
	KeyV2        string `json:"KeyV2"`
	SchemeCodeV2 string `json:"SchemeCodeV2"`
}

type FLDG struct {
	Sid string `json:"SID"`
	Key string `json:"Key"`
}

type STPL struct {
	Sid string `json:"SID"`
	Key string `json:"Key"`
}

type Idfc struct {
	Url                          string `dynamic:"true"`
	GetAccessTokenUrl            string `dynamic:"true"`
	MandatePageUrl               string `dynamic:"true"`
	EnableEncryption             bool
	SecretKey                    string `json:"SecretKey"`
	Iv                           string `json:"Iv"`
	PrivateKey                   string
	ClientId                     string `json:"ClientId"`
	Kid                          string `json:"KId"`
	CorrelationId                string `json:"CorrelationId"`
	Source                       string `json:"Source"`
	MerchantCode                 string `json:"MerchantCode"`
	MandateRegistrationIv        string `json:"MandateRegistrationIv"`
	MandateRegistrationSecretKey string `json:"MandateRegistrationSecretKey"`
	// Deprecated: use GetBankCodeFromIfsc from "vendorgateway/lending/preapprovedloan/idfc/utils/bank_code.go"
	BankCode         string `json:"BankCode"`
	SimulatorHttpURL string
}

type Lenden struct {
	BaseUrl       string
	Authorization string `json:"Authorization"`
	SecretKey     string `json:"SecretKey"`
	Iv            string `json:"Iv"`
	ProductId     string
	EnableCryptor bool
}

type Abfl struct {
	Url                 string
	BreUrl              string
	TxnDetailsUrl       string
	Username            string `json:"Username"`
	Password            string `json:"Password"`
	PwaJourneyUrl       string
	PwaAuthorizationKey string `json:"PwaAuthorizationKey"`
}

type Lentra struct {
	Url string
}

type SecuredLoans struct {
	Url                 string
	FiftyFinLamfSecrets *FiftyFinLamfSecrets
}

type FederalCreditLine struct {
	Url string
}

type CreditCard struct {
	M2P     *M2PCreditCard
	Federal *FederalCreditCard
}

type FederalCreditCard struct {
	Url                                              string
	UpdateCreditCardLimitDetailsUnsecuredChannel     string
	UpdateCreditCardLimitDetailsMassUnsecuredChannel string
}

type M2PCreditCard struct {
	RegisterCustomerHost    string
	M2PHost                 string
	M2PFallbackHost         string
	M2PLMSHost              string
	M2PPartnerSdkUrl        string
	M2PSetPinUrl            string
	EnableEncryption        bool
	CreditCardRepaymentHost string
	M2PFederalHost          string
	RotateKey               bool
}

type M2p struct {
	RegisterCustomerHost string
	M2PHost              string
}

type M2PSecrets struct {
	PartnerId     string `json:"partnerId"`
	PartnerToken  string `json:"partnerToken"`
	Authorization string `json:"Authorization"`
	Tenant        string `json:"TENANT"`
	EntityIdFi    string `json:"entityIdFi"`
	Source        string `json:"Source"`
	AgencyId      string `json:"AgencyId"`
	MerchantId    string `json:"MerchantId"`
	AggregatorId  string `json:"AggregatorId"`
}

type Esign struct {
	Leegality *LeegalityEsign
}

type LeegalityEsign struct {
	Url              string
	LeegalitySecrets *LeegalitySecrets
}

type LeegalitySecrets struct {
	XAuthToken                         string `json:"X-Auth-Token"`
	FederalLeegalityProfileId          string `json:"FederalLeegalityProfileId"`
	SavingsAccountFederalProfileId     string `json:"SavingsAccountFederalProfileId"`
	PreApprovedLoansFederalProfileId   string `json:"PreApprovedLoansFederalProfileId"`
	PreApprovedLoansFederalProfileIdV2 string `json:"PreApprovedLoansFederalProfileIdV2"`
}

type VKYCAgentDashCred struct {
	UserName string `json:"UserName"`
	Password string `json:"Password"`
}

type Karvy struct {
	KarviAppId             string
	AgentCode              string
	BranchCode             string
	BucketName             string `iam:"s3-readwrite"`
	FATCAFileURL           string
	OrderFeedFileSyncURL   string
	OrderFeedFileV2SyncURL string
	XMLTagReplaceFlag      bool
	NFTFileUploadURL       string
	GetFolioDetailsURL     string
	NomineeUpdateURL       string
	NCTCFTokenGenerateURL  string
	UserCode               string
}

type CvlSecrets struct {
	SftpUploadUser string `json:"sftp-upload-user"`
	SftpUploadPass string `json:"sftp-upload-pass"`
	ApiPassword    string `json:"api-password"`
}

type MaxmindSecrets struct {
	UserId     string
	LicenseKey string
}

type BureauSecrets struct {
	Id       string
	Password string
}

func (c *CvlSecrets) GetSftpUploadUser() string {
	if c == nil {
		return ""
	}
	return c.SftpUploadUser
}

func (c *CvlSecrets) GetSftpUploadPass() string {
	if c == nil {
		return ""
	}
	return c.SftpUploadPass
}

func (c *CvlSecrets) GetApiPassword() string {
	if c == nil {
		return ""
	}
	return c.ApiPassword
}

type SignzySecrets struct {
	Username string
	Password string
	UserId   string
}

type ProfileValidation struct {
	// Hunter API for federal
	Federal *FederalProfileValidation
}

type FederalProfileValidation struct {
	SenderAccessCode string `json:"SenderAccessCode"`
	SenderId         string `json:"SenderId"`
	SenderCode       string `json:"SenderCode"`
	TenantId         string `json:"TenantId"`
	Url              string
}

type Aml struct {
	Tss      *Tss
	TSSCloud *TSSCloud
}

type Tss struct {
	Epifi         *TSSService
	StockGuardian *TSSService
}

// TSSService contains configuration for connecting to one isolated TSS service set up for a specific regulated entity
// to provide AML case reviewing agents access to only cases of that entity.
type TSSService struct {
	// The URL of the TSS service which accepts requests to screen users for money laundering activities
	ScreeningUrl string
	// API token to connect to the URL
	ApiToken string

	SystemName    string
	ParentCompany string
}

type TSSCloud struct {
	// EnableCryptor is used to disable the cryptor when calling in-house simulator of TSS Cloud
	// and enable only when calling TSS Cloud
	EnableCryptor bool

	Common *TSSCloudCommon

	// Map of tenant owner to tenant config
	Tenants map[string]*TSSCloudTenant
}

type TSSCloudCommon struct {
	PublicCertPEMBase64 string
	PublicKey           *rsa.PublicKey
}

type TSSCloudTenant struct {
	// Name of the tenant, e.g., EpifiTech, LMS
	// This maps one to one with owner of as screening request
	Name                string
	URL                 string
	APIToken            string
	PrivateKeyPEMBase64 string
	PrivateKey          *rsa.PrivateKey
}

type MorningStar struct {
	MorningStarSecret             *MorningStarSecret
	MorningStarAccountSecret      *MorningStarAccountSecret
	TokenCacheTtl                 time.Duration
	EquityAPIURL                  string
	ApiUrl                        string
	ApiAccessTokenExpiryInDays    int
	MorningStarObsoleteFundAPIUrl string
}

type MorningStarSecret struct {
	UserName string `json:"UserName"`
	Password string `json:"Password"`
}

type FreshdeskAccountConfig struct {
	// Base url for the freshdesk account being used
	// This will remain same across all the api endpoints for a particular account.
	BaseURL string
	// Api key's secret name for an account
	ApiTokenSecretName string
}

type FreshdeskURI struct {
	Agent        string
	Ticket       string
	FilterTicket string
	Solutions    string
	Contacts     string
	TicketField  string
	Job          string
	BulkUpdate   string
}

type MorningStarAccountSecret struct {
	UserName string `json:"CompanyUserName"`
	Password string `json:"CompanyPassword"`
}

type DisputeConfig struct {
	// fi - requester id, given by Federal bank
	FiRequesterId string
}

type Vistara struct {
	Sftp *VistaraSftp
	Http *VistaraHttp
}

type VistaraSftp struct {
	User     string
	Password string
	Host     string
	Port     int
	SshKey   string
}

type VistaraHttp struct {
	BaseUrl  string
	Username string
	Password string
}

// Federal lien apis config
type FederalLien struct {
	ReasonEnumToValueMap          map[string]string
	LienRequestTypeEnumToValueMap map[string]string
	LienTypeEnumToValueMap        map[string]string
	// Date will be captured in DateLayout and postfix will be appended
	DateLayout        string
	DateLayoutPostfix string
	// Fi
	Channel string
	// Federal response status code to rpc status code
	StatusCodeToEnumMap map[string]string
	// Federal cbs status code to rpc status code
	CBSStatusCodeToEnumMap map[string]string
}

type SlackTokens struct {
	AppToken string `json:"apptoken"`
	BotToken string `json:"bottoken"`
}

type IncomeEstimatorConfig struct {
	InhouseIncomeEstimatorURL string `dynamic:"true"`
}

type LocationModelConfig struct {
	RiskSeverityValToEnumMapping map[string]string
	InHouseUrl                   string `dynamic:"true"`
}

type CasePrioritisationModelConfig struct {
	InHouseUrl string `dynamic:"true"`
}

type Vkyc struct {
}

type EnachConfig struct {
	FederalConfig *EnachFederalConfig
}

type EpanConfig struct {
	FederalConfig *EpanFederalConfig
}

type EpanFederalConfig struct {
	SftpConn *SftpConn
}

type SftpConn struct {
	Host string
	Port int
}

type AclSftp struct {
	User     string `dynamic:"true"`
	Host     string `dynamic:"true"`
	Port     int    `dynamic:"true"`
	Password string `dynamic:"true"`
	SshKey   string `dynamic:"true"`
}

type EnachFederalConfig struct {
	SftpConn *SftpConn
	// Path on federal remote server at which to upload the file
	FederalSFTPUploadPath string
	// Path on federal remote server from which to download the file
	FederalSFTPDownloadPath string
	// the key is map should be string value of api/vendorgateway.openbanking.enach.enums.EnachFileType
	FileTypeToSFTPPathMap map[string]string
}

type LentraSecrets struct {
	AggregatorId      string `json:"AggregatorId"`
	SClientIdentifier string `json:"sClientIdentifier"`
	SClientSecret     string `json:"sClientSecret"`
	SInstitutionName  string `json:"sInstitutionName"`
	ClientId          string `json:"ClientId"`
	ClientSecret      string `json:"ClientSecret"`
}

type FiftyFinLamfSecrets struct {
	ClientId     string `json:"ClientId"`
	ClientSecret string `json:"ClientSecret"`
}

type Cibil struct {
	CibilSecrets                       *CibilSecrets
	PingUrl                            string
	FulfillOfferUrl                    string
	GetAuthQuestionsUrl                string
	VerifyAuthAnswersUrl               string
	GetCustomerAssetsUrl               string
	GetProductTokenUrl                 string
	ProductUrlPrefix                   string
	DisableDefaultValuesInFulfillOffer bool
}

type CibilSecrets struct {
	SiteName               string `json:"SiteName"`
	AccountName            string `json:"AccountName"`
	AccountCode            string `json:"AccountCode"`
	ApiKey                 string `json:"ApiKey"`
	ClientSecret           string `json:"ClientSecret"`
	MemberRefId            string `json:"MemberRefId"`
	ProductConfigurationId string `json:"ProductConfigurationId"`
	PrivateKey             string `json:"PrivateKey"`
	Certificate            string `json:"Certificate"`
	ApiKeyV2               string `json:"ApiKeyV2"`
	ClientSecretV2         string `json:"ClientSecretV2"`
}

type PanValidationSecrets struct {
	SenderCode string `json:"SenderCode"`
	SenderId   string `json:"SenderId"`
	SenderKey  string `json:"SenderKey"`
}

type VideoSdk struct {
	ApiHost           string
	ApiVersion        string
	JwtExpiryDuration time.Duration
	Secrets           *VideoSdkSecrets
}

type VideoSdkSecrets struct {
	Path      string `iam:"sm-read"`
	ApiKey    string `field:"Path" jsonPath:"apiKey"`
	SecretKey string `field:"Path" jsonPath:"secretKey"`
}

type FederalAPICreds struct {
	Path            string `iam:"sm-read"`
	ClientId        string `field:"Path" jsonPath:"ClientId"`
	ClientSecretKey string `field:"Path" jsonPath:"ClientSecretKey"`
	UserId          string `field:"Path" jsonPath:"UserId"`
	Password        string `field:"Path" jsonPath:"Password"`
	SenderCode      string `field:"Path" jsonPath:"SenderCode"`
}

type Uqudo struct {
	AccessTokenURL   string
	PublicKeyURL     string
	FetchImageURL    string
	EmiratesIdOcrUrl string
	Secrets          *UqudoSecrets
}

type UqudoSecrets struct {
	Path         string `iam:"sm-read"`
	ClientId     string `field:"Path" jsonPath:"clientId"`
	ClientSecret string `field:"Path" jsonPath:"clientSecret"`
}

type KarzaSecrets struct {
	FederalLoanVkycApiKey string `json:"federalLoanVkycApiKey"`
}

type BureauIdSecrets struct {
	ApiKey     string `json:"ApiKey"`
	Password   string `json:"Password"`
	WorkflowId string `json:"WorkflowId"`
}

type Digitap struct {
	UanAdvancedUrl string
	DigitapSecrets *DigitapSecrets `json:"DigitapSecrets"`
}

type DigitapSecrets struct {
	// Authorization is Base64 encoded value of (client_id:client_secret)
	Authorization string `json:"Authorization"`
}

type PerfiosDigiLocker struct {
	ApiHost                  string
	PerfiosDigilockerSecrets string
}

type SetU struct {
	SetuBillPaySecrets              *SetuBillPaySecret
	SetuMobileRechargeSecret        *SetuMobileRechargeSecret
	PartnerId                       string
	BaseURL                         string
	MobileRechargeProductInstanceId string
	MobileRechargeBaseUrl           string
	MobileRechargeLoginBaseUrl      string
	AuthTokenUrl                    string
	GetCategoriesUrl                string
	GetBillersUrl                   string
	GetHealthUrl                    string
	GetDisputesUrl                  string
	GetTxnsUrl                      string
	RaiseDisputeUrl                 string
	CheckDisputeUrl                 string
	FetchBillUrl                    string
	FetchedBillUrl                  string
	PaymentRequestUrl               string
	PaymentResponseUrl              string

	MobileRechargeFetchAccessTokenUrl string
	MobileRechargeFetchOperatorUrl    string
	MobileRechargeFetchPlansUrl       string
	MobileRechargeInitiateRechargeUrl string
	MobileRechargeEnquireStatusUrl    string
	MobileRechargeGetTransactionsUrl  string
	MobileRechargeGetWalletBalanceUrl string
}

type DCIssuance struct {
	ChargeType string
	PartnerId  string
	ApiName    string
}

type Bridgewise struct {
	ApiHost           string
	BridgewiseSecrets *BridgewiseSecret
	TokenCacheTtl     time.Duration
}

type BridgewiseSecret struct {
	ApplicationClientId string `json:"ApplicationClientId"`
	Secret              string `json:"Secret"`
}

type SetuBillPaySecret struct {
	ClientId string `json:"ClientId"`
	Secret   string `json:"Secret"`
}

type SetuMobileRechargeSecret struct {
	ClientId string `json:"ClientId"`
	Secret   string `json:"Secret"`
}

type Nps struct {
	ApiHost string
}

// Nugget configuration for Zomato Nugget chatbot service
type Nugget struct {
	BaseURL             string
	Secrets             *NuggetSecrets
	AccessTokenEndpoint string
	BusinessId          int32
}

type NuggetSecrets struct {
	Username string `json:"Username"`
	Password string `json:"Password"`
	ClientID int32  `json:"ClientID"`
}

func (v *VisaSecrets) DecodeSecretsFromBase64String() error {
	decodePrivateKey, decodeErr := base64.StdEncoding.DecodeString(v.Key)
	if decodeErr != nil {
		return fmt.Errorf("error while decoding visa private-key: %w", decodeErr)
	}
	decodeCert, decodeErr := base64.StdEncoding.DecodeString(v.Cert)
	if decodeErr != nil {
		return fmt.Errorf("error while decoding visa certrificate: %w", decodeErr)
	}
	v.Key = string(decodePrivateKey)
	v.Cert = string(decodeCert)
	return nil
}
