package lenden

// Common constants and types used across LDC APIs

type InternalApiCode string

type MessageCode string

func (m MessageCode) String() string {
	return string(m)
}

const (
	// Origin systems

	OriginSystemPartner = "PARTNER"

	// API Codes

	InternalApiCodeCreateUser                InternalApiCode = "USER_CREATION"
	InternalApiCodeCreateLoan                InternalApiCode = "LOAN_CREATION"
	InternalApiCodeCheckHardEligibility      InternalApiCode = "HARD_ELIGIBILITY_V2"
	InternalApiCodeSelectOffer               InternalApiCode = "SELECTED_OFFER_V2"
	InternalApiCodeModifyRoi                 InternalApiCode = "MODIFY_ROI_V2"
	InternalApiCodeKycInit                   InternalApiCode = "KYC_INIT_V2"
	InternalApiCodeCheckKycStatus            InternalApiCode = "KYC_STATUS_CHECK"
	InternalApiCodeAddBankDetails            InternalApiCode = "ADD_BANK_DETAILS"
	InternalApiCodeInitMandate               InternalApiCode = "MANDATE_INIT_V2"
	InternalApiCodeCheckMandateStatus        InternalApiCode = "MANDATE_STATUS_CHECK"
	InternalApiCodeGenerateKfsLa             InternalApiCode = "GENERATE_KFS_LA"
	InternalApiCodeGetLoanDetails            InternalApiCode = "FETCH_LOAN_DETAILS_API"
	InternalApiCodeGetPreDisbursementDetails InternalApiCode = "PRE_DISBURSEMENT_DETAILS_API"
	InternalApiCodeAddEligibilityData        InternalApiCode = "ADD_ELIGIBILITY_DATA"
	InternalApiCodeGetForeclosureDetails     InternalApiCode = "FORECLOSURE_DETAILS_API"
	InternalApiCodeGeneratePaymentLink       InternalApiCode = "PAYMENT_LINK_GENERATION_API"
	InternalApiCodeGetPaymentStatus          InternalApiCode = "PAYMENT_STATUS_CHECK_API"

	// LOS (Loans Origination System) Message Codes

	// Message codes expected with 2XX HTTP response statuses

	MessageCodeSuccess MessageCode = "2001"

	// Message codes expected with 4XX HTTP response statuses

	MessageCodeMissingMandatoryField            MessageCode = "4001"
	MessageCodeFieldAlreadyPresent              MessageCode = "4004"
	MessageCodeInvalidValueForField             MessageCode = "4005"
	MessageCodeExtraFieldsNotAllowed            MessageCode = "4010"
	MessageCodeInvalidLoanStatus                MessageCode = "4012"
	MessageCodeFieldValueOutOfRange             MessageCode = "4013"
	MessageCodeBankAccountNotActive             MessageCode = "4016"
	MessageCodeNameMismatch                     MessageCode = "4017"
	MessageCodeBankConnectionError              MessageCode = "4018"
	MessageCodeMandateVerificationFailed        MessageCode = "4023"
	MessageCodeKYCAlreadyCompleted              MessageCode = "4026"
	MessageCodeUserNotActive                    MessageCode = "4027"
	MessageCodeOfferOrAccountDetailsNotFound    MessageCode = "4029"
	MessageCodeInvalidBankDetails               MessageCode = "4031"
	MessageCodeMandateNotCompleted              MessageCode = "4034"
	MessageCodeUnsignedKfsOrLaNotPresentMessage MessageCode = "4045"
	MessageCodeBankAccountNotVerifiedMessage    MessageCode = "4047"
	MessageCodeMissingBankStatement             MessageCode = "4048"
	MessageCodeFetchBureauFailed                MessageCode = "4061"
	MessageCodeEnachAlreadyCompleted            MessageCode = "4062"
	MessageCodeRoiModificationNotApplicable     MessageCode = "4070"
	MessageCodeMaxActiveLoanCountExceeded       MessageCode = "4078"
	MessageCodeAccountDetailsMismatchMessage    MessageCode = "4079"
	MessageCodeBreChecksFailed                  MessageCode = "4080"
	MessageCodeFfFailed                         MessageCode = "4081"
	MessageCodeSignedDocumentsAlreadyPresent    MessageCode = "4082"
	MessageCodePrincipalPaid                    MessageCode = "4112"
	MessageCodeForeclosureOrCoolOffNotAllowed   MessageCode = "4113"
	MessageCodeLastDueDateClose                 MessageCode = "4114"
	MessageCodeLoanCoolOffNotAvailable          MessageCode = "2028"
	MessageInvalidLoanStatus                    MessageCode = "4106"

	// Message codes expected with 5XX HTTP response statuses

	MessageCodeUnableToProcess MessageCode = "5002"
)
