//nolint:dupl
package lenden

import (
	"context"
	"fmt"
	"net/http"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	lendenPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	vendorLendenPb "github.com/epifi/gamma/api/vendors/lenden"
	"github.com/epifi/gamma/vendorgateway/config"
)

// GenerateKfsLaRequest represents the request structure for generating KFS and loan agreement.
type GenerateKfsLaRequest struct {
	Req  *lendenPb.GenerateKfsLaRequest
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

// GenerateKfsLaResponse represents the response structure for generating KFS and loan agreement.
type GenerateKfsLaResponse struct {
	Conf *config.Lenden
	Cryptor
	LogRedactor
}

func (c *GenerateKfsLaRequest) URL() string {
	return c.Conf.BaseUrl
}

func (c *GenerateKfsLaRequest) HTTPMethod() string {
	return http.MethodPost
}

func (c *GenerateKfsLaRequest) GetResponse() vendorapi.Response {
	return &GenerateKfsLaResponse{
		Conf:        c.Conf,
		Cryptor:     c.Cryptor,
		LogRedactor: c.LogRedactor,
	}
}

//nolint:dupl
func (c *GenerateKfsLaRequest) Marshal() ([]byte, error) {
	requestPayload := &vendorLendenPb.GenerateKfsLaRequest{
		Params: &vendorLendenPb.Params{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Fields: &vendorLendenPb.Fields{
			// Required To be passed empty, as it's a mandatory field from LDC
		},
		Json: &vendorLendenPb.GenerateKfsLaRequestPayload{
			ProductId: c.Conf.ProductId,
			UserId:    c.Req.GetUserId(),
			LoanId:    c.Req.GetLoanId(),
			IsSigned:  false,
		},
		Attributes: &vendorLendenPb.Attributes{
			Authorization: c.Conf.Authorization,
		},
		ApiCode: string(InternalApiCodeGenerateKfsLa),
	}

	// Step 2: Marshal the VendorRequest into JSON
	vendorReqBytes, err := protojson.Marshal(requestPayload)
	if err != nil {
		return nil, fmt.Errorf("error marshaling vendor request: %w", err)
	}

	// Step 3: Encrypt the JSON request
	encryptedReqBytes, err := c.Cryptor.Encrypt(vendorReqBytes)
	if err != nil {
		return nil, fmt.Errorf("error encrypting vendor request: %w", err)
	}

	// Step 4: Return the encrypted bytes
	return encryptedReqBytes, nil
}

//nolint:dupl
func (c *GenerateKfsLaResponse) Unmarshal(b []byte) (proto.Message, error) {
	unmarshaller := protojson.UnmarshalOptions{DiscardUnknown: true}
	encryptedResponse := vendorLendenPb.LendenEncryptedResponse{}
	if err := unmarshaller.Unmarshal(b, &encryptedResponse); err != nil {
		return nil, fmt.Errorf("error unmarshalling response body: %w", err)
	}
	encryptedResponsePayload := encryptedResponse.GetResponse().GetPayload()
	if encryptedResponsePayload == "" {
		return nil, errors.New("empty response received from vendor")
	}
	decryptedBytes, err := c.Cryptor.Decrypt([]byte(encryptedResponsePayload))
	if err != nil {
		return nil, fmt.Errorf("error decrypting response: %w", err)
	}
	responseWithWrapper := vendorLendenPb.GenerateKfsLaResponseWrapper{}
	if err := unmarshaller.Unmarshal(decryptedBytes, &responseWithWrapper); err != nil {
		return nil, fmt.Errorf("error unmarshaling response: %w", err)
	}
	vendorResponse := responseWithWrapper.GetResponse().GetResponseData()
	return &lendenPb.GenerateKfsLaResponse{
		Status:              rpc.StatusOk(),
		KfsDocUrl:           vendorResponse.GetKfs(),
		LoanAgreementDocUrl: vendorResponse.GetLoanAgreement(),
	}, nil
}

func (c *GenerateKfsLaResponse) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, "http error in lenden generate kfs la API", zap.String(logger.PAYLOAD, string(b)), zap.Any(logger.HTTP_STATUS, httpStatus))
	if httpStatus >= 400 && httpStatus < 500 {
		wrappedRes := &vendorLendenPb.GenerateKfsLaResponseWrapper{}
		err := (protojson.UnmarshalOptions{DiscardUnknown: true}).Unmarshal(b, wrappedRes)
		if err != nil {
			return nil, errors.Wrap(err, "error unmarshalling response")
		}
		switch wrappedRes.GetResponse().GetMessageCode() {
		case MessageCodeSignedDocumentsAlreadyPresent.String():
			debugMsg := fmt.Sprintf("Documents already signed. Call SignKfsLa to get the signed documents. Vendor response message: %s", wrappedRes.GetResponse().GetMessage())
			return &lendenPb.GenerateKfsLaResponse{Status: rpc.StatusAlreadyExistsWithDebugMsg(debugMsg)}, nil
		default:
			return &lendenPb.GenerateKfsLaResponse{Status: rpc.StatusInternalWithDebugMsg(wrappedRes.GetResponse().GetMessage())}, nil
		}
	}
	return &lendenPb.GenerateKfsLaResponse{
		Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error response: %s", string(b))),
	}, nil
}
