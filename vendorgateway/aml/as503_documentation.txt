TrackWizz API Document
Specific Case ID Details API (AS503)

Document
Document version: v1.0

Email :<EMAIL> 1
Tel : +91-22–6551 4191 / 92



I. Purpose: This API will provide the below list
Details of the Case ID passed will be sent in response

II. Request - Header

Field Name Mandatory Data Type Remarks

ApiToken Yes String Static Value will be shared by the TrackWizz team during
implementation.

Cluster Yes String Static Value will be shared by the TrackWizz team during
implementation.Static Value : CL1_User should be passed

Domain Yes String The Domain header is a critical component of our API requests,
allowing our clients to specify the originating domain of their
requests.

Request - Body

Field Name Mandat Data Type Remarks
ory

requestId Yes String Unique serial number that is used to identify the record for
processing. RequestId should be different for every record

apiRequestDto Yes Complex Refer the table below

Request- Data
Field Name Mandatory Data Type Remarks

caseId Yes String Case id for which case details needs to be fetched

III. Response Details

Failure Response - Main (Status Code - 400)

Field Name Mandat Data Type Remarks
ory

requestId Yes String RequestId which was passed in the request

validationCodes Yes String List of Validation codes

validationDescription
s Yes String List of Validation descriptions

Email :<EMAIL> 2
Tel : +91-22–6551 4191 / 92



Success Response - Main (Status Code - 200)
Field Name Mandatory Data Type Remarks

requestId Yes String RequestId which was passed in the request

responseData Yes Complex Refer table 1.1

Table 1.1 Response Data
Field Name Mandatory Data Type Remarks

caseDetails Yes Complex Details of the case sent in the request. Refer table 1.2

Table 1.2 Case Details
Data

Field Name Mandatory Type Remarks

caseId Yes String Case ID

the exact moment a case was created in a globally
standardized time format (Coordinated Universal

caseCreationDateTimeInUTC Yes String Time) in the format : yyyy-MM-ddTHH:mm:ssZ

sourceSystemName No String Source System of the customer

sourceSystemCustomerCode No String SourceSystemCustomerCode of the Customer

applicationRefNumber No String Application ref number of the Customer

Yes String Whether the case is of Main Customer or its Related
caseOf Person

Possible Values: MainKYC/RelatedPersonKYC

No String SourceSystemCustomerCode of the customer of the
linkedToSourceSystemCustomer

related person. This field is applicable only in case of
Code

a related person

relation No String Relation with the customer in case of a Related
person

screeningProfile No String Screening Profile code with which the record was
screened

screeningProfileName Screening Profile name with which the record was
No String screened

Email :<EMAIL> 3
Tel : +91-22–6551 4191 / 92



customerName No String Record name

caseType Yes String Whether the case is an Initial or a Watchlist Added
cases
Possible values: Initial/WatchlistAdded

initialScreeningMode No Enum If the case type is Initial Screening, whether the case
was created via Bulk search or Purpose 03 is
mentioned here
Possible values: API/LookupInBulk

onboardingDecision No Enum Value will be sent only in case of Initial Screening
case.
Possible values: Proceed/Decline

totalAlertCount Yes number Total alerts for the case

confirmedAlertCount Yes number Total confirmed alert count

probableAlertCount Yes number Total Probable alert count

pendingForDecision Yes number Count of the alerts where decision is pending

noMatchCount Yes number Alert count where Nomatch is marked

trueMatchCount Yes number Alert count where Truematch is marked

caseStage Yes String Current Stage of the case

caseCategory Yes String Possible values: Open/Pending/Completed

currentAssignee No String Current assignee of the Case

caseClosureDateTimeInUTC No String Closure date of case in a globally standardized time
format (Coordinated Universal Time) in the format :
yyyy-MM-ddTHH:mm:ssZ

finalRemarks No String Final Remarks of the case

caseActions Mrugank on 21-Dec-2023: Reviewed

Comple Susheel on 23-Dec-2023: Checked
No x Refer Table 1.3

reportDataInBase64String

Yes String PDF in base64 format

alertDetails Array(c
omplex

Yes ) List of alerts for a given case. Refer Table 1.3

Email :<EMAIL> 4
Tel : +91-22–6551 4191 / 92



Table 1.3 Case Actions
Field Name Mandatory Data Type Remarks

userName Yes String Assignee

Time when an action was added in a globally
standardized time format (Coordinated
Universal Time) in the format :

dateTimeInUTC Yes String yyyy-MM-ddTHH:mm:ssZ

action Yes String Action

Table 1.4 Alert details
Field Name Mandatory Data Type Remarks

alertId Yes String AlertId

source No String Source Name

watchlistSourceId Yes String SourceId of the watchlist

matchType Yes String Possible values: Confirmed/Probable

matchingAttributes Yes Array of string Matching attributes of the alert

Matching attributes for Refinitiv and Dow Jones.
SourceIdentification No Array(Complex) Multiple can exist. Refer Table 1.5

watchlistName Yes String Watchlist Name of the record

alertDecision No String Possible values: TrueMatch/NoMatch/Pending

comments No Complex Refer Table 1.6

Table 1.5 SourceIdentification fields
Field Name Mandatory Data Type Remarks

sourceIdentificationID Yes String SourceIdentificationID

sourceIdentificationKey Yes String Keyword

sourceIdentificationValue Yes String SourceIdentificationValues

Table 1.6 Comments
Field Name Mandatory Data Type Remarks

userName Yes String Assignee
Email :<EMAIL> 5
Tel : +91-22–6551 4191 / 92



Time when a comment was added in a globally
standardized time format (Coordinated
Universal Time) in the format :

dateTimeInUTC Yes String yyyy-MM-ddTHH:mm:ssZ

comment Yes String Comment

NOTE: Field or table marked as (development awaiting) are yet to be capture in the application
IV. Rejection codes

Sr.No Validation code Validation description

1. MRV1 API Token is mandatory

2. MRV2 Invalid API Token or API Token is not recognized.

3. MRV3 The User does not have access to API in Subscription Master

4. MRV4 API Subscription details mentioned in API token is invalid

5. MRV5 API Subscription details not found in API token.

7. MRV24 An Unexpected Error Occurred <exception>. Contact TSS Support

8. MRV29 RequestID is mandatory

No Special Characters are allowed other than hyphen, underscore, dot
9. MRV28 and space in RequestId

10 MRV44 CaseID is mandatory

11 MRV45 CaseID should be numeric code

12 MRV46 CaseID is not found

Sample Request Response folder :
RequestResponseSample

Postman Collection :
POSTMAN COLLECTION

Email :<EMAIL> 6
Tel : +91-22–6551 4191 / 92