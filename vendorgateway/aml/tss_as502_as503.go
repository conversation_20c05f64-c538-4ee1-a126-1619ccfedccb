package aml

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/vendorapi"
	amlVgPb "github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendorgateway/config"
)

// AS502 - List Cases Request Implementation
type listCasesReq struct {
	req        *amlVgPb.ListCasesRequest
	tenantConf *config.TSSCloudTenant
}

func (r *listCasesReq) URL() string {
	return r.tenantConf.URL + "/customerinfo/as502"
}

func (r *listCasesReq) HTTPMethod() string {
	return http.MethodPost
}

func (r *listCasesReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", r.URL())
	req.Header.Add("ApiToken", r.tenantConf.APIToken)
	return req
}

func (r *listCasesReq) GetResponse() vendorapi.Response {
	return &listCasesRes{}
}

func (r *listCasesReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (r *listCasesReq) Marshal() ([]byte, error) {
	// Convert vendorgateway enums to TSS enums
	caseCategoriesVendor := make([]tss.CaseCategory, len(r.req.GetCaseCategories()))
	for i, cat := range r.req.GetCaseCategories() {
		caseCategoriesVendor[i] = convertVendorCaseCategoryToTSS(cat)
	}

	caseTypesVendor := make([]tss.CaseType, len(r.req.GetCaseTypes()))
	for i, caseType := range r.req.GetCaseTypes() {
		caseTypesVendor[i] = convertVendorCaseTypeToTSS(caseType)
	}

	apiRequestDto := &tss.AS502ApiRequestDto{
		CaseCategory:     caseCategoriesVendor,
		SourceSystemName: r.req.GetSourceSystemName(),
		FromDateTime:     r.req.GetFromDateTime(),
		ToDateTime:       r.req.GetToDateTime(),
		CaseType:         caseTypesVendor,
	}

	req := &tss.AS502Request{
		RequestId:       r.req.GetRequestId(),
		ApiRequestDto:   apiRequestDto,
	}

	return protojson.Marshal(req)
}

// AS502 Response Implementation
type listCasesRes struct{}

func (r *listCasesRes) Unmarshal(b []byte) (proto.Message, error) {
	tssResp := &tss.AS502Response{}
	if err := protojson.Unmarshal(b, tssResp); err != nil {
		return nil, errors.Wrap(err, "error unmarshaling AS502 response")
	}

	// Handle error response
	if tssResp.GetErrorResponse() != nil {
		return &amlVgPb.ListCasesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("TSS error: %v", tssResp.GetErrorResponse().GetValidationDescriptions())),
		}, nil
	}

	// Convert TSS response to vendorgateway response
	responseData := tssResp.GetResponseData()
	if responseData == nil {
		return &amlVgPb.ListCasesResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty response data"),
		}, nil
	}

	// Convert case details
	caseDetails := make([]*amlVgPb.CaseDetails, len(responseData.GetCaseDetails()))
	for i, tssCase := range responseData.GetCaseDetails() {
		caseDetails[i] = convertTSSCaseDetailsToVendor(tssCase)
	}

	return &amlVgPb.ListCasesResponse{
		Status:      rpcPb.StatusOk(),
		RequestId:   tssResp.GetRequestId(),
		CaseCount:   responseData.GetCaseCount(),
		CaseDetails: caseDetails,
	}, nil
}

func (r *listCasesRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return &amlVgPb.ListCasesResponse{
		Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("HTTP error %d: %s", httpStatus, string(b))),
	}, nil
}

// AS503 - Get Case Details Request Implementation
type getCaseDetailsReq struct {
	req        *amlVgPb.GetCaseDetailsRequest
	tenantConf *config.TSSCloudTenant
}

func (r *getCaseDetailsReq) URL() string {
	return r.tenantConf.URL + "/customerinfo/as503"
}

func (r *getCaseDetailsReq) HTTPMethod() string {
	return http.MethodPost
}

func (r *getCaseDetailsReq) Add(req *http.Request) *http.Request {
	req.Header.Add("Cluster", "CL1_User")
	req.Header.Add("Domain", r.URL())
	req.Header.Add("ApiToken", r.tenantConf.APIToken)
	return req
}

func (r *getCaseDetailsReq) GetResponse() vendorapi.Response {
	return &getCaseDetailsRes{}
}

func (r *getCaseDetailsReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (r *getCaseDetailsReq) Marshal() ([]byte, error) {
	apiRequestDto := &tss.AS503ApiRequestDto{
		CaseId: r.req.GetCaseId(),
	}

	req := &tss.AS503Request{
		RequestId:     r.req.GetRequestId(),
		ApiRequestDto: apiRequestDto,
	}

	return protojson.Marshal(req)
}

// AS503 Response Implementation
type getCaseDetailsRes struct{}

func (r *getCaseDetailsRes) Unmarshal(b []byte) (proto.Message, error) {
	tssResp := &tss.AS503Response{}
	if err := protojson.Unmarshal(b, tssResp); err != nil {
		return nil, errors.Wrap(err, "error unmarshaling AS503 response")
	}

	// Handle error response
	if tssResp.GetErrorResponse() != nil {
		return &amlVgPb.GetCaseDetailsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("TSS error: %v", tssResp.GetErrorResponse().GetValidationDescriptions())),
		}, nil
	}

	// Convert TSS response to vendorgateway response
	responseData := tssResp.GetResponseData()
	if responseData == nil {
		return &amlVgPb.GetCaseDetailsResponse{
			Status: rpcPb.StatusInvalidArgumentWithDebugMsg("empty response data"),
		}, nil
	}

	// Convert case details with report
	caseDetails := convertTSSCaseDetailsWithReportToVendor(responseData.GetCaseDetails())

	return &amlVgPb.GetCaseDetailsResponse{
		Status:      rpcPb.StatusOk(),
		RequestId:   tssResp.GetRequestId(),
		CaseDetails: caseDetails,
	}, nil
}

func (r *getCaseDetailsRes) HandleHttpError(_ context.Context, httpStatus int, b []byte) (proto.Message, error) {
	return &amlVgPb.GetCaseDetailsResponse{
		Status: rpcPb.StatusInternalWithDebugMsg(fmt.Sprintf("HTTP error %d: %s", httpStatus, string(b))),
	}, nil
}

// Helper functions for enum conversion
func convertVendorCaseCategoryToTSS(cat amlVgPb.CaseCategory) tss.CaseCategory {
	switch cat {
	case amlVgPb.CaseCategory_CASE_CATEGORY_OPEN:
		return tss.CaseCategory_CASE_CATEGORY_OPEN
	case amlVgPb.CaseCategory_CASE_CATEGORY_PENDING:
		return tss.CaseCategory_CASE_CATEGORY_PENDING
	case amlVgPb.CaseCategory_CASE_CATEGORY_COMPLETED:
		return tss.CaseCategory_CASE_CATEGORY_COMPLETED
	default:
		return tss.CaseCategory_CASE_CATEGORY_UNSPECIFIED
	}
}

func convertVendorCaseTypeToTSS(caseType amlVgPb.CaseType) tss.CaseType {
	switch caseType {
	case amlVgPb.CaseType_CASE_TYPE_INITIAL:
		return tss.CaseType_CASE_TYPE_INITIAL
	case amlVgPb.CaseType_CASE_TYPE_WATCHLIST_ADDED:
		return tss.CaseType_CASE_TYPE_WATCHLIST_ADDED
	default:
		return tss.CaseType_CASE_TYPE_UNSPECIFIED
	}
}

func convertTSSCaseCategoryToVendor(cat tss.CaseCategory) amlVgPb.CaseCategory {
	switch cat {
	case tss.CaseCategory_CASE_CATEGORY_OPEN:
		return amlVgPb.CaseCategory_CASE_CATEGORY_OPEN
	case tss.CaseCategory_CASE_CATEGORY_PENDING:
		return amlVgPb.CaseCategory_CASE_CATEGORY_PENDING
	case tss.CaseCategory_CASE_CATEGORY_COMPLETED:
		return amlVgPb.CaseCategory_CASE_CATEGORY_COMPLETED
	default:
		return amlVgPb.CaseCategory_CASE_CATEGORY_UNSPECIFIED
	}
}

func convertTSSCaseTypeToVendor(caseType tss.CaseType) amlVgPb.CaseType {
	switch caseType {
	case tss.CaseType_CASE_TYPE_INITIAL:
		return amlVgPb.CaseType_CASE_TYPE_INITIAL
	case tss.CaseType_CASE_TYPE_WATCHLIST_ADDED:
		return amlVgPb.CaseType_CASE_TYPE_WATCHLIST_ADDED
	default:
		return amlVgPb.CaseType_CASE_TYPE_UNSPECIFIED
	}
}

func convertTSSInitialScreeningModeToVendor(mode tss.InitialScreeningMode) amlVgPb.InitialScreeningMode {
	switch mode {
	case tss.InitialScreeningMode_INITIAL_SCREENING_MODE_API:
		return amlVgPb.InitialScreeningMode_INITIAL_SCREENING_MODE_API
	case tss.InitialScreeningMode_INITIAL_SCREENING_MODE_LOOKUP_IN_BULK:
		return amlVgPb.InitialScreeningMode_INITIAL_SCREENING_MODE_LOOKUP_IN_BULK
	default:
		return amlVgPb.InitialScreeningMode_INITIAL_SCREENING_MODE_UNSPECIFIED
	}
}

func convertTSSOnboardingDecisionToVendor(decision tss.OnboardingDecision) amlVgPb.OnboardingDecision {
	switch decision {
	case tss.OnboardingDecision_ONBOARDING_DECISION_PROCEED:
		return amlVgPb.OnboardingDecision_ONBOARDING_DECISION_PROCEED
	case tss.OnboardingDecision_ONBOARDING_DECISION_DECLINE:
		return amlVgPb.OnboardingDecision_ONBOARDING_DECISION_DECLINE
	default:
		return amlVgPb.OnboardingDecision_ONBOARDING_DECISION_UNSPECIFIED
	}
}

func convertTSSCaseOfToVendor(caseOf tss.CaseOf) amlVgPb.CaseOf {
	switch caseOf {
	case tss.CaseOf_CASE_OF_MAIN_KYC:
		return amlVgPb.CaseOf_CASE_OF_MAIN_KYC
	case tss.CaseOf_CASE_OF_RELATED_PERSON_KYC:
		return amlVgPb.CaseOf_CASE_OF_RELATED_PERSON_KYC
	default:
		return amlVgPb.CaseOf_CASE_OF_UNSPECIFIED
	}
}

func convertTSSMatchTypeToVendor(matchType tss.MatchType) amlVgPb.MatchType {
	switch matchType {
	case tss.MatchType_MATCH_TYPE_CONFIRMED:
		return amlVgPb.MatchType_MATCH_TYPE_CONFIRMED
	case tss.MatchType_MATCH_TYPE_PROBABLE:
		return amlVgPb.MatchType_MATCH_TYPE_PROBABLE
	default:
		return amlVgPb.MatchType_MATCH_TYPE_UNSPECIFIED
	}
}

func convertTSSAlertDecisionToVendor(decision tss.AlertDecision) amlVgPb.AlertDecision {
	switch decision {
	case tss.AlertDecision_ALERT_DECISION_TRUE_MATCH:
		return amlVgPb.AlertDecision_ALERT_DECISION_TRUE_MATCH
	case tss.AlertDecision_ALERT_DECISION_NO_MATCH:
		return amlVgPb.AlertDecision_ALERT_DECISION_NO_MATCH
	case tss.AlertDecision_ALERT_DECISION_PENDING:
		return amlVgPb.AlertDecision_ALERT_DECISION_PENDING
	default:
		return amlVgPb.AlertDecision_ALERT_DECISION_UNSPECIFIED
	}
}

// Convert TSS CaseDetails to Vendor CaseDetails
func convertTSSCaseDetailsToVendor(tssCase *tss.CaseDetails) *amlVgPb.CaseDetails {
	if tssCase == nil {
		return nil
	}

	// Convert case actions
	caseActions := make([]*amlVgPb.CaseAction, len(tssCase.GetCaseActions()))
	for i, action := range tssCase.GetCaseActions() {
		caseActions[i] = &amlVgPb.CaseAction{
			UserName: action.GetUserName(),
			DateTime: action.GetDateTimeInUtc(),
			Action:   action.GetAction(),
		}
	}

	// Convert alert details
	alertDetails := make([]*amlVgPb.AlertDetails, len(tssCase.GetAlertDetails()))
	for i, alert := range tssCase.GetAlertDetails() {
		// Convert source identification
		sourceIdentifications := make([]*amlVgPb.SourceIdentification, len(alert.GetSourceIdentification()))
		for j, srcId := range alert.GetSourceIdentification() {
			sourceIdentifications[j] = &amlVgPb.SourceIdentification{
				SourceIdentificationId:    srcId.GetSourceIdentificationId(),
				SourceIdentificationKey:   srcId.GetSourceIdentificationKey(),
				SourceIdentificationValue: srcId.GetSourceIdentificationValue(),
			}
		}

		// Convert comments
		comments := make([]*amlVgPb.Comment, len(alert.GetComments()))
		for j, comment := range alert.GetComments() {
			comments[j] = &amlVgPb.Comment{
				UserName: comment.GetUserName(),
				DateTime: comment.GetDateTimeInUtc(),
				Comment:  comment.GetComment(),
			}
		}

		alertDetails[i] = &amlVgPb.AlertDetails{
			AlertId:               alert.GetAlertId(),
			Source:                alert.GetSource(),
			WatchlistSourceId:     alert.GetWatchlistSourceId(),
			MatchType:             convertTSSMatchTypeToVendor(alert.GetMatchType()),
			MatchingAttributes:    alert.GetMatchingAttributes(),
			SourceIdentification:  sourceIdentifications,
			WatchlistName:         alert.GetWatchlistName(),
			AlertDecision:         convertTSSAlertDecisionToVendor(alert.GetAlertDecision()),
			Comments:              comments,
		}
	}

	return &amlVgPb.CaseDetails{
		CaseId:                              tssCase.GetCaseId(),
		CaseCreationDateTime:                tssCase.GetCaseCreationDateTimeInUtc(),
		SourceSystemName:                    tssCase.GetSourceSystemName(),
		SourceSystemCustomerCode:            tssCase.GetSourceSystemCustomerCode(),
		ApplicationRefNumber:                tssCase.GetApplicationRefNumber(),
		CaseOf:                              convertTSSCaseOfToVendor(tssCase.GetCaseOf()),
		LinkedToSourceSystemCustomerCode:    tssCase.GetLinkedToSourceSystemCustomerCode(),
		Relation:                            tssCase.GetRelation(),
		ScreeningProfile:                    tssCase.GetScreeningProfile(),
		ScreeningProfileName:                tssCase.GetScreeningProfileName(),
		CustomerName:                        tssCase.GetCustomerName(),
		CaseType:                            convertTSSCaseTypeToVendor(tssCase.GetCaseType()),
		InitialScreeningMode:                convertTSSInitialScreeningModeToVendor(tssCase.GetInitialScreeningMode()),
		OnboardingDecision:                  convertTSSOnboardingDecisionToVendor(tssCase.GetOnboardingDecision()),
		TotalAlertCount:                     tssCase.GetTotalAlertCount(),
		ConfirmedAlertCount:                 tssCase.GetConfirmedAlertCount(),
		ProbableAlertCount:                  tssCase.GetProbableAlertCount(),
		PendingForDecision:                  tssCase.GetPendingForDecision(),
		NoMatchCount:                        tssCase.GetNoMatchCount(),
		TrueMatchCount:                      tssCase.GetTrueMatchCount(),
		CaseStage:                           tssCase.GetCaseStage(),
		CaseCategory:                        convertTSSCaseCategoryToVendor(tssCase.GetCaseCategory()),
		CurrentAssignee:                     tssCase.GetCurrentAssignee(),
		CaseClosureDateTime:                 tssCase.GetCaseClosureDateTimeInUtc(),
		FinalRemarks:                        tssCase.GetFinalRemarks(),
		CaseActions:                         caseActions,
		AlertDetails:                        alertDetails,
	}
}

// Convert TSS AS503CaseDetails to Vendor CaseDetailsWithReport
func convertTSSCaseDetailsWithReportToVendor(tssCase *tss.AS503CaseDetails) *amlVgPb.CaseDetailsWithReport {
	if tssCase == nil {
		return nil
	}

	// Convert case actions
	caseActions := make([]*amlVgPb.CaseAction, len(tssCase.GetCaseActions()))
	for i, action := range tssCase.GetCaseActions() {
		caseActions[i] = &amlVgPb.CaseAction{
			UserName: action.GetUserName(),
			DateTime: action.GetDateTimeInUtc(),
			Action:   action.GetAction(),
		}
	}

	// Convert alert details
	alertDetails := make([]*amlVgPb.AlertDetails, len(tssCase.GetAlertDetails()))
	for i, alert := range tssCase.GetAlertDetails() {
		// Convert source identification
		sourceIdentifications := make([]*amlVgPb.SourceIdentification, len(alert.GetSourceIdentification()))
		for j, srcId := range alert.GetSourceIdentification() {
			sourceIdentifications[j] = &amlVgPb.SourceIdentification{
				SourceIdentificationId:    srcId.GetSourceIdentificationId(),
				SourceIdentificationKey:   srcId.GetSourceIdentificationKey(),
				SourceIdentificationValue: srcId.GetSourceIdentificationValue(),
			}
		}

		// Convert comments
		comments := make([]*amlVgPb.Comment, len(alert.GetComments()))
		for j, comment := range alert.GetComments() {
			comments[j] = &amlVgPb.Comment{
				UserName: comment.GetUserName(),
				DateTime: comment.GetDateTimeInUtc(),
				Comment:  comment.GetComment(),
			}
		}

		alertDetails[i] = &amlVgPb.AlertDetails{
			AlertId:               alert.GetAlertId(),
			Source:                alert.GetSource(),
			WatchlistSourceId:     alert.GetWatchlistSourceId(),
			MatchType:             convertTSSMatchTypeToVendor(alert.GetMatchType()),
			MatchingAttributes:    alert.GetMatchingAttributes(),
			SourceIdentification:  sourceIdentifications,
			WatchlistName:         alert.GetWatchlistName(),
			AlertDecision:         convertTSSAlertDecisionToVendor(alert.GetAlertDecision()),
			Comments:              comments,
		}
	}

	return &amlVgPb.CaseDetailsWithReport{
		CaseId:                              tssCase.GetCaseId(),
		CaseCreationDateTime:                tssCase.GetCaseCreationDateTimeInUtc(),
		SourceSystemName:                    tssCase.GetSourceSystemName(),
		SourceSystemCustomerCode:            tssCase.GetSourceSystemCustomerCode(),
		ApplicationRefNumber:                tssCase.GetApplicationRefNumber(),
		CaseOf:                              convertTSSCaseOfToVendor(tssCase.GetCaseOf()),
		LinkedToSourceSystemCustomerCode:    tssCase.GetLinkedToSourceSystemCustomerCode(),
		Relation:                            tssCase.GetRelation(),
		ScreeningProfile:                    tssCase.GetScreeningProfile(),
		ScreeningProfileName:                tssCase.GetScreeningProfileName(),
		CustomerName:                        tssCase.GetCustomerName(),
		CaseType:                            convertTSSCaseTypeToVendor(tssCase.GetCaseType()),
		InitialScreeningMode:                convertTSSInitialScreeningModeToVendor(tssCase.GetInitialScreeningMode()),
		OnboardingDecision:                  convertTSSOnboardingDecisionToVendor(tssCase.GetOnboardingDecision()),
		TotalAlertCount:                     tssCase.GetTotalAlertCount(),
		ConfirmedAlertCount:                 tssCase.GetConfirmedAlertCount(),
		ProbableAlertCount:                  tssCase.GetProbableAlertCount(),
		PendingForDecision:                  tssCase.GetPendingForDecision(),
		NoMatchCount:                        tssCase.GetNoMatchCount(),
		TrueMatchCount:                      tssCase.GetTrueMatchCount(),
		CaseStage:                           tssCase.GetCaseStage(),
		CaseCategory:                        convertTSSCaseCategoryToVendor(tssCase.GetCaseCategory()),
		CurrentAssignee:                     tssCase.GetCurrentAssignee(),
		CaseClosureDateTime:                 tssCase.GetCaseClosureDateTimeInUtc(),
		FinalRemarks:                        tssCase.GetFinalRemarks(),
		CaseActions:                         caseActions,
		ReportDataInBase64:                  tssCase.GetReportDataInBase64String(),
		AlertDetails:                        alertDetails,
	}
}
