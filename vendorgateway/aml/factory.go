package aml

import (
	"net/http"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/vendorapi"
	"github.com/epifi/gamma/api/vendorgateway/aml"
	"github.com/epifi/gamma/vendorgateway/config"
)

func (s *Service) getRequestFactoryMap(vendor commonvgpb.Vendor, req proto.Message) (map[commonvgpb.Vendor]vendorapi.SyncRequestFactory, error) {
	switch vendor {
	case commonvgpb.Vendor_TSS:
		tssReqFactory, err := s.NewTssRequestFactory(req)
		if err != nil {
			return nil, errors.Wrap(err, "error creating TSS request")
		}
		return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
			commonvgpb.Vendor_TSS: tssReqFactory,
		}, nil
	default:
		return nil, errors.Errorf("unsupported vendor: %v", vendor)
	}
}

func (s *Service) NewTssRequestFactory(m proto.Message) (vendorapi.SyncRequestFactory, error) {
	switch req := m.(type) {
	case *aml.ScreenCustomerRequest:
		return func(m proto.Message) vendorapi.SyncRequest {
			return &screenCustomerReq{
				method: http.MethodPost,
				req:    req,
				config: s.conf.Tss,
			}
		}, nil
	case *aml.InitiateScreeningRequest:
		tenantConf, err := getTSSCloudTenantConfig(s.conf.TSSCloud, req.GetOwner())
		if err != nil {
			return nil, errors.Wrap(err, "error getting TSS cloud tenant config")
		}
		return func(m proto.Message) vendorapi.SyncRequest {
			return &initiateScreeningReq{
				req:            req,
				tenantConf:     tenantConf,
				SecureExchange: s.secureExchange,
			}
		}, nil
	default:
		return nil, errors.Errorf("unsupported request type: %T", req)
	}
}

func getTSSCloudTenantConfig(conf *config.TSSCloud, owner common.Owner) (*config.TSSCloudTenant, error) {
	tenant := conf.Tenants[owner.String()]
	if tenant == nil {
		return nil, errors.Errorf("tenant config not found for owner: %s", owner.String())
	}
	return tenant, nil
}
