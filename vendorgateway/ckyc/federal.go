package ckyc

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifierrors"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"fmt"
	"log"
	"regexp"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/postaladdress"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/json"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/simulator/kyc/federal"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/vendorgateway/ckyc"
	"github.com/epifi/gamma/vendorgateway/config"
	vgFed "github.com/epifi/gamma/vendorgateway/federal"
	federal2 "github.com/epifi/gamma/vendorgateway/vendorapi/cryptor/federal"
)

const (
	OTPSentSuccessMessagePattern           = "OTP has been sent to the registered mobile number"
	MobileNumberMismatchMessage            = "Download failed. Mobile number does not match that registered in the KYC record"
	AuthFactorMismatchMessage              = "Download failed. Auth Factor does not match that registered in the KYC record"
	MobileNumberNotRegisteredMessage       = "Download failed. Mobile number is not registered in the KYC record"
	ResendOTPExceededPattern               = "Download failed. Number of attempts to resend OTP exceeded"
	InvalidOtpPattern                      = "Invalid OTP entered. Remaining attempts:"
	OTPExpiredMessagePattern               = "OTP Expired. To re-attempt use the 'Resend OTP' option. Attempts remaining"
	ResendOTPCoolDownActive                = "Resend OTP will be enabled after 90s from first request"
	OTPValidationFailedAndRetriesExhausted = "Download failed as OTP validation was unsuccessful"
	OTPResendSuccessMessage                = "OTP re-sent successfully to registered Mobile number"
)

// following pb files convention for defining enum names
var (
	federalRequestIdRandomSequenceLen = 5

	IdProofType_name = map[kyc.IdProofType]string{
		kyc.IdProofType_PASSPORT:        "A",
		kyc.IdProofType_VOTER_ID:        "B",
		kyc.IdProofType_PAN:             "C",
		kyc.IdProofType_DRIVING_LICENSE: "D",
		kyc.IdProofType_UID:             "E",
		kyc.IdProofType_NREGA_JOB_CARD:  "F",
		kyc.IdProofType_CKYC_RECORD:     "Z",
	}

	// following pb files convention for defining enum values
	IdProofType_value = map[string]kyc.IdProofType{
		"A": kyc.IdProofType_PASSPORT,
		"B": kyc.IdProofType_VOTER_ID,
		"C": kyc.IdProofType_PAN,
		"D": kyc.IdProofType_DRIVING_LICENSE,
		"E": kyc.IdProofType_UID,
		"H": kyc.IdProofType_UID,
		"I": kyc.IdProofType_UID,
		"F": kyc.IdProofType_NREGA_JOB_CARD,
		"Z": kyc.IdProofType_CKYC_RECORD,
	}

	SearchResponseCodeMapping = map[string]*rpc.Status{
		"":        rpc.StatusOk(),
		"000":     rpc.StatusOk(),
		"OBE0001": rpc.StatusPermissionDenied(),   // Invalid sender details
		"OBE0002": rpc.StatusPermissionDenied(),   // Service is not enabled for this Sender Code
		"OBE0003": rpc.StatusInvalidArgument(),    // Duplicate RequestId
		"OBE0004": rpc.StatusInvalidArgument(),    // RequestId Required
		"OBE0007": rpc.StatusInternal(),           // Internal Service Down
		"OBE0999": rpc.StatusInternal(),           // error in reason
		"OBE0048": rpc.StatusInvalidArgument(),    // Field is missing or Field value is missing
		"OBE0049": rpc.StatusInvalidArgument(),    // Fields should not contains special characters
		"OBE0050": rpc.StatusFailedPrecondition(), // Exceeds the specified length
	}

	StatusDOBMismatch                   = rpc.NewStatusWithoutDebug(uint32(ckyc.GetDataResponse_DOB_MISMATCH), "DOB Mismatch")
	StatusPhoneMismatch                 = rpc.NewStatusWithoutDebug(uint32(ckyc.GetDataResponse_PHONE_NUMBER_MISMATCH), "Phone Number Mismatch")
	StatusPhoneMissing                  = rpc.NewStatusWithoutDebug(uint32(ckyc.GetDataResponse_PHONE_NUMBER_NOT_LINKED), "Phone Number Mismatch")
	StatusInvalidData                   = rpc.NewStatusWithoutDebug(uint32(codes.FailedPrecondition), "Invalid CKYC Download Data")
	StatusInvalidRequestId              = rpc.NewStatusWithoutDebug(uint32(codes.FailedPrecondition), "Invalid Request ID")
	StatusResendOTPExceeded             = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_RESEND_OTP_LIMIT_EXCEEDED), "Resend OTP Limit Exceeded")
	StatusInvalidOTP                    = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_INVALID_OTP), "Invalid OTP")
	StatusOTPExpired                    = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_OTP_EXPIRED), "OTP Expired")
	StatusResendOTPCoolDownActive       = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_RESEND_OTP_COOLDOWN_ACTIVE), "Resend OTP Cooldown Active")
	StatusOTPValidationRetriesExhausted = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED), "OTP Validation Failed and Retry Exhausted")
	StatusResentSuccessfully            = rpc.NewStatusWithoutDebug(uint32(ckyc.VerifyOTPResponse_OTP_RESENT_SUCCESSFULLY), "OTP Resent Successfully")
)

var authFactorTypeToString = map[ckyc.AuthFactorType]string{
	ckyc.AuthFactorType_DATE_OF_BIRTH_OR_INCORPORATION: "01",
	ckyc.AuthFactorType_PINCODE_AND_YEAR_OF_BIRTH:      "02",
	ckyc.AuthFactorType_MOBILE_NUMBER:                  "03",
}

// NewFederalRequest creates a new request for federal bank provisioning APIs depending on the
// type of the proto message.
func NewFederalRequest(req proto.Message) vendorapi.SyncRequest {
	// TODO(team): Figure out config loading and passing for VG
	// This is a short-term hack to  unblock dev work
	conf, _ := config.Load()

	if err := registerVendorErrorCode(conf); err != nil {
		logger.ErrorNoCtx("Error in registering vendor error code", zap.Error(err))
		return nil
	}

	switch v := req.(type) {
	case *ckyc.SearchRequest:
		return ckycSearchReq{
			method:            "POST",
			req:               req.(*ckyc.SearchRequest),
			url:               conf.Application.CkycSearchURL,
			senderCode:        conf.Secrets.Ids[config.SenderCode],
			serviceAccessId:   conf.Secrets.Ids[config.ServiceAccessId],
			serviceAccessCode: conf.Secrets.Ids[config.ServiceAccessCode],
		}
	case *ckyc.GetDataRequest:
		return getCkycDataRequest{
			method:            "POST",
			req:               req.(*ckyc.GetDataRequest),
			url:               conf.Application.GetKycDataURL,
			senderCode:        conf.Secrets.Ids[config.SenderCode],
			serviceAccessId:   conf.Secrets.Ids[config.ServiceAccessId],
			serviceAccessCode: conf.Secrets.Ids[config.ServiceAccessCode],
		}
	case *ckyc.VerifyOTPRequest:
		return &getVerifyOTPRequest{
			method:            "POST",
			req:               req.(*ckyc.VerifyOTPRequest),
			url:               conf.Application.VerifyCkycOtpURL,
			senderCode:        conf.Secrets.Ids[config.SenderCode],
			serviceAccessId:   conf.Secrets.Ids[config.ServiceAccessId],
			serviceAccessCode: conf.Secrets.Ids[config.ServiceAccessCode],
		}
	default:
		log.Println("Unsupported request type", v)
		return nil
	}
}

func registerVendorErrorCode(config *config.Config) error {
	err := vendorapi.MonitoredVendorErrorCodeRegistry.Register(config.Application.GetKycDataURL, []string{"OBE0999"})
	if err != nil {
		return err
	}

	err = vendorapi.MonitoredVendorErrorCodeRegistry.Register(config.Application.CkycSearchURL, []string{"OBE0001", "OBE0002", "OBE0007", "OBE0999"})
	if err != nil {
		return err
	}
	return nil
}

// ckycSearchReq provides functionality for adapting to Federal bank's customer creation API.
type ckycSearchReq struct {
	*federal2.DefaultPGPSecuredExchange
	*vgFed.DefaultHeaderAdder

	method string
	req    *ckyc.SearchRequest
	url    string

	senderCode        string
	serviceAccessId   string
	serviceAccessCode string
}

// Marshal provides the json for Federal bank's CKYC Search / Enquire API call.
func (c ckycSearchReq) Marshal() ([]byte, error) {
	// v := ckyc.SearchRequest{}
	// TODO(neeraj): Actual id generation to be moved out. Possibly callers send it in the request
	// or it moves to some helper.
	reqId := idgen.FederalRandomSequence("NEOCKYCS", federalRequestIdRandomSequenceLen)

	idType, ok := IdProofType_name[c.req.KycDocument.Type]
	if !ok {
		return nil, fmt.Errorf("invalid id proof type: %v", c.req.KycDocument.Type)
	}

	req := &federal.SearchRequest{
		SenderCode:        c.senderCode,
		ServiceAccessId:   c.serviceAccessId,
		ServiceAccessCode: c.serviceAccessCode,
		RequestId:         reqId,
		IdValue:           c.req.KycDocument.IdValue,
		IdType:            idType,
		MobileNumber:      c.req.MobileNumber.ToString(),
	}
	return protojson.Marshal(req)
}

// URL provides the URL to send the request to
func (c ckycSearchReq) URL() string {
	return c.url
}

// HTTPMethod returns the http method to use for the API call.
func (c ckycSearchReq) HTTPMethod() string {
	return c.method
}

// NewResponse returns Response struct that can deserialize the vendor response
func (c ckycSearchReq) GetResponse() vendorapi.Response {
	return searchRes{}
}

// searchRes provides functionality for adapting to Federal bank's customer creation API.
type searchRes struct {
}

// nolint:goconst
func (c searchRes) EvaluateAndGetMetricErrorCode(status *commonvgpb.VendorStatus) (bool, string) {
	if status.GetCode() == "OBE0999" &&
		(strings.EqualFold(strings.ToLower(status.GetDescription()), "no data found") ||
			strings.EqualFold(strings.ToLower(status.GetDescription()), "no record found")) {
		return false, ""
	}
	return true, status.GetCode()
}

// Unmarshal converts the response received from federal bank's CKYC search API into
// SearchResponse proto.
func (c searchRes) Unmarshal(b []byte) (proto.Message, error) {
	b, err := json.SanitiseJSON(context.Background(), b)
	if err != nil {
		return nil, fmt.Errorf("error in sanitise json: %w", err)
	}

	m := federal.SearchResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &m)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshal search resp: %w", err)
	}

	vendorStatus := &commonvgpb.VendorStatus{
		Code:        m.GetResponse(),
		Description: m.GetReason(),
	}

	st := c.evaluateRPCStatus(m.GetResponse(), m.GetReason())
	if !st.IsSuccess() {
		return &ckyc.SearchResponse{
			Status:       st,
			VendorStatus: vendorStatus,
		}, nil
	}

	// TODO(aditya): update the VG response to have single entry
	pids := []*federal.SearchResponsePidData{m.Pid}
	records := make([]*ckyc.Record, len(pids))
	for i := 0; i < len(pids); i++ {
		pid := pids[i]
		records[i] = &ckyc.Record{
			CkycNumber:  pid.GetCkycNo(),
			Name:        (&commontypes.Name{}).Parse(pid.GetName()),
			FathersName: (&commontypes.Name{}).Parse(pid.GetFathersName()),
			Photo: &commontypes.Image{
				ImageType:       commontypes.StringToImageType(pid.GetImageType()),
				ImageDataBase64: pid.GetPhotoBase64(),
			},
			KycDate:     datetime.DateFromString(pid.GetKycDate()),
			UpdatedDate: datetime.DateFromString(pid.GetUpdatedDate()),
		}
	}

	return &ckyc.SearchResponse{
		Status:          rpc.StatusOk(),
		CkycReferenceId: m.GetPid().GetCkycReferenceId(),
		Records:         records,
	}, nil
}

func (c searchRes) evaluateRPCStatus(code, reason string) *rpc.Status {
	if code == "OBE0999" &&
		(strings.EqualFold(strings.ToLower(reason), "no data found") ||
			strings.EqualFold(strings.ToLower(reason), "no record found")) {
		return rpc.StatusRecordNotFound()
	}
	if st, ok := SearchResponseCodeMapping[code]; ok {
		return st
	}
	return rpc.StatusUnknown()
}

type getCkycDataRequest struct {
	*federal2.DefaultPGPSecuredExchange
	*vgFed.DefaultHeaderAdder

	method string
	req    *ckyc.GetDataRequest
	url    string

	senderCode        string
	serviceAccessId   string
	serviceAccessCode string
}

func (r getCkycDataRequest) Marshal() ([]byte, error) {
	if r.req.GetCkycReferenceId() == "" {
		return nil, fmt.Errorf("ckyc reference id not passed in request : %w", epifierrors.ErrInvalidArgument)
	}
	if r.req.GetAuthFactorType() != ckyc.AuthFactorType_MOBILE_NUMBER || len(r.req.GetAuthFactorValue()) < 1 {
		return nil, fmt.Errorf("authfactortype is not mobile number or value is empty : %w", epifierrors.ErrInvalidArgument)
	}
	reqId := idgen.FederalRandomSequence("NEOCKYCD", federalRequestIdRandomSequenceLen)
	req := &federal.GetDataRequest{
		SenderCode:        r.senderCode,
		ServiceAccessId:   r.serviceAccessId,
		ServiceAccessCode: r.serviceAccessCode,
		RequestId:         reqId,
		MobileNumber:      r.req.GetMobileNumber().ToString(),
		PidData: &federal.DownloadRequestPidData{
			CkycNo:         stripCKYCRefId(r.req.GetCkycReferenceId()),
			AuthFactorType: "03", // hardcoding for mobile number as it is mandatory
			AuthFactor:     r.req.GetAuthFactorValue(),
		},
	}
	return protojson.Marshal(req)
}

// https://monorail.pointz.in/p/fi-app/issues/detail?id=4265
func stripCKYCRefId(s string) string {
	/*
		https://monorail.pointz.in/p/fi-app/issues/detail?id=93340
		For both ckyc number or ckyc reference number it should have 14 digits.
		If the value received from CERSAI is 15 digits, then the prefix such as L/M /O can be trimmed. But after trimming, there should be 14 digits.
	*/
	if len(s) <= 14 {
		return s
	}
	caps := strings.ToUpper(s)
	if strings.HasPrefix(caps, "L") ||
		strings.HasPrefix(caps, "S") ||
		strings.HasPrefix(caps, "O") {
		return s[1:]
	}
	return s
}

func (r getCkycDataRequest) HTTPMethod() string {
	return r.method
}

func (r getCkycDataRequest) URL() string {
	return r.url
}

func (r getCkycDataRequest) GetResponse() vendorapi.Response {
	return getCkycDataResponse{}
}

func (r getCkycDataResponse) Unmarshal(b []byte) (proto.Message, error) {
	return r.UnmarshalV2(context.Background(), b)
}

type getCkycDataResponse struct{}

func (r getCkycDataResponse) UnmarshalV2(ctx context.Context, b []byte) (proto.Message, error) {
	b, err := json.SanitiseJSON(context.Background(), b)
	if err != nil {
		return nil, fmt.Errorf("error in sanitize json: %w", err)
	}

	m := federal.GetDataResponseV3{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true}.Unmarshal(b, &m)
	if err != nil {
		return nil, fmt.Errorf("error in unmarshal ckyc download resp: %w", err)
	}

	vendorStatus := &commonvgpb.VendorStatus{
		Code:        m.GetResponseCode(),
		Description: m.GetCkycInquiry().GetMessage(),
	}
	rpcStatus := r.evaluateGetDataResponseStatus(ctx, &m)

	return &ckyc.GetDataResponse{
		Status:       rpcStatus,
		VendorStatus: vendorStatus,
		RequestId:    m.GetRequestId(),
		OtpRequestId: m.GetCkycInquiry().GetOtpRequestId(),
	}, nil
}

func (r getCkycDataResponse) EvaluateAndGetMetricErrorCode(status *commonvgpb.VendorStatus) (bool, string) {
	nonVendorFailureMsg := []string{
		"date of birth entered does not match",
		"ckyc no-dob not matching",
		"mobile number entered does not match",
		"mobile number entered does not exist for ckyc number",
		"no data found",
		"authentication failed",
	}

	if status.GetCode() == "OBE0999" {
		description := strings.ToLower(status.GetDescription())
		for _, msg := range nonVendorFailureMsg {
			if strings.Contains(description, msg) {
				return false, ""
			}
		}
		return true, status.GetCode()
	}
	return false, ""
}

func (r getCkycDataResponse) evaluateGetDataResponseStatus(ctx context.Context, getDataResponse *federal.GetDataResponseV3) *rpc.Status {
	if getDataResponse.GetResponseCode() == "OBE0999" {
		switch {
		case strings.Contains(strings.ToLower(getDataResponse.GetReason()), "date of birth entered does not match") ||
			strings.Contains(strings.ToLower(getDataResponse.GetReason()), "ckyc no-dob not matching"):
			return StatusDOBMismatch

		case strings.Contains(strings.ToLower(getDataResponse.GetReason()), "mobile number entered does not match") ||
			strings.Contains(strings.ToLower(getDataResponse.GetReason()), "mobile number entered does not exist for ckyc number"):
			return StatusPhoneMismatch

		case strings.Contains(strings.ToLower(getDataResponse.GetReason()), "no data found") || strings.Contains(strings.ToLower(getDataResponse.GetReason()), "no record found"):
			return rpc.StatusRecordNotFound()

		case strings.Contains(strings.ToLower(getDataResponse.GetReason()), "authentication failed"):
			return rpc.StatusUnauthenticated()

		default:
			return rpc.StatusUnknown()
		}
	}

	switch {
	case getDataResponse.CkycInquiry == nil:
		logger.Info(ctx, "CkycInq is nil in download v2 response")
		return rpc.StatusInternal()
	case strings.Contains(getDataResponse.CkycInquiry.Message, OTPSentSuccessMessagePattern):
		// OTP sent successfully (this is a success case)
		logger.Info(ctx, "OTP sent successfully for CKYC DownloadV2", zap.String("message", getDataResponse.CkycInquiry.Message))
		return rpc.StatusOk()
	case strings.Contains(getDataResponse.CkycInquiry.Message, MobileNumberMismatchMessage), strings.EqualFold(getDataResponse.CkycInquiry.Message, AuthFactorMismatchMessage):
		logger.Info(ctx, "mobile number does not match with the CKYC registered number ")
		return StatusPhoneMismatch
	case strings.Contains(getDataResponse.CkycInquiry.Message, MobileNumberNotRegisteredMessage):
		logger.Info(ctx, "No mobile number linked to CKYC")
		return StatusPhoneMissing
	default:
		logger.Error(ctx, "unexpected error message", zap.String(logger.MESSAGE, getDataResponse.CkycInquiry.Message))
		return rpc.StatusUnknown()
	}
}

func getPersonalDetails(pd *federal.PersonalDetails) *kyc.PersonalDetails {
	retVal := &kyc.PersonalDetails{
		CkycNo: pd.CkycNo,
		Name: &commontypes.Name{
			FirstName:  pd.Fname,
			MiddleName: pd.Mname,
			LastName:   pd.Lname,
			Honorific:  pd.Prefix,
		},
		Gender: types.ParseGender(pd.Gender),
		MaidenName: &commontypes.Name{
			Honorific:  pd.MaidenPrefix,
			FirstName:  pd.MaidenFname,
			MiddleName: pd.MaidenMname,
			LastName:   pd.MaidenLname,
		},
		AccType: pd.AccType,
		FatherSpouseName: &commontypes.Name{
			FirstName:  pd.FatherFname,
			MiddleName: pd.FatherMname,
			LastName:   pd.FatherLname,
			Honorific:  pd.FatherPrefix,
		},
		MotherName: &commontypes.Name{
			FirstName:  pd.MotherFname,
			MiddleName: pd.MotherMname,
			LastName:   pd.MotherLname,
			Honorific:  pd.MotherPrefix,
		},
		Nationality:  pd.Nationality,
		BirthDate:    datetime.DateFromString(pd.Dob),
		BirthCountry: pd.BirthCountry,
		BirthPlace:   pd.BirthPlace,
		PermanentAddress: &postaladdress.PostalAddress{
			RegionCode:         pd.PermCountry,
			PostalCode:         pd.PermPin,
			AdministrativeArea: pd.PermState,
			Locality:           pd.PermCity,
			Sublocality:        pd.PermDist,
			AddressLines:       []string{pd.PermLine1, pd.PermLine2, pd.PermLine3},
		},
		Mobile: getMobNum(pd.MobNum),
		Email:  pd.GetEmail(),
	}
	// populate correspondence address only if it's different from permanent address
	isPermCorresAddressSame := getBooleanFromValue(pd.PermCorresSameflag)
	if !isPermCorresAddressSame {
		retVal.CorresAddress = &postaladdress.PostalAddress{
			RegionCode:         pd.CorresCountry,
			PostalCode:         pd.CorresPin,
			AdministrativeArea: pd.CorresState,
			Locality:           pd.CorresCity,
			Sublocality:        pd.CorresDist,
			AddressLines:       []string{pd.CorresLine1, pd.CorresLine2, pd.CorresLine3},
		}
	}
	return retVal
}

func getMobNum(num string) *commontypes.PhoneNumber {
	if num == "" {
		logger.InfoNoCtx("no mobile in ckyc download")
		return nil
	}

	val, err := strconv.Atoi(num)
	if err != nil {
		logger.InfoNoCtx("invalid mobile in ckyc download", zap.String("Mob", num))
		return nil
	}
	return &commontypes.PhoneNumber{
		CountryCode:    91,
		NationalNumber: uint64(val),
	}
}

func getIdentityDetails(identityDetails *federal.IdentityDetails, personal *federal.PersonalDetails) []*kyc.Identity {
	if identityDetails == nil {
		return nil
	}
	idsArray := identityDetails.Identity
	var idDetails []*kyc.Identity
	for _, id := range idsArray {
		idType, ok := IdProofType_value[id.IdentType]
		if !ok {
			logger.ErrorNoCtx("unsupported id proof type", zap.String("idtype", id.IdentType))
			idType = kyc.IdProofType_ID_PROOF_TYPE_UNSPECIFIED
		}

		// https://monorail.pointz.in/p/fi-app/issues/detail?id=9950
		// https://monorail.pointz.in/p/fi-app/issues/detail?id=11212
		if idType == kyc.IdProofType_NREGA_JOB_CARD ||
			idType == kyc.IdProofType_CKYC_RECORD ||
			idType == kyc.IdProofType_NATIONAL_POPULATION_REGISTER_LETTER {
			continue
		}

		if idType == kyc.IdProofType_DRIVING_LICENSE && !idNumberPatternValidation(id.IdentNum, idType) {
			continue
		}

		if idType == kyc.IdProofType_PASSPORT && !idNumberPatternValidation(id.IdentNum, idType) {
			continue
		}

		idDetails = append(idDetails, &kyc.Identity{
			IdProof: &kyc.IdProof{
				Type:    idType,
				IdValue: id.IdentNum,
				Expiry:  datetime.DateFromString(id.IdExpiryDate),
			},
			IsIdProofSubmitted: getBooleanFromValue(id.IdProofSubmitted),
			IsIdVerified:       getBooleanFromValue(id.IdverStatus),
		})
	}
	idDetails = addPANFromPersonalDetails(idDetails, personal)
	return idDetails
}

func idNumberPatternValidation(idNumber string, idType kyc.IdProofType) bool {
	// nolint: gosec
	passportRegex := "^[A-PR-WYa-pr-wy][1-9]\\d\\s?\\d{4}[1-9]$"
	DLRegex := "^(([A-Z]{2}[0-9]{2})|([A-Z]{2}-[0-9]{2}))((19|20)[0-9][0-9])[0-9]{7}$"

	switch idType {
	case kyc.IdProofType_PASSPORT:
		matched, err := regexp.MatchString(passportRegex, idNumber)
		if err != nil {
			logger.InfoNoCtx("Passport regex match failed", zap.Error(err))
			return true
		} else {
			logger.InfoNoCtx("Passport regex result", zap.Bool("Matched", matched))
			return matched
		}
	case kyc.IdProofType_DRIVING_LICENSE:
		matched, err := regexp.MatchString(DLRegex, idNumber)
		if err != nil {
			logger.InfoNoCtx("DL regex match failed", zap.Error(err))
			return true
		} else {
			logger.InfoNoCtx("DL regex result", zap.Bool("Matched", matched))
			return matched
		}
	default:
		return true
	}
}

func addPANFromPersonalDetails(ids []*kyc.Identity, personal *federal.PersonalDetails) []*kyc.Identity {
	// return early if PAN already there in ID proofs
	for _, id := range ids {
		if id.IdProof.Type == kyc.IdProofType_PAN {
			return ids
		}
	}

	// add PAN to ids if available in personal details
	if personal.GetPan() != "" {
		ids = append(ids, &kyc.Identity{
			IdProof: &kyc.IdProof{
				Type:    kyc.IdProofType_PAN,
				IdValue: personal.Pan,
			},
			IsIdProofSubmitted: true,
			IsIdVerified:       true,
		})
	}
	return ids
}

// Federal sends boolean value as string "01" for Yes, "02" for No
func getBooleanFromValue(s string) bool {
	if val, err := strconv.Atoi(s); err == nil {
		logger.InfoNoCtx(fmt.Sprintf("Error converting '%s' to integer", s))
		return val == 1
	}
	return false
}

func getRelatedPersonDetails(rps *federal.RelatedPersonDetails) []*kyc.RelatedPerson {
	if rps == nil {
		return nil
	}
	rpsArray := rps.RelatedPersonData
	rPersons := make([]*kyc.RelatedPerson, len(rpsArray))
	for i := 0; i < len(rpsArray); i++ {
		rp := rpsArray[i]
		rPersons[i] = &kyc.RelatedPerson{
			CkycNumber: rp.CkycNo,
			Name: &commontypes.Name{
				FirstName:  rp.Fname,
				MiddleName: rp.Mname,
				LastName:   rp.Lname,
				Honorific:  rp.Prefix,
			},
			RelationType:         parseRelatedPersonType(rp.RelType),
			Pan:                  rp.Pan,
			Aadhar:               rp.Uid,
			VoterCard:            rp.Voterid,
			NregaCard:            rp.Nrega,
			PassportNumber:       rp.Passport,
			PassportExpiry:       rp.PassportExp,
			DrivingLicenseNumber: rp.DrivingLicence,
			DrivingExpiry:        rp.DrivingExp,
			OtherIdName:          rp.OtheridName,
			OtherIdNumber:        rp.OtheridNo,
			KycVerifier: &kyc.KYCVerifier{
				DeclarationDate:         rp.DecDate,
				DeclarationPlace:        rp.DecPlace,
				KycVerificationDate:     rp.KycDate,
				KycVerifierName:         rp.KycName,
				KycVerifierEmployeeCode: rp.KycEmpcode,
				KycVerifierDesignation:  rp.KycDesignation,
				KycVerificationBranch:   rp.KycBranch,
				SubmittedDocsType:       parseSubmittedDocType(rp.DocSub),
				OrgName:                 rp.OrcName,
				OrgCode:                 rp.OrgCode,
			},
		}
	}
	return rPersons
}

func getImageDetails(imgs *federal.ImageDetails) ([]*commontypes.Image, string, string, string) {
	if imgs == nil {
		return nil, "", "", ""
	}
	var signImage, panImage, passportImage string
	imgsArray := imgs.Image
	images := make([]*commontypes.Image, len(imgsArray))
	for i := 0; i < len(imgsArray); i++ {
		images[i] = &commontypes.Image{
			ImageType:       commontypes.StringToImageType(imgsArray[i].ImageType),
			ImageDataBase64: imgsArray[i].ImageData,
		}
		if imgsArray[i].ImageCode == "09" {
			signImage = imgsArray[i].ImageData
		}
		if imgsArray[i].ImageCode == "03" {
			panImage = imgsArray[i].ImageData
		}
		if imgsArray[i].ImageCode == "05" {
			passportImage = imgsArray[i].ImageData
		}
	}
	return images, signImage, panImage, passportImage
}

func parseRelatedPersonType(s string) kyc.RelatedPersonType {
	val, err := strconv.Atoi(s)
	if err != nil {
		logger.InfoNoCtx(fmt.Sprintf("Error in Parsing RelatedPersonType %s", s))
	}
	switch val {
	case 1:
		return kyc.RelatedPersonType_GUARDIAN_OF_MINOR
	case 2:
		return kyc.RelatedPersonType_ASSIGNEE
	case 3:
		return kyc.RelatedPersonType_AUTHORISED_REPRESENTATIVE
	default:
		return kyc.RelatedPersonType_RELATED_PERSON_TYPE_UNSPECIFIED
	}
}

func parseSubmittedDocType(s string) kyc.SubmittedDocType {
	val, err := strconv.Atoi(s)
	if err != nil {
		logger.InfoNoCtx(fmt.Sprintf("Error in Parsing SubmittedDocType %s", s))
	}
	switch val {
	case 1:
		return kyc.SubmittedDocType_SUB_DOC_CERTIFIED_COPIES
	default:
		return kyc.SubmittedDocType_SUB_DOC_UNSPECIFIED
	}
}
