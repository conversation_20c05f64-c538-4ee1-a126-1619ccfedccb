package ckyc

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/ckyc"
)

var requestFactoryMap = map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
	commonvgpb.Vendor_FEDERAL_BANK: NewFederalRequest,
}

// Service provides an implementation of the cKYC gRPC service
type Service struct {
	Handler *vendorapi.HTTPRequestHandler
}

// NewService creates and returns an initialized instance of the cKYCService
func NewService(h *vendorapi.HTTPRequestHandler) *Service {
	return &Service{Handler: h}
}

func (s *Service) Search(ctx context.Context, req *ckyc.SearchRequest) (*ckyc.SearchResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, requestFactoryMap)
	if err != nil {
		return nil, err
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in vendor request", zap.Error(err))
		return nil, err
	}
	return res.(*ckyc.SearchResponse), nil
}

func (s *Service) GetData(ctx context.Context, req *ckyc.GetDataRequest) (*ckyc.GetDataResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, requestFactoryMap)
	if err != nil {
		return nil, err
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in vendor request", zap.Error(err))
		return nil, err
	}
	return res.(*ckyc.GetDataResponse), nil
}

func (s *Service) VerifyOTP(ctx context.Context, req *ckyc.VerifyOTPRequest) (*ckyc.VerifyOTPResponse, error) {
	vendorReq, err := vendorapi.NewVendorRequest(req, requestFactoryMap)
	if err != nil {
		return nil, err
	}
	res, err := s.Handler.Handle(ctx, vendorReq)
	if err != nil {
		logger.Error(ctx, "error in vendor request", zap.Error(err))
		return nil, err
	}
	return res.(*ckyc.VerifyOTPResponse), nil
}
