package bridgewise

import (
	bridgewiseVgPb "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
)

var (
	mapAssetTypeToSecurityAssetType = map[string]bridgewiseVgPb.SecurityAssetType{
		"fund":  bridgewiseVgPb.SecurityAssetType_SECURITY_ASSET_TYPE_FUND,
		"stock": bridgewiseVgPb.SecurityAssetType_SECURITY_ASSET_TYPE_STOCK,
	}
	mapCompanyParametersToSecurityParameterType = map[int64]bridgewiseVgPb.SecurityParameterType{
		4020:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_BOOK_VALUE_PER_SHARE,
		4038:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD,
		100003: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PE_RATIO,
		100067: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_PB_RATIO,
		4128:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY,
		1072:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARES_OUTSTANDING,
		28:     bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_REVENUE,
		373:    bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_EXPENSES,
		10:     bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_PROFIT,
		15:     bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_INCOME,
		4051:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA,
		1007:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_ASSETS,
		1276:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_LIABILITIES,
		4030:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_CURRENT_RATIO,
		1222:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_FREE_CASH_FLOW,
		2006:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_OPERATING_CASH_FLOW,
		2005:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_INVESTING_CASH_FLOW,
		2004:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_FINANCING_CASH_FLOW,
		4074:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_MARGIN,
		4047:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA_MARGIN,
		4094:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_MARGIN,
		4363:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_TOTAL_CAPITAL,
		4034:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_DEBT_TO_EQUITY,
		4200:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DILUTED_EPS_GROWTH,
		4194:   bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_REVENUE_GROWTH,
		100063: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EV_TO_EBITDA,
	}
	mapFundParametersToSecurityParameterType = map[int64]bridgewiseVgPb.SecurityParameterType{
		6044130104: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD,
		6044130124: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARPE_RATIO,
		6044130125: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE,
		6044130134: bridgewiseVgPb.SecurityParameterType_SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE,
	}
)
