package bridgewise

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"

	"github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise"
	vendorBridgewise "github.com/epifi/gamma/api/vendors/catalog/bridgewise"
	"github.com/epifi/gamma/vendorgateway/config"
)

const (
	PeriodTypeTTM = "last_12_month"
	Quarterly     = "quarterly"
	Annually      = "annually"
)

type GetCompanyFundamentalParametersReq struct {
	Method string
	Req    *bridgewise.GetCompanyFundamentalParametersRequest
	Conf   *config.Bridgewise
	AddTokenToRequestHeader
}

func (g *GetCompanyFundamentalParametersReq) HTTPMethod() string {
	return g.Method
}

func (g *GetCompanyFundamentalParametersReq) URL() string {
	baseUrl := fmt.Sprintf("%s/companies/%s/fundamental-parameters", g.Conf.ApiHost, g.Req.GetCompanyId())
	queryParams := url.Values{}
	if g.Req.GetCalendarYear() != 0 {
		queryParams.Add("year", fmt.Sprintf("%v", g.Req.GetCalendarYear()))
	}
	if g.Req.GetCalendarQuarter() != 0 {
		queryParams.Add("quarter", fmt.Sprintf("%v", g.Req.GetCalendarQuarter()))
	}
	switch g.Req.GetPeriodType() {
	case bridgewise.PeriodType_PERIOD_TYPE_TTM:
		queryParams.Add("period_type", PeriodTypeTTM)
	case bridgewise.PeriodType_PERIOD_TYPE_ANNUAL:
		queryParams.Add("period_type", Annually)
	case bridgewise.PeriodType_PERIOD_TYPE_QUARTER:
		queryParams.Add("period_type", Quarterly)
	}

	if queryParams.Has("year") || queryParams.Has("quarter") || queryParams.Has("period_type") {
		baseUrl = baseUrl + "?" + queryParams.Encode()
	}
	return baseUrl
}

func (g *GetCompanyFundamentalParametersReq) GetResponse() vendorapi.Response {
	return &GetCompanyFundamentalParametersResp{}
}

func (g *GetCompanyFundamentalParametersReq) ContentTypeString() string {
	return vendorapi.ContentTypeJSON
}

func (g *GetCompanyFundamentalParametersReq) Marshal() ([]byte, error) {
	return nil, nil
}

type GetCompanyFundamentalParametersResp struct {
}

func (g *GetCompanyFundamentalParametersResp) HandleHttpError(ctx context.Context, httpStatus int, b []byte) (proto.Message, error) {
	logger.Error(ctx, fmt.Sprintf("http error, http_status = %v, response = %v", httpStatus, string(b)))
	// Bridgewise is returning 400 Bad Request if the given page is out of bounds, converting this to NotFound for better handling
	if httpStatus == http.StatusBadRequest {
		httpStatus = http.StatusNotFound
	}
	return &bridgewise.GetCompanyFundamentalParametersResponse{
		Status: vendorapi.GetRpcStatusFromHttpCode(ctx, httpStatus),
	}, nil
}

func (g *GetCompanyFundamentalParametersResp) Unmarshal(b []byte) (proto.Message, error) {
	var res []*vendorBridgewise.CompanyFundamentalParameters
	err := json.Unmarshal(b, &res)
	if err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal CompanyFundamentalParameters")
	}

	paragraphs := lo.FilterMap(res, func(item *vendorBridgewise.CompanyFundamentalParameters, _ int) (*bridgewise.CompanyFundamentalParameters, bool) {
		securityParameterType, ok := mapCompanyParametersToSecurityParameterType[item.GetParameterId()]
		if !ok {
			return nil, false
		}
		return &bridgewise.CompanyFundamentalParameters{
			Language:                   item.GetLanguage(),
			UpdatedAt:                  item.GetUpdatedAt(),
			CalendarYear:               item.GetCalendarYear(),
			CalendarQuarter:            item.GetCalendarQuarter(),
			AnalysisType:               item.GetAnalysisType(),
			AnalysisTypeName:           item.GetAnalysisTypeName(),
			SectionType:                item.GetSectionType(),
			SectionTypeName:            item.GetSectionTypeName(),
			FilingDate:                 item.GetFilingDate(),
			PeriodType:                 item.GetPeriodType(),
			SecurityParameterType:      securityParameterType,
			ParameterName:              item.GetParameterName(),
			ParameterDescription:       item.GetParameterDescription(),
			ParameterValue:             item.GetParameterValue(),
			ParameterValueChange:       item.GetParameterValueChange(),
			ParameterType:              item.GetParameterType(),
			ParameterCurrencyIso3:      item.GetParameterCurrencyIso3(),
			ParameterExchangeRateToUsd: item.GetParameterExchangeRateToUsd(),
		}, true
	})

	return &bridgewise.GetCompanyFundamentalParametersResponse{
		Status:     rpc.StatusOk(),
		Parameters: paragraphs,
	}, nil
}
