package holdingsimporter

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	hiPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/holdingsimporter/smallcase"
	mft "github.com/epifi/gamma/vendorgateway/wealth/mutualfund/token/mf_central"
	"github.com/epifi/gamma/vendorgateway/wire/types"
)

const (
	MFCentralKey = "MFCentralKey"
	SmallCaseKey = "SmallCaseKey"
)

type SmallCaseCredential struct {
	GatewayAuthToken string `json:"GatewayAuthToken"`
	GatewaySecret    string `json:"GatewaySecret"`
}

type MFCentralCredentials struct {
	UserName     string `json:"UserName"`
	Password     string `json:"Password"`
	ClientId     string `json:"ClientId"`
	ClientSecret string `json:"ClientSecret"`
}

type HoldingsImporterFactory struct {
	conf                 *genconf.Config
	smallCaseCredential  *SmallCaseCredential
	mfCentralCredentials *MFCentralCredentials
	Handler              *vendorapi.HTTPRequestHandler
	cacheClient          cache.CacheStorage
}

func NewHoldingsImporterFactory(conf *genconf.Config, h *vendorapi.HTTPRequestHandler, cacheClient cache.CacheStorage) *HoldingsImporterFactory {
	mutualFundFactory := &HoldingsImporterFactory{
		conf:        conf,
		Handler:     h,
		cacheClient: cacheClient,
	}

	mutualFundFactory.mfCentralCredentials = extractMFCentralCredentialsFromConfig(MFCentralKey, conf)
	mutualFundFactory.smallCaseCredential = extractSmallCaseCredentialsFromConfig(SmallCaseKey, conf)

	return mutualFundFactory
}

func (f *HoldingsImporterFactory) getRequestFactoryMap(ctx context.Context) map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_MF_CENTRAL: f.NewHoldingsImporterMFCentralRequest,
		commonvgpb.Vendor_SMALL_CASE: func(message proto.Message) vendorapi.SyncRequest {
			return f.NewHoldingsImporterSmallCaseRequest(ctx, message)
		},
	}
}

func (f *HoldingsImporterFactory) NewHoldingsImporterSmallCaseRequest(ctx context.Context, req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *hiPb.CreateTransactionRequest:
		return &smallcase.CreateTransactionRequest{
			Method:           http.MethodPost,
			Url:              f.conf.Application().SmallCase().CreateTransactionURL(),
			GatewayAuthToken: f.smallCaseCredential.GatewayAuthToken,
			GatewaySecret:    f.smallCaseCredential.GatewaySecret,
		}
	case *hiPb.InitiateHoldingsImportRequest:
		return &smallcase.InitiateHoldingsImportRequest{
			Method:                http.MethodPost,
			Url:                   f.conf.Application().SmallCase().InitiateHoldingsImportURL(),
			GatewayAuthToken:      f.smallCaseCredential.GatewayAuthToken,
			GatewaySecret:         f.smallCaseCredential.GatewaySecret,
			TokenFetcher:          mft.NewFetcher(f.cacheClient, f.Handler, f.conf.Application().MFCentral().GenerateTokenURL, f.mfCentralCredentials.UserName, f.mfCentralCredentials.Password, f.mfCentralCredentials.ClientId, f.mfCentralCredentials.ClientSecret),
			EncryptAndSign:        mft.NewEncryptAndSign(f.Handler, types.EncryptAndSignUrl(f.conf.Application().MFCentral().EncryptAndSignURL)),
			VerifyAndDecrypt:      mft.NewVerifyAndDecrypt(f.Handler, types.VerifyAndDecryptUrl(f.conf.Application().MFCentral().VerifyAndDecryptURL)),
			ClientReferenceNumber: v.GetClientReferenceNumber(),
			PANNumber:             v.GetPanNumber(),
			MobileNumber:          v.GetMobileNumber(),
			EmailID:               v.GetEmailId(),
			UseSingleOtpFlow:      v.GetSingleOtpFlow(),
			TransactionId:         v.GetTransactionId(),
			SmallCaseGateway:      f.conf.Application().SmallCase().SmallCaseGateway(),
			// TODO: clean this up once deployment is stable
			EnableNotFoundStatus: f.conf.Application().SmallCase().EnableNotFoundInInitHoldingsImport(),
		}
	case *hiPb.TriggerHoldingsImportFetchRequest:
		return &smallcase.TriggerHoldingsImportFetchRequest{
			Ctx:                   ctx,
			Method:                http.MethodPost,
			Url:                   f.conf.Application().SmallCase().TriggerHoldingsImportFetchURL(),
			GatewayAuthToken:      f.smallCaseCredential.GatewayAuthToken,
			GatewaySecret:         f.smallCaseCredential.GatewaySecret,
			EncryptAndSign:        mft.NewEncryptAndSign(f.Handler, types.EncryptAndSignUrl(f.conf.Application().MFCentral().EncryptAndSignURL)),
			VerifyAndDecrypt:      mft.NewVerifyAndDecrypt(f.Handler, types.VerifyAndDecryptUrl(f.conf.Application().MFCentral().VerifyAndDecryptURL)),
			TokenFetcher:          mft.NewFetcher(f.cacheClient, f.Handler, f.conf.Application().MFCentral().GenerateTokenURL, f.mfCentralCredentials.UserName, f.mfCentralCredentials.Password, f.mfCentralCredentials.ClientId, f.mfCentralCredentials.ClientSecret),
			TransactionId:         v.GetTransactionId(),
			OtpReference:          v.GetOtpDetails().GetOtpReference(),
			EnteredOTP:            v.GetOtpDetails().GetOtp(),
			RequestId:             v.GetInitiateHoldingsImportRequestId(),
			ClientReferenceNumber: v.GetClientReferenceNumber(),
			SmallCaseGateway:      f.conf.Application().SmallCase().SmallCaseGateway(),
		}
	default:
		logger.InfoNoCtx("Unsupported Request Type for Mutual Fund SmallCase Vendor", zap.String("Request", protojson.Format(req)))
		return nil
	}
}

// ToDo(Junaid): Complete this for mf-central on-boarding
//
//nolint:dupl
func (f *HoldingsImporterFactory) NewHoldingsImporterMFCentralRequest(req proto.Message) vendorapi.SyncRequest {
	// not implemented
	return nil
}

func extractSmallCaseCredentialsFromConfig(key string, conf *genconf.Config) *SmallCaseCredential {
	var credential SmallCaseCredential
	err := json.Unmarshal([]byte(conf.Secrets().Ids[key]), &credential)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Marshalling credentials for %s has failed", key))
		return &SmallCaseCredential{}
	}
	return &credential
}

func extractMFCentralCredentialsFromConfig(key string, conf *genconf.Config) *MFCentralCredentials {
	var credential MFCentralCredentials
	err := json.Unmarshal([]byte(conf.Secrets().Ids[key]), &credential)
	if err != nil {
		logger.Fatal(fmt.Sprintf("Marshalling credentials for %s has failed", key))
		return &MFCentralCredentials{}
	}
	return &credential
}
