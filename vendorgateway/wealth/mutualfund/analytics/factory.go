package analytics

import (
	"encoding/json"
	"fmt"
	"net/http"

	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/vendorapi"
	analyticsPb "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics"
	"github.com/epifi/gamma/vendorgateway/config/genconf"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/analytics/smallcase"
	"github.com/epifi/gamma/vendorgateway/wealth/mutualfund/constants"
)

type MFAnalyticsFactory struct {
	conf                 *genconf.Config
	smallCaseCredentials *SmallCaseCredential
}

func NewMFAnalyticsFactory(conf *genconf.Config) *MFAnalyticsFactory {
	factory := &MFAnalyticsFactory{
		conf: conf,
	}

	factory.smallCaseCredentials = extractSmallCaseCredentialsFromConfig(constants.SmallCaseKey, conf)
	return factory
}

func (f *MFAnalyticsFactory) getRequestFactoryMap() map[commonvgpb.Vendor]vendorapi.SyncRequestFactory {
	return map[commonvgpb.Vendor]vendorapi.SyncRequestFactory{
		commonvgpb.Vendor_SMALL_CASE: f.NewSmallCaseRequest,
	}
}

func (f *MFAnalyticsFactory) NewSmallCaseRequest(req proto.Message) vendorapi.SyncRequest {
	switch v := req.(type) {
	case *analyticsPb.GetMFAnalyticsRequest:
		return &smallcase.GetSmallCaseMFAnalyticsRequest{
			Method:           http.MethodPost,
			Url:              f.conf.Application().SmallCase().MFAnalyticsURL(),
			RequestId:        v.GetRequestId(),
			GatewayAuthToken: f.smallCaseCredentials.GatewayAuthToken,
			GatewaySecret:    f.smallCaseCredentials.GatewaySecret,
			Transactions:     v.GetTransactions(),
			Summary:          v.GetSummary(),
			SmallCaseGateway: f.conf.Application().SmallCase().SmallCaseGateway(),
		}
	default:
		logger.InfoNoCtx("unsupported req type for mutual fund's Smallcase vendor", zap.String(logger.REQUEST, protojson.Format(req)))
		return nil
	}
}

func extractSmallCaseCredentialsFromConfig(key string, conf *genconf.Config) *SmallCaseCredential {
	var credential SmallCaseCredential
	err := json.Unmarshal([]byte(conf.Secrets().Ids[key]), &credential)
	if err != nil {
		logger.Fatal(fmt.Sprintf("unmarshalling smallcase credentials for %s has failed", key))
		return &SmallCaseCredential{}
	}
	return &credential
}

type SmallCaseCredential struct {
	GatewayAuthToken string `json:"GatewayAuthToken"`
	GatewaySecret    string `json:"GatewaySecret"`
}
