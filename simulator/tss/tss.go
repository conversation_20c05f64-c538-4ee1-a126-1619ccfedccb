package tss

import (
	"encoding/xml"
	"io"
	"net/http"

	"github.com/pborman/uuid"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/simulator/config/genconf"
	"github.com/epifi/gamma/vendorgateway/aml"
)

type TssService struct {
	dynConf *genconf.Config
}

func NewTssService(conf *genconf.Config) *TssService {
	return &TssService{
		dynConf: conf,
	}
}

// nolint:funlen
func (s *TssService) GenerateScreeningAlert(w http.ResponseWriter, request *http.Request) {
	matchPansCfg := s.dynConf.AmlMatchFoundPans()
	matchStatus := s.dynConf.AMLScreeningMatchValue()
	if matchStatus != "Match" && matchStatus != "NotMatch" && matchStatus != "Error" {
		logger.ErrorNoCtx("match status is invalid", zap.String("match status value-", matchStatus))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}

	reqBytes, rErr := io.ReadAll(request.Body)
	if rErr != nil {
		logger.ErrorNoCtx("error in reading request bytes")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	jsonReq := &tss.ScreenCustomerRequest{}
	jErr := protojson.Unmarshal(reqBytes, jsonReq)
	if jErr != nil {
		logger.ErrorNoCtx("error in unmarshalling json")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	xmlReq := &aml.ScreeningRequestData{}
	xErr := xml.Unmarshal([]byte(jsonReq.GetXmlString()), xmlReq)
	if xErr != nil {
		logger.ErrorNoCtx("error in unmarshalling xml")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}
	if xmlReq.Records == nil || xmlReq.Records.Record == nil {
		logger.ErrorNoCtx("empty records")
		s.sendResponse(w, http.StatusInternalServerError, nil)
		return
	}

	pan := xmlReq.Records.Record.Pan
	if matchStatus != "Error" {
		if matchPansCfg.Get(pan) {
			matchStatus = "Match"
		} else {
			matchStatus = "NotMatch"
		}
	}

	obj := &aml.ScreeningResults{
		XMLName: xml.Name{},
		ScreeningResult: struct {
			Text             string `xml:",chardata"`
			RejectionMessage string `xml:"RejectionMessage"`
			RejectionCode    string `xml:"RejectionCode"`
			SystemName       string `xml:"SystemName"`
			RequestId        string `xml:"RequestId"`
			RecordIdentifier string `xml:"RecordIdentifier"`
			Matched          string `xml:"Matched"`
			CaseId           string `xml:"CaseId"`
			Link             string `xml:"Link"`
			AlertCount       string `xml:"AlertCount"`
			Alerts           struct {
				Text  string `xml:",chardata"`
				Alert []struct {
					Text                           string `xml:",chardata"`
					CaseId                         string `xml:"CaseId"`
					AlertId                        string `xml:"AlertId"`
					Source                         string `xml:"Source"`
					Rule                           string `xml:"Rule"`
					SourceUniqueId                 string `xml:"SourceUniqueId"`
					TrackwizzId                    string `xml:"TrackwizzId"`
					WatchListName                  string `xml:"WatchListName"`
					MatchingPercentage             string `xml:"MatchingPercentage"`
					IsActive                       string `xml:"IsActive"`
					WatchlistChangeLogActionableId struct {
						Text string `xml:",chardata"`
						Nil  string `xml:"nil,attr"`
					} `xml:"WatchlistChangeLogActionableId"`
					Activity       string `xml:"Activity"`
					AmlWatchlistId string `xml:"AmlWatchlistId"`
					MatchType      string `xml:"MatchType"`
				} `xml:"Alert"`
			} `xml:"Alerts"`
		}{
			SystemName:       "Epifi Tech",
			RequestId:        xmlReq.Records.Record.RequestId,
			RecordIdentifier: xmlReq.Records.Record.RecordIdentifier,
		},
	}
	obj.ScreeningResult.Matched = matchStatus
	switch matchStatus {
	case "Error":
		obj.ScreeningResult.RejectionCode = "RC749"
		obj.ScreeningResult.RejectionMessage = "ApplicationFormNumber had exceeded the database length.Allowed length is 100 characters max."
	case "Match":
		caseId := uuid.NewUUID().String()
		obj.ScreeningResult.CaseId = caseId
		obj.ScreeningResult.AlertCount = "1"
		obj.ScreeningResult.Alerts.Alert = []struct {
			Text                           string `xml:",chardata"`
			CaseId                         string `xml:"CaseId"`
			AlertId                        string `xml:"AlertId"`
			Source                         string `xml:"Source"`
			Rule                           string `xml:"Rule"`
			SourceUniqueId                 string `xml:"SourceUniqueId"`
			TrackwizzId                    string `xml:"TrackwizzId"`
			WatchListName                  string `xml:"WatchListName"`
			MatchingPercentage             string `xml:"MatchingPercentage"`
			IsActive                       string `xml:"IsActive"`
			WatchlistChangeLogActionableId struct {
				Text string `xml:",chardata"`
				Nil  string `xml:"nil,attr"`
			} `xml:"WatchlistChangeLogActionableId"`
			Activity       string `xml:"Activity"`
			AmlWatchlistId string `xml:"AmlWatchlistId"`
			MatchType      string `xml:"MatchType"`
		}{
			{
				CaseId:    caseId,
				AlertId:   "1",
				MatchType: "Confirmed",
			},
		}
	default:
		// do nothing
	}

	objResp, err := xml.Marshal(obj)
	if err != nil {
		logger.ErrorNoCtx("error while marshaling data in xml")
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}
	jsonObj, jErr := protojson.Marshal(&tss.ScreenCustomerResponse{ResultXml: string(objResp)})
	if jErr != nil {
		logger.ErrorNoCtx("error while marshaling data in json")
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"Message": "<?xml version=\"1.0\" encoding=\"utf-16\"?>\r\n<A44ResponseModel xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">\r\n<ErrorMessage>There is an error in XML document (0, 0).</ErrorMessage>\r\n  <ScreeningResults />\r\n</A44ResponseModel>"}`))
		return
	}
	s.sendResponse(w, 200, jsonObj)
}

func (s *TssService) getScreeningRes(request *http.Request) ([]byte, error) {
	reqBody, err := io.ReadAll(request.Body)
	if err != nil {
		return nil, errors.Wrap(err, "error reading request body")
	}
	as501Req := &tss.AS501Request{}
	err = protojson.Unmarshal(reqBody, as501Req)
	if err != nil {
		return nil, errors.Wrap(err, "error unmarshalling request body json to AS501Request proto")
	}
	if len(as501Req.GetCustomerList()) == 0 {
		return nil, errors.New("customer list is empty")
	}
	defaultMatchVal := s.dynConf.AMLScreeningMatchValue()
	if defaultMatchVal != "Match" && defaultMatchVal != "NotMatch" {
		return nil, errors.Errorf("invalid default match value: %s", defaultMatchVal)
	}
	matchFoundPans := s.dynConf.AmlMatchFoundPans()
	customer := as501Req.GetCustomerList()[0]
	var matchVal string
	if matchFoundPans.Get(customer.GetPan()) {
		matchVal = "Match"
	} else {
		matchVal = defaultMatchVal
	}

	var customerResponse *tss.CustomerResponse
	if matchVal == "Match" {
		caseId := uuid.NewUUID().String()
		customerResponse = &tss.CustomerResponse{
			SourceSystemCustomerCode: customer.GetSourceSystemCustomerCode(),
			ValidationOutcome:        "Success",
			SuggestedAction:          "Review",
			PurposeResponse: []*tss.PurposeResponse{
				{
					Purpose:     "Initial Screening",
					PurposeCode: as501Req.GetPurpose(),
					Data: &tss.PurposeData{
						HitsDetected: "Yes",
						HitsCount:    1,
						ConfirmedHit: "No",
						CaseId:       caseId,
						CaseUrl:      "https://screenza.example.com/case/" + caseId,
						HitResponse: []*tss.HitResponse{
							{
								Source:                      "FBI Wanted Person",
								WatchlistSourceId:           "20",
								MatchType:                   "Probable",
								Score:                       80.0,
								ConfirmedMatchingAttributes: "Name",
							},
						},
					},
				},
			},
		}
	} else {
		// Default no-match response
		customerResponse = &tss.CustomerResponse{
			SourceSystemCustomerCode: customer.GetSourceSystemCustomerCode(),
			ValidationOutcome:        "Success",
			SuggestedAction:          "Proceed",
			PurposeResponse: []*tss.PurposeResponse{
				{
					Purpose:     "Initial Screening",
					PurposeCode: as501Req.GetPurpose(),
					Data: &tss.PurposeData{
						HitsDetected: "No",
						HitsCount:    0,
						ConfirmedHit: "No",
					},
				},
			},
		}
	}
	res := &tss.AS501Response{
		RequestId:        as501Req.GetRequestId(),
		OverallStatus:    "AcceptedByTW",
		CustomerResponse: []*tss.CustomerResponse{customerResponse},
	}
	resJson, err := protojson.Marshal(res)
	if err != nil {
		return nil, errors.Wrap(err, "error marshalling response to JSON")
	}
	return resJson, nil
}

func (s *TssService) InitiateScreening(w http.ResponseWriter, request *http.Request) {
	res, err := s.getScreeningRes(request)
	if err != nil {
		logger.ErrorNoCtx("error handling screening request", zap.Error(err))
		s.sendResponse(w, http.StatusInternalServerError, []byte(`{"ValidationCode": "RC001", "ValidationDescription": "Internal error"}`))
		return
	}
	s.sendResponse(w, http.StatusOK, res)
}

func (s *TssService) sendResponse(w http.ResponseWriter, statusCode int, resp []byte) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	_, _ = w.Write(resp)
}
