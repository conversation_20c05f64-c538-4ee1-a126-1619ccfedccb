package servergenhook

import (
	"context"
	"net/http"
	"time"

	"go.uber.org/zap"
	"golang.org/x/sync/errgroup" // nolint:depguard
	"google.golang.org/grpc"

	"github.com/epifi/be-common/pkg/cfg/dynconf"

	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/connected_account/securities"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	connectedAccPkg "github.com/epifi/gamma/pkg/connectedaccount/securities"

	"github.com/rudderlabs/analytics-go"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/epifiserver"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"

	analyservariablespb "github.com/epifi/gamma/api/analyser/variables"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/creditreportv2"
	epfpb "github.com/epifi/gamma/api/insights/epf"
	netWorthPb "github.com/epifi/gamma/api/insights/networth"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/httpgw/config"
	"github.com/epifi/gamma/httpgw/config/genconf"
	"github.com/epifi/gamma/mcp/networth"

	// MCP imports
	"github.com/mark3labs/mcp-go/server"
)

// nolint:funlen,ineffassign,staticcheck
func StartHttpGWServer(s *grpc.Server, ctx context.Context, initNotifier chan<- cfg.ServerName) (func(), error) {
	ctx, cancel := context.WithCancel(ctx)
	cleanupFn := func() { cancel() }

	conf, err := config.Load()
	if err != nil {
		return cleanupFn, err
	}

	genConf, err := dynconf.LoadConfig(config.Load, genconf.NewConfig, cfg.HTTP_GATEWAY_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic config", zap.Error(err), zap.Any(logger.SERVICE, cfg.HTTP_GATEWAY_SERVICE))
		return cleanupFn, err
	}

	g, grpCtx := errgroup.WithContext(context.Background())

	httpMux := http.NewServeMux()
	epifiserver.RegisterHealthCheckEndpoint(httpMux, conf.Application.Name)

	// Create and mount MCP server using streamable HTTP transport
	insightsConn := epifigrpc.NewConnByService(cfg.INSIGHTS_SERVICE)
	defer epifigrpc.CloseConn(insightsConn)
	authConn := epifigrpc.NewConnByService(cfg.AUTH_SERVICE)
	defer epifigrpc.CloseConn(authConn)
	analyserConn := epifigrpc.NewConnByService(cfg.ANALYSER_SERVICE)
	defer epifigrpc.CloseConn(analyserConn)
	caConn := epifigrpc.NewConnByService(cfg.CONNECTED_ACC_SERVICE)
	defer epifigrpc.CloseConn(caConn)
	creditReportConn := epifigrpc.NewConnByService(cfg.CREDIT_REPORT_SERVICE)
	defer epifigrpc.CloseConn(creditReportConn)
	investmentConn := epifigrpc.NewConnByService(cfg.INVESTMENT_SERVICE)
	defer epifigrpc.CloseConn(investmentConn)
	actorConn := epifigrpc.NewConnByService(cfg.ACTOR_SERVICE)
	defer epifigrpc.CloseConn(actorConn)
	userConn := epifigrpc.NewConnByService(cfg.USER_SERVICE)
	defer epifigrpc.CloseConn(userConn)

	netWorthClient := netWorthPb.NewNetWorthClient(insightsConn)
	sessionManagerClient := sessionPb.NewSessionManagerClient(authConn)
	variableGeneratorClient := analyservariablespb.NewVariableGeneratorClient(analyserConn)
	connectedAccountClient := connectedAccountPb.NewConnectedAccountClient(caConn)
	creditReportClient := creditreportv2.NewCreditReportManagerClient(creditReportConn)
	mfExternalOrdersClient := mfExternalPb.NewMFExternalOrdersClient(investmentConn)
	epfClient := epfpb.NewEpfClient(insightsConn)
	actorClient := actorPb.NewActorClient(actorConn)
	userGroupClient := userGroupPb.NewGroupClient(userConn)
	securitiesClient := securities.NewSecuritiesClient(caConn)
	stockTxnsHandler := connectedAccPkg.NewMinimalStockTransactions(connectedAccountClient, securitiesClient)

	rudderClient, err := analytics.NewWithConfig(
		conf.Secrets.RudderWriteKeyValue, "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  conf.RudderStack.IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: conf.RudderStack.BatchSize,
			Verbose:   conf.RudderStack.Verbose,
		},
	)
	if err != nil {
		logger.ErrorNoCtx("Failed to initialize RudderStack analytics: ", zap.Error(err))
	}
	eventBroker := events.NewRudderStackBroker(rudderClient)

	mcpServer := networth.CreateMcpServer(genConf,
		netWorthClient,
		sessionManagerClient,
		variableGeneratorClient,
		connectedAccountClient,
		creditReportClient,
		mfExternalOrdersClient,
		epfClient,
		actorClient,
		userGroupClient,
		eventBroker,
		stockTxnsHandler,
	)

	// Configure streamable HTTP server with proper endpoints
	streamableServer := server.NewStreamableHTTPServer(mcpServer,
		server.WithEndpointPath("/stream"),
	)

	// Mount MCP server using StripPrefix to handle routing correctly
	logger.InfoNoCtx("Mounting MCP server with streamable HTTP at /mcp/")

	httpMux.Handle("/mcp/", streamableServer)

	// Add a test handler to verify the path works
	httpMux.HandleFunc("/mcp/test", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		_, _ = w.Write([]byte(`{"status": "MCP streamable HTTP server is working", "path": "/mcp/test", "transport": "streamable-http"}`))
	})

	logger.InfoNoCtx("MCP streamable HTTP server mounted successfully")

	httpServer := epifiserver.NewHttpServer(
		conf.Server.Port, httpMux,
	)
	httpServer.ReadTimeout = conf.HttpServer.ReadTimeout
	httpServer.ReadHeaderTimeout = conf.HttpServer.ReadHeaderTimeout
	httpClosure := epifiserver.StartHttpServer(grpCtx, g, httpServer)

	healthCheckClosure := func() {}
	// skip http servers since all the use-cases with this is not required for TEST_TENANT setup where
	// all servers are hosted in the same instance for short time for testing purpose.
	if !(cfg.IsTestTenantEnabled() || cfg.IsRemoteDebugEnabled()) {
		// instantiate health check server
		healthCheckServer := epifiserver.NewHttpServer(conf.Server.HealthCheckPort, http.DefaultServeMux)
		epifiserver.RegisterHealthCheckEndpoint(http.DefaultServeMux, conf.Application.Name)
		epifiserver.RegisterMonitoringEndpoint(http.DefaultServeMux)
		healthCheckClosure = epifiserver.StartHttpServer(grpCtx, g, healthCheckServer)
		epifiserver.RegisterLogLevelEndpoint(http.DefaultServeMux)
	}
	initNotifier <- cfg.HTTP_GATEWAY_SERVER
	// block till we get sig term or one of server crashes
	epifiserver.HandleGracefulShutdown(grpCtx, g, httpClosure, healthCheckClosure)
	return cleanupFn, nil
}
