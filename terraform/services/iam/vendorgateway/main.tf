terraform {
  backend "s3" {
    # Pass configuration from file through command line
    #profile = "demo"
  }
}

variable "service" {
  type    = string
  default = "vendorgateway"
}

data "aws_iam_policy" "s3-ssm-logging" {
  name = "ssm_s3_logging_policy"
}

module "vendorgateway" {
  source = "../../../modules/grpc-service/v3/iam"

  allow_clamav_policy_arn = var.allow_clamav_policy_arn
  env                     = var.env
  owner                   = var.owner
  service_name            = var.service
  eks_cluster_oidc_issuer = var.eks_cluster_oidc_issuer
  ses_identity_arn = var.env == "prod" ? [
    "*"
    ] : [
    "arn:aws:ses:ap-south-1:632884248997:identity/fi.money",
    "arn:aws:ses:ap-south-1:632884248997:identity/fi.care",
    "arn:aws:ses:ap-south-1:632884248997:identity/stockguardian.in"
  ]

  secretmanagerread = local.secretmanagerread[var.env]

  s3readwrite = var.env == "prod" ? flatten([var.s3readwrite_buckets, "epifi-prod-audit-logs", "epifi-${var.env}-usstocks-alpaca", "epifi-${var.env}-preapprovedloan", local.s3readwrite[var.env]]) : flatten([var.s3readwrite_buckets, "epifi-${var.env}-usstocks-alpaca", "epifi-${var.env}-preapprovedloan", local.s3readwrite[var.env]])

  s3-ssm-logging = data.aws_iam_policy.s3-ssm-logging.arn

}

# Block generated by servergen iam generator. DO NOT Edit Manually
locals {
  secretmanagerread = {
    prod = [
      "prod/gcloud/profiling-service-account-key",
      "prod/investment-vendorgateway/*",
      "prod/pgp/*",
      "prod/redis/growth-infra/prefixaccess",
      "prod/redis/vendorgateway/prefixaccess",
      "prod/rudder/*",
      "prod/sftp/vendors/acl/password",
      "prod/vendorgateway/*",
      "prod/vg-vgpci/*",
      "prod/vg-vn-simulator-vgpci/federal-auth-sender-code",
      "prod/vg-vn-vgpci/*",
      "prod/vg-vn/*",
      "prod/vg-vngw-vgpci/*"
    ]
    qa = [
      "qa/gcloud/profiling-service-account-key",
      "qa/investment-vendorgateway/*",
      "qa/pgp/*",
      "qa/rudder/*",
      "qa/vendorgateway-simulator/sftp-upload-pass",
      "qa/vendorgateway-simulator/sftp-upload-user",
      "qa/vendorgateway/*",
      "qa/vg-vgpci/*",
      "qa/vg-vn-simulator-vgpci/federal-auth-sender-code",
      "qa/vg-vn-vgpci/*",
      "qa/vg-vn/*",
      "qa/vg-vngw-vgpci/*"
    ]
    staging = [
      "deploy/sftp/vendors/kaleyra/password",
      "staging/gcloud/profiling-service-account-key",
      "staging/investment-vendorgateway/*",
      "staging/pgp/*",
      "staging/rudder/*",
      "staging/sftp/vendors/kaleyra/password",
      "staging/vendorgateway-simulator/sftp-upload-pass",
      "staging/vendorgateway-simulator/sftp-upload-user",
      "staging/vendorgateway/*",
      "staging/vg-vgpci/*",
      "staging/vg-vn-simulator-vgpci/federal-auth-sender-code",
      "staging/vg-vn-vgpci/*",
      "staging/vg-vn/*",
      "staging/vg-vngw-vgpci/*"
    ]
    uat = [
      "uat/gcloud/profiling-service-account-key",
      "uat/investment-vendorgateway/*",
      "uat/pgp/*",
      "uat/rudder/*",
      "uat/vendorgateway-simulator/sftp-upload-pass",
      "uat/vendorgateway-simulator/sftp-upload-user",
      "uat/vendorgateway/*",
      "uat/vg-vgpci/*",
      "uat/vg-vn-simulator-vgpci/federal-auth-sender-code",
      "uat/vg-vn-vgpci/*",
      "uat/vg-vn/*",
      "uat/vg-vngw-vgpci/*"
    ]
  }
  s3readwrite       = {
    prod = [
      "epifi-prod-automatic-profiling",
      "epifi-prod-cx-ticket-attachments",
      "epifi-prod-mutualfund",
      "epifi-prod-mutualfund-karvy"
    ]
    qa = [
      "epifi-deploy-automatic-profiling",
      "epifi-qa-cx-ticket-attachments",
      "epifi-qa-mutualfund",
      "epifi-qa-mutualfund-karvy"
    ]
    staging = [
      "epifi-deploy-automatic-profiling",
      "epifi-staging-cx-ticket-attachments",
      "epifi-staging-mutualfund",
      "epifi-staging-mutualfund-karvy"
    ]
    uat = [
      "epifi-deploy-automatic-profiling",
      "epifi-uat-cx-ticket-attachments",
      "epifi-uat-mutualfund",
      "epifi-uat-mutualfund-karvy"
    ]
  }
}
