Flags:
  TrimDebugMessageFromStatus: true

Logging:
  EnableLoggingToFile: true
  LogPath: "/var/log/casbin/info.log"
  MaxSizeInMBs: 1024
  MaxBackups: 1

Tracing:
  Enable: false

RoleToServiceEntity:
  DEVELOPER:
  - ACCOUNTS: ["SAVINGS_ACCOUNT_BALANCES"]
  - ACTOR: ["USER_ENTITY", "ACTOR_ENTITY"]
  - ALFRED: ["SERVICE_REQUEST_DATA"]
  - AML: ["AML_ENTITY_CASE_DETAILS", "AML_ENTITY_SCREENING_ATTEMPTS", "AML_ENTITY_FILE_GENERATION_ATTEMPTS", "AML_ENTITY_FILE_GENERATION_CLIENT_ID_MAPPINGS"]
  - ANALYSER: ["ANALYSER_TRANSACTIONS", "ANALYSER_AA_TRANSACTIONS", "ANALYSER_TRANSACTIONS_UPDATE", "ANALYSER_AA_TRANSACTIONS_UPDATE", "ANALYSER_ANALYSIS_TASK"]
  - AUTH: ["TOKEN_STORE", "AUTH_FACTOR_UPDATE", "OTP", "PII_TOKEN_FOR_LOGS_SEARCH", "DEVICE_REGISTRATIONS", "AUTH_ATTEMPTS", "BIOMETRICS", "OAUTH_SIGNUP_DATA", "IOS_DEVICE_ATTESTATION_DATA", "DEVICE_INTEGRITY_RESULTS"]
  - AUTH_ORCHESTRATOR: ["AUTH_REQUEST", "AUTH_REQUEST_STAGE"]
  - BANK_CUSTOMER: ["BANK_CUSTOMER", "PERIODIC_KYC"]
  - CARD: ["CARD", "CARDS_FOR_ACTOR", "CARD_CREATION_REQUEST", "CARD_PIN", "CARD_LIMIT", "CARD_DELIVERY_TRACKING", "CARD_TRACKING_DETAILS", "CARD_ACTION_ATTEMPT", "CARD_AUTH_ATTEMPT", "PHYSICAL_CARD_DISPATCH_REQUESTS", "DC_FOREX_TXN_REFUND", "CARD_REQUEST", "CARD_NOTIFICATION", "DEBIT_CARD_MANDATE"]
  - CASBIN: ["ROLE_PERMISSIONS"]
  - CASPER: ["OFFER", "OFFER_INVENTORY", "OFFER_LISTING", "REDEEMED_OFFER", "REDEMPTION_REQUEST", "LOYLTY_REDEMPTION", "OFFLINE_REDEMPTION", "LOYLTY_EGV_OFFER_CATALOG", "LOYLTY_EGV_PRODUCT_DETAIL", "QWIKCILVER_REDEMPTION", "QWIKCILVER_CATEGORY_DETAILS", "QWIKCILVER_PRODUCT_LIST", "QWIKCILVER_PRODUCT_DETAILS", "EXCHANGER_OFFER", "EXCHANGER_OFFER_ACTOR_ATTEMPT", "EXCHANGER_OFFER_LISTING", "EXCHANGER_OFFER_ORDER", "EXCHANGER_OFFER_ORDER_FULFILLMENT_REQUEST", "EXCHANGER_OFFER_GROUP", "OFFER_DISPLAY_RANK", "EXCHANGER_OFFER_INVENTORY", "EXCHANGER_OFFER_LEVEL_REWARD_UNITS_UTILISATION", "DISCOUNT", "IN_HOUSE_REDEMPTION", "EXTERNAL_VENDOR_REDEMPTION", "FI_STORE_REDEMPTION"]
  - CATEGORIZER: ["TRANSACTION_CATEGORY", "GPLACE_CATEGORY"]
  - CELESTIAL: ["WORKFLOW_REQUEST", "WORKFLOW_HISTORIES"]
  - CMS: ["PRODUCT", "SKU", "COUPON", "REDEMPTION"]
  - COLLECTION: ["ENTITY_LEAD", "ENTITY_ALLOCATION"]
  - COMMS: ["USER_COMMUNICATIONS", "FCM_DEVICE_TOKEN", "SMS_TEMPLATE", "SMS_CALLBACKS", "MESSAGE", "USER_COMMS_PREFERENCE", "WHATSAPP_CALLBACKS", "WHATSAPP_USER_REPLIES", "EMAIL_TEMPLATE", "EMAIL_CALLBACKS", "APP_NOTIFICATIONS", "IN_APP_TARGETED_COMMS_ELEMENT", "IN_APP_TARGETED_COMMS_MAPPING", "IN_APP_TARGETED_COMMS_CALLBACK", "ELEMENT_TO_APP_DETAILS_MAPPING", "WHATSAPP_MESSAGE_HISTORY"]
  - CONNECTED_ACCOUNT: ["DATA_ATTEMPT_ENTITY", "CONSENT_ENTITY", "ACCOUNTS_ENTITY", "BANK_PREFERENCE_ENTITY", "NOTIFICATION_ENTITY", "TRANSACTIONS_ENTITY","ANALYSED_USER_ENTITY","ANALYSIS_REQUEST_ENTITY", "ANALYSIS_ATTEMPT_ENTITY"]
  - CX: ["DISPUTE_CONFIG", "DISPUTE", "CUSTOMER_AUTH", "AUTH_FACTOR_DETAILS", "FRESHDESK_CONSUMER", "DISPUTE_MANUAL_INTERVENTION", "SUPPORT_TICKET_DETAILS", "CALL_DETAILS", "TICKET_DETAILS_TRANSFORMATIONS", "ISSUE_RESOLUTION_FEEDBACKS", "ISSUE_RESOLUTION_USER_RESPONSES", "FRESHCHAT_USER_MAPPINGS", "PAYOUT_DETAILS", "SPRINKLR_CASE_DETAILS", "WATSON_INCIDENT", "WATSON_INCIDENT_TICKET_DETAIL", "WATSON_INCIDENT_COMMS_DETAIL", "ACTOR_APP_LOG_MAPPING", "CRM_TO_ISSUE_TRACKER_MAPPING", "DISPUTE_TICKET_LOG", "DISPUTE_NOTIFICATION_LOG", "DISPUTE_CORRESPONDENCE_DETAIL", "DISPUTE_DOCUMENT_DETAIL", "DISPUTE_ID_TO_TICKET_ID_MAPPING", "FEDERAL_DMP_DISPUTE_DETAIL", "ISSUE_CONFIG", "ISSUE_CATEGORY", "CALL_IVR_DETAILS", "ACTIVITY_METADATA_DETAILS","FEDERAL_ESCALATION_DETAILS","FEDERAL_ESCALATION_UPDATES","FEDERAL_ESCALATION_ATTACHMENTS","NUGGET_RESOURCE_API"]
  - DEPOSIT: ["ALL_DEPOSITS_ACTOR", "SINGLE_DEPOSIT", "All_DEPOSIT_REQUESTS_ACTOR", "DEPOSIT_TXNS_FOR_ACCOUNT", "DEPOSIT_GET_INTEREST_RATES"]
  - EMPLOYMENT: ["EMPLOYMENT_DATA","EMPLOYER","EMPLOYER_PI_MAPPING"]
  - ENACH: ["ENACH_MANDATE", "ENACH_MANDATE_ACTION"]
  - FIREFLY: ["CREDIT_ACCOUNT", "CARD_TRANSACTION", "TRANSACTION_ADDITIONAL_INFO", "CREDIT_CARD_BILL", "CREDIT_CARD_BILL_PAYMENT", "CARD_AUDIT", "CARD_REQUEST", "CARD_REQUEST_STAGE", "CREDIT_CARD_OFFER", "CREDIT_CARD", "CREDIT_CARD_SKU", "CREDIT_CARD_SKU_OVERRIDES", "LOAN_ACCOUNT", "TRANSACTION_LOAN_OFFERS", "CREDIT_CARD_OFFER_ELIGIBILITY_CRITERIA", "DISPUTED_TRANSACTIONS", "PINOT_CC_TRANSACTIONS", "CREDIT_CARD_RECOMMENDATION_INFO", "CARD_REQUEST_V2", "CREDIT_CARD_V2", "CREDIT_CARD_OFFER_V2"]
  - FITTT: ["ACTIONS", "ACTIONS_EXECUTIONS", "EVENTS", "SCHEDULES", "JOBS", "AGGREGATION_ENTITIES", "AGGREGATED_EXECUTIONS"]
  - HEALTH_ENGINE: ["PAYMENTS_HEALTH"]
  - IN_APP_REFERRAL: ["ALL_FINITE_CODES_ACTOR", "ALL_REFEREES_FOR_ACTOR", "REFEREE_REFERRAL_DETAILS", "REFERRAL_UNLOCK_ACTOR", "SEASON", "REFERRALS_SEGMENTED_COMPONENTS", "FINITE_CODES", "FINITE_CODE_CLAIMS", "NOTIFICATION_CONFIG_INFOS"]
  - INAPPHELP: ["FAQ_VERSION", "CATEGORIES", "FOLDERS", "ARTICLES", "RELATED_FAQ", "MEDIA_CONTENT", "MEDIA_PLAYLIST", "UI_CONTEXT_TO_MEDIA_PLAYLIST_MAPPING", "MEDIA_PLAYLIST_TO_MEDIA_CONTENT_MAPPING", "FEEDBACK_ATTEMPT", "USER_FEEDBACK", "IN_APP_HELP_APP_CLIENT_CONFIG_MAPPING", "IN_APP_HELP_ENTITY_MAPPING", "FEEDBACK_QUESTION_RESPONSES", "FEEDBACK_QUESTIONS", "FEEDBACK_SURVEY_ATTEMPTS", "FEEDBACK_SURVEYS"]
  - INSIGHTS: ["MESSAGE_PROCESSING_STATE", "MAIL_SYNC_REQUEST", "MERCHANT_QUERY", "MERCHANT", "INSIGHT_FRAMEWORK", "INSIGHT_SEGMENT", "INSIGHT_CONTEXT_MAPPING", "GENERATION_SCRIPT_RUN", "CONTENT_TEMPLATE", "INSIGHT_ENGAGEMENT", "EPF_PASSBOOK_REQUEST", "ASSET_HISTORY"]
  - INVESTMENT: ["ORDER_SUMMARY", "MUTUAL_FUND_ORDERS", "MUTUAL_FUND_FOLIO_LEDGER_FOR_ACTOR", "MUTUAL_FUND_ACTOR_PREREQUISITES", "MUTUAL_FUND_ENTITY_FILE_MAPPER", "MUTUAL_FUND_FILE_GENERATION_ATTEMPTS", "MUTUAL_FUND_BATCH_ORDER_PROCESSING_DETAILS", "MUTUAL_FUND_BATCH_ORDER_PROCESSING_STEP_DETAILS", "MUTUAL_FUND_FILE_STATE", "MUTUAL_FUND_PAYMENTS", "MUTUAL_FUNDS", "MUTUAL_FUND_AMC_INFOS", "MF_COLLECTIONS", "FUNDS_IN_COLLECTION", "COLLECTION_FILTER_MAPPING", "MF_FILTERS", "ALL_FILTERS_WITH_GROUPS", "PAST_ORDERS_FOR_ACTOR", "ORDER_TIMELINE", "MUTUAL_FUND_SIP_LEDGER", "MUTUAL_FUND_EXTERNAL_HOLDINGS_IMPORT_REQUESTS", "DYNAMIC_UI_ELEMENT_VARIANT_CONTENT", "DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG", "MF_SINGLE_OTP_CAS_IMPORT_REQUESTS", "MF_EXTERNAL_NFT_REQUESTS"]
  - KYC: ["KYC_SUMMARY", "VKYC_SUMMARY", "VKYC_PRIORITY_USERS", "EXTRACTED_DOCUMENTS", "KYC_AGENT"]
  - LIVENESS: ["LIVENESS_SUMMARY", "LIVENESS_INHOUSE_SUMMARY"]
  - MERCHANT: ["MERCHANT", "PROBABLE_KNOWN_MERCHANT"]
  - NPS: ["NPS_NAV_DETAILS", "NPS_SCHEME_DETAILS", "NPS_PFM_DETAILS"]
  - NUDGE: ["NUDGE", "ACTOR_NUDGE", "JOURNEY", "ACTOR_JOURNEY"]
  - OMEGLE: ["CALL_INFOS", "USER_APPLICATION_DOCUMENTS", "USER_APPLICATION"]
  - ORDER: ["ORDER", "ORDER_ATTEMPT", "TRANSACTION", "SAVING_LEDGER_RECON", "ORDER_WITH_TXN", "AA_TRANSACTION", "TXNS_FOR_ACTOR", "ORDER_METADATA", "AA_DATA_PURGE_REQUESTS", "ORDER_VENDOR_ORDER_MAP", "TRANSACTION_NOTIFICATION_MAP"]
  - P2P_INVESTMENT: ["INVESTOR", "SCHEME", "INVESTMENT_TRANSACTION", "MATURITY_CONSENT"]
  - PAN: ["EPAN_ATTEMPTS", "EPAN_ATTEMPT_EVENTS", "SCANNED_PAN_ATTEMPTS", "PAN_UPDATE_ATTEMPTS"]
  - PAY: ["FILE_GENERATION_ATTEMPTS_WITH_ENTITY_MAPPINGS", "FOREX_RATE", "IFT_CHECKS", "LRS_CHECKS", "SOF_DETAILS", "TRANSACTION"]
  - BILLPAY: ["RECHARGE_ORDERS", "RECHARGE_ORDER_STAGES"]
  - PAYMENT_INSTRUMENT: ["PAYMENT_INSTRUMENT", "AA_ACCOUNT_PI"]
  - PRE_APPROVED_LOAN: ["LOAN_REQUESTS", "LOAN_OFFERS", "LOAN_ACCOUNTS", "LOAN_STEP_EXECUTIONS", "LOAN_OFFER_ELIGIBILITY_CRITERIA", "LOAN_INSTALLMENT_INFO", "LOAN_INSTALLMENT_PAYOUT", "LOAN_ACTIVITY", "LOAN_PAYMENT_REQUEST", "LOAN_APPLICANTS", "PARTNER_LMS_USER", "FETCHED_ASSET", "MANDATE_REQUESTS", "LOANS_MASTER"]
  - QUEST: ["VARIANT", "VARIANT_VARIABLE_MAP", "EXPERIMENT", "EXPERIMENT_VERSION", "VARIABLE", "REDIS_EXPERIMENTS", "VARIANT_EVALUATION_VALUE"]
  - RECURRING_PAYMENT: ["RECURRING_PAYMENTS", "RECURRING_PAYMENT_ACTIONS", "STANDING_INSTRUCTIONS", "STANDING_INSTRUCTION_REQUESTS","RECURRING_PAYMENT_VENDOR_DETAILS"]
  - REWARDS: ["REWARD_OFFER", "REWARD_OFFER_GROUP", "REWARD", "LUCKY_DRAW_CAMPAIGN", "LUCKY_DRAW", "LUCKY_DRAW_REGISTRATION", "LUCKY_DRAW_WINNING", "REWARD_OFFER_DISPLAY_RANK", "REWARD_CLAWBACKS", "CREDIT_CARD_REWARD_INFO", "REWARD_OFFER_ACTOR_OFFER_LEVEL_MONTHLY_UTILISATION", "REWARD_OFFER_ACTOR_GROUP_LEVEL_MONTHLY_UTILISATION", "REWARD_PROJECTIONS", "REAWRD_PROCESSING_REQUESTS"]
  - RISK: ["RISK_BANK_ACTIONS", "RISK_DATA", "RISK_ALERTS", "RISK_REVIEW_ACTIONS", "RISK_SCREENER_ATTEMPTS", "RISK_ENTITY_FORM", "RISK_UNIFIED_LEA_COMPLAINTS", "RISK_LIEN_REQUESTS"]
  - RMS: ["RULES", "RULE_SUBSCRIPTIONS", "RULE_EXECUTIONS", "HOME_CARDS", "RULE_TAGS", "COLLECTIONS"]
  - SALARY_PROGRAM: ["REGISTRATION", "REGISTRATION_STAGE_DETAILS", "SALARY_TXN_VERIFICATION_REQUEST", "SALARY_PROGRAM_ACTIVATION_HISTORY", "SALARY_PROGRAM_REFERRALS_SEASON", "HEALTH_INSURANCE_POLICY_ISSUANCE_REQUEST", "HEALTH_INSURANCE_POLICY_DETAILS", "SALARY_LITE_MANDATE_REQUEST", "SALARY_LITE_MANDATE_EXECUTION_REQUEST", "WHITELISTED_B2B_USER", "SALARY_PROGRAM_ALL_DETAILS", "DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG", "DYNAMIC_UI_ELEMENT_VARIANT"]
  - SAVINGS: ["SAVINGS_ACCOUNT", "SAVINGS_ACCOUNT_CLOSURE_REQUESTS", "CLOSED_ACCOUNT_BALANCE_TRANSFER", "BANK_ACCOUNT_VERIFICATIONS"]
  - SCREENER: ["ENTITY_SCREENER_DATA"]
  - SECURITIES: ["SECURITIES_DETAILS", "SECURITIES_LISTINGS_DETAILS"]
  - SEGMENT: ["SEGMENTS", "SEGMENT_INSTANCES", "SEGMENT_INSTANCE_DETAILS", "SEGMENT_MEMBERSHIP"]
  - TIERING: ["TIERING_ENTITY_TIER_CRITERIA", "TIERING_ENTITY_ELIGIBLE_TIER_MOVEMENT", "TIERING_ENTITY_TIER_MOVEMENT_HISTORY", "TIERING_ENTITY_ACTOR_TIER_INFO", "TIERING_ENTITY_REDIS_STATES", "TIERING_ENTITY_PINOT_EOD_BALANCE","TIERING_ENTITY_TIERING_ABUSER"]
  - TIMELINE: ["TIMELINE"]
  - TSP_USER: ["ENTITY_TSP_USER"]
  - UPCOMING_TRANSACTIONS: ["UPCOMING_TRANSACTIONS", "SUBSCRIPTIONS"]
  - UPI: ["UPI_ACCOUNT_INFO", "ACCOUNT_UPI_PIN_INFO", "PSP_KEY", "VERIFIED_ADDRESS_ENTRY", "VPA_MERCHANT_INFO", "UPI_MANDATE", "UPI_MANDATE_REQUEST", "UPI_ACCOUNT", "UPI_ONBOARDING_DETAILS", "UPI_REQUEST_LOGS", "UPI_MANDATE", "UPI_MANDATE_REQUEST", "UPI_NUMBER_PI_MAPPING", "ACTOR_UPI_NUMBER_RESOLUTION"]
  - USER: ["ONBOARDING_CROSS_VALIDATION","ONBOARDING_STATE_ACTOR", "USER", "USER_GROUP_MAPPING", "SHIPPING_PREFERENCE", "USER_CONTACT", "ONBOARDING_VENDOR_RESPONSES", "CREDIT_REPORT", "CONSENT", "CHANGE_FEED", "NOMINEE", "USER_INTEL", "CREDIT_REPORT_USER_SUBSCRIPTION", "CREDIT_REPORT_DOWNLOAD", "TEMP_CREDIT_REPORT_MANUAL_FETCH", "USER_DEVICE_PROPERTIES", "USER_PREFERENCES"]
  - USSTOCKS: ["ACCOUNTS", "ACCOUNT_SUMMARY", "AGG_REMITTANCE_TXN", "COLLECTIONS", "COLLECTION_STOCK_MAPPINGS", "INVESTORS", "MANUAL_REVIEWS", "MARKET_CATEGORIES", "ORDERS", "REMITTANCE_PROCESS", "STOCKS", "SUITABILITY_LEDGER", "TRANSACTION_REMITTANCE_REQUEST", "WALLET_ORDERS", "WATCHLISTS", "WATCHLIST_STOCK_MAPPINGS"]
  - VENDOR_MAPPING: ["BE_MAPPING"]
  - VKYC_CALL: ["CALL_DETAILS", "AUDITOR_REVIEW_DETAILS", "CALL_ID_TO_ACTOR_ID"]
  - WEALTH_ONBOARDING: ["ONBOARDING_ENTITY", "ESIGN_ENTITY", "USER_ENTITY", "CKYC_DATA_ENTITY"]
  - LEADS: ["LEAD"]
  - CREDIT_REPORT: ["CREDIT_REPORT", "CREDIT_REPORTS_RAW", "CREDIT_REPORT_DOWNLOAD"]
  - SALARY_ESTIMATION: ["SALARY_ESTIMATION_ATTEMPTS"]
  STOCKGUARDIANDEVELOPER:
  - STOCKGUARDIAN_APPLICANT: ["APPLICANT_ENTITY_APPLICANT"]
  - STOCKGUARDIAN_APPLICATION: ["SG_APPLICATION_ENTITY_LOAN_APPLICATION", "SG_APPLICATION_ENTITY_LOAN_APPLICATION_STAGE"]
  - STOCKGUARDIAN_CREDIT_RISK: ["CREDIT_RISK_ENTITY_LOAN_OFFER", "CREDIT_RISK_ENTITY_LOAN_OFFER_ELIGIBILITY_CRITERIA"]
  - STOCKGUARDIAN_CUSTOMER: ["CUSTOMER_ENTITY_CUSTOMER"]
  - STOCKGUARDIAN_DOCS: ["SG_DOCS_ENTITY_DOCUMENT"]
  - STOCKGUARDIAN_E_SIGN: ["ESIGN_ENTITY_ESIGN"]
  - STOCKGUARDIAN_LMS: ["LMS_ENTITY_LOAN_ACCOUNT","LMS_ENTITY_LOAN_PAYMENT_POSTING_REQUEST","LMS_ENTITY_RECURRING_PAYMENT_EXECUTION","LMS_ENTITY_RECURRING_PAYMENT_EXECUTION_BATCH"]
  - STOCKGUARDIAN_MATRIX: ["MATRIX_ENTITY_CUSTOMER_APPLICATION","MATRIX_ENTITY_CUSTOMER_APPLICATION_DETAIL"]
  - STOCKGUARDIAN_KYC_VENDOR_DATA: ["SG_KYC_ENTITY_KYC_VENDOR_DATA"]
  - STOCKGUARDIAN_OMEGLE: [ "CALL_INFOS", "USER_APPLICATION_DOCUMENTS", "USER_APPLICATION" ]
  - STOCKGUARDIAN_VKYC_CALL: [ "CALL_DETAILS", "AUDITOR_REVIEW_DETAILS", "CALL_ID_TO_ACTOR_ID" ]
