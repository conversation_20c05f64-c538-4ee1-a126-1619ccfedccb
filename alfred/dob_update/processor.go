package dobupdate

import (
	"context"
	"errors"
	"fmt"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/alfred/config/genconf"
	"github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/alfred/procfactory"
	"github.com/epifi/gamma/alfred/profile_update/orchestrator"
	"github.com/epifi/gamma/alfred/profile_update/orchestrator/stageproc"
	profileupdate "github.com/epifi/gamma/alfred/profile_update/validator"
	alfredPb "github.com/epifi/gamma/api/alfred"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
)

type Processor struct {
	alfredDao    dao.ServiceRequestsDaoI
	genConf      *genconf.Config
	validator    profileupdate.Validator
	idgen        idgen.IdGenerator
	orchestrator orchestrator.Orchestrator
}

func NewDobUpdateProcessor(alfredDao dao.ServiceRequestsDaoI, genConf *genconf.Config, validator profileupdate.Validator,
	idgen idgen.IdGenerator, orchestrator orchestrator.Orchestrator) *Processor {
	return &Processor{
		alfredDao:    alfredDao,
		genConf:      genConf,
		validator:    validator,
		idgen:        idgen,
		orchestrator: orchestrator,
	}
}

var (
	// build check for ensuring processor implements interface
	_ procfactory.RequestManager = &Processor{}
)

var ProviderSet = wire.NewSet(
	NewDobUpdateProcessor,
	wire.Bind(new(procfactory.DobUpdate), new(*Processor)),
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*dlPb.Deeplink, error) {
	var (
		inProgress    bool
		failureReason alfredPb.ProfileUpdateMetadata_FailureReason
	)
	serviceReq, err := p.getLatestInProgressRecord(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		if inProgress, err = p.validator.IsProfileUpdateInProgress(ctx, actorId); err != nil {
			return nil, err
		}
		if inProgress {
			logger.Info(ctx, "another profile update in progress")
			return stageproc.GetDeeplinkByFailureReason(ctx, alfredPb.ProfileUpdateMetadata_FAILURE_REASON_ANOTHER_REQUEST_IN_PROGRESS), nil
		}

		failureReason, err = p.validator.CheckUpdateEligibility(ctx, actorId, []alfredPb.ProfileField{
			alfredPb.ProfileField_PROFILE_FIELD_DOB,
		})
		if err != nil {
			return nil, err
		}
		if failureReason != alfredPb.ProfileUpdateMetadata_FAILURE_REASON_UNSPECIFIED {
			logger.Info(ctx, "failed eligibility check", zap.String(logger.REASON, failureReason.String()))
			return stageproc.GetDeeplinkByFailureReason(ctx, failureReason), nil
		}

		serviceReq, err = p.createAlfredRequest(ctx, actorId)
		if err != nil {
			return nil, err
		}
	}

	return p.orchestrator.Orchestrate(ctx, serviceReq.GetId())
}

func (p *Processor) GetRequestStatus(ctx context.Context, requestId string) (*dlPb.Deeplink, error) {
	return p.orchestrator.Orchestrate(ctx, requestId)
}

func (p *Processor) GetRequestStatusDetails(ctx context.Context, sr *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	_, err := p.orchestrator.Orchestrate(ctx, sr.GetId())
	if err != nil {
		logger.Error(ctx, "error in syncing state", zap.Error(err))
		return nil, err
	}
	return p.alfredDao.GetById(ctx, sr.GetId())
}
func (p *Processor) GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken, pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented")
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

func (p *Processor) getLatestInProgressRecord(ctx context.Context, actorId string) (*alfredPb.ServiceRequest, error) {
	serviceReqs, err := p.alfredDao.GetByActorIdRequestType(ctx, actorId, alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE)
	if err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			logger.Info(ctx, fmt.Sprintf("no %v request found", alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE.String()))
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, fmt.Sprintf("error in getting service request of type %v", alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE.String()))
		return nil, err
	}

	latestServiceReq := serviceReqs[0]
	if latestServiceReq.GetStatus().IsTerminal() {
		logger.Info(ctx, "last request in terminal state")
		if err = p.alfredDao.DeleteByActorIdRequestType(ctx, actorId, alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE); err != nil {
			logger.Error(ctx, "error in removing terminal requests", zap.Error(err))
			return nil, err
		}
		return nil, epifierrors.ErrRecordNotFound
	}
	return latestServiceReq, nil
}

func (p *Processor) createAlfredRequest(ctx context.Context, actorId string) (*alfredPb.ServiceRequest, error) {
	serviceReq, err := p.alfredDao.Create(ctx, &alfredPb.ServiceRequest{
		RequestType: alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE,
		ActorId:     actorId,
		Status:      alfredPb.Status_STATUS_IN_PROGRESS,
		Id:          p.idgen.GetInAlphaNumeric(idgen.DobUpdate),
		Details: &alfredPb.Details{
			UserInput: &alfredPb.Details_ProfileUpdateUserInput{},
			Metadata: &alfredPb.Details_ProfileUpdateMetadata{
				ProfileUpdateMetadata: &alfredPb.ProfileUpdateMetadata{},
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in creating service request", zap.Error(err))
		return nil, err
	}
	return serviceReq, nil
}
