package usstocks_document

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"encoding/json"
	"fmt"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	successIcon = "https://epifi-icons.pointz.in/usstocks_images/success_icon.png"
	failureIcon = "https://epifi-icons.pointz.in/usstocks_images/failure_icon.png"
	ussTaxTitle = "US Stocks Tax Statements"
)

var SendUssTaxDocumentsWireSet = wire.NewSet(NewSendUssTaxDocuments, wire.Bind(new(procfactory.SendUssTaxDocuments), new(*SendUssTaxDocumentsProcessor)))

type SendUssTaxDocumentsProcessor struct {
	ussTaxClient tax.UssTaxServiceClient
}

func NewSendUssTaxDocuments(ussTaxClient tax.UssTaxServiceClient) *SendUssTaxDocumentsProcessor {
	return &SendUssTaxDocumentsProcessor{
		ussTaxClient: ussTaxClient,
	}
}

func (p *SendUssTaxDocumentsProcessor) ProvisionNewRequest(ctx context.Context, actorId string, blob []byte) (*deeplink.Deeplink, error) {
	if blob == nil {
		return nil, fmt.Errorf("blob cannto be nil")
	}
	var timeRangeInfo TimeRangeInfo
	err := json.Unmarshal(blob, &timeRangeInfo)
	if err != nil {
		return nil, fmt.Errorf("err unmarshalling timeRangeInfo: %w", err)
	}
	if timeRangeInfo.TimeRangeType == tax.TimeRangeType_TIME_RANGE_TYPE_UNSPECIFIED {
		return nil, errors.New("timeRangeType is required")
	}
	if timeRangeInfo.ComputedTill.IsZero() {
		return nil, errors.New("computedTill is required")
	}
	sendDocumentResp, sendDocumentsErr := p.ussTaxClient.SendTaxDocumentsToUser(ctx, &tax.SendTaxDocumentsToUserRequest{
		ActorId: actorId,
		TaxDocumentToSendParams: &tax.TaxDocumentsToSendParams{
			Params: &tax.TaxDocumentsToSendParams_DocumentsInTimeRange{
				DocumentsInTimeRange: &tax.DocumentsInTimeRange{
					TimeRangeType: timeRangeInfo.TimeRangeType,
					ComputedTill:  timestamppb.New(timeRangeInfo.ComputedTill),
				},
			},
		},
		Channels: []tax.DocumentShareChannel{
			tax.DocumentShareChannel_DOCUMENT_SHARE_CHANNEL_EMAIL,
		},
	})
	if rpcErr := epifigrpc.RPCError(sendDocumentResp, sendDocumentsErr); rpcErr != nil && !sendDocumentResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to send uss tax documents: %w", rpcErr)
	}
	if sendDocumentResp.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "no uss tax documents found")
		return InfoAckDeeplink(ussTaxTitle, failureIcon, "Your tax documents are being processed & will be available shortly"), nil
	}
	return InfoAckDeeplink(ussTaxTitle, successIcon, sendDocumentResp.GetMessage()), nil
}

func (p *SendUssTaxDocumentsProcessor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *SendUssTaxDocumentsProcessor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *SendUssTaxDocumentsProcessor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *SendUssTaxDocumentsProcessor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

func InfoAckDeeplink(title, iconUrl, description string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_INFO_ACKNOWLEDGEMENT_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.InfoAcknowledgementBottomSheetScreenOption{
			Title: ui.NewITC().WithTexts(commontypes.GetPlainStringText(title).WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_HEADLINE_M)),
			Icon: commontypes.GetVisualElementImageFromUrl(iconUrl).WithProperties(&commontypes.VisualElementProperties{
				Width:  80,
				Height: 80,
			}),
			Description: ui.NewITC().WithTexts(commontypes.GetPlainStringText(description).WithFontColor(colors.ColorOnLightHighEmphasis).WithFontStyle(commontypes.FontStyle_HEADLINE_M)),
			Ctas: []*deeplink.Cta{
				{
					Type:         deeplink.Cta_DONE,
					Text:         "Done",
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
		}),
	}
}
