package usstocks_document

// nolint: goimports
import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	ussAccountMgPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var AccountStatementMonthSelectionWireSet = wire.NewSet(NewAccountStatementMonthSelection, wire.Bind(new(procfactory.UssAccountStatementMonthSelection), new(*AccountStatementMonthSelectionProcessor)))

type AccountStatementMonthSelectionProcessor struct {
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient
	userClient          user.UsersClient
	timeClient          datetime.Time
}

func NewAccountStatementMonthSelection(ussAccountMgrClient ussAccountMgPb.AccountManagerClient, userClient user.UsersClient, timeClient datetime.Time) *AccountStatementMonthSelectionProcessor {
	return &AccountStatementMonthSelectionProcessor{
		ussAccountMgrClient: ussAccountMgrClient,
		userClient:          userClient,
		timeClient:          timeClient,
	}
}

// nolint:dupl
func (p *AccountStatementMonthSelectionProcessor) ProvisionNewRequest(ctx context.Context, actorId string, blob []byte) (*deeplink.Deeplink, error) {
	if blob == nil {
		return nil, fmt.Errorf("blob cannto be nil")
	}
	var monthlyStatementInfo UssMonthlyStatementYearInfo
	err := json.Unmarshal(blob, &monthlyStatementInfo)
	if err != nil {
		return nil, fmt.Errorf("err unmarshalling monthlyStatementYearInfo: %w", err)
	}

	choices, err := getMonthChoiceForUssMonthlyStatement(monthlyStatementInfo, p.timeClient.Now().In(datetime.IST))
	if err != nil {
		return nil, fmt.Errorf("failed to get financial years choices : %w", err)
	}
	if len(choices) == 0 {
		logger.Info(ctx, "no year range selection choices applicable for actor")
		return InfoAckDeeplink(ussMonthlyStatementTitle, failureIcon, ussMonthlyStatementDesc), nil
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.RequestChoiceScrollableBottomSheetScreenOptions{
			Title:              ui.NewITC().WithTexts(commontypes.GetPlainStringText("US Stocks Monthly Statements").WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_HEADLINE_M)),
			ChoicesDescription: ui.NewITC().WithTexts(commontypes.GetPlainStringText(fmt.Sprintf("CHOOSE A MONTH FOR %d", monthlyStatementInfo.MonthlyStatementYear)).WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Choices:            choices,
			FooterInfo: ui.NewITC().WithTexts(commontypes.GetPlainStringText(fmt.Sprintf("The statement will be sent to \n%s", monthlyStatementInfo.EmailId)).
				WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Cta: &deeplink.Cta{
				Text:         "Send Email",
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		}),
	}, nil
}

func (p *AccountStatementMonthSelectionProcessor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *AccountStatementMonthSelectionProcessor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *AccountStatementMonthSelectionProcessor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *AccountStatementMonthSelectionProcessor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

func getMonthChoiceForUssMonthlyStatement(monthlyStatementInfo UssMonthlyStatementYearInfo, currentTime time.Time) ([]*alfred.RequestChoiceV2, error) {
	var choices []*alfred.RequestChoiceV2

	for i := 1; i <= 12; i++ {
		// ignoring months before account creation
		if monthlyStatementInfo.MonthlyStatementYear == monthlyStatementInfo.AccountCreatedAt.Year() &&
			int(monthlyStatementInfo.AccountCreatedAt.Month()) > i {
			continue
		}

		// ignoring months after current month
		if monthlyStatementInfo.MonthlyStatementYear == currentTime.Year() &&
			int(currentTime.Month()) <= i {
			continue
		}

		blob, err := json.Marshal(&UssMonthlyStatementInfo{
			MonthlyStatementYear:  monthlyStatementInfo.MonthlyStatementYear,
			MonthlyStatementMonth: i,
			EmailId:               monthlyStatementInfo.EmailId,
			AccountCreatedAt:      monthlyStatementInfo.AccountCreatedAt,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to marshal monthly statement year : %w", err)
		}

		choices = append(choices, &alfred.RequestChoiceV2{
			ChoiceText: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(time.Month(i).String(), colors.ColorNight, commontypes.FontStyle_SUBTITLE_M)),
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PROVISION_NEW_REQUEST,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.ProvisionNewRequestScreenOptions{
						RequestType: alfredPb.RequestType_REQUEST_TYPE_SEND_USS_MONTHLY_STATEMENTS.String(),
						Blob:        blob,
					}),
				},
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
			IsSelected: false,
		})
	}
	return choices, nil
}

type UssMonthlyStatementInfo struct {
	MonthlyStatementYear  int
	MonthlyStatementMonth int
	AccountCreatedAt      time.Time
	EmailId               string
}
