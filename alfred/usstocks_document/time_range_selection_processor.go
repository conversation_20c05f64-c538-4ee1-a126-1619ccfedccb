package usstocks_document

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	ussAccountMgPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/usstocks/tax"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var UssTaxDocumentsTimeRangeSelectionWireSet = wire.NewSet(NewUssTaxDocumentsTimeRangeSelectionProcessor, wire.Bind(new(procfactory.UssTaxDocumentTimeRangeSelection), new(*UssTaxDocumentsTimeRangeSelectionProcessor)))

type UssTaxDocumentsTimeRangeSelectionProcessor struct {
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient
	userClient          user.UsersClient
	timeClient          datetime.Time
}

func NewUssTaxDocumentsTimeRangeSelectionProcessor(ussAccountMgrClient ussAccountMgPb.AccountManagerClient,
	userClient user.UsersClient,
	timeClient datetime.Time) *UssTaxDocumentsTimeRangeSelectionProcessor {
	return &UssTaxDocumentsTimeRangeSelectionProcessor{
		ussAccountMgrClient: ussAccountMgrClient,
		userClient:          userClient,
		timeClient:          timeClient,
	}
}

func (p *UssTaxDocumentsTimeRangeSelectionProcessor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	var (
		account *ussAccountMgPb.Account
		emailId string
	)
	errGrp, grpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		getAccountResp, getAccountErr := p.ussAccountMgrClient.GetAccount(grpCtx, &ussAccountMgPb.GetAccountRequest{
			Vendor:  commonvgpb.Vendor_ALPACA,
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(getAccountResp, getAccountErr); rpcErr != nil {
			if getAccountResp.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("no uss account found : %w", epifierrors.ErrRecordNotFound)
			}
			return fmt.Errorf("failed to get account : %w", rpcErr)
		}
		account = getAccountResp.GetAccount()
		return nil
	})
	errGrp.Go(func() error {
		getUserResp, getUserErr := p.userClient.GetUser(grpCtx, &user.GetUserRequest{
			Identifier: &user.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(getUserResp, getUserErr); rpcErr != nil {
			return fmt.Errorf("failed to get user : %w", rpcErr)
		}
		emailId = getUserResp.GetUser().GetProfile().GetEmail()
		return nil
	})
	if gErr := errGrp.Wait(); gErr != nil {
		if errors.Is(gErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "either uss acc or user profile not found", zap.Error(gErr))
			return InfoAckDeeplink(ussTaxTitle, failureIcon, "Uh-ho! We couldn’t find any documents for you"), nil
		}
		return nil, fmt.Errorf("failed to get data to build time range selection screen: %w", gErr)
	}
	choices, err := getFinancialYearsChoiceList(account.GetCreatedAt().AsTime(), p.timeClient.Now().In(datetime.IST))
	if err != nil {
		return nil, fmt.Errorf("failed to get financial years choices : %w", err)
	}
	if len(choices) == 0 {
		logger.Info(ctx, "no time range selection choices applicable for actor")
		return InfoAckDeeplink(ussTaxTitle, failureIcon, "Uh-ho! We couldn’t find any documents for you"), nil
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.RequestChoiceScrollableBottomSheetScreenOptions{
			Title:              ui.NewITC().WithTexts(commontypes.GetPlainStringText("US Stocks Tax Statements").WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_HEADLINE_M)),
			ChoicesDescription: ui.NewITC().WithTexts(commontypes.GetPlainStringText("CHOOSE A FINANCIAL YEAR").WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Choices:            choices,
			FooterInfo: ui.NewITC().WithTexts(commontypes.GetPlainStringText(fmt.Sprintf("The statement will be sent to \n%s", emailId)).
				WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Cta: &deeplink.Cta{
				Text:         "Send Email",
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		}),
	}, nil
}

func (p *UssTaxDocumentsTimeRangeSelectionProcessor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *UssTaxDocumentsTimeRangeSelectionProcessor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *UssTaxDocumentsTimeRangeSelectionProcessor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *UssTaxDocumentsTimeRangeSelectionProcessor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

// getFinancialYearsChoiceList returns request choices for financial years selection, it takes account creation time and based on that
// decides the FYs that user might have documents for. The lower hard limit is 2023-2024 FY
// we generate all eligible years, and later if there are no documents for a FY we show a no documents found
func getFinancialYearsChoiceList(accountCreatedAt time.Time, currentTime time.Time) ([]*alfred.RequestChoiceV2, error) {
	var choices []*alfred.RequestChoiceV2
	lastFinancialYearEnd := datetime.StartOfFinancialYear(currentTime.In(datetime.IST)).Add(-1 * time.Nanosecond)
	for lastFinancialYearEnd.After(accountCreatedAt) && lastFinancialYearEnd.Year() >= 2024 {
		yearEnd := lastFinancialYearEnd.In(datetime.IST).Year()
		blob, err := json.Marshal(&TimeRangeInfo{
			ComputedTill:  lastFinancialYearEnd,
			TimeRangeType: tax.TimeRangeType_TIME_RANGE_TYPE_FINANCIAL_YEAR,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to marshal financial year end time : %w", err)
		}
		durationStr := fmt.Sprintf("April %d to March %d", yearEnd-1, yearEnd)
		choices = append(choices, &alfred.RequestChoiceV2{
			ChoiceText: ui.NewITC().WithTexts(commontypes.GetPlainStringText(durationStr).WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_SUBTITLE_M)),
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PROVISION_NEW_REQUEST,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.ProvisionNewRequestScreenOptions{
						RequestType: alfredPb.RequestType_REQUEST_TYPE_SEND_USS_TAX_DOCUMENTS.String(),
						Blob:        blob,
					}),
				},
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
			IsSelected: false,
		})
		lastFinancialYearEnd = lastFinancialYearEnd.AddDate(-1, 0, 0)
	}
	return choices, nil
}

type TimeRangeInfo struct {
	ComputedTill  time.Time
	TimeRangeType tax.TimeRangeType
}
