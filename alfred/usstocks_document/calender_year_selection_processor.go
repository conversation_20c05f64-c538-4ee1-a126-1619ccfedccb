package usstocks_document

// nolint: goimports
import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user"
	ussAccountMgPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

const (
	ussMonthlyStatementTitle = "US Stocks Monthly Statements"
	ussMonthlyStatementDesc  = "Uh-ho! We couldn’t find any documents for you"
)

var AccountStatementYearSelectionWireSet = wire.NewSet(NewAccountStatementYearSelection, wire.Bind(new(procfactory.UssAccountStatementYearSelection), new(*AccountStatementYearSelectionProcessor)))

type AccountStatementYearSelectionProcessor struct {
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient
	userClient          user.UsersClient
	timeClient          datetime.Time
}

func NewAccountStatementYearSelection(ussAccountMgrClient ussAccountMgPb.AccountManagerClient, userClient user.UsersClient, timeClient datetime.Time) *AccountStatementYearSelectionProcessor {
	return &AccountStatementYearSelectionProcessor{
		ussAccountMgrClient: ussAccountMgrClient,
		userClient:          userClient,
		timeClient:          timeClient,
	}
}

// nolint:dupl
func (p *AccountStatementYearSelectionProcessor) ProvisionNewRequest(ctx context.Context, actorId string, blob []byte) (*deeplink.Deeplink, error) {
	var (
		account *ussAccountMgPb.Account
		emailId string
	)
	errGrp, grpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		getAccountResp, getAccountErr := p.ussAccountMgrClient.GetAccount(grpCtx, &ussAccountMgPb.GetAccountRequest{
			Vendor:  commonvgpb.Vendor_ALPACA,
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(getAccountResp, getAccountErr); rpcErr != nil {
			if getAccountResp.GetStatus().IsRecordNotFound() {
				return fmt.Errorf("no uss account found : %w", epifierrors.ErrRecordNotFound)
			}
			return fmt.Errorf("failed to get account : %w", rpcErr)
		}
		account = getAccountResp.GetAccount()
		return nil
	})
	errGrp.Go(func() error {
		getUserResp, getUserErr := p.userClient.GetUser(grpCtx, &user.GetUserRequest{
			Identifier: &user.GetUserRequest_ActorId{
				ActorId: actorId,
			},
		})
		if rpcErr := epifigrpc.RPCError(getUserResp, getUserErr); rpcErr != nil {
			return fmt.Errorf("failed to get user : %w", rpcErr)
		}
		emailId = getUserResp.GetUser().GetProfile().GetEmail()
		return nil
	})
	if gErr := errGrp.Wait(); gErr != nil {
		if errors.Is(gErr, epifierrors.ErrRecordNotFound) {
			logger.Info(ctx, "either uss acc or user profile not found", zap.Error(gErr))
			return InfoAckDeeplink(ussMonthlyStatementTitle, failureIcon, ussMonthlyStatementDesc), nil
		}
		return nil, fmt.Errorf("failed to get data to build year range selection screen: %w", gErr)
	}

	choices, err := getYearChoiceForUssMonthlyStatement(account, p.timeClient.Now().In(datetime.IST), emailId)
	if err != nil {
		return nil, fmt.Errorf("failed to get financial years choices : %w", err)
	}
	if len(choices) == 0 {
		logger.Info(ctx, "no year range selection choices applicable for actor")
		return InfoAckDeeplink(ussMonthlyStatementTitle, failureIcon, ussMonthlyStatementDesc), nil
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REQUEST_CHOICE_SCROLLABLE_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.RequestChoiceScrollableBottomSheetScreenOptions{
			Title:              ui.NewITC().WithTexts(commontypes.GetPlainStringText("US Stocks Monthly Statements").WithFontColor(colors.ColorNight).WithFontStyle(commontypes.FontStyle_HEADLINE_M)),
			ChoicesDescription: ui.NewITC().WithTexts(commontypes.GetPlainStringText("CHOOSE A YEAR").WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Choices:            choices,
			FooterInfo: ui.NewITC().WithTexts(commontypes.GetPlainStringText(fmt.Sprintf("The statement will be sent to \n%s", emailId)).
				WithFontColor(colors.ColorOnDarkMediumEmphasis).WithFontStyle(commontypes.FontStyle_OVERLINE_XS_CAPS)),
			Cta: &deeplink.Cta{
				Text:         "Select Month",
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		}),
	}, nil
}

func (p *AccountStatementYearSelectionProcessor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *AccountStatementYearSelectionProcessor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *AccountStatementYearSelectionProcessor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *AccountStatementYearSelectionProcessor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

// nolint:gocritic
func getYearChoiceForUssMonthlyStatement(account *ussAccountMgPb.Account, currentTime time.Time, emailId string) ([]*alfred.RequestChoiceV2, error) {
	var choices []*alfred.RequestChoiceV2
	if account.GetAccountStatus() != ussAccountMgPb.AccountStatus_ACTIVE {
		return choices, nil
	}

	accountCreatedAt := account.GetCreatedAt().AsTime()
	currentYear := currentTime.In(datetime.IST).Year()
	accountCreatedYear := accountCreatedAt.In(datetime.IST).Year()

	for accountCreatedYear <= currentYear {
		blob, err := json.Marshal(&UssMonthlyStatementYearInfo{
			MonthlyStatementYear: currentYear,
			AccountCreatedAt:     accountCreatedAt,
			EmailId:              emailId,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to marshal monthly statement year : %w", err)
		}

		choices = append(choices, &alfred.RequestChoiceV2{
			ChoiceText: ui.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle(strconv.Itoa(currentYear), colors.ColorNight, commontypes.FontStyle_SUBTITLE_M)),
			Cta: &deeplink.Cta{
				Type: deeplink.Cta_CUSTOM,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PROVISION_NEW_REQUEST,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred.ProvisionNewRequestScreenOptions{
						RequestType: alfredPb.RequestType_REQUEST_TYPE_USS_DOCUMENTS_MONTH_SELECTION.String(),
						Blob:        blob,
					}),
				},
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
			IsSelected: false,
		})
		currentYear = currentYear - 1
	}

	return choices, nil
}

type UssMonthlyStatementYearInfo struct {
	MonthlyStatementYear int
	AccountCreatedAt     time.Time
	EmailId              string
}
