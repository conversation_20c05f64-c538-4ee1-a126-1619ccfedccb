package usstocks_document

// nolint: goimports
import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/google/wire"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	ussAccountMgPb "github.com/epifi/gamma/api/usstocks/account"
	"github.com/epifi/gamma/api/vendorgateway/stocks"
)

var SendUssMonthlyStatementsWireSet = wire.NewSet(NewSendUssMonthlyStatements, wire.Bind(new(procfactory.SendUssMonthlyStatements), new(*SendUssMonthlyStatementsProcessor)))

type SendUssMonthlyStatementsProcessor struct {
	ussAccountMgrClient ussAccountMgPb.AccountManagerClient
}

func NewSendUssMonthlyStatements(ussAccountMgrClient ussAccountMgPb.AccountManagerClient) *SendUssMonthlyStatementsProcessor {
	return &SendUssMonthlyStatementsProcessor{
		ussAccountMgrClient: ussAccountMgrClient,
	}
}

func (p *SendUssMonthlyStatementsProcessor) ProvisionNewRequest(ctx context.Context, actorId string, blob []byte) (*deeplink.Deeplink, error) {
	if blob == nil {
		return nil, fmt.Errorf("blob cannto be nil")
	}
	var monthlyStatementInfo UssMonthlyStatementInfo
	err := json.Unmarshal(blob, &monthlyStatementInfo)
	if err != nil {
		return nil, fmt.Errorf("err unmarshalling monthlyStatementYearInfo: %w", err)
	}

	startDate, endDate := getStartDateAndEndDateForMonthlyStatement(monthlyStatementInfo.MonthlyStatementMonth, monthlyStatementInfo.MonthlyStatementYear)

	sendDocumentResp, sendDocumentsErr := p.ussAccountMgrClient.SendDocumentToUser(ctx, &ussAccountMgPb.SendDocumentToUserRequest{
		ActorId:               actorId,
		StartDate:             startDate,
		EndDate:               endDate,
		DocumentStatementType: stocks.DocumentStatementType_DOCUMENT_STATEMENT_TYPE_MONTHLY_ACCOUNT_STATEMENT,
	})
	if rpcErr := epifigrpc.RPCError(sendDocumentResp, sendDocumentsErr); rpcErr != nil && !sendDocumentResp.GetStatus().IsRecordNotFound() {
		return nil, fmt.Errorf("failed to send uss monthly statement to user: %w", rpcErr)
	}
	if sendDocumentResp.GetStatus().IsRecordNotFound() {
		logger.Info(ctx, "no uss monthly statement document found")
		return InfoAckDeeplink(ussMonthlyStatementTitle, failureIcon, ussMonthlyStatementDesc), nil
	}
	return InfoAckDeeplink(ussMonthlyStatementTitle, successIcon, fmt.Sprintf("Your statement will be sent to your email %s.", monthlyStatementInfo.EmailId)), nil
}

func (p *SendUssMonthlyStatementsProcessor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *SendUssMonthlyStatementsProcessor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *SendUssMonthlyStatementsProcessor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *SendUssMonthlyStatementsProcessor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

func getStartDateAndEndDateForMonthlyStatement(month, year int) (firstDay, lastDay *date.Date) {
	// first day of month with month and year in request
	firstDayOfMonth := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, datetime.IST)
	// last day of month with month and year in request
	lastDayOfMonth := datetime.EndOfMonth(firstDayOfMonth)

	return datetime.TimestampToDateInLoc(timestamppb.New(firstDayOfMonth), datetime.IST), datetime.TimestampToDateInLoc(timestamppb.New(lastDayOfMonth), datetime.IST)
}
