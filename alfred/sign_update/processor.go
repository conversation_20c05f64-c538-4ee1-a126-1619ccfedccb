package signupdate

import (
	"context"
	"sort"
	"sync"
	"time"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/api/alfred"
	savingsClientPb "github.com/epifi/gamma/api/savings"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/gamma/alfred/chequebook/checker"
	"github.com/epifi/gamma/alfred/eligibility"
	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
)

type Processor struct {
	alfredDao          dao.ServiceRequestsDaoI
	eligibilityChecker procfactory.EligibilityChecker
	savingsClient      savingsClientPb.SavingsClient
}

func NewChequebookProcessor(alfredDao dao.ServiceRequestsDaoI, kycEligibilityChecker *checker.KYCChecker, savingsClient savingsClientPb.SavingsClient) *Processor {
	return &Processor{
		alfredDao:          alfredDao,
		eligibilityChecker: kycEligibilityChecker,
		savingsClient:      savingsClient,
	}
}

var (
	// build check for ensuring processor implements interface
	_ procfactory.RequestManager = &Processor{}
)

var ProviderSet = wire.NewSet(
	NewChequebookProcessor,

	// bind to interface
	wire.Bind(new(procfactory.SignUpdate), new(*Processor)),
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	eligibilityCheckerResp, err := p.eligibilityChecker.IsEligible(ctx, &procfactory.IsEligibleRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "Error in checking KYC eligibility in sign update flow", zap.Error(err))
		return nil, err
	}

	if !eligibilityCheckerResp.IsEligible {
		return eligibilityCheckerResp.NextAction, nil
	}

	signResp, signErr := p.savingsClient.FetchOrCreateSignAttempt(ctx, &savingsClientPb.FetchOrCreateSignAttemptRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(signResp, signErr); te != nil {
		logger.Error(ctx, "Error in fetching sign attempt", zap.Error(te))
		return nil, te
	}
	if signResp.GetSignStatus() == savingsClientPb.SignStatus_SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK {
		return getSavingsSignAlreadyPresentPromptDl(), nil
	}

	return eligibility.EligibilityFailureErrorScreen(ctx, &eligibility.DynamicValue{
		SignUrl:     signResp.GetSignUrl(),
		SignExitUrl: signResp.GetExitUrl(),
		SignStatus:  signResp.GetSignStatus(),
	}, alfred.RequestType_REQUEST_TYPE_SAVINGS_ACC_SIGNATURE_CHANGE, eligibility.MissingSignatureV2), nil

}

func (p *Processor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("GetRequestStatus: unimplemented method in sign update flow")
}
func (p *Processor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("GetRequestStatusDetails: unimplemented method in sign update flow")
}

func (p *Processor) GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken,
	pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	serviceRequests, pageContextResp, err := p.alfredDao.GetPaginatedRequestDetails(ctx, filters.GetActorId(), filters.GetRequestTypes()[0], filters.GetStatusList(), pageToken, pageSize, sortOrder)
	if err != nil {
		logger.Error(ctx, "error in getting the paginated service requests", zap.Error(err))
		return nil, nil, err
	}
	var serviceReqList []*alfredPb.ServiceRequest
	// Process service request in parallel
	var wg sync.WaitGroup
	for _, sr := range serviceRequests {
		goSr := sr
		wg.Add(1)
		goroutine.Run(ctx, time.Second*5, func(ctx context.Context) {
			defer wg.Done()
			// else update the service requests according workflow request
			updatedSr, err1 := p.GetRequestStatusDetails(ctx, goSr)
			if err1 != nil {
				serviceReqList = append(serviceReqList, goSr)
				return
			}
			// if ordered at is empty means chequebook is not requested yet
			if updatedSr.GetDetails().GetChequebookMetadata().GetOrderedAt() == nil {
				return
			}

			serviceReqList = append(serviceReqList, updatedSr)
		})
	}
	waitgroup.SafeWaitWithDefaultTimeout(&wg)
	// sorting here, because doing this async wouldn't ensure chronological order,
	// as items can be appended in list in any order.
	sort.SliceStable(serviceReqList, func(i, j int) bool {
		// sorting in decreasing order of createdAt date
		return serviceReqList[i].GetCreatedAt().AsTime().After(serviceReqList[j].GetCreatedAt().AsTime())
	})
	return serviceReqList, pageContextResp, nil
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}
