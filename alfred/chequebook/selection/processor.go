package selection

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"fmt"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/alfred/chequebook/checker"
	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	alfred2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/alfred"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

type Processor struct {
	freeChequebookEligibilityChecker checker.FreeChequebookEligibilityChecker
}

func NewSelectionProcessor(freeChequebookEligibilityChecker checker.FreeChequebookEligibilityChecker) *Processor {
	return &Processor{
		freeChequebookEligibilityChecker: freeChequebookEligibilityChecker,
	}
}

var (
	_ procfactory.RequestManager = &Processor{}

	ProviderSet = wire.NewSet(
		NewSelectionProcessor,

		// bind to interface
		wire.Bind(new(procfactory.Selection), new(*Processor)),
	)
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	var (
		chequebookPrice = 118
	)

	isFreeChkbq, err := p.freeChequebookEligibilityChecker.IsEligibleForFreeChequebook(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while checking free eligibility for chequebook", zap.Error(err))
		return nil, err
	}
	if isFreeChkbq {
		chequebookPrice = 0
	}

	return &deeplink.Deeplink{
		Screen: deeplink.Screen_REQUEST_CHOICE_BOTTOM_SHEET,
		ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred2.RequestChoiceBottomSheetScreenOptions{
			Title: commontypes.GetTextFromStringFontColourFontStyle(bottomSheetTitle, blackFontColor1, commontypes.FontStyle_SUBTITLE_1),
			RequestChoices: []*alfred2.RequestChoice{
				getRequestChoice(cancelledChequeTitle, cancelledChequeSubtitle, "₹0", alfredPb.RequestType_REQUEST_TYPE_DOWNLOAD_DIGITAL_CANCELLED_CHEQUEBOOK.String()),
				getRequestChoice(chequebookTitle, chequebookSubtitle, fmt.Sprintf("₹%v", chequebookPrice), alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK.String()),
			},
		}),
	}, nil
}

func getRequestChoice(title, subtitle, price, requestType string) *alfred2.RequestChoice {
	return &alfred2.RequestChoice{
		Title:    commontypes.GetTextFromStringFontColourFontStyle(title, blackFontColorNeutral, commontypes.FontStyle_HEADLINE_M),
		Subtitle: commontypes.GetTextFromStringFontColourFontStyle(subtitle, blackFontColorLead, commontypes.FontStyle_BODY_S),
		Price:    commontypes.GetTextFromStringFontColourFontStyle(fmt.Sprintf("%v", price), blackFontColorNight, commontypes.FontStyle_HEADLINE_L),
		Ctas: []*deeplink.Cta{
			{
				Type: deeplink.Cta_CUSTOM,
				Text: continueCtaText,
				Deeplink: &deeplink.Deeplink{
					Screen: deeplink.Screen_PROVISION_NEW_REQUEST,
					ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&alfred2.ProvisionNewRequestScreenOptions{
						RequestType: requestType,
					}),
				},
				DisplayTheme: deeplink.Cta_PRIMARY,
			},
		},
	}
}

func (p *Processor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, fmt.Errorf("unimplemented")
}

func (p *Processor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, fmt.Errorf("unimplemented")
}

func (p *Processor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, pageToken *pagination.PageToken, pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, fmt.Errorf("unimplemented")
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, fmt.Errorf("eligibility checker is not allowed for this request type")
}
