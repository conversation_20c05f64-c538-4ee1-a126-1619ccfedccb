// nolint: dupl
package chequebook

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/google/wire"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"

	"github.com/epifi/gamma/alfred/chequebook/checker"
	"github.com/epifi/gamma/alfred/config/genconf"
	"github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/alfred/eligibility"
	"github.com/epifi/gamma/alfred/procfactory"
	events2 "github.com/epifi/gamma/pkg/chequebook/events"
	"github.com/epifi/gamma/pkg/chequebook/helper"

	alfredPb "github.com/epifi/gamma/api/alfred"

	"github.com/epifi/gamma/api/frontend/deeplink"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	types "github.com/epifi/gamma/api/typesv2"
)

type Processor struct {
	alfredDao                        dao.ServiceRequestsDaoI
	idgen                            idgen.IdGenerator
	genConf                          *genconf.Config
	requestSynchronizer              procfactory.RequestSynchronizer
	requestEligibilityChecker        procfactory.RequestEligibilityChecker
	freeChequebookEligibilityChecker checker.FreeChequebookEligibilityChecker
	eventBroker                      events.Broker
}

func NewChequebookProcessor(alfredDao dao.ServiceRequestsDaoI, idgen idgen.IdGenerator, genConf *genconf.Config, requestSynchronizer procfactory.RequestSynchronizer,
	requestEligibilityChecker procfactory.RequestEligibilityChecker, freeChequebookEligibilityChecker checker.FreeChequebookEligibilityChecker, eventBroker events.Broker) *Processor {
	return &Processor{
		alfredDao:                        alfredDao,
		idgen:                            idgen,
		genConf:                          genConf,
		requestSynchronizer:              requestSynchronizer,
		requestEligibilityChecker:        requestEligibilityChecker,
		freeChequebookEligibilityChecker: freeChequebookEligibilityChecker,
		eventBroker:                      eventBroker,
	}
}

var (
	// build check for ensuring processor implements interface
	_ procfactory.RequestManager = &Processor{}
)

var ProviderSet = wire.NewSet(
	NewChequebookProcessor,

	// bind to interface
	wire.Bind(new(procfactory.Chequebook), new(*Processor)),
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	var (
		sr              = &alfredPb.ServiceRequest{}
		serviceRequests []*alfredPb.ServiceRequest
		err             error
		syncErr         error
		daoErr          error
	)

	// fetch chequebook request record
	serviceRequests, err = p.alfredDao.GetByActorIdRequestType(ctx, actorId, alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK)
	// ignore if record not found, since for new request we don't have any entry in db
	if err != nil && !storagev2.IsRecordNotFoundError(err) {
		logger.Error(ctx, "unable to get the request details", zap.Error(err))
		return nil, err
	}
	if !storagev2.IsRecordNotFoundError(err) {
		sr, syncErr = p.requestSynchronizer.SyncRequest(ctx, serviceRequests[0].GetId())
		if syncErr != nil && !errors.Is(syncErr, epifierrors.ErrRecordNotFound) {
			logger.Error(ctx, "error while syncing the request details", zap.String(logger.REQUEST_ID, serviceRequests[0].GetId()),
				zap.Error(syncErr))
			return nil, syncErr
		}
	}
	logger.Info(ctx, fmt.Sprintf("logging the updated sr %v", sr))
	//  request is success or progress but haven't breached 31 days limit
	if isRequestAlreadyPlaced(sr) {
		logger.Debug(ctx, "service request is in progress or success on have not passed 31 days", zap.String(logger.REQUEST_ID, sr.GetId()))
		return alreadyPlacedRequestWithInXDays(sr, p.isCopyTrackingUrlFeatureEnabled(ctx)), nil
	}

	if isRequestInPending(sr) {
		logger.Info(ctx, "request in pending state")
		return p.getOrderChqbkDL(ctx, sr)
	}

	// request in failed or stuck and success and breached 31 days limit, we delete current record
	if isRequestEligibleForDeletionCreation(sr) {
		if daoErr = p.alfredDao.DeleteByActorIdRequestType(ctx, sr.GetActorId(), sr.GetRequestType()); daoErr != nil {
			logger.Error(ctx, fmt.Sprintf("unable to delete service request: %v",
				alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK.String()), zap.String(logger.REQUEST_ID, sr.GetId()))
			return nil, fmt.Errorf("error while deleting request %v", sr.GetId())
		}
	}

	// create a new record
	sr, daoErr = p.createServiceRequest(ctx, actorId)
	if daoErr != nil {
		logger.Error(ctx, "error in creating new request", zap.Error(daoErr))
		return nil, daoErr
	}
	events2.EmitChequebookEvents(ctx, actorId, sr.GetId(), events2.ChequebookFlowStarted, p.eventBroker)
	return p.getOrderChqbkDL(ctx, sr)
}

func (p *Processor) updateRequest(ctx context.Context, sr *alfredPb.ServiceRequest, isFreeChqbk bool) error {
	if isFreeChqbk {
		sr.Details = &alfredPb.Details{
			Metadata: &alfredPb.Details_ChequebookMetadata{
				ChequebookMetadata: &alfredPb.ChequebookMetadata{
					Charges: &types.Money{
						CurrencyCode: money.RupeeCurrencyCode,
						Units:        0,
					},
				},
			},
		}
	} else {
		sr.Details = &alfredPb.Details{
			Metadata: &alfredPb.Details_ChequebookMetadata{
				ChequebookMetadata: &alfredPb.ChequebookMetadata{
					Charges: helper.GetBalRequiredToOrderChequeBook(),
				},
			},
		}
	}
	updateErr := p.updateServiceRequest(ctx, sr, []alfredPb.ServiceRequestFieldMask{alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_METADATA})
	if updateErr != nil {
		logger.Error(ctx, "error while updating service request", zap.Error(updateErr))
		return updateErr
	}
	return nil
}

// nolint: dupl
func (p *Processor) GetRequestStatus(ctx context.Context, requestId string) (*deeplink.Deeplink, error) {
	var (
		sr  = &alfredPb.ServiceRequest{}
		err error
	)
	// fetch chequebook request record
	sr, err = p.alfredDao.GetById(ctx, requestId)
	if err != nil {
		logger.Error(ctx, "unable to get the request details", zap.String(logger.REQUEST_ID, requestId))
		return nil, err
	}

	sr, err = p.requestSynchronizer.SyncRequest(ctx, sr.GetId())
	if err != nil {
		logger.Error(ctx, "error while syncing the request", zap.String(logger.REQUEST_ID, sr.GetId()), zap.Error(err))
		return nil, err
	}

	if isRequestInPending(sr) {
		return sendPollingDL(sr), nil
	}

	if isRequestAlreadyPlaced(sr) {
		isFreeChkbq, err := p.freeChequebookEligibilityChecker.IsEligibleForFreeChequebook(ctx, sr.GetActorId())
		if err != nil {
			logger.Error(ctx, "error while checking free eligibility for chequebook", zap.Error(err))
			return nil, err
		}
		if isFreeChkbq {
			contentOptions := eligibility.ContentOptions{
				Title:       infoAckTitle,
				Subtitle:    infoAckFreeChkbqSubtitle,
				ImageUrl:    infoAckImageUrl,
				CtaDeeplink: requestDetailsDL(sr, p.isCopyTrackingUrlFeatureEnabled(ctx)),
				CtaText:     infoAckCtaText,
			}
			return eligibility.InfoAcknowledgementScreen(contentOptions), nil
		}
		contentOptions := eligibility.ContentOptions{
			Title:       infoAckTitle,
			Subtitle:    infoAckSubtitle,
			ImageUrl:    infoAckImageUrl,
			CtaDeeplink: requestDetailsDL(sr, p.isCopyTrackingUrlFeatureEnabled(ctx)),
			CtaText:     infoAckCtaText,
		}
		return eligibility.InfoAcknowledgementScreen(contentOptions), nil
	}

	// return status screen if request is in terminal state
	if isRequestInTerminalState(sr) {
		logger.Debug(ctx, "request in terminal state")
		return requestStatusToDL(ctx, sr, p.isCopyTrackingUrlFeatureEnabled(ctx)), nil
	}
	return nil, errors.Wrap(nil, "unable to fetch the latest status of request")
}

func (p *Processor) updateServiceRequest(ctx context.Context, sr *alfredPb.ServiceRequest, updatingFieldMask []alfredPb.ServiceRequestFieldMask) error {
	_, err := p.alfredDao.UpdateServiceRequestByFieldMask(ctx, sr, updatingFieldMask)
	if err != nil {
		logger.Error(ctx, "unable to update service request", zap.Error(err))
		return err
	}
	return nil
}

func (p *Processor) createServiceRequest(ctx context.Context, actorId string) (*alfredPb.ServiceRequest, error) {
	sr, err := p.alfredDao.Create(ctx, &alfredPb.ServiceRequest{
		Id:          p.idgen.GetInAlphaNumeric(idgen.Chequebook),
		ActorId:     actorId,
		RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
		Status:      alfredPb.Status_STATUS_IN_PROGRESS,
		Details:     &alfredPb.Details{},
	})
	if err != nil {
		logger.Error(ctx, "error while creating new request", zap.Error(err))
		return nil, err
	}
	return sr, nil
}

func (p *Processor) getOrderChqbkDL(ctx context.Context, sr *alfredPb.ServiceRequest) (*deeplink.Deeplink, error) {
	resp, err := p.requestEligibilityChecker.IsActorEligible(ctx, &procfactory.IsActorEligibleRequest{
		ActorId: sr.GetActorId(),
	})
	if err != nil {
		logger.Error(ctx, "error in checking eligibility for new request", zap.String(logger.REQUEST_ID, sr.GetId()))
		return nil, err
	}
	if !resp.IsEligible {
		logger.Info(ctx, "user is not eligible to request chequebook", zap.String(logger.REQUEST_ID, sr.GetId()))
		return resp.NextAction, nil
	}
	isFreeChqbk, freeChqbkErr := p.freeChequebookEligibilityChecker.IsEligibleForFreeChequebook(ctx, sr.GetActorId())
	if freeChqbkErr != nil {
		logger.Error(ctx, "error in checking the free chequebook eligibility", zap.Error(freeChqbkErr))
		return nil, freeChqbkErr
	}
	if daoErr := p.updateRequest(ctx, sr, isFreeChqbk); daoErr != nil {
		logger.Error(ctx, "error in checking free chequebook for user", zap.Error(daoErr))
		return nil, daoErr
	}
	return initialiseOrderChqbkDeeplinkOptions(ctx, isFreeChqbk, sr), nil
}

// nolint: funlen
func (p *Processor) GetRequestStatusDetails(ctx context.Context, sr *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	// if request is in terminal state then it means request is already updated
	// no need to check further
	updatedSr, err := p.requestSynchronizer.SyncRequest(ctx, sr.GetId())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return sr, nil
		}
		logger.Error(ctx, "error while syncing the request", zap.String(logger.REQUEST_ID, sr.GetId()), zap.Error(err))
		return nil, err
	}
	return updatedSr, nil
}

func (p *Processor) GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken,
	pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpcPb.PageContextResponse, error) {
	serviceRequests, pageContextResp, err := p.alfredDao.GetPaginatedRequestDetails(ctx, filters.GetActorId(), filters.GetRequestTypes()[0], filters.GetStatusList(), pageToken, pageSize, sortOrder)
	if err != nil {
		logger.Error(ctx, "error in getting the paginated service requests", zap.Error(err))
		return nil, nil, err
	}
	var serviceReqList []*alfredPb.ServiceRequest
	// Process service request in parallel
	wg := &sync.WaitGroup{}
	for _, sr := range serviceRequests {
		goSr := sr
		wg.Add(1)
		goroutine.Run(ctx, time.Second*5, func(ctx context.Context) {
			defer wg.Done()
			// else update the service requests according workflow request
			updatedSr, err1 := p.GetRequestStatusDetails(ctx, goSr)
			if err1 != nil {
				serviceReqList = append(serviceReqList, goSr)
				return
			}
			// if ordered at is empty means chequebook is not requested yet
			if updatedSr.GetDetails().GetChequebookMetadata().GetOrderedAt() == nil {
				return
			}

			serviceReqList = append(serviceReqList, updatedSr)
		})
	}
	waitgroup.SafeWaitCtx(ctx, wg)
	// sorting here, because doing this async wouldn't ensure chronological order,
	// as items can be appended in list in any order.
	sort.SliceStable(serviceReqList, func(i, j int) bool {
		// sorting in decreasing order of createdAt date
		return serviceReqList[i].GetCreatedAt().AsTime().After(serviceReqList[j].GetCreatedAt().AsTime())
	})
	return serviceReqList, pageContextResp, nil
}

func (p *Processor) isCopyTrackingUrlFeatureEnabled(ctx context.Context) bool {
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, p.genConf.EnableCopyTrackingUrl()) {
		logger.Info(ctx, "copy tracking feature is enabled")
		return true
	}
	return false
}

// Map to convert MissingEligibility to EligibilityFailureReason enum
var missingEligibilityToFailureReasonMap = map[eligibility.MissingEligibility]alfredPb.EligibilityFailureReason{
	eligibility.FullKycMissing:                       alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING,
	eligibility.InsufficientBalanceToOrderChequebook: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE,
	eligibility.MissingSignature:                     alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE,
	eligibility.MissingSignatureV2:                   alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE,
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	// Use the existing eligibility checker which now returns specific failure reasons
	eligibilityResult, err := p.requestEligibilityChecker.IsActorEligible(ctx, &procfactory.IsActorEligibleRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in checking eligibility for new request", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, err
	}

	if eligibilityResult.IsEligible {
		// If user is eligible, return true
		logger.Debug(ctx, "user is eligible for chequebook request", zap.String(logger.ACTOR_ID_V2, actorId))
		return true, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, nil
	}

	// Map the specific missing eligibility to the corresponding failure reason enum
	failureReason, exists := missingEligibilityToFailureReasonMap[eligibilityResult.MissingEligibility]
	if !exists {
		logger.WarnWithCtx(ctx, "unknown missing eligibility type",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.String("missing_eligibility", string(eligibilityResult.MissingEligibility)))
		failureReason = alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED
	}

	logger.Info(ctx, "user is not eligible to request chequebook",
		zap.String(logger.ACTOR_ID_V2, actorId),
		zap.String("missing_eligibility", string(eligibilityResult.MissingEligibility)))
	return false, failureReason, nil

}
