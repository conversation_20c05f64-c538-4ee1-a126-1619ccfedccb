package checker

import (
	"context"
	"fmt"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/alfred/eligibility"
	"github.com/epifi/gamma/alfred/procfactory"
)

type Processor struct {
	kycCheckerProc     procfactory.EligibilityChecker
	balanceCheckerProc procfactory.EligibilityChecker
	signCheckerProc    procfactory.EligibilityChecker
}

func NewProcessor(kycCheckerProc *KYCChecker, balanceCheckerProc *BalanceChecker,
	signCheckerProc *SignatureChecker) *Processor {
	return &Processor{
		kycCheckerProc:     kycCheckerProc,
		balanceCheckerProc: balanceCheckerProc,
		signCheckerProc:    signCheckerProc,
	}
}

var (
	_ procfactory.RequestEligibilityChecker = &Processor{}
)

var ProviderSet = wire.NewSet(
	NewProcessor,

	// bind to interface
	wire.Bind(new(procfactory.RequestEligibilityChecker), new(*Processor)),
)

func (p *Processor) IsActorEligible(ctx context.Context, req *procfactory.IsActorEligibleRequest) (*procfactory.IsActorEligibleResponse, error) {
	var eligibilityCheckerToProcMap = map[eligibility.MissingEligibility]procfactory.EligibilityChecker{
		eligibility.FullKycMissing:                       p.kycCheckerProc,
		eligibility.MissingSignature:                     p.signCheckerProc,
		eligibility.InsufficientBalanceToOrderChequebook: p.balanceCheckerProc,
	}

	var eligibilityCheckerOrder = []eligibility.MissingEligibility{
		eligibility.FullKycMissing,
		eligibility.MissingSignature,
		eligibility.InsufficientBalanceToOrderChequebook,
	}

	for _, eligibilityChecker := range eligibilityCheckerOrder {
		eligibilityCheckerProc, ok := eligibilityCheckerToProcMap[eligibilityChecker]
		if !ok {
			logger.Error(ctx, fmt.Sprintf("Can not find process for eligibility checker processor %v:", eligibilityChecker))
			return nil, epifierrors.ErrInvalidArgument
		}
		eligibilityCheckerProcResp, err := eligibilityCheckerProc.IsEligible(ctx, &procfactory.IsEligibleRequest{
			ActorId: req.ActorId,
		})

		if err != nil {
			logger.Error(ctx, "Error in checking eligibility", zap.Error(err))
			return nil, err
		}
		if !eligibilityCheckerProcResp.IsEligible {
			return &procfactory.IsActorEligibleResponse{
				NextAction:         eligibilityCheckerProcResp.NextAction,
				IsEligible:         false,
				MissingEligibility: eligibilityChecker,
			}, nil
		}
	}
	return &procfactory.IsActorEligibleResponse{
		IsEligible: true,
	}, nil
}
