package download_cancelled_cheque

import (
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"context"
	"errors"

	"github.com/google/wire"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/frontend/deeplink"
	kycPb "github.com/epifi/gamma/api/kyc"
	vkycBe "github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/pkg/vkyc"
)

type Processor struct {
	bcClient bankcust.BankCustomerServiceClient
}

func NewDownloadCancelledChequeProcessor(bcClient bankcust.BankCustomerServiceClient) *Processor {
	return &Processor{
		bcClient: bcClient,
	}
}

var ProviderSet = wire.NewSet(
	NewDownloadCancelledChequeProcessor,

	// bind to interface
	wire.Bind(new(procfactory.DownloadCancelledCheque), new(*Processor)),
)

var (
	// build check for ensuring processor implements interface
	_ procfactory.RequestManager = &Processor{}
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	bcResp, err := p.bcClient.GetBankCustomer(ctx, &bankcust.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching bank customer")
		return nil, rpcErr
	}
	if bcResp.GetBankCustomer().GetKycInfo().GetKycLevel() == kycPb.KYCLevel_MIN_KYC {
		logger.Info(ctx, "user in min kyc, not allowing user to download cancelled chequebook")
		return vkyc.BuildVKYCStatusDeeplink(&vkyc.StatusScreenOptions{
			EntryPoint: vkycBe.EntryPoint_ENTRY_POINT_CANCELLED_CHEQUE,
		})
	}
	return downloadCancelledChequeDl, nil
}

func (p *Processor) GetRequestStatus(ctx context.Context, requestId string) (*deeplink.Deeplink, error) {
	return nil, nil
}

func (p *Processor) GetRequestStatusDetails(ctx context.Context, sr *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, nil
}

func (p *Processor) GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken, pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, nil
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}
