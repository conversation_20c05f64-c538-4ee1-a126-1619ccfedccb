package alfred_test

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/golang/protobuf/proto"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/api/rpc"
	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
)

var (
	error1 = errors.New("failed to get updated service requests")
	error2 = errors.New("failed to get service request")
	error3 = errors.New("failed to poll request")
	id1    = "id-1"
	dob    = &date.Date{
		Month: 1,
		Day:   1,
		Year:  2000,
	}
	serviceReq = &alfredPb.ServiceRequest{
		Id:          id1,
		RequestType: alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE,
		Details:     &alfredPb.Details{},
	}

	profileUserInput = &alfredPb.ProfileUpdateUserInput{
		Dob: dob,
	}
	serviceReqWithInput = &alfredPb.ServiceRequest{
		Id:          id1,
		RequestType: alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE,
		Details: &alfredPb.Details{
			UserInput: &alfredPb.Details_ProfileUpdateUserInput{
				ProfileUpdateUserInput: profileUserInput,
			},
		},
	}
)

func TestService_ProvisionNewRequest(t *testing.T) {

	type args struct {
		ctx context.Context
		req *alfredPb.ProvisionNewRequestRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    *alfredPb.ProvisionNewRequestResponse
		wantErr bool
	}{
		{
			name: "unable to get request processor",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.ProvisionNewRequestRequest{},
			},
			mocks: nil,
			want: &alfredPb.ProvisionNewRequestResponse{
				Status: rpc.StatusInternalWithDebugMsg("no request processor defined for the request type: REQUEST_TYPE_UNSPECIFIED"),
			},
			wantErr: false,
		},
		{
			name: "failed to provision new request",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.ProvisionNewRequestRequest{
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					ActorId:     id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().ProvisionNewRequest(context.Background(), id1, nil).Return(nil, error1)
			},
			want: &alfredPb.ProvisionNewRequestResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.ProvisionNewRequestRequest{
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					ActorId:     id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().ProvisionNewRequest(context.Background(), id1, nil).
					Return(&deeplink.Deeplink{Screen: deeplink.Screen_ORDER_CHEQUEBOOK}, nil)
			},
			want: &alfredPb.ProvisionNewRequestResponse{
				Status:     rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{Screen: deeplink.Screen_ORDER_CHEQUEBOOK},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.ProvisionNewRequest(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProvisionNewRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProvisionNewRequest() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_PollRequestStatus(t *testing.T) {

	type args struct {
		ctx context.Context
		req *alfredPb.PollRequestStatusRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    *alfredPb.PollRequestStatusResponse
		wantErr bool
	}{
		{
			name: "unable to get request processor",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.PollRequestStatusRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(nil, error2)
			},
			want: &alfredPb.PollRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg(error2.Error()),
			},
			wantErr: false,
		},
		{
			name: "unable to get request processor",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.PollRequestStatusRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:      id1,
					ActorId: id1,
				}, nil)
			},
			want: &alfredPb.PollRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg("no request processor defined for the request type: REQUEST_TYPE_UNSPECIFIED"),
			},
			wantErr: false,
		},
		{
			name: "failed to poll request status",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.PollRequestStatusRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				}, nil)
				mocks.requestManager.EXPECT().GetRequestStatus(context.Background(), id1).Return(nil, error3)
			},
			want: &alfredPb.PollRequestStatusResponse{
				Status: rpc.StatusInternalWithDebugMsg(error3.Error()),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.PollRequestStatusRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				}, nil)
				mocks.requestManager.EXPECT().GetRequestStatus(context.Background(), id1).
					Return(&deeplink.Deeplink{Screen: deeplink.Screen_STATUS_POLLING}, nil)
			},
			want: &alfredPb.PollRequestStatusResponse{
				Status:     rpc.StatusOk(),
				NextAction: &deeplink.Deeplink{Screen: deeplink.Screen_STATUS_POLLING},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.PollRequestStatus(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("PollRequestStatus() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("PollRequestStatus() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetRequestStatusDetails(t *testing.T) {
	type args struct {
		ctx context.Context
		req *alfredPb.GetRequestStatusDetailsRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    *alfredPb.GetRequestStatusDetailsResponse
		wantErr bool
	}{
		{
			name: "request id is missing",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{},
			},
			mocks: nil,
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("request id is missing"),
			},
			wantErr: false,
		},
		{
			name: "request id is missing",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{},
			},
			mocks: nil,
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("request id is missing"),
			},
			wantErr: false,
		},
		{
			name: "error in getting service request",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(nil, error1)
			},
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: false,
		},
		{
			name: "unable to get request processor",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{Id: id1, RequestType: alfredPb.RequestType_REQUEST_TYPE_UNSPECIFIED}, nil)
			},
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("no request processor defined for the request type: REQUEST_TYPE_UNSPECIFIED"),
			},
			wantErr: false,
		},
		{
			name: "failed to get updated request details",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{
					Id:          id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				}, nil)
				mocks.requestManager.EXPECT().GetRequestStatusDetails(context.Background(), &alfredPb.ServiceRequest{
					Id:          id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				}).
					Return(nil, error1)
			},
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetRequestStatusDetailsRequest{
					RequestId: id1,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.dao.EXPECT().GetById(context.Background(), id1).Return(&alfredPb.ServiceRequest{Id: id1, RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK}, nil)
				mocks.requestManager.EXPECT().GetRequestStatusDetails(context.Background(), &alfredPb.ServiceRequest{
					Id: id1, RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				}).
					Return(&alfredPb.ServiceRequest{
						Id:          id1,
						ActorId:     id1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					}, nil)
			},
			want: &alfredPb.GetRequestStatusDetailsResponse{
				Status: rpc.StatusOk(),
				ServiceRequest: &alfredPb.ServiceRequest{
					Id:          id1,
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.GetRequestStatusDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRequestStatusDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetRequestStatusDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_GetAllRequestStatusDetails(t *testing.T) {

	type args struct {
		ctx context.Context
		req *alfredPb.GetAllRequestStatusDetailsRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    *alfredPb.GetAllRequestStatusDetailsResponse
		wantErr bool
	}{
		{
			name: "unable to get request processor",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetAllRequestStatusDetailsRequest{
					Filters: &alfredPb.Filters{
						RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_UNSPECIFIED},
						ActorId:      id1,
					},
				},
			},
			mocks: nil,
			want: &alfredPb.GetAllRequestStatusDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg("no request processor defined for the request type: REQUEST_TYPE_UNSPECIFIED"),
			},
			wantErr: false,
		},
		{
			name: "failed to get all request details",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetAllRequestStatusDetailsRequest{
					Filters: &alfredPb.Filters{
						RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
						ActorId:      id1,
					},
					SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().GetAllRequestStatusDetails(context.Background(), &alfredPb.Filters{
					RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
					ActorId:      id1,
				}, nil, uint32(0), alfredPb.SortOrder_SORT_ORDER_DESC).
					Return(nil, nil, error1)
			},
			want: &alfredPb.GetAllRequestStatusDetailsResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.GetAllRequestStatusDetailsRequest{
					Filters: &alfredPb.Filters{
						RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
						ActorId:      id1,
					},
					SortOrder: alfredPb.SortOrder_SORT_ORDER_DESC,
					PageContext: &rpc.PageContextRequest{
						Token:    nil,
						PageSize: 1,
					},
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().GetAllRequestStatusDetails(context.Background(), &alfredPb.Filters{
					RequestTypes: []alfredPb.RequestType{alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK},
					ActorId:      id1,
				}, nil, uint32(1), alfredPb.SortOrder_SORT_ORDER_DESC).
					Return([]*alfredPb.ServiceRequest{
						{
							Id:          id1,
							ActorId:     id1,
							RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
						}}, nil, nil)
			},
			want: &alfredPb.GetAllRequestStatusDetailsResponse{
				Status: rpc.StatusOk(),
				ServiceRequestList: []*alfredPb.ServiceRequest{
					{
						Id:          id1,
						ActorId:     id1,
						RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
					},
				},
				PageContext: nil,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.GetAllRequestStatusDetails(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllRequestStatusDetails() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllRequestStatusDetails() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_UpdateUserInput(t *testing.T) {

	type args struct {
		ctx context.Context
		req *alfredPb.UpdateUserInputRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(args args, mocks *mockServices)
		want    *alfredPb.UpdateUserInputResponse
		wantErr error
	}{
		{
			name: "empty request ID",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{},
			},
			mocks: nil,
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("request id is missing"),
			},
			wantErr: nil,
		},
		{
			name: "get by ID failed",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{
					RequestId: id1,
					UserInput: &alfredPb.UpdateUserInputRequest_ProfileUpdateUserInput{
						ProfileUpdateUserInput: profileUserInput,
					},
				},
			},
			mocks: func(args args, mocks *mockServices) {
				mocks.dao.EXPECT().GetById(gomock.Any(), args.req.GetRequestId()).Return(nil, error2)
			},
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusInternalWithDebugMsg(error2.Error()),
			},
			wantErr: nil,
		},
		{
			name: "no handling for request type found",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{
					RequestId: id1,
					UserInput: &alfredPb.UpdateUserInputRequest_ProfileUpdateUserInput{
						ProfileUpdateUserInput: profileUserInput,
					},
				},
			},
			mocks: func(args args, mocks *mockServices) {
				mocks.dao.EXPECT().GetById(gomock.Any(), args.req.GetRequestId()).Return(&alfredPb.ServiceRequest{
					RequestType: alfredPb.RequestType_REQUEST_TYPE_UNSPECIFIED,
				}, nil)
			},
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("unhandled request type").Error()),
			},
			wantErr: nil,
		},
		{
			name: "unexpected input type",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{
					RequestId: id1,
					UserInput: nil,
				},
			},
			mocks: func(args args, mocks *mockServices) {
				mocks.dao.EXPECT().GetById(gomock.Any(), args.req.GetRequestId()).Return(serviceReq, nil)
			},
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusInternalWithDebugMsg(errors.New("unexpected user input type").Error()),
			},
			wantErr: nil,
		},
		{
			name: "update failed",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{
					RequestId: id1,
					UserInput: &alfredPb.UpdateUserInputRequest_ProfileUpdateUserInput{
						ProfileUpdateUserInput: profileUserInput,
					},
				},
			},
			mocks: func(args args, mocks *mockServices) {
				mocks.dao.EXPECT().GetById(gomock.Any(), args.req.GetRequestId()).Return(serviceReq, nil)
				mocks.dao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), serviceReqWithInput, []alfredPb.ServiceRequestFieldMask{
					alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_USER_INPUT,
				}).Return(nil, error1)
			},
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: nil,
		},
		{
			name: "success",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.UpdateUserInputRequest{
					RequestId: id1,
					UserInput: &alfredPb.UpdateUserInputRequest_ProfileUpdateUserInput{
						ProfileUpdateUserInput: profileUserInput,
					},
				},
			},
			mocks: func(args args, mocks *mockServices) {
				mocks.dao.EXPECT().GetById(gomock.Any(), args.req.GetRequestId()).Return(serviceReq, nil)
				mocks.dao.EXPECT().UpdateServiceRequestByFieldMask(gomock.Any(), serviceReqWithInput, []alfredPb.ServiceRequestFieldMask{
					alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_USER_INPUT,
				}).Return(serviceReqWithInput, nil)
			},
			want: &alfredPb.UpdateUserInputResponse{
				Status: rpc.StatusOk(),
			},
			wantErr: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(tt.args, md)
			}
			got, err := svc.UpdateUserInput(tt.args.ctx, tt.args.req)
			assert.Equal(t, tt.wantErr, err)
			if !proto.Equal(got, tt.want) {
				t.Errorf("ProvisionNewRequest() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_IsEligibleForRequest(t *testing.T) {
	type args struct {
		ctx context.Context
		req *alfredPb.IsEligibleForRequestRequest
	}

	tests := []struct {
		name    string
		args    args
		mocks   func(mocks *mockServices)
		want    *alfredPb.IsEligibleForRequestResponse
		wantErr bool
	}{
		{
			name: "return invalid argument when actor id is missing",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: nil,
			want: &alfredPb.IsEligibleForRequestResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id is missing"),
			},
			wantErr: false,
		},
		{
			name: "return invalid argument when request type is unspecified",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_UNSPECIFIED,
				},
			},
			mocks: nil,
			want: &alfredPb.IsEligibleForRequestResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("request type is missing"),
			},
			wantErr: false,
		},
		{
			name: "return invalid argument when request type is not supported",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType(999), // unsupported request type
				},
			},
			mocks: nil,
			want: &alfredPb.IsEligibleForRequestResponse{
				Status: rpc.StatusInvalidArgumentWithDebugMsg("unsupported request type: 999"),
			},
			wantErr: false,
		},
		{
			name: "return internal error when processor fails to check eligibility",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, error1)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status: rpc.StatusInternalWithDebugMsg(error1.Error()),
			},
			wantErr: false,
		},
		{
			name: "return isEligible true when user is eligible for chequebook request",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(true, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, nil)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status:        rpc.StatusOk(),
				IsEligible:    true,
				FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED,
			},
			wantErr: false,
		},
		{
			name: "return isEligible false when user is not eligible due to full KYC missing",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING, nil)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status:        rpc.StatusOk(),
				IsEligible:    false,
				FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING,
			},
			wantErr: false,
		},
		{
			name: "return isEligible false when user is not eligible due to insufficient balance",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE, nil)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status:        rpc.StatusOk(),
				IsEligible:    false,
				FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE,
			},
			wantErr: false,
		},
		{
			name: "return isEligible false when user is not eligible due to missing signature",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE, nil)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status:        rpc.StatusOk(),
				IsEligible:    false,
				FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE,
			},
			wantErr: false,
		},
		{
			name: "success with tax statement elss request type",
			args: args{
				ctx: context.Background(),
				req: &alfredPb.IsEligibleForRequestRequest{
					ActorId:     id1,
					RequestType: alfredPb.RequestType_REQUEST_TYPE_TAX_STATEMENT_ELSS,
				},
			},
			mocks: func(mocks *mockServices) {
				mocks.requestManager.EXPECT().IsEligibleForRequest(context.Background(), id1).
					Return(true, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, nil)
			},
			want: &alfredPb.IsEligibleForRequestResponse{
				Status:        rpc.StatusOk(),
				IsEligible:    true,
				FailureReason: alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			svc, md := getTestServiceWithMock(t)
			if tt.mocks != nil {
				tt.mocks(md)
			}
			got, err := svc.IsEligibleForRequest(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsEligibleForRequest() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !proto.Equal(got, tt.want) {
				t.Errorf("IsEligibleForRequest() got = %v, want %v", got, tt.want)
			}
		})
	}
}
