//go:generate mockgen -source=./processor_factory.go -destination=../test/mocks/processor_factory.go -package=mocks
package procfactory

import (
	"context"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/pagination"

	alfredPb "github.com/epifi/gamma/api/alfred"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
)

// RequestManager interface implement methods which
//  1. ProvisionNewRequest: resume any ongoing request status, or create a fresh request after running eligibility checks
//  2. GetRequestStatus: provide status or resume request with relevant next action from the request identifier
type RequestManager interface {
	// ProvisionNewRequest method which,
	//	resume any ongoing request, or
	//	create a fresh request after running eligibility checks
	ProvisionNewRequest(ctx context.Context, actorId string, blobData []byte) (*deeplinkPb.Deeplink, error)
	// GetRequestStatus method which,
	//	provide terminal details or resume request with relevant next action from the request identifier
	GetRequestStatus(ctx context.Context, requestId string) (*deeplinkPb.Deeplink, error)
	// GetRequestStatusDetails will get take service request, sync for updates and returns details
	GetRequestStatusDetails(ctx context.Context, sr *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error)
	// GetAllRequestStatusDetails  will fetch records from Alfred Db and run GetRequestStatusDetails if any request status is in non-terminal state.
	GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken,
		pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error)
	// IsEligibleForRequest checks if an actor is eligible for a specific request type
	// Returns eligibility status and failure reason enum if not eligible
	IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error)
}

type Factory struct {
	Chequebook                        Chequebook
	TaxStatementElss                  TaxStatementElss
	DownloadCancelledCheque           DownloadCancelledCheque
	AddressUpdate                     AddressUpdate
	DobUpdate                         DobUpdate
	ChequebookSelection               Selection
	SignUpdate                        SignUpdate
	UssTaxDocumentTimeRangeSelection  UssTaxDocumentTimeRangeSelection
	SendUssTaxDocuments               SendUssTaxDocuments
	UssAccountStatementMonthSelection UssAccountStatementMonthSelection
	UssAccountStatementYearSelection  UssAccountStatementYearSelection
	SendUssMonthlyStatements          SendUssMonthlyStatements
}

func NewFactory(chequebook Chequebook, taxStatementElss TaxStatementElss, downloadCancelledCheque DownloadCancelledCheque,
	addressUpdate AddressUpdate, dobUpdate DobUpdate, selection Selection, signUpdate SignUpdate, ussTaxDocumentTimeRangeSelection UssTaxDocumentTimeRangeSelection,
	sendUssTaxDocuments SendUssTaxDocuments, ussAccountStatementMonthSelection UssAccountStatementMonthSelection, ussAccountStatementYearSelection UssAccountStatementYearSelection,
	sendUssMonthlyStatements SendUssMonthlyStatements) *Factory {
	return &Factory{
		Chequebook:                        chequebook,
		TaxStatementElss:                  taxStatementElss,
		DownloadCancelledCheque:           downloadCancelledCheque,
		AddressUpdate:                     addressUpdate,
		DobUpdate:                         dobUpdate,
		ChequebookSelection:               selection,
		SignUpdate:                        signUpdate,
		UssTaxDocumentTimeRangeSelection:  ussTaxDocumentTimeRangeSelection,
		SendUssTaxDocuments:               sendUssTaxDocuments,
		SendUssMonthlyStatements:          sendUssMonthlyStatements,
		UssAccountStatementYearSelection:  ussAccountStatementYearSelection,
		UssAccountStatementMonthSelection: ussAccountStatementMonthSelection,
	}
}

type Chequebook RequestManager
type TaxStatementElss RequestManager
type DownloadCancelledCheque RequestManager
type AddressUpdate RequestManager
type DobUpdate RequestManager
type Selection RequestManager
type SignUpdate RequestManager
type UssTaxDocumentTimeRangeSelection RequestManager
type SendUssTaxDocuments RequestManager
type SendUssMonthlyStatements RequestManager
type UssAccountStatementYearSelection RequestManager
type UssAccountStatementMonthSelection RequestManager
