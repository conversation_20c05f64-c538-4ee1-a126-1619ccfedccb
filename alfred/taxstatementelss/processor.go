package taxstatementelss

import (
	"context"
	"errors"
	"time"

	"github.com/google/wire"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/gamma/alfred/eligibility"
	"github.com/epifi/gamma/alfred/procfactory"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	alfredPb "github.com/epifi/gamma/api/alfred"
	"github.com/epifi/gamma/api/frontend/deeplink"
	statementPb "github.com/epifi/gamma/api/investment/mutualfund/statement"
	wealthOBPb "github.com/epifi/gamma/api/wealthonboarding"
)

type Processor struct {
	statementServiceClient statementPb.StatementServiceClient
	wobClient              wealthOBPb.WealthOnboardingClient
}

// Months for which we would generate statement for the 'latest' financial year
var monthsForLatestFy = []time.Month{time.August, time.September, time.October, time.November, time.December}

func NewTaxStatementElssProcessor(statementServiceClient statementPb.StatementServiceClient, wobClient wealthOBPb.WealthOnboardingClient) *Processor {
	return &Processor{
		statementServiceClient: statementServiceClient,
		wobClient:              wobClient,
	}
}

var ProviderSet = wire.NewSet(
	NewTaxStatementElssProcessor,

	// bind to interface
	wire.Bind(new(procfactory.TaxStatementElss), new(*Processor)),
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*deeplink.Deeplink, error) {
	year, month, _ := time.Now().Date()
	startDate, endDate := getRelevantFinancialYear(year, month)
	req := &statementPb.GenerateStatementRequest{
		StatementType: statementPb.StatementType_STATEMENT_TYPE_USER_TAX,
		GenerateStatementRequestType: &statementPb.GenerateStatementRequest_GenerateTaxStatementRequest{GenerateTaxStatementRequest: &statementPb.GenerateTaxStatementRequest{
			ActorId:   actorId,
			StartDate: startDate,
			EndDate:   endDate,
		}},
	}
	userdata, err := p.wobClient.GetInvestmentData(ctx, &wealthOBPb.GetInvestmentDataRequest{ActorIds: []string{actorId}})
	if rpcErr := epifigrpc.RPCError(userdata, err); rpcErr != nil {
		logger.Error(ctx, "Error while Getting Investment data for the user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return eligibility.DetailedErrorViewScreen(failedRequestState), nil
	}

	if userdata.GetInvestmentDetailInfo()[actorId].GetEmailId() == "" {
		logger.Info(ctx, "no email id found in user's wealth onboarding info", zap.String(logger.ACTOR_ID_V2, actorId))
		return eligibility.DetailedErrorViewScreen(errorOptionsForNotWealthOnboardedUser), nil
	}

	res, err := p.statementServiceClient.GenerateStatement(ctx, req)
	if rpcErr := epifigrpc.RPCError(res, err); rpcErr != nil {
		logger.Error(ctx, "Error while Generating statement for the user", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(rpcErr))
		return eligibility.DetailedErrorViewScreen(failedRequestState), nil
	}

	successRequestState.Subtitle = "Check your email '" + userdata.GetInvestmentDetailInfo()[actorId].GetEmailId() + "' for the statement"
	return eligibility.DetailedErrorViewScreen(successRequestState), nil
}

func (p *Processor) GetRequestStatus(_ context.Context, _ string) (*deeplink.Deeplink, error) {
	return nil, errors.New("unimplemented method")
}
func (p *Processor) GetRequestStatusDetails(_ context.Context, _ *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	return nil, errors.New("unimplemented method")
}

func (p *Processor) GetAllRequestStatusDetails(_ context.Context, _ *alfredPb.Filters, _ *pagination.PageToken,
	_ uint32, _ alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	return nil, nil, errors.New("unimplemented method")
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

/*
ELSS statement would be generated for that year only for the months defined in 'monthsForLatestFy'
Essentially, we want to generate previous years statement for the months of April-July so that user can use that for
filing taxes. Rest of the time we would return the current financial years statement
Egs:
  - Statement requested on 10th April 2023 would be from 1st April 2022, 31st March 2023
  - Statement requested on 10th October 2023 would be from 1st April 2023, 31st March 2024
*/
func getRelevantFinancialYear(currYear int, month time.Month) (startDate *date.Date, endDate *date.Date) {
	if lo.Contains(monthsForLatestFy, month) {
		startDate = &date.Date{Year: int32(currYear), Month: 4, Day: 1}
		endDate = &date.Date{Year: int32(currYear + 1), Month: 3, Day: 31}
	} else {
		startDate = &date.Date{Year: int32(currYear - 1), Month: 4, Day: 1}
		endDate = &date.Date{Year: int32(currYear), Month: 3, Day: 31}
	}
	return startDate, endDate
}
