package alfred

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	"github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/alfred/procfactory"
	alfredPb "github.com/epifi/gamma/api/alfred"
)

type Service struct {
	alfredPb.UnimplementedAlfredServer
	dao         dao.ServiceRequestsDaoI
	procFactory *procfactory.Factory
}

func NewService(dao dao.ServiceRequestsDaoI, procFactory *procfactory.Factory) *Service {
	return &Service{
		dao:         dao,
		procFactory: procFactory,
	}
}

func (s *Service) getRequestProcessor(requestType alfredPb.RequestType) (procfactory.RequestManager, error) {
	requestProcessorMap := map[alfredPb.RequestType]procfactory.RequestManager{
		alfredPb.RequestType_REQUEST_TYPE_ORDER_CHEQUEBOOK:                       s.procFactory.Chequebook,
		alfredPb.RequestType_REQUEST_TYPE_TAX_STATEMENT_ELSS:                     s.procFactory.TaxStatementElss,
		alfredPb.RequestType_REQUEST_TYPE_DOWNLOAD_DIGITAL_CANCELLED_CHEQUEBOOK:  s.procFactory.DownloadCancelledCheque,
		alfredPb.RequestType_REQUEST_TYPE_CHEQUEBOOK:                             s.procFactory.ChequebookSelection,
		alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE:                     s.procFactory.DobUpdate,
		alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE:   s.procFactory.AddressUpdate,
		alfredPb.RequestType_REQUEST_TYPE_SAVINGS_ACC_SIGNATURE_CHANGE:           s.procFactory.SignUpdate,
		alfredPb.RequestType_REQUEST_TYPE_USS_TAX_DOCUMENTS_TIME_RANGE_SELECTION: s.procFactory.UssTaxDocumentTimeRangeSelection,
		alfredPb.RequestType_REQUEST_TYPE_SEND_USS_TAX_DOCUMENTS:                 s.procFactory.SendUssTaxDocuments,
		alfredPb.RequestType_REQUEST_TYPE_SEND_USS_MONTHLY_STATEMENTS:            s.procFactory.SendUssMonthlyStatements,
		alfredPb.RequestType_REQUEST_TYPE_USS_DOCUMENTS_MONTH_SELECTION:          s.procFactory.UssAccountStatementMonthSelection,
		alfredPb.RequestType_REQUEST_TYPE_USS_DOCUMENTS_CALENDER_YEAR_SELECTION:  s.procFactory.UssAccountStatementYearSelection,
	}

	res, ok := requestProcessorMap[requestType]
	if !ok {
		return nil, fmt.Errorf("no request processor defined for the request type: %v", requestType.String())
	}
	return res, nil
}

func (s *Service) ProvisionNewRequest(ctx context.Context, req *alfredPb.ProvisionNewRequestRequest) (*alfredPb.ProvisionNewRequestResponse, error) {
	errRes := func(status *rpc.Status) (*alfredPb.ProvisionNewRequestResponse, error) {
		return &alfredPb.ProvisionNewRequestResponse{
			Status: status,
		}, nil
	}
	requestType := req.GetRequestType()
	requestProcessor, err := s.getRequestProcessor(requestType)
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to get request processor: %v", requestType.String()))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	nextAction, err := requestProcessor.ProvisionNewRequest(ctx, req.GetActorId(), req.GetBlob())
	if err != nil {
		logger.Error(ctx, "failed to get initialise new request", zap.Error(err))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	return &alfredPb.ProvisionNewRequestResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) PollRequestStatus(ctx context.Context, req *alfredPb.PollRequestStatusRequest) (*alfredPb.PollRequestStatusResponse, error) {
	start := time.Now()
	errRes := func(status *rpc.Status) (*alfredPb.PollRequestStatusResponse, error) {
		return &alfredPb.PollRequestStatusResponse{
			Status: status,
		}, nil
	}
	requestId := req.GetRequestId()
	serviceRequest, err := s.dao.GetById(ctx, requestId)
	if err != nil {
		logger.Error(ctx, "failed to find service request", zap.String(logger.REQUEST_ID, requestId))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	requestProcessor, err := s.getRequestProcessor(serviceRequest.GetRequestType())
	if err != nil {
		logger.Error(ctx, "error processing request", zap.Error(err), zap.String(logger.REQUEST_ID, requestId))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	nextAction, err := requestProcessor.GetRequestStatus(ctx, req.GetRequestId())
	if err != nil {
		logger.Error(ctx, "failed to get initialise new request", zap.Error(err), zap.String(logger.REQUEST_ID, requestId))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	logger.Info(ctx, fmt.Sprintf("got next action from PollRequestStatus: %v", nextAction.String()))

	// add sleep for 1.5 second
	// since context switch from polling screen to order details screen was happening very fast
	// because it was a dao call, and we can't remove that screen because that was generic screen
	// so it was not giving good impression so we added delay of 1.5 second,
	//
	time.Sleep(1500*time.Millisecond - time.Since(start))
	return &alfredPb.PollRequestStatusResponse{
		Status:     rpc.StatusOk(),
		NextAction: nextAction,
	}, nil
}

func (s *Service) GetRequestStatusDetails(ctx context.Context, req *alfredPb.GetRequestStatusDetailsRequest) (*alfredPb.GetRequestStatusDetailsResponse, error) {
	errRes := func(status *rpc.Status) (*alfredPb.GetRequestStatusDetailsResponse, error) {
		return &alfredPb.GetRequestStatusDetailsResponse{
			Status: status,
		}, nil
	}
	// check for mandatory params
	if req.GetRequestId() == "" {
		return errRes(rpc.StatusInvalidArgumentWithDebugMsg("request id is missing"))
	}
	requestId := req.GetRequestId()
	// fetch the service request by requestId
	serviceRequest, err := s.dao.GetById(ctx, requestId)
	if err != nil {
		logger.Error(ctx, "failed to find service request", zap.String(logger.REQUEST_ID, requestId))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}

	// fetch the request processor
	requestProcessor, err := s.getRequestProcessor(serviceRequest.GetRequestType())
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to get request processor: %v", serviceRequest.GetRequestType()))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	// get the updated service request
	sr, err := requestProcessor.GetRequestStatusDetails(ctx, serviceRequest)
	if err != nil {
		logger.Error(ctx, "failed to get updated service request", zap.Error(err), zap.String(logger.REQUEST_ID, sr.GetId()))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	return &alfredPb.GetRequestStatusDetailsResponse{
		Status:         rpc.StatusOk(),
		ServiceRequest: sr,
	}, nil
}

func (s *Service) GetAllRequestStatusDetails(ctx context.Context, req *alfredPb.GetAllRequestStatusDetailsRequest) (
	*alfredPb.GetAllRequestStatusDetailsResponse, error) {
	errRes := func(status *rpc.Status) (*alfredPb.GetAllRequestStatusDetailsResponse, error) {
		return &alfredPb.GetAllRequestStatusDetailsResponse{
			Status: status,
		}, nil
	}
	// check for mandatory params
	if req.GetFilters().GetActorId() == "" || len(req.GetFilters().GetRequestTypes()) == 0 {
		return errRes(rpc.StatusInvalidArgumentWithDebugMsg("actor id or request type is missing"))
	}
	// fetch the page token from page context
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Any("filters", req.GetFilters()), zap.Error(err))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	// fetch the request processor for the request type
	requestProcessor, err := s.getRequestProcessor(req.GetFilters().GetRequestTypes()[0])
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to get request processor: %v", req.GetFilters().GetRequestTypes()[0].String()))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	// fetch all updated service requests
	serviceRequests, pageContextResp, err := requestProcessor.GetAllRequestStatusDetails(ctx, req.GetFilters(), pageToken, req.GetPageContext().GetPageSize(), req.GetSortOrder())
	if err != nil {
		logger.Error(ctx, "failed to get updated service requests", zap.Error(err))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error()))
	}
	logger.Info(ctx, "service requests fetched successfully", zap.Int32("service requests count", int32(len(serviceRequests))),
		zap.Any("Page Context", pageContextResp))
	return &alfredPb.GetAllRequestStatusDetailsResponse{
		Status:             rpc.StatusOk(),
		ServiceRequestList: serviceRequests,
		PageContext:        pageContextResp,
	}, nil
}

func (s *Service) UpdateUserInput(ctx context.Context, req *alfredPb.UpdateUserInputRequest) (*alfredPb.UpdateUserInputResponse, error) {
	errRes := func(status *rpc.Status) *alfredPb.UpdateUserInputResponse {
		return &alfredPb.UpdateUserInputResponse{
			Status: status,
		}
	}
	requestId := req.GetRequestId()
	// check for mandatory params
	if requestId == "" {
		return errRes(rpc.StatusInvalidArgumentWithDebugMsg("request id is missing")), nil
	}

	serviceRequest, err := s.dao.GetById(ctx, requestId)
	if err != nil {
		logger.Error(ctx, "failed to find service request", zap.String(logger.REQUEST_ID, requestId))
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error())), nil
	}

	if err = s.populateUserInput(ctx, serviceRequest, req); err != nil {
		return errRes(rpc.StatusInternalWithDebugMsg(err.Error())), nil
	}

	return &alfredPb.UpdateUserInputResponse{
		Status: rpc.StatusOk(),
	}, nil
}

func (s *Service) SoftDeleteServiceRequest(ctx context.Context, req *alfredPb.SoftDeleteServiceReqRequest) (*alfredPb.SoftDeleteServiceReqReqResponse, error) {
	var serviceRequest *alfredPb.ServiceRequest
	switch {
	case req.GetServiceReqId() != "":
		var getErr error
		serviceRequest, getErr = s.dao.GetById(ctx, req.GetServiceReqId())
		if getErr != nil {
			if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
				return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg(getErr.Error())}, nil
			}
			logger.Error(ctx, "failed to get service request", zap.Error(getErr), zap.String(logger.ID, req.GetServiceReqId()))
			return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusInternalWithDebugMsg(getErr.Error())}, nil
		}
	case req.GetActorId() != "":
		serviceRequests, getErr := s.dao.GetByActorIdRequestType(ctx, req.GetActorId(), req.GetRequestType())
		if getErr != nil {
			if errors.Is(getErr, epifierrors.ErrRecordNotFound) {
				return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg(getErr.Error())}, nil
			}
			logger.Error(ctx, "failed to get service request", zap.Error(getErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusInternalWithDebugMsg(getErr.Error())}, nil
		}
		// since we have a db constraint on actor_id, service_request, deleted_at - there can be only one service request for an actor
		serviceRequest = serviceRequests[0]
	default:
		return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusInvalidArgument()}, nil
	}

	serviceRequest.DeletedAtUnix = time.Now().Unix()
	_, updErr := s.dao.UpdateServiceRequestByFieldMask(ctx, serviceRequest, []alfredPb.ServiceRequestFieldMask{
		alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_DELETED_AT_UNIX,
	})
	if updErr != nil {
		logger.Error(ctx, "failed to update service request", zap.Error(updErr), zap.String(logger.ACTOR_ID_V2, serviceRequest.GetActorId()), zap.String(logger.ID, serviceRequest.GetId()))
		return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusInternalWithDebugMsg(updErr.Error())}, nil
	}

	return &alfredPb.SoftDeleteServiceReqReqResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) populateUserInput(ctx context.Context, serviceReq *alfredPb.ServiceRequest, req *alfredPb.UpdateUserInputRequest) error {
	switch serviceReq.GetRequestType() {
	case alfredPb.RequestType_REQUEST_TYPE_PROFILE_DOB_CHANGE, alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE:
		userInput, ok := req.GetUserInput().(*alfredPb.UpdateUserInputRequest_ProfileUpdateUserInput)
		if !ok {
			logger.Error(ctx, "unexpected user input type")
			return errors.New("unexpected user input type")
		}
		serviceReq.Details.UserInput = &alfredPb.Details_ProfileUpdateUserInput{
			ProfileUpdateUserInput: userInput.ProfileUpdateUserInput,
		}
	default:
		logger.Error(ctx, "unhandled request type")
		return errors.New("unhandled request type")
	}

	_, err := s.dao.UpdateServiceRequestByFieldMask(ctx, serviceReq, []alfredPb.ServiceRequestFieldMask{
		alfredPb.ServiceRequestFieldMask_SERVICE_REQUEST_FIELD_MASK_USER_INPUT,
	})
	if err != nil {
		logger.Error(ctx, "failed to find service request", zap.String(logger.REQUEST_ID, serviceReq.GetId()))
		return err
	}
	return nil
}

func (s *Service) IsEligibleForRequest(ctx context.Context, req *alfredPb.IsEligibleForRequestRequest) (*alfredPb.IsEligibleForRequestResponse, error) {

	// check for mandatory params
	if req.GetActorId() == "" {
		return &alfredPb.IsEligibleForRequestResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("actor id is missing"),
		}, nil
	}
	if req.GetRequestType() == alfredPb.RequestType_REQUEST_TYPE_UNSPECIFIED {
		return &alfredPb.IsEligibleForRequestResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("request type is missing"),
		}, nil
	}

	// get the request processor for the given request type
	requestProcessor, err := s.getRequestProcessor(req.GetRequestType())
	if err != nil {
		logger.Error(ctx, fmt.Sprintf("unable to get request processor: %v", req.GetRequestType().String()))
		// Request type not found/supported is an invalid argument
		return &alfredPb.IsEligibleForRequestResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg(fmt.Sprintf("unsupported request type: %v", req.GetRequestType().String())),
		}, nil
	}

	// check eligibility using the processor
	isEligible, failureReason, err := requestProcessor.IsEligibleForRequest(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to check eligibility", zap.Error(err), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.REQUEST_TYPE, req.GetRequestType().String()))
		return &alfredPb.IsEligibleForRequestResponse{
			Status: rpc.StatusInternalWithDebugMsg(err.Error()),
		}, nil
	}

	return &alfredPb.IsEligibleForRequestResponse{
		Status:        rpc.StatusOk(),
		IsEligible:    isEligible,
		FailureReason: failureReason,
	}, nil
}
