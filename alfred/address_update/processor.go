package addrsupdate

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/google/wire"
	"go.uber.org/zap"

	// nolint: goimports
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/async/waitgroup"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storageV2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/alfred/config/genconf"
	"github.com/epifi/gamma/alfred/dao"
	"github.com/epifi/gamma/alfred/procfactory"
	"github.com/epifi/gamma/alfred/profile_update/orchestrator"
	"github.com/epifi/gamma/alfred/profile_update/orchestrator/stageproc"
	profileupdate "github.com/epifi/gamma/alfred/profile_update/validator"
	alfredPb "github.com/epifi/gamma/api/alfred"
	dlPb "github.com/epifi/gamma/api/frontend/deeplink"
)

type Processor struct {
	alfredDao    dao.ServiceRequestsDaoI
	genConf      *genconf.Config
	validator    profileupdate.Validator
	idgen        idgen.IdGenerator
	orchestrator orchestrator.Orchestrator
}

func NewAddressUpdateProcessor(alfredDao dao.ServiceRequestsDaoI, genConf *genconf.Config, validator profileupdate.Validator,
	idgen idgen.IdGenerator, orchestrator orchestrator.Orchestrator) *Processor {
	return &Processor{
		alfredDao:    alfredDao,
		genConf:      genConf,
		validator:    validator,
		idgen:        idgen,
		orchestrator: orchestrator,
	}
}

var (
	// build check for ensuring processor implements interface
	_ procfactory.RequestManager = &Processor{}
)

var ProviderSet = wire.NewSet(
	NewAddressUpdateProcessor,
	wire.Bind(new(procfactory.AddressUpdate), new(*Processor)),
)

func (p *Processor) ProvisionNewRequest(ctx context.Context, actorId string, _ []byte) (*dlPb.Deeplink, error) {
	var (
		inProgress    bool
		failureReason alfredPb.ProfileUpdateMetadata_FailureReason
	)
	serviceReq, err := p.getLatestInProgressRecord(ctx, actorId)
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return nil, err
	}
	if errors.Is(err, epifierrors.ErrRecordNotFound) {
		if inProgress, err = p.validator.IsProfileUpdateInProgress(ctx, actorId); err != nil {
			return nil, err
		}
		if inProgress {
			logger.Info(ctx, "another profile update in progress")
			return stageproc.GetDeeplinkByFailureReason(ctx, alfredPb.ProfileUpdateMetadata_FAILURE_REASON_ANOTHER_REQUEST_IN_PROGRESS), nil
		}

		failureReason, err = p.validator.CheckUpdateEligibility(ctx, actorId, []alfredPb.ProfileField{
			alfredPb.ProfileField_PROFILE_FIELD_COMMUNICATION_ADDRESS,
		})
		if err != nil {
			return nil, err
		}
		if failureReason != alfredPb.ProfileUpdateMetadata_FAILURE_REASON_UNSPECIFIED {
			logger.Info(ctx, "failed eligibility check", zap.String(logger.REASON, failureReason.String()))
			return stageproc.GetDeeplinkByFailureReason(ctx, failureReason), nil
		}

		serviceReq, err = p.createAlfredRequest(ctx, actorId)
		if err != nil {
			return nil, err
		}
	}

	return p.orchestrator.Orchestrate(ctx, serviceReq.GetId())
}

func (p *Processor) GetRequestStatus(ctx context.Context, requestId string) (*dlPb.Deeplink, error) {
	return p.orchestrator.Orchestrate(ctx, requestId)
}

func (p *Processor) GetRequestStatusDetails(ctx context.Context, sr *alfredPb.ServiceRequest) (*alfredPb.ServiceRequest, error) {
	_, err := p.orchestrator.Orchestrate(ctx, sr.GetId())
	if err != nil {
		logger.Error(ctx, "error in syncing state", zap.Error(err))
		return nil, err
	}
	return p.alfredDao.GetById(ctx, sr.GetId())
}
func (p *Processor) GetAllRequestStatusDetails(ctx context.Context, filters *alfredPb.Filters, pageToken *pagination.PageToken, pageSize uint32, sortOrder alfredPb.SortOrder) ([]*alfredPb.ServiceRequest, *rpc.PageContextResponse, error) {
	if filters.GetRequestTypes() == nil || len(filters.GetRequestTypes()) == 0 {
		return nil, nil, fmt.Errorf("empty request types %v", epifierrors.ErrInvalidArgument)
	}
	serviceRequests, pageContextResp, err := p.alfredDao.GetPaginatedRequestDetails(ctx, filters.GetActorId(), filters.GetRequestTypes()[0], filters.GetStatusList(), pageToken, pageSize, sortOrder)
	if err != nil {
		logger.Error(ctx, "error in getting the paginated service requests", zap.Error(err))
		return nil, nil, err
	}
	var serviceReqList []*alfredPb.ServiceRequest
	// Process service request in parallel
	var wg sync.WaitGroup
	for _, sr := range serviceRequests {
		goSr := sr
		wg.Add(1)
		goroutine.Run(ctx, time.Second*5, func(ctx context.Context) {
			defer wg.Done()
			// else update the service requests according workflow request
			updatedSr, err1 := p.GetRequestStatusDetails(ctx, goSr)
			if err1 != nil {
				serviceReqList = append(serviceReqList, goSr)
				return
			}
			// Empty user input to avoid showing sensitive info in sherlock
			updatedSr.GetDetails().UserInput = nil
			serviceReqList = append(serviceReqList, updatedSr)
		})
	}
	waitgroup.SafeWaitWithDefaultTimeout(&wg)
	// sorting here, because doing this async wouldn't ensure chronological order,
	// as items can be appended in list in any order.
	sort.SliceStable(serviceReqList, func(i, j int) bool {
		// sorting in decreasing order of createdAt date
		return serviceReqList[i].GetCreatedAt().AsTime().After(serviceReqList[j].GetCreatedAt().AsTime())
	})
	return serviceReqList, pageContextResp, nil
}

func (p *Processor) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfredPb.EligibilityFailureReason, error) {
	logger.Info(ctx, "eligibility checker is not implemented for this request type", zap.String(logger.ACTOR_ID_V2, actorId))
	return false, alfredPb.EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED, errors.New("eligibility checker is not allowed for this request type")
}

func (p *Processor) getLatestInProgressRecord(ctx context.Context, actorId string) (*alfredPb.ServiceRequest, error) {
	serviceReqs, err := p.alfredDao.GetByActorIdRequestType(ctx, actorId, alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE)
	if err != nil {
		if storageV2.IsRecordNotFoundError(err) {
			logger.Info(ctx, fmt.Sprintf("no %v request found", alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE.String()))
			return nil, epifierrors.ErrRecordNotFound
		}
		logger.Error(ctx, fmt.Sprintf("error in getting service request of type %v", alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE.String()))
		return nil, err
	}

	latestServiceReq := serviceReqs[0]
	if latestServiceReq.GetStatus().IsTerminal() {
		logger.Info(ctx, "last request in terminal state")
		if err = p.alfredDao.DeleteByActorIdRequestType(ctx, actorId, alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE); err != nil {
			logger.Error(ctx, "error in removing terminal requests", zap.Error(err))
			return nil, err
		}
		return nil, epifierrors.ErrRecordNotFound
	}
	return latestServiceReq, nil
}

func (p *Processor) createAlfredRequest(ctx context.Context, actorId string) (*alfredPb.ServiceRequest, error) {
	logger.Info(ctx, "creating new address update request")
	serviceReq, err := p.alfredDao.Create(ctx, &alfredPb.ServiceRequest{
		RequestType: alfredPb.RequestType_REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE,
		ActorId:     actorId,
		Status:      alfredPb.Status_STATUS_IN_PROGRESS,
		Id:          p.idgen.GetInAlphaNumeric(idgen.CommunicationAddressUpdate),
		Details: &alfredPb.Details{
			UserInput: &alfredPb.Details_ProfileUpdateUserInput{},
			Metadata: &alfredPb.Details_ProfileUpdateMetadata{
				ProfileUpdateMetadata: &alfredPb.ProfileUpdateMetadata{},
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error in creating service request", zap.Error(err))
		return nil, err
	}
	return serviceReq, nil
}
