// Code generated by MockGen. DO NOT EDIT.
// Source: ./processor_factory.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	rpc "github.com/epifi/be-common/api/rpc"
	pagination "github.com/epifi/be-common/pkg/pagination"
	alfred "github.com/epifi/gamma/api/alfred"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	gomock "github.com/golang/mock/gomock"
)

// MockRequestManager is a mock of RequestManager interface.
type MockRequestManager struct {
	ctrl     *gomock.Controller
	recorder *MockRequestManagerMockRecorder
}

// MockRequestManagerMockRecorder is the mock recorder for MockRequestManager.
type MockRequestManagerMockRecorder struct {
	mock *MockRequestManager
}

// NewMockRequestManager creates a new mock instance.
func NewMockRequestManager(ctrl *gomock.Controller) *MockRequestManager {
	mock := &MockRequestManager{ctrl: ctrl}
	mock.recorder = &MockRequestManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRequestManager) EXPECT() *MockRequestManagerMockRecorder {
	return m.recorder
}

// GetAllRequestStatusDetails mocks base method.
func (m *MockRequestManager) GetAllRequestStatusDetails(ctx context.Context, filters *alfred.Filters, pageToken *pagination.PageToken, pageSize uint32, sortOrder alfred.SortOrder) ([]*alfred.ServiceRequest, *rpc.PageContextResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRequestStatusDetails", ctx, filters, pageToken, pageSize, sortOrder)
	ret0, _ := ret[0].([]*alfred.ServiceRequest)
	ret1, _ := ret[1].(*rpc.PageContextResponse)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetAllRequestStatusDetails indicates an expected call of GetAllRequestStatusDetails.
func (mr *MockRequestManagerMockRecorder) GetAllRequestStatusDetails(ctx, filters, pageToken, pageSize, sortOrder interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRequestStatusDetails", reflect.TypeOf((*MockRequestManager)(nil).GetAllRequestStatusDetails), ctx, filters, pageToken, pageSize, sortOrder)
}

// GetRequestStatus mocks base method.
func (m *MockRequestManager) GetRequestStatus(ctx context.Context, requestId string) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRequestStatus", ctx, requestId)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatus indicates an expected call of GetRequestStatus.
func (mr *MockRequestManagerMockRecorder) GetRequestStatus(ctx, requestId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatus", reflect.TypeOf((*MockRequestManager)(nil).GetRequestStatus), ctx, requestId)
}

// GetRequestStatusDetails mocks base method.
func (m *MockRequestManager) GetRequestStatusDetails(ctx context.Context, sr *alfred.ServiceRequest) (*alfred.ServiceRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRequestStatusDetails", ctx, sr)
	ret0, _ := ret[0].(*alfred.ServiceRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatusDetails indicates an expected call of GetRequestStatusDetails.
func (mr *MockRequestManagerMockRecorder) GetRequestStatusDetails(ctx, sr interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatusDetails", reflect.TypeOf((*MockRequestManager)(nil).GetRequestStatusDetails), ctx, sr)
}

// IsEligibleForRequest mocks base method.
func (m *MockRequestManager) IsEligibleForRequest(ctx context.Context, actorId string) (bool, alfred.EligibilityFailureReason, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEligibleForRequest", ctx, actorId)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(alfred.EligibilityFailureReason)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// IsEligibleForRequest indicates an expected call of IsEligibleForRequest.
func (mr *MockRequestManagerMockRecorder) IsEligibleForRequest(ctx, actorId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEligibleForRequest", reflect.TypeOf((*MockRequestManager)(nil).IsEligibleForRequest), ctx, actorId)
}

// ProvisionNewRequest mocks base method.
func (m *MockRequestManager) ProvisionNewRequest(ctx context.Context, actorId string, blobData []byte) (*deeplink.Deeplink, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProvisionNewRequest", ctx, actorId, blobData)
	ret0, _ := ret[0].(*deeplink.Deeplink)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvisionNewRequest indicates an expected call of ProvisionNewRequest.
func (mr *MockRequestManagerMockRecorder) ProvisionNewRequest(ctx, actorId, blobData interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionNewRequest", reflect.TypeOf((*MockRequestManager)(nil).ProvisionNewRequest), ctx, actorId, blobData)
}
