//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.
//
//go:generate wire
package wire

import (
	"context"

	alfredPb "github.com/epifi/gamma/api/alfred"
	variablesPb "github.com/epifi/gamma/api/analyser/variables"
	commsPb "github.com/epifi/gamma/api/comms"
	aaAnalyticsPb "github.com/epifi/gamma/api/connected_account/analytics"
	federalpb "github.com/epifi/gamma/api/cx/federal"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	fireflyV2Pb "github.com/epifi/gamma/api/firefly/v2"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/risk/profile"
	vgSmsPb "github.com/epifi/gamma/api/vendorgateway/sms"
	"github.com/epifi/gamma/vendornotification/auth"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
	vnChatbotFreschat "github.com/epifi/gamma/vendornotification/cx/chatbot/freshchat"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/nugget"
	"github.com/epifi/gamma/vendornotification/cx/federal"
	"github.com/epifi/gamma/vendornotification/interceptor/ratelimiter"
	ratelimiterNamespace "github.com/epifi/gamma/vendornotification/interceptor/ratelimiter/namespace"
	webhookAreaProcessor "github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/area"
	webhookLoansUsecaseProcessor "github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/usecase/loans/dropoff"
	"github.com/epifi/gamma/vendornotification/pushnotification"
	"github.com/epifi/gamma/vendornotification/reward"
	airtel2 "github.com/epifi/gamma/vendornotification/whatsapp/airtel"

	"github.com/epifi/gamma/vendornotification/sms/airtel"

	vgScienapticPb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	"github.com/epifi/gamma/featurestore/scienaptic"
	"github.com/epifi/gamma/vendornotification/paymentgateway/razorpay"
	"github.com/epifi/gamma/vendornotification/sms/netcore"

	awsV2 "github.com/aws/aws-sdk-go-v2/aws"
	cmap "github.com/orcaman/concurrent-map"
	"go.uber.org/zap"

	callIvrPb "github.com/epifi/gamma/api/cx/call_ivr"
	"github.com/epifi/gamma/vendornotification/config"

	config2 "github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/videocall/videosdk"

	"github.com/google/wire"

	"github.com/epifi/be-common/pkg/aws/v2/kinesis"
	sqsPkg "github.com/epifi/be-common/pkg/aws/v2/sqs"
	pkgWire "github.com/epifi/be-common/pkg/aws/v2/wire"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc/interceptors/ratelimit/keygen"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	ratelimiterWire "github.com/epifi/be-common/pkg/ratelimiter/wire"
	syncresp "github.com/epifi/be-common/pkg/syncwrapper/response"

	questSdkGenConf "github.com/epifi/be-common/quest/sdk/config/genconf"

	celestialPb "github.com/epifi/be-common/api/celestial"
	epifitemporalfaas "github.com/epifi/be-common/pkg/epifitemporal/faas"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"

	balancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/analyser/investment"
	txnAggregatesPb "github.com/epifi/gamma/api/analyser/txnaggregates"
	authPb "github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/card/provisioning"
	evrPb "github.com/epifi/gamma/api/casper/external_vendor_redemption"
	categorizerPb "github.com/epifi/gamma/api/categorizer"
	"github.com/epifi/gamma/api/collection"
	caPb "github.com/epifi/gamma/api/connected_account"
	connectedaccountPb "github.com/epifi/gamma/api/connected_account"
	credit_report "github.com/epifi/gamma/api/creditreportv2"
	callRoutingPb "github.com/epifi/gamma/api/cx/call_routing"
	cxLiveChatFbkPb "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback"
	cxChatbotWorkflowPb "github.com/epifi/gamma/api/cx/chat/bot/workflow"
	dPb "github.com/epifi/gamma/api/cx/dispute"
	sprinklrPb "github.com/epifi/gamma/api/cx/sprinklr"
	depositPb "github.com/epifi/gamma/api/deposit"
	fireflyPb "github.com/epifi/gamma/api/firefly"
	ffAccPb "github.com/epifi/gamma/api/firefly/accounting"
	ffBillingPb "github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/inapphelp/issue_reporting"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	userDeclarationPb "github.com/epifi/gamma/api/insights/user_declaration"
	catalogPb "github.com/epifi/gamma/api/investment/mutualfund/catalog"
	merchantPb "github.com/epifi/gamma/api/merchant"
	orderPb "github.com/epifi/gamma/api/order"
	panPb "github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	paymentinstrumentPb "github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/preapprovedloan"
	questManagerPb "github.com/epifi/gamma/api/quest/manager"
	rewardsPb "github.com/epifi/gamma/api/rewards"
	salaryPb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	savingsPb "github.com/epifi/gamma/api/savings"
	segmentPb "github.com/epifi/gamma/api/segment"
	upiOnbPb "github.com/epifi/gamma/api/upi/onboarding"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	usStockscatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	vgAaPb "github.com/epifi/gamma/api/vendorgateway/aa"
	fennelVgPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	creditCardVgPb "github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	"github.com/epifi/gamma/featurestore"
	fennel2 "github.com/epifi/gamma/featurestore/fennel"
	helper2 "github.com/epifi/gamma/firefly/helper"
	"github.com/epifi/gamma/pkg/connectedaccount"
	insigthsDataFetcher "github.com/epifi/gamma/pkg/dmf/datafetcher"
	"github.com/epifi/gamma/pkg/dmf/txnaggregates"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	"github.com/epifi/gamma/vendornotification/aa"
	ignoisAa "github.com/epifi/gamma/vendornotification/aa/ignosis"
	"github.com/epifi/gamma/vendornotification/aml/tss"
	shipwayCard "github.com/epifi/gamma/vendornotification/card/shipway"
	unsubscribeVn "github.com/epifi/gamma/vendornotification/comms/unsubscribe"
	"github.com/epifi/gamma/vendornotification/config/genconf"
	"github.com/epifi/gamma/vendornotification/creditcard/m2p"
	ccm2p "github.com/epifi/gamma/vendornotification/creditcard/m2p"
	"github.com/epifi/gamma/vendornotification/creditcard/paisabazaar"
	"github.com/epifi/gamma/vendornotification/creditcard/saven"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/auth/senseforth"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/helper"
	liveChatFlbkSenseforth "github.com/epifi/gamma/vendornotification/cx/chatbot/livechatfallback/senseforth"
	botWorkflow "github.com/epifi/gamma/vendornotification/cx/chatbot/workflow/senseforth"
	"github.com/epifi/gamma/vendornotification/cx/freshchat"
	"github.com/epifi/gamma/vendornotification/cx/ozonetel"
	"github.com/epifi/gamma/vendornotification/cx/sprinklr"
	"github.com/epifi/gamma/vendornotification/email"
	epanKarza "github.com/epifi/gamma/vendornotification/epan/karza"
	ficoinsAccVnPb "github.com/epifi/gamma/vendornotification/fi_coins_accounting"
	"github.com/epifi/gamma/vendornotification/healthinsurance/riskcovry"
	"github.com/epifi/gamma/vendornotification/investment/smallcase"
	axisEkyc "github.com/epifi/gamma/vendornotification/kyc/axis"
	vnIdfc "github.com/epifi/gamma/vendornotification/kyc/idfc"
	"github.com/epifi/gamma/vendornotification/lending/bre/inhouse"
	credgenicsVn "github.com/epifi/gamma/vendornotification/lending/credgenics"
	AbflVn "github.com/epifi/gamma/vendornotification/lending/loans/abfl"
	lendingFederal "github.com/epifi/gamma/vendornotification/lending/loans/federal"
	fiftyfinVn "github.com/epifi/gamma/vendornotification/lending/loans/fiftyfin"
	idfcVnPb "github.com/epifi/gamma/vendornotification/lending/loans/idfc"
	moneyviewVn "github.com/epifi/gamma/vendornotification/lending/loans/moneyview"
	SetuVn "github.com/epifi/gamma/vendornotification/lending/setu"
	"github.com/epifi/gamma/vendornotification/liveness/karza"
	"github.com/epifi/gamma/vendornotification/notifications/moengage"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/factory"
	moengageSpendsInsights "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/insights/spends"
	moengageStocks "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/stocks"
	offersVnPb "github.com/epifi/gamma/vendornotification/offers/externalredemptions"
	axisAccounts "github.com/epifi/gamma/vendornotification/openbanking/accounts/axis"
	federalAccounts "github.com/epifi/gamma/vendornotification/openbanking/accounts/federal"
	federalAuth "github.com/epifi/gamma/vendornotification/openbanking/auth/federal"
	federalCard "github.com/epifi/gamma/vendornotification/openbanking/card/federal"
	federalDeposit "github.com/epifi/gamma/vendornotification/openbanking/deposit/federal"
	federalDmp "github.com/epifi/gamma/vendornotification/openbanking/dispute"
	federalKycTypeChange "github.com/epifi/gamma/vendornotification/openbanking/kyctypechange/federal"
	axisPayment "github.com/epifi/gamma/vendornotification/openbanking/payment/axis"
	federalPayment "github.com/epifi/gamma/vendornotification/openbanking/payment/federal"
	federalRecurringPayment "github.com/epifi/gamma/vendornotification/openbanking/recurringpayment/federal"
	rpPb "github.com/epifi/gamma/vendornotification/openbanking/recurringpayment/federal"
	shippingAddressUpdate "github.com/epifi/gamma/vendornotification/openbanking/shipping_preference/federal"
	gupshupRcs "github.com/epifi/gamma/vendornotification/rcs/gupshup"
	"github.com/epifi/gamma/vendornotification/sms/acl"
	"github.com/epifi/gamma/vendornotification/sms/kaleyra"
	vnTypes "github.com/epifi/gamma/vendornotification/types"
	karza2 "github.com/epifi/gamma/vendornotification/vkyc/karza"
	aclWa "github.com/epifi/gamma/vendornotification/whatsapp/acl"
	gupshupWa "github.com/epifi/gamma/vendornotification/whatsapp/gupshup"
)

func syncRespHandlerProvider(conf *config.Config) *syncresp.SyncRespHandler {
	return conf.SyncRespHandler
}

func upiListVaePublisherProvider(ctx context.Context, conf *config.Config, awsConf awsV2.Config) federalPayment.UPIListVaePublisher {
	return pkgWire.InitializeExtendedPublisher(ctx, awsConf,
		queue.NewDefaultMessage(),
		sqsPkg.QueueName(conf.UPIListVaePublisher.GetQueueName()),
		conf.UPIListVaePublisher.GetBucketName(),
		sqsPkg.ServiceName(cfg.VENDOR_NOTIFI_SERVICE))
}

func syncRespCallbackHandlerChannelsProvider() cmap.ConcurrentMap {
	return cmap.New()
}

func InitializeFederalPayService(
	ctx context.Context,
	conf *config.Config,
	awsConf awsV2.Config,
	updateTransactionEventsPublisher federalPayment.UpdateTransactionEventsPublisher,
	inboundTxnPublisher federalPayment.InboundTxnPublisher,
	upiReqAuthEventPublisher federalPayment.UPIReqAuthEventPublisher,
	upiRespPayEventPublisher federalPayment.UPIRespPayEventPublisher,
	upiRespMandateEventPublisher federalPayment.UPIRespMandateEventPublisher,
	upiReqTxnConfirmationPublisher federalPayment.UPIReqTxnConfirmationEventPublisher,
	upiReqValAddressPublisher federalPayment.UPIReqValAddressEventPublisher,
	upiListPspKeysPublisher federalPayment.UPIListPspKeysEventPublisher,
	orderClient orderPb.OrderServiceClient,
	upiReqAuthMandateEventPublisher federalPayment.UPIReqAuthMandateEventPublisher,
	upiReqMandateConfirmationEventPublisher federalPayment.UPIReqMandateConfirmationEventPublisher,
	upiReqTxnConfirmationComplaintPublisher federalPayment.UPIReqTxnConfirmationComplaintEventPublisher,
	upiReqAuthValCustEventPublisher federalPayment.UPIReqAuthValCustEventPublisher,
	inboundUpiTxnPublisher federalPayment.InboundUpiTxnPublisher,
	inboundLoanTxnPublisher federalPayment.InboundLoanTxnPublisher,
	upiRespComplaintEventPublisher federalPayment.UPIRespComplaintEventPublisher,
	upiReqMapperConfirmationEventPublisher federalPayment.UPIReqMapperConfirmationEventPublisher,
	dynamicConf *genconf.Config,
) (*federalPayment.Service, error) {
	wire.Build(
		syncRespCallbackHandlerChannelsProvider,
		syncRespHandlerProvider,
		federalPayment.NewService, syncresp.NewHandler,
		upiListVaePublisherProvider,
	)
	return &federalPayment.Service{}, nil
}

func InitializeFederalCardService(creationCallbackPublisher federalCard.CreateCardCallbackPublisher, dispatchPhysicalCardCallbackPublisher federalCard.DispatchPhysicalCardCallbackPublisher,
	cardSwitchFinancialNotificationPublisher federalCard.CardSwitchFinancialNotificationPublisher,
	cardSwitchNonFinancialNotificationPublisher federalCard.CardSwitchNonFinancialNotificationPublisher) *federalCard.Service {
	wire.Build(
		federalCard.NewService,
	)
	return &federalCard.Service{}
}

func InitialiseShipwayCardService(cardTrackingCallbackPublisher shipwayCard.CardTrackingCallbackPublisher) *shipwayCard.Service {
	wire.Build(
		shipwayCard.NewService,
	)
	return &shipwayCard.Service{}
}

func InitializeKarzaLivenessService() *karza.Service {
	wire.Build(
		karza.NewService,
	)
	return &karza.Service{}
}

func InitializeVideoSdkService() *videosdk.Service {
	wire.Build(
		videosdk.NewService,
	)
	return &videosdk.Service{}
}

func InitializeFederalDepositService(
	createDepositCallbackPublisher federalDeposit.CreateDepositCallbackPublisher,
	preCloseDepositCallbackPublisher federalDeposit.PreCloseDepositCallbackPublisher,
	fdAutoRenewCallbackPublisher federalDeposit.FdAutoRenewCallbackPublisher,
) *federalDeposit.Service {
	wire.Build(
		federalDeposit.NewService,
	)
	return &federalDeposit.Service{}
}

func InitializeFederalDmpService(
	disputeClient dPb.DisputeClient,
) *federalDmp.Service {
	wire.Build(
		federalDmp.NewService,
	)
	return &federalDmp.Service{}
}

func InitializeFederalAuthService(deviceReregCallbackPublisher federalAuth.DeviceReRegCallbackPublisher, deviceRegSMSAckPublisher federalAuth.DeviceRegSMSAckPublisher, mobileNumberUpdateCallbackPub federalAuth.FederalMobileNumberUpdatePublisher) *federalAuth.Service {
	wire.Build(
		federalAuth.NewService,
	)
	return &federalAuth.Service{}
}

func InitializeSmsAclCallbackService(callbackPublisher acl.AclSmsCallbackPublisher, conf *config.Config) *acl.Service {
	wire.Build(
		acl.NewService,
	)
	return &acl.Service{}
}

func InitializeWhatsappAclCallbackService(conf *config.Config, whatsappCallbackPub aclWa.AclWhatsappCallbackPublisher, whatsappReplyPub aclWa.AclWhatsappReplyPublisher) *aclWa.Service {
	wire.Build(
		aclWa.NewService,
	)
	return &aclWa.Service{}
}

func InitializeKycTypeChangeCallbackService(fedVkycUpdPublisher federalKycTypeChange.FederalVkycUpdatePublisher, fedBankCustKycStateChangePublisherfederalKycTypeChange federalKycTypeChange.FederalBankCustKycStateChangePublisher) *federalKycTypeChange.Service {
	wire.Build(
		federalKycTypeChange.NewService,
	)
	return &federalKycTypeChange.Service{}
}

func InitializeAccountsCallbackService(bankCustCallbackPublisher federalAccounts.BankCustCallbackPublisher,
	accountCreationCallbackPublisher federalAccounts.AccountCreationCallbackPublisher, config *genconf.Config, accountStatusCallBackPublisher federalAccounts.AccountStatusCallBackPublisher, publisher federalAccounts.FederalResidentialStatusUpdatePublisher) *federalAccounts.Service {
	wire.Build(
		federalAccounts.NewService,
	)
	return &federalAccounts.Service{}
}

func InitializeShippingAddressUpdateCallbackService(addressUpdateCallbackPublisher shippingAddressUpdate.UpdateShippingAddressCallbackPublisher) *shippingAddressUpdate.Service {
	wire.Build(
		shippingAddressUpdate.NewService,
	)
	return &shippingAddressUpdate.Service{}
}

func InitializeVKYCKarzaCallbackService(
	callEventPub karza2.KarzaVkycCallEventPublisher,
	agentPub karza2.KarzaVkycAgentResponsePublisher,
	auditorPub karza2.KarzaVkycAuditorResponsePublisher,
	conf *config.Config,
) *karza2.Service {
	wire.Build(
		karza2.NewService,
	)
	return &karza2.Service{}
}

func InitializeEmailCallbackService(emailCallbackPub email.EmailCallbackPublisher, conf *config.Config) *email.Service {
	wire.Build(
		email.NewService,
	)
	return &email.Service{}
}

func InitializeAANotificationService(consentPub aa.ConsentCallbackPublisher, fiPub aa.FICallbackPublisher, beCaClient caPb.ConnectedAccountClient,
	conf *genconf.Config, vgAaClient vgAaPb.AccountAggregatorClient, alsPub aa.AccountLinkStatusCallbackPublisher) *aa.NotificationService {
	wire.Build(
		aa.NewNotificationService,
		connectedaccount.InMemoryAaCacheWireSet,
	)
	return &aa.NotificationService{}
}

func InitializeOzonetelCallRoutingService(cxCallRoutingClient callRoutingPb.CallRoutingClient, conf *config.Config, ozonetelCallDetails ozonetel.OzonetelCallDetailsPublisher, CallIvrClient callIvrPb.IvrClient) *ozonetel.CallRoutingService {
	wire.Build(
		ozonetel.NewCallRoutingService,
	)
	return &ozonetel.CallRoutingService{}
}

func InitializeSprinklrEventHandlingService(sprinklrEventProcessingService sprinklrPb.SprinklrClient, conf *config.Config) *sprinklr.EventsHandlingService {
	wire.Build(
		sprinklr.NewEventsHandlingService,
	)
	return &sprinklr.EventsHandlingService{}
}

func InitializeAxisPaymentCallBackService(conf *config.Config) *axisPayment.Service {
	wire.Build(
		axisPayment.NewService,
	)
	return &axisPayment.Service{}
}

func InitializeAxisAccountsCallBackService(conf *config.Config) *axisAccounts.Service {
	wire.Build(
		axisAccounts.NewService,
	)
	return &axisAccounts.Service{}
}

func InitializeAxisEKycCallBackService(conf *config.Config) *axisEkyc.Service {
	wire.Build(
		axisEkyc.NewService,
	)
	return &axisEkyc.Service{}
}

func InitializeKaleyraCallbackService(callbackPublisher kaleyra.KaleyraSmsCallbackPublisher, conf *config.Config) *kaleyra.Service {
	wire.Build(
		kaleyra.NewService,
	)
	return &kaleyra.Service{}
}

func InitializeNetCoreCallbackService(callbackPublisher netcore.NetCoreSmsCallbackPublisher, conf *config.Config) *netcore.Service {
	wire.Build(
		netcore.NewService,
	)
	return &netcore.Service{}
}

func InitializeAirtelCallbackService(conf *config.Config, callbackPublisher airtel.AirtelSmsCallbackPublisher, smsClient vgSmsPb.SMSClient) *airtel.Service {
	wire.Build(
		airtel.NewService,
	)
	return &airtel.Service{}
}

func InitializeAirtelWhatsappCallbackService(conf *config.Config, callbackPublisher airtel2.AirtelWhatsappCallbackPublisher) *airtel2.Service {
	wire.Build(
		airtel2.NewService,
	)
	return &airtel2.Service{}
}

func rateLimitConfigProvider(conf *config.Config) *cfg.RateLimitConfig {
	return conf.RateLimitConfig
}

func InitializeSenseforthChatBotAuthService(conf *config.Config, authClient authPb.AuthClient,
	rateLimiterRedisStore types.RateLimiterRedisStore) (*senseforth.Service, error) {
	wire.Build(
		senseforth.NewService,
		rateLimitConfigProvider,
		types.RateLimiterRedisStoreRedisClientProvider,
		ratelimiterWire.WireSetV1,
		wire.NewSet(helper.NewSenseforthAuthHelper, wire.Bind(new(helper.IAuthHelper), new(*helper.SenseforthAuthHelper))),
		wire.NewSet(helper.NewRatelimitHelper, wire.Bind(new(helper.IRateLimitHelper), new(*helper.RatelimitHelper))),
	)
	return &senseforth.Service{}, nil
}

func InitializeSenseforthLiveChatFallbackService(conf *config.Config, cxLiveChatFallbackPbClient cxLiveChatFbkPb.LiveChatFallbackClient, authClient authPb.AuthClient,
	rateLimiterRedisStore types.RateLimiterRedisStore) (*liveChatFlbkSenseforth.Service, error) {
	wire.Build(
		liveChatFlbkSenseforth.NewService,
		rateLimitConfigProvider,
		types.RateLimiterRedisStoreRedisClientProvider,
		ratelimiterWire.WireSetV1,
		wire.NewSet(helper.NewSenseforthAuthHelper, wire.Bind(new(helper.IAuthHelper), new(*helper.SenseforthAuthHelper))),
		wire.NewSet(helper.NewRatelimitHelper, wire.Bind(new(helper.IRateLimitHelper), new(*helper.RatelimitHelper))),
	)
	return &liveChatFlbkSenseforth.Service{}, nil
}

func InitializeFreshchatCallbackService(conf *config.Config, fcCallbackPub freshchat.FreshchatActionCallbackPublisher) *freshchat.Service {
	wire.Build(
		freshchat.NewService,
	)
	return &freshchat.Service{}
}

func InitializeFreshchatAIBotService(conf *genconf.Config, userClient usersPb.UsersClient, irClient issue_reporting.ServiceClient,
	vendorMapping vendormappingPb.VendorMappingServiceClient) *vnChatbotFreschat.Service {
	wire.Build(
		vnChatbotFreschat.NewService,
	)
	return &vnChatbotFreschat.Service{}
}

func FireflyFaasExecutorProvider(ctx context.Context, awsConf awsV2.Config, conf *config.Config) (faas.FaaSExecutor, error) {
	return epifitemporalfaas.NewFaaSExecutor(ctx, sqsPkg.InitSQSClient(awsConf), namespace.Firefly, conf.ProcrastinatorWorkflowPublisher)
}

func InitializeM2PCreditCardService(
	ctx context.Context,
	cardTransactionNotificationPublisher ccm2p.CCTransactionNotificationPublisher, cardStatementNotificationPublisher ccm2p.CCStatementNotificationPublisher,
	cardAcsNotificationPublisher ccm2p.CCAcsNotificationPublisher, dynamicConf *genconf.Config, awsConf awsV2.Config, conf *config.Config,
	switchNotificationsS3Client ccm2p.CcSwitchNotificationsS3Client, rawSwitchNotificationsS3Client ccm2p.CcRawSwitchNotificationsS3Client,
	ccNonFinancialNotificationPublisher ccm2p.CCNonFinancialNotificationPublisher,
) (*m2p.Service, error) {
	wire.Build(m2p.NewService, FireflyFaasExecutorProvider)
	return &m2p.Service{}, nil
}

func InitializePaisabazaarCallBackService(
	ffClient fireflyPb.FireflyClient,
) *paisabazaar.Service {
	wire.Build(paisabazaar.NewService)
	return &paisabazaar.Service{}
}

func InitializeChatbotWorkflowService(conf *config.Config, authClient authPb.AuthClient, cxWorkflowClient cxChatbotWorkflowPb.WorkflowClient) *botWorkflow.Service {
	wire.Build(
		botWorkflow.NewService,
		wire.NewSet(helper.NewSenseforthAuthHelper, wire.Bind(new(helper.IAuthHelper), new(*helper.SenseforthAuthHelper))),
	)
	return &botWorkflow.Service{}
}

func InitializeTssWebhookCallBackService(conf *config.Config, tssWebhookCallBackPublisher tss.TssWebhookCallBackPublisher) *tss.Service {
	wire.Build(
		tss.NewService,
	)
	return &tss.Service{}
}

func InitializeRiskcovryCallbackService(conf *config.Config, healthInsuranceClient healthinsurancePb.HealthInsuranceClient, policyIssuanceCallbackPublisher riskcovry.HealthInsurancePolicyIssuanceEventPublisher) *riskcovry.Service {
	wire.Build(
		riskcovry.NewService,
	)
	return &riskcovry.Service{}
}

func smallcaseWebhookPublisherProvider(ctx context.Context, conf *config.Config, awsConf awsV2.Config) queue.ExtendedPublisher {
	return pkgWire.InitializeExtendedPublisher(ctx, awsConf,
		queue.NewDefaultMessage(),
		sqsPkg.QueueName(conf.SmallcaseProcessMFHoldingsWebhookPublisher.GetQueueName()),
		conf.SmallcaseProcessMFHoldingsWebhookPublisher.GetBucketName(),
		sqsPkg.ServiceName(cfg.VENDOR_NOTIFI_SERVICE))
}

func InitializeSmallcaseWebhookService(ctx context.Context, conf *config.Config, awsConf awsV2.Config) *smallcase.Service {
	wire.Build(
		smallcase.NewService,
		smallcaseWebhookPublisherProvider,
	)
	return &smallcase.Service{}
}

func InitializeLendingFederalInboundService(conf *config.Config) *lendingFederal.Service {
	wire.Build(
		lendingFederal.NewService,
	)
	return &lendingFederal.Service{}
}

func InitializeEPANCallbackService(conf *config.Config, callbackPublisher epanKarza.SignalWorkflowPublisher, panClient panPb.PanClient, celestialClient celestialPb.CelestialClient,
	s3Client epanKarza.EpanCallbackS3Client) *epanKarza.Service {
	wire.Build(
		epanKarza.NewService,
	)
	return &epanKarza.Service{}
}

func InitializeMoengageService(
	conf *config.Config,
	genConf *genconf.Config,
	questCacheStorage types.QuestCacheStorage,
	vendorMapping vendormappingPb.VendorMappingServiceClient,
	rewardsServiceClient rewardsPb.RewardsGeneratorClient,
	actorClient actorPb.ActorClient,
	usersClient usersPb.UsersClient,
	userGroupClient userGroupPb.GroupClient,
	segmentClient segmentPb.SegmentationServiceClient,
	questManagerCl questManagerPb.ManagerClient,
	eventsBroker events.Broker,
	client txnAggregatesPb.TxnAggregatesClient,
	savingsClient savingsPb.SavingsClient,
	fireflyClient fireflyPb.FireflyClient,
	connectedAccountsClient connectedaccountPb.ConnectedAccountClient,
	merchantClient merchantPb.MerchantServiceClient,
	piClient paymentinstrumentPb.PiClient,
	categorizerClient categorizerPb.TxnCategorizerClient,
	palClient preapprovedloan.PreApprovedLoanClient,
	balanceClient balancePb.BalanceClient,
	ccVgClient creditCardVgPb.CreditCardClient,
	accountingClient ffAccPb.AccountingClient,
	billingClient ffBillingPb.BillingClient,
	depositClient depositPb.DepositClient,
	provisioningClient provisioning.CardProvisioningClient,
	payClient pay.PayClient,
	usStocksRedisClient types.USStocksRedisStore,
	usStocksCatalogManagerClient usStockscatalogPb.CatalogManagerClient,
	salaryClient salaryPb.SalaryProgramClient,
	ticketClient ticketPb.TicketClient,
	upiOnboardingClient upiOnbPb.UpiOnboardingClient,
	accountPiClient accountPiPb.AccountPIRelationClient,
	netWorthClient networthPb.NetWorthClient,
	catalogManagerClient catalogPb.CatalogManagerClient,
	userDeclarationClient userDeclarationPb.ServiceClient,
	investAnalyticsClient investment.InvestmentAnalyticsClient,
	epfClient beEpfPb.EpfClient,
	variableGeneratorClient variablesPb.VariableGeneratorClient,
) *moengage.Service {
	wire.Build(
		helper2.NewCommsDataHelper,
		moengage.NewService,
		types.QuestCacheStorageProvider,
		questSDKClientConfProvider,
		questsdkinit.GetQuestSDKClient,
		txnaggregates.TxnAggregatesWireSet,
		insigthsDataFetcher.ActorAccountsWireSet,
		insigthsDataFetcher.IMerchantsWireSet,
		moengageSpendsInsights.NewSpendsAttributesFetcher,
		moengageSpendsInsights.WireSummaryGeneratorSet,
		usStocksCacheStorageProvider,
		moengageStocks.StocksAttributeProviderWireSet,
		moengageStocks.NewStocksAttributeFetcher,
		datetime.WireDefaultTimeSet,
		factory.FactoryWireSet,
		userattributesfetcher.AllUserAttributesFetcherWireSet,
		webhookAreaProcessor.AreaProcessorFactoryWireSet,
		webhookAreaProcessor.NewLoansAreaProcessor,
		webhookLoansUsecaseProcessor.NewLoansUseCaseProcessorFactory,
		dropoff.NewDropOffOutcallUseCaseProcessor,
	)
	return &moengage.Service{}
}

func usStocksCacheStorageProvider(usstockRedisCl types.USStocksRedisStore) vnTypes.USStocksCacheStorage {
	return cache.NewRedisCacheStorage(usstockRedisCl)
}

func InitializeInhouseBreService(fennelClient fennelVgPb.FennelFeatureStoreClient, scienapticClient vgScienapticPb.ScienapticClient, usersClient usersPb.UsersClient, conf *config.Config, creditReportManagerClient credit_report.CreditReportManagerClient) *inhouse.Service {
	wire.Build(
		inhouse.NewService,
		featurestore.FactoryWireSet,
		fennel2.NewFennelClient,
		scienaptic.NewScienapticFeatureStoreClient,
	)
	return &inhouse.Service{}
}

func InitialiseRecurringPaymentFederalService(
	federalEnachRegistrationAuthorisationCallbackPublisher federalRecurringPayment.EnachRegistrationAuthorisationCallbackPublisher,
) *rpPb.Service {
	wire.Build(
		rpPb.NewService,
	)
	return &rpPb.Service{}
}

func InitializeIdfcKycCallbackService(kycStatusUpdatePublisher vnIdfc.KycStatusUpdatePublisher) *vnIdfc.Service {
	wire.Build(
		vnIdfc.NewService,
	)
	return &vnIdfc.Service{}
}

func InitializeFiftyfinNotificationService(conf *config.Config, celestialClient celestialPb.CelestialClient) *fiftyfinVn.Service {
	wire.Build(
		fiftyfinVn.NewService,
	)
	return &fiftyfinVn.Service{}
}

func questSDKClientConfProvider(conf *genconf.Config) *questSdkGenConf.Config {
	return conf.QuestSdk()
}

func InitializeFiCoinsAccountingService(genConf *genconf.Config, vendorMappingClient vendormappingPb.VendorMappingServiceClient, externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient, userClient usersPb.UsersClient,
	fireflyClient fireflyPb.FireflyClient) *ficoinsAccVnPb.Service {
	wire.Build(
		ficoinsAccVnPb.NewService,
	)
	return &ficoinsAccVnPb.Service{}
}

func InitializeAbflNotificationService(conf *config.Config, celestialClient celestialPb.CelestialClient) *AbflVn.Service {
	wire.Build(
		AbflVn.NewService,
	)
	return &AbflVn.Service{}
}

func InitializeSetuNotificationService(conf *config.Config, celestialClient celestialPb.CelestialClient) *SetuVn.Service {
	wire.Build(
		SetuVn.NewService,
	)
	return &SetuVn.Service{}
}

func InitializeMoneyviewCallbackService(conf *config.Config, celestialClient celestialPb.CelestialClient) *moneyviewVn.Service {
	wire.Build(
		moneyviewVn.NewService,
	)
	return &moneyviewVn.Service{}
}

func InitializeIdfcCallbackService(conf *config.Config, celestialClient celestialPb.CelestialClient) *idfcVnPb.Service {
	wire.Build(
		idfcVnPb.NewService,
	)
	return &idfcVnPb.Service{}
}

func InitializeExternalOfferRedemptionsService(genConf *genconf.Config, vendorMappingClient vendormappingPb.VendorMappingServiceClient, externalVendorRedemptionClient evrPb.ExternalVendorRedemptionServiceClient, userClient usersPb.UsersClient) *offersVnPb.Service {
	wire.Build(
		offersVnPb.NewService,
	)
	return &offersVnPb.Service{}
}

func InitializeCommsUnsubscribeService(conf *genconf.Config, vendorMappingClient vendormappingPb.VendorMappingServiceClient, userClient usersPb.UsersClient) *unsubscribeVn.Service {
	wire.Build(
		unsubscribeVn.NewService,
	)
	return &unsubscribeVn.Service{}
}

func InitializeCredgenicsWebhookService(conf *config.Config, awsConf awsV2.Config, palClient preapprovedloan.PreApprovedLoanClient, collectionClient collection.CollectionClient) *credgenicsVn.Service {

	wire.Build(
		InitializeCredgenicsCallbackStreamProducers,
		wire.FieldsOf(&CredgenicsStreamProducers{}, "SmsCallbackStreamProducer", "CallingCallbackStreamProducer", "WhatsappCallbackStreamProducer",
			"EmailCallbackStreamProducer", "VoiceMessageCallbackStreamProducer"),
		credgenicsVn.NewService,
	)
	return &credgenicsVn.Service{}
}

type CredgenicsStreamProducers struct {
	SmsCallbackStreamProducer          vnTypes.SmsCallbackStreamProducer
	CallingCallbackStreamProducer      vnTypes.CallingCallbackStreamProducer
	WhatsappCallbackStreamProducer     vnTypes.WhatsappCallbackStreamProducer
	EmailCallbackStreamProducer        vnTypes.EmailCallbackStreamProducer
	VoiceMessageCallbackStreamProducer vnTypes.VoiceMessageCallbackStreamProducer
}

func InitializeCredgenicsCallbackStreamProducers(conf *config.Config, awsConf awsV2.Config) CredgenicsStreamProducers {
	kinesisClient := kinesis.NewKinesisClient(&awsConf)

	var smsCallbackStreamProducer vnTypes.SmsCallbackStreamProducer
	var callingCallbackStreamProducer vnTypes.CallingCallbackStreamProducer
	var whatsappCallbackStreamProducer vnTypes.WhatsappCallbackStreamProducer
	var emailCallbackStreamProducer vnTypes.EmailCallbackStreamProducer
	var voiceMessageCallbackStreamProducer vnTypes.VoiceMessageCallbackStreamProducer

	streamConfig := conf.CredgenicsCallbackStreamProducer

	var initErr error
	smsCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.SmsStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.SmsStream)
	callingCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.CallingStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.CallingStream)
	whatsappCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.WhatsappStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.WhatsappStream)
	emailCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.EmailStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.EmailStream)
	voiceMessageCallbackStreamProducer, initErr = kinesis.NewKinesisProducer(context.Background(), streamConfig.VoiceMessageStream, kinesisClient)
	handleStreamInitErr(initErr, streamConfig.VoiceMessageStream)

	return CredgenicsStreamProducers{smsCallbackStreamProducer, callingCallbackStreamProducer, whatsappCallbackStreamProducer, emailCallbackStreamProducer, voiceMessageCallbackStreamProducer}
}

func handleStreamInitErr(err error, streamProducer *cfg.KinesisProducer) {
	if err != nil {
		logger.Panic("Error initialising producer for stream "+streamProducer.StreamName, zap.Error(err))
	}
}

func InitializeWhatsappGupshupCallbackService(conf *config.Config, whatsappCallbackPub gupshupWa.GupshupWhatsappCallbackPublisher) *gupshupWa.Service {
	wire.Build(
		gupshupWa.NewService,
	)
	return &gupshupWa.Service{}
}

func InitializeRcsGupshupCallbackService(conf *config.Config, rcsCallbackPub gupshupRcs.GupshupRcsCallbackPublisher) *gupshupRcs.Service {
	wire.Build(
		gupshupRcs.NewService,
	)
	return &gupshupRcs.Service{}
}

func InitializeRazorpayInboundEventService(conf *genconf.Config, pgInboundEventPub razorpay.PgRazorpayInboundEventPublisher) *razorpay.Service {
	wire.Build(
		razorpay.NewService,
	)
	return &razorpay.Service{}
}

func InitializeIgnosisAaService(conf *genconf.Config, aaAnalysisClient aaAnalyticsPb.AnalyticsClient) *ignoisAa.IgnosisAaService {
	wire.Build(
		ignoisAa.NewIgnosisAaService,
	)
	return &ignoisAa.IgnosisAaService{}
}

func InitializeFederalEventHandlingService(federalEventProcessingService federalpb.FederalClient, genConf *config2.Config, federalEscalationUpdateEventPublisher vnTypes.FederalEscalationUpdateEventPublisher) *federal.FederalEventsHandlingService {
	wire.Build(
		federal.NewFederalEventsHandlingService,
	)
	return &federal.FederalEventsHandlingService{}
}

func InitializeSavenCallBackService(ffV2Client fireflyV2Pb.FireflyV2Client, conf *config.Config, publisher saven.CcOnboardingStateUpdateEventPublisher) *saven.Service {
	wire.Build(
		saven.NewService,
	)
	return &saven.Service{}
}

func InitializeNuggetService(
	genConf *genconf.Config,
	authClient authPb.AuthClient,
	orderClient orderPb.OrderServiceClient,
	riskProfileClient profile.ProfileClient,
	vendorMappingClient vendormappingPb.VendorMappingServiceClient,
	saClosureClient saClosurePb.SavingsAccountClosureClient,
	alfredClient alfredPb.AlfredClient,
	eventBroker events.Broker,
	nuggetEventPub nugget.NuggetEventCallbackPublisher,
) (*nugget.NuggetService, error) {
	wire.Build(
		nugget.NewNuggetService,
		wire.NewSet(
			data_collector.NewDataCollectorFactorySvc,
			wire.Bind(new(data_collector.DataCollectorFactory), new(*data_collector.DataCollectorFactorySvc)),
			collectors.NewFreezeDataCollector,
			collectors.NewTransactionDataCollector,
			collectors.NewAccountClosureDataCollector,
			collectors.NewChequeBookEligibilityDataCollector,
			collectors.NewChequebookDataCollector,
		),
	)
	return nil, nil
}

func InitializeAuthService(conf *config.Config) *auth.Service {
	wire.Build(
		auth.NewService,
	)
	return &auth.Service{}
}

func InitializeRewardService(gconf *genconf.Config, conf *config.Config, ffV2Client fireflyV2Pb.FireflyV2Client,
	rewardsGenClient rewardsPb.RewardsGeneratorClient, vendorRewardFulfillmentPublisher reward.VendorRewardFulfillmentPublisher) (*reward.Service, error) {
	wire.Build(
		reward.NewRewardService,
	)
	return &reward.Service{}, nil
}

func InitializeKeyGenerator() keygen.IKeyGenerator {
	wire.Build(
		ratelimiter.NewKeyGenerator,
		ratelimiterNamespace.NewDefaultFactory,
		ratelimiterNamespace.NewClientIdFromCtxNamespaceGenerator,
		ratelimiterNamespace.NewClientIdFromReqNamespaceGenerator,
	)
	return &ratelimiter.KeyGenerator{}
}

func InitializePushNotificationService(commsClient commsPb.CommsClient, fireflyV2Client fireflyV2Pb.FireflyV2Client, userClient usersPb.UsersClient, conf *config.Config) (*pushnotification.Service, error) {
	wire.Build(
		pushnotification.NewService,
	)
	return &pushnotification.Service{}, nil
}
