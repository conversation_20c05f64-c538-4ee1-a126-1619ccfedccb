package tss

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/aml"
	"github.com/epifi/gamma/api/vendors/tss"
	"github.com/epifi/gamma/vendornotification/redactor"
)

func (s *Service) ProcessWHS501CallbackForEpifi(ctx context.Context, req *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	err := s.processReq(ctx, req, commontypes.Owner_OWNER_EPIFI_TECH)
	if err != nil {
		return nil, err
	}
	return &tss.ProcessWHS501CallbackResponse{Status: "OK"}, nil
}

func (s *Service) ProcessWHS501CallbackForSG(ctx context.Context, req *tss.ProcessWHS501CallbackRequest) (*tss.ProcessWHS501CallbackResponse, error) {
	err := s.processReq(ctx, req, commontypes.Owner_OWNER_STOCK_GUARDIAN_TSP)
	if err != nil {
		return nil, err
	}
	return &tss.ProcessWHS501CallbackResponse{Status: "OK"}, nil
}

func (s *Service) processReq(ctx context.Context, req *tss.ProcessWHS501CallbackRequest, owner commontypes.Owner) error {
	redactor.LogCallbackRequestData(ctx, req, ProcessWHS501Callback, req.GetRequestData().GetRequestId(), nil)
	if req.GetEventType() == "" {
		logger.Info(ctx, "event type is empty")
		return nil
	}

	// Validate event type
	if req.GetEventType() != "WHS501" && req.GetEventType() != "ValidateEndPoint" {
		logger.Error(ctx, "invalid event type", zap.String("event_type", req.GetEventType()))
		return status.Errorf(codes.InvalidArgument, "invalid event type: %s", req.GetEventType())
	}

	// Handle endpoint validation
	if req.GetEventType() == "ValidateEndPoint" {
		logger.Info(ctx, "endpoint validation request received")
		return nil
	}

	// Process WHS501 webhook data
	requestData := req.GetRequestData()
	if requestData == nil {
		logger.Error(ctx, "request data is nil")
		return status.Errorf(codes.InvalidArgument, "request data is required")
	}
	decisionDetails, err := convertToDecisionDetails(requestData)
	if err != nil {
		logger.Error(ctx, "error converting to decision details", zap.Error(err))
		return status.Errorf(codes.Internal, "error converting to decision details")
	}
	tssWebhookEvent := &aml.ProcessCallbackForDecisionsOnCaseRequest{
		VendorRequestId: req.GetRequestData().GetRequestId(),
		// Note: In TSS cloud callbacks, unlike in-house ones, only one case is sent per request.
		DecisionDetails: []*aml.DecisionDetails{decisionDetails},
		VendorName:      commonvgpb.Vendor_TSS,
		Owner:           owner,
	}
	sqsMsgId, err := s.tssWebhookCallBackPublisher.Publish(ctx, tssWebhookEvent)
	if err != nil {
		logger.Error(ctx, "error publishing tss webhook callback event to queue", zap.Error(err))
		return status.Errorf(codes.Internal, "internal server error")
	}
	logger.Info(ctx, "tss webhook callback event published to queue successfully", zap.String(logger.QUEUE_MESSAGE_ID, sqsMsgId))
	return nil
}

func convertToDecisionDetails(caseDetails *tss.WHS501RequestData) (*aml.DecisionDetails, error) {
	caseDecision, err := convertToCaseDecision(caseDetails.GetOnboardingDecision())
	if err != nil {
		return nil, errors.Wrap(err, "error converting onboarding decision to case decision")
	}
	caseClosureDate, err := time.ParseInLocation("02-Jan-2006", caseDetails.GetCaseClosureDate(), datetime.IST)
	if err != nil {
		return nil, errors.Wrap(err, "error parsing case closure date")
	}
	return &aml.DecisionDetails{
		TransactionId:    caseDetails.GetCaseId(),
		RecordIdentifier: caseDetails.GetSourceSystemCustomerCode(),
		VendorCaseId:     caseDetails.GetCaseId(),
		CaseDecision:     caseDecision,
		FinalRemarks:     caseDetails.GetFinalRemarks(),
		ApprovedOn:       timestamppb.New(caseClosureDate),
		ApprovedBy:       caseDetails.GetCaseClosedBy(),
		// Note: Fields like case URL, PEP type, PEP classification, etc. are not provided in the cloud webhook payload.
		// TODO(Brijesh): Check which all are needed by US stocks, MF and Loans and how to retrieve them from TSS.
	}, nil
}

func convertToCaseDecision(onboardingDecision string) (aml.CaseDecision, error) {
	switch onboardingDecision {
	case "Proceed":
		return aml.CaseDecision_CASE_DECISION_APPROVED, nil
	case "Decline":
		return aml.CaseDecision_CASE_DECISION_REJECTED, nil
	default:
		return 0, errors.Errorf("unsupported onboarding decision: %s", onboardingDecision)
	}
}
