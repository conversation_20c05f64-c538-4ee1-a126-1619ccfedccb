//go:generate mockgen -source=factory.go -destination=../test/mocks/mock_data_collector_factory.go -package=mocks
package data_collector

import (
	"fmt"

	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/vendornotification/cx/chatbot/data_collector/collectors"
)

type DataCollectorFactory interface {
	GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error)
}

type DataCollectorFactorySvc struct {
	freezeDataCollector                *collectors.FreezeBotDataCollector
	transactionDataCollector           *collectors.TransactionDataCollector
	chequeBookEligibilityDataCollector *collectors.ChequeBookEligibilityDataCollector
	accountClosureDataCollector *collectors.AccountClosureDataCollector
	chequebookDataCollector  *collectors.ChequebookDataCollector
}

func NewDataCollectorFactorySvc(
	freezeDataCollector *collectors.FreezeBotDataCollector,
	transactionDataCollector *collectors.TransactionDataCollector,
	chequeBookEligibilityDataCollector *collectors.ChequeBookEligibilityDataCollector,
	accountClosureDataCollector *collectors.AccountClosureDataCollector,
	chequebookDataCollector *collectors.ChequebookDataCollector,
) *DataCollectorFactorySvc {
	return &DataCollectorFactorySvc{
		freezeDataCollector:                freezeDataCollector,
		transactionDataCollector:           transactionDataCollector,
		chequeBookEligibilityDataCollector: chequeBookEligibilityDataCollector,
		accountClosureDataCollector: accountClosureDataCollector,
		chequebookDataCollector:  chequebookDataCollector,
	}
}

func (d *DataCollectorFactorySvc) GetImpl(qcType typesv2.ChatbotRequestedDataField) (DataCollector, error) {
	switch qcType {
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS:
		return d.freezeDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS:
		return d.transactionDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY:
		return d.chequeBookEligibilityDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE:
		return d.accountClosureDataCollector, nil
	case typesv2.ChatbotRequestedDataField_CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS:
		return d.chequebookDataCollector, nil
	default:
		return nil, fmt.Errorf("data collector for %s is not implemented yet", qcType.String())
	}
}
