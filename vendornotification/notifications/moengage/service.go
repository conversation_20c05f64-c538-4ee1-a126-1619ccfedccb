package moengage

import (
	"context"
	"fmt"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	goutils "github.com/epifi/be-common/pkg/go_utils"
	"github.com/epifi/be-common/pkg/logger"

	vendormappingPb "github.com/epifi/gamma/api/vendormapping"
	moengageVnPb "github.com/epifi/gamma/api/vendornotification/notifications/moengage"
	moengageVendorPb "github.com/epifi/gamma/api/vendors/moengage"
	"github.com/epifi/gamma/vendornotification/config"
	"github.com/epifi/gamma/vendornotification/metrics"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
	"github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher/factory"
	webhookAreaProcessor "github.com/epifi/gamma/vendornotification/notifications/moengage/webhookprocessor/area"
)

type Service struct {
	conf                         *config.Config
	userAttributesFetcherFactory factory.IUserAttributesFetcherFactory
	vendorMapping                vendormappingPb.VendorMappingServiceClient
	webhookAreaProcessorFactory  webhookAreaProcessor.IAreaProcessorFactory
}

func NewService(conf *config.Config,
	userAttributesFetcherFactory factory.IUserAttributesFetcherFactory,
	vendorMapping vendormappingPb.VendorMappingServiceClient,
	webhookAreaProcessorFactory webhookAreaProcessor.IAreaProcessorFactory) *Service {
	return &Service{
		conf:                         conf,
		userAttributesFetcherFactory: userAttributesFetcherFactory,
		vendorMapping:                vendorMapping,
		webhookAreaProcessorFactory:  webhookAreaProcessorFactory,
	}
}

var _ moengageVnPb.MoengageServer = &Service{}

const (
	GenericSuccessStatus = "Success"
	GenericFailureStatus = "Failed"
	NotFoundStatus       = "NotFound"
)

func (s *Service) GetUserAttributes(ctx context.Context, request *moengageVendorPb.GetUserAttributesRequest) (*moengageVendorPb.GetUserAttributesResponse, error) {
	// todo(divyadeep): whitelist Moengage's IPs for this API
	// IP whitelisting
	// if err := security.CheckWhiteList(ctx, s.conf._, s.conf.NumberOfHopsThatAddXForwardedFor, s.conf.VpcCidrIPPrefix); err != nil {
	//	return nil, err
	// }
	if request.GetUserId() == "" || request.GetAreas() == "" {
		logger.Error(ctx, "mandatory params missing", zap.String("userId", request.GetUserId()), zap.String("areas", request.GetAreas()))
		return nil, status.Errorf(codes.InvalidArgument, "mandatory params missing")
	}

	areas, err := getAreaEnumsFromString(ctx, request.GetAreas())
	if err != nil {
		logger.Error(ctx, "unsupported area types", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "error: %s", err.Error())
	}

	getActorIdByVendorIdResponse, err := s.vendorMapping.GetActorIdByVendorId(ctx, &vendormappingPb.GetActorIdByVendorIdRequest{
		VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
			MoengageId: request.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getActorIdByVendorIdResponse, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching ActorId mapped to moengage user ID", zap.String(logger.USER_ID, request.GetUserId()), zap.Error(rpcErr))
		return nil, fmt.Errorf("error while fetching ActorId mapped to moengage user ID, err: %w", rpcErr)
	}
	ctx = epificontext.CtxWithActorId(ctx, getActorIdByVendorIdResponse.GetActorId())
	areaToFieldNameUserAttributes := make(map[string]*moengageVendorPb.FieldNameToUserAttributeMap, 0)
	// use recordAreaBasedMetricsAndReturnGetUserAttributesResponse to return from here
	// Example : return recordAreaBasedMetricsAndReturnGetUserAttributesResponse(areas,GetUserAttributesResponse,err)
	// if inside the loop, send the area as an array
	for _, area := range areas {
		// fetch getter
		userAttributesFetcher, err2 := s.userAttributesFetcherFactory.GetUserAttributesFetcher(area)
		if err2 != nil {
			logger.Error(ctx, "error while getting userAttributesFetcher", zap.String("area", area.String()), zap.Error(err2))
			return recordAreaBasedMetricsAndReturnGetUserAttributesResponse([]moengageVnPb.Area{area}, nil, fmt.Errorf("error while getting userAttributesFetcher"))
		}
		// fetch attribute details
		getAttributesRes, err2 := userAttributesFetcher.GetAttributes(ctx, &userattributesfetcher.GetAttributesRequest{
			ActorId:         getActorIdByVendorIdResponse.GetActorId(),
			FieldMask:       getFieldMaskListFromString(request.GetFieldMask()),
			RequestMetadata: getRequestMetadataMapFromString(ctx, request.GetRequestMetadata()),
		})
		if err2 != nil {
			if errors.Is(err2, epifierrors.ErrResourceExhausted) {
				return recordAreaBasedMetricsAndReturnGetUserAttributesResponse([]moengageVnPb.Area{area}, nil, status.Errorf(codes.ResourceExhausted, "resource exhausted while fetching attributes"))
			}
			if errors.Is(err2, epifierrors.ErrInvalidArgument) {
				return recordAreaBasedMetricsAndReturnGetUserAttributesResponse([]moengageVnPb.Area{area}, nil, status.Errorf(codes.InvalidArgument, "invalid argument passed by the client"))
			}
			if errors.Is(err2, epifierrors.ErrRecordNotFound) {
				return recordAreaBasedMetricsAndReturnGetUserAttributesResponse([]moengageVnPb.Area{area}, nil, status.Errorf(codes.NotFound, "data not found for the  user"))
			}
			logger.Error(ctx, "error while getting user attributes", zap.String("area", area.String()), zap.Any("fieldMask", request.GetFieldMask()), zap.Any("requestMetadata", request.GetRequestMetadata()), zap.Error(err2))
			return recordAreaBasedMetricsAndReturnGetUserAttributesResponse([]moengageVnPb.Area{area}, nil, fmt.Errorf("error while getting user attributes"))
		}
		// add user attributes for area to areaToFieldNameUserAttributes
		areaToFieldNameUserAttributes[area.String()] = &moengageVendorPb.FieldNameToUserAttributeMap{
			FieldNameToUserAttributes: getAttributesRes.GetFieldNameToAttributesMap(),
		}
	}

	// record success state for all the areas
	return recordAreaBasedMetricsAndReturnGetUserAttributesResponse(areas, &moengageVendorPb.GetUserAttributesResponse{
		AreaToFieldNameUserAttributes: areaToFieldNameUserAttributes,
	}, nil)
}

func getAreaEnumsFromString(ctx context.Context, areasString string) ([]moengageVnPb.Area, error) {
	var areaList []moengageVnPb.Area
	for _, area := range strings.Split(areasString, ",") {
		areaUpperStr := strings.ToUpper(area)
		areaEnum := goutils.Enum(areaUpperStr, moengageVnPb.Area_value, moengageVnPb.Area_AREA_UNSPECIFIED)
		if areaEnum == moengageVnPb.Area_AREA_UNSPECIFIED {
			logger.Error(ctx, "unsupported area provided", zap.String("area", area))
			return nil, fmt.Errorf("unsupported area provided, area: %s", area)
		}
		areaList = append(areaList, areaEnum)
	}
	return areaList, nil
}

func getFieldMaskListFromString(fieldMaskString string) []userattributesfetcher.UserAttributesReqField {
	var requestedAttribs []userattributesfetcher.UserAttributesReqField
	for _, field := range strings.Split(fieldMaskString, ",") {
		requestedAttribs = append(requestedAttribs, userattributesfetcher.UserAttributesReqField(strings.TrimSpace(field)))
	}
	return requestedAttribs
}

func getRequestMetadataMapFromString(ctx context.Context, keyValuePairsString string) map[userattributesfetcher.RequestMetadataKey]string {
	keyValuePairs := strings.Split(keyValuePairsString, ",")
	keyValueMap := make(map[userattributesfetcher.RequestMetadataKey]string, len(keyValuePairsString))
	for _, keyValuePair := range keyValuePairs {
		vals := strings.Split(strings.TrimSpace(keyValuePair), ":")
		if len(vals) != 2 {
			logger.Error(ctx, "can't split key/value pair, not separated by ':'", zap.String("value", keyValuePair))
			continue
		}
		keyValueMap[userattributesfetcher.RequestMetadataKey(strings.TrimSpace(vals[0]))] = strings.TrimSpace(vals[1])
	}
	return keyValueMap
}

// records area specific metrics and returns GetUserAttributesResponse
func recordAreaBasedMetricsAndReturnGetUserAttributesResponse(areas []moengageVnPb.Area, response *moengageVendorPb.GetUserAttributesResponse, err error) (*moengageVendorPb.GetUserAttributesResponse, error) {
	if err != nil {
		statusCode := GenericFailureStatus
		if status.Code(err) == codes.NotFound {
			statusCode = NotFoundStatus
		}
		for _, area := range areas {
			metrics.RecordAreaBasedStatusCounterForGetUserAttributes(area.String(), statusCode)
		}
		return nil, err
	}
	for _, area := range areas {
		metrics.RecordAreaBasedStatusCounterForGetUserAttributes(area.String(), GenericSuccessStatus)
	}
	return response, err
}

func (s *Service) ProcessConnectorWebhook(ctx context.Context, request *moengageVendorPb.ProcessConnectorWebhookRequest) (*emptypb.Empty, error) {
	// Validate request
	if request.GetUserId() == "" || request.GetArea() == "" || request.GetUseCase() == "" || request.GetCampaignName() == "" {
		logger.Error(ctx, "mandatory params missing",
			zap.String("userId", request.GetUserId()),
			zap.String("area", request.GetArea()),
			zap.String("useCase", request.GetUseCase()),
			zap.String("campaignName", request.GetCampaignName()))
		return nil, status.Errorf(codes.InvalidArgument, "mandatory params missing")
	}

	// Get area enum from string
	area, err := getAreaEnumFromString(ctx, request.GetArea())
	if err != nil {
		logger.Error(ctx, "unsupported area type", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "error: %s", err.Error())
	}

	// Get use case enum from string
	useCase, err := getUseCaseEnumFromString(ctx, request.GetUseCase())
	if err != nil {
		logger.Error(ctx, "unsupported use case type", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "error: %s", err.Error())
	}

	// Get actor ID from user ID
	getActorIdByVendorIdResponse, err := s.vendorMapping.GetActorIdByVendorId(ctx, &vendormappingPb.GetActorIdByVendorIdRequest{
		VendorId: &vendormappingPb.GetActorIdByVendorIdRequest_MoengageId{
			MoengageId: request.GetUserId(),
		},
	})
	if rpcErr := epifigrpc.RPCError(getActorIdByVendorIdResponse, err); rpcErr != nil {
		logger.Error(ctx, "error while fetching actor id mapped to moengage user id",
			zap.String(logger.USER_ID, request.GetUserId()),
			zap.Error(rpcErr))
		return nil, fmt.Errorf("error while fetching actor id mapped to moengage user id, err: %w", rpcErr)
	}

	// Get area processor
	ctx = epificontext.CtxWithActorId(ctx, getActorIdByVendorIdResponse.GetActorId())
	areaProcessor, err := s.webhookAreaProcessorFactory.GetAreaProcessor(area)
	if err != nil {
		logger.Error(ctx, "error getting area processor",
			zap.String("area", request.GetArea()),
			zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, "unsupported area: %s", request.GetArea())
	}

	// Process the webhook
	err = areaProcessor.Process(ctx, &webhookAreaProcessor.ProcessRequest{
		ActorId:      getActorIdByVendorIdResponse.GetActorId(),
		Area:         area,
		UseCase:      useCase,
		CampaignName: request.GetCampaignName(),
		CampaignMeta: request.GetCampaignMeta(),
	})
	if err != nil {
		logger.Error(ctx, "error processing webhook",
			zap.String("area", request.GetArea()),
			zap.String("useCase", request.GetUseCase()),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "error processing webhook: %v", err)
	}

	return &emptypb.Empty{}, nil
}
