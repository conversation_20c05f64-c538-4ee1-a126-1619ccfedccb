package stocks

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	usStocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	moengageStocksPb "github.com/epifi/gamma/api/vendors/moengage/stocks"
	userAttributeFetcher "github.com/epifi/gamma/vendornotification/notifications/moengage/userattributesfetcher"
)

// GetStockDetails returns enriched details about a stock on the basis of stock id
// Stock details are enriched in a best effort manner with the data available from domain service
func (s *StocksAttributeProvider) GetStockDetails(ctx context.Context, requestMeta map[userAttributeFetcher.RequestMetadataKey]string) (*moengageStocksPb.Stock, error) {
	stockId, ok := requestMeta[userAttributeFetcher.RequestMetadataKey_StockId]
	if !ok {
		return nil, fmt.Errorf("missing mandatory key %v", userAttributeFetcher.RequestMetadataKey_StockId)
	}

	var (
		stock                *usStocksCatalogPb.Stock
		stockPricesForPeriod []*usStocksCatalogPb.StockPricesForPeriod
	)

	g, gctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		res, err := s.ussCatalogClient.GetStocks(gctx, &usStocksCatalogPb.GetStocksRequest{
			Identifiers: &usStocksCatalogPb.GetStocksRequest_StockIds{
				StockIds: &usStocksCatalogPb.RepeatedStrings{Ids: []string{stockId}},
			},
		})
		if err = epifigrpc.RPCError(res, err); err != nil {
			return fmt.Errorf("failed to get stock with id %s: %w", stockId, err)
		}
		stocksMap := res.GetStocks()
		if stocksMap == nil {
			return fmt.Errorf("stocks map cannot be empty")
		}
		stck, ok := stocksMap[stockId]
		if !ok {
			return fmt.Errorf("stock with id %s not found in stock map", stockId)
		}
		stock = stck
		return nil
	})
	g.Go(func() error {
		res, err := s.ussCatalogClient.GetHistoricalStockPrices(gctx, &usStocksCatalogPb.GetHistoricalStockPricesRequest{StockId: stockId})
		if err = epifigrpc.RPCError(res, err); err != nil {
			if res.GetStatus().IsResourceExhausted() {
				return errors.Wrap(epifierrors.ErrResourceExhausted, "resource exhausted while fetching historical stock prices")
			}
			return fmt.Errorf("failed to get historical stock prices for stock id %s, %w", stockId, err)
		}
		stockPricesForPeriod = res.GetStockPricesForPeriods()
		return nil
	})

	if err := g.Wait(); err != nil {
		return nil, fmt.Errorf("failed to get stock details: %w", err)
	}
	stockMsg, err := moengageStocksPb.NewUsStockMessageParam(stock)
	if err != nil {
		return nil, fmt.Errorf("failed to create us stocks moengage message: %w", err)
	}
	stockMsg.WithUsStockHistoricalPerformance(stockPricesForPeriod)
	return stockMsg, nil
}
