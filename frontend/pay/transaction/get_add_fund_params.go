package transaction

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"

	"context"
	"errors"
	"fmt"
	"math"
	"time"

	"github.com/epifi/be-common/pkg/errgroup"

	"go.uber.org/zap"

	headerPb "github.com/epifi/gamma/api/frontend/header"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/pay/transaction/addfunds"
	"github.com/epifi/gamma/frontend/tiering/helper"

	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	bankCustPb "github.com/epifi/gamma/api/bankcust"
	feTransactionPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	orderPb "github.com/epifi/gamma/api/order"
	beRewardOffersPb "github.com/epifi/gamma/api/rewards/rewardoffers"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/config"
	afv2Params "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params"
	payFeEvents "github.com/epifi/gamma/frontend/pay/transaction/events"
	"github.com/epifi/gamma/pkg/feature/release"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

var (
	entryPointToConfigMap = map[feTransactionPb.UIEntryPoint]string{
		feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS:   "OnboardAddFunds",
		feTransactionPb.UIEntryPoint_TRANSFER_IN:         "TransferIn",
		feTransactionPb.UIEntryPoint_ACCOUNT_DETAILS:     "TransferIn",
		feTransactionPb.UIEntryPoint_ACCOUNT_SUMMARY:     "TransferIn",
		feTransactionPb.UIEntryPoint_HOME:                "TransferIn",
		feTransactionPb.UIEntryPoint_REFERRALS:           "TransferIn",
		feTransactionPb.UIEntryPoint_HOME_PERSISTENT_CTA: "TransferIn",
		feTransactionPb.UIEntryPoint_DEPOSIT_CREATION:    "TransferIn",
		feTransactionPb.UIEntryPoint_BONUS_JAR_CREATION:  "TransferIn",
		feTransactionPb.UIEntryPoint_CHEQUEBOOK:          "TransferIn",
	}
)

var (
	uiEntrypointConfigFetchErr = fmt.Errorf("error fetching ui entrypoint config")
)

const DefaultUiEntrypoint = "Default"

// GetAddFundParams RPC method to return parameters required for the add funds screen
// Takes in ui entry point to send different parameters based on the ui entry point
// nolint: funlen
func (s *Service) GetAddFundParams(ctx context.Context, req *feTransactionPb.GetAddFundParamsRequest) (*feTransactionPb.GetAddFundParamsResponse, error) {
	res := &feTransactionPb.GetAddFundParamsResponse{}
	uiEntryPoint := req.GetUiEntryPoint()
	actorId := req.GetReq().GetAuth().GetActorId()
	appPlatform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAuth().GetDevice().GetAppVersion()

	// gather data required for add funds
	collectedData, gatherDataErr := s.gatherDataForAddFunds(ctx, actorId,
		s.conf.MinKycMandatoryAddFundConfig().IsEnabled() && uiEntryPoint == feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS)
	if gatherDataErr != nil {
		logger.Warn("error gathering data", zap.Error(gatherDataErr))
	}

	// compute add funds version depending on the flags
	var addFundsVersion feTransactionPb.AddFundsVersion
	switch {
	case collectedData.IsV4EnabledData():
		addFundsVersion = feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V4
	case collectedData.IsV3EnabledData():
		addFundsVersion = feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V3
	case s.isAddFundsV2EnabledForPlatformAndAppVersion(ctx, appPlatform, appVersion) &&
		!s.conf.AddFundsV2Params().SkipAddFundsV2():
		addFundsVersion = feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V2
	default:
		addFundsVersion = feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V1
	}

	var tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails
	// tiering pitch can be disabled if user already have enough funds or user is min kyc
	var isTieringPitchEnabled bool
	if addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V2 ||
		addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V3 ||
		addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V4 {
		var getAddFundsDetailsErr error
		isTieringPitchEnabled, tieringPitchDetails, getAddFundsDetailsErr = s.tieringAddFundsManager.GetAddFundsDetails(ctx, actorId, appPlatform, appVersion, uiEntryPoint, collectedData.TrialsResponse())
		if getAddFundsDetailsErr != nil {
			logger.Error(ctx, "error in fetching tiering pitch details", zap.Error(getAddFundsDetailsErr))
		}
	}

	// if tiering pitch is disabled in v2, redirect to v1
	if addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V2 && !isTieringPitchEnabled {
		addFundsVersion = feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V1
	}

	populateResponseErrGrp, _ := errgroup.WithContext(ctx)
	// TODO(sainath): ios always needs v1 details, keeping this until client side fix is raised
	if addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V1 || appPlatform == commontypes.Platform_IOS {
		populateResponseErrGrp.Go(func() error {
			return s.populateV1RelatedDetails(ctx, req, res)
		})
	}

	res.AddFundsVersion = addFundsVersion
	// We need to check for tiering pitch only if the ui entry point is not onboarding add funds and if
	// add funds v2 is enabled for this platform
	if req.GetUiEntryPoint() != feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS &&
		addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V2 &&
		isTieringPitchEnabled {
		populateResponseErrGrp.Go(func() error {
			return s.populateV2RelatedDetails(ctx, tieringPitchDetails, appPlatform, appVersion, res)
		})
	}

	if req.GetUiEntryPoint() != feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS &&
		(addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V3 || addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V4) {
		populateResponseErrGrp.Go(func() error {
			return s.populateV3RelatedDetails(ctx, isTieringPitchEnabled, tieringPitchDetails, appPlatform, appVersion, res, req.GetUiEntryPoint())
		})
	}

	if req.GetUiEntryPoint() != feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS && addFundsVersion == feTransactionPb.AddFundsVersion_ADD_FUNDS_VERSION_V4 {
		populateResponseErrGrp.Go(func() error {
			return s.populateV4RelatedDetails(ctx, actorId, res)
		})
	}

	if req.GetUiEntryPoint() == feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_INFINITE ||
		req.GetUiEntryPoint() == feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PLUS ||
		req.GetUiEntryPoint() == feTransactionPb.UIEntryPoint_ALL_PLANS_JOIN_PRIME {
		populateResponseErrGrp.Go(func() error {
			s.setTierMovementFeedbackEngineInfoInResHeader(ctx, actorId, res)
			return nil
		})
	}

	if _, ok := helper.UIEntryPointToExternalTierMap[req.GetUiEntryPoint()]; ok {
		populateResponseErrGrp.Go(func() error {
			s.setDropOffBottomSheet(ctx, req.GetUiEntryPoint(), res, &tieringPb.GetDropOffBottomSheetRequest{
				ActorId: req.GetReq().GetAuth().GetActorId(),
			}, collectedData.TrialsResponse())
			return nil
		})
	}

	populateResponseErr := populateResponseErrGrp.Wait()
	if populateResponseErr != nil {
		if errors.Is(populateResponseErr, uiEntrypointConfigFetchErr) {
			logger.Error(ctx, "error fetching ui entrypoint config", zap.Error(populateResponseErr))
			return &feTransactionPb.GetAddFundParamsResponse{
				Status: rpcPb.StatusInvalidArgument(),
				RespHeader: &headerPb.ResponseHeader{
					Status: rpcPb.StatusInvalidArgument(),
				},
			}, nil
		}
		logger.Error(ctx, "error in populating response", zap.Error(populateResponseErr))
		return &feTransactionPb.GetAddFundParamsResponse{
			Status: rpcPb.StatusInternal(),
			RespHeader: &headerPb.ResponseHeader{
				Status: rpcPb.StatusInternal(),
			},
		}, nil
	}

	if collectedData.BankCustomerData().GetBankCustomer().GetKycLevelUpdateFlow() == bankCustPb.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC {
		res.MinAmount = typesPb.GetFromBeMoney(s.conf.MinKycMandatoryAddFundConfig().MinimumAmount())
	}
	logger.Debug(ctx, "showing add funds version", zap.String(logger.VERSION_ID, addFundsVersion.String()))

	platform, version := epificontext.AppPlatformAndVersion(ctx)
	if platform == commontypes.Platform_ANDROID && version >= s.conf.AddFundsParams().IntentNavigateToPayStatusAndroidVersion() {
		res.IntentNavigateToPayStatus = true
	}
	if platform == commontypes.Platform_ANDROID && version >= s.conf.AddFundsParams().CollectNavigateToPayStatusAndroidVersion() {
		res.CollectNavigateToPayStatus = true
	}

	// event for analysis
	goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
		s.emitAddFundsParamsLoadedEvent(ctx, actorId, uiEntryPoint.String(), isTieringPitchEnabled, collectedData)
	})

	res.Status = rpcPb.StatusOk()
	return res, nil
}

// emitAddFundsParamsLoadedEvent fetches the current tier and the account balance of the user
// And emit an event
func (s *Service) emitAddFundsParamsLoadedEvent(ctx context.Context, actorId, uiEntryPoint string, isTieringPitchEnabled bool, collectedData *addfunds.CollectedData) {
	var currentTier string
	var balanceResp *moneyPb.Money

	errGrp, _ := errgroup.WithContext(ctx)

	// fetch the current tier of the user
	errGrp.Go(func() error {
		getTierAtTimeRes, getTierAtTimeErr := s.tieringClient.GetTierAtTime(ctx, &tieringPb.GetTierAtTimeRequest{
			ActorId:       actorId,
			TierTimestamp: timestampPb.Now(),
		})
		if rpcErr := epifigrpc.RPCError(getTierAtTimeRes, getTierAtTimeErr); rpcErr != nil {
			currentTier = tieringExtPb.Tier_TIER_UNSPECIFIED.String()
			return rpcErr
		}
		currentTier = getTierAtTimeRes.GetTierInfo().GetTier().String()
		return nil
	})

	// fetch the account balance of the user
	errGrp.Go(func() error {
		var err error
		balanceResp, err = s.dataCollector.GetBalance(ctx, actorId)
		if err != nil {
			return err
		}
		return nil
	})
	// gracefully handling errors from the goroutine as event emission should not be affected for failure in fetching current tier/balance
	goErr := errGrp.Wait()
	if goErr != nil {
		logger.Error(ctx, "error in getting current tier or account balance ", zap.Error(goErr), zap.String(logger.ACTOR_ID_V2, actorId))
	}

	// default balance bucket is "0"
	balanceBucket := "0"
	if balanceResp != nil {
		balanceBucket = tieringPkg.DetermineBalanceBucket(balanceResp.GetUnits())
	}

	s.eventBroker.AddToBatch(ctx, payFeEvents.NewAddFundsScreenLoadRequestBackend(
		actorId,
		currentTier,
		balanceBucket,
		uiEntryPoint,
		// if pitching is enabled, progress bar will be shown
		isTieringPitchEnabled,
		collectedData.TrialsResponse(),
	))
}

func (s *Service) populateV4RelatedDetails(ctx context.Context, actorId string, res *feTransactionPb.GetAddFundParamsResponse) error {
	ctaOperationDetails, getCtaOperationDetailsErr := s.getCtaOperationDetails(ctx, actorId)
	if getCtaOperationDetailsErr != nil {
		// gracefully ignoring error and will fall back to either V1/V2/V3
		logger.Error(ctx, "error getting details for add funds v4, falling back", zap.Error(getCtaOperationDetailsErr))
	} else {
		res.AddFundsCtaOperationDetails = ctaOperationDetails
		if res.GetAddFundsV3ScreenDetails().GetPrimaryAddFundsDetails().GetDefaultAmount() != nil {
			res.GetAddFundsV3ScreenDetails().GetPrimaryAddFundsDetails().GetDefaultAmount().MaxAmount = amount(math.MaxInt64)
		}
	}
	return nil
}

// isAMBCardEnabled determines if the AMB card should be shown instead of the tiering card
func (s *Service) isAMBCardEnabled(ctx context.Context, actorId string) bool {
	// TODO: Replace this with a proper feature flag check when ready
	// For now, use a simple release flag
	ambFeatureConstraint := release.NewCommonConstraintData(typesPb.Feature_FEATURE_AMB_ENTRYPOINT_BANNER).WithActorId(actorId)
	isAMBEnabled, err := s.releaseEvaluator.Evaluate(ctx, ambFeatureConstraint)
	if err != nil {
		logger.Error(ctx, "error evaluating for AMB add funds card feature", zap.Error(err))
		return false
	}
	return isAMBEnabled
}

func (s *Service) populateV3RelatedDetails(ctx context.Context, isTieringPitchEnabled bool, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails,
	appPlatform commontypes.Platform, appVersion uint32, res *feTransactionPb.GetAddFundParamsResponse, uiEntryPoint feTransactionPb.UIEntryPoint) error {

	actorId := epificontext.ActorIdFromContext(ctx)
	isAMBEnabled := s.isAMBCardEnabled(ctx, actorId)

	// If from AMB details entry point and AMB card is enabled, get AMB related params
	if uiEntryPoint == feTransactionPb.UIEntryPoint_AMB_DETAILS && isAMBEnabled {
		addFundsV3Params, getAddFundsV3ParamsErr := afv2Params.GetAMBAddFundsV3Params(
			ctx,
			s.conf,
			actorId,
			s.dataCollector,
			s.tieringPinotClient,
			appPlatform,
			appVersion,
			s.tieringClient,
		)
		if getAddFundsV3ParamsErr != nil {
			// gracefully ignoring error and will fall back to either V1 or tiering V3
			logger.Error(ctx, "error getting AMB details for add funds v3, falling back to tiering", zap.Error(getAddFundsV3ParamsErr))
		} else {
			res.AddFundsV3ScreenDetails = addFundsV3Params
			return nil
		}
	}

	// Default to tiering add funds params
	addFundsV3Params, getAddFundsV3ParamsErr := afv2Params.GetTieringAddFundsV3Params(ctx, s.conf, isTieringPitchEnabled, tieringPitchDetails, appPlatform, appVersion)
	if getAddFundsV3ParamsErr != nil {
		// gracefully ignoring error and will fall back to either V1 or V2(if v2 is enabled)
		logger.Error(ctx, "error getting details for add funds v3, falling back", zap.Error(getAddFundsV3ParamsErr))
	} else {
		res.AddFundsV3ScreenDetails = addFundsV3Params
	}
	return nil
}

func (s *Service) populateV2RelatedDetails(ctx context.Context, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails,
	appPlatform commontypes.Platform, appVersion uint32, res *feTransactionPb.GetAddFundParamsResponse) error {
	// Check whether tiering is enabled for actor or not
	// This involves checking whether tiering is enabled for the actor id as a whole
	// It also checks if user is in cool or grace period
	// Also checks whether user is in max tier or not
	// If we need to pitch the screen, the details are filled accordingly
	addFundsV2Params, getAddFundsV2ParamsErr := afv2Params.GetTieringAddFundsV2Params(ctx, s.conf, tieringPitchDetails, appPlatform, appVersion)
	if getAddFundsV2ParamsErr != nil {
		logger.Error(ctx, "error fetching add funds v2 params", zap.Error(getAddFundsV2ParamsErr))
	} else {
		res.IsTieringEnabled = commontypes.BooleanEnum_TRUE
		res.AddFundsV2TieringScreen = addFundsV2Params
	}
	return nil
}

// nolint: funlen
func (s *Service) populateV1RelatedDetails(ctx context.Context, req *feTransactionPb.GetAddFundParamsRequest,
	res *feTransactionPb.GetAddFundParamsResponse) error {
	actorId := req.GetReq().GetAuth().GetActorId()
	var suggestedAmounts []*feTransactionPb.GetAddFundParamsResponse_SuggestedAmount
	conf, option, getAddFundsParamsConfigErr := s.getAddFundsParamsConfig(ctx, req.GetUiEntryPoint(), actorId)
	if getAddFundsParamsConfigErr != nil {
		return fmt.Errorf("%w: %w", uiEntrypointConfigFetchErr, getAddFundsParamsConfigErr)
	}

	for _, suggestedAmountConf := range conf.SuggestedAmounts {
		suggestedAmount := &feTransactionPb.GetAddFundParamsResponse_SuggestedAmount{
			Amount: typesPb.GetFromBeMoney(suggestedAmountConf.Amount),
		}
		for _, tagConf := range suggestedAmountConf.Tags {
			suggestedAmount.Tags = append(suggestedAmount.GetTags(), &feTransactionPb.GetAddFundParamsResponse_Tag{
				Title:   tagConf.Title,
				IconUrl: tagConf.IconUrl,
			})
		}

		suggestedAmounts = append(suggestedAmounts, suggestedAmount)
	}
	res.SuggestedAmounts = suggestedAmounts
	res.DefaultAmount = typesPb.GetFromBeMoney(conf.DefaultAmount)
	res.MinAmount = typesPb.GetFromBeMoney(conf.MinAmount)
	res.MaxAmount = typesPb.GetFromBeMoney(conf.MaxAmount)
	orderRes, err := s.orderClient.GetOrdersForActor(ctx, &orderPb.GetOrdersForActorRequest{
		ActorId:         req.GetReq().GetAuth().GetActorId(),
		PageSize:        1,
		TransactionType: orderPb.GetOrdersForActorRequest_BOTH,
		FieldMask:       []orderPb.OrderFieldMask{orderPb.OrderFieldMask_ALL},
		StatusFilters:   []orderPb.GetOrdersForActorRequest_OrderStatusFilter{orderPb.GetOrdersForActorRequest_SUCCESS},
		ToTime:          timestampPb.Now(),
		SortBy:          orderPb.OrderFieldMask_UPDATED_AT,
	})
	if err = epifigrpc.RPCError(orderRes, err); err != nil {
		return fmt.Errorf("error while fetching orders for actor: %w", err)
	}
	rewardsReq := &beRewardOffersPb.GetRewardOffersForScreenRequest{}
	switch {
	case (req.GetUiEntryPoint() == feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS || len(orderRes.GetOrders()) != 0) &&
		conf.RewardsBanner != nil:
		res.RewardsBanner = &feTransactionPb.GetAddFundParamsResponse_RewardsBanner{
			Description: conf.RewardsBanner.Description,
			IconUrl:     conf.RewardsBanner.IconUrl,
		}
	case len(orderRes.GetOrders()) == 0:
		res.RewardsBanner = &feTransactionPb.GetAddFundParamsResponse_RewardsBanner{
			Description: s.FirstTransactionRewardsBanner.Description,
			IconUrl:     s.FirstTransactionRewardsBanner.IconUrl,
		}
	}
	if len(orderRes.GetOrders()) == 0 {
		rewardsReq.Filter = &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			Tags: []beRewardOffersPb.RewardOfferTag{beRewardOffersPb.RewardOfferTag_FIRST_FUND_ADDITION}}
	} else {
		rewardsReq.Filter = &beRewardOffersPb.GetRewardOffersForScreenRequest_Filter{
			Tags: []beRewardOffersPb.RewardOfferTag{beRewardOffersPb.RewardOfferTag_SUBSEQUENT_FUND_ADDITION}}
	}
	rewardsReq.ActorId = req.GetReq().GetAuth().GetActorId()
	rewardsRes, err := s.rewardsOfferClient.GetRewardOffersForScreen(ctx, rewardsReq)
	if err = epifigrpc.RPCError(rewardsRes, err); err != nil {
		// just logging the error, we will not fail the loading off add funds screen
		logger.Error(ctx, "error fetching reward offers", zap.Error(err))
	}

	if len(rewardsRes.GetRewardOffers()) > 0 {
		res.RewardsBanner = &feTransactionPb.GetAddFundParamsResponse_RewardsBanner{
			Description: rewardsRes.GetRewardOffers()[0].GetDisplayMeta().GetShortDesc(),
			IconUrl:     rewardsRes.GetRewardOffers()[0].GetDisplayMeta().GetIcon(),
		}
	} else {
		// no reward is active
		res.RewardsBanner = nil
	}

	// TODO(rchougule): Please burn this code within few weeks
	// BURN START =====================================================================================
	env, _ := cfg.GetEnvironment()
	if cfg.IsProdEnv(env) || cfg.IsStagingEnv(env) {
		res.RewardsBanner = &feTransactionPb.GetAddFundParamsResponse_RewardsBanner{
			Description: "You can add up to ₹2000 per feTransactionPb via Fi. To add higher amounts - send directly using PhonePe, GPay, NEFT or IMPS.",
			IconUrl:     "https://epifi-icons.pointz.in/home/<USER>",
		}
	}
	// BURN END =============================================================================================================================================

	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.persistAddFundOptionIfChanged(ctx, res.RewardsBanner, req.GetReq().GetAuth().GetActorId(), conf)
	})

	goroutine.Run(ctx, 10*time.Second, func(ctx context.Context) {
		s.eventBroker.AddToBatch(epificontext.WithEventAttributes(ctx), payFeEvents.NewConfiguredAddFundsOptions(time.Now(), req.GetReq().GetAuth().GetActorId(), payFeEvents.SUCCESS, req.GetUiEntryPoint(), option))
	})

	res.Remarks = conf.Remarks
	res.IsBlocking = conf.IsBlocking
	return nil
}

func (s *Service) gatherDataForAddFunds(ctx context.Context, actorId string, toCollectBankCustomerData bool) (*addfunds.CollectedData, error) {
	gatherDataErrGrp, _ := errgroup.WithContext(ctx)
	collectedData := &addfunds.CollectedData{}
	if toCollectBankCustomerData {
		gatherDataErrGrp.Go(func() error {
			getBankCustResp, bankCustErr := s.bankCustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
				Vendor: commonvgpb.Vendor_FEDERAL_BANK,
				Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
					ActorId: actorId,
				},
			})
			if rpcErr := epifigrpc.RPCError(getBankCustResp, bankCustErr); rpcErr != nil {
				return fmt.Errorf("failed to get user: %w", rpcErr)
			}
			collectedData.SetBankCustomerData(getBankCustResp)
			return nil
		})
	}
	gatherDataErrGrp.Go(func() error {
		v3FeatureConstraint := release.NewCommonConstraintData(typesPb.Feature_ADD_FUNDS_V3).WithActorId(actorId)
		isV3Enabled, evaluateErr := s.releaseEvaluator.Evaluate(ctx, v3FeatureConstraint)
		if evaluateErr != nil {
			return fmt.Errorf("error evaluating for add funds v3 feature, :%w", evaluateErr)
		}
		collectedData.SetIsV3EnabledData(isV3Enabled)
		return nil
	})
	gatherDataErrGrp.Go(func() error {
		v4FeatureConstraint := release.NewCommonConstraintData(typesPb.Feature_ADD_FUNDS_V4).WithActorId(actorId)
		isV4Enabled, evaluateErr := s.releaseEvaluator.Evaluate(ctx, v4FeatureConstraint)
		if evaluateErr != nil {
			return fmt.Errorf("error evaluating for add funds v4 feature, :%w", evaluateErr)
		}
		collectedData.SetIsV4EnabledData(isV4Enabled)
		return nil
	})
	gatherDataErrGrp.Go(func() error {
		trialsResponse, trialRespErr := s.tieringClient.GetTrialDetails(ctx, &tieringPb.GetTrialDetailsRequest{ActorId: actorId})
		if rpcErr := epifigrpc.RPCError(trialsResponse, trialRespErr); rpcErr != nil {
			logger.Error(ctx, "error getting trial details for actor", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
			return nil
		}

		collectedData.SetTrialsResponse(trialsResponse)
		return nil
	})

	return collectedData, gatherDataErrGrp.Wait()
}

// returns true if v2 flow is enabled for platform, false otherwise
// also returns false if platform is unidentifiable
func (s *Service) isAddFundsV2EnabledForPlatformAndAppVersion(ctx context.Context, appPlatform commontypes.Platform, appVersion uint32) bool {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		return appVersion >= s.conf.AddFundsV2Params().ReleaseParams().MinVersionAndroid()
	case commontypes.Platform_IOS:
		return appVersion >= s.conf.AddFundsV2Params().ReleaseParams().MinVersionIos()
	default:
		logger.Error(ctx, "received unidentifiable app platform, returning false", zap.Any(logger.PLATFORM, appPlatform))
		return false
	}
}

// getAddFundsParamsConfig returns the config based on the ui entry point and actorId
// logic for fetching option for ui entry point account number % len(options available for entry point)
func (s *Service) getAddFundsParamsConfig(ctx context.Context, uiEntryPoint feTransactionPb.UIEntryPoint, actorId string) (*config.AddFundsParamOptions, int, error) {
	var (
		activeAddFundOptions []*config.AddFundsParamOptions
		conf                 []*config.AddFundsParamOptions
	)
	entryPointConfig, ok := entryPointToConfigMap[uiEntryPoint]
	if !ok {
		entryPointConfig = DefaultUiEntrypoint
		logger.Info(ctx, fmt.Sprintf("Config doesn't exist for the UIEntryPoint: %v", uiEntryPoint.String()))
	}

	if conf, ok = s.AddFundsScreenParams[entryPointConfig]; ok {
		enabledOptions := 0
		for _, entryOption := range conf {
			if entryOption.IsEnabled {
				activeAddFundOptions = append(activeAddFundOptions, entryOption)
				enabledOptions += 1
			}
		}
		option, err := payPkg.GetAddFundsOption(actorId, enabledOptions)
		if err != nil {
			return nil, -1, fmt.Errorf("error fetching option for actor id: %s, err: %w", actorId, err)
		}
		if activeAddFundOptions != nil {
			return activeAddFundOptions[option], option, nil
		}
	}
	return nil, -1, fmt.Errorf("config not found for entryPoint: %s", uiEntryPoint)
}

// persistAddFundOptionIfChanged persists add fund option for the given actor
func (s *Service) persistAddFundOptionIfChanged(
	ctx context.Context,
	rewardsBanner *feTransactionPb.GetAddFundParamsResponse_RewardsBanner,
	actorId string,
	addFundParamConf *config.AddFundsParamOptions,
) {
	var (
		suggestedAmounts []*orderPb.AmountWithTag
	)
	addFundOption := &orderPb.AddFundOption{
		DefaultAmount: addFundParamConf.DefaultAmount,
		MinAmount:     addFundParamConf.MinAmount,
		MaxAmount:     addFundParamConf.MaxAmount,
		RewardsBanner: &orderPb.AddFundOption_RewardsBanner{
			Description: rewardsBanner.GetDescription(),
			IconUrl:     rewardsBanner.GetIconUrl(),
		},
		IsBlocking: addFundParamConf.IsBlocking,
	}

	for _, suggestedAmountConf := range addFundParamConf.SuggestedAmounts {
		suggestedAmount := &orderPb.AmountWithTag{
			Amount: suggestedAmountConf.Amount,
		}
		for _, tagConf := range suggestedAmountConf.Tags {
			suggestedAmount.Tags = append(suggestedAmount.Tags, &orderPb.AmountWithTag_Tag{
				Title:   tagConf.Title,
				IconUrl: tagConf.IconUrl,
			})
		}
		suggestedAmounts = append(suggestedAmounts, suggestedAmount)
	}
	addFundOption.SuggestedAmounts = suggestedAmounts

	res, err := s.orderClient.PersistAddFundOption(ctx, &orderPb.PersistAddFundOptionRequest{
		ActorId:       actorId,
		AddFundOption: addFundOption,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		// given this is a user facing rpcPb, failure in persisting add fund option should
		// not fail the rpcPb, so just logging the error
		logger.Error(ctx, "error persisting add fund option", zap.Error(err), zap.String(logger.ACTOR_ID, actorId))
	}
}

func (s *Service) isAddFundsMandatory(ctx context.Context, actorId string, uiEntryPoint feTransactionPb.UIEntryPoint) bool {
	if !s.conf.MinKycMandatoryAddFundConfig().IsEnabled() {
		return false
	}
	getBankCustResp, errBankCust := s.bankCustClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(getBankCustResp, errBankCust); rpcErr != nil {
		logger.Error(ctx, "failed to get user", zap.Error(rpcErr))
		return false
	}

	return getBankCustResp.GetBankCustomer().GetKycLevelUpdateFlow() == bankCustPb.KycLevelUpdateFlow_KYC_LEVEL_UPDATE_FLOW_VKYC_ONBOARDING_DEDUPE_PARTIAL_KYC &&
		uiEntryPoint == feTransactionPb.UIEntryPoint_ONBOARD_ADD_FUNDS
}

func (s *Service) setTierMovementFeedbackEngineInfoInResHeader(ctx context.Context, actorId string, res *feTransactionPb.GetAddFundParamsResponse) {
	if s.conf.AddFundsParams().IsTierMovementDropOffFeedbackFlowEnabled() {
		feedbackEngineInfo, feedbackFlowErr := s.tierMovementFeedbackFlowSvcClient.GetFeedbackEngineInfo(ctx, actorId, typesPb.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF)
		if feedbackFlowErr != nil {
			logger.Error(ctx, "error in setting feedback flow id in resp header", zap.Any(logger.ACTOR_ID_V2, actorId), zap.Error(feedbackFlowErr))
		}
		if res.GetRespHeader() == nil {
			res.RespHeader = &headerPb.ResponseHeader{FeedbackEngineInfo: feedbackEngineInfo}
		} else {
			res.RespHeader.FeedbackEngineInfo = feedbackEngineInfo
		}
	}
}

func (s *Service) setDropOffBottomSheet(ctx context.Context, uIEntryPoint feTransactionPb.UIEntryPoint, res *feTransactionPb.GetAddFundParamsResponse, req *tieringPb.GetDropOffBottomSheetRequest, trialsResponse *tieringPb.GetTrialDetailsResponse) {
	if !s.conf.Tiering().TierDropOffBottomSheet().ShouldShowAddFunds() {
		return
	}
	var (
		tierToPitch tieringExtPb.Tier
		ok          bool
	)
	if tierToPitch, ok = helper.UIEntryPointToExternalTierMap[uIEntryPoint]; !ok {
		return
	}

	req.TieringPitchMethod = tieringPb.GetDropOffBottomSheetRequest_ADD_FUNDS
	req.TierToPitch = tierToPitch

	// skip setting drop off bottom sheet if user is eligible for trial and the trial tier is same as the tier to pitch
	if trialsResponse.GetIsEligibleForTrial() && trialsResponse.GetEligibleTrialTier() == tierToPitch {
		return
	}

	tieringEssentials, err := s.dataCollector.GetTieringEssentials(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching tiering essentials", zap.String("actorId", req.GetActorId()), zap.Error(err))
		return
	}
	if !tieringEssentials.IsMultipleWaysToEnterTieringEnabledForActor {
		return
	}
	dropOffBottomSheetResponse, dropOffBottomSheetResponseErr := helper.GetDropOffBottomSheet(req, tieringEssentials.GetTierCriteriaMinValuesMap(), tieringEssentials.GetIsUSStocksAccountActive())

	if dropOffBottomSheetResponseErr != nil {
		logger.Error(ctx, "error fetching drop off bottom sheet response", zap.String("actorId", req.GetActorId()), zap.Error(dropOffBottomSheetResponseErr))
		return
	}

	if dropOffBottomSheetResponse != nil {
		res.BackNavigationDropOffDeeplink = dropOffBottomSheetResponse.GetDeeplink()
	}
}
