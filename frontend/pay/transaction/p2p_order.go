package transaction

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"
	"time"

	"github.com/golang/protobuf/proto"
	"github.com/samber/lo"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	"github.com/epifi/gamma/api/accounts"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/frontend/deeplink/timeline"
	"github.com/epifi/gamma/api/frontend/header"
	"github.com/epifi/gamma/api/frontend/pay"
	"github.com/epifi/gamma/api/frontend/pay/transaction"
	feQrPb "github.com/epifi/gamma/api/frontend/qr"
	beOrderPb "github.com/epifi/gamma/api/order"
	bePaymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	"github.com/epifi/gamma/api/savings"
	types "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	upiPb "github.com/epifi/gamma/api/upi"
	upiDomainPb "github.com/epifi/gamma/api/upi/domain_model"
	"github.com/epifi/gamma/api/vendors"
	"github.com/epifi/gamma/frontend/events"
	"github.com/epifi/gamma/frontend/pay/internal"
	"github.com/epifi/gamma/pkg/feature/release"
	errors2 "github.com/epifi/gamma/pkg/frontend/errors"
	payPkg "github.com/epifi/gamma/pkg/pay"
	"github.com/epifi/gamma/pkg/qr"
	"github.com/epifi/gamma/pkg/upi"
)

var (
	statusInvalidSignature rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(uint32(transaction.CreateFundTransferOrderResponse_INVALID_SIGNATURE),
			"Cannot verify the QR", "invalid signature passed in urn")
	}
	statusCoolOffValidationFailedFundTransfer rpc.StatusFactory = func() *rpc.Status {
		return rpc.NewStatus(
			uint32(transaction.CreateFundTransferOrderResponse_COOL_OFF_VALIDATION_FAILED),
			"Device is in cool off period. Total amount of the transaction should be less then 5k",
			"Device is in cool off period. Total amount of the transaction should be less then 5k",
		)
	}

	transactionUIEntryPointToUIEntryPointMap = map[timeline.TransactionUIEntryPoint]transaction.UIEntryPoint{
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_TIMELINE:                      transaction.UIEntryPoint_TIMELINE,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_QR_CODE:                       transaction.UIEntryPoint_QR_CODE,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_INTENT:                        transaction.UIEntryPoint_INTENT,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS:             transaction.UIEntryPoint_ONBOARD_ADD_FUNDS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING: transaction.UIEntryPoint_UI_ENTRY_POINT_ONBOARD_ADD_FUNDS_PRE_FUNDING,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_TRANSFER_IN:                   transaction.UIEntryPoint_TRANSFER_IN,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_HOME:                          transaction.UIEntryPoint_HOME,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ACCOUNT_DETAILS:               transaction.UIEntryPoint_ACCOUNT_DETAILS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ACCOUNT_SUMMARY:               transaction.UIEntryPoint_ACCOUNT_SUMMARY,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_REFERRALS:                     transaction.UIEntryPoint_REFERRALS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_HOME_PERSISTENT_CTA:           transaction.UIEntryPoint_HOME_PERSISTENT_CTA,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_DEPOSIT_CREATION:              transaction.UIEntryPoint_DEPOSIT_CREATION,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_BONUS_JAR_CREATION:            transaction.UIEntryPoint_BONUS_JAR_CREATION,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_MF_BUY_ONE_TIME:               transaction.UIEntryPoint_MF_BUY_ONE_TIME,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_CHEQUEBOOK:                    transaction.UIEntryPoint_CHEQUEBOOK,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ASK_FI:                        transaction.UIEntryPoint_ASK_FI,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_QR_SHARE_AND_PAY:              transaction.UIEntryPoint_QR_SHARE_AND_PAY,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_PHYSICAL_DEBIT_CARD_CHARGES:   transaction.UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_AA_SALARY:                     transaction.UIEntryPoint_AA_SALARY,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ADD_FUNDS_USS:                 transaction.UIEntryPoint_ADD_FUNDS_USS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT:               transaction.UIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT,
		timeline.TransactionUIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES:              transaction.UIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT:              transaction.UIEntryPoint_RECHARGE_PAYMENT,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PLUS:           transaction.UIEntryPoint_ALL_PLANS_JOIN_PLUS,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_INFINITE:       transaction.UIEntryPoint_ALL_PLANS_JOIN_INFINITE,
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_ALL_PLANS_JOIN_PRIME:          transaction.UIEntryPoint_ALL_PLANS_JOIN_PRIME,
	}

	transactionUIEntryPointToOrderTagsMap = map[timeline.TransactionUIEntryPoint][]beOrderPb.OrderTag{
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_PHYSICAL_DEBIT_CARD_CHARGES: {beOrderPb.OrderTag_DEBIT_CARD_CHARGES},
		timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT:            {beOrderPb.OrderTag_RECHARGE_PAYMENT},
	}
)

// Spilled over logic of APO priority from the DecisionEngine to cater for use-cases such as:
//  1. Figuring out the beneficiary account PI in case DE says that the Payee PI is in cooldown, thus, requiring liveness to bypass.
//  2. Selection of account or PIs belonging to an account based on the supported APO in the priority order.
//
// Note:
//
//	todo(rohan)
//	These redundant references to APO priority ordering has to be moved to a central place (preferably pkg/) once the use-cases
//	and usages are solidified.
var (
	// fallbackPreferredAPOInPriority : Priority order in which Account(s) can be selected for payments purpose when there isn't any APO priority list handy
	fallbackPreferredAPOInPriority = []accountTypesPb.AccountProductOffering{
		accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
		accountTypesPb.AccountProductOffering_APO_REGULAR,
		accountTypesPb.AccountProductOffering_APO_NRE,
		accountTypesPb.AccountProductOffering_APO_NRO,
	}

	// sanitiseAndFallbackApoPriorityIfApplicable : Attempts to sanitise the passed apoPriority list by doing the following:
	// 1. If it's empty, it assumes the fallbackPreferredAPOInPriority.
	// 2. If it has only the UNSPECIFIED value, we include REGULAR with it. This will help till the time we backfill the older entries in database.
	//
	// Note: It doesn't modify the original apoPriority list. Though, it can return the original if no changes are made to it.
	sanitiseAndFallbackApoPriorityIfApplicable = func(apoPriority []accountTypesPb.AccountProductOffering) []accountTypesPb.AccountProductOffering {
		if len(apoPriority) == 0 {
			return slices.Clone(fallbackPreferredAPOInPriority)
		}

		if len(apoPriority) == 1 && apoPriority[0] == accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED {
			return []accountTypesPb.AccountProductOffering{accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED, accountTypesPb.AccountProductOffering_APO_REGULAR}
		}

		return apoPriority
	}

	// payerToSupportedPayeeAPOInPriority : Map of Payer APO to supported Payee, i.e. Beneficiary APOs.
	// Given a Payer account of an APO, the mapping tells the supported Payee (i.e. receiver) accounts.
	// The ordering recommends the priority in which the Payee accounts can be selected.
	//
	// Note: UNSPECIFIED to be treated same as APO_REGULAR till the backfill of data is done
	payerToSupportedPayeeAPOInPriority = map[accountTypesPb.AccountProductOffering][]accountTypesPb.AccountProductOffering{
		accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED: {
			accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
			accountTypesPb.AccountProductOffering_APO_REGULAR,
			accountTypesPb.AccountProductOffering_APO_NRO,
		},
		accountTypesPb.AccountProductOffering_APO_REGULAR: {
			accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
			accountTypesPb.AccountProductOffering_APO_REGULAR,
			accountTypesPb.AccountProductOffering_APO_NRO,
		},
		accountTypesPb.AccountProductOffering_APO_NRE: {
			accountTypesPb.AccountProductOffering_APO_NRE,
			accountTypesPb.AccountProductOffering_APO_NRO,
			accountTypesPb.AccountProductOffering_APO_REGULAR,
			accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
		},
		accountTypesPb.AccountProductOffering_APO_NRO: {
			accountTypesPb.AccountProductOffering_APO_NRO,
			accountTypesPb.AccountProductOffering_APO_REGULAR,
			accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
		},
	}
)

// PaymentCreationFailureCollectedData can be used to consolidate the data when payment creation fails.
// This can be used to propagate the same for sending events for analytics purposes.
type PaymentCreationFailureCollectedData struct {
	L1Error              string
	L2Error              string
	L3Error              string
	DecisionEngineStatus *rpc.Status
	IsOrderCreated       bool
}

// merchantDetails is used to store the details of the merchant that need to be passed on to md := merchantDetails{}
type merchantDetails struct {
	merchantId         string
	merchantStoreId    string
	merchantRefId      string
	mcc                string
	merchantTerminalId string
}

// CreateFundTransferOrder creates an order for a Fund Transfer workflow.
// Does the following operation in the specified sequence-
//  1. check if payment is blocked and returns permission denied
//  2. If a urn is present, verify the urn signature
//     i. urn will be passed by the client in QR/Intent based payments
//  3. Fetch PIs of payee actor
//  4. Fetch PIs of payer actor
//  5. Invoke decision engine to get suggested transaction entities i.e., payment instruments, payment protocols
//  6. Generate transaction parameters, if not passed by client i.e., transaction requestId and merchantRefId
//  7. Create order with P2P_FUND_TRANSFER workflow
//  8. Respond with transaction attributes that may be required to
//     generate credentials using common library (or) to initiate a payment e.g.,
//     i. Suggested payment protocol
//     ii. Payment instruments
//     iii. Transaction parameters
//
// This information will be used by client-
// 1. To invoke `GetCredentials` in common library.
// 2. To invoke `InitiatePayment` with the cred block which executes the transaction
// nolint:funlen
func (s *Service) CreateFundTransferOrder(ctx context.Context, req *transaction.CreateFundTransferOrderRequest) (*transaction.CreateFundTransferOrderResponse, error) {
	var (
		res                                  = &transaction.CreateFundTransferOrderResponse{}
		currentActorId                       = req.GetReq().GetAuth().GetActorId()
		preferredPaymentProtocol             bePaymentPb.PaymentProtocol
		verifyURNRes                         = &upiPb.VerifyURNResponse{}
		verifyURNStatus                      *rpc.Status
		getPayeePiStatus                     *rpc.Status
		err                                  error
		provenance                           beOrderPb.OrderProvenance
		uiEntryPoint                         beOrderPb.UIEntryPoint
		qrData                               string
		orderTags                            []beOrderPb.OrderTag
		parsedUrnInfo                        *upiDomainPb.UrnInfo
		payerAccountId                       string
		validateInternationalPaymentResponse *upiPb.ValidateInternationalPaymentResponse
		purposeCode                          string
		paymentCreationFailureCollectedData  *PaymentCreationFailureCollectedData
		clientIdentificationTxnMetaData      = &payPb.ClientIdentificationTxnMetaData{}
		payerUserIdentifier                  *bePaymentPb.UserIdentifier
		payeeUserIdentifier                  *bePaymentPb.UserIdentifier
		urnAmount                            *moneyPb.Money
	)

	// This is added only to support for passing fields required for orchestration for some specific flows,
	// for eg - PHYSICAL_DEBIT_CARD_CHARGES where we need client_req_id for payment status orchestration,
	// We might not use all the fields present in OrchestrationMetadata for this flow,
	// for eg - Workflow enum is present in it, but we might decide to ignore it for this flow.
	if len(req.GetOrchestrationMetadata()) != 0 {
		decryptRes, decryptErr := s.payV1Client.GetPlainData(ctx, &payPb.GetPlainDataRequest{
			SignedData: req.GetOrchestrationMetadata(),
		})
		if te := epifigrpc.RPCError(decryptRes, decryptErr); te != nil {
			logger.Error(ctx, "error in decrypting orchestration metadata", zap.Error(te))
			res.RespHeader.Status = rpc.StatusInternal()
			return res, nil
		}
		err = proto.Unmarshal(decryptRes.GetPlainData(), clientIdentificationTxnMetaData)
		if err != nil {
			logger.Error(ctx, "error in unmarshalling orchestration metadata", zap.Error(err))
			res.RespHeader.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	// This is responsible for handling events related to payment creation failures.
	// This function is critical for capturing and processing errors during payment creation processes.
	// It's important to note that any additional error scenarios introduced in the future should ensure
	// that  PaymentCreationFailureCollectedData is properly set to gather relevant data.
	defer func() {
		if paymentCreationFailureCollectedData != nil && paymentCreationFailureCollectedData.L1Error != "" {
			var decisionEngineStatus string
			if paymentCreationFailureCollectedData.DecisionEngineStatus != nil {
				decisionEngineStatus = bePaymentPb.GetTransferDecisionResponse_Status_name[int32(paymentCreationFailureCollectedData.DecisionEngineStatus.GetCode())]
				if decisionEngineStatus == "" {
					decisionEngineStatus = paymentCreationFailureCollectedData.DecisionEngineStatus.GetShortMessage()
				}
			}
			createFundTransferOrderRPCStatus := transaction.CreateFundTransferOrderResponse_Status_name[int32(res.GetStatus().GetCode())]
			if createFundTransferOrderRPCStatus == "" {
				createFundTransferOrderRPCStatus = res.GetStatus().GetShortMessage()
			}
			goroutine.RunWithDefaultTimeout(ctx, func(ctx context.Context) {
				s.eventBroker.AddToBatch(ctx, events.NewPaymentCreationFailed(paymentCreationFailureCollectedData.L1Error, paymentCreationFailureCollectedData.L2Error,
					"CreateFundTransferOrder", currentActorId, req.GetTransactionUiEntryPoint().String(), decisionEngineStatus, createFundTransferOrderRPCStatus, "", "", paymentCreationFailureCollectedData.IsOrderCreated))
			})
		}
	}()
	res.Status = &rpc.Status{}

	if req.GetBaseAmountQuoteCurrency() == nil && !payPkg.IsCurrencySupported(req.GetAmount()) {
		res.Status = rpc.StatusInvalidArgumentWithDebugMsg("Wrong Currency Type")
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "invalid currency type", L2Error: fmt.Sprintf("invalid currency type as currency code is: %s", req.GetBaseAmountQuoteCurrency().GetCurrencyCode()), DecisionEngineStatus: nil}
		return res, nil
	}

	// giving priority to TransactionUIEntryPoint over the deprecated enum UiEntryPoint if its mapping exists to the same.
	// UiEntryPoint is what actually gets stored in the Order entity.
	// In case TransactionUIEntryPoint isn't available or its mapping doesn't exist with the deprecated enum, we will continue
	// with whatever that we have received from the caller (i.e. client).
	if req.GetTransactionUiEntryPoint() != timeline.TransactionUIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED {
		if mappedUiEntryPt, ok := transactionUIEntryPointToUIEntryPointMap[req.GetTransactionUiEntryPoint()]; ok {
			req.UiEntryPoint = mappedUiEntryPt
		}
	}

	// use v2 user identifier if it is present
	if req.GetPayerUserIdentifierV2() != nil {
		payerUserIdentifier, err = payPkg.GetBeUserIdentifier(req.GetPayerUserIdentifierV2(), req.GetReq().GetAuth().GetActorId(), req.GetPayerAccountId(), req.GetPayerAccountType())
	} else {
		payerUserIdentifier, err = getBeUserIdentifier(
			req.GetPayerUserIdentifier(),
			req.GetReq().GetAuth().GetActorId(),
			req.GetPayerAccountId(),
			req.GetPayerAccountType(),
		)
	}
	if err != nil {
		logger.Error(ctx, "error fetching user identifiers for payer", zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: internalErrorView(),
		}
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "fetching user identifier for payer", L2Error: err.Error(), DecisionEngineStatus: nil}
		return res, nil
	}

	// use v2 user identifier if it is present
	if req.GetPayeeUserIdentifierV2() != nil {
		payeeUserIdentifier, err = payPkg.GetBeUserIdentifier(req.GetPayeeUserIdentifierV2(), req.GetPayeeActorId(), "", accounts.Type_TYPE_UNSPECIFIED)
	} else {
		payeeUserIdentifier, err = getBeUserIdentifier(
			req.GetPayeeUserIdentifier(),
			req.GetPayeeActorId(),
			"",
			accounts.Type_TYPE_UNSPECIFIED,
		)
	}
	if err != nil {
		logger.Error(ctx, "error fetching user identifiers for payee", zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: internalErrorView(),
		}
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "fetching user identifier for payee", L2Error: err.Error(), DecisionEngineStatus: nil}
		return res, nil
	}

	// payee actor id will be used here
	downTimeCheckStatus := s.downtimeCheck(ctx, payeeUserIdentifier.GetActorId())
	switch {
	case downTimeCheckStatus.IsInternal():
		res.Status = rpc.StatusInternal()
		res.RespHeader = &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: internalErrorView(),
		}
		res.ErrorView = DefaultErrorView
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "evaluating bank downtime", L2Error: "", DecisionEngineStatus: nil}
		return res, nil
	case downTimeCheckStatus.IsUnavailable():
		res.Status = downTimeCheckStatus
		res.RespHeader = &header.ResponseHeader{
			Status:    downTimeCheckStatus,
			ErrorView: csisErrorView(downTimeCheckStatus.GetShortMessage()),
		}
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		res.ErrorView.Description = downTimeCheckStatus.GetShortMessage()
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "bank downtime applies", L2Error: "", DecisionEngineStatus: nil}
		return res, nil
	}

	validationErr := s.validateOrderAmount(ctx, req.GetAmount())
	if validationErr != nil {
		res.Status = rpc.StatusInvalidArgument()
		if errors.Is(validationErr, invalidAmountError) {
			res.ErrorView = proto.Clone(ZeroAmountErrorView).(*transaction.ErrorView)
		}
		res.ErrorView = proto.Clone(DefaultErrorView).(*transaction.ErrorView)
		return res, nil
	}

	// in case payee actor has blocked or is blocked by current actor payment is not allowed
	if err = s.isPaymentBlocked(ctx, payerUserIdentifier.GetActorId(), payeeUserIdentifier.GetActorId()); err != nil {
		if errors.Is(err, errPaymentBlocked) {
			logger.Debug(ctx, "payment blocked for payee actor", zap.String(logger.ACTOR_ID_V2, payeeUserIdentifier.GetActorId()))
			res.Status = rpc.StatusPermissionDenied()
			res.ErrorView = proto.Clone(BlockedActorErrorView).(*transaction.ErrorView)
		} else {
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
		}
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "payment blocked for payee", L2Error: err.Error(), DecisionEngineStatus: nil}
		return res, nil
	}

	if req.GetQrData() != "" {
		qrData = strings.TrimSpace(req.GetQrData())
	} else if req.GetUpiUrn() != "" {
		qrData = strings.TrimSpace(req.GetUpiUrn())
	}

	if qrData != "" {
		switch qr.GetQRType(qrData) {
		case feQrPb.QRType_BHARAT_QR:
			logger.Debug(ctx, "received Bharat QR", zap.String(logger.URI, qrData))
			parsedUrnInfo, err = upi.GetParamsFromBharatQR(qrData, s.conf.Flags().OverrideTransactionRefIdForBharatQr())
			if err != nil {
				logger.Error(ctx, "unable to fetch bharatQR params", zap.String(logger.BHARAT_QR, qrData),
					zap.Error(err))
				res.Status = rpc.StatusInternal()
				paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "unable to get BharatQR details", L2Error: err.Error(), DecisionEngineStatus: nil}
				return res, nil
			}
			if parsedUrnInfo.InitiationMode == "" {
				parsedUrnInfo.InitiationMode = vendors.BharatQRInitiationMode
			}
			payeeUserIdentifier.PiId, getPayeePiStatus = s.getPayeePiId(ctx, parsedUrnInfo.GetPayeeAddress())
			if getPayeePiStatus != nil {
				res.Status = getPayeePiStatus
				res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
				paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "unable to get payee pi id from Bharat qr", L2Error: fmt.Sprintf("payee address is %s", parsedUrnInfo.GetPayeeAddress()), DecisionEngineStatus: nil}
				return res, nil
			}
			preferredPaymentProtocol = bePaymentPb.PaymentProtocol_UPI
			orderTags = append(orderTags, beOrderPb.OrderTag_BHARAT_QR)
		case feQrPb.QRType_UPI_QR, feQrPb.QRType_UPI_INTERNATIONAL_QR, feQrPb.QRType_UPI_MANDATE_QR:
			logger.Debug(ctx, "received UPI URI", zap.String(logger.URI, qrData))
			urnType := getUrnType(req)
			verifyURNRes, verifyURNStatus = s.beVerifyURN(ctx, qrData, "", urnType)
			if verifyURNStatus != nil {
				res.Status = verifyURNStatus
				res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
				paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "URN is invalid", L2Error: qrData, DecisionEngineStatus: nil}
				return res, nil
			}

			parsedUrnInfo = verifyURNRes.GetParsedUrnInfo()

			payeeUserIdentifier.PiId, getPayeePiStatus = s.getPayeePiId(ctx, parsedUrnInfo.GetPayeeAddress())
			if getPayeePiStatus != nil {
				res.Status = getPayeePiStatus
				res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
				paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "unable to get payee PI ID", L2Error: fmt.Sprintf("unable to get payee PI ID from %s", qr.GetQRType(qrData).String()), DecisionEngineStatus: nil}
				return res, nil
			}

			// for QR/Intent based payments only upi payment protocol is allowed
			preferredPaymentProtocol = bePaymentPb.PaymentProtocol_UPI
		default:
			logger.Error(ctx, "Invalid qr type", zap.String(logger.BHARAT_QR, qrData))
			res.Status = rpc.StatusInvalidArgument()
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "invalid qr type", L2Error: fmt.Sprintf("error in qrData: %s", qrData), DecisionEngineStatus: nil}
			return res, nil
		}
	}

	purposeCode = getPurposeCodeForUpiLite(payerUserIdentifier, payeeUserIdentifier)
	if purposeCode == "" {
		purposeCode = parsedUrnInfo.GetPurpose()
	}

	if qr.GetQRType(qrData) == feQrPb.QRType_UPI_MANDATE_QR {
		urnAmount = verifyURNRes.GetMandateUrnInfo().GetOneTimeAmount()
	} else {
		urnAmount = parsedUrnInfo.GetAmount()
	}

	// validating req amount with urn amount(urn amount will differ based on the type of URN. For mandates, it will be one time amount i.e oam tag's value)
	// req amount should be in between urn min amount and urn amount
	if urnAmount != nil && !money.IsZero(urnAmount) && money.Compare(urnAmount, req.GetAmount().GetBeMoney()) != 0 {
		// the req amount should be in between the urn amount and the urn min amount
		if parsedUrnInfo.GetMinAmount() == nil || money.Compare(req.GetAmount().GetBeMoney(), parsedUrnInfo.GetMinAmount()) < 0 ||
			money.Compare(urnAmount, req.GetAmount().GetBeMoney()) < 0 {
			logger.Error(ctx, "amount mismatch between request and urn")
			res.ErrorView = proto.Clone(AmountMisMatchErrorView).(*transaction.ErrorView)
			res.Status = rpc.StatusInvalidArgument()
			res.RespHeader = &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: errors2.GetHeaderErrorViewFromPayErrorView(ctx, res.GetErrorView()),
			}
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "req amount is not in between the urn min amount and urn max amount", L2Error: qrData, DecisionEngineStatus: nil}
			return res, nil
		}
	}

	if purposeCode == vendors.DefaultPurposeUpiLitePayment || purposeCode == vendors.DefaultPurposeUpiLiteTopUp {
		// UPI Lite payments are only done through UPI protocol
		preferredPaymentProtocol = bePaymentPb.PaymentProtocol_UPI
	}

	// get transfer decision
	logger.Debug(ctx, "calling the decision engine")
	payerPi, payeePi, protocol, transferDecisionStatus, errorView, decisionEngineStatus := s.beGetTransferDecision(
		ctx,
		currentActorId,
		payeeUserIdentifier.GetActorId(),
		req.Amount.GetBeMoney(),
		false,
		preferredPaymentProtocol,
		parsedUrnInfo.GetInitiationMode(),
		purposeCode,
		payeeUserIdentifier.GetPiId(),
		payerUserIdentifier,
		payeeUserIdentifier,
	)
	// check if status returned by DE needs to be overridden
	overrided, overrideErr := s.isStatusOverrideNeeded(ctx, currentActorId, payerUserIdentifier.GetApo(), payeeUserIdentifier.GetActorId(), transferDecisionStatus, res)
	if overrideErr != nil {
		logger.Error(ctx, "failed to check if status returned by DE needs to be updated", zap.String("de status", transferDecisionStatus.String()), zap.Error(err))
		res.Status = rpc.StatusInternal()
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failure while checking if DE status should be overridden", L2Error: overrideErr.Error(), DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	}
	if overrided {
		return res, nil
	}

	if transferDecisionStatus != nil {
		res.Status = transferDecisionStatus
		res.ErrorView = errorView
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "non-OK response from decision engine", L2Error: qrData, L3Error: decisionEngineStatus.GetShortMessage(), DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	}

	isOtherActorInternal, err := s.isActorInternal(ctx, payeeUserIdentifier.GetActorId())
	if err != nil {
		logger.Error(ctx, "failed to check if other actor is internal", zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed to check if payee is internal", L2Error: err.Error(), DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	}

	if isOtherActorInternal && payeePi.GetType() == paymentinstrument.PaymentInstrumentType_UPI && currentActorId != payeeUserIdentifier.GetActorId() {
		isInternalAccountPi, internalAccErr := s.isInternalAccountPi(ctx, payeePi.GetId(), payeeUserIdentifier.GetActorId())
		switch {
		case internalAccErr != nil && !storagev2.IsRecordNotFoundError(internalAccErr):
			logger.Error(ctx, "failed to check if given pi is of internal fi account or not", zap.Error(internalAccErr))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed to check if given pi is of internal fi account", L2Error: internalAccErr.Error(), DecisionEngineStatus: decisionEngineStatus}
			return res, nil
		case storagev2.IsRecordNotFoundError(internalAccErr):
			logger.Debug(ctx, "no savings account found for given actor id, user is onboarded from fi lite", zap.String(logger.ACTOR_ID_V2, payeeUserIdentifier.GetActorId()))
		case !isInternalAccountPi:
			// if the other actor is internal and they have not set the internal fi account as their primary account,
			// we don't want the payments to go through till we have decision engine v2 in place. Once we have DE v2, this can be removed
			logger.Error(ctx, "fi to fi payments for non fi account not allowed", zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "payee is internal but they haven't set Fi account as primary", L2Error: "", DecisionEngineStatus: decisionEngineStatus}
			return res, nil
		}
	}

	isMerchantPayment := payeePi.IsMerchantPI()

	if isMerchantPayment {
		orderTags = append(orderTags, beOrderPb.OrderTag_MERCHANT)
	}

	md := getMerchantDetails(protocol, parsedUrnInfo, payeePi)

	// TODO(raunak) refactor this method
	paymentDetails, err := s.getPaymentDetails(
		payerPi.GetId(),
		payeePi.GetId(),
		protocol,
		md.merchantRefId,
		parsedUrnInfo.GetTxnId(),
		parsedUrnInfo.GetReferenceUrl(),
		req.GetRemarks(),
		parsedUrnInfo.GetInitiationMode(),
		purposeCode,
		md.mcc,
		md.merchantId,
		md.merchantStoreId,
		md.merchantTerminalId,
		parsedUrnInfo.GetOrgId(),
		req.GetAmount(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create payment details for order", zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed to create payment details for order", L2Error: err.Error(), DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	}

	provenance, uiEntryPoint = getProvenanceAndEntryPoint(req.GetUiEntryPoint(), verifyURNRes.GetIsSignatureVerified())

	logger.Info(ctx, "order entry point for the urn", zap.Bool("IsUrnVerified", verifyURNRes.GetIsSignatureVerified()),
		zap.String(logger.UI_ENTRY_POINT, uiEntryPoint.String()), zap.String(logger.ACTOR_ID_V2, payerUserIdentifier.GetActorId()))

	orderTags = populateOrderTagsForUiEntryPoint(orderTags, req.GetTransactionUiEntryPoint())

	orderId, isPinRequired, status := s.beCreateOrder(
		ctx,
		beOrderPb.OrderWorkflow_P2P_FUND_TRANSFER,
		paymentDetails,
		payerUserIdentifier.GetActorId(),
		payeeUserIdentifier.GetActorId(),
		payerUserIdentifier.GetAccountId(),
		payeeUserIdentifier.GetAccountId(),
		req.GetReq().GetAuth().GetDevice(),
		provenance,
		uiEntryPoint,
		verifyURNRes.GetIsDynamicQrInitialised(),
		orderTags,
		req.GetReq().GetAuth().GetDevice().GetLocationToken(),
		"",
		req.GetPostPaymentDeeplink(),
		clientIdentificationTxnMetaData.GetClientReqId(),
	)
	switch {
	case status.GetCode() == statusCoolOffValidationFailed().GetCode():
		res.Status = statusCoolOffValidationFailedFundTransfer()
		res.ErrorView = proto.Clone(CoolOffValidationFailedErrorView).(*transaction.ErrorView)
		res.ErrorView.SubTitle = status.GetShortMessage()
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "device is currently in a cool off period", L2Error: "", DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	case status.GetCode() == statusURNAmountLimitExceeded().GetCode():
		res.Status = statusURNAmountLimitExceeded()
		res.ErrorView = proto.Clone(URNAmountLimitExceededErrorView).(*transaction.ErrorView)
		res.ErrorView.SubTitle = status.GetShortMessage()
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "payment amount limit exceeded for URN", L2Error: "", DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	case status != nil:
		res.Status = status
		res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed to create order", L2Error: "", DecisionEngineStatus: decisionEngineStatus}
		return res, nil
	}

	payerAccountId = payerUserIdentifier.GetAccountId()
	if payerAccountId == "" {
		accPiResp, rpcErr := s.accountPiRelationClient.GetByPiId(ctx, &accountPiPb.GetByPiIdRequest{
			PiId: payerPi.GetId(),
		})
		if err = epifigrpc.RPCError(accPiResp, rpcErr); err != nil {
			logger.Error(ctx, "failed to fetch account pi details for payer pi id",
				zap.String(logger.PI_ID, payerPi.GetId()),
				zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed to fetch account pi details for payer PI ID", L2Error: err.Error(), DecisionEngineStatus: decisionEngineStatus, IsOrderCreated: true}
			return res, nil
		}
		payerAccountId = accPiResp.GetAccountId()
	}

	ta, err := s.getTransactionAttribute(ctx, paymentDetails, payerAccountId,
		req.GetPayerUserIdentifier().GetUpiNumber(), req.GetPayeeUserIdentifier().GetUpiNumber(), payerPi, payeePi)
	if err != nil {
		logger.Error(ctx, "error while generating transaction attributes", zap.String("order-id", orderId),
			zap.Error(err))
		res.Status = rpc.StatusInternal()
		res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
		paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "failed in generating transaction attributes", L2Error: err.Error(), DecisionEngineStatus: decisionEngineStatus, IsOrderCreated: true}
		return res, nil
	}

	// Storing OrderMetadata for the given orderId in the orderMetadata table
	if qrData != "" {
		s.storeOrderMetadata(epificontext.CloneCtx(ctx), orderId, qrData, beOrderPb.MetadataType_METADATA_TYPE_QR)
	}

	res.OrderId = orderId
	res.PinRequired = getPinTypeRequired(isPinRequired, protocol)

	// NEFT Message banner on order creation screen
	if protocol == bePaymentPb.PaymentProtocol_NEFT {
		res.TransactionInfo = s.paymentInfoBanner.NeftTxnInfoBanner
	}

	if req.GetBaseAmountQuoteCurrency() != nil {
		validateInternationalPaymentResponse, err = s.upiClient.ValidateInternationalPayment(ctx, &upiPb.ValidateInternationalPaymentRequest{
			ActorId:                 req.GetReq().GetAuth().GetActorId(),
			BaseAmountQuoteCurrency: req.GetBaseAmountQuoteCurrency().GetBeMoney(),
			TotalAmountInr:          req.GetAmount().GetBeMoney(),
		})
		if err = epifigrpc.RPCError(validateInternationalPaymentResponse, err); err != nil {
			logger.Error(ctx, "error while validating conversion of baseAmountQuoteCurrency to inr currency during order creation", zap.Error(err))
			res.Status = rpc.StatusInternal()
			res.ErrorView = proto.Clone(DefaultErrorViewOrderCreation).(*transaction.ErrorView)
			paymentCreationFailureCollectedData = &PaymentCreationFailureCollectedData{L1Error: "validating currency conversion to INR", L2Error: err.Error(), DecisionEngineStatus: decisionEngineStatus, IsOrderCreated: true}
			return res, nil
		}
	}
	res.TransactionAttribute = ta
	res.Status = rpc.StatusOk()
	return res, nil
}

// populateOrderTagsForUiEntryPoint - 1. takes a list of applicable order tags,
// 2. fetches the list of orderTags applicable based on the uiEntryPoint,
// 3. checks for each uiEntryPoint specific order is already present in the list of all applicable orderTags, if not then append it.
func populateOrderTagsForUiEntryPoint(allApplicableOrderTags []beOrderPb.OrderTag, uiEntryPoint timeline.TransactionUIEntryPoint) []beOrderPb.OrderTag {
	uiEntryPointOrderTags, ok := transactionUIEntryPointToOrderTagsMap[uiEntryPoint]
	if !ok {
		return allApplicableOrderTags
	}
	for _, ot := range uiEntryPointOrderTags {
		if !lo.Contains(allApplicableOrderTags, ot) {
			allApplicableOrderTags = append(allApplicableOrderTags, ot)
		}
	}
	return allApplicableOrderTags
}

func getMerchantDetails(protocol bePaymentPb.PaymentProtocol, parsedUrnInfo *upiDomainPb.UrnInfo, payeePi *paymentinstrument.PaymentInstrument) *merchantDetails {
	switch protocol {
	case bePaymentPb.PaymentProtocol_UPI:
		return &merchantDetails{
			merchantId:         payeePi.GetUpi().GetMerchantDetails().GetMerchantId(),
			merchantStoreId:    payeePi.GetUpi().GetMerchantDetails().GetMerchantStoreId(),
			mcc:                payeePi.GetUpi().GetMerchantDetails().GetMcc(),
			merchantTerminalId: payeePi.GetUpi().GetMerchantDetails().GetMerchantTerminalId(),
			merchantRefId:      parsedUrnInfo.GetMerchantRefId(),
		}
	default:
		return &merchantDetails{
			merchantId:         parsedUrnInfo.GetMerchantId(),
			merchantStoreId:    parsedUrnInfo.GetMerchantStoreId(),
			merchantRefId:      parsedUrnInfo.GetMerchantRefId(),
			mcc:                parsedUrnInfo.GetMcc(),
			merchantTerminalId: parsedUrnInfo.GetMerchantTerminalId(),
		}
	}
}

// isStatusOverrideNeeded is useful when the transaction initiation is failed by Decision Engine due to any rule not passing
// but still we need to return rpc status as OK and custom objects to the client to trigger the next flows
// some of the use cases are (but not limited to):
// 1. Transaction failed due to beneficiary is in cooldown phase: now we need to return the payee pi and a bottom sheet so that client can initiate the liveness
//
// NOTE: we cannot return custom status in these cases and have to return status OK because android client cannot access the data in the response if the status is not OK.
// nolint:funlen
func (s *Service) isStatusOverrideNeeded(ctx context.Context, actorFrom string, actorFromAPO accountTypesPb.AccountProductOffering, actorTo string, feStatusCode *rpc.Status, res *transaction.CreateFundTransferOrderResponse) (bool, error) {
	if feStatusCode.GetCode() == uint32(transaction.CreateFundTransferOrderResponse_BENEFICIARY_IS_IN_COOLDOWN_PHASE) {
		isBeneficiaryActivationAllowed, isBeneficiaryActivationAllowedErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_ACTIVATE_BENEFICIARY_VIA_LIVENESS).WithActorId(actorFrom))
		if isBeneficiaryActivationAllowedErr != nil {
			return false, fmt.Errorf("failed to check if beneficiary activation is allowed or not,err: %w", isBeneficiaryActivationAllowedErr)
		}
		if !isBeneficiaryActivationAllowed {
			return false, nil
		}

		res.Status = rpc.StatusOkWithDebugMsg("beneficiary in cooldown phase")
		actorRes, actorResErr := s.actorClient.GetActorById(ctx, &actor.GetActorByIdRequest{
			Id: actorTo,
		})
		if err := epifigrpc.RPCError(actorRes, actorResErr); err != nil {
			return false, fmt.Errorf("failed to fetch the actor to details,actor to id: %s ,err: %w", actorTo, err)
		}
		var piTo string
		// return the pi of the savings account
		if actorRes.GetActor().Type == types.Actor_USER {
			// DecisionEngine tells that Beneficiary is in cooldown, though, doesn't tell which beneficiary.
			//	- Thus, in cases where the Payee can have multiple accounts, we are relying on the supported list (in order of priority) of Payee APOs w.r.t Payer APO.
			//	- Though this isn't a foolproof solution, it will cover some scenarios instead of blindly attempting to pick any Payee account.
			//
			// Ideal solve:	Decision engine sends extra metadata regarding which beneficiary-PI it is talking about.
			//
			// Impact of not doing the ideal solve:
			//	- We might end up triggering liveness for an unintended account.
			//	- Thus, it's possible that the Payer gets stuck in a loop where Payer has done the liveness for, lets say NRE, but DecisionEngine is asking the user
			//	to do it again (because it picked NRO as the beneficiary account). And since DecisionEngine isn't telling which account it is talking about, thus the loop.
			//	- But, this lasts only for the 24hrs period of cooldown. Also, they can still transact <1L.
			internalAccount, internalAccErr := s.getInternalSavingsAccount(ctx, actorTo, slices.Clone(payerToSupportedPayeeAPOInPriority[actorFromAPO]))
			if internalAccErr != nil {
				return false, fmt.Errorf("failed to fetch internal savings account for the actor: %s, err: %w", actorTo, internalAccErr)
			}

			// fetch the account type pi
			piRes, piResErr := s.piClient.GetPi(ctx, &paymentinstrument.GetPiRequest{
				Type: paymentinstrument.PaymentInstrumentType_BANK_ACCOUNT,
				Identifier: &paymentinstrument.GetPiRequest_AccountRequestParams_{
					AccountRequestParams: &paymentinstrument.GetPiRequest_AccountRequestParams{
						ActualAccountNumber: internalAccount.GetAccountNo(),
						IfscCode:            internalAccount.GetIfscCode(),
					},
				},
			})
			if err := epifigrpc.RPCError(piRes, piResErr); err != nil {
				return false, fmt.Errorf("failed to fetch the pi of actor to,actor to id %s, err: %w", actorTo, err)
			}
			piTo = piRes.GetPaymentInstrument().GetId()
		} else {
			// fetch the pi from the actor pi resolution
			// Since we are using only first pi, we are limiting the response to 1
			actorToPisRes, actorToPisResErr := s.actorClient.GetPIsOfActorTo(ctx, &actor.GetPIsOfActorToRequest{
				ActorFrom: actorFrom,
				ActorTo:   actorTo,
				Limit:     1,
			})
			if err := epifigrpc.RPCError(actorToPisRes, actorToPisResErr); err != nil {
				return false, fmt.Errorf("failed to fetch the pi of actor to by actor pi resolution, actor to id %s, err: %w", actorTo, err)
			}
			if len(actorToPisRes.GetPiIds()) == 0 {
				return false, fmt.Errorf("no pis found from actor pi resolution, actor from id: %s, actor to id: %s", actorFrom, actorTo)
			}
			piTo = actorToPisRes.GetPiIds()[0]
		}
		res.CooldownBottomSheet = internal.GetBeneficiaryCoolDownBottomSheet(actorFrom, piTo, pay.BeneficiaryActivationAuthMode_ACTIVATION_AUTH_MODE_LIVENESS)
		return true, nil
	}

	return false, nil
}

// storeOrderMetadata calls the Backend RPC CreateOrderMetadata which creates an orderMetadata entry in the database
func (s *Service) storeOrderMetadata(ctx context.Context, orderId, qrData string, metadataType beOrderPb.MetadataType) {
	storeOrderMetadataFn := func(ctx context.Context) {
		createOrderMetadataResponse, err := s.orderClient.CreateOrderMetadata(ctx, &beOrderPb.CreateOrderMetadataRequest{
			OrderId:      orderId,
			MetadataType: metadataType,
			Metadata: &beOrderPb.Metadata{
				QrData: qrData,
			},
		})
		switch {
		case err != nil:
			logger.Error(ctx, "error while creating OrderMetadata for the given OrderId", zap.String(logger.ORDER_ID, orderId),
				zap.Error(err))
		case !createOrderMetadataResponse.GetStatus().IsSuccess():
			logger.Error(ctx, "non-success error code while creating OrderMetadata for the given OrderId", zap.String(logger.ORDER_ID, orderId),
				zap.String(logger.STATUS_CODE, createOrderMetadataResponse.GetStatus().String()))
		}
	}
	goroutine.Run(ctx, 5*time.Second, storeOrderMetadataFn)
}

// getPinTypeRequired returns the type of pin required for the authorising payment for the order based on payment protocol
// returns `NONE` in case no pin is required
func getPinTypeRequired(isPinRequired bool, protocol bePaymentPb.PaymentProtocol) transaction.PinRequiredType {
	if !isPinRequired {
		return transaction.PinRequiredType_NONE
	}

	if protocol == bePaymentPb.PaymentProtocol_UPI {
		return transaction.PinRequiredType_NPCI
	} else {
		return transaction.PinRequiredType_SECURE_PIN
	}
}

// verifyURN calls the upi service to verify if the urn is valid or not
// in case the verification is success returns the verifyURNRes and nil status
// in case the verification fails, returns nil with relevant status
func (s *Service) beVerifyURN(ctx context.Context, urn string, mcc string, urnType upiPb.URNType) (*upiPb.VerifyURNResponse, *rpc.Status) {
	res, err := s.upiClient.VerifyURN(ctx, &upiPb.VerifyURNRequest{
		Urn:     urn,
		Mcc:     mcc,
		UrnType: urnType,
	})
	switch {
	case err != nil:
		logger.Debug(ctx, "error verifying the urn", zap.Error(err))
		return nil, rpc.StatusInternal()
	case res.Status.Code == uint32(upiPb.VerifyURNResponse_INVALID_SIGNATURE):
		logger.Debug(ctx, "invalid signature in the urn")
		return nil, statusInvalidSignature()
	case res.Status.IsInvalidArgument():
		logger.Debug(ctx, "invalid argument passed in verify urn")
		return nil, rpc.StatusInvalidArgument()
	case !res.Status.IsSuccess():
		logger.Error(ctx, "error verifying the urn")
		return nil, rpc.StatusInternal()
	}
	return res, nil
}

func (s *Service) getPayeePiId(ctx context.Context, payeeVpa string) (string, *rpc.Status) {

	vpaPi, err := s.piClient.GetPi(ctx, &paymentinstrument.GetPiRequest{
		Type: paymentinstrument.PaymentInstrumentType_UPI,
		Identifier: &paymentinstrument.GetPiRequest_UpiRequestParams_{
			UpiRequestParams: &paymentinstrument.GetPiRequest_UpiRequestParams{
				Vpa: payeeVpa,
			},
		},
	})

	switch {
	case err != nil:
		logger.Error(ctx, "error in getting vpa pi", zap.String(logger.VPA, payeeVpa), zap.Error(err))
		return "", rpc.StatusInternal()
	case vpaPi.Status.IsRecordNotFound():
		logger.Error(ctx, "pi not found", zap.String(logger.VPA, payeeVpa), zap.Error(err))
		return "", rpc.StatusInvalidArgument()
	case !vpaPi.Status.IsSuccess():
		logger.Error(ctx, "non-success response for get vpa pi", zap.String(logger.RPC_STATUS, vpaPi.Status.String()))
		return "", rpc.StatusInternal()
	}
	return vpaPi.GetPaymentInstrument().GetId(), nil
}

// getProvenanceAndEntryPoint returns the BE order provenance and ui entry point
func getProvenanceAndEntryPoint(feUiEntry transaction.UIEntryPoint, isUrnVerified bool) (beOrderPb.OrderProvenance, beOrderPb.UIEntryPoint) {
	var (
		provenance   beOrderPb.OrderProvenance
		uiEntryPoint beOrderPb.UIEntryPoint
	)

	switch feUiEntry {
	case transaction.UIEntryPoint_INTENT:
		provenance = beOrderPb.OrderProvenance_THIRD_PARTY
		if isUrnVerified {
			uiEntryPoint = beOrderPb.UIEntryPoint_SECURE_INTENT
		} else {
			uiEntryPoint = beOrderPb.UIEntryPoint_INSECURE_INTENT
		}
	case transaction.UIEntryPoint_QR_CODE:
		provenance = beOrderPb.OrderProvenance_THIRD_PARTY
		if isUrnVerified {
			uiEntryPoint = beOrderPb.UIEntryPoint_SECURE_QR_CODE
		} else {
			uiEntryPoint = beOrderPb.UIEntryPoint_INSECURE_QR_CODE
		}
	case transaction.UIEntryPoint_QR_SHARE_AND_PAY:
		provenance = beOrderPb.OrderProvenance_THIRD_PARTY
		if isUrnVerified {
			uiEntryPoint = beOrderPb.UIEntryPoint_SECURE_QR_SHARE_AND_PAY
		} else {
			uiEntryPoint = beOrderPb.UIEntryPoint_INSECURE_QR_SHARE_AND_PAY
		}
	case transaction.UIEntryPoint_MF_BUY_ONE_TIME:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_MF_BUY_ONE_TIME
	case transaction.UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES
	case transaction.UIEntryPoint_AA_SALARY:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_AA_SALARY
	case transaction.UIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES
	case transaction.UIEntryPoint_ADD_FUNDS_USS:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_ADD_FUNDS_USS
	case transaction.UIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT
	case transaction.UIEntryPoint_ALL_PLANS_JOIN_PLUS:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PLUS
	case transaction.UIEntryPoint_ALL_PLANS_JOIN_INFINITE:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_INFINITE
	case transaction.UIEntryPoint_ALL_PLANS_JOIN_PRIME:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_TIERING_ALL_PLANS_JOIN_PRIME
	case transaction.UIEntryPoint_AMB_DETAILS:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_AMB_DETAILS
	case transaction.UIEntryPoint_RECHARGE_PAYMENT:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT
	default:
		provenance = beOrderPb.OrderProvenance_USER_APP
		uiEntryPoint = beOrderPb.UIEntryPoint_TIMELINE
	}
	return provenance, uiEntryPoint
}

// getUrnType returns the URN type based on UI entry point
func getUrnType(req *transaction.CreateFundTransferOrderRequest) upiPb.URNType {
	switch req.GetUiEntryPoint() {
	case transaction.UIEntryPoint_QR_CODE, transaction.UIEntryPoint_QR_SHARE_AND_PAY:
		return upiPb.URNType_QR_URN
	case transaction.UIEntryPoint_INTENT:
		return upiPb.URNType_INTENT_URN
	default:
		return upiPb.URNType_URN_TYPE_UNSPECIFIED
	}
}

// returns be user identifier from given fe userIdentifier. If user identifier is nil will use default values for actor and accoutn ids
func getBeUserIdentifier(
	userIdentifier *transaction.UserIdentifier,
	defaultActorId, defaultAccountId string,
	defaultAccountType accounts.Type,
) (*bePaymentPb.UserIdentifier, error) {
	var (
		userActorId     = defaultActorId
		userAccountId   = defaultAccountId
		userPiId        string
		userAccountType = defaultAccountType
	)

	if userIdentifier.GetDerivedAccountId() != "" {
		derivedAccountIdProto := &accounts.DerivedAccountId{}
		err := idgen.DecodeProtoFromStdBase64(userIdentifier.GetDerivedAccountId(), derivedAccountIdProto)
		if err != nil {
			return nil, fmt.Errorf("failed to decode derived account id %s %w", userIdentifier.GetDerivedAccountId(), err)
		}
		if derivedAccountIdProto.GetInternalAccountId() != "" {
			userAccountId = derivedAccountIdProto.GetInternalAccountId()
		} else if derivedAccountIdProto.GetTpapAccountId() != "" {
			userAccountId = derivedAccountIdProto.GetTpapAccountId()
		}
	}

	if userIdentifier.GetActorId() != "" {
		userActorId = userIdentifier.GetActorId()
	}
	if userIdentifier.GetAccountType() != accounts.Type_TYPE_UNSPECIFIED {
		userAccountType = userIdentifier.GetAccountType()
	}
	if userIdentifier.GetPiId() != "" {
		userPiId = userIdentifier.GetPiId()
	}

	return &bePaymentPb.UserIdentifier{
		ActorId:     userActorId,
		AccountId:   userAccountId,
		AccountType: userAccountType,
		PiId:        userPiId,
		Apo:         userIdentifier.GetAccountProductOffering(),
	}, nil
}

// getPurposeCodeForUpiLite - return the purpose code in case payer or payee identifier is upi lite type
func getPurposeCodeForUpiLite(payerUserIdentifier, payeeUserIdentifier *bePaymentPb.UserIdentifier) string {
	switch {
	case payerUserIdentifier.GetAccountType() == accounts.Type_UPI_LITE:
		return vendors.DefaultPurposeUpiLitePayment
	case payeeUserIdentifier.GetAccountType() == accounts.Type_UPI_LITE:
		return vendors.DefaultPurposeUpiLiteTopUp
	default:
		return ""
	}
}

// getInternalSavingsAccount fetches the internal saving account for the given actor id based on the given APO priority (in case there are multiple internal accounts).
//
// Note:
//  1. It is possible that no account is returned because of the APO list filtering or if the user is Fi-lite. ErrRNF is attached with it.
//  2. In case apoPriority is empty, the list is sanitised to a fallback value.
func (s *Service) getInternalSavingsAccount(ctx context.Context, actorId string, apoPriority []accountTypesPb.AccountProductOffering) (*savings.Account, error) {
	sanitisedApoPriority := sanitiseAndFallbackApoPriorityIfApplicable(apoPriority)

	accountsListRes, err := s.savingsClient.GetAccountsList(ctx, &savings.GetAccountsListRequest{
		Identifier: &savings.GetAccountsListRequest_BulkActorIdentifier{
			BulkActorIdentifier: &savings.BulkActorIdentifier{
				ActorIds: []string{actorId},
				// long term goal is to avoid sending UNSPECIFIED
				AccountProductOfferings: lo.Without(sanitisedApoPriority, accountTypesPb.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED),
				PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(accountsListRes, err); rpcErr != nil {
		if accountsListRes.GetStatus().IsRecordNotFound() {
			logger.WarnWithCtx(ctx, "no savings-accounts found by actor-id and APO", zap.String(logger.ACTOR_ID_V2, actorId),
				zap.Any("apoPriority", apoPriority), zap.Any("sanitisedApoPriority", sanitisedApoPriority),
			)
			return nil, epifierrors.ErrRecordNotFound
		}

		logger.Error(ctx, "error fetching savings accounts by actor-id and APO", zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Any("apoPriority", apoPriority), zap.Any("sanitisedApoPriority", sanitisedApoPriority), zap.Error(rpcErr),
		)
		return nil, fmt.Errorf("error fetching savings accounts by actor-id and APO: %w", rpcErr)
	}

	// return one of the internal accounts based on the sanitised APO priority
	for _, apo := range sanitisedApoPriority {
		for _, account := range accountsListRes.GetAccounts() {
			if account.GetSkuInfo().GetAccountProductOffering() == apo {
				return account, nil
			}
		}
	}

	logger.WarnWithCtx(ctx, "no savings account available for the actor-id and APO", zap.String(logger.ACTOR_ID_V2, actorId),
		zap.Any("apoPriority", apoPriority), zap.Any("sanitisedApoPriority", sanitisedApoPriority),
	)

	return nil, epifierrors.ErrRecordNotFound
}
