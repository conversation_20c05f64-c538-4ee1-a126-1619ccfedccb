package add_funds_v2_params

import (
	"context"

	moneypb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	pkgMoney "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	afV2Pb "github.com/epifi/gamma/api/frontend/pay/add_funds_v2"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/frontend/config/genconf"
	tieringCardOptions "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/card_options/tiering"
	tieringProperties "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/properties/tiering"
	errorSuggestions "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/suggestions/error"
	tieringSuggestions "github.com/epifi/gamma/frontend/pay/transaction/add_funds_v2_params/suggestions/tiering"
	"github.com/epifi/gamma/frontend/pkg/tiering/amb"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
)

const (
	intentScreenTitle                      = "Select a payment method"
	intentScreenTitleFontColor             = "#262728"
	intentScreenCtaText                    = "Add $AMOUNT$"
	footerLeftImgUrl                       = "https://epifi-icons.pointz.in/tiering/add_funds/shield.png"
	footerLeftImgHeight              int32 = 24
	footerLeftImgWidth               int32 = 24
	footerLeftImgPadding             int32 = 4
	footerText                             = "Secure banking with Federal Bank"
	footerTextFontColor                    = "#8D8D8D"
	collectScreenTitle                     = "Enter your UPI ID"
	collectScreenTitleFontColor            = "#313234"
	collectScreenHintText                  = "UPI ID"
	collectScreenHintTextFontColor         = "#B9B9B9"
	collectScreenHelperText                = "Money will be transferred from this UPI ID"
	collectScreenHelperTextFontColor       = "#A4A4A4"
	collectScreenCtaText                   = "Verify & add $AMOUNT$"
	handleFontColor                        = "#6A6D70"
	handleContainerBgColor                 = "#FFFFFF"
	handleContainerBorderRadius      int32 = 16
	handleContainerLeftPadding       int32 = 12
	handleContainerRightPadding      int32 = 12
	handleContainerTopPadding        int32 = 8
	handleContainerBottomPadding     int32 = 8
)

var (
	primaryCta = func() *deeplink.Cta {
		return &deeplink.Cta{
			Type:   deeplink.Cta_CONTINUE,
			Text:   tieringProperties.PrimaryCtaText,
			Status: deeplink.Cta_CTA_STATUS_ENABLED,
			// TODO (sayan) : populate deeplink
		}
	}

	fallbackCta = func() *deeplink.Cta {
		return &deeplink.Cta{
			Type:   deeplink.Cta_CONTINUE,
			Text:   tieringProperties.FallbackCtaText,
			Status: deeplink.Cta_CTA_STATUS_ENABLED,
			// TODO (sayan) : populate deeplink
		}
	}
)

func GetTieringAddFundsV2Params(ctx context.Context, conf *genconf.Config, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails, appPlatform commontypes.Platform, appVersion uint32) (*afV2Pb.AddFundsV2ScreenDetails, error) {
	return &afV2Pb.AddFundsV2ScreenDetails{
		Title:                  commontypes.GetTextFromStringFontColourFontStyle(AddFundsScreenTitle, AddFundsScreenTitleFontColour, commontypes.FontStyle_HEADLINE_M),
		TopCard:                tieringCardOptions.GetAddFundsTopCardForTiering(ctx, conf, true, tieringPitchDetails, appPlatform, appVersion),
		PrimaryAddFundsDetails: getAddFundsPrimaryDetails(conf, true, tieringPitchDetails, true),
		BgColour:               widgetPb.GetBlockBackgroundColour(AddFundsBgColour),
	}, nil
}

func GetTieringAddFundsV3Params(ctx context.Context, conf *genconf.Config, isPitchEnabled bool, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails, appPlatform commontypes.Platform, appVersion uint32) (*afV2Pb.AddFundsV2ScreenDetails, error) {
	return &afV2Pb.AddFundsV2ScreenDetails{
		Title:                  commontypes.GetTextFromStringFontColourFontStyle(AddFundsScreenTitle, AddFundsScreenTitleFontColour, commontypes.FontStyle_HEADLINE_M),
		BgColour:               widgetPb.GetBlockBackgroundColour(AddFundsBgColour),
		TopCard:                tieringCardOptions.GetAddFundsTopCardForTiering(ctx, conf, isPitchEnabled, tieringPitchDetails, appPlatform, appVersion),
		PrimaryAddFundsDetails: getAddFundsPrimaryDetails(conf, isPitchEnabled, tieringPitchDetails, false),
		IntentThreshold: &typesPb.Money{
			CurrencyCode: "INR",
			Units:        conf.AddFundsParams().AddFundsV3Params().IntentThreshold(),
		},
		IntentFlowBottomSheet:  getIntentFlowBottomSheet(),
		CollectFlowBottomSheet: getCollectFlowBottomSheet(conf),
	}, nil
}

func getIntentFlowBottomSheet() *afV2Pb.IntentFlowBottomSheet {
	return &afV2Pb.IntentFlowBottomSheet{
		Title: commontypes.GetTextFromStringFontColourFontStyle(intentScreenTitle, intentScreenTitleFontColor, commontypes.FontStyle_HEADLINE_M),
		Cta: &deeplink.Cta{
			Type:         deeplink.Cta_CONTINUE,
			Text:         intentScreenCtaText,
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		Footer: getAddFundsFooter(),
	}
}

func getAddFundsFooter() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(footerText, footerTextFontColor, commontypes.FontStyle_SUBTITLE_XS),
		},
		LeftImgTxtPadding: footerLeftImgPadding,
		LeftVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(footerLeftImgUrl, footerLeftImgHeight, footerLeftImgWidth),
	}
}

func getCollectFlowBottomSheet(conf *genconf.Config) *afV2Pb.CollectFlowBottomSheet {
	return &afV2Pb.CollectFlowBottomSheet{
		Title:                commontypes.GetTextFromStringFontColourFontStyle(collectScreenTitle, collectScreenTitleFontColor, commontypes.FontStyle_HEADLINE_M),
		InputFieldHint:       commontypes.GetTextFromStringFontColourFontStyle(collectScreenHintText, collectScreenHintTextFontColor, commontypes.FontStyle_OVERLINE_XS_CAPS),
		InputFieldHelperText: commontypes.GetTextFromStringFontColourFontStyle(collectScreenHelperText, collectScreenHelperTextFontColor, commontypes.FontStyle_BODY_4),
		VpaHandles:           addFundsVpaHandles(conf.AddFundsParams().AddFundsV3Params().VpaHandles()),
		Cta: &deeplink.Cta{
			Type:         deeplink.Cta_CONTINUE,
			Text:         collectScreenCtaText,
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		Footer: getAddFundsFooter(),
	}
}

func addFundsVpaHandles(handles []string) []*ui.IconTextComponent {
	handlesAsItcs := make([]*ui.IconTextComponent, 0)
	for _, handle := range handles {
		handlesAsItcs = append(handlesAsItcs, &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(handle, handleFontColor, commontypes.FontStyle_NUMBER_S),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       handleContainerBgColor,
				CornerRadius:  handleContainerBorderRadius,
				LeftPadding:   handleContainerLeftPadding,
				RightPadding:  handleContainerRightPadding,
				TopPadding:    handleContainerTopPadding,
				BottomPadding: handleContainerBottomPadding,
			},
		})
	}
	return handlesAsItcs
}

func getAddFundsPrimaryDetails(conf *genconf.Config, isPitchEnabled bool, tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails, toShowPaymentApps bool) *afV2Pb.AddFundsMainComponent {
	minAmount := tieringPitchDetails.GetMinAmount()
	suggestedAmount := pkgMoney.NewMoney(tieringPitchDetails.GetSuggestedAmount()).Ceil()
	suggestionComponent := tieringSuggestions.GetSuggestionForTiering(tieringPitchDetails)
	primaryCTA := getPrimaryCta(tieringPitchDetails)
	if !isPitchEnabled {
		minAmount = &moneypb.Money{CurrencyCode: "INR", Units: conf.AddFundsParams().AddFundsV3Params().MinAmount()}
		suggestedAmount = &moneypb.Money{CurrencyCode: "INR", Units: conf.AddFundsParams().AddFundsV3Params().DefaultAmount()}
		suggestionComponent = nil
		primaryCTA = fallbackCta()
	}
	return &afV2Pb.AddFundsMainComponent{
		Title:                      commontypes.GetTextFromStringFontColourFontStyle(AddFundsMainComponentTitle, AddFundsMainComponentTitleFontColour, commontypes.FontStyle_SUBTITLE_2),
		BgColour:                   widgetPb.GetBlockBackgroundColour(AddFundsMainComponentBgColour),
		DefaultAmount:              getDefaultAmount(conf, minAmount, suggestedAmount),
		Suggestion:                 suggestionComponent,
		PaymentModesHeading:        commontypes.GetTextFromStringFontColourFontStyle(PaymentModesHeadingText, PaymentModesHeadingTextFontColour, commontypes.FontStyle_SUBTITLE_S),
		ViewMoreText:               commontypes.GetTextFromStringFontColourFontStyle(ViewMoreText, ViewMoreTextFontColour, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		Footer:                     getFooter(),
		PrimaryCta:                 primaryCTA,
		FallbackCta:                fallbackCta(),
		AddFundsUpiPaymentModeList: getUpiPaymentModeList(conf),
		MaxAmountErrorSuggestion:   errorSuggestions.GetSuggestionForError(),
		ToShowPaymentApps:          commontypes.BoolToBooleanEnum(toShowPaymentApps),
	}
}

func getUpiPaymentModeList(conf *genconf.Config) []*afV2Pb.AddFundsUpiPaymentMode {
	var resUpiPaymentModeList []*afV2Pb.AddFundsUpiPaymentMode
	upiAppDetailsList := conf.AddFundsV2Params().UpiAppUrnPrefixMap()
	for upiAppName, upiAppDetails := range upiAppDetailsList {
		resUpiPaymentModeList = append(resUpiPaymentModeList, &afV2Pb.AddFundsUpiPaymentMode{
			TitleText:       commontypes.GetTextFromStringFontColourFontStyle(upiAppDetails.DisplayName, UpiPaymentModeFontColour, commontypes.FontStyle_BODY_XS),
			DisplayImage:    &commontypes.Image{ImageUrl: upiAppDetails.ImageUrl},
			UpiApp:          afV2Pb.UpiApp(afV2Pb.UpiApp_value[upiAppName]),
			UpiAppUriPrefix: upiAppDetails.UrnPrefix,
		})
	}

	return resUpiPaymentModeList
}

func getDefaultAmount(conf *genconf.Config, minAmount, suggestedAmount *moneypb.Money) *afV2Pb.AddFundsMainAmountComponent {
	return &afV2Pb.AddFundsMainAmountComponent{
		BgColour:                   widgetPb.GetBlockBackgroundColour(AddFundsMainAmountComponentBgColour),
		MinAmount:                  typesPb.GetFromBeMoney(minAmount),
		MinAmountDisplayText:       commontypes.GetTextFromStringFontColourFontStyle(pkgMoney.ToDisplayStringInIndianFormat(minAmount, 0, true), AddFundsMainAmountComponentAmountFontColour, commontypes.FontStyle_NUMBERS_3),
		SuggestedAmount:            typesPb.GetFromBeMoney(suggestedAmount),
		SuggestedAmountDisplayText: commontypes.GetTextFromStringFontColourFontStyle(pkgMoney.ToDisplayStringInIndianFormat(suggestedAmount, 0, true), SuggestedAmountDisplayTextFontColour, commontypes.FontStyle_NUMBER_S),
		MaxAmount:                  typesPb.GetFromBeMoney(&moneypb.Money{CurrencyCode: "INR", Units: conf.AddFundsParams().AddFundsV3Params().MaxAmount()}),
	}
}

func getFooter() *afV2Pb.AddFundsFooter {
	return &afV2Pb.AddFundsFooter{
		Image:     &commontypes.Image{ImageUrl: FooterImageUrl},
		BankImage: &commontypes.Image{ImageUrl: FooterBankImageUrl},
		Text:      commontypes.GetTextFromStringFontColourFontStyle(FooterText, AddFundsFooterTextColour, commontypes.FontStyle_BODY_4),
	}
}

func getPrimaryCta(tieringPitchDetails *tieringPb.TieringPitchAddFundsDetails) *deeplink.Cta {
	if tieringPitchDetails.GetIsRetentionPitch() {
		return fallbackCta()
	}
	return primaryCta()
}

// GetAMBAddFundsV3Params creates add funds V3 screen details with AMB card instead of tiering card
func GetAMBAddFundsV3Params(
	ctx context.Context,
	conf *genconf.Config,
	actorID string,
	dataCollector tieringData.DataCollector,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	appPlatform commontypes.Platform,
	appVersion uint32,
	tieringClient tieringPb.TieringClient,
) (*afV2Pb.AddFundsV2ScreenDetails, error) {
	// Get AMB data for the actor
	ambData, err := amb.GetAMBData(ctx, actorID, dataCollector, tieringPinotClient, tieringClient)
	if err != nil {
		return nil, err
	}

	return &afV2Pb.AddFundsV2ScreenDetails{
		Title:                  commontypes.GetTextFromStringFontColourFontStyle(AddFundsScreenTitle, AddFundsScreenTitleFontColour, commontypes.FontStyle_HEADLINE_M),
		BgColour:               widgetPb.GetBlockBackgroundColour(AddFundsBgColour),
		TopCard:                tieringCardOptions.GetAddFundsTopCardForAMB(ctx, conf, ambData, appPlatform, appVersion),
		PrimaryAddFundsDetails: getAddFundsPrimaryDetailsForAMB(conf, ambData),
		IntentThreshold: &typesPb.Money{
			CurrencyCode: "INR",
			Units:        conf.AddFundsParams().AddFundsV3Params().IntentThreshold(),
		},
		IntentFlowBottomSheet:  getIntentFlowBottomSheet(),
		CollectFlowBottomSheet: getCollectFlowBottomSheet(conf),
	}, nil
}

// getAddFundsPrimaryDetailsForAMB creates primary details component for AMB
func getAddFundsPrimaryDetailsForAMB(conf *genconf.Config, ambData *amb.AMBData) *afV2Pb.AddFundsMainComponent {
	// Use the amount needed to maintain AMB as the suggested amount
	suggestedAmount := ambData.AmountNeeded
	// Set a minimum amount to add
	minAmount := &moneypb.Money{CurrencyCode: "INR", Units: conf.AddFundsParams().AddFundsV3Params().MinAmount()}

	return &afV2Pb.AddFundsMainComponent{
		Title:                      commontypes.GetTextFromStringFontColourFontStyle(AddFundsMainComponentTitle, AddFundsMainComponentTitleFontColour, commontypes.FontStyle_SUBTITLE_2),
		BgColour:                   widgetPb.GetBlockBackgroundColour(AddFundsMainComponentBgColour),
		DefaultAmount:              getDefaultAmount(conf, minAmount, suggestedAmount),
		PaymentModesHeading:        commontypes.GetTextFromStringFontColourFontStyle(PaymentModesHeadingText, PaymentModesHeadingTextFontColour, commontypes.FontStyle_SUBTITLE_S),
		ViewMoreText:               commontypes.GetTextFromStringFontColourFontStyle(ViewMoreText, ViewMoreTextFontColour, commontypes.FontStyle_OVERLINE_2XS_CAPS),
		Footer:                     getFooter(),
		PrimaryCta:                 fallbackCta(), // Use a default CTA for AMB
		FallbackCta:                fallbackCta(),
		AddFundsUpiPaymentModeList: getUpiPaymentModeList(conf),
		MaxAmountErrorSuggestion:   errorSuggestions.GetSuggestionForError(),
		ToShowPaymentApps:          commontypes.BooleanEnum_FALSE,
	}
}
