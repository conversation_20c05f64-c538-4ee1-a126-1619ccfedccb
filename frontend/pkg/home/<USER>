package home

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"

	networthPb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/pkg/common"

	pkgUser "github.com/epifi/gamma/pkg/user"
)

// DirectToFiLiteVariantVariablePath is the quest variable path for direct to fi lite variant
const DirectToFiLiteVariantVariablePath = "user/Onboarding/DirectToFiLite/Variant"

// GetSuitableUserTypeRequest is the request struct for GetSuitableUserType.
type GetSuitableUserTypeRequest struct {
	ActorId      string
	ExternalDeps *common.ExternalDependencies
}

// GetActorId safely returns the ActorId.
func (r *GetSuitableUserTypeRequest) GetActorId() string {
	if r == nil {
		return ""
	}
	return r.ActorId
}

// GetExternalDeps safely returns the common.ExternalDependencies.
func (r *GetSuitableUserTypeRequest) GetExternalDeps() *common.ExternalDependencies {
	if r == nil {
		return nil
	}
	return r.ExternalDeps
}

// Validate checks if the request is valid.
func (r *GetSuitableUserTypeRequest) Validate(ctx context.Context) error {
	if r == nil {
		return fmt.Errorf("request is nil")
	}
	if r.GetActorId() == "" {
		return fmt.Errorf("actorId is empty")
	}
	externalDeps := r.GetExternalDeps()
	if externalDeps == nil {
		return fmt.Errorf("external dependencies is nil")
	}
	if externalDeps.GetOnboardingClient() == nil {
		return fmt.Errorf("onboarding client is nil")
	}
	if externalDeps.GetUserAttributeFetcher() == nil {
		return fmt.Errorf("user attribute fetcher is nil")
	}
	if externalDeps.GetNetWorthClient() == nil {
		return fmt.Errorf("networth client is nil")
	}
	return nil
}

// GetSuitableUserType returns the most suitable user type to generate home layout, shortcuts, etc
func GetSuitableUserType(ctx context.Context, req *GetSuitableUserTypeRequest) (UserType, *onboarding.GetDetailsResponse, *pkgUser.IsNonResidentUserResponse, error) {
	if err := req.Validate(ctx); err != nil {
		logger.Error(ctx, "GetSuitableUserType: request validation failed", zap.Error(err))
		return UserTypeUnspecified, nil, nil, err
	}

	// Initialize required variables from request
	var (
		actorId              = req.GetActorId()
		externalDeps         = req.GetExternalDeps()
		onboardingClient     = externalDeps.GetOnboardingClient()
		userAttributeFetcher = externalDeps.GetUserAttributeFetcher()
		getDetailsRes        *onboarding.GetDetailsResponse
		isNRUserRes          *pkgUser.IsNonResidentUserResponse
		isNRUser             bool
	)
	errGrp, grpCtx := errgroup.WithContext(ctx)
	errGrp.Go(func() error {
		var err error
		getDetailsRes, err = onboardingClient.GetDetails(grpCtx, &onboarding.GetDetailsRequest{
			ActorId:    actorId,
			CachedData: true,
		})
		if rpcErr := epifigrpc.RPCError(getDetailsRes, err); rpcErr != nil {
			logger.Error(grpCtx, "failed to get user details", zap.Error(rpcErr))
			return fmt.Errorf("failed to get user details, err: %w", rpcErr)
		}
		return nil
	})
	errGrp.Go(func() error {
		var err error
		isNRUserRes, err = userAttributeFetcher.IsNonResidentUser(grpCtx, &pkgUser.IsNonResidentUserRequest{
			ActorId: actorId,
		})
		if err != nil {
			logger.Error(grpCtx, "error in IsNonResidentUser", zap.Error(err))
			return fmt.Errorf("error in IsNonResidentUser, err: %w", err)
		}
		isNRUser = isNRUserRes.GetIsNonResidentUser()
		return nil
	})
	if err := errGrp.Wait(); err != nil {
		logger.Error(ctx, "failed to get user details", zap.Error(err))
		return UserTypeUnspecified, getDetailsRes, isNRUserRes, err
	}

	onboardingIntent := getDetailsRes.GetDetails().GetStageMetadata().GetIntentSelectionMetadata().GetSelection()
	featureInfo := getDetailsRes.GetDetails().GetFeatureDetails().GetFeatureInfo()
	isFiLiteEnabled := getDetailsRes.GetDetails().GetFiLiteDetails().GetIsEnabled()
	stageDetails := getDetailsRes.GetDetails().GetStageDetails().GetStageMapping()

	if isNRUser {
		return UserTypeFiNR, getDetailsRes, isNRUserRes, nil
	}

	// for old users, all were SA (feature) one's and hence feature info was always nil (wasn't enabled).
	// Hence, checking if feature info is nil, then if onboardingIntent is UNSPECIFIED, return the SA specific layout.
	if featureInfo == nil && onboardingIntent == onboarding.OnboardingIntent_ONBOARDING_INTENT_UNSPECIFIED && isFiLiteEnabled != commontypes.BooleanEnum_TRUE {
		return UserTypeFiSA, getDetailsRes, isNRUserRes, nil
	}

	// return layout for first primary activated/inactivated feature SA > CC > PL
	switch {
	case featureInfo[onboarding.Feature_FEATURE_SA.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE,
		featureInfo[onboarding.Feature_FEATURE_SA.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_INACTIVE:
		currentTime := time.Now()
		completedAt := featureInfo[onboarding.Feature_FEATURE_SA.String()].GetCompletedAt().AsTime()
		daysSinceCompletion := currentTime.Sub(completedAt).Hours() / 24
		// if user onboarded via SA within a time duration, we return the corresponding UserType
		// else we return UserTypeFiSA as default
		switch {
		case daysSinceCompletion <= 7:
			return UserTypeFiSAWithD0To7, getDetailsRes, isNRUserRes, nil
		case daysSinceCompletion <= 14:
			return UserTypeFiSAWithD8To14, getDetailsRes, isNRUserRes, nil
		case daysSinceCompletion <= 28:
			return UserTypeFiSAWithD15To28, getDetailsRes, isNRUserRes, nil
		default:
			return UserTypeFiSA, getDetailsRes, isNRUserRes, nil
		}
	// if user is old user (featureInfo would be nil) and onboardingIntent is not UNSPECIFIED due to user started some other flow after SA creation,
	// then check for below account creation stage details to confirm if user is a SA user or not.
	case stageDetails[onboarding.OnboardingStage_ACCOUNT_CREATION.String()].GetState() == onboarding.OnboardingState_SUCCESS:
		return UserTypeFiSA, getDetailsRes, isNRUserRes, nil
	case featureInfo[onboarding.Feature_FEATURE_CC.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE, featureInfo[onboarding.Feature_FEATURE_CC.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_INACTIVE:
		return UserTypeFiLiteCC, getDetailsRes, isNRUserRes, nil
	case featureInfo[onboarding.Feature_FEATURE_PL.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE, featureInfo[onboarding.Feature_FEATURE_PL.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_INACTIVE:
		return UserTypeFiLitePL, getDetailsRes, isNRUserRes, nil
	case featureInfo[onboarding.Feature_FEATURE_WEALTH_ANALYSER.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE, featureInfo[onboarding.Feature_FEATURE_WEALTH_ANALYSER.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_INACTIVE:
		return getWealthAnalyserUserType(ctx, actorId, externalDeps.GetNetWorthClient()), getDetailsRes, isNRUserRes, nil
	case featureInfo[onboarding.Feature_FEATURE_UPI_TPAP.String()].GetFeatureStatus() == onboarding.FeatureStatus_FEATURE_STATUS_ACTIVE:
		return UserTypeUpiTPAP, getDetailsRes, isNRUserRes, nil
	}

	// if there are no primary activated/inactivated features, use onboarding intent
	switch onboardingIntent {
	case onboarding.OnboardingIntent_ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT:
		return UserTypeFiLite, getDetailsRes, isNRUserRes, nil
	case onboarding.OnboardingIntent_ONBOARDING_INTENT_CREDIT_CARD:
		return UserTypeFiLiteCC, getDetailsRes, isNRUserRes, nil
	case onboarding.OnboardingIntent_ONBOARDING_INTENT_PERSONAL_LOANS:
		return UserTypeFiLitePL, getDetailsRes, isNRUserRes, nil
	case onboarding.OnboardingIntent_ONBOARDING_INTENT_WEALTH_ANALYSER, onboarding.OnboardingIntent_ONBOARDING_INTENT_NET_WORTH:
		return getWealthAnalyserUserType(ctx, actorId, externalDeps.GetNetWorthClient()), getDetailsRes, isNRUserRes, nil
	case onboarding.OnboardingIntent_ONBOARDING_INTENT_FI_LITE:
		return UserTypeFiLite, getDetailsRes, isNRUserRes, nil
	default:
		// return default type
		return UserTypeFiLite, getDetailsRes, isNRUserRes, nil
	}
}

func getWealthAnalyserUserType(ctx context.Context, actorId string, networthClient networthPb.NetWorthClient) UserType {
	nwValueResp, err := networthClient.GetNetWorthValue(ctx, &networthPb.GetNetWorthValueRequest{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(nwValueResp, err); rpcErr != nil {
		logger.Error(ctx, "error in fetching net worth value", zap.Error(rpcErr))
		return UserTypeWealthAnalyser
	}
	if nwValueResp.GetStatus().IsSuccess() {
		for _, assetValue := range nwValueResp.GetAssetValues() {
			if assetValue.GetComputationStatus() == networthPb.ComputationStatus_COMPUTATION_STATUS_NOT_FOUND {
				continue
			}
			return UserTypeWealthAnalyser
		}
	}
	return UserTypeNoAssetConnectedWealthAnalyser
}

func IsWealthAnalyserUser(userType UserType) bool {
	return userType == UserTypeWealthAnalyser || userType == UserTypeNoAssetConnectedWealthAnalyser
}
