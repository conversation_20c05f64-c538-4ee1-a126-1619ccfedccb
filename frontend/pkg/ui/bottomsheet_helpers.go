package ui

import (
	"context"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/pkg/feature/release"
)

// GetBottomSheetTopPadding evaluates the FEATURE_SHOW_UPDATED_BOTTOM_SHEET feature flag
// and returns the appropriate top padding value for SDUI bottom sheets.
// Returns 0px for legacy behavior (flag disabled) or 60px for enhanced behavior (flag enabled).
func GetBottomSheetTopPadding(ctx context.Context, releaseEvaluator release.IEvaluator, actorId string) int32 {
	// Evaluate feature flag to determine if user's app version supports the updated bottomsheet with no hardcoded padding on client
	// This checks app version constraints, rollout percentage, and user group constraints from FeatureReleaseConfig
	showUpdatedBottomSheet, err := releaseEvaluator.Evaluate(ctx,
		release.NewCommonConstraintData(typesv2.Feature_FEATURE_SHOW_UPDATED_BOTTOM_SHEET).WithActorId(actorId))
	if err != nil {
		// Log error and default to false for graceful degradation
		logger.Error(ctx, "failed to evaluate FEATURE_SHOW_UPDATED_BOTTOM_SHEET feature flag",
			zap.String("actorId", actorId),
			zap.Error(err))
		showUpdatedBottomSheet = false
	}

	// Determine top padding based on feature flag evaluation
	topPadding := int32(0) // Default padding
	if showUpdatedBottomSheet {
		topPadding = 60 // Enhanced padding
	}

	logger.Debug(ctx, "bottom sheet top padding determined by feature flag",
		zap.String("actorId", actorId),
		zap.Bool("showUpdatedBottomSheet", showUpdatedBottomSheet),
		zap.Int32("topPadding", topPadding))

	return topPadding
}
