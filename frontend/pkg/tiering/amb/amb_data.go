package amb

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/jonboulle/clockwork"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/pkg/tiering"

	"github.com/epifi/gamma/api/tiering/external"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/tiering/helper"
	rewardsPkg "github.com/epifi/gamma/pkg/rewards"
)

// AMBData contains all the information needed for AMB-related features
type AMBData struct {
	CurrentAMB         *moneyPb.Money
	TargetAMB          *moneyPb.Money
	AmountNeeded       *moneyPb.Money
	ProgressPercentage int32
	PenaltyAmount      *moneyPb.Money
	RewardsAtStake     string
	CurrentTier        external.Tier
	TieringEssentials  *helper.TieringFeEssentials
	CurrentBalance     *moneyPb.Money // Current account balance
	TrialsResponse     *tieringPb.GetTrialDetailsResponse
}

// GetAMBData fetches and calculates all the data needed for AMB-related features
func GetAMBData(
	ctx context.Context,
	actorID string,
	dataCollector tieringData.DataCollector,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	tieringClient tieringPb.TieringClient,
) (*AMBData, error) {
	return GetAMBDataWithClock(ctx, actorID, dataCollector, tieringPinotClient, clockwork.NewRealClock(), tieringClient)
}

// GetAMBDataWithClock is the same as GetAMBData but accepts a clock for testing time-dependent behavior
func GetAMBDataWithClock(
	ctx context.Context,
	actorID string,
	dataCollector tieringData.DataCollector,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	clock clockwork.Clock,
	tieringClient tieringPb.TieringClient,
) (*AMBData, error) {
	// Get tiering essentials which contains current tier, previous tier, tier min values, etc.
	tieringEssentials, err := dataCollector.GetTieringEssentials(ctx, actorID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tiering essentials: %w", err)
	}

	// Get current tier information from tiering essentials
	currentTier := tieringEssentials.GetCurrentTier()

	// Get account balance information
	balanceInfo, err := dataCollector.GetBalance(ctx, actorID)
	if err != nil {
		return nil, fmt.Errorf("failed to get account balance info: %w", err)
	}

	trialsResponse, trialsErr := tieringClient.GetTrialDetails(ctx, &tieringPb.GetTrialDetailsRequest{
		ActorId: actorID,
	})
	if rpcErr := epifigrpc.RPCError(trialsResponse, trialsErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get trial details: %w", rpcErr)
	}

	// Get target AMB for the current tier
	// If available in tieringEssentials, use it directly
	targetAMB, err := tiering.GetTargetAMBFromTieringPitch(tieringEssentials.GetTieringPitchResp(), tieringEssentials.GetGetConfigParamsResp().GetRegularTierConfigParams(), trialsResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to get target AMB for tier: %w", err)
	}

	// Get penalty amount if applicable
	penaltyAmount := getPenaltyAmount(tieringEssentials)

	// Calculate projected AMB and amount needed
	currentAMB, amountNeeded, err := calculateCurrentAMBAndShortfall(ctx, actorID, balanceInfo, targetAMB, tieringPinotClient, tieringEssentials, clock)
	if err != nil {
		return nil, fmt.Errorf("failed to calculate current AMB: %w", err)
	}

	// Calculate progress percentage
	progressPercentage := calculateProgressPercentage(currentAMB, targetAMB)

	// Get cashback percentages based on tier for rewards at stake
	cashbackPercentage := getCashbackPercentageForTier(currentTier)

	return &AMBData{
		CurrentAMB:         money.ParseFloat(currentAMB, money.RupeeCurrencyCode),
		TargetAMB:          money.ParseFloat(targetAMB, money.RupeeCurrencyCode),
		AmountNeeded:       money.ParseFloat(amountNeeded, money.RupeeCurrencyCode),
		ProgressPercentage: progressPercentage,
		PenaltyAmount:      penaltyAmount,
		RewardsAtStake:     cashbackPercentage,
		CurrentTier:        currentTier,
		TieringEssentials:  tieringEssentials,
		CurrentBalance:     balanceInfo, // Pass the current balance from GetBalance
		TrialsResponse:     trialsResponse,
	}, nil
}

// calculateCurrentAMBAndShortfall calculates the current AMB based on account balance and historical data
// It also calculates the amount needed to reach the target AMB
// Note: Although named "currentAMB", this function returns a projected AMB value (projected through end of month).
// We maintain the name "currentAMB" since that is how this information is presented, and we ensure it is
// what users can expect their AMB to be at the end of the month if they maintain their current balance.
func calculateCurrentAMBAndShortfall(
	ctx context.Context,
	actorID string,
	balanceInfo *moneyPb.Money,
	targetAMB float64,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	tieringEssentials *helper.TieringFeEssentials,
	clock clockwork.Clock,
) (currentAMB float64, amountNeeded float64, err error) {
	// Get current time in IST
	currentTime := clock.Now().Local()
	currentYear, currentMonth, currentDay := currentTime.Date()

	// Get the current balance
	currentBalance, _ := money.ToDecimal(balanceInfo).Float64()

	// Get the start and end of the current month
	startOfMonth := time.Date(currentYear, currentMonth, 1, 0, 0, 0, 0, currentTime.Location())

	// For tiers other than REGULAR, use the later of (month start, tier movement timestamp)
	fromDate := startOfMonth
	tier := tieringEssentials.GetCurrentTier()
	var daysSoFar int
	if tier != external.Tier_TIER_FI_REGULAR && tier != external.Tier_TIER_UNSPECIFIED && tier != external.Tier_TIER_FI_BASIC {
		tierMovementTimestamp := tieringEssentials.GetLastMovementTimestamp()
		// Only use tier movement timestamp if it's after the month start and not in the future
		if tierMovementTimestamp.After(startOfMonth) && tierMovementTimestamp.Before(currentTime) {
			fromDate = tierMovementTimestamp
			// Update the days so far based on the tier movement date
			daysSoFar = currentDay - tierMovementTimestamp.Day() + 1
		} else {
			daysSoFar = currentDay
		}
	} else {
		daysSoFar = currentDay
	}

	// Calculate the total days in the current month
	endOfMonth := startOfMonth.AddDate(0, 1, 0).Add(-time.Second)
	totalDaysInMonth := endOfMonth.Day()

	// Calculate the number of days in the relevant period (from fromDate to endOfMonth)
	// Ensure fromDate is not after endOfMonth (edge case)
	effectiveEndDate := endOfMonth
	if fromDate.After(effectiveEndDate) {
		fromDate = effectiveEndDate
	}
	// Calculate duration and add 1 day because both start and end are inclusive
	totalDaysInRelevantPeriod := effectiveEndDate.Day() - fromDate.Day() + 1

	// Get average EOD balance for the current month up to yesterday
	fromTimestamp := timestamppb.New(fromDate)
	// Yesterday's end of day
	toTimestamp := timestamppb.New(time.Date(currentYear, currentMonth, currentDay-1, 23, 59, 59, 0, currentTime.Location()))

	// If we're on the first day of the month or tier changed today, we don't have any EOD balance data yet
	var avgEODBalance float64
	if daysSoFar > 1 && fromDate.Before(currentTime.AddDate(0, 0, -1)) {
		// Get average EOD balance from the tiering pinot service
		eodBalanceResp, err := tieringPinotClient.GetAverageEODBalanceInDateRange(ctx, &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
			ActorId:       actorID,
			FromTimestamp: fromTimestamp,
			ToTimestamp:   toTimestamp,
		})

		if rpcErr := epifigrpc.RPCError(eodBalanceResp, err); rpcErr != nil {
			// EOD data does not exist in non prod pinot
			env, _ := cfg.GetEnvironment()
			if cfg.IsStagingEnv(env) || cfg.IsQaEnv(env) {
				avgEODBalance = 4986.5
			} else {
				logger.Error(ctx, "failed to get average EOD balance", zap.Error(rpcErr))
				return 0, 0, fmt.Errorf("failed to get average EOD balance: %w", rpcErr)
			}
		} else {
			avgEODBalance = eodBalanceResp.GetAvgBalance()
		}
	}

	// Sum for days that have passed within the relevant period (excluding today)
	var passedDaysSum float64
	numberOfPastDaysInPeriod := float64(daysSoFar - 1)
	if numberOfPastDaysInPeriod < 0 {
		numberOfPastDaysInPeriod = 0
	}

	if daysSoFar == 1 {
		// If it's the first day of the month or tier changed today, there are no past days
		passedDaysSum = 0
	} else {
		// Sum of balances for days that have already passed
		passedDaysSum = avgEODBalance * numberOfPastDaysInPeriod
	}

	// Calculate the number of remaining days (including today) until the end of the month
	remainingDays := float64(totalDaysInMonth - currentDay + 1)

	// Ensure remainingDays is at least 1 to avoid division by zero
	if remainingDays < 1 {
		remainingDays = 1
	}

	// Calculate projected EOD sum for remaining days (assuming current balance for all remaining days)
	projectedRemainingDaysSum := currentBalance * remainingDays

	// Calculate total projected sum for the relevant period
	projectedTotalSum := passedDaysSum + projectedRemainingDaysSum

	// Calculate projected AMB for the entire month (this is what we'll return as "currentAMB")
	// Although named "currentAMB" in the return values, this is actually the projected AMB through end of month
	projectedAMB := projectedTotalSum / float64(totalDaysInRelevantPeriod)

	if projectedAMB >= targetAMB {
		return projectedAMB, 0, nil
	}

	// Calculate the target sum needed for the relevant period
	targetSumRelevantPeriod := targetAMB * float64(totalDaysInRelevantPeriod)

	// Calculate the sum required from today until the end of the month
	sumNeededFromToday := targetSumRelevantPeriod - passedDaysSum

	// Calculate the average balance needed for the remaining days
	avgBalanceNeededForRemainingDays := sumNeededFromToday / remainingDays

	// The amount needed is the difference between the required average daily balance for the remaining days
	// and the current balance. This tells the user how much to add right now to stay on track.
	amountNeeded = avgBalanceNeededForRemainingDays - currentBalance

	// Ensure amount needed is not negative
	amountNeeded = math.Max(0, amountNeeded)

	return projectedAMB, amountNeeded, nil
}

// getPenaltyAmount gets the penalty amount for not maintaining AMB
func getPenaltyAmount(tieringEssentials *helper.TieringFeEssentials) *moneyPb.Money {
	tier := tieringEssentials.GetCurrentTier()
	// Check if tier criteria min values are available for the requested tier
	if tier != external.Tier_TIER_FI_REGULAR {
		return nil
	}

	return tieringEssentials.GetGetConfigParamsResp().GetRegularTierConfigParams().GetMinBalancePenaltyForRegularTier()
}

// getCashbackPercentageForTier returns the cashback percentage for a given tier
func getCashbackPercentageForTier(tier external.Tier) string {
	percent := rewardsPkg.BeTierToCashbackFactorMap[tier] * 100
	return fmt.Sprintf("%.0f%%", percent)
}

// calculateProgressPercentage calculates the progress percentage based on projected and target AMB
func calculateProgressPercentage(currentAMB, targetAMB float64) int32 {
	// Convert to Money objects for safe comparison
	targetAMBMoney := money.ParseFloat(targetAMB, money.RupeeCurrencyCode)

	// If target AMB is zero or negative, return 0
	if money.IsZero(targetAMBMoney) {
		return 0
	}

	// Calculate percentage
	percentage := (currentAMB / targetAMB) * 100

	// Cap percentage at 100%
	if percentage > 100 {
		return 100
	}
	return int32(percentage)
}
