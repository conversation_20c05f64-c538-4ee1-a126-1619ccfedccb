package saclosure

import (
	"context"
	"fmt"
	"time"

	"github.com/samber/lo"

	"github.com/epifi/be-common/pkg/epifierrors"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	genCfg "github.com/epifi/be-common/pkg/frontend/app/genconf"
	"github.com/epifi/be-common/pkg/logger"
	typesPkg "github.com/epifi/be-common/pkg/types"

	beMoney "github.com/epifi/be-common/pkg/money"

	bankCustPb "github.com/epifi/gamma/api/bankcust"
	ticketPb "github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
	"github.com/epifi/gamma/api/frontend/deeplink"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/sa_closure"
	infoPb "github.com/epifi/gamma/api/typesv2/info"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/group"
	"github.com/epifi/gamma/frontend/account/saclosure/criteria/item"
	"github.com/epifi/gamma/frontend/account/saclosure/helper"
	"github.com/epifi/gamma/frontend/account/saclosure/orchestrator"
	"github.com/epifi/gamma/frontend/account/saclosure/savings"
	"github.com/epifi/gamma/frontend/account/saclosure/screen"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/pkg/accrual"
	"github.com/epifi/gamma/pkg/deeplinkv3"
)

var (
	saClosureCriteriaItemToClosureReasonStringMap = map[enums.SaClosureCriteriaItem]string{
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_UNSPECIFIED:             "unspecified reason",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_JUMP:                    "Jump Investment",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SMART_DEPOSIT:           "Smart Deposit",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FIXED_DEPOSIT:           "Fixed Deposit",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_MUTUAL_FUND:             "Mutual Fund",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_US_STOCK:                "US Stocks Investments",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_INDIAN_STOCK:            "Indian Stocks Investments",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_VEHICLE_LOAN:            "Vehicle Loan",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_PERSONAL_LOAN:           "Personal Loan",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_EDUCATION_LOAN:          "Education Loan",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_HOME_LOAN:               "Home Loan",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_OTHER_LOAN:              "Other Loan",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AMPLIFI_CREDIT_CARD:     "Amplifi credit card",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SIMPLIFI_CREDIT_CARD:    "Simplifi credit card",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY:                "Auto Pay",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY_SIP:            "Auto Pay SIP",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_FI_COIN_BALANCE:         accrual.ReplaceCoinWithPointIfApplicable("Zero Fi coin balance", nil),
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_SAVINGS_ACCOUNT_BALANCE: "Zero savings account balance",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_MAGNIFI_CREDIT_CARD:     "Magnifi credit card",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_AUTO_SAVE:               "Auto save",
		enums.SaClosureCriteriaItem_SA_CLOSURE_CRITERIA_ITEM_UPI_LITE:                "UPI Lite",
	}
	saClosureKycLevel                    = "USER KYC LEVEL"
	saClosureAccountLien                 = "Account Lien"
	saClosurePendingCharges              = "Pending Charges"
	saClosurePendingChargesAmount        = "Pending Charges Amount (with GST)"
	saClosurePendingChargesAmountRounded = "Pending Charges Amount (with GST) Rounded"
	saClosureAccountCreditFreeze         = "Credit Freeze"
	saClosureAccountDebitFreeze          = "Debit Freeze"
	saClosureAccountFreezeStatus         = "Account Freeze Status"
	saClosureAccountClosed               = "Is Account Closed"
	pending                              = "Pending"
	done                                 = "Done"
	yes                                  = "YES"
	no                                   = "NO"
	maxWidth                             = 45
	getInternalErrorScreen               = func() *errorsPb.ErrorView {
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_FULL_SCREEN_V2,
			Options: &errorsPb.ErrorView_FullScreenErrorViewV2{
				FullScreenErrorViewV2: &errorsPb.FullScreenErrorViewV2{
					Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(screen.ErrorScreenImageUrl, screen.ErrorScreenImageHeight, screen.ErrorScreenImageWidth),
					Title:    commontypes.GetTextFromStringFontColourFontStyle(screen.ErrorScreenTitle, screen.ErrorScreenTitleColor, commontypes.FontStyle_HEADLINE_L),
					Subtitle: commontypes.GetTextFromHtmlStringFontColourFontStyle(screen.ErrorScreenSubTitle, screen.ErrorScreenSubTitleColor, commontypes.FontStyle_BODY_S),
					Ctas: []*deeplink.Cta{
						{
							Type:         deeplink.Cta_RETRY,
							Text:         screen.ErrorScreenCtaText,
							DisplayTheme: deeplink.Cta_PRIMARY,
							Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						},
					},
					ArrangeCtasVertically: true,
					BackgroundColor:       screen.WhiteColor,
				},
			},
		}
	}

	getUnableToSubmitErrorScreen = func() *errorsPb.ErrorView {
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_FULL_SCREEN_V2,
			Options: &errorsPb.ErrorView_FullScreenErrorViewV2{
				FullScreenErrorViewV2: &errorsPb.FullScreenErrorViewV2{
					Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(screen.ErrorScreenImageUrl, screen.ErrorScreenImageHeight, screen.ErrorScreenImageWidth),
					Title:    commontypes.GetTextFromStringFontColourFontStyle(screen.FailedSubmissionTitle, screen.ErrorScreenTitleColor, commontypes.FontStyle_HEADLINE_L),
					Subtitle: commontypes.GetTextFromStringFontColourFontStyle(screen.FailedSubmissionSubTitle, screen.ErrorScreenSubTitleColor, screen.ErrorScreenSubTitleFont),
					Ctas: []*deeplink.Cta{
						{
							Type:         deeplink.Cta_RETRY,
							Text:         screen.ErrorScreenCtaText,
							DisplayTheme: deeplink.Cta_PRIMARY,
							Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						},
						{
							Type:         deeplink.Cta_DONE,
							Text:         screen.FailedSubmission2ndaryCtaText,
							DisplayTheme: deeplink.Cta_SECONDARY,
							Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						},
					},
					BackgroundColor: screen.WhiteColor,
				},
			},
		}
	}

	getIncorrectPanDobErrorScreen = func(title, subtitle string) *errorsPb.ErrorView {
		return &errorsPb.ErrorView{
			Type: errorsPb.ErrorViewType_FULL_SCREEN_V2,
			Options: &errorsPb.ErrorView_FullScreenErrorViewV2{
				FullScreenErrorViewV2: &errorsPb.FullScreenErrorViewV2{
					Image:    commontypes.GetVisualElementFromUrlHeightAndWidth(screen.ErrorScreenImageUrl, screen.ErrorScreenImageHeight, screen.ErrorScreenImageWidth),
					Title:    commontypes.GetTextFromStringFontColourFontStyle(title, screen.ErrorScreenTitleColor, commontypes.FontStyle_HEADLINE_L),
					Subtitle: commontypes.GetTextFromHtmlStringFontColourFontStyle(subtitle, screen.ErrorScreenSubTitleColor, commontypes.FontStyle_BODY_S),
					Ctas: []*deeplink.Cta{
						{
							Type:         deeplink.Cta_CONTINUE,
							Deeplink:     &deeplink.Deeplink{Screen: deeplink.Screen_PAN_DOB_INPUT_SCREEN},
							Text:         screen.IncorrectPanRetryCtaText,
							DisplayTheme: deeplink.Cta_PRIMARY,
							Status:       deeplink.Cta_CTA_STATUS_ENABLED,
						},
					},
					BackgroundColor:       screen.WhiteColor,
					ArrangeCtasVertically: true,
				},
			},
		}
	}
)

var idFieldMask = func() []savingsPb.SavingsAccountClosureRequestFieldMask {
	return []savingsPb.SavingsAccountClosureRequestFieldMask{
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ID,
	}
}

var idStatusFieldMask = func() []savingsPb.SavingsAccountClosureRequestFieldMask {
	return []savingsPb.SavingsAccountClosureRequestFieldMask{
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ID,
		savingsPb.SavingsAccountClosureRequestFieldMask_SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS,
	}
}

type SavingsAccountClosureService struct {
	saClosurePb.UnimplementedSavingsAccountClosureServer
	conf                 *genconf.Config
	dlOrchestrator       orchestrator.IDeeplinkOrchestrator
	savingsClient        savingsPb.SavingsClient
	screenFactory        screen.IScreenFactory
	criteriaGroupBuilder group.IBuilder
	usersClient          userPb.UsersClient
	savingsEvaluator     savings.Evaluator
	cxTicketClient       ticketPb.TicketClient
	itemParallelExecutor item.IParallelExecutor
	bcClient             bankCustPb.BankCustomerServiceClient
}

func NewSavingsAccountClosureService(conf *genconf.Config,
	dlOrchestrator orchestrator.IDeeplinkOrchestrator, savingsClient savingsPb.SavingsClient,
	screenFactor screen.IScreenFactory, criteriaGroupBuilder group.IBuilder,
	usersClient userPb.UsersClient, savingsEvaluator savings.Evaluator,
	cxTicketClient ticketPb.TicketClient, itemParallelExecutor item.IParallelExecutor,
	bcClient bankCustPb.BankCustomerServiceClient) *SavingsAccountClosureService {
	return &SavingsAccountClosureService{
		conf:                 conf,
		dlOrchestrator:       dlOrchestrator,
		savingsClient:        savingsClient,
		screenFactory:        screenFactor,
		criteriaGroupBuilder: criteriaGroupBuilder,
		usersClient:          usersClient,
		savingsEvaluator:     savingsEvaluator,
		cxTicketClient:       cxTicketClient,
		itemParallelExecutor: itemParallelExecutor,
		bcClient:             bcClient,
	}
}

func (s *SavingsAccountClosureService) GetSaClosureFlow(ctx context.Context, request *saClosurePb.GetSaClosureFlowRequest) (*saClosurePb.GetSaClosureFlowResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	freezeAction, err := s.dlOrchestrator.GetPreClosureNextActionForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "failed to evaluate freeze action for actor", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))

		status := rpc.StatusInternalWithDebugMsg(err.Error())
		if errors.Is(err, savings.ErrGetOperationalStatusApiFailure) {
			status = &rpc.Status{
				Code:         uint32(saClosurePb.GetSaClosureFlowResponse_FETCH_OPERATIONAL_STATUS_FAILURE),
				ShortMessage: saClosurePb.GetSaClosureFlowResponse_FETCH_OPERATIONAL_STATUS_FAILURE.String(),
				DebugMessage: err.Error(),
			}
		}
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			status = rpc.StatusRecordNotFound()
		}
		return &saClosurePb.GetSaClosureFlowResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	if freezeAction != nil {
		return &saClosurePb.GetSaClosureFlowResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			NextAction: freezeAction,
		}, nil
	}

	nextAction, err := s.dlOrchestrator.GetNextActionForActor(ctx, &orchestrator.GetNextActionParams{
		ActorId:    actorId,
		CurrScreen: deeplink.Screen_DEEP_LINK_URI_UNSPECIFIED,
		EntryPoint: helper.GetBeEntryPoint(request.GetEntryPoint()),
	})
	if err != nil {
		logger.Error(ctx, "failed to get next action for user", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.GetSaClosureFlowResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	return &saClosurePb.GetSaClosureFlowResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: nextAction,
	}, nil

}

func (s *SavingsAccountClosureService) GetSaClosureBenefits(ctx context.Context, request *saClosurePb.GetSaClosureBenefitsRequest) (*saClosurePb.GetSaClosureBenefitsResponse, error) {
	displayValues := s.conf.SavingsAccountClosure().DisplayValues().BenefitsScreen()

	benefitsSectionHeader := ui.NewITC().WithTexts(genCfg.NewTextPb(displayValues.BenefitsSectionHeaderText(), ""))
	benefitsSectionHeader.WithRightVisualElement(genCfg.NewVisualElementImagePb(displayValues.BenefitsSectionHeaderIcon())).
		WithContainer(0, 0, 0, displayValues.BenefitsSectionProperties().BgColor()).
		WithContainerPadding(0, 0, 0, 20).
		WithRightImagePadding(12)

	var benefitItems []*widget.VisualElementTitleSubtitleElement
	for _, item := range displayValues.BenefitItems() {
		benefitItems = append(benefitItems, &widget.VisualElementTitleSubtitleElement{
			VisualElement:   typesPkg.NewVisualElementImage(item.Icon),
			TitleText:       typesPkg.NewText(item.Title, ""),
			SubtitleText:    typesPkg.NewText(item.SubTitle, ""),
			BackgroundColor: item.BgColor,
		})
	}

	return &saClosurePb.GetSaClosureBenefitsResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageTitle:             genCfg.NewTextPb(displayValues.PageTitle(), ""),
		BenefitsSectionHeader: benefitsSectionHeader,
		BenefitItems:          benefitItems,
		ProceedCta: &deeplink.Cta{
			Type: deeplink.Cta_CUSTOM,
			Text: displayValues.ProceedCta().DisplayString(),
			Deeplink: &deeplink.Deeplink{
				Screen: deeplink.Screen_API_SA_CLOSURE_LANDING_PAGE,
			},
			DisplayTheme: deeplink.Cta_PRIMARY,
		},
		CancelCta: &deeplink.Cta{
			Type:         deeplink.Cta_DONE,
			Text:         displayValues.CancelCta().DisplayString(),
			DisplayTheme: deeplink.Cta_TERTIARY,
		},
	}, nil
}

// nolint: funlen
func (s *SavingsAccountClosureService) SubmitSaClosureUserFeedback(ctx context.Context, request *saClosurePb.SubmitSaClosureUserFeedbackRequest) (*saClosurePb.SubmitSaClosureUserFeedbackResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	if request.GetFeedbackText() == "" {
		logger.Error(ctx, "feedback should not be empty", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternal(),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	closureRequestResp, err := s.savingsClient.CreateOrGetSaClosureRequest(ctx, &savingsPb.CreateOrGetSaClosureRequestRequest{
		ActorId:    actorId,
		FieldMasks: idFieldMask(),
	})
	if rpcErr := epifigrpc.RPCError(closureRequestResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get closure request for actor", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	closureRequestId := closureRequestResp.GetClosureRequest().GetId()

	recordFeedbackResp, recordFeedbackErr := s.savingsClient.RecordSaClosureUserFeedback(ctx, &savingsPb.RecordSaClosureUserFeedbackRequest{
		ClosureRequestId: closureRequestId,
		UserFeedback:     &savingsPb.SaClosureRequestUserFeedback{FeedbackText: request.GetFeedbackText()},
	})
	if rpcErr := epifigrpc.RPCError(recordFeedbackResp, recordFeedbackErr); rpcErr != nil {
		logger.Error(ctx, "failed to record sa closure feedback", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestId))
		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	if request.GetCreateSupportTicket() {
		submittedTicketDl, createTicketErr := s.createSupportTicketOnFeedback(ctx, actorId, closureRequestId, request.GetFeedbackText())
		if createTicketErr != nil {
			logger.Error(ctx, "failed to create support ticket on feedback for user", zap.Error(createTicketErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestId))
			return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg(createTicketErr.Error()),
					ErrorView: getInternalErrorScreen(),
				},
			}, nil
		}

		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
			NextAction: submittedTicketDl,
		}, nil
	}

	updateStatusResp, updateStatusErr := s.savingsClient.UpdateSaClosureRequestStatus(ctx, &savingsPb.UpdateSaClosureRequestStatusRequest{
		ClosureRequestId: closureRequestId,
		Status:           savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_FEEDBACK_RECEIVED,
	})
	if rpcErr := epifigrpc.RPCError(updateStatusResp, updateStatusErr); rpcErr != nil {
		logger.Error(ctx, "failed to update sa closure status", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestId))
		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	nextAction, nextActionErr := s.dlOrchestrator.GetNextActionForActor(ctx, &orchestrator.GetNextActionParams{
		ActorId:    actorId,
		CurrScreen: deeplink.Screen_SA_CLOSURE_USER_FEEDBACK_SCREEN,
	})
	if nextActionErr != nil {
		logger.Error(ctx, "failed get next action after submitting feedback", zap.Error(nextActionErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestId))
		return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(nextActionErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	return &saClosurePb.SubmitSaClosureUserFeedbackResponse{
		RespHeader: &header.ResponseHeader{Status: rpc.StatusOk()},
		NextAction: nextAction,
	}, nil
}

func (s *SavingsAccountClosureService) isTicketActive(ticket *ticketPb.Ticket) bool {
	if ticket.GetStatus() == ticketPb.Status_STATUS_CLOSED ||
		ticket.GetStatus() == ticketPb.Status_STATUS_RESOLVED {
		return false
	}

	if time.Since(ticket.GetCreatedAt().AsTime()) > s.conf.SavingsAccountClosure().SupportTicketExpiryDuration() {
		return false
	}

	return true
}

// checks if there is already a ticket with same issue category id
// if no ticket of same type is created in last 1 month (check config for exact value), a new ticket is created
func (s *SavingsAccountClosureService) createSupportTicket(ctx context.Context, actorId, issueCategoryId,
	subject, description string) error {

	getTicketResp, getTicketErr := s.cxTicketClient.GetSupportTickets(ctx, &ticketPb.GetSupportTicketsRequest{
		TicketFilters: &ticketPb.TicketFilters{
			ActorIdList:     []string{actorId},
			IssueCategoryId: issueCategoryId,
		},
	})
	if rpcErr := epifigrpc.RPCError(getTicketResp, getTicketErr); rpcErr != nil && !getTicketResp.GetStatus().IsRecordNotFound() {
		return errors.Wrapf(rpcErr, "failed to fetch ticket with issue category id %s for actor", issueCategoryId)
	}

	for _, ticket := range getTicketResp.GetTickets() {
		if s.isTicketActive(ticket) {
			logger.Info(ctx, "user already has an active ticket",
				zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.TICKET_ID, ticket.GetId()))
			return nil
		}
	}

	getUserResp, getUserErr := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(getUserResp, getUserErr); rpcErr != nil {
		return errors.Wrap(rpcErr, "failed to get user details to fetch email id")
	}

	createTicketResp, createTicketErr := s.cxTicketClient.CreateTicket(ctx, &ticketPb.CreateTicketRequest{Ticket: &ticketPb.Ticket{
		CustomFields:    &ticketPb.CustomFields{},
		Subject:         subject,
		Description:     description,
		Email:           getUserResp.GetUser().GetProfile().GetEmail(),
		Priority:        ticketPb.Priority_PRIORITY_HIGH,
		Status:          ticketPb.Status_STATUS_OPEN,
		CreatedAt:       timestamppb.Now(),
		UpdatedAt:       timestamppb.Now(),
		ActorId:         actorId,
		IssueCategoryId: issueCategoryId,
		Attachments:     nil,
	}})
	if rpcErr := epifigrpc.RPCError(createTicketResp, createTicketErr); rpcErr != nil {
		return errors.Wrap(rpcErr, "failed to create a support ticket for user")
	}

	return nil
}

func (s *SavingsAccountClosureService) createSupportTicketOnFeedback(ctx context.Context, actorId, closureReqId, feedbackText string) (*deeplink.Deeplink, error) {
	issueCategoryId := s.conf.SavingsAccountClosure().IssueCategoryIds().UnResolvedIssueFeedback()
	createTicketErr := s.createSupportTicket(ctx, actorId, issueCategoryId, feedbackText, feedbackText)
	if createTicketErr != nil {
		return nil, errors.Wrap(createTicketErr, "failed to create support ticket")
	}

	updateStatusResp, updateStatusErr := s.savingsClient.UpdateSaClosureRequestStatus(ctx, &savingsPb.UpdateSaClosureRequestStatusRequest{
		ClosureRequestId: closureReqId,
		Status:           savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUPPORT_TICKET_CREATED_AND_CLOSED,
	})
	if rpcErr := epifigrpc.RPCError(updateStatusResp, updateStatusErr); rpcErr != nil {
		return nil, rpcErr
	}

	screenBuilder, getScreenBuilderErr := s.screenFactory.GetScreenBuilder(deeplink.Screen_SA_CLOSURE_FEEDBACK_TICKET_SUBMITTED_SCREEN)
	if getScreenBuilderErr != nil {
		return nil, errors.Wrapf(getScreenBuilderErr, "failed to get screen builder from factory for %s", deeplink.Screen_SA_CLOSURE_FEEDBACK_TICKET_SUBMITTED_SCREEN.String())
	}

	dl, buildScreenErr := screenBuilder.BuildScreen(ctx, nil)
	if buildScreenErr != nil {
		return nil, errors.Wrapf(getScreenBuilderErr, "failed to build screen %s from builder", deeplink.Screen_SA_CLOSURE_FEEDBACK_TICKET_SUBMITTED_SCREEN)
	}

	return dl, nil
}

func (s *SavingsAccountClosureService) FetchSaClosureCriteriasForUser(ctx context.Context, request *saClosurePb.FetchSaClosureCriteriasForUserRequest) (*saClosurePb.FetchSaClosureCriteriasForUserResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()
	appPlatform := request.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := request.GetReq().GetAuth().GetDevice().GetAppVersion()

	criteriaGroups, criteriaGroupErr := s.criteriaGroupBuilder.BuildCriteriaGroups(ctx, actorId)
	if criteriaGroupErr != nil {
		logger.Error(ctx, "failed get criteria groups", zap.Error(criteriaGroupErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.FetchSaClosureCriteriasForUserResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(criteriaGroupErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	criteriaGroups[0].IsCollapsed = false

	nextAction, nextActionErr := s.dlOrchestrator.GetNextActionForActor(ctx, &orchestrator.GetNextActionParams{
		ActorId:    actorId,
		CurrScreen: deeplink.Screen_SA_CLOSURE_CRITERIA_SCREEN,
	})
	if nextActionErr != nil {
		return &saClosurePb.FetchSaClosureCriteriasForUserResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(nextActionErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	ctaStatus := deeplink.Cta_CTA_STATUS_DISABLED
	if s.isNextActionAllowed(criteriaGroups) {
		ctaStatus = deeplink.Cta_CTA_STATUS_ENABLED
	}

	disallowProceedText := screen.CriteriaScreenDisallowProceedText
	if s.isNextActionUnAllowedDueToAppVersionConstraint(appPlatform, appVersion, criteriaGroups) {
		disallowProceedText = screen.CriteriaScreenUpdateAppDisallowProceedText
	}

	return &saClosurePb.FetchSaClosureCriteriasForUserResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		PageTitle:      commontypes.GetTextFromStringFontColourFontStyle(screen.CriteriaScreenTitleText, screen.CriteriaScreenTitleColor, commontypes.FontStyle_HEADLINE_L),
		PageSubtitle:   commontypes.GetTextFromStringFontColourFontStyle(screen.CriteriaScreenSubTitleText, screen.CriteriaScreenSubTitleColor, commontypes.FontStyle_BODY_S),
		CriteriaGroups: criteriaGroups,
		ProceedCta: &deeplink.Cta{
			Type:         deeplink.Cta_CUSTOM,
			Deeplink:     nextAction,
			Text:         "Proceed",
			DisplayTheme: deeplink.Cta_PRIMARY,
			Status:       ctaStatus,
		},
		DisallowProceedText: commontypes.GetTextFromStringFontColourFontStyle(disallowProceedText, screen.CriteriaScreenDisallowProceedTextColor, commontypes.FontStyle_BODY_XS),
	}, nil
}

// proceed action is unallowed if user is in lower app version and there is a criteria item with FullGroupCriteriaItem
func (s *SavingsAccountClosureService) isNextActionUnAllowedDueToAppVersionConstraint(appPlatform commontypes.Platform, appVersion uint32, criteriaGroups []*saClosurePb.CriteriaGroup) bool {
	switch appPlatform {
	case commontypes.Platform_ANDROID:
		if appVersion >= s.conf.SavingsAccountClosure().FullGroupCriteiaItemMinAppVersions().MinVersionAndroid() {
			return false
		}
	case commontypes.Platform_IOS:
		if appVersion >= s.conf.SavingsAccountClosure().FullGroupCriteiaItemMinAppVersions().MinVersionIos() {
			return false
		}
	default:
	}

	for _, criteriaGroup := range criteriaGroups {
		if criteriaGroup.GetFullGroupCriteriaItem() != nil {
			return true
		}
	}

	return false
}

func (s *SavingsAccountClosureService) isNextActionAllowed(criteriaGroups []*saClosurePb.CriteriaGroup) bool {
	for _, criteriaGroup := range criteriaGroups {
		for _, criteriaItem := range criteriaGroup.GetCriteriaItemList().GetCriteriaItems() {
			if criteriaItem.GetMeta().GetFetchFailed() {
				return false
			}

			if criteriaItem.GetMeta().GetSoftCriteria() {
				continue
			}

			// disallow next action if there is any hard criteria item
			return false
		}

		if criteriaGroup.GetFullGroupCriteriaItem() != nil {
			return false
		}
	}

	return true
}

// nolint:funlen
func (s *SavingsAccountClosureService) ValidatePanDobForSaClosure(ctx context.Context, request *saClosurePb.ValidatePanDobForSaClosureRequest) (*saClosurePb.ValidatePanDobForSaClosureResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()

	getUserResp, getUserErr := s.usersClient.GetUser(ctx, &userPb.GetUserRequest{
		Identifier: &userPb.GetUserRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(getUserResp, getUserErr); rpcErr != nil {
		logger.Error(ctx, "failed to get user details from user client", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.ValidatePanDobForSaClosureResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	var invalidPan, invalidDob bool
	panFromDb := getUserResp.GetUser().GetProfile().GetPAN()
	if request.GetPan() != panFromDb {
		invalidPan = true
	}

	dobFromDb := getUserResp.GetUser().GetProfile().GetDateOfBirth()
	if !datetime.DateEquals(dobFromDb, datetime.TimeToDateInLoc(request.GetDob().AsTime(), nil)) {
		invalidDob = true

	}

	var errorView *errorsPb.ErrorView
	switch {
	case invalidPan && invalidDob:
		errorView = getIncorrectPanDobErrorScreen(screen.IncorrectPanAndDobTitle, screen.IncorrectPanAndDobSubTitle)
	case invalidPan:
		errorView = getIncorrectPanDobErrorScreen(screen.IncorrectPanTitle, screen.IncorrectPanSubTitle)
	case invalidDob:
		errorView = getIncorrectPanDobErrorScreen(screen.IncorrectDobTitle, screen.IncorrectDobSubTitle)
	default:
		errorView = nil
	}

	if errorView != nil {
		return &saClosurePb.ValidatePanDobForSaClosureResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInvalidArgument(),
				ErrorView: errorView,
			},
		}, nil
	}

	nextAction, err := s.dlOrchestrator.GetNextActionForActor(ctx, &orchestrator.GetNextActionParams{
		ActorId:    actorId,
		CurrScreen: deeplink.Screen_PAN_DOB_INPUT_SCREEN,
	})
	if err != nil {
		return &saClosurePb.ValidatePanDobForSaClosureResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	return &saClosurePb.ValidatePanDobForSaClosureResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: nextAction,
	}, nil
}

const (
	closureForAccountWithFreezeSubject     = "SA Closure - credit freeze"
	closureForAccountWithFreezeDescription = "savings account closure request for account with credit freeze"
)

func (s *SavingsAccountClosureService) createSupportTicketForFreeze(ctx context.Context, actorId string, freezeRes *savings.EvaluatePreClosureResult) (*deeplink.Deeplink, error) {
	issueCategoryId := s.conf.SavingsAccountClosure().IssueCategoryIds().CloseAccountsWithCreditFreeze()
	createTicketErr := s.createSupportTicket(ctx, actorId, issueCategoryId, closureForAccountWithFreezeSubject, closureForAccountWithFreezeDescription)
	if createTicketErr != nil {
		return nil, errors.Wrap(createTicketErr, "failed to create support ticket for user")
	}

	builder, getBuilderErr := s.screenFactory.GetScreenBuilder(deeplink.Screen_SA_CLOSURE_REQUEST_SUBMITTED_SCREEN)
	if getBuilderErr != nil {
		return nil, errors.Wrap(getBuilderErr, "failed to get screen builder")
	}

	dl, buildScreenErr := builder.BuildScreen(ctx, &screen.BuildScreenParams{EvaluatePreClosureResult: freezeRes})
	if buildScreenErr != nil {
		return nil, errors.Wrap(buildScreenErr, "failed to build screen")
	}

	return dl, nil
}

// nolint:funlen
func (s *SavingsAccountClosureService) SubmitClosureRequest(ctx context.Context, request *saClosurePb.SubmitClosureRequestRequest) (*saClosurePb.SubmitClosureRequestResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()

	evalRes, evalErr := s.savingsEvaluator.EvaluatePreClosureStartChecks(ctx, &savings.EvaluateAccountFreezeParams{ActorId: actorId})
	if evalErr != nil {
		logger.Error(ctx, "failed to evaluate account freeze", zap.Error(evalErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		status := rpc.StatusInternalWithDebugMsg(evalErr.Error())
		if errors.Is(evalErr, savings.ErrGetOperationalStatusApiFailure) {
			status = &rpc.Status{
				Code:         uint32(saClosurePb.SubmitClosureRequestResponse_FETCH_OPERATIONAL_STATUS_FAILURE),
				ShortMessage: saClosurePb.SubmitClosureRequestResponse_FETCH_OPERATIONAL_STATUS_FAILURE.String(),
				DebugMessage: evalErr.Error(),
			}
		}
		if errors.Is(evalErr, epifierrors.ErrRecordNotFound) {
			status = rpc.StatusRecordNotFound()
		}
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    status,
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	if evalRes.GetHasCreditFreeze() && !evalRes.GetHasDebitFreeze() {
		nextAction, supportTicketErr := s.createSupportTicketForFreeze(ctx, actorId, evalRes)
		if supportTicketErr != nil {
			logger.Error(ctx, "failed to create support ticket for account with freeze", zap.Error(supportTicketErr), zap.Any(logger.ACTOR_ID_V2, actorId))
			return &saClosurePb.SubmitClosureRequestResponse{
				RespHeader: &header.ResponseHeader{
					Status:    rpc.StatusInternalWithDebugMsg(supportTicketErr.Error()),
					ErrorView: getInternalErrorScreen(),
				},
			}, nil
		}

		logger.Info(ctx, "user has credit freeze, support ticket will be created", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusOk(),
			},
			NextAction: nextAction,
		}, nil
	}

	hasPendingCharges := beMoney.IsPositive(evalRes.GetConsolidatedPendingAmount())
	if hasPendingCharges {
		logger.Error(ctx, "failing since user has pending charge", zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg("failing since user has pending charge"),
				ErrorView: getInternalErrorScreen(), // returning internal since this is handled in orchestration service
			},
		}, nil
	}

	closureRequestResp, err := s.savingsClient.CreateOrGetSaClosureRequest(ctx, &savingsPb.CreateOrGetSaClosureRequestRequest{
		ActorId:    actorId,
		FieldMasks: idFieldMask(),
	})
	if rpcErr := epifigrpc.RPCError(closureRequestResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get closure request for actor", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	updateStatusResp, updateStatusErr := s.savingsClient.UpdateSaClosureRequestStatus(ctx, &savingsPb.UpdateSaClosureRequestStatusRequest{
		ClosureRequestId: closureRequestResp.GetClosureRequest().GetId(),
		Status:           savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUBMITTED,
	})
	if rpcErr := epifigrpc.RPCError(updateStatusResp, updateStatusErr); rpcErr != nil {
		logger.Error(ctx, "failed to update sa closure status", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestResp.GetClosureRequest().GetId()))
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	nextAction, err := s.dlOrchestrator.GetNextActionForActor(ctx, &orchestrator.GetNextActionParams{
		ActorId:    actorId,
		CurrScreen: deeplink.Screen_SA_CLOSURE_SUBMIT_REQUEST_SWIPE_ACTION_SCREEN,
	})
	if err != nil {
		return &saClosurePb.SubmitClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	return &saClosurePb.SubmitClosureRequestResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: nextAction,
	}, nil
}

// nolint: funlen
func (s *SavingsAccountClosureService) CancelClosureRequest(ctx context.Context, request *saClosurePb.CancelClosureRequestRequest) (*saClosurePb.CancelClosureRequestResponse, error) {
	actorId := request.GetReq().GetAuth().GetActorId()

	closureRequestResp, err := s.savingsClient.CreateOrGetSaClosureRequest(ctx, &savingsPb.CreateOrGetSaClosureRequestRequest{
		ActorId:    actorId,
		FieldMasks: idStatusFieldMask(),
	})
	if rpcErr := epifigrpc.RPCError(closureRequestResp, err); rpcErr != nil {
		logger.Error(ctx, "failed to get closure request for actor", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &saClosurePb.CancelClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	var statusToUpdate savingsPb.SAClosureRequestStatus
	switch closureRequestResp.GetClosureRequest().GetStatus() {
	case savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_SUBMITTED:
		statusToUpdate = savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY
	case savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_INITIATED, savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_FEEDBACK_RECEIVED:
		statusToUpdate = savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY_BEFORE_SUBMISSION
	default:
	}

	updateStatusResp, updateStatuErr := s.savingsClient.UpdateSaClosureRequestStatus(ctx, &savingsPb.UpdateSaClosureRequestStatusRequest{
		ClosureRequestId: closureRequestResp.GetClosureRequest().GetId(),
		Status:           statusToUpdate,
	})
	if rpcErr := epifigrpc.RPCError(updateStatusResp, updateStatuErr); rpcErr != nil {
		logger.Error(ctx, "failed to update sa closure status", zap.Error(rpcErr), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestResp.GetClosureRequest().GetId()))
		return &saClosurePb.CancelClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(rpcErr.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	if statusToUpdate == savingsPb.SAClosureRequestStatus_SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY {

	}

	// todo: move to screen package if needed
	screenOptionsV2, err := deeplinkv3.GetScreenOptionV2(&sa_closure.SaClosureRequestSubmittedScreenOptions{
		FullScreenInfoView: &infoPb.FullScreenInfoView{
			Image: commontypes.GetVisualElementFromUrlHeightAndWidth(screen.ResolveIssuesScreenIconUrl, screen.UnResolvedIssuesScreenIconWidth, screen.UnResolvedIssuesScreenIconHeight),
			Title: commontypes.GetTextFromStringFontColourFontStyle(screen.ClosureCanceledTitle, screen.ClosureCanceledTitleColor, commontypes.FontStyle_HEADLINE_L),
			Body:  commontypes.GetTextFromStringFontColourFontStyle(screen.ClosureCanceledSubTitle, screen.ClosureCanceledSubTitleColor, commontypes.FontStyle_BODY_S),
			Ctas: []*deeplink.Cta{
				{
					Type:         deeplink.Cta_DONE,
					Text:         screen.SubmitSuccess1stCtaText,
					DisplayTheme: deeplink.Cta_PRIMARY,
				},
			},
			BackgroundColor:       screen.WhiteColor,
			ArrangeCtasVertically: true,
		},
	})
	if err != nil {
		logger.Error(ctx, "failed to get thank you screen", zap.Error(err), zap.Any(logger.ACTOR_ID_V2, actorId), zap.Any(logger.SA_CLOSURE_ID, closureRequestResp.GetClosureRequest().GetId()))
		return &saClosurePb.CancelClosureRequestResponse{
			RespHeader: &header.ResponseHeader{
				Status:    rpc.StatusInternalWithDebugMsg(err.Error()),
				ErrorView: getInternalErrorScreen(),
			},
		}, nil
	}

	nextAction := &deeplink.Deeplink{
		Screen:          deeplink.Screen_SA_CLOSURE_REQUEST_SUBMITTED_SCREEN,
		ScreenOptionsV2: screenOptionsV2,
	}

	return &saClosurePb.CancelClosureRequestResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		NextAction: nextAction,
	}, nil
}

// TODO: Move this logic to a BE service. Frontend should mostly handle presentation layer. Currently, other services are depending here to get the eligibility; move to some other BE service. Github Issue : https://github.com/epiFi/tickets/issues/58469

func (s *SavingsAccountClosureService) EvaluateUserForClosureEligibility(ctx context.Context, request *saClosurePb.EvaluateUserForClosureEligibilityRequest) (*saClosurePb.EvaluateUserForClosureEligibilityResponse, error) {
	evalRes, evalErr := s.savingsEvaluator.EvaluatePreClosureStartChecks(ctx, &savings.EvaluateAccountFreezeParams{ActorId: request.GetActorId()})
	if evalErr != nil {
		logger.Error(ctx, "failed to evaluate on account freeze", zap.Error(evalErr))
		status := rpc.StatusInternalWithDebugMsg(evalErr.Error())
		if errors.Is(evalErr, savings.ErrGetOperationalStatusApiFailure) {
			status = &rpc.Status{
				Code:         uint32(saClosurePb.EvaluateUserForClosureEligibilityResponse_FETCH_OPERATIONAL_STATUS_FAILURE),
				ShortMessage: saClosurePb.EvaluateUserForClosureEligibilityResponse_FETCH_OPERATIONAL_STATUS_FAILURE.String(),
				DebugMessage: evalErr.Error(),
			}
		}

		if errors.Is(evalErr, savings.ErrSavingsAccountNotFound) {
			status = rpc.StatusRecordNotFound()
		}
		return &saClosurePb.EvaluateUserForClosureEligibilityResponse{
			RespHeader: &header.ResponseHeader{
				Status: status,
			},
		}, nil
	}

	criterias, collectDataErr := s.itemParallelExecutor.ExecuteDataCollection(ctx, request.GetActorId(), group.GetAllowedCriteriaItems())

	if collectDataErr != nil {
		logger.Error(ctx, "failed to collect criteria data for user", zap.Error(collectDataErr))
		return &saClosurePb.EvaluateUserForClosureEligibilityResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg(collectDataErr.Error()),
			},
		}, nil
	}

	var failureReasons []string
	var failedCriteriaCheck bool
	var pendingReasons, ignoredPendingReasons []enums.SaClosureCriteriaItem
	var evaluatedCriteriaItems []*saClosurePb.SaClosureCriteriaItemStatus

	bcResp, err := s.bcClient.GetBankCustomer(ctx, &bankCustPb.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankCustPb.GetBankCustomerRequest_ActorId{
			ActorId: request.GetActorId(),
		},
	})

	if err = epifigrpc.RPCError(bcResp, err); err != nil {
		logger.Error(ctx, "failed to get bank customer info", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.String(logger.VENDOR, commonvgpb.Vendor_FEDERAL_BANK.String()), zap.Error(err))
	} else {
		failureReasons = addToList(failureReasons, saClosureKycLevel, bcResp.GetBankCustomer().GetKycInfo().GetKycLevel().String())
	}

	for itemEnum, meta := range criterias {
		if meta.GetFetchFailed() {
			logger.Error(ctx, "criteria fetch failed", zap.Any("item", itemEnum.String()),
				zap.Any(logger.ACTOR_ID_V2, request.GetActorId()))
			return &saClosurePb.EvaluateUserForClosureEligibilityResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("criteria fetch failed, %s", itemEnum.String())),
				},
			}, nil
		}

		if meta.GetSoftCriteria() {
			ignoredPendingReasons = append(ignoredPendingReasons, itemEnum)
			continue
		}

		if _, ok := saClosureCriteriaItemToClosureReasonStringMap[itemEnum]; ok {
			pendingReasons = append(pendingReasons, itemEnum)
		}
	}
	// FailedCriteriaCheck is true if there is any hard criteria item
	if len(pendingReasons) > 0 {
		failedCriteriaCheck = true
	}

	response := &saClosurePb.EvaluateUserForClosureEligibilityResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		FailedCriteriaCheck: failedCriteriaCheck,
		IsAccountClosed:     evalRes.GetAccountState() == savingsPb.State_CLOSED,
	}

	// populating statuses of evaluated criteria items
	for key, value := range saClosureCriteriaItemToClosureReasonStringMap {
		if lo.Contains(ignoredPendingReasons, key) {
			continue
		}
		if lo.Contains(pendingReasons, key) {
			failureReasons = addToList(failureReasons, value, pending)
			evaluatedCriteriaItems = append(evaluatedCriteriaItems, &saClosurePb.SaClosureCriteriaItemStatus{
				CriteriaItem: key,
				IsFailed:     true,
			})
		} else {
			failureReasons = addToList(failureReasons, value, done)
			evaluatedCriteriaItems = append(evaluatedCriteriaItems, &saClosurePb.SaClosureCriteriaItemStatus{
				CriteriaItem: key,
				IsFailed:     false,
			})
		}
	}

	response.EvaluatedCriteriaItems = evaluatedCriteriaItems

	switch {
	case evalRes.GetHasLienAmount():
		response.HasLien = true
		failureReasons = addToList(failureReasons, saClosureAccountLien, pending)
	default:
		failureReasons = addToList(failureReasons, saClosureAccountLien, done)
	}

	response.HasFreeze = evalRes.GetHasCreditFreeze() || evalRes.GetHasDebitFreeze()

	switch {
	case evalRes.GetHasCreditFreeze():
		failureReasons = addToList(failureReasons, saClosureAccountCreditFreeze, pending)
	default:
		failureReasons = addToList(failureReasons, saClosureAccountCreditFreeze, done)
	}

	switch {
	case evalRes.GetHasDebitFreeze():
		failureReasons = addToList(failureReasons, saClosureAccountDebitFreeze, pending)
	default:
		failureReasons = addToList(failureReasons, saClosureAccountDebitFreeze, done)
	}

	switch {
	case evalRes.GetHasCreditFreeze() && evalRes.GetHasDebitFreeze():
		failureReasons = addToList(failureReasons, saClosureAccountFreezeStatus, evalRes.GetAccountFreezeStatus().String())
	default:
		failureReasons = addToList(failureReasons, saClosureAccountFreezeStatus, done)
	}

	hasPendingCharges := beMoney.IsPositive(evalRes.GetConsolidatedPendingAmount())
	switch {
	case hasPendingCharges:
		response.HasPendingCharges = true
		response.PendingChargesAmount = evalRes.GetConsolidatedPendingAmountRounded()
		failureReasons = addToList(failureReasons, saClosurePendingCharges, pending)
		failureReasons = addToList(failureReasons, saClosurePendingChargesAmount, beMoney.ToDisplayStringWithPrecision(evalRes.GetConsolidatedPendingAmount(), 2))
		failureReasons = addToList(failureReasons, saClosurePendingChargesAmountRounded, beMoney.ToDisplayStringWithPrecision(evalRes.GetConsolidatedPendingAmountRounded(), 2))
	default:
		failureReasons = addToList(failureReasons, saClosurePendingCharges, done)
		failureReasons = addToList(failureReasons, saClosurePendingChargesAmount, "0")
	}

	switch evalRes.GetAccountState() {
	case savingsPb.State_CLOSED:
		failureReasons = addToList(failureReasons, saClosureAccountClosed, yes)
	default:
		failureReasons = addToList(failureReasons, saClosureAccountClosed, no)
	}

	response.FailureReasons = failureReasons
	return response, nil
}

func addToList(list []string, item string, status string) []string {
	list = append(list, fmt.Sprintf("%-*s - %s", maxWidth, item, status))
	return list
}
