//go:generate mockgen -source=parallel_executor.go -destination=mocks/mock_parallel_executor.go -package=mocks

package item

import (
	"context"

	"github.com/google/wire"
	"github.com/pkg/errors"

	"github.com/epifi/be-common/pkg/errgroup"

	"github.com/epifi/gamma/api/frontend/account/enums"
	saClosurePb "github.com/epifi/gamma/api/frontend/account/sa_closure"
)

type IParallelExecutor interface {
	// Execute executes the collection of criteria data and enrichment to saClosurePb.CriteriaItem in parallel
	Execute(ctx context.Context, actorId string, items []enums.SaClosureCriteriaItem) (map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem, error)
	// ExecuteDataCollection executes the collection of criteria data in parallel
	ExecuteDataCollection(ctx context.Context, actorId string, items []enums.SaClosureCriteriaItem) (map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta, error)
}

type ParallelExecutor struct {
	factory ICriteriaItemFactory
}

var CriteriaItemParallelExecutor = wire.NewSet(NewParallelExecutor, wire.Bind(new(IParallelExecutor), new(*ParallelExecutor)))

func NewParallelExecutor(factory ICriteriaItemFactory) *ParallelExecutor {
	return &ParallelExecutor{factory: factory}
}

func (p *ParallelExecutor) Execute(ctx context.Context, actorId string, items []enums.SaClosureCriteriaItem) (map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem, error) {
	itemMakers := make(map[ICriteriaItemMaker]bool)
	for _, item := range items {
		maker, err := p.factory.GetCriteriaItemMaker(item)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get item maker")
		}

		itemMakers[maker] = true
	}

	resultChan := make(chan map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem, len(itemMakers))
	g, _ := errgroup.WithContext(ctx)
	for itemMaker, ok := range itemMakers {
		if !ok {
			continue
		}

		itemMaker := itemMaker
		g.Go(func() error {
			criteriaItemsMap := make(map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem)

			criteriaMetas, collectDataErr := itemMaker.CollectData(ctx, actorId)
			if collectDataErr != nil {
				return errors.Wrapf(collectDataErr, "faield to collect data")
			}

			for itemEnum, meta := range criteriaMetas {
				item, buildItemErr := itemMaker.BuildItem(itemEnum, meta)
				if buildItemErr != nil {
					return errors.Wrapf(buildItemErr, "failed to build item for %s", itemEnum.String())
				}

				criteriaItemsMap[itemEnum] = item
			}

			resultChan <- criteriaItemsMap
			return nil
		})
	}

	err := g.Wait()
	if err != nil {
		return nil, errors.Wrap(err, "failed to collect and make item in err group")
	}

	close(resultChan)

	criteriaItemMap := make(map[enums.SaClosureCriteriaItem]saClosurePb.ICriteriaItem)
	for result := range resultChan {
		for key, val := range result {
			criteriaItemMap[key] = val
		}
	}

	return criteriaItemMap, nil
}

func (p *ParallelExecutor) ExecuteDataCollection(ctx context.Context, actorId string, items []enums.SaClosureCriteriaItem) (map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta, error) {
	itemMakers := make(map[ICriteriaItemMaker]bool)
	for _, item := range items {
		maker, err := p.factory.GetCriteriaItemMaker(item)
		if err != nil {
			return nil, errors.Wrap(err, "failed to get item maker")
		}

		itemMakers[maker] = true
	}

	resultChan := make(chan map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta, len(itemMakers))
	g, gCtx := errgroup.WithContext(ctx)
	for itemMaker, ok := range itemMakers {
		if !ok {
			continue
		}

		itemMaker := itemMaker
		g.Go(func() error {
			criteriaMetas, collectDataErr := itemMaker.CollectData(gCtx, actorId)
			if collectDataErr != nil {
				return errors.Wrapf(collectDataErr, "faield to collect data")
			}

			resultChan <- criteriaMetas
			return nil
		})
	}

	err := g.Wait()
	if err != nil {
		return nil, errors.Wrap(err, "failed to collect and make item in err group")
	}

	close(resultChan)

	criteriaMetaMap := make(map[enums.SaClosureCriteriaItem]*saClosurePb.CriteriaItemMeta)
	for result := range resultChan {
		for key, val := range result {
			criteriaMetaMap[key] = val
		}
	}

	return criteriaMetaMap, nil
}
