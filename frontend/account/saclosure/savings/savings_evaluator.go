//go:generate mockgen -source=savings_evaluator.go -destination=mocks/mock_evaluator.go -package=mocks

package savings

import (
	"context"
	"fmt"

	"github.com/google/wire"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/accounts/enums"
	operationalStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	savingsPb "github.com/epifi/gamma/api/savings"
)

var (
	ErrGetOperationalStatusApiFailure = fmt.Errorf("account operational status api failed")
	ErrSavingsAccountNotFound         = fmt.Errorf("savings account not found")
)

type Evaluator interface {
	// todo : fix all usages of this interface
	EvaluatePreClosureStartChecks(ctx context.Context, params *EvaluateAccountFreezeParams) (res *EvaluatePreClosureResult, err error)
}

var EvaluatorWireSet = wire.NewSet(NewSaAccountEvaluator, wire.Bind(new(Evaluator), new(*SaAccountEvaluator)))

type SaAccountEvaluator struct {
	savingsClient           savingsPb.SavingsClient
	operationalStatusClient operationalStatusPb.OperationalStatusServiceClient
}

func NewSaAccountEvaluator(savingsClient savingsPb.SavingsClient, operationalStatusClient operationalStatusPb.OperationalStatusServiceClient) *SaAccountEvaluator {
	return &SaAccountEvaluator{savingsClient: savingsClient, operationalStatusClient: operationalStatusClient}
}

func (f *SaAccountEvaluator) EvaluatePreClosureStartChecks(ctx context.Context, params *EvaluateAccountFreezeParams) (res *EvaluatePreClosureResult, err error) {
	getEssentialsResp, getEssentialsErr := f.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
		ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
			ActorId:     params.GetActorId(),
			PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
		}},
	})
	if rpcErr := epifigrpc.RPCError(getEssentialsResp, getEssentialsErr); rpcErr != nil {
		if getEssentialsResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("savings account not found, %s, %w", rpcErr.Error(), ErrSavingsAccountNotFound)
		}
		return nil, fmt.Errorf("failed to get savings account essentials, %w", rpcErr)
	}

	getOperStatusResp, getOperStatusErr := f.operationalStatusClient.GetOperationalStatus(ctx, &operationalStatusPb.GetOperationalStatusRequest{
		DataFreshness:     operationalStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_10_MIN_STALE,
		AccountIdentifier: &operationalStatusPb.GetOperationalStatusRequest_SavingsAccountId{SavingsAccountId: getEssentialsResp.GetAccount().GetId()},
	})
	if rpcErr := epifigrpc.RPCError(getOperStatusResp, getOperStatusErr); rpcErr != nil {
		return nil, fmt.Errorf("failed to get account operational status, %s, %w", rpcErr.Error(), ErrGetOperationalStatusApiFailure)
	}

	var hasCreditFreeze, hasDebitFreeze bool
	switch getOperStatusResp.GetOperationalStatusInfo().GetFreezeStatus() {
	case enums.FreezeStatus_FREEZE_STATUS_TOTAL_FREEZE:
		hasCreditFreeze = true
		hasDebitFreeze = true
	case enums.FreezeStatus_FREEZE_STATUS_DEBIT_FREEZE:
		hasDebitFreeze = true
	case enums.FreezeStatus_FREEZE_STATUS_CREDIT_FREEZE:
		hasCreditFreeze = true
	default:
	}

	hasLienAmount := money.IsPositive(getOperStatusResp.GetOperationalStatusInfo().GetTotalLienMarking())
	consolidatedPendingAmount := getOperStatusResp.GetOperationalStatusInfo().GetVendorResponse().GetFederalAccountStatusEnquiryResponse().GetConsolidatedPendingAmount()
	pendingChargesGst := money.ZeroINR().GetPb()
	consolidatedPendingAmountWithGst := money.ZeroINR().GetPb()
	consolidatedPendingAmountWithGstRounded := money.ZeroINR().GetPb()
	if money.IsPositive(consolidatedPendingAmount) {
		var calculateGstErr, sumErr error
		pendingChargesGst, calculateGstErr = money.CalculatePercentageOfMoney(18, consolidatedPendingAmount)
		if calculateGstErr != nil {
			return nil, fmt.Errorf("failed to calculate gst on pending charges, %w", calculateGstErr)
		}

		consolidatedPendingAmountWithGst, sumErr = money.Sum(consolidatedPendingAmount, pendingChargesGst)
		if sumErr != nil {
			return nil, fmt.Errorf("failed to sum pending charges with gst, %w", sumErr)
		}
		consolidatedPendingAmountWithGstRounded = &moneyPb.Money{
			Units:        consolidatedPendingAmountWithGst.GetUnits(),
			Nanos:        consolidatedPendingAmountWithGst.GetNanos(),
			CurrencyCode: consolidatedPendingAmountWithGst.GetCurrencyCode(),
		}
		if consolidatedPendingAmountWithGstRounded.GetNanos() > 0 {
			consolidatedPendingAmountWithGstRounded.Units += 1
			consolidatedPendingAmountWithGstRounded.Nanos = 0
		}
	}

	return &EvaluatePreClosureResult{
		HasCreditFreeze:                  hasCreditFreeze,
		HasDebitFreeze:                   hasDebitFreeze,
		HasLienAmount:                    hasLienAmount,
		ConsolidatedPendingAmount:        consolidatedPendingAmountWithGst,
		ConsolidatedPendingAmountRounded: consolidatedPendingAmountWithGstRounded,
		AccountFreezeStatus:              getOperStatusResp.GetOperationalStatusInfo().GetFreezeStatus(),
		AccountState:                     getEssentialsResp.GetAccount().GetState(),
	}, nil
}
