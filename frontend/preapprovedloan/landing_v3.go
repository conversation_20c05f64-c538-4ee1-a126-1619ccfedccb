package preapprovedloan

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/frontend/preapprovedloan/deeplinks/provider"
	helper2 "github.com/epifi/gamma/preapprovedloan/helper"

	"github.com/epifi/be-common/pkg/epificontext"

	"go.uber.org/zap"

	palFeEnumsPb "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	palFePb "github.com/epifi/gamma/api/frontend/preapprovedloan"
	palBePb "github.com/epifi/gamma/api/preapprovedloan"
	palEvents "github.com/epifi/gamma/frontend/preapprovedloan/events"
	"github.com/epifi/gamma/frontend/preapprovedloan/helper"
)

const (
	LandingStateNotEligible = iota
	LandingStateLoanDashboard
	LandingStateLoanOptions
)

func (s *Service) getDashboardV3(ctx context.Context, req *palFePb.GetDashboardRequest) (*palFePb.GetDashboardResponse, error) {
	res := &palFePb.GetDashboardResponse{
		RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()},
	}
	startTime := time.Now()
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, "", "", palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	beRes, beErr := s.preApprovedLoanClient.GetLandingInfoV3(ctx, &palBePb.GetLandingInfoV3Request{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if beErr != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLandingInfoV3 BE API failed", zap.Error(beErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	var latestLr *palBePb.LoanRequest
	var latestLse *palBePb.LoanStepExecution
	if len(beRes.GetLoanRequestInfos()) > 0 && !beRes.GetLoanRequestInfos()[0].GetLr().IsSuccess() {
		latestLr = beRes.GetLoanRequestInfos()[0].GetLr()
		latestLse = beRes.GetLoanRequestInfos()[0].GetLatestLse()
	}
	dl, err := s.aggregatedDeeplinkProvider.GetLoanDashboardDeepLinkV3(ctx, &palBePb.GetDashboardResponse{
		Status:            rpcPb.StatusOk(),
		RecentLoanRequest: latestLr,
		LoanInfoList:      beRes.GetLoanAccountInfos(),
		LoanSteps:         []*palBePb.LoanStepExecution{latestLse},
		LoanOptions:       beRes.GetLoanOptions(),
	})
	if err != nil {
		logger.Error(ctx, "error in GetLoanDashboardDeepLinkV3", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.Deeplink = dl.Deeplink

	s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, time.Since(startTime), palEvents.StatusSuccess, dl.EventSubStatus,
		"", latestLr.GetId(), palEvents.LoanManagementFlowV2, getEventsOwnershipFromAccountAndRequest(latestLr, beRes.GetLoanAccountInfos()), req.GetLoanHeader().GetEventData())

	return res, nil
}

func getEventsOwnershipFromAccountAndRequest(lr *palBePb.LoanRequest, lis []*palBePb.LoanInfo) string {
	if lr != nil {
		return getEventsOwnership(helper.GetPalFeVendorFromBe(lr.GetVendor()), helper.GetFeLoanProgramFromBe(lr.GetLoanProgram()))
	}
	for _, li := range lis {
		if li.GetLoanAccount().GetStatus() == palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			return getEventsOwnership(helper.GetPalFeVendorFromBe(li.GetLoanAccount().GetVendor()), helper.GetFeLoanProgramFromBe(li.GetLoanAccount().GetLoanProgram()))
		}
	}
	return ""
}

func (s *Service) getLandingInfoV3(ctx context.Context, req *palFePb.GetLandingInfoRequest) (*palFePb.GetLandingInfoResponse, error) {
	startTime := time.Now()
	res := &palFePb.GetLandingInfoResponse{RespHeader: &header.ResponseHeader{Status: rpcPb.StatusOk()}}
	s.PushApiTriggeredEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, "", "", palEvents.LoanManagementFlow,
		getEventsOwnership(req.GetLoanHeader().GetVendor(), req.GetLoanHeader().GetLoanProgram()), req.GetLoanHeader().GetEventData())

	beRes, beErr := s.preApprovedLoanClient.GetLandingInfoV3(ctx, &palBePb.GetLandingInfoV3Request{
		ActorId:    req.GetReq().GetAuth().GetActorId(),
		LoanHeader: helper.GetBeLoanHeaderByFeLoanHeader(req.GetLoanHeader()),
	})
	if beErr != nil {
		logger.Error(ctx, "preApprovedLoanClient.GetLandingInfoV3 BE API failed", zap.Error(beErr))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	err := applyFilters(beRes, req.GetLandingInfoFilters())
	if err != nil {
		logger.Error(ctx, "applyFilters failed", zap.Error(err))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}

	landingScreenState := LandingStateNotEligible

	var loanOptions []*palBePb.LoanOption
	for _, lOpt := range beRes.GetLoanOptions() {
		if lOpt.GetLoanOffer() != nil {
			platform, version := epificontext.AppPlatformAndVersion(ctx)
			if s.isUpdateNeeded(ctx, platform, version, lOpt.GetLoanOffer().GetVendor(), lOpt.GetLoanOffer().GetLoanProgram(), req.GetReq().GetAuth().GetActorId()) {
				ownership := getEventsOwnership(helper.GetPalFeVendorFromBe(lOpt.GetLoanOffer().GetVendor()), helper.GetFeLoanProgramFromBe(lOpt.GetLoanOffer().GetLoanProgram()))
				s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusUpdateForLoanOffer, lOpt.GetLoanOffer().GetId(), "", palEvents.LoanManagementFlow, ownership, req.GetLoanHeader().GetEventData())
				continue
			}
		}
		loanOptions = append(loanOptions, lOpt)
		landingScreenState = LandingStateLoanOptions
	}

	for _, li := range beRes.GetLoanAccountInfos() {
		if li.GetLoanAccount().GetStatus() == palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			landingScreenState = LandingStateLoanDashboard
			break
		}
	}

	var latestLr *palBePb.LoanRequest
	var latestLse *palBePb.LoanStepExecution
	if len(beRes.GetLoanRequestInfos()) > 0 && !beRes.GetLoanRequestInfos()[0].GetLr().IsSuccess() {
		latestLr = beRes.GetLoanRequestInfos()[0].GetLr()
		latestLse = beRes.GetLoanRequestInfos()[0].GetLatestLse()
	}

	if latestLr != nil {
		landingScreenState = LandingStateLoanDashboard
	}

	dl, err := s.createDlFromScreenState(ctx, landingScreenState, req, loanOptions, latestLse, latestLr, beRes.GetLoanAccountInfos(), beRes.GetRejectedLoanOffers())
	if err != nil {
		logger.Error(ctx, "error in creating dl from screen state", zap.Error(err), zap.Any("landing_screen_status", landingScreenState))
		res.RespHeader.Status = rpcPb.StatusInternal()
		res.RespHeader.ErrorView = defaultErrorView
		return res, nil
	}
	res.Deeplink = dl
	res.GetRespHeader().FeedbackEngineInfo = getFeedbackInfo(landingScreenState)
	s.sendLandingApiRespEvent(ctx, req.GetReq().GetAuth().GetActorId(), startTime, latestLr, beRes.GetLoanAccountInfos(), loanOptions, req.GetLoanHeader().GetEventData())

	return res, nil
}

func applyFilters(beRes *palBePb.GetLandingInfoV3Response, filters []string) error {
	for _, filter := range filters {
		switch filter {
		case palBePb.LandingInfoFilter_SHOW_ONLY_CURRENT_LR_OFFERS.String():
			if len(beRes.GetLoanRequestInfos()) == 0 {
				return errors.New("no loan request info found for filter")
			}
			filteredOptions := make([]*palBePb.LoanOption, 0)
			for _, lo := range beRes.GetLoanOptions() {
				if beRes.GetLoanRequestInfos()[0].GetLr().GetLoanProgram() == lo.GetLoanHeader().GetLoanProgram() && beRes.GetLoanRequestInfos()[0].GetLr().GetVendor() == lo.GetLoanHeader().GetVendor() {
					filteredOptions = append(filteredOptions, lo)
				}
			}
			beRes.LoanAccountInfos = nil
			beRes.LoanRequestInfos = nil
			beRes.LoanOptions = filteredOptions
		case palBePb.LandingInfoFilter_LANDING_INFO_SKIP_FAILED_LR_FETCH.String():
			if len(beRes.GetLoanRequestInfos()) > 0 && beRes.GetLoanRequestInfos()[0].GetLr().IsFailedTerminal() {
				beRes.LoanRequestInfos = nil
			}
		}
	}
	return nil
}

func (s *Service) sendLandingApiRespEvent(ctx context.Context, actorId string, startTime time.Time, lr *palBePb.LoanRequest, laInfos []*palBePb.LoanInfo, lopts []*palBePb.LoanOption, eventData *palFeEnumsPb.EventData) {
	var ownership string
	var la *palBePb.LoanAccount
	for _, laInfo := range laInfos {
		if laInfo.GetLoanAccount().GetStatus() == palBePb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE {
			la = laInfo.GetLoanAccount()
			break
		}
	}
	if la != nil {
		ownership = getEventsOwnership(helper.GetPalFeVendorFromBe(la.GetVendor()), helper.GetFeLoanProgramFromBe(la.GetLoanProgram()))
	}
	if lr != nil {
		ownership = getEventsOwnership(helper.GetPalFeVendorFromBe(lr.GetVendor()), helper.GetFeLoanProgramFromBe(lr.GetLoanProgram()))
	}
	if la != nil || lr != nil {
		s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanManagement, "", lr.GetId(), palEvents.LoanManagementFlowV2, ownership, eventData)
		return
	}

	if len(lopts) > 0 {
		if lopts[0].GetLoanOffer() != nil {
			ownership = getEventsOwnership(helper.GetPalFeVendorFromBe(lopts[0].GetLoanOffer().GetVendor()), helper.GetFeLoanProgramFromBe(lopts[0].GetLoanOffer().GetLoanProgram()))
			s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanOffer, lopts[0].GetLoanOffer().GetId(), "", palEvents.LoanManagementFlow, ownership, eventData)
		} else {
			ownership = getEventsOwnership(helper.GetPalFeVendorFromBe(lopts[0].GetEligibilityHeader().GetVendor()), helper.GetFeLoanProgramFromBe(lopts[0].GetEligibilityHeader().GetLoanProgram()))
			s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusLoanEligibility, "", "", palEvents.LoanEligibilityFlow, ownership, eventData)
		}
		return
	}

	s.PushApiResponseEvent(ctx, actorId, palEvents.ApiNameLandingInfo, time.Since(startTime), palEvents.StatusSuccess, palEvents.SubStatusPermissionDenied, "", "", palEvents.LoanManagementFlow, "", eventData)
}

func getFeedbackInfo(landingScreenState int) *header.FeedbackEngineInfo {
	if landingScreenState == LandingStateLoanOptions {
		return &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN.String(),
			},
		}
	}
	return nil
}

func (s *Service) createDlFromScreenState(ctx context.Context, landingScreenState int, req *palFePb.GetLandingInfoRequest,
	lOpts []*palBePb.LoanOption, lse *palBePb.LoanStepExecution, latestLr *palBePb.LoanRequest, loanInfos []*palBePb.LoanInfo, rejectedOffers []*palBePb.LoanHeader) (*deeplink.Deeplink, error) {
	switch landingScreenState {
	case LandingStateLoanDashboard:
		dl, err := s.aggregatedDeeplinkProvider.GetLoanDashboardDeepLinkV3(ctx, &palBePb.GetDashboardResponse{
			Status:            rpcPb.StatusOk(),
			RecentLoanRequest: latestLr,
			LoanInfoList:      loanInfos,
			LoanSteps:         []*palBePb.LoanStepExecution{lse},
			LoanOptions:       lOpts,
		})
		if err != nil {
			return nil, errors.Wrap(err, "could not get loan dashboard")
		}
		s.PushApiResponseEvent(ctx, req.GetReq().GetAuth().GetActorId(), palEvents.ApiNameGetDashboard, 0, palEvents.StatusSuccess, dl.EventSubStatus,
			"", latestLr.GetId(), palEvents.LoanManagementFlowV2, getEventsOwnershipFromAccountAndRequest(latestLr, loanInfos), req.GetLoanHeader().GetEventData())

		return dl.Deeplink, nil

	case LandingStateLoanOptions:
		if len(lOpts) == 0 {
			return nil, errors.New("no loan options provided")
		}
		userFeatProp, err := helper2.GetUserFeatureProperty(ctx, req.GetReq().GetAuth().GetActorId(), s.onbClient, s.savingsClient)
		if err != nil {
			return nil, errors.Wrap(err, "error in GetUserFeatureProperty")
		}
		if !userFeatProp.IsHomeAccessible {
			deeplinkProvider := s.getDeeplinkProvider(ctx, helper.GetPalFeVendorFromBe(lOpts[0].GetLoanHeader().GetVendor()), helper.GetFeLoanProgramFromBe(lOpts[0].GetLoanHeader().GetLoanProgram()))
			return deeplinkProvider.CheckLoanEligibilityScreenDeeplink(ctx, req.GetReq().GetAuth().GetActorId(), helper.GetFeLoanHeaderByBeLoanHeader(lOpts[0].GetLoanHeader()))
		}
		dl, err := s.aggregatedDeeplinkProvider.GetLoanOfferIntroScreen(ctx, &provider.LandingInfoRequest{
			LoanOptions:        lOpts,
			ActorId:            req.GetReq().GetAuth().GetActorId(),
			RejectedLoanOffers: rejectedOffers,
		})
		if err != nil {
			return nil, errors.Wrap(err, "error while generating loans offer intro deeplink")
		}

		return dl, nil

	case LandingStateNotEligible:
		deeplinkProvider := s.getDeeplinkProvider(ctx, palFeEnumsPb.Vendor_VENDOR_UNSPECIFIED, palFeEnumsPb.LoanProgram_LOAN_PROGRAM_UNSPECIFIED)
		dl := s.getNoOfferDeeplink(ctx, deeplinkProvider, nil, nil, req.GetReq().GetAuth().GetActorId())

		return dl, nil

	default:
		return nil, fmt.Errorf("unhandled landingState: %d", landingScreenState)
	}
}
