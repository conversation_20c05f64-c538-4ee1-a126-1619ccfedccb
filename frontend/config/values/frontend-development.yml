Application:
  Environment: "development"
  Name: "frontend"
  EnableDeviceIntegrityCheck: false
  EnableLocationInterceptor: false
  MaxGRPCTimeout: "1m"

Server:
  Ports:
    GrpcPort: 8082
    GrpcSecurePort: 9509
    HttpPort: 9887
    HttpPProfPort: 9990

SecureLogging:
  EnablePartnerLog: true
  EnableSecureLog: true
  SecureLogPath: "/tmp/secure.log"
  PartnerLogPath: "/tmp/partner.log"
  MaxSizeInMBs: 5
  MaxBackups: 20

RedisOptions:
  IsSecureRedis: false
  Options:
    Addr: "localhost:6379"
    Password: "" ## empty string for no password
    DB: 0
    ClientName: home

ABFeatureReleaseConfig:
  FeatureConstraints:
    - ASK_FI_HOME_SEARCH_BAR:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 0
            MinIOSVersion: 0
        Buckets:
          - CONTROL:
              Start: 70
              End: 74
          - TYPE_1:
              Start: 75
              End: 79
          - TYPE_2:
              Start: 80
              End: 84
          - TYPE_3:
              Start: 85
              End: 89
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - CONTROL_1:
              Start: 10
              End: 13
    - INVESTMENT_LANDING_COMPONENTS_ORDERING:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 100
            MinIOSVersion: 100
        Buckets:
          - INVESTMENT_LANDING_MUTUAL_FUNDS_INSTRUMENT_DEEPLINK_CONTROL_1:
              Start: 48
              End: 59
    - JUMP_DASHBOARD_FOR_NON_INVESTED_USERS:
        Buckets:
          - ZERO_STATE_DASHBOARD_VARIANT_ENABLED:
              Start: 50
              End: 70
    - FIXED_DEPOSIT_INTEREST_RATES:
        Buckets:
          - FIXED_DEPOSIT_INTEREST_RATES_EXPERIMENT_MIN_1_YEAR:
              Start: 27
              End: 42
    - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM:
        Buckets:
          - FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM_EXPERIMENT_15_MONTHS:
              Start: 27
              End: 42
    - UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS:
        ConstraintConfig:
          AppVersionConstraintConfig: # this should be the same as the app version for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-development.yml
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets: # this should be the same as the buckets for AFFLUENT_USER_BONUS_TRANSITION_SCREEN in user-development.yml
          - ONE:
              Start: 0
              End: 99
    - APP_UPDATE_HARD_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 2 # FNF
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - APP_UPDATE_SOFT_NUDGE_REFERRALS:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 99999
            MinIOSVersion: 99999
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99
    - PHONE_NUMBER_AS_REFERRAL_CODE:
        ConstraintConfig:
          AppVersionConstraintConfig:
            MinAndroidVersion: 1
            MinIOSVersion: 1
          UserGroupConstraintConfig:
            AllowedGroups:
              - 1 # INTERNAL
        Buckets:
          - ONE:
              Start: 0
              End: 99

UserProfile:
  ProfileHeaderV2MinVersion:
    MinVersionAndroid: 1
    MinVersionIos: 1
  IsEmailEditableConfig:
    MinAndroidVersion: 1
    MinIOSVersion: 1

LegalDocuments:
  FiTncUrl: "https://web.demo.pointz.in/T&C"
  FederalBankTncUrl: "https://www.federalbank.co.in/epifi-tandc#CASA"
  FiPrivacyPolicyUrl: "https://web.demo.pointz.in/privacy"
  FiWealthTncUrl: "https://web.demo.pointz.in/wealth/TnC"
  OpenSourceLicenses:
    Firebase: ""
    Cronet: ""
    ChromeWebView: ""

#json file path
PayErrorViewJson: "mappingJson/errorViewMapping.json"
CardErrorViewJson: "mappingJson/cardErrorViewMapping.json"
DisplayCategoryMappingJson: "mappingJson/displayCategoryMapping.json"

Flags:
  SkipAddMoney: false
  TrimDebugMessageFromStatus: false
  EnableCardTracking: true
  EnableCardBlock: true
  EnableCardQRCodeScan: true
  EnableCardOnlineTxnEnabledTile: true
  EnableCardOffers: true
  EnableVKYCOnlyForInternalUsers: true
  EnableVkycScheduleFlow: true
  # redmit 8A, Samsumng S20+, Samsung note10+
  SkipUserRevokeStateCheck: false
  EnableCardTrackingAndActivationChanges:
    IsEnableOnAndroid: true
    MinAndroidVersion: 117
    IsEnableOnIos: true
    MinIosVersion: 200
  EnableAutoInvestComponentOnInvestLanding:
    MinAndroidVersion: 231
    MinIOSVersion: 333
    FallbackToEnableFeature: false
    DisableFeature: false
  EnableUSStocksInstrumentCardFlag: true
  EnableMFInstrumentCardFlag: true
  EnableSDInstrumentCardFlag: true
  EnableFDInstrumentCardFlag: true
  EnableJumpInstrumentCardFlag: true
  EnableInvestmentLandingQuickLinksComponent:
    MinAndroidVersion: 310
    MinIOSVersion: 1272
    FallbackToEnableFeature: false
    DisableFeature: false
  EnablePartnersComponentInvestLanding:
    IsEnableOnAndroid: false
    MinAndroidVersion: 1000
    IsEnableOnIos: false
    MinIosVersion: 1000
  UseAppsFlyerClientKeyV2: true
  EnableCCBillDashboardV2FeatureFlag:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 1674
  EnableSecuredCardsRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 281
    IsEnableOnIos: true
    MinIosVersion: 381
  EnableGenericRewardsDashboard:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  EnableCardDesignEnhancement:
    MinAndroidVersion: 1
    MinIOSVersion: 1
    FallbackToEnableFeature: false
    DisableFeature: false

RudderStack:
  Host: "https://rudder.pointz.in"
  IntervalInSec: 10
  BatchSize: 100
  Verbose: false

RewardsFrontendMeta:
  AndroidAppVersionsNotSupportingMultiChoiceRewards: [ 0 ]

Aws:
  Region: "ap-south-1"

Secrets:
  Ids:
    RudderWriteKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    GoogleCloudProfilingServiceAccountKey: "development"
    RudderClientApiKey: "rudder/client-api-key"
    AppsFlyerClientKey: "appsflyer/client-api-key"
    OneMoneyClientSecret: "onemoney/client-secret"
    DeviceIdsEnabledForSafetyNetV2: "[\"deviceId1\"]"
    MoengageAppSdkKey: "moengage/app-sdk-key"
    ActiveDeviceIntegrityTokenSigningKey: "Isignshorttokens6"
    MiAmpPushSecretJson: "mi_amp_push/mi_secret_json"
    AppsFlyerClientKeyV2: "appsflyer/client-api-key-2"
    RudderIosClientApiKey: "1eGkhVch5jk2oJBF9lrTEN4I3zB"
    RazorpaySecrets: '{ "KeyId": "rzp_test_mTjruR1jqLxq8s", "KeySecret": "gYaZsWJubkB44JmXCuGy4S7g" }'

VKYC:
  PopupTileDuration: 12 # in hours
  PopupTileNonDismissableAfter: 1728 # in minutes
  AccountFreezePopupNonDismissibleWithinDays: 30 # in days
  AccountFreezeThreshold: "240h" # 10 days
  SavingsBalanceLimitPercent: 70
  CreditBalanceLimitPercent: 70
  PerformEkycAfter: "72h"
  SlotDays: 5
  MorningStart: 8
  SplitMorning: 12
  SplitAfternoon: 15
  SplitLateAfternoon: 18
  EveningEnd: 22
  ScheduleToLiveCutOffMinutes: 2
  NewPopupTileAccountCreationTimeLimit: 180 # in days
  NewPopupTileDuration: 84 # in hours
  HomeBannerColorHex: "#383838"

DeviceIntegrity:
  EnableWhitelistedTokens: true
  SkipAsyncDeviceIntegrityChecks: false
  WhitelistedTokensList: [ "DUMMY_TOKEN" ]
  DefaultHighRiskDeviceConsentDuration: "24h"
  MaxHighRiskDeviceConsentDuration: "1080h" # 45 days
  AsyncDeviceIntegrityCheck:
    DisableFeature: true
    MinAndroidVersion: 1000
    MinIOSVersion: 1000

InsightsParams:
  GetInsightConfig:
    MarkNoticedAfter: 2

Card:
  MinAndroidVersionForFMAuth: 1
  CardDynamicTileDuration:
    ViewCardDeliveryTracking: "5m"
    QRCodeAsPrimaryTile: "15m"
    ViewCardOnlineTxnEnabledTile: "5m"
    ViewQRCodeScanTime: "10m"
    ViewSecurePinActivationTime: "20m"
  MinAndroidVersionForCardOffers: 79
  MinAndroidVersionForCardTracking: 83
  MinAndroidVersionForSecurePinValidation: 100
  MinIOSVersionForSecurePinValidation: 100
  EnableSecurePinValidationAuth: true
  OffersDynamicTileExpiryTime: "31-08-2022T23:59:00"
  EnableCardOffersInformativeDynamicTile: true
  PrintingToDispatchDynamicTileDuration: "24h"
  EnableSecurePinActivationFlow:
    IsEnableOnAndroid: true
    MinAndroidVersion: 250
    IsEnableOnIos: true
    MinIosVersion: 250
Comms:
  NotificationsPageSize: 10

Screening:
  EmpVerificationCheckStatusPollIntervalInSecs: 5
  CheckCreditReportAvailabilityStatusPollIntervalInSecs: 5
  CheckCreditReportVerificationStatusPollIntervalInSecs: 5

IPInterceptorParams:
  EnableIPInterceptor: false

ConnectedAccountUserGroupParams:
  IsConnectedAccountRestricted: false
  AllowedUserGrps:
    - 1 # INTERNAL
    - 2 # FNF
    - 7 # CONNECTED_ACCOUNT

ConnectedAccount:
  V2FlowParams:
    UseV2Flow: false
    AccountDiscoveryTitleText: "Select the accounts you want to link & track on Fi"
    AccountDiscoverySubtitleText: "We found these accounts linked to %s"
    AccountDiscoverySubtitleSearchingText: "One moment! We're searching for your accounts linked to %s."
    AccountDiscoveryCtaText: "Continue"
    MinVersionAndroid: 1
    MinVersionIos: 1
    AccountDiscoveryLoadingText: "Searching for your accounts"
    RegisterOtherAccountsText: "Can't see your accounts?"
  MinimumDurationRequiredToPermitDisconnect: "72h"
  EnableAccountManagerConsentRenewalEntryPoint: true
  EnableConsentRenewalSegmentNonProdTest: true
  BalanceDashboardRenewalPopupDismissDuration: "3m"

Investment:
  MfNavChart:
    WebUrl: "https://web.demo.pointz.in/fin-charts/line-chart"
    LastUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  MinAndroidVersionToSupportMINKYCCheckForPurchase: 137
  MinIOSVersionToSupportMINKYCCheckForPurchase: 169
  MinAndroidVersionToUploadPan: 137
  MinIosVersionToUploadPan: 169
  ISKYCCheckOnPurchaseEnabledForIOS: false
  ISKYCCheckOnPurchaseEnabledForAndroid: true
  FundActivityManualInterventionSupport:
    IsEnableOnAndroid: 1
    MinAndroidVersion: 164
    IsEnableOnIos: 1
    MinIosVersion: 169
  MinAndroidVersionForNextOnboardingStep: 164
  MinIOSVersionForNextOnboardingStep: 300
  InvestmentLandingRecommendations:
    SegmentExpressionToRecommendationDetailsMap:
      - "TestSegmentID":
          RecommendationID: "STABLE_AND_ABLE_COLLECTION"
          InstrumentType: 1 # 1 represents mutual fund
      - "IsMember('AWS_test-segment')":
          RecommendationID: "INTERNAL"
          InstrumentType: 0 # 0 represents hybrid recommendations
    FallBackRecommendationDetails:
      RecommendationID: "HYBRID_1"
      InstrumentType: 0 # 0 represents hybrid recommendations
    MFRecommendationIDToDetailsMap:
      - "STABLE_AND_ABLE_COLLECTION":
          CollectionID: "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA=="
          HeaderDisplayString: "Stable And Able"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
      - "POPULAR_IN_MF":
          FilterIDs:
            - "INDEX_FUND_TYPE"
            - "UPTO_POINT5_PER_EXPENSE"
            - "1001_TO_5000CR_FUND_SIZE"
            - "5001_TO_10KCR_FUND_SIZE"
            - "MORE_THAN_10KCR_FUND_SIZE"
          HeaderDisplayString: "Popular in Mutual funds"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    FallBackMFRecommendationDetails:
      CollectionID: "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA=="
      HeaderDisplayString: "Stable And Able"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    USStocksRecommendationIDToDetailsMap:
      - "BANKING_COLLECTION":
          CollectionID: "BANKING"
          HeaderDisplayString: "Banking"
          SubtitleString: "Based on what people invest"
          HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    FallBackUSStocksRecommendationDetails:
      CollectionID: "BIG_TECH"
      HeaderDisplayString: "Big Tech"
      SubtitleString: "Based on what people invest"
      HeaderImageUrl: "https://epifi-icons.pointz.in/investments/Cash100.png"
    HybridRecommendationIDToDetailsMap:
      - "INTERNAL":
          TitleString: "Mutual Funds collections"
          CTAForInstrumentType: 1
          SubtitleString: "Save time with expert-picked funds"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
      #          USStockRecommendationCards:
      #            - "1":
      #                Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
      #                Tag:
      #                  FontColor: "#879EDB"
      #                  BgColor: "#D1DAF1"
      #                  Content: "US STOCKS"
      #                Rank: 9
      #            - "2":
      #                Id: "USS5sknExOBTGORbhwWrupJuQ230522=="
      #                Tag:
      #                  FontColor: "#879EDB"
      #                  BgColor: "#D1DAF1"
      #                  Content: "US STOCKS"
      #                Rank: 10
      #          MFRecommendationCards:
      #            - "1":
      #                Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
      #                Tag:
      #                  FontColor: "#879EDB"
      #                  BgColor: "#D1DAF1"
      #                  Content: "MUTUAL FUNDS"
      #                Rank: 1
      #            - "2":
      #                Id: "MF220411EeRofPZwTIusYZUOko4eig=="
      #                Tag:
      #                  FontColor: "#879EDB"
      #                  BgColor: "#D1DAF1"
      #                  Content: "MUTUAL FUNDS"
      #                Rank: 5
      #          FixedDepositRecommendationCards:
      #            - "1":
      #                Id: "FD725"
      #                Tag:
      #                  FontColor: "#9287BD"
      #                  BgColor: "#CDC6E8"
      #                  Content: "FIXED DEPOSIT"
      #                Rank: 4
      #                Title: "Bumper jar - Lock safe returns"
      #                Info: "15 months | 7.25% p.a. "
      #          SmartDepositRecommendationCards:
      #            - "1":
      #                Id: "SD4"
      #                Tag:
      #                  FontColor: "#87BA6B"
      #                  BgColor: "#D9F2CC"
      #                  Content: "SMART DEPOSIT"
      #                Rank: 6
      #                Title: "Monthly autosave in a Smart deposit"
      #                Info: "₹100 only | 6.8% p.a."
      #          P2PRecommendationCards:
      #            - "SCHEME_NAME_LL_BOOSTER":
      #                Tag:
      #                  FontColor: "#D3B250"
      #                  BgColor: "#F4E7BF"
      #                  Content: "JUMP"
      #                Rank: 2
      #                Title: "Booster plan - Invest up to ₹10L"
      #                Info: "Limited period"
      #            - "SCHEME_NAME_LL_SHORT_TERM":
      #                Tag:
      #                  FontColor: "#879EDB"
      #                  BgColor: "#D1DAF1"
      #                  Content: "JUMP"
      #                Rank: 6
      #                Title: "Short term plan Start with ₹10,000"
      #                Info: "Join 10k+ investors"
      - "HYBRID_1":
          TitleString: "Mutual Funds collections"
          CTAForInstrumentType: 1
          SubtitleString: "Save time with expert-picked funds"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    #          USStockRecommendationCards:
    #            - "1":
    #                Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "US STOCKS"
    #                Rank: 9
    #            - "2":
    #                Id: "USS221223amO0Chb9SjiM3uBqbezcgg=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "US STOCKS"
    #                Rank: 10
    #          MFRecommendationCards:
    #            - "1":
    #                Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "MUTUAL FUNDS"
    #                Rank: 1
    #            - "2":
    #                Id: "MF220411EeRofPZwTIusYZUOko4eig=="
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "MUTUAL FUNDS"
    #                Rank: 5
    #          FixedDepositRecommendationCards:
    #            - "1":
    #                Id: "FD725"
    #                Tag:
    #                  FontColor: "#9287BD"
    #                  BgColor: "#CDC6E8"
    #                  Content: "FIXED DEPOSIT"
    #                Rank: 4
    #                Title: "Bumper jar - Lock safe returns"
    #                Info: "15 months | 7.25% p.a. "
    #          SmartDepositRecommendationCards:
    #            - "1":
    #                Id: "SD4"
    #                Tag:
    #                  FontColor: "#87BA6B"
    #                  BgColor: "#D9F2CC"
    #                  Content: "SMART DEPOSIT"
    #                Rank: 6
    #                Title: "Monthly autosave in a Smart deposit"
    #                Info: "₹100 only | 6.8% p.a."
    #          P2PRecommendationCards:
    #            - "SCHEME_NAME_LL_BOOSTER":
    #                Tag:
    #                  FontColor: "#D3B250"
    #                  BgColor: "#F4E7BF"
    #                  Content: "JUMP"
    #                Rank: 2
    #                Title: "Booster plan - Invest up to ₹10L"
    #                Info: "Limited period"
    #            - "SCHEME_NAME_LL_SHORT_TERM":
    #                Tag:
    #                  FontColor: "#879EDB"
    #                  BgColor: "#D1DAF1"
    #                  Content: "JUMP"
    #                Rank: 6
    #                Title: "Short term plan Start with ₹10,000"
    #                Info: "Join 10k+ investors"
    FallBackHybridRecommendationDetails:
      TitleString: "Mutual Funds collections"
      CTAForInstrumentType: 1
      SubtitleString: "Save time with expert-picked funds"
      TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
      CollectionRecommendationCards:
        "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
          InstrumentType: 1
          Rank: 1
          Title: "SIP with ₹500"
          Subtitle: "Invest regularly with these top funds"
          ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
        "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
          InstrumentType: 1
          Rank: 2
          Title: "Tax saver"
          Subtitle: "Funds to save tax under section 80C"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
        "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
          InstrumentType: 1
          Rank: 3
          Title: "Better than FD"
          Subtitle: "Returns up to 7.8% at lower risk"
          ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
        "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
          InstrumentType: 1
          Rank: 4
          Title: "Index funds"
          Subtitle: "Invest in top Indian companies"
          ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    MinIosVersionForV2Recommendations: 0
    MinAndroidVersionForV2Recommendations: 0
    AndroidVersionForSubtitleSwapFix: 10000
    EnableMultipleRecommendation: true
    MultipleHybridRecommendationIDToDetailsMap:
      - "HYBRID_1":
          Recommendations:
            "1":
              TitleString: "Popular on Fi"
              SubtitleString: "Based on what people invest"
              TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
              USStockRecommendationCards:
                - "1":
                    Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "US STOCKS"
                    Rank: 9
                - "2":
                    Id: "USS5sknExOBTGORbhwWrupJuQ230522=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "US STOCKS"
                    Rank: 10
              MFRecommendationCards:
                - "1":
                    Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "MUTUAL FUNDS"
                    Rank: 1
                - "2":
                    Id: "MF220411EeRofPZwTIusYZUOko4eig=="
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "MUTUAL FUNDS"
                    Rank: 5
              FixedDepositRecommendationCards:
                - "1":
                    Id: "FD725"
                    Tag:
                      FontColor: "#9287BD"
                      BgColor: "#CDC6E8"
                      Content: "FIXED DEPOSIT"
                    Rank: 4
                    Title: "Bumper jar - Lock safe returns"
                    Info: "15 months | 7.25% p.a. "
              SmartDepositRecommendationCards:
                - "1":
                    Id: "SD4"
                    Tag:
                      FontColor: "#87BA6B"
                      BgColor: "#D9F2CC"
                      Content: "SMART DEPOSIT"
                    Rank: 6
                    Title: "Monthly autosave in a Smart deposit"
                    Info: "₹100 only | 6.8% p.a."
              P2PRecommendationCards:
                - "SCHEME_NAME_LL_BOOSTER":
                    Tag:
                      FontColor: "#D3B250"
                      BgColor: "#F4E7BF"
                      Content: "JUMP"
                    Rank: 2
                    Title: "Booster plan - Invest up to ₹10L"
                    Info: "Limited period"
                - "SCHEME_NAME_LL_SHORT_TERM":
                    Tag:
                      FontColor: "#879EDB"
                      BgColor: "#D1DAF1"
                      Content: "JUMP"
                    Rank: 6
                    Title: "Short term plan Start with ₹10,000"
                    Info: "Join 10k+ investors"
            "2":
              TitleString: "Mutual Funds collections"
              CTAForInstrumentType: 1
              SubtitleString: "Save time with expert-picked funds"
              TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
              CollectionRecommendationCards:
                "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
                  InstrumentType: 1
                  Rank: 1
                  Title: "SIP with ₹500"
                  Subtitle: "Invest regularly with these top funds"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
                "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
                  InstrumentType: 1
                  Rank: 2
                  Title: "Tax saver"
                  Subtitle: "Funds to save tax under section 80C"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
                "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
                  InstrumentType: 1
                  Rank: 3
                  Title: "Better than FD"
                  Subtitle: "Returns up to 7.8% at lower risk"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
                "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
                  InstrumentType: 1
                  Rank: 4
                  Title: "Index funds"
                  Subtitle: "Invest in top Indian companies"
                  ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"
    FallBackMultipleHybridRecommendationDetails:
      Recommendations:
        "1":
          TitleString: "Popular on Fi"
          SubtitleString: "Based on what people invest"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/landing/recommendation_star.png"
          USStockRecommendationCards:
            - "1":
                Id: "USS230105jtc+1hQ0QAio048mZutSyQ=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "US STOCKS"
                Rank: 9
            - "2":
                Id: "USS5sknExOBTGORbhwWrupJuQ230522=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "US STOCKS"
                Rank: 10
          MFRecommendationCards:
            - "1":
                Id: "MF220622XboJDGq7RHG9oR0ButrRyQ=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "MUTUAL FUNDS"
                Rank: 1
            - "2":
                Id: "MF220411EeRofPZwTIusYZUOko4eig=="
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "MUTUAL FUNDS"
                Rank: 5
          FixedDepositRecommendationCards:
            - "1":
                Id: "FD725"
                Tag:
                  FontColor: "#9287BD"
                  BgColor: "#CDC6E8"
                  Content: "FIXED DEPOSIT"
                Rank: 4
                Title: "Bumper jar - Lock safe returns"
                Info: "15 months | 7.25% p.a. "
          SmartDepositRecommendationCards:
            - "1":
                Id: "SD4"
                Tag:
                  FontColor: "#87BA6B"
                  BgColor: "#D9F2CC"
                  Content: "SMART DEPOSIT"
                Rank: 6
                Title: "Monthly autosave in a Smart deposit"
                Info: "₹100 only | 6.8% p.a."
          P2PRecommendationCards:
            - "SCHEME_NAME_LL_BOOSTER":
                Tag:
                  FontColor: "#D3B250"
                  BgColor: "#F4E7BF"
                  Content: "JUMP"
                Rank: 2
                Title: "Booster plan - Invest up to ₹10L"
                Info: "Limited period"
            - "SCHEME_NAME_LL_SHORT_TERM":
                Tag:
                  FontColor: "#879EDB"
                  BgColor: "#D1DAF1"
                  Content: "JUMP"
                Rank: 6
                Title: "Short term plan Start with ₹10,000"
                Info: "Join 10k+ investors"
        "2":
          TitleString: "Mutual Funds collections"
          CTAForInstrumentType: 1
          SubtitleString: "Save time with expert-picked funds"
          TitleImageUrl: "https://epifi-icons.pointz.in/investments/investment_landing_collection.png"
          CollectionRecommendationCards:
            "MFCOLL11yBS2aEbfSoOR6wzNnn3LFA==":
              InstrumentType: 1
              Rank: 1
              Title: "SIP with ₹500"
              Subtitle: "Invest regularly with these top funds"
              ImgUrl: "https://epifi-icons.pointz.in/investments/landing/mf_sip_500_collection_logo.png"
            "MFCOLL11q30wxq/uS0SO98y2Fqzr2w==":
              InstrumentType: 1
              Rank: 2
              Title: "Tax saver"
              Subtitle: "Funds to save tax under section 80C"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Pad.png"
            "MFCOLL11TM0etSbBQh6UBaCkZ50YBg==":
              InstrumentType: 1
              Rank: 3
              Title: "Better than FD"
              Subtitle: "Returns up to 7.8% at lower risk"
              ImgUrl: "https://epifi-icons.pointz.in/investments/Boxes-1.png"
            "MFCOLL11Wda9BHSQTM2gbsBkJ7SrPA==":
              InstrumentType: 1
              Rank: 4
              Title: "Index funds"
              Subtitle: "Invest in top Indian companies"
              ImgUrl: "https://epifi-icons.pointz.in/investments/invest-landing-graph.png"


Deposit:
  Preclosure:
    ConfirmationNudge:
      FaqCategoryId: "82000180883" # currently its save category, also category id is different for prod and non prod
  MaturityAmountVisibility: # deposit's maturity amount feature flags belong here
    Global: true # if false, maturity amount will be hidden everywhere irrespective of the screen
    GlobalAllowedUserGroups: [ ] # allowed user groups
    SDCreation: false  # if false, maturity amount will be hidden in SD creation flow
    FDCreation: false # if false, maturity amount will be hidden in FD creation flow
    SDDetails: false # if false, maturity amount will be hidden in SD details screen
    FDDetails: true # if false, maturity amount will be hidden in FD details screen
    SDAddFunds: false # if false, maturity amount will be hidden in SD add funds flow
  Goals:
    GoalDetailsInDepositList:
      Enable: true
    GoalDetailsInDepositDetails:
      Enable: true
  AutoSave:
    PostCreationFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
    DetailsFlow:
      Enable: true
      GlobalAllowedUserGroups: [ ] # allowed user groups
      EnableAutoSaveSuggestions: true
      EnableAutoSaveRuleList: true
    PreCreationFlow:
      Enable: true
      AllowedUserGroups: [ ] # allowed user groups
  Statement:
    Enable: true
    AllowedUserGroups: [ ] # allowed user groups

AnalyserParams:
  ShowAnalyser: true
  GetAnalyserGenerateDummyResponse: false
  AnalyserConfigJson: "mappingJson/analyserConfig.json"
  AnalyserHubConfig:
    Experiments:
      - EXPERIMENT_NAME_CTA_BOTTOM_LEFT_WITHOUT_TEXT
  CreditReportParams:
    DownloadProcessExpiry: 30m
  CreditScoreAnalyserConfig:

Goals:
  GoalDiscovery:
    Enable: true
    AllowedGroups: [ ] # allowed user groups
  GoalDiscoveryInExistingInvestmentInstrument:
    Enable: true
    AllowedGroups: [ ] # allowed user groups

Lending:
  PreApprovedLoan:
    IsAlternateAccountFlowEnabled: true
    IsAlternateAccountFlowEnabledForLL: true
    LoanDetailsSelectionV2Flow:
      IsEnabled: true
      IsRewardsEnabled: false
      SkipAmountSelectionScreen: false
      ShowMultipleOfferSelection: false
      ShowCollapsedBreakdownView: false
      ShowCongratulatoryText: false
      ShowDiscountedPf: false
      EnableLoanPrograms:
        - IDFC
        - FEDERAL_BANK
        - LOAN_PROGRAM_PRE_APPROVED_LOAN
        - LOAN_PROGRAM_FLDG
      DefaultAmountPercentage:
        - "LIQUILOANS": 0.75

Profiling:
  StackDriverProfiling:
    ProjectId: "development"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

AppLogs:
  MaxMessageCountThreshold: 10

Tiering:
  NewTiersConstraints:
    AaSalary:
      IsEnableOnAndroid: true
      MinAndroidVersion: 1
      IsEnableOnIos: true
      MinIosVersion: 1
  TieringFeature:
    MinVersionAndroid: 1
    MinVersionIos: 1
  HeroBenefits:
    MinVersionAndroid: 1
    MinVersionIos: 1
  TierIntroduction:
    ReleaseConstraints:
      MinVersionAndroid: 10000
      MinVersionIos: 10000
    LaunchAnimationInactivitySeconds: 2

USStocks:
  A2FormURL: "https://fi.money/assets/pages/a2-form/v2"
  ETF:
    IsEnabled: false
  VersionSupport:
    MinIOSAppVersionToSupportAnnouncementsInSymbolDetails: 10000
    MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails: 10000
  BuyTimeoutInMilliseconds: 60000
  PriceGraphURL: "https://web.demo.pointz.in/fin-charts/line-chart"
  RatioGraphURL: "https://web.demo.pointz.in/fin-charts/multi-chart"
  PriceGraphUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  RatioGraphUpdatedAt: ********** # Wednesday, 8 February 2023 12:00:00 AM GMT+05:30
  IsBuyDisabled: false
  IsSellDisabled: false

FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_CA_BANK_SELECTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - NSDL_PAN_API_V2_FOR_CA:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_INTERACTIVE_TALK_TO_AI_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD:
        AppVersionConstraintConfig:
          MinAndroidVersion: 20000
          MinIOSVersion: 2351
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_SHOW_UPDATED_BOTTOM_SHEET:
         AppVersionConstraintConfig:
            MinAndroidVersion: 480
            MinIOSVersion: 1000000
         StickyPercentageConstraintConfig:
            RolloutPercentage: 100
    - HOME_PAGE_LAYOUT_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 16 # HOME_V2_INTERNAL
            - 19 # TIERING
    - TIME_ANALYSER_UPCOMING_TRANSACTIONS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 247
          MinIOSVersion: 343
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - JUMP_ALL_ACTIVITY_DEEPLINK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 100
    - FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - HEALTH_ENGINE_FOR_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 25000000
          MinIOSVersion: **********
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 2000
          MinIOSVersion: 2000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 2460
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TOGGLE_TRAVEL_MODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1000
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_DC_TRAVEL_MODE_LOTTIE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 388
          MinIOSVersion: 2595
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - POST_PAYMENT_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 100000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_BIOMETRIC_REVALIDATION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 200
          MinIOSVersion: 200
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 10000
          MinIOSVersion: 10000
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_IND_SECURITIES_WIDGET:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - FEATURE_SECURED_LOANS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
    - ONB_ADD_FUNDS_TIERING_SUCCESS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
            - 7 # CONNECTED_ACCOUNT
    - ADD_FUNDS_V3:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - ADD_FUNDS_V4:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_TPAP_PAYMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - DEPOSIT_AUTO_RENEW_CTA:
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM:
        AppVersionConstraintConfig:
          MinAndroidVersion: 100
          MinIOSVersion: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_ETF:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_REIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - INDIAN_SECURITIES_INVIT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 321
          MinIOSVersion: 459
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - MANUAL_UAN_EPF_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 473
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - NETWORTH_REFRESH_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 9999
          MinIOSVersion: 9999
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - LOANS_IDFC_VKYC_V2:
        AppVersionConstraintConfig:
          MinAndroidVersion: 328
          MinIOSVersion: 482
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 0
            RolloutPercentageAndroid: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

    - NSDL_PAN_FLOW_V2_MF_ANALYSER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 343
          MinIOSVersion: 484
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
    - FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME:
        AppVersionConstraintConfig:
          MinAndroidVersion: 401
          MinIOSVersion: 555
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW:
        AppVersionConstraintConfig:
          MinAndroidVersion: 405
          MinIOSVersion: 2570
        UserGroupConstraintConfig:
          AllowedGroups: [ ]
    - FEATURE_AMB_ENTRYPOINT_BANNER:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 0
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_HOME_DESIGN_ENHANCEMENTS:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentageByPlatform:
            RolloutPercentageIOS: 100
            RolloutPercentageAndroid: 100
    - FEATURE_WB_DASHBOARD_LIABILITIES:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_FI_MCP_TOTP_CODE:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
    - FEATURE_DISPUTE_COMPLAINT_SUMMARY_INPUT:
        AppVersionConstraintConfig:
          MinAndroidVersion: 0
          MinIOSVersion: 0
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_NET_WORTH_GOLD_DISTRIBUTION:
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100

Referrals:
  LandingPageVersion: 2 # referrals-v1
  IsReferralsViaFiniteCodeAllowed: true
  CanClaimInAppReferralFiniteCode: true
  ReferralsHistoryPageSize: 40
  # even though we show 4 recent referrals, we are fetching 40 * 10 (pages) for now to also calculate the lifetime winnings
  RecentReferralsPageSize: 40
  AchievementImageUrl: "https://epifi-icons.pointz.in/referrals/achievement.png"
  SeasonCompletionTitle: "That was the Season Finale!"
  SeasonCompletionText: [ "You won all your referral rewards for this season. Come back next season for more!", "You can continue referring until you've made REFERRAL_REWARDS_MAX_CAP referrals and win ₹SEASON_MAX_REWARD_AMOUNT per referral." ]
  SeasonCompletionImage: "https://epifi-icons.pointz.in/referrals/season-finale.png"
  SeasonCompletionTnc: [ "" ]
  # this URL will take the user to Fi website where they will have option of downloading the app
  AppDownloadUrl: "https://fi.onelink.me/GvZH/7d8ypmz0"
  LandingScreenPromotionalBanner:
    ShowBanner: true
    Title: "Get a special gift for successful referral on Fi app"
    TitleColor: "#FFFFFF"
    ImageUrl: "https://epifi-icons.pointz.in/referrals/promotional_banner.png"
    BgColor: "#383838"
    Cta:
      Text: "Refer Now"
      TextColor: "#FFFFFF"
      BgColor: "#00B899"
      ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
      IsVisible: true
      IsEnabled: true
    ExpandedStateTitle: "Offer Details"
    ExpandedStateDesc: "Invite your friends to Fi & earn rewards"
    ExpandedStateTitleColor: "#FFFFFF"
    ExpandedStateDescColor: "#FFFFFF"
    ExpandedStateIconUrl: "https://epifi-icons.pointz.in/referrals/promo_banner_icon.png"
    ExpandedStateCta:
      Text: ""
      TextColor: ""
      BgColor: ""
      ImageUrl: ""
      IsVisible: true
      IsEnabled: false
    ExpandedStateHeading1: ""
    ExpandedStateInfos1: "The Fi.nite code is a unique alphanumeric code assigned to each of our users. Fi's referral programme allows you to invite other users to our platform by sharing this Fi.nite code.\nThe Fi.nite code can be claimed upto 100 times! Go ahead and invite any friends who might enjoy Fi.\nWhen your friends join Fi, you can earn rewards. See reward T&Cs below for details.\nAnyone using an Android device can sign up on our platform — using a Fi.nite code. However, the user will have to complete our quick signup process (providing details like Name, PAN details, etc.). For more, check out Fi's Terms & Conditions.\nRegarding iOS users: We're currently Beta-testing our iOS app, and it may take us a while to reach the App Store.\nThe Fi app is currently only open to working professionals. So, before allowing users — including those with a Fi.nite code — to sign up on our platform, Fi quickly verifies their employment status during the signup process."
    ExpandedStateHeading2: "Reward T&Cs"
    ExpandedStateInfos2: "You will receive a reward only when: A user you invited opens a Fi account using your Fi.nite code; That user adds ₹3,000/- to their savings account within 7 days of opening the account.\nAnyone who uses your Fi.nite code to open an account will also earn a reward when they complete the above steps.\nThe rewards could be in any form, including direct cashback, Fi-Coins, etc., as decided internally by Fi from time to time.\nThe user can learn about the form of reward via the Referral & Rewards pages.\nThese rewards are also subject to the Terms & Conditions mentioned in the Rewards section of Fi's T&Cs."
  MinAndroidVersionSupportingNewHomeReferralsWidget: 1
  MinIosVersionSupportingNewHomeReferralsWidget: 1
  MinIosVersionSupportingCtasFixForHomeV1EntryPoint: 1
  PrefixCtaForIosHomeV1ReferralsUnlockedEntryPoint:
    Text: "Share Code"
    FontColor: "#FFFFFF"
    IconUrl: "https://epifi-icons.pointz.in/referrals/share_code_icon.png"
    BgColor: "#9287BD"
    ShadowHeight: 4
    ShadowColor: "#6F62A4"
    ShareFiniteCodeMessage: "Hey, I've been using the Fi app and I love it! It's used by 25 lakh Indians and for good reason ✌️. I use it for savings, investing, and tracking all my expenses.\n\nIt gives me access to an RBI licensed Federal Bank Savings Account & offers multiple investment options.\n\nPlus, you can connect all your existing bank accounts & track your balances and spends on Fi. 😍\n\nGive it a try. Download Fi app using this link - APP_DOWNLOAD_URL_WITH_FINITE_CODE & use my referral code FINITE_CODE_PLACEHOLDER while joining to get a ₹100 joining bonus"
  InfoDuringOnboarding:
    OfferCodes:
      - CODE_1:
          IsEnabled: true
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FI200-offer-icon.png"
          DisplayCode: "FI200"
          UnderlyingFiniteCode: "VNJT9V27WJ"
          Offer: "Get up to ₹200 cashback"
          Desc: "When you add money to your account"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI200” applied"
            Subtitle: "You get up to ₹200 cashback"
            Desc: "Sign up & add funds to your Savings Account to claim this offer"
            CanDismiss: true
      - CODE_2:
          IsEnabled: false
          Icon: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/FiCoins-offer-icon.png"
          DisplayCode: "FI2500"
          UnderlyingFiniteCode: ""
          Offer: "Get flat 2,500 Fi-Coins"
          Desc: "When you complete 3 UPI payments using Fi"
          CtaText: "Apply now"
          CodeAppliedPopupDetails:
            Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
            Title: "Yay! “FI2500” applied"
            Subtitle: "You get flat 2,500 Fi-Coins"
            Desc: "Sign up & complete 3 UPI payments using Fi to claim this offer"
            CanDismiss: true
    ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "It’s a great start!"
      Subtitle: "You get cashback up to ₹160"
      Desc: "Sign up and make P2M transactions to claim rewards"
      CanDismiss: false
    Fi200ReferralCodeAppliedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-applied-1.png"
      Title: "Yay! Code applied"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: false
    ReferralCodeClaimFailedPopupDetails:
      Image: "https://epifi-icons.pointz.in/referrals/referrals_v1/onboarding/referral_entry/code-auto-apply-1.png"
      Title: "That code didn't work. Here's an awesome offer for you anyway"
      Subtitle: "You'll get up to ₹200 cashback"
      Desc: "Sign up & add funds to your Savings Account to claim this offer"
      CanDismiss: true
    ReferralCodeClaimFailedPopup:
      MinAndroidVersion: 1
      MinIOSVersion: 1
    MaxReferralCodeClaimAttempts: 3
  FeatureRestrictionParams:
    AgeRestriction:
      Enable: false
      AgeLowerBound: 30

ReferralsV1:
  StackedRewards:
    StartDateTime: "2023-08-10T15:04:05+05:30"
    EndDateTime: "2033-08-10T15:04:05+05:30"
    ShowReferralHistoryEarningSummary: true
    RefereeSignupActionExpiryDuration: 168h
  AppUpdateHardNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99
  AppUpdateSoftNudgeConf:
    ShowNudgeTillAppVersionAndroid: 8999
    ShowNudgeTillAppVersionIos: 8999
    UserBucketStart: 0
    UserBucketEnd: 99

AddFundsParams:
  AddFundsV3Params:
    ImageWithTextConstraints:
      IsEnableOnAndroid: true
      MinAndroidVersion: 1
      IsEnableOnIos: true
      MinIosVersion: 1
  IsTierMovementDropOffFeedbackFlowEnabled: true
  IntentNavigateToPayStatusAndroidVersion: 1
  CollectNavigateToPayStatusAndroidVersion: 1

AddFundsV2Params:
  WhitelistedActorIds: [ ]
  OnboardingAddFundsV2ScreenDetails:
    SalaryB2BSignupUrl: "https://fi.money/signup"

NonTpapPspHandles: [ "fede" ]

HomeRevampParams:
  HomeNudgeParams:
    TieringParams:
      ToUseV2States: false
  ToShowTieringInfoInSavingsDashboard: true
  LayoutBySegFeatureRelease:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1
  HomeLayoutUIRevamp:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1
    IsEnableOnIos: true
    MinIosVersion: 1

SalaryProgram:
  SalaryBenefitsLandingPageQuickLinksSection:
    IsVisible: true
    MinAndroidVersionSupportingQuickLinksSection: 100
    MinIOSVersionSupportingQuickLinksSection: 729
    QuickLinksTiles:
      Link1:
        Title: "Get a chequebook"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/chequebook.png"
        BgColor: "#FAD0D0"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-pink.png"
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "STORY_SCREEN"
            StoryScreenOptions:
              StoryTitle: "Get a chequebook"
              StoryUrl: "https://stories.fi.money/stories/salary-chequebook-request"
              StoryId: "e24825d2-740b-4fce-9aba-6a1ecc47d0ef"
        TileRank: 1
      Link2:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/Quick_links/statement.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/right-chevron-dark-blue.png"
          BgColor: "#C0DAE0"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
        TileRank: 2
      Link3:
        Title: "Download your bank statement"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#DEEEF2"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#DEEEF2"
          Deeplink:
            Screen: "STATEMENT_REQUEST_SCREEN"
        TileRank: 3
      Link4:
        VisibleFromAndroidVersion: 100
        VisibleFromIosVersion: 100
        Title: "Download Cancelled cheque"
        TitleColor: "#333333"
        ImageUrl: "https://epifi-icons.pointz.in/salaryprogram/coins-home-card.png"
        BgColor: "#FAD0D0"
        CTA:
          IsVisible: true
          ImageUrl: "https://epifi-icons.pointz.in/referrals/chevron-right.png"
          BgColor: "#EFC0C0"
          Deeplink:
            Screen: "DOWNLOAD_DIGITAL_CANCELLED_CHEQUE"
            DownloadDigitalCancelledChequeScreenOptions:
              CtaText:
                FontColor: "#FFFFFF"
                PlainString: "Preview & Download"
                BgColor: '#00B899'
                StandardFontStyle: "BUTTON_M"
              Title:
                FontColor: "#333333"
                PlainString: "Download a digital cancelled cheque on your device"
                StandardFontStyle: "SUBTITLE_1"
              Description:
                FontColor: "#646464"
                PlainString: "Your cancelled cheque is now on your device and ready for you to share"
                StandardFontStyle: "BODY_S"
        TileRank: 4
  HealthInsuranceRewardOfferIdToPolicyConfigMap:
    75c7221a-c14d-4204-bcea-d3d747afc7b1:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "SUPER_TOP_UP_INSURANCE"
    64502d97-6638-45b7-9f52-3254c8d068ce:
      HealthInsurancePolicyFAQsDocS3Path: "healthinsurance/docs/healthInsuranceFAQs.pdf"
      HealthInsurancePolicyClaimProcessDocS3Path: "healthinsurance/docs/claimsProcessDoc.pdf"
      HealthInsuranceCashlessHospitalListDocS3Path: "healthinsurance/docs/cashlessHospitalsDoc.pdf"
      HealthInsuranceTncsDocS3Path: "healthinsurance/docs/healthInsuranceTncs.pdf"
      HealthInsurancePolicyType: "BASE_HEALTH_INSURANCE"
    opd-1:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A2C"
    opd-2:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_2A"
    opd-3:
      HealthInsurancePolicyType: "ONSURITY_OPD_WELLNESS_1A"
  AaSalaryConfig:
    AaSalaryAmountSetupVersionV2:
      MinAndroidVersion: 1
      MinIOSVersion: 1
    AaSalarySourceScreenHideConnectTitle:
      MinAndroidVersion: 1
      MinIOSVersion: 1
    AaSalaryAmountSetupBackConfirmationPopup:
      Enable : true
      AppVersion:
        MinAndroidVersion: 1
        MinIOSVersion: 1
  SalaryWinningSectionPrioritizedMonthlyIntervals:
    StartDate: 6
    EndDate: 23


QrDeeplinkParams:
  MinAndroidVersionForAmountScreenDeeplink: 1
  MinIosVersionForAmountScreenDeeplink: 1

CreditCard:
  AppVersionSupport:
    MinIosVersionForCreditCard: 330
    MinAndroidVersionForCreditCard: 228
    MinIosVersionForCreditCardIntroV2: 349
    MinAndroidVersionForCreditCardIntroV2: 250
  OnboardingRetryAttemptCutoff: 10
  FiLiteOnboardingHomeScreenRedirectAttemptCutoff: 7
  FiLiteOnboardingBottomTextDisplayAttemptCutoff: 5
  EnableCCAllTxnPagination: false
  PaymentSuccessBannerTimeInMinutes: 5
  ShowCreditCardTabByDefaultFromCardTab: true
  WorkflowConstraints:
    - "CARD_REQUEST_WORKFLOW_CARD_ACTIVATION":
        AppVersionConstraintConfig:
          MinIOSVersion: 9999
    # to be used for secured card onboarding.
    - "CARD_REQUEST_WORKFLOW_CARD_ONBOARDING":
        AppVersionConstraintConfig:
          MinAndroidVersion: 1
          MinIOSVersion: 1
  AllEligibleCcScreenConfig:
    CardComponentTemplateVersion: 1
  IsCcChoicesComponentListViewEnabled: true
  CcNetworkSelectionScreenVersionCheck:
    IsEnableOnAndroid: true
    MinAndroidVersion: 1000
    IsEnableOnIos: true
    MinIosVersion: 1000
  FiLiteBottomCtaConfigs:
    EnableFiLiteBottomCtaVersionCheckFlag:
      IsEnableOnAndroid: true
      MinAndroidVersion: 1000
      IsEnableOnIos: true
      MinIosVersion: 1000
    IsCcChoicesComponentListViewEnabled: true
  EnableDashboardSegmentationCarousels: false
  SegmentIdToCarouselObjectMap:

P2PInvestment:
  SchemesAvailability:
    - "SCHEME_NAME_LL_BOOSTER":
        IsUnavailable: true
        StartTime: "2023-06-28 19:20:00"
        EndTime: "2023-06-28 22:00:00"
  MinIosVersionForConsentCardV2: 100
  MinAndroidVersionForConsentCardV2: 100
  DisableFlexiSchemeBanners: true



NetworthConfig:
  ConfigPath: "mappingJson/networthConfig.json"
  EpfPassbookParams:
    IsEpfRedirectionEnabled: false

QuestSdk:
  Disable: true

PaymentOptionsConfig:
  IntentOptionsConfig:
    UpiAppsAndroidPackages:
      - PackageId: "com.phonepe.app"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/phone_pe.png"
        AppName: "Phone Pe"
      - PackageId: "com.google.android.apps.nbu.paisa.user"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/google_pay.png"
        AppName: "Google Pay"
      - PackageId: "net.one97.paytm"
        AppLogo: "https://epifi-icons.pointz.in/tiering/add_funds/pay_tm.png"
        AppName: "Paytm"
      - PackageId: "com.epifi.paisa.qa"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Fi QA"
      - PackageId: "com.epifi.paisa.staging"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Fi Staging"
      - PackageId: "in.org.npci.upiapp"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "NPCI upi app"
      - PackageId: "com.idfcfirstbank.optimus"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "IDFC"
      - PackageId: "com.axis.mobile"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "AXIS"
      - PackageId: "com.flipkart.android"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Flipkart"
      - PackageId: "org.altruist.BajajExperia"
        AppLogo: "https://epifi-icons.pointz.in/M.fund+icon.png"
        AppName: "Bajaj"
    AllowedUserGroups:
      - 21 # TPAP_INTERNAL

ShowAutoRenewCta: true

VpaMigrationScreenParams:
  OldVpaHandle: "fede"

HomeExploreConfig:
  EnableAskFiSection: false
  EnableFeedbackSection: false

MoneySecrets:
  MfStocksBreakdown:
    MaxStocks: 50
