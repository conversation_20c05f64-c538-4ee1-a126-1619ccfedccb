// nolint: funlen
package user

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/syncmap"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering"
	tieringPkg "github.com/epifi/gamma/pkg/tiering"
	"github.com/epifi/gamma/tiering/tiermappings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	accounts2 "github.com/epifi/gamma/api/accounts"
	actorPb "github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bankcust/compliance"
	"github.com/epifi/gamma/api/frontend/account/enums"
	"github.com/epifi/gamma/api/frontend/deeplink"
	headerPb "github.com/epifi/gamma/api/frontend/header"
	feVkyc "github.com/epifi/gamma/api/frontend/kyc/vkyc"
	feUserPb "github.com/epifi/gamma/api/frontend/user"
	externalPb "github.com/epifi/gamma/api/frontend/user/external"
	"github.com/epifi/gamma/api/kyc"
	beVkycPb "github.com/epifi/gamma/api/kyc/vkyc"
	accountsPb "github.com/epifi/gamma/api/savings"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	types "github.com/epifi/gamma/api/typesv2"
	accountTypesPb "github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	ambHelper "github.com/epifi/gamma/frontend/pkg/tiering/amb"
	fePkgUpi "github.com/epifi/gamma/frontend/pkg/upi"
	earnedBenefits "github.com/epifi/gamma/frontend/tiering/earned_benefits"
	feTieringHelper "github.com/epifi/gamma/frontend/tiering/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"
	pkgOnb "github.com/epifi/gamma/pkg/onboarding"
	pkgUser "github.com/epifi/gamma/pkg/user"
	"github.com/epifi/gamma/pkg/vkyc"
)

type HeaderSectionData struct {
	SalaryAccountTags              []*commontypes.Text
	IsTieringEnabled               bool
	IsUserInGracePeriod            bool
	CurrentTierMinBalance          *gmoney.Money
	CurrTierMovementTimestamp      time.Time
	PreviousTier                   beTieringExtPb.Tier
	DowngradeWindowDuration        time.Duration
	IsUserDowngraded               bool
	GracePeriodExpiry              time.Time
	GracePeriodExpiryStr           string
	CurrentTier                    beTieringExtPb.Tier
	VkycTopBannerData              *beVkycPb.VKYCNudge
	VkycMiddleBannerData           *beVkycPb.VKYCNudge
	UserProfileResp                *userPb.GetUserProfileResponse
	KycLevel                       kyc.KYCLevel
	KycExpiresAt                   *timestampPb.Timestamp
	SavingsAccountBasicInfo        *feUserPb.BasicAccountInfo
	PeriodicKYCBannerData          *compliance.PeriodicKYCNudge
	ComplianceStatus               compliance.KYCComplianceStatus
	IsEarnedBenefitsFeatureEnabled bool
	IsTieringMultipleWaysEnabled   bool
	AccountInfoMap                 map[accountTypesPb.AccountProductOffering]*AccountInfoData
	userCurrentBalance             *gmoney.Money
	tierEssentials                 feTieringHelper.TieringFeEssentials
	// AMB related fields
	IsAmbEnabledForActor bool
	AmbInfo              *beTieringPb.GetAMBInfoResponse
	IsAmbOnTrack         bool
}

type HeaderSectionFirstIterationData struct {
	UserId                         string
	SalaryAccountTags              []*commontypes.Text
	IsTieringEnabledForActor       bool
	IsUserInGracePeriod            bool
	LastDowngradeDetails           *beTieringExtPb.LatestMovementDetails
	LastUpgradeDetails             *beTieringExtPb.LatestMovementDetails
	DowngradeWindowDuration        time.Duration
	IsUserDowngraded               bool
	CurrentTier                    beTieringExtPb.Tier
	CurrentTierMinBalance          *gmoney.Money
	CurrTierMovementTimestamp      time.Time
	PreviousTier                   beTieringExtPb.Tier
	GracePeriodExpiry              time.Time
	GracePeriodExpiryStr           string
	VkycTopBannerData              *beVkycPb.VKYCNudge
	VkycMiddleBannerData           *beVkycPb.VKYCNudge
	PeriodicKYCBannerData          *compliance.PeriodicKYCNudge
	IsEarnedBenefitsFeatureEnabled bool
	IsTieringMultipleWaysEnabled   bool
	CurrentBalance                 *gmoney.Money
	// AMB related fields
	IsAmbEnabledForActor bool
}

type HeaderSectionSecondIterationData struct {
	UserProfileResp         *userPb.GetUserProfileResponse
	AccountInfoMap          map[accountTypesPb.AccountProductOffering]*AccountInfoData
	KycLevel                kyc.KYCLevel
	KycExpiresAt            *timestampPb.Timestamp
	SavingsAccountBasicInfo *feUserPb.BasicAccountInfo
	ComplianceStatus        compliance.KYCComplianceStatus
	IsAmbOnTrack            bool
	AmbInfo                 *beTieringPb.GetAMBInfoResponse
}

type AccountInfoData struct {
	account  *accountsPb.SavingsAccountEssentials
	vpaId    string
	vpaState feUserPb.VpaState
}

const (
	GenericABVariantOne = "ONE"
)

func (s *Service) isFiliteUser(ctx context.Context, actorId string) (bool, error) {
	isFilite, err := s.onboardingClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
	})

	if err = epifigrpc.RPCError(isFilite, err); err != nil {
		logger.Error(ctx, "error in getting fi-lite user details")
		return false, err
	}

	return isFilite.GetIsFiLiteUser(), nil
}

func (s *Service) getProfileHeaderForFiLiteUser(ctx context.Context, actorId string) (*feUserPb.GetProfileHeaderSectionResponse, error) {
	actorRes, err := s.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})
	if err = epifigrpc.RPCError(actorRes, err); err != nil {
		logger.Error(ctx, "error in getting actor by id", zap.Error(err))
		return nil, err
	}
	userResp, err := s.client.GetUserProfile(ctx, &userPb.GetUserProfileRequest{
		ActorId: actorId,
		UserId:  actorRes.GetActor().GetEntityId(),
	})
	if err = epifigrpc.RPCError(userResp, err); err != nil {
		logger.Error(ctx, "error in get user profile", zap.Error(err))
		return nil, err
	}

	txtComponent, err := s.GetFiLiteTextComponents(ctx)
	if err != nil {
		logger.Error(ctx, "error in getting fi lite text components", zap.Error(err))
		return nil, err
	}

	nameVal := userResp.GetDisplayName()
	if userResp.GetGivenName() != "" {
		nameVal = userResp.GetGivenName()
	}

	return &feUserPb.GetProfileHeaderSectionResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		DisplayName: commontypes.GetTextFromStringFontColourFontStyle(
			nameVal,
			DisplayNameFontColorFilite,
			commontypes.FontStyle_HEADLINE_L,
		),
		DisplayPhoneNumber: commontypes.GetTextFromStringFontColourFontStyle(
			userResp.GetPhoneNumber().ToStringInMobileFormat(),
			DisplayPhoneFontColorFilite,
			commontypes.FontStyle_SUBTITLE_S,
		),
		PhoneNumber:             userResp.GetPhoneNumber(),
		ProfileImage:            userResp.GetImage(),
		ProfileImageBorderColor: ProfileImageBorderWoTieringColor,
		BackgroundColor:         ui.GetBlockColor(BgColorBasic),
		TextComponentList:       []*ui.IconTextComponent{txtComponent},
	}, nil

}

func (s *Service) shouldShowProfileHeaderV2(req *feUserPb.GetProfileHeaderSectionRequest) bool {
	appVersion := req.GetReq().GetAuth().GetDevice().GetAppVersion()
	switch req.GetReq().GetAuth().GetDevice().GetPlatform() {
	case commontypes.Platform_ANDROID:
		return appVersion >= s.genConfig.UserProfile().ProfileHeaderV2MinVersion().MinVersionAndroid()
	case commontypes.Platform_IOS:
		return appVersion >= s.genConfig.UserProfile().ProfileHeaderV2MinVersion().MinVersionIos()
	default:
	}

	return false
}

// GetProfileHeaderSection returns user details to be shown on header section on profile home screen
func (s *Service) GetProfileHeaderSection(ctx context.Context, req *feUserPb.GetProfileHeaderSectionRequest) (*feUserPb.GetProfileHeaderSectionResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	showProfileHeaderV2 := s.shouldShowProfileHeaderV2(req)
	isFilite, err := s.isFiliteUser(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in isFiliteUser", zap.Error(err))
		return nil, err
	}

	if isFilite {
		header, er := s.getProfileHeaderForFiLiteUser(ctx, actorId)
		if er != nil {
			logger.Error(ctx, "error in getProfileHeaderForFiLiteUser", zap.Error(er))
			return nil, er
		}
		return header, nil
	}

	headerData, gatherDataErr := s.gatherDataForHeaderSection(ctx, actorId)

	if gatherDataErr != nil {
		logger.Error(ctx, "error gathering data", zap.Error(gatherDataErr), zap.Any(logger.ACTOR_ID_V2, actorId))
		return &feUserPb.GetProfileHeaderSectionResponse{
			RespHeader: &headerPb.ResponseHeader{
				Status: rpc.StatusInternalWithDebugMsg("error gathering data"),
			},
		}, nil
	}

	textComponents, err := s.getTextComponentsForProfileSection(ctx, actorId, headerData.SalaryAccountTags, headerData.IsTieringEnabled, headerData.CurrentTier, headerData.KycLevel, headerData.KycExpiresAt, isFilite, showProfileHeaderV2, headerData.IsTieringMultipleWaysEnabled)
	if err != nil {
		logger.Error(ctx, "error in getting text component for profile section", zap.Error(err))
		return nil, err
	}

	// todo: make required proto changes and handle different qr codes if both NRE and NRO APOs are present
	accountInfoData, ok := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_REGULAR]
	if !ok {
		accountInfoData = headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRE]
	}

	qrCodeDeeplink := s.getQrCodeDeeplink(headerData.UserProfileResp.GetDisplayName(), accountInfoData.vpaId)
	var userTierComponent *feUserPb.UserTierComponent
	isNRUserRes, err := s.userAttrFetcher.IsNonResidentUser(ctx, &pkgUser.IsNonResidentUserRequest{
		ActorId: actorId,
	})
	if err != nil {
		logger.Error(ctx, "error in IsNonResidentUser", zap.Error(err))
		return nil, err
	}
	if !isNRUserRes.GetIsNonResidentUser() {
		var usrTierCompErr error
		userTierComponent, usrTierCompErr = s.getUserTierComponent(ctx, actorId, headerData)
		if usrTierCompErr != nil {
			return nil, fmt.Errorf("failed to get user tier component, %w", usrTierCompErr)
		}
	}

	res := &feUserPb.GetProfileHeaderSectionResponse{
		RespHeader: &headerPb.ResponseHeader{
			Status: rpc.StatusOk(),
		},
		QrCodeDeeplink: qrCodeDeeplink,
		DisplayName: commontypes.GetTextFromStringFontColourFontStyle(
			headerData.UserProfileResp.GetDisplayName(),
			GetDisplayNameFontColor(headerData.IsTieringEnabled),
			commontypes.FontStyle_HEADLINE_L,
		),
		DisplayPhoneNumber: commontypes.GetTextFromStringFontColourFontStyle(
			headerData.UserProfileResp.GetPhoneNumber().ToStringInMobileFormat(),
			GetDisplayPhoneFontColor(headerData.IsTieringEnabled),
			commontypes.FontStyle_SUBTITLE_S,
		),
		PhoneNumber:             headerData.UserProfileResp.GetPhoneNumber(),
		AlertBarComponent:       s.getAlertBarComponent(headerData.KycLevel, headerData.KycExpiresAt, headerData.VkycTopBannerData, headerData.PeriodicKYCBannerData),
		ProfileImage:            headerData.UserProfileResp.GetImage(),
		ProfileImageBorderColor: GetProfileImageBordercolor(headerData.IsTieringEnabled),
		BackgroundColor:         s.getBackgroundColorForProfileSection(headerData.IsTieringEnabled, headerData.CurrentTier),
		TextComponentList:       textComponents,
		ProfileBanner:           s.getProfileBanner(headerData.IsTieringEnabled, headerData.IsUserInGracePeriod, headerData.CurrentTierMinBalance, headerData.GracePeriodExpiryStr, headerData.CurrentTier),
		KycLevel:                types.KYCLevel(headerData.KycLevel),
		MinKycExpiresAt:         headerData.KycExpiresAt,
		Vpa:                     accountInfoData.vpaId,
		VpaState:                accountInfoData.vpaState,
		ProfileTag:              s.getProfileTag(headerData.KycLevel, headerData.CurrentTier, headerData.ComplianceStatus),
		ProfileVkycBanner:       s.getProfileMiddleBanner(headerData.VkycMiddleBannerData),
		AccountInfoSection:      s.getAccountInfoSection(headerData),
		AccountInfoSectionV2:    s.getAccountInfoSectionV2(headerData),
		AccountInfoSectionV3:    s.getAccountInfoSectionV3(headerData),
		UserTierComponent:       userTierComponent,
	}
	if accountInfoData.vpaState != feUserPb.VpaState_ENABLED {
		res.UpiComponent = s.getDisabledUpiComponent(headerData.IsTieringEnabled)
	} else {
		res.UpiComponent = s.getEnabledUpiComponent(headerData.IsTieringEnabled, accountInfoData.vpaId, headerData.CurrentTier)
	}
	return res, nil
}

func getAccountNumberComponent(acctNum string) *feUserPb.CopyTextComponent {
	return &feUserPb.CopyTextComponent{
		LeftText: commontypes.GetTextFromStringFontColourFontStyle(
			SavingsAccountCopyLeftText,
			AccountInfoLeftTextColor,
			commontypes.FontStyle_SUBTITLE_S),
		CopyText: commontypes.GetTextFromStringFontColourFontStyle(
			acctNum,
			AccountInfoCopyTextColor,
			commontypes.FontStyle_HEADLINE_S,
		),
		LeftImage: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  FederalIconUrl,
			Width:     int32(FederalIconWidth),
			Height:    int32(FederalIconHeight),
		},
		CopyIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  UpiEnabledCopyIconUrl,
			Width:     UpiEnabledCopyIconWidth,
			Height:    UpiEnabledCopyIconHeight,
		},
		BgColor: ui.GetBlockColor(AccountInfoCopyTextBGColor),
	}
}

func getIfscCodeComponent(ifscCode string) *feUserPb.CopyTextComponent {
	return &feUserPb.CopyTextComponent{
		LeftText: commontypes.GetTextFromStringFontColourFontStyle(
			IfscCopyLeftText,
			AccountInfoLeftTextColor,
			commontypes.FontStyle_SUBTITLE_S),
		CopyText: commontypes.GetTextFromStringFontColourFontStyle(
			ifscCode,
			AccountInfoCopyTextColor,
			commontypes.FontStyle_HEADLINE_S,
		),
		CopyIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  UpiEnabledCopyIconUrl,
			Width:     UpiEnabledCopyIconWidth,
			Height:    UpiEnabledCopyIconHeight,
		},
		BgColor: ui.GetBlockColor(AccountInfoCopyTextBGColor),
	}
}

func getAccountInfoUpiEnabledComponent(_ bool, _ beTieringExtPb.Tier, vpaId string) *feUserPb.AccountInfoSection_CopyUpiEnabledComponent {
	return &feUserPb.AccountInfoSection_CopyUpiEnabledComponent{
		CopyUpiEnabledComponent: &feUserPb.CopyTextComponent{
			LeftText: commontypes.GetTextFromStringFontColourFontStyle(
				UpiEnabledLeftText,
				UpiEnabledLeftTextFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
			CopyText: commontypes.GetTextFromStringFontColourFontStyle(
				vpaId,
				UpiEnabledVpaFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
			CopyIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  UpiEnabledCopyIconUrl,
				Width:     UpiEnabledCopyIconWidth,
				Height:    UpiEnabledCopyIconHeight,
			},
			BgColor: ui.GetBlockColor(AccountInfoCopyTextBGColor),
		},
	}
}

func getAccountInfoUpiDisabledComponent(isTieringEnabled bool) *feUserPb.AccountInfoSection_UpiDisabledComponent {
	return &feUserPb.AccountInfoSection_UpiDisabledComponent{
		UpiDisabledComponent: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					UpiDisabledText,
					GetUpiDisabledWTieringFontColor(isTieringEnabled),
					commontypes.FontStyle_SUBTITLE_S,
				),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       GetUpiDisabledWTieringBgColor(isTieringEnabled),
				CornerRadius:  UpiDisabledContainerCornerRadius,
				Height:        UpiDisabledContainerHeight,
				Width:         UpiDisabledContainerWidth,
				LeftPadding:   UpiDisabledContainerPaddingLeft,
				RightPadding:  UpiDisabledContainerPaddingRight,
				TopPadding:    UpiDisabledContainerPaddingTop,
				BottomPadding: UpiDisabledContainerPaddingBottom,
			},
		},
	}
}

func (s *Service) getAccountInfoSection(headerData *HeaderSectionData) *feUserPb.AccountInfoSection {
	// using regular APO for maintaining backward compatibility
	accountInfoData, ok := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_REGULAR]
	if !ok {
		return nil
	}

	section := &feUserPb.AccountInfoSection{
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			AccountInfoSectionTitle,
			colorChalk,
			commontypes.FontStyle_HEADLINE_S,
		),
		Cta:                        getAccountDetailsCTA(headerData),
		CopyAccountNumberComponent: getAccountNumberComponent(accountInfoData.account.GetAccountNo()),
		CopyIfscCodeComponent:      getIfscCodeComponent(accountInfoData.account.GetIfscCode()),
		BgColour:                   s.getBackgroundColorForProfileSection(headerData.IsTieringEnabled, headerData.CurrentTier),
	}

	if accountInfoData.vpaState != feUserPb.VpaState_ENABLED {
		section.UpiComponent = getAccountInfoUpiDisabledComponent(headerData.IsTieringEnabled)
	} else {
		section.UpiComponent = getAccountInfoUpiEnabledComponent(headerData.IsTieringEnabled, headerData.CurrentTier, accountInfoData.vpaId)
	}

	return section
}

func (s *Service) getCopyTextComponent(label string, value *commontypes.Text, copyDisabled bool) *feUserPb.CopyTextComponent {
	copyIcon := CopyIcon
	if copyDisabled {
		copyIcon = CopyIconDisabled
	}

	return &feUserPb.CopyTextComponent{
		LeftText:          commontypes.GetTextFromStringFontColourFontStyle(label, colorHighEmphasis50, commontypes.FontStyle_SUBTITLE_S),
		CopyText:          value,
		CopyDisabled:      copyDisabled,
		CopyVisualElement: commontypes.GetVisualElementFromUrlHeightAndWidth(copyIcon, 20, 20),
	}
}

func (s *Service) getVpaComponent(vpaState feUserPb.VpaState, vpaId string) *feUserPb.CopyTextComponent {
	var vpaCopyComponent *feUserPb.CopyTextComponent
	if vpaState != feUserPb.VpaState_ENABLED {
		vpaCopyComponent = s.getCopyTextComponent(UpiEnabledLeftText, commontypes.GetTextFromStringFontColourFontStyle(UpiDisabledText, colorSweetPink, commontypes.FontStyle_SUBTITLE_S), true)
	} else {
		vpaCopyComponent = s.getCopyTextComponent(UpiEnabledLeftText, commontypes.GetTextFromStringFontColourFontStyle(vpaId, AccountInfoCopyTextColor, commontypes.FontStyle_SUBTITLE_S), false)
	}

	return vpaCopyComponent
}

func (s *Service) getAccountInfoSectionV2(headerData *HeaderSectionData) *feUserPb.AccountInfoSectionV2 {
	// AccountInfoSectionV2 supports only single account info. so using regular or NRE APO
	accountInfoData, ok := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_REGULAR]
	if !ok {
		accountInfoData = headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRE]
	}

	section := &feUserPb.AccountInfoSectionV2{
		Title: commontypes.GetTextFromStringFontColourFontStyle(AccountInfoSectionTitle, colorChalk, commontypes.FontStyle_HEADLINE_S),
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      getAccountInfoBgColor(headerData.CurrentTier),
			CornerRadius: 16,
		},
	}

	section.AccountInfoList = append(section.GetAccountInfoList(),
		s.getVpaComponent(accountInfoData.vpaState, accountInfoData.vpaId),
		s.getCopyTextComponent(SavingsAccountCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetAccountNo(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
		s.getCopyTextComponent(IfscCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetIfscCode(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
	)

	return section
}

func (s *Service) getAccountInfoSectionV3(headerData *HeaderSectionData) *feUserPb.AccountInfoSectionV3 {
	accountInfoSectionTitle := AccountInfoSectionTitle
	section := &feUserPb.AccountInfoSectionV3{
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      getAccountInfoBgColor(headerData.CurrentTier),
			CornerRadius: 16,
		},
	}

	_, hasRegularSaAccount := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_REGULAR]
	if hasRegularSaAccount {
		section.AccountInfos = append(section.GetAccountInfos(), s.getRegularSavingsAccountInfo(headerData))
	}

	_, hasNREAccount := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRE]
	if hasNREAccount {
		section.AccountInfos = append(section.GetAccountInfos(), s.getNREAccountInfo(headerData))
		section.ContainerProperties.BgColor = colors.ColorDarkLayer2
	}

	_, hasNROAccount := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRO]
	if hasNROAccount {
		section.AccountInfos = append(section.GetAccountInfos(), s.getNROAccountInfo(headerData))
		section.ContainerProperties.BgColor = colors.ColorDarkLayer2
	}

	if len(section.GetAccountInfos()) == 1 && hasNREAccount {
		accountInfoSectionTitle = fmt.Sprintf("<span style=\"color:%s\">NRE</span> %s", colors.ColorMoss400, AccountInfoSectionTitle)
	}

	if len(section.GetAccountInfos()) == 1 && hasNROAccount {
		accountInfoSectionTitle = fmt.Sprintf("<span style=\"color:&s\">NRO</span> %s %s", colors.ColorOcean400, AccountInfoSectionTitle)
	}

	if len(section.GetAccountInfos()) >= 2 {
		section.SelectedTabId = accountTypesPb.AccountProductOffering_APO_NRE.String()
		section.TabWidget = &ui.TabWidget{
			Theme: ui.TabWidget_THEME_DARK,
			Size:  ui.TabWidget_SIZE_SMALL,
		}
		section.GetContainerProperties().BgColor = colors.ColorDarkLayer2

		for _, accountInfo := range section.GetAccountInfos() {
			var tabText string
			switch accountInfo.GetId() {
			case accountTypesPb.AccountProductOffering_APO_NRE.String():
				tabText = "NRE"
			case accountTypesPb.AccountProductOffering_APO_NRO.String():
				tabText = "NRO"
			default:
			}

			section.TabWidget.Tabs = append(section.TabWidget.Tabs, &ui.TabWidget_Tab{
				Id:   accountInfo.GetId(),
				Text: tabText,
			})
		}
	}

	section.Title = commontypes.GetTextFromHtmlStringFontColourFontStyle(accountInfoSectionTitle, colorChalk, commontypes.FontStyle_HEADLINE_S)
	return section
}

// nolint: dupl
func (s *Service) getRegularSavingsAccountInfo(headerData *HeaderSectionData) *feUserPb.AccountInfo {
	accountInfoData := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_REGULAR]
	return &feUserPb.AccountInfo{
		Id: accountTypesPb.AccountProductOffering_APO_REGULAR.String(),
		AccountInfoList: []*feUserPb.CopyTextComponent{
			s.getVpaComponent(accountInfoData.vpaState, accountInfoData.vpaId),
			s.getCopyTextComponent(SavingsAccountCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetAccountNo(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			s.getCopyTextComponent(IfscCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetIfscCode(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
		},
	}
}

// nolint: dupl
func (s *Service) getNREAccountInfo(headerData *HeaderSectionData) *feUserPb.AccountInfo {
	accountInfoData := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRE]
	return &feUserPb.AccountInfo{
		Id: accountTypesPb.AccountProductOffering_APO_NRE.String(),
		AccountInfoList: []*feUserPb.CopyTextComponent{
			s.getVpaComponent(accountInfoData.vpaState, accountInfoData.vpaId),
			s.getCopyTextComponent(SavingsAccountCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetAccountNo(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			s.getCopyTextComponent(IfscCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetIfscCode(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			s.getCopyTextComponent(SwiftCodeLeftText, commontypes.GetTextFromStringFontColourFontStyle(IBANCode, AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			// s.getCopyTextComponent(IBANLeftText, commontypes.GetTextFromStringFontColourFontStyle("accountInfoData.account.GetIBAN()", AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
		},
	}
}

// nolint: dupl
func (s *Service) getNROAccountInfo(headerData *HeaderSectionData) *feUserPb.AccountInfo {
	accountInfoData := headerData.AccountInfoMap[accountTypesPb.AccountProductOffering_APO_NRO]
	return &feUserPb.AccountInfo{
		Id: accountTypesPb.AccountProductOffering_APO_NRO.String(),
		AccountInfoList: []*feUserPb.CopyTextComponent{
			s.getVpaComponent(accountInfoData.vpaState, accountInfoData.vpaId),
			s.getCopyTextComponent(SavingsAccountCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetAccountNo(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			s.getCopyTextComponent(IfscCopyLeftText, commontypes.GetTextFromStringFontColourFontStyle(accountInfoData.account.GetIfscCode(), AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			s.getCopyTextComponent(SwiftCodeLeftText, commontypes.GetTextFromStringFontColourFontStyle(IBANCode, AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
			// s.getCopyTextComponent(IBANLeftText, commontypes.GetTextFromStringFontColourFontStyle("accountInfoData.account.GetIBAN()", AccountInfoCopyTextColor, commontypes.FontStyle_HEADLINE_S), false),
		},
	}
}

func getAccountInfoBgColor(tier beTieringExtPb.Tier) string {
	if tier == beTieringExtPb.Tier_TIER_UNSPECIFIED || tier.IsBaseTier() {
		return "#********"
	}

	if tier.IsSalaryRelatedTier() {
		return colorGrey55Alpha
	}

	return colorGrey40Alpha
}

func getDeeplinkForUserTierComponent(headerData *HeaderSectionData) (*deeplink.Deeplink, error) {
	dl := tiering.AllPlansDeeplink(headerData.CurrentTier, headerData.IsTieringMultipleWaysEnabled)
	if headerData.IsEarnedBenefitsFeatureEnabled && earnedBenefits.ShouldShowTieringEarnedBenefitsScreen(headerData.CurrentTier, headerData.PreviousTier, headerData.IsUserDowngraded) {
		var getDlErr error
		tierToLoad := earnedBenefits.GetTierToLoad(headerData.CurrentTier, headerData.PreviousTier, headerData.IsUserDowngraded)
		dl, getDlErr = earnedBenefits.GetEarnedBenefitsDeeplink(tierToLoad)
		if getDlErr != nil {
			return nil, fmt.Errorf("failed to get earned benefits dl, %w", getDlErr)
		}
	}

	return dl, nil
}

func (s *Service) getUserTierComponent(ctx context.Context, actorId string, headerData *HeaderSectionData) (*feUserPb.UserTierComponent, error) {
	dl, dlErr := getDeeplinkForUserTierComponent(headerData)
	if dlErr != nil {
		return nil, fmt.Errorf("failed to get deeplink for user tier component, %w", dlErr)
	}

	userTierComponent := &feUserPb.UserTierComponent{
		RightIcon: commontypes.GetVisualElementFromUrlHeightAndWidth(TierTagRightIconUrl, 30, 30),
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      getAccountInfoBgColor(headerData.CurrentTier),
			CornerRadius: 24,
		},
		Deeplink: dl,
	}

	currTier := headerData.CurrentTier
	currTierDispName := GetTierDisplayName(currTier)
	tierSectionTitle := TierSectionTitle
	leftIconUrl := GetProfileTierIcon(currTier)

	if headerData.IsTieringMultipleWaysEnabled {
		tierSectionTitle = TierSectionTitleV2
		userTierComponent.InfoActionSection = s.getTierInfoActionSectionV2(ctx, actorId, headerData)
		leftIconUrl = GetProfileTierIconV2(currTier)
	} else {
		userTierComponent.InfoActionSection = s.getTierInfoActionSection(headerData)
	}

	titleText := fmt.Sprintf(tierSectionTitle, currTierDispName)
	userTierComponent.Title = commontypes.GetTextFromStringFontColourFontStyle(titleText, colorHighEmphasis50, commontypes.FontStyle_SUBTITLE_M)
	subtitleText := fmt.Sprintf(TierSectionSubtitle, headerData.CurrTierMovementTimestamp.Format(dateMonthFormat))
	userTierComponent.SubTitle = commontypes.GetTextFromStringFontColourFontStyle(subtitleText, colorHighEmphasis50Alpha55, commontypes.FontStyle_OVERLINE_2XS_CAPS)
	userTierComponent.LeftIcon = commontypes.GetVisualElementFromUrlHeightAndWidth(leftIconUrl, 36, 36)

	return userTierComponent, nil
}

//nolint:dupl
func (s *Service) getTierInfoActionSection(headerData *HeaderSectionData) *feUserPb.InfoActionSection {
	var infoText *commontypes.Text
	var action *ui.IconTextComponent

	switch {
	case headerData.IsUserInGracePeriod:
		infoText, action = s.getTieringGraceInfoAction(headerData)
	case headerData.IsUserDowngraded:
		infoText, action = s.getTierDowngradedInfoAction(headerData)
	default:
		return nil
	}

	infoActionSection := &feUserPb.InfoActionSection{
		InfoText: infoText,
		Action:   action,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      colorNeutralsInk70Alpha,
			CornerRadius: 24,
		},
	}

	return infoActionSection
}

func (s *Service) LogWhitelistedActors() *syncmap.Map[string, bool] {
	return s.genConfig.ActorsWhitelistedForLogs()
}

func (s *Service) getTierInfoActionSectionV2(ctx context.Context, actorId string, headerData *HeaderSectionData) *feUserPb.InfoActionSection {
	var (
		infoText          *commontypes.Text
		action            *ui.IconTextComponent
		bgColor           = colorNeutralsInk70Alpha // Default background color
		isB2BVerified     bool
		assumeB2BVerified bool
	)

	// handling gracefully as error in evaluating feature flow should not end the flow
	featurePitchTierInProfile, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_TIERING_PITCH_IN_PROFILE).WithActorId(actorId))
	if err != nil {
		logger.Error(ctx, "failed to evaluate if pitching in profile is enabled or not. Handling gracefully", zap.Error(err))
	}

	// get b2b verification status for user
	isUserB2BVerified, isUserB2BVerifiedErr := s.client.GetB2BSalaryProgramVerificationStatus(ctx, &userPb.GetB2BSalaryProgramVerificationStatusRequest{
		Identifier: &userPb.GetB2BSalaryProgramVerificationStatusRequest_ActorId{ActorId: actorId},
	})
	if rpcErr := epifigrpc.RPCError(isUserB2BVerified, isUserB2BVerifiedErr); rpcErr != nil {
		logger.Error(ctx, "failed to get b2b verification status. Handling gracefully", zap.Error(rpcErr))
		// Due to the error, B2B verified users may incorrectly have their status set to false,
		// which could lead to entry points being shown to them even though they are actually B2B verified.
		assumeB2BVerified = true
	} else {
		isB2BVerified = isUserB2BVerified.GetIsVerified()
		logger.InfoForActor(ctx, s.LogWhitelistedActors(), "The B2B verification status of user ", zap.String("b2b_status", isUserB2BVerified.String()), zap.String("current_tier", headerData.CurrentTier.String()), zap.String(logger.ACTOR_ID_V2, actorId))
	}
	// The order of the switch cases is intentional and important from a business perspective.
	switch {
	case headerData.IsUserInGracePeriod:
		infoText, action = s.getTieringGraceInfoActionV2(headerData)
	case headerData.IsUserDowngraded:
		infoText, action = s.getTierDowngradedInfoActionV2(headerData)
	case headerData.IsAmbEnabledForActor && !headerData.IsAmbOnTrack && headerData.AmbInfo != nil:
		infoText, action, bgColor = s.getLowAmbInfoAction(headerData)
	case headerData.CurrentTier.IsBaseTier() && !isB2BVerified && !assumeB2BVerified && featurePitchTierInProfile:
		infoText, action, bgColor = s.getBaseTierInfoAction(ctx, headerData, actorId)
	default:
		isDiffBalanceEnabled, variant, err := s.abEvaluatorGeneric.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE).WithActorId(actorId))
		if err != nil {
			logger.Error(ctx, "error in evaluating release", zap.Error(err))
			return nil
		}
		if isDiffBalanceEnabled && variant == GenericABVariantOne {
			infoText, action = s.getTierInfoActionForDeltaBalance(headerData)
		} else {
			if !earnedBenefits.IsTierAllowedForEarnedBenefitsScreen(headerData.CurrentTier) {
				return nil
			}
			externalToInternalMap := tiermappings.GetExternalToInternalMap()
			internalPrevTier, internalCurrentTier := externalToInternalMap[headerData.PreviousTier], externalToInternalMap[headerData.CurrentTier]
			if internalPrevTier < internalCurrentTier {
				infoText, action = s.getTierUpgradedInfoAction(headerData)
			}
		}
	}

	if infoText == nil || action == nil {
		return nil
	}

	return &feUserPb.InfoActionSection{
		InfoText: infoText,
		Action:   action,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:      lo.Ternary(bgColor != "", bgColor, colorNeutralsInk70Alpha),
			CornerRadius: 24,
		},
	}
}

func (s *Service) getTierInfoActionForDeltaBalance(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	switch headerData.CurrentTier {
	case beTieringExtPb.Tier_TIER_FI_BASIC, beTieringExtPb.Tier_TIER_FI_REGULAR:
		return s.infoActionBasedOnCurrBalanceForBasic(headerData)
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return s.infoActionBasedOnCurrBalanceForPlus(headerData)
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return s.infoActionBasedOnCurrBalanceForInfinite(headerData)
	}
	return nil, nil
}

// nolint:gocritic
func (s *Service) infoActionBasedOnCurrBalanceForBasic(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink
	if headerData.userCurrentBalance.GetUnits() >= s.genConfig.Tiering().DeltaBalanceParams().BalanceForPrime() {
		return nil, nil
	} else if headerData.userCurrentBalance.GetUnits() > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingPrimeForBasic() {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingPrimeForBasic(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForPrime()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, nil)
	} else if headerData.userCurrentBalance.GetUnits() > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingInfiniteForBasic() {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingInfiniteForBasic(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForInfinite()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_INFINITE, nil)
	} else {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingPlusForBasic(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForPlus()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_PLUS, nil)
	}
	actionTextStr = s.genConfig.Tiering().DeltaBalanceParams().ActionText()
	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)
	return infoText, action
}

// nolint:gocritic
func (s *Service) infoActionBasedOnCurrBalanceForPlus(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink
	if headerData.userCurrentBalance.GetUnits() > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingPrimeForPlus() {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingPrimeForPlus(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForPrime()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, nil)
	} else if headerData.userCurrentBalance.GetUnits() > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingInfiniteForPlus() {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingInfiniteForPlus(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForInfinite()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_INFINITE, nil)
	} else {
		return nil, nil
	}
	actionTextStr = s.genConfig.Tiering().DeltaBalanceParams().ActionText()
	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)
	return infoText, action
}

func (s *Service) infoActionBasedOnCurrBalanceForInfinite(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink
	if headerData.userCurrentBalance.GetUnits() > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingPrimeForInfinite() {
		infoTextStr = fmt.Sprintf(s.genConfig.Tiering().DeltaBalanceParams().TextForPitchingPrimeForInfinite(), s.genConfig.Tiering().DeltaBalanceParams().BalanceForPrime()-headerData.userCurrentBalance.GetUnits())
		dl = tieringPkg.GetDeeplinkForAddFunds(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, nil)
	} else {
		return nil, nil
	}
	actionTextStr = s.genConfig.Tiering().DeltaBalanceParams().ActionText()
	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)
	return infoText, action
}

func (s *Service) getTieringGraceInfoAction(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink

	if headerData.CurrentTier.IsSalaryTier() {
		infoTextStr = salaryGraceText
		actionTextStr = salaryDowngradedCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION}
	} else {
		infoTextStr = s.getTieringGraceInfoText(headerData)
		actionTextStr = tierGraceCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action
}

//nolint:dupl
func (s *Service) getTieringGraceInfoActionV2(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink

	if headerData.CurrentTier.IsSalaryTier() {
		infoTextStr = salaryGraceText
		actionTextStr = salaryDowngradedCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION}
	} else {
		infoTextStr = s.getTieringGraceInfoText(headerData)
		actionTextStr = tierGraceCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action
}

// getBaseTierInfoAction gets info text, icon text component and the background color
func (s *Service) getBaseTierInfoAction(ctx context.Context, headerData *HeaderSectionData, actorId string) (*commontypes.Text, *ui.IconTextComponent, string) {
	var (
		infoTextStr, actionTextStr, actionTextColor, infoTextColor, bgColor string
		dl                                                                  *deeplink.Deeplink
	)

	actionTextStr = TierTagBasicProfileText
	balance := headerData.userCurrentBalance.GetUnits()

	switch {
	// Balance More than 50000 -> Pitch for prime
	// prime is TIER_FI_AA_SALARY_BAND_3
	case balance > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingPrimeForBasic():
		infoTextStr = infoTextPitchPrime
		bgColor = colorDarkBlue
		dl = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, headerData.IsTieringMultipleWaysEnabled)
		actionTextColor = colorWhite
		infoTextColor = colorWhite
	// Balance (25000, 50000] -> Pitch for infinite
	case balance > s.genConfig.Tiering().DeltaBalanceParams().BalanceForPitchingInfiniteForBasic():
		infoTextStr = infoTextPitchInfinite
		bgColor = colorDarkChalk
		dl = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_INFINITE, headerData.IsTieringMultipleWaysEnabled)
		actionTextColor = colorNeutralsInk
		infoTextColor = colorDullBlack
	// Balance [0, 25000] -> Pitch for plus
	default:
		infoTextStr = infoTextPitchPlus
		bgColor = colorDarkBrown
		dl = tiering.AllPlansDeeplink(beTieringExtPb.Tier_TIER_FI_PLUS, headerData.IsTieringMultipleWaysEnabled)
		actionTextColor = colorWhite
		infoTextColor = colorNeutralsInk
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, infoTextColor, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, actionTextColor, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	logger.InfoForActor(ctx, s.LogWhitelistedActors(), "This base tier actor is eligible for pitching", zap.String(logger.ACTOR_ID_V2, actorId))
	return infoText, action, bgColor
}

func (s *Service) getTieringGraceInfoText(headerData *HeaderSectionData) string {
	if headerData.GracePeriodExpiry.Before(time.Now().Add(3 * 24 * time.Hour)) {
		return fmt.Sprintf(tierGraceTextLastXDays, int(math.Floor(time.Until(headerData.GracePeriodExpiry).Hours()/24)), GetTierDisplayName(headerData.CurrentTier))
	}

	moneyText := moneyPkg.ToDisplayStringWithSuffixAndPrecisionV2(headerData.CurrentTierMinBalance, false, false, 0, false, moneyPkg.IndianNumberSystem)
	return fmt.Sprintf(tierGraceText, moneyText, GetTierDisplayName(headerData.CurrentTier))
}

func (s *Service) getTierDowngradedInfoAction(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink
	switch {
	case headerData.PreviousTier.IsAaSalaryTier() && headerData.CurrentTier.IsAaSalaryTier():
		infoTextStr = primeToPrimeDowngradedText
		actionTextStr = tierPrimeToPrimeDowngradeCtaText
		earnedBenefitsDeeplink, getEarnedBenefitsDeeplinkErr := earnedBenefits.GetEarnedBenefitsDeeplink(headerData.CurrentTier)
		if getEarnedBenefitsDeeplinkErr != nil {
			logger.Error(context.Background(), "failed to get earned benefits deeplink", zap.Error(getEarnedBenefitsDeeplinkErr))
			return nil, nil
		}
		dl = earnedBenefitsDeeplink
	case headerData.PreviousTier.IsSalaryTier():
		infoTextStr = salaryDowngradedText
		actionTextStr = salaryDowngradedCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_SALARY_PROGRAM_LANDING_SCREEN_REDIRECTION}

	default:
		infoTextStr = fmt.Sprintf(tierDowngradedText, GetTierDisplayName(headerData.PreviousTier))
		actionTextStr = tierDowngradeCtaText
		dl = &deeplink.Deeplink{Screen: deeplink.Screen_TRANSFER_IN}
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorSweetPink, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action
}

func (s *Service) getTierDowngradedInfoActionV2(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string
	infoTextColor := infoTextDowngradeColor

	dl := tiering.AllPlansDeeplink(headerData.PreviousTier, true)

	switch {
	case headerData.PreviousTier.IsAaSalaryTier():
		if headerData.CurrentTier.IsAaSalaryTier() {
			infoTextStr, actionTextStr = primeToPrimeDowngradedText, tierPrimeToPrimeDowngradeV2CtaText
			earnedBenefitsDeeplink, getEarnedBenefitsDeeplinkErr := earnedBenefits.GetEarnedBenefitsDeeplink(headerData.CurrentTier)
			if getEarnedBenefitsDeeplinkErr != nil {
				logger.Error(context.Background(), "failed to get earned benefits deeplink", zap.Error(getEarnedBenefitsDeeplinkErr))
				return nil, nil
			}
			dl = earnedBenefitsDeeplink
		} else {
			infoTextStr, actionTextStr = fmt.Sprintf(tierDowngradedText, GetTierDisplayName(headerData.PreviousTier)), tierDowngradeCtaText
		}

	case headerData.PreviousTier == beTieringExtPb.Tier_TIER_FI_INFINITE:
		infoTextStr = fmt.Sprintf(tierDowngradedText, GetTierDisplayName(headerData.PreviousTier))
		actionTextStr = tierDowngradeCtaText
	case headerData.PreviousTier.IsSalaryTier():
		infoTextColor = colorHighEmphasis50
		if headerData.CurrentTier.IsAaSalaryTier() {
			infoTextStr, actionTextStr = youGotUpgradedText, tierPrimeToPrimeDowngradeV2CtaText
			dl = tiering.AllPlansDeeplink(headerData.CurrentTier, true)
		} else {
			infoTextStr, actionTextStr, infoTextColor = switchedFromSalaryProgram, changePlanText, colorHighEmphasis50
		}
	case headerData.PreviousTier == beTieringExtPb.Tier_TIER_FI_PLUS:
		infoTextStr, actionTextStr = fmt.Sprintf(tierDowngradedText, GetTierDisplayName(headerData.PreviousTier)), tierDowngradeCtaText
	default:
		return nil, nil
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, infoTextColor, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action
}

func (s *Service) getTierUpgradedInfoAction(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent) {
	var infoTextStr, actionTextStr string

	earnedBenefitsDeeplink, getEarnedBenefitsDeeplinkErr := earnedBenefits.GetEarnedBenefitsDeeplink(headerData.CurrentTier)
	if getEarnedBenefitsDeeplinkErr != nil {
		logger.Error(context.Background(), "failed to get earned benefits deeplink", zap.Error(getEarnedBenefitsDeeplinkErr))
		return nil, nil
	}
	dl := earnedBenefitsDeeplink

	switch {
	case headerData.PreviousTier == beTieringExtPb.Tier_TIER_FI_INFINITE:
		infoTextStr, actionTextStr = fmt.Sprintf(youGotUpgradedFromText, GetTierDisplayName(headerData.PreviousTier)), seeBenefitsText
	default:
		infoTextStr, actionTextStr = youGotUpgradedText, seeBenefitsText
	}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorHighEmphasis50, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorFiGreen, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action
}

func (s *Service) getLowAmbInfoAction(headerData *HeaderSectionData) (*commontypes.Text, *ui.IconTextComponent, string) {
	if headerData.AmbInfo == nil {
		return nil, nil, ""
	}

	var infoTextStr, actionTextStr string
	var dl *deeplink.Deeplink

	if headerData.CurrentTier == beTieringExtPb.Tier_TIER_FI_REGULAR {
		infoTextStr = lowAmbInfoRegularText
		actionTextStr = trackAmbActionRegularText
	} else {
		infoTextStr = lowAmbInfoText
		actionTextStr = trackAmbActionText
	}
	dl = &deeplink.Deeplink{Screen: deeplink.Screen_AMB_DETAILS_SCREEN}

	infoText := commontypes.GetTextFromStringFontColourFontStyle(infoTextStr, colorAmbInfo, commontypes.FontStyle_SUBTITLE_XS)
	action := ui.NewITC().
		WithTexts(commontypes.GetTextFromStringFontColourFontStyle(actionTextStr, colorAmbAction, commontypes.FontStyle_SUBTITLE_XS)).
		WithDeeplink(dl)

	return infoText, action, colorAmbBg
}

func getAccountDetailsCTA(headerData *HeaderSectionData) *deeplink.Cta {
	screenOptions := deeplinkv3.GetScreenOptionV2WithoutError(&frontend.FederalSavingsAccountInfoOptions{
		BasicAccountInfo: headerData.SavingsAccountBasicInfo,
	})
	dl := &deeplink.Deeplink{
		Screen:          deeplink.Screen_FEDERAL_SAVINGS_ACCOUNT_INFO,
		ScreenOptionsV2: screenOptions,
	}

	return &deeplink.Cta{
		Type:     deeplink.Cta_CUSTOM,
		Text:     AccountInfoViewSettingsText,
		Deeplink: dl,
	}
}

// getBackgroundColorForProfileSection returns background color for the profile header section
// based on the users current tier
func (s *Service) getBackgroundColorForProfileSection(isTieringEnabled bool, currentTier beTieringExtPb.Tier) *ui.BackgroundColour {
	switch {
	case isTieringEnabled && currentTier != beTieringExtPb.Tier_TIER_FI_BASIC && currentTier != beTieringExtPb.Tier_TIER_FI_REGULAR:
		return &ui.BackgroundColour{
			Colour: &ui.BackgroundColour_RadialGradient{
				RadialGradient: &ui.RadialGradient{
					Center: &ui.CenterCoordinates{
						CenterX: 1,
						CenterY: 0,
					},
					OuterRadius: BgColorGradientRadius,
					Colours:     GetBgColors(isTieringEnabled, currentTier),
				},
			},
		}
	default:
		return ui.GetBlockColor(colors.ColorDarkBase)
	}
}

// getAlertBarComponent returns alert bar component in case of min kyc
func (s *Service) getAlertBarComponent(kycLevel kyc.KYCLevel, _ *timestampPb.Timestamp, nudge *beVkycPb.VKYCNudge, periodicKYCNudge *compliance.PeriodicKYCNudge) *feUserPb.AlertBarComponent {
	if periodicKYCNudge != nil {
		return &feUserPb.AlertBarComponent{
			Text:                       periodicKYCNudge.GetProfileNudge().GetTitle(),
			Deeplink:                   periodicKYCNudge.GetProfileNudge().GetDeeplink(),
			BgColor:                    periodicKYCNudge.GetProfileNudge().GetBgColor(),
			LottieAnimationRepeatCount: AlertComponentIconRepeatCount,
			Icon: &feUserPb.AlertBarComponent_AlertIcon{
				AlertIcon: periodicKYCNudge.GetProfileNudge().GetIcon(),
			},
			ComponentType: externalPb.ComponentType_COMPONENT_TYPE_VKYC.String(),
			RightIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  "https://epifi-icons.pointz.in/vkyc/right-arrow.png",
				Width:     20,
				Height:    20,
			},
			AnalyticsEventProperties: periodicKYCNudge.GetAnalyticsEventProperties(),
		}

	}
	if nudge == nil || kycLevel != kyc.KYCLevel_MIN_KYC {
		return nil
	}
	var rightIcon *commontypes.Image
	if nudge.GetDeeplink() != nil {
		rightIcon = &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  AlertComponentRightIconUrl,
			Width:     AlertComponentRightIconWidth,
			Height:    AlertComponentRightIconHeight,
		}
	}
	alertBarComponent := &feUserPb.AlertBarComponent{
		Text: commontypes.GetTextFromStringFontColourFontStyle(
			nudge.GetTitle(),
			AlertComponentFontColor,
			commontypes.FontStyle_SUBTITLE_XS,
		),
		RightIcon:                  rightIcon,
		Deeplink:                   nudge.GetDeeplink(),
		BgColor:                    ui.GetBlockColor(AlertComponentBgColor),
		LottieAnimationRepeatCount: AlertComponentIconRepeatCount,
		ComponentType:              externalPb.ComponentType_COMPONENT_TYPE_VKYC.String(),
	}
	_ = &feUserPb.AlertBarComponent_AlertIcon{
		AlertIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  nudge.GetIconUrl(),
			Width:     AlertComponentIconWidth,
			Height:    AlertComponentIconHeight,
		},
	}
	alertBarComponent.Icon = &feUserPb.AlertBarComponent_IconLottieString{
		IconLottieString: AlertComponentIconLottieString,
	}
	return alertBarComponent
}

// getTextComponentsForProfileSection returns all the text components for the profile header section
// based on conditions whether tiering is enabled or not, user's current tier, user's kyc level
func (s *Service) getTextComponentsForProfileSection(ctx context.Context, actorId string, _ []*commontypes.Text,
	_ bool, currentTier beTieringExtPb.Tier, kycLevel kyc.KYCLevel, kycExpiresAt *timestampPb.Timestamp, isFiLite,
	showProfileV2, isTieringAllPlansV2Enabled bool) ([]*ui.IconTextComponent, error) {
	var components []*ui.IconTextComponent
	if isFiLite {
		component, err := s.GetFiLiteTextComponents(ctx)
		if err != nil {
			logger.Error(ctx, "error in getting fi lite text components", zap.Error(err))
			return nil, err
		}

		return []*ui.IconTextComponent{component}, nil
	}
	tieringComponent := s.getTieringTextComponent(ctx, actorId, currentTier, showProfileV2, isTieringAllPlansV2Enabled)
	if tieringComponent != nil {
		components = append(components, tieringComponent)
	}
	if kycLevel == kyc.KYCLevel_MIN_KYC {
		accExpiryComponent := s.getAccExpiryTextComponent(kycExpiresAt)
		if accExpiryComponent != nil {
			components = append(components, accExpiryComponent)
		}
	}
	return components, nil
}

func (s *Service) getProfileTag(level kyc.KYCLevel, currentTier beTieringExtPb.Tier, complianceStatus compliance.KYCComplianceStatus) *ui.IconTextComponent {
	if complianceStatus == compliance.KYCComplianceStatus_KYC_COMPLIANCE_STATUS_DUE {
		return s.getReKYCTextComponent()
	}
	if level == kyc.KYCLevel_MIN_KYC {
		return s.getMinKycTextComponent()
	}
	return s.getFullKycTextComponent(currentTier)
}

// getFullKycTextComponent returns properties for full kyc text component
func (s *Service) getFullKycTextComponent(currentTier beTieringExtPb.Tier) *ui.IconTextComponent {
	fullKycTagBorderColor := FullKycTagBorderColorBasicTier
	if currentTier != beTieringExtPb.Tier_TIER_FI_BASIC {
		fullKycTagBorderColor = FullKycTagBorderColorNonBasicTier
	}
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				FullKycTagText,
				FullKycTagFontColor,
				commontypes.FontStyle_SUBTITLE_XS,
			),
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       FullKycTagContainerBgColor,
			CornerRadius:  FullKycTagContainerCornerRadius,
			TopPadding:    FullKycTagPaddingTop,
			BottomPadding: FullKycTagPaddingBottom,
			BorderColor:   fullKycTagBorderColor,
			BorderWidth:   FullKycTagBorderWidth,
		},
	}
}

// getReKYCTextComponent returns properties for re kyc due text component
func (s *Service) getReKYCTextComponent() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				ReKycTagText,
				ReKycTagFontColor,
				commontypes.FontStyle_SUBTITLE_XS,
			),
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       ReKycTagContainerBgColor,
			CornerRadius:  ReKycTagContainerCornerRadius,
			TopPadding:    ReKycTagPaddingTop,
			BottomPadding: ReKycTagPaddingBottom,
			BorderColor:   ReKycTagBorderColor,
			BorderWidth:   ReKycTagBorderWidth,
		},
	}
}

// getMinKycTextComponent returns properties for full kyc text component
func (s *Service) getMinKycTextComponent() *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				MinKycTagText,
				MinKycTagFontColor,
				commontypes.FontStyle_SUBTITLE_XS,
			),
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       MinKycTagContainerBgColor,
			CornerRadius:  MinKycTagContainerCornerRadius,
			TopPadding:    MinKycTagPaddingTop,
			BottomPadding: MinKycTagPaddingBottom,
			BorderColor:   MinKycTagBorderColor,
			BorderWidth:   MinKycTagBorderWidth,
		},
	}
}

func (s *Service) GetFiLiteTextComponents(ctx context.Context) (*ui.IconTextComponent, error) {
	dl, err := pkgOnb.GetSABenefitsScreen(ctx)
	if err != nil {
		return nil, err
	}
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  TierTagBasicIconUrl,
			Width:     TierTagLeftIconWidth,
			Height:    TierTagLeftIconHeight,
		},
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				TierTagBasicText,
				TierTagFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
		},
		LeftImgTxtPadding:  TierTagLeftPadding,
		RightImgTxtPadding: TierTagRightPadding,
		Deeplink:           dl,
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       TierTagContainerBgColor,
			CornerRadius:  TierTagContainerCornerRadius,
			Height:        TierTagContainerHeight,
			Width:         TierTagContainerWidth,
			LeftPadding:   TierTagPaddingLeft,
			RightPadding:  TierTagPaddingRightBasicTier,
			TopPadding:    TierTagPaddingTop,
			BottomPadding: TierTagPaddingBottom,
		},
	}, nil
}

// getTieringTextComponent returns tiering text component properties
func (s *Service) getTieringTextComponent(_ context.Context, _ string, currentTier beTieringExtPb.Tier, showProfileV2, isTieringAllPlansV2Enabled bool) *ui.IconTextComponent {
	if showProfileV2 {
		return nil
	}

	// ignoring error since errors in all plans deeplink shouldn't affect the profile
	// so doing it in the best effort basis
	var rightIcon *commontypes.Image
	rightPadding := TierTagPaddingRightBasicTier
	if currentTier != beTieringExtPb.Tier_TIER_FI_BASIC {
		rightIcon = &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  TierTagRightIconUrl,
			Width:     TierTagRightIconWidth,
			Height:    TierTagRightIconHeight,
		}
		rightPadding = TierTagPaddingRightNonBasicTier
	}
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  GetTierTagUrl(currentTier),
			Width:     TierTagLeftIconWidth,
			Height:    TierTagLeftIconHeight,
		},
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				GetTierTagLeftIconText(currentTier),
				TierTagFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
		},
		RightIcon:          rightIcon,
		LeftImgTxtPadding:  TierTagLeftPadding,
		RightImgTxtPadding: TierTagRightPadding,
		Deeplink:           tiering.AllPlansDeeplink(currentTier, isTieringAllPlansV2Enabled),
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       TierTagContainerBgColor,
			CornerRadius:  TierTagContainerCornerRadius,
			Height:        TierTagContainerHeight,
			Width:         TierTagContainerWidth,
			LeftPadding:   TierTagPaddingLeft,
			RightPadding:  rightPadding,
			TopPadding:    TierTagPaddingTop,
			BottomPadding: TierTagPaddingBottom,
		},
	}
}

// getAccExpiryTextComponent returns account expiry text component if the user is min_kyc
func (s *Service) getAccExpiryTextComponent(kycExpiresAt *timestampPb.Timestamp) *ui.IconTextComponent {
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				fmt.Sprintf(AccExpiryTagText, kycExpiresAt.AsTime().Local().Format("02 Jan 06")),
				AccExpiryTagFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
		},
		RightIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  AccExpiryTagIconUrl,
			Width:     AccExpiryTagIconWidth,
			Height:    AccExpiryTagIconHeight,
		},
		RightImgTxtPadding: AccExpiryTagRightPadding,
	}
}

// getSalaryTextComponent returns salary text component properties
func (s *Service) getSalaryTextComponent(salaryTags []*commontypes.Text) *ui.IconTextComponent {
	if salaryTags == nil {
		return nil
	}
	return &ui.IconTextComponent{
		LeftIcon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  SalaryTagIconUrl,
			Width:     SalaryTagIconWidth,
			Height:    SalaryTagIconHeight,
		},
		Texts: []*commontypes.Text{
			commontypes.GetTextFromStringFontColourFontStyle(
				SalaryTagText,
				SalaryTextFontColor,
				commontypes.FontStyle_SUBTITLE_XS,
			),
		},
		LeftImgTxtPadding: SalaryTagLeftPadding,
		Deeplink: &deeplink.Deeplink{
			Screen: deeplink.Screen_SALARY_PROGRAM_ACCOUNT_BENEFITS_SCREEN,
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			BgColor:       SalaryTagContainerBgColor,
			CornerRadius:  SalaryTagContainerCornerRadius,
			Height:        SalaryTagContainerHeight,
			Width:         SalaryTagContainerWidth,
			LeftPadding:   SalaryTagPaddingLeft,
			RightPadding:  SalaryTagPaddingRight,
			TopPadding:    SalaryTagPaddingTop,
			BottomPadding: SalaryTagPaddingBottom,
			BorderColor:   SalaryTagBorderColor,
			BorderWidth:   SalaryTagBorderWidth,
		},
	}
}

// getProfileBanner returns banner in profile header section
// eg: Add funds for users in grace period
func (s *Service) getProfileBanner(isTieringEnabled, isUserInGracePeriod bool, currentTierMinBalance *gmoney.Money,
	gracePeriodExpiry string, currentTier beTieringExtPb.Tier) *feUserPb.Banner {
	if !isTieringEnabled || !isUserInGracePeriod {
		return nil
	}
	return &feUserPb.Banner{
		Icon: &commontypes.Image{
			ImageType: commontypes.ImageType_PNG,
			ImageUrl:  BannerIconUrl,
			Width:     BannerIconWidth,
			Height:    BannerIconHeight,
		},
		Title: commontypes.GetTextFromStringFontColourFontStyle(
			GetBannerTitleText(isTieringEnabled, currentTier),
			BannerTitleFontColor,
			commontypes.FontStyle_SUBTITLE_S,
		),
		SubTitle: commontypes.GetTextFromStringFontColourFontStyle(
			GetBannerSubTitleText(isTieringEnabled, currentTier, currentTierMinBalance, gracePeriodExpiry),
			BannerSubTitleFontColor,
			commontypes.FontStyle_SUBTITLE_XS,
		),
		BgColor:  ui.GetBlockColor(BannerBgColor),
		Deeplink: GetBannerDeeplink(currentTier),
	}
}

// getEnabledUpiComponent returns upi component if UPI is enabled for the user
func (s *Service) getEnabledUpiComponent(isTieringEnabled bool, vpaId string, currentTier beTieringExtPb.Tier) *feUserPb.GetProfileHeaderSectionResponse_UpiEnabledComponent {
	return &feUserPb.GetProfileHeaderSectionResponse_UpiEnabledComponent{
		UpiEnabledComponent: &feUserPb.UpiEnabledComponent{
			LeftText: commontypes.GetTextFromStringFontColourFontStyle(
				UpiEnabledLeftText,
				UpiEnabledLeftTextFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
			VpaId: commontypes.GetTextFromStringFontColourFontStyle(
				vpaId,
				UpiEnabledVpaFontColor,
				commontypes.FontStyle_SUBTITLE_S,
			),
			CopyIcon: &commontypes.Image{
				ImageType: commontypes.ImageType_PNG,
				ImageUrl:  UpiEnabledCopyIconUrl,
				Width:     UpiEnabledCopyIconWidth,
				Height:    UpiEnabledCopyIconHeight,
			},
			BgColor: ui.GetBlockColor(GetUpiEnabledBgColor(isTieringEnabled, currentTier)),
		},
	}
}

// getDisabledUpiComponent returns upi component if UPI is disabled for the user
func (s *Service) getDisabledUpiComponent(isTieringEnabled bool) *feUserPb.GetProfileHeaderSectionResponse_UpiDisabledComponent {
	return &feUserPb.GetProfileHeaderSectionResponse_UpiDisabledComponent{
		UpiDisabledComponent: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(
					UpiDisabledText,
					GetUpiDisabledWTieringFontColor(isTieringEnabled),
					commontypes.FontStyle_SUBTITLE_S,
				),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor:       GetUpiDisabledWTieringBgColor(isTieringEnabled),
				CornerRadius:  UpiDisabledContainerCornerRadius,
				Height:        UpiDisabledContainerHeight,
				Width:         UpiDisabledContainerWidth,
				LeftPadding:   UpiDisabledContainerPaddingLeft,
				RightPadding:  UpiDisabledContainerPaddingRight,
				TopPadding:    UpiDisabledContainerPaddingTop,
				BottomPadding: UpiDisabledContainerPaddingBottom,
			},
		},
	}
}

// getQrCodeDeeplink returns QR code deeplink for profile header section
func (s *Service) getQrCodeDeeplink(name string, vpaId string) *deeplink.Deeplink {
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_PROFILE_UPI_QR_CODE_SCREEN,
		ScreenOptions: &deeplink.Deeplink_ProfileUpiQrCodeScreenOptions{
			ProfileUpiQrCodeScreenOptions: &deeplink.ProfileUpiQrCodeScreenOptions{
				Name: name,
				Vpa:  vpaId,
			},
		},
	}
}

// gatherDataForHeaderSection gathers all the data that is needed for profile header section
// parent method to all gather data methods
func (s *Service) gatherDataForHeaderSection(ctx context.Context, actorId string) (*HeaderSectionData, error) {
	firstIterationData, firstIterationErr := s.gatherDataForHeaderSectionFirstIteration(ctx, actorId)
	if firstIterationErr != nil {
		return nil, firstIterationErr
	}
	secondIterationData, secondIterationErr := s.gatherDataForHeaderSectionSecondIteration(ctx, actorId, firstIterationData)
	if secondIterationErr != nil {
		return nil, secondIterationErr
	}
	return &HeaderSectionData{
		SalaryAccountTags:              firstIterationData.SalaryAccountTags,
		IsTieringEnabled:               firstIterationData.IsTieringEnabledForActor,
		IsUserInGracePeriod:            firstIterationData.IsUserInGracePeriod,
		CurrentTierMinBalance:          firstIterationData.CurrentTierMinBalance,
		CurrentTier:                    firstIterationData.CurrentTier,
		CurrTierMovementTimestamp:      firstIterationData.CurrTierMovementTimestamp,
		PreviousTier:                   firstIterationData.PreviousTier,
		DowngradeWindowDuration:        firstIterationData.DowngradeWindowDuration,
		IsUserDowngraded:               firstIterationData.IsUserDowngraded,
		GracePeriodExpiry:              firstIterationData.GracePeriodExpiry,
		GracePeriodExpiryStr:           firstIterationData.GracePeriodExpiryStr,
		VkycTopBannerData:              firstIterationData.VkycTopBannerData,
		VkycMiddleBannerData:           firstIterationData.VkycMiddleBannerData,
		UserProfileResp:                secondIterationData.UserProfileResp,
		KycLevel:                       secondIterationData.KycLevel,
		KycExpiresAt:                   secondIterationData.KycExpiresAt,
		SavingsAccountBasicInfo:        secondIterationData.SavingsAccountBasicInfo,
		PeriodicKYCBannerData:          firstIterationData.PeriodicKYCBannerData,
		ComplianceStatus:               secondIterationData.ComplianceStatus,
		IsEarnedBenefitsFeatureEnabled: firstIterationData.IsEarnedBenefitsFeatureEnabled,
		IsTieringMultipleWaysEnabled:   firstIterationData.IsTieringMultipleWaysEnabled,
		AccountInfoMap:                 secondIterationData.AccountInfoMap,
		userCurrentBalance:             firstIterationData.CurrentBalance,
		IsAmbEnabledForActor:           firstIterationData.IsAmbEnabledForActor,
		AmbInfo:                        secondIterationData.AmbInfo,
		IsAmbOnTrack:                   secondIterationData.IsAmbOnTrack,
	}, nil
}

// nolint:funlen
// gatherDataForHeaderSectionFirstIteration gathers all the data that we can gather with only actorId
func (s *Service) gatherDataForHeaderSectionFirstIteration(ctx context.Context, actorId string) (*HeaderSectionFirstIterationData, error) {
	firstIterationData := &HeaderSectionFirstIterationData{}
	firstIterationErrGrp, gCtx := errgroup.WithContext(ctx)
	// get userId
	firstIterationErrGrp.Go(func() error {
		actorResp, errResp := s.actorClient.GetActorById(gCtx, &actorPb.GetActorByIdRequest{
			Id: actorId,
		})
		if err := epifigrpc.RPCError(actorResp, errResp); err != nil {
			return errors.Wrap(err, "error in fetching actor")
		}
		firstIterationData.UserId = actorResp.GetActor().GetEntityId()
		return nil
	})

	// get salary account tags
	firstIterationErrGrp.Go(func() error {
		salaryTags, salaryTagsErr := s.getSalaryAccountTagsForProfile(gCtx, actorId)
		if salaryTagsErr != nil {
			return errors.Wrap(salaryTagsErr, "error in fetching salary tags")
		}
		firstIterationData.SalaryAccountTags = salaryTags
		return nil
	})
	// get data regarding tiering
	firstIterationErrGrp.Go(func() error {
		tieringPitchResp, err := s.beTieringClient.GetTieringPitchV2(gCtx, &beTieringPb.GetTieringPitchV2Request{
			ActorId: actorId,
		})
		if tieringPitchErr := epifigrpc.RPCError(tieringPitchResp, err); tieringPitchErr != nil {
			if tieringPitchResp.GetStatus().GetCode() != uint32(beTieringPb.GetTieringPitchV2Response_DISABLED) {
				return errors.Wrap(tieringPitchErr, "error in fetching current tier for actor")
			}
		}
		getInfoErr := s.populateTieringInfoFromResp(tieringPitchResp, firstIterationData)
		if getInfoErr != nil {
			return errors.Wrap(getInfoErr, "error fetching tiering info from pitch response")
		}
		return nil
	})
	firstIterationErrGrp.Go(func() error {
		tieringConfResp, getConfParamErr := s.beTieringClient.GetConfigParams(gCtx, &beTieringPb.GetConfigParamsRequest{ActorId: actorId})
		if tieringConfErr := epifigrpc.RPCError(tieringConfResp, getConfParamErr); tieringConfErr != nil {
			return fmt.Errorf("GetConfigParams rpc failed, %w", tieringConfErr)
		}

		firstIterationData.DowngradeWindowDuration = tieringConfResp.GetDowngradeWindowDuration().AsDuration()
		firstIterationData.IsTieringMultipleWaysEnabled = tieringConfResp.GetIsMultipleWaysToEnterTieringEnabledForActor()
		return nil
	})
	// get vkyc intro deeplink
	firstIterationErrGrp.Go(func() error {
		vkycResp, err := s.vKycFeClient.GetVKYCNudges(ctx, &beVkycPb.GetVKYCNudgesRequest{
			ActorId: actorId,
			NudgeType: []beVkycPb.UIElement{
				beVkycPb.UIElement_UI_ELEMENT_PROFILE_TOP_BANNER,
				beVkycPb.UIElement_UI_ELEMENT_PROFILE_MIDDLE_WIDGET,
			},
		})
		if vkycErr := epifigrpc.RPCError(vkycResp, err); vkycErr != nil {
			if vkycResp.GetStatus().IsRecordNotFound() {
				return nil
			}
			return errors.Wrap(vkycErr, "error fetching vkyc deeplink")
		}
		for _, nudge := range vkycResp.GetVkycNudges() {
			if nudge.GetNudgeType() == beVkycPb.UIElement_UI_ELEMENT_PROFILE_TOP_BANNER {
				firstIterationData.VkycTopBannerData = nudge
			}
			if nudge.GetNudgeType() == beVkycPb.UIElement_UI_ELEMENT_PROFILE_MIDDLE_WIDGET {
				firstIterationData.VkycMiddleBannerData = nudge
			}
		}
		return nil
	})

	// get periodic kyc deeplink
	firstIterationErrGrp.Go(func() error {
		resp, err := s.complianceClient.GetPeriodicKYCNudges(ctx, &compliance.GetPeriodicKYCNudgesRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return nil
			}
			return errors.Wrap(rpcErr, "error fetching periodic kyc deeplink")
		}
		firstIterationData.PeriodicKYCBannerData = resp.GetNudges()[compliance.NudgeType_NUDGE_TYPE_PROFILE_TOP_BANNER.String()]
		logger.Info(ctx, "Showing periodic kyc banner in profile")
		return nil
	})

	firstIterationErrGrp.Go(func() error {
		var evalErr error
		releaseConstraint := release.NewCommonConstraintData(types.Feature_TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH).WithActorId(actorId)
		firstIterationData.IsEarnedBenefitsFeatureEnabled, evalErr = s.releaseEvaluator.Evaluate(ctx, releaseConstraint)
		if evalErr != nil {
			return fmt.Errorf("failed to evaluate TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH feature, %w", evalErr)
		}

		return nil
	})

	firstIterationErrGrp.Go(func() error {
		currBalance, balanceErr := s.tieringDataCollector.GetBalance(gCtx, actorId)
		if balanceErr != nil {
			return balanceErr
		}
		firstIterationData.CurrentBalance = currBalance
		return nil
	})

	// check if AMB is enabled for the actor
	firstIterationErrGrp.Go(func() error {
		isAmbEnabled := ambHelper.IsAmbEnabledForActor(gCtx, actorId, s.genConfig.Tiering(), s.beTieringClient, s.segmentClient, s.releaseEvaluator)
		firstIterationData.IsAmbEnabledForActor = isAmbEnabled
		return nil
	})

	// get AMB info in second iteration if AMB is enabled
	firstIterationErrGrp.Go(func() error {
		// This will be populated later in second iteration based on IsAmbEnabledForActor
		return nil
	})

	firstIterationErr := firstIterationErrGrp.Wait()
	if firstIterationErr != nil {
		return nil, firstIterationErr
	}

	lastDowngradeTimestamp := firstIterationData.LastDowngradeDetails.GetMovementTimestamp().AsTime()
	lastUpgradeTimestamp := firstIterationData.LastUpgradeDetails.GetMovementTimestamp().AsTime()
	if lastDowngradeTimestamp.After(lastUpgradeTimestamp) &&
		time.Since(lastDowngradeTimestamp) < firstIterationData.DowngradeWindowDuration {
		firstIterationData.IsUserDowngraded = true
	}

	return firstIterationData, nil
}

// populateTieringInfoFromResp populates tiering related info to data struct from pitch rpc response
func (s *Service) populateTieringInfoFromResp(tieringPitchResp *beTieringPb.GetTieringPitchV2Response,
	firstIterationData *HeaderSectionFirstIterationData) error {
	if tieringPitchResp.GetStatus().GetCode() == uint32(beTieringPb.GetTierAtTimeResponse_DISABLED) {
		firstIterationData.IsTieringEnabledForActor = false
		return nil
	}
	firstIterationData.IsTieringEnabledForActor = true
	currentFeTier := tieringPitchResp.GetCurrentTier()
	firstIterationData.CurrentTier = currentFeTier

	latestMovementDetails := tieringPkg.GetLatestMovementDetails(tieringPitchResp)
	firstIterationData.CurrTierMovementTimestamp = latestMovementDetails.GetMovementTimestamp().AsTime()
	firstIterationData.LastUpgradeDetails = tieringPitchResp.GetLastUpgradeDetails()
	firstIterationData.LastDowngradeDetails = tieringPitchResp.GetLastDowngradeDetails()
	firstIterationData.PreviousTier = firstIterationData.LastDowngradeDetails.GetFromTier()

	isCurrentTierOptionsPresent := false
	for _, detail := range tieringPitchResp.GetMovementDetailsList() {
		if detail.GetTierName() != currentFeTier {
			continue
		}
		isCurrentTierOptionsPresent = true
		firstIterationData.IsUserInGracePeriod = !detail.GetIsMovementAllowed()
		firstIterationData.GracePeriodExpiry = detail.GetMovementTimestamp().AsTime()
		firstIterationData.GracePeriodExpiryStr = firstIterationData.GracePeriodExpiry.Format("02 January")
		curFeTierMinBalance, _ := feTieringHelper.GetMinBalanceFromOptions(detail.GetOptions())
		firstIterationData.CurrentTierMinBalance = curFeTierMinBalance
	}
	if isCurrentTierOptionsPresent {
		return nil
	}
	return fmt.Errorf("tier options not present for %s tier", currentFeTier.String())
}

// gatherDataForHeaderSectionSecondIteration gathers all the data that is dependent on first iteration
// will be called after gatherDataForHeaderSectionFirstIteration method
func (s *Service) gatherDataForHeaderSectionSecondIteration(ctx context.Context, actorId string, firstIterationData *HeaderSectionFirstIterationData) (*HeaderSectionSecondIterationData, error) {
	secondIterationData := &HeaderSectionSecondIterationData{}
	secondIterationErrGrp, gCtx := errgroup.WithContext(ctx)
	// get kyc data
	secondIterationErrGrp.Go(func() error {
		customerInfo, errResp := s.bcClient.GetBankCustomer(gCtx, &bankcust.GetBankCustomerRequest{
			Vendor:     commonvgpb.Vendor_FEDERAL_BANK,
			Identifier: &bankcust.GetBankCustomerRequest_ActorId{ActorId: actorId},
		})
		if bcErr := epifigrpc.RPCError(customerInfo, errResp); bcErr != nil {
			return errors.Wrap(bcErr, "error in fetching bank customer of user")
		}
		secondIterationData.KycLevel = customerInfo.GetBankCustomer().GetKycInfo().GetKycLevel()
		// client will consume account expiry at, only when user is min kyc user
		if secondIterationData.KycLevel != kyc.KYCLevel_FULL_KYC {
			// Account will expire after AccountClosureTimeLimit hence we are adding AccountClosureTimeLimit and considering day account created deducting 1 day from it
			secondIterationData.KycExpiresAt = timestampPb.New(customerInfo.GetBankCustomer().GetVendorCreationSucceededAt().AsTime().Add(vkyc.AccountClosureTimeLimit).AddDate(0, 0, -1))
		}
		return nil
	})

	// get rekyc status
	secondIterationErrGrp.Go(func() error {
		resp, err := s.complianceClient.GetPeriodicKYCDetail(ctx, &compliance.GetPeriodicKYCDetailRequest{
			ActorId: actorId,
		})
		if rpcErr := epifigrpc.RPCError(resp, err); rpcErr != nil {
			if resp.GetStatus().IsRecordNotFound() {
				return nil
			}
			logger.Error(ctx, "error in getting compliance detail", zap.Error(rpcErr))
			return errors.Wrap(rpcErr, "error in getting compliance detail")
		}
		secondIterationData.ComplianceStatus = resp.GetPeriodicKYCDetail().GetKYCComplianceStatus()
		return nil
	})

	// get user profile data
	secondIterationErrGrp.Go(func() error {
		userResp, err := s.client.GetUserProfile(ctx, &userPb.GetUserProfileRequest{
			ActorId: actorId,
			UserId:  firstIterationData.UserId,
		})
		if userProfileErr := epifigrpc.RPCError(userResp, err); userProfileErr != nil {
			return errors.Wrap(userProfileErr, "error getting user profile")
		}
		secondIterationData.UserProfileResp = userResp
		return nil
	})

	// get savings account basic info
	secondIterationErrGrp.Go(func() error {
		accounts, accountsErr := s.getAllAccountsBasicInfo(ctx, actorId, 100, []enums.AccountProvenance{enums.AccountProvenance_ACCOUNT_PROVENANCE_INTERNAL}, feUserPb.EntryPoint_ENTRY_POINT_PROFILE)
		if accountsErr != nil {
			return errors.Wrap(accountsErr, "error in getting all accounts basic info")
		}
		secondIterationData.SavingsAccountBasicInfo = getSavingsAccountBasicInfo(accounts)
		return nil
	})

	// get vpa and savings account related data
	secondIterationErrGrp.Go(func() error {
		actorSavingsAccounts, savingsAccountErr := s.dataCollector.GetSavingsAccounts(ctx, actorId)
		if savingsAccountErr != nil {
			return savingsAccountErr
		}

		accountInfoMap := make(map[accountTypesPb.AccountProductOffering]*AccountInfoData)
		for _, account := range actorSavingsAccounts {
			// We're not crashing the API if there is an error in getting UPI ID
			// as it's one of the small components of this API.
			// Error logging has been done in GetVPA method.
			vpaId, vpaState, _ := fePkgUpi.GetVPA(ctx, account.GetId(), account.GetAccountNo(), accounts2.Type_SAVINGS, s.accountPiClient)
			accountInfoMap[account.GetSkuInfo().GetAccountProductOffering()] = &AccountInfoData{
				account:  account,
				vpaId:    vpaId,
				vpaState: vpaState,
			}
		}

		secondIterationData.AccountInfoMap = accountInfoMap

		return nil
	})

	// get AMB info if AMB is enabled for the actor
	secondIterationErrGrp.Go(func() error {
		// Only fetch AMB info if AMB is enabled for the actor
		if !firstIterationData.IsAmbEnabledForActor {
			return nil
		}

		ambResp, ambErr := s.beTieringClient.GetAMBInfo(gCtx, &beTieringPb.GetAMBInfoRequest{
			ActorId: actorId,
		})
		if ambRpcErr := epifigrpc.RPCError(ambResp, ambErr); ambRpcErr != nil {
			// Log the error but don't fail the entire request for AMB info
			logger.Error(gCtx, "error in fetching AMB info", zap.Error(ambRpcErr), zap.String("actorId", actorId))
			return nil // Don't fail the entire request for AMB info
		}

		secondIterationData.AmbInfo = ambResp

		// Calculate IsAmbOnTrack based on current AMB vs target AMB
		// IsAmbOnTrack = true when user doesn't need to add balance (i.e., CurrentAMB >= TargetAMB)
		if ambResp.GetCurrentAmb() != nil && ambResp.GetTargetAmb() != nil {
			currentAMB := ambResp.GetCurrentAmb().GetUnits()
			targetAMB := ambResp.GetTargetAmb().GetUnits()
			secondIterationData.IsAmbOnTrack = currentAMB >= targetAMB
		}

		return nil
	})

	secondIterationErr := secondIterationErrGrp.Wait()
	if secondIterationErr != nil {
		return nil, secondIterationErr
	}
	return secondIterationData, nil
}

func getSavingsAccountBasicInfo(accounts []*feUserPb.BasicAccountInfo) *feUserPb.BasicAccountInfo {
	for _, account := range accounts {
		if account.GetAccountType() == accounts2.Type_SAVINGS {
			return account
		}
	}

	return nil
}
func (s *Service) getProfileMiddleBanner(nudge *beVkycPb.VKYCNudge) *feVkyc.VKYCTile {
	if nudge == nil {
		return nil
	}

	return &feVkyc.VKYCTile{
		IconUrl:  nudge.GetIconUrl(),
		Title:    nudge.GetTitle(),
		ShowTile: true,
		CtaList:  convertToVkycCTA(nudge.GetCtas()),
		Details:  nudge.GetBody(),
	}
}

func convertToVkycCTA(ctas []*deeplink.Cta) []*feVkyc.CTA {
	var kycCtas []*feVkyc.CTA
	for _, cta := range ctas {
		kycCtas = append(kycCtas, &feVkyc.CTA{
			Text:     cta.GetText(),
			Deeplink: cta.GetDeeplink(),
		})
	}
	return kycCtas
}
