package networth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"google.golang.org/protobuf/testing/protocmp"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/colors"

	creditReport "github.com/epifi/gamma/api/creditreportv2"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh"
)

func TestService_GetNextNetWorthRefreshAction(t *testing.T) {
	type args struct {
		ctx context.Context
		req *networthFePb.GetNextNetWorthRefreshActionRequest
	}
	tests := []struct {
		name       string
		args       args
		setupMocks func(f *fields)
		want       *networthFePb.GetNextNetWorthRefreshActionResponse
		wantErr    bool
	}{
		{
			name: "create new refresh session failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo1,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "failed to get existing refresh session,failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo1,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "update nw refresh session failed, failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo1,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
									RefreshId:     "uan-1",
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
								RefreshId:     "uan-1",
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "epf init passbook import rpc fail, failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo1,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
									RefreshId:     "uan-1",
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
								RefreshId:     "uan-1",
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
								RefreshId:     "uan-1",
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
						RefreshId:     "uan-1",
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
						RefreshId:     "uan-1",
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.epfClient.EXPECT().InitEpfPassbookImportFlow(gomock.Any(), &epfPb.InitEpfPassbookImportFlowRequest{
					ActorId: "actor-1",
					ExitDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
					},
					UanNumber: "uan-1",
				}).Return(&epfPb.InitEpfPassbookImportFlowResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "successfully refresh epf",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo1,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
									RefreshId:     "uan-1",
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
								RefreshId:     "uan-1",
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
								RefreshId:     "uan-1",
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
						RefreshId:     "uan-1",
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF.String(),
						RefreshId:     "uan-1",
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.epfClient.EXPECT().InitEpfPassbookImportFlow(gomock.Any(), &epfPb.InitEpfPassbookImportFlowRequest{
					ActorId: "actor-1",
					ExitDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
					},
					UanNumber: "uan-1",
				}).Return(&epfPb.InitEpfPassbookImportFlowResponse{
					Status:          rpc.StatusOk(),
					ClientRequestId: "clientId-1",
				}, nil)
			},
			want: &networthFePb.GetNextNetWorthRefreshActionResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: getEpfOtpDeeplink("uan-1", "clientId-1"),
				RefreshHeader: &networth_refresh.NetWorthRefreshHeader{
					NetWorthRefreshId: "net-worth-refresh-id-1",
					CurrentAssetName:  "EPF",
					ProgressDetails: &networth_refresh.RefreshAssetProgressDetails{
						TotalAssetsToRefresh: 2,
						AssetsRefreshed:      1,
					},
					AssetDisplayInfo: map[string]*networth_refresh.RefreshAssetDisplayInfo{
						"EPF": &networth_refresh.RefreshAssetDisplayInfo{
							Title: commontypes.GetTextFromStringFontColourFontStyle("EPF", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S),
							SkipDeeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "credit report start download process rpc fail, failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo2,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.creditReportClient.EXPECT().StartDownloadProcess(gomock.Any(), gomock.Any()).
					Return(&creditReport.StartDownloadProcessResponse{
						Status: rpc.StatusInternal(),
					}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "Successfully refresh credit score",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "",
						AssetRefreshInfoV2: assetRefreshInfo2,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().CreateNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.CreateNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.creditReportClient.EXPECT().StartDownloadProcess(gomock.Any(), gomock.Any()).
					Return(&creditReport.StartDownloadProcessResponse{
						Status: rpc.StatusOk(),
						NextAction: &deeplinkPb.Deeplink{
							Screen: deeplinkPb.Screen_CREDIT_REPORT_POLL_STATUS,
							ScreenOptions: &deeplinkPb.Deeplink_CreditReportDownloadStatusPollScreenOptions{
								CreditReportDownloadStatusPollScreenOptions: &deeplinkPb.CreditReportDownloadStatusPollScreenOptions{
									ClientRequestId: "client-req-id",
								},
							},
						},
					}, nil)
			},
			want: &networthFePb.GetNextNetWorthRefreshActionResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_CREDIT_REPORT_POLL_STATUS,
					ScreenOptions: &deeplinkPb.Deeplink_CreditReportDownloadStatusPollScreenOptions{
						CreditReportDownloadStatusPollScreenOptions: &deeplinkPb.CreditReportDownloadStatusPollScreenOptions{
							ClientRequestId: "client-req-id",
						},
					},
				},
				RefreshHeader: &networth_refresh.NetWorthRefreshHeader{
					NetWorthRefreshId: "net-worth-refresh-id-1",
					CurrentAssetName:  "Credit Score",
					ProgressDetails: &networth_refresh.RefreshAssetProgressDetails{
						TotalAssetsToRefresh: 2,
						AssetsRefreshed:      1,
					},
					AssetDisplayInfo: map[string]*networth_refresh.RefreshAssetDisplayInfo{
						"Credit Score": &networth_refresh.RefreshAssetDisplayInfo{
							Title: commontypes.GetTextFromStringFontColourFontStyle("LOANS & LIABILITIES", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S),
							SkipDeeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "mf holding import rpc fail, failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo3,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.mfExternalOrdersClient.EXPECT().CreateMFHoldingsImport(gomock.Any(), &mfExternalPb.CreateMFHoldingsImportRequest{
					ActorId:    "actor-1",
					Provenance: mfExternalPb.Provenance_PROVENANCE_NET_WORTH_REFRESH,
					ExitDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
					},
				}).Return(&mfExternalPb.CreateMFHoldingsImportResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "Successfully refresh mutual funds",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo3,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.deeplinkBuilder.EXPECT().NextNetWorthRefreshScreenDeeplink("net-worth-refresh-id-1", []*networth_refresh.AssetRefreshInfo{
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL.String(),
					},
					{
						AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS.String(),
						RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SKIP.String(),
					},
				}).Return(&deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, nil)

				f.mfExternalOrdersClient.EXPECT().CreateMFHoldingsImport(gomock.Any(), &mfExternalPb.CreateMFHoldingsImportRequest{
					ActorId:    "actor-1",
					Provenance: mfExternalPb.Provenance_PROVENANCE_NET_WORTH_REFRESH,
					ExitDeeplink: &deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
					},
				}).Return(&mfExternalPb.CreateMFHoldingsImportResponse{
					Status:     rpc.StatusOk(),
					ExternalId: "externalId-1",
				}, nil)
			},
			want: &networthFePb.GetNextNetWorthRefreshActionResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: deeplink_builder.InitMfHoldingScreenDeeplinkForRefresh("externalId-1", &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
				}, mfExternalPb.Provenance_PROVENANCE_NET_WORTH_REFRESH),
				RefreshHeader: &networth_refresh.NetWorthRefreshHeader{
					NetWorthRefreshId: "net-worth-refresh-id-1",
					CurrentAssetName:  "Mutual Funds",
					ProgressDetails: &networth_refresh.RefreshAssetProgressDetails{
						TotalAssetsToRefresh: 2,
						AssetsRefreshed:      2,
					},
					AssetDisplayInfo: map[string]*networth_refresh.RefreshAssetDisplayInfo{
						"Mutual Funds": &networth_refresh.RefreshAssetDisplayInfo{
							Title: commontypes.GetTextFromStringFontColourFontStyle("MUTUAL FUNDS", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S),
							SkipDeeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_GET_NEXT_ACTION,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "get net_worth instrument refresh details rpc fail for manual assets, failure",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo4,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().GetNetWorthInstrumentsRefreshDetails(gomock.Any(), &networthPb.GetNetWorthInstrumentsRefreshDetailsRequest{
					ActorId: "actor-1",
				}).Return(&networthPb.GetNetWorthInstrumentsRefreshDetailsResponse{
					Status: rpc.StatusInternal(),
				}, nil)
			},
			want:    fullScreenErrResponse(),
			wantErr: false,
		},
		{
			name: "successfully refresh manual assets, success",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo4,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
								},
							},
						},
					}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().UpdateNetWorthRefreshSession(gomock.Any(), &networthPb.UpdateNetWorthRefreshSessionRequest{
					UpdatedNetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
					FieldMasks: []modelPb.NetWorthRefreshSessionFieldMask{
						modelPb.NetWorthRefreshSessionFieldMask_NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS,
					},
				}).Return(&networthPb.UpdateNetWorthRefreshSessionResponse{
					Status: rpc.StatusOk(),
					NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
						Id:      "net-worth-refresh-id-1",
						ActorId: "actor-1",
						AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
							},
							{
								AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS,
								RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_INITIATED,
							},
						},
					},
				}, nil)

				f.networthClient.EXPECT().GetNetWorthInstrumentsRefreshDetails(gomock.Any(), &networthPb.GetNetWorthInstrumentsRefreshDetailsRequest{
					ActorId: "actor-1",
				}).Return(&networthPb.GetNetWorthInstrumentsRefreshDetailsResponse{
					Status:                   rpc.StatusOk(),
					InstrumentRefreshSummary: instrumentRefreshSummary1,
				}, nil)

				f.deeplinkBuilder.EXPECT().NetWorthRefreshSuccessScreen().
					Return(&deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_SUCCESS_SCREEN,
					}, nil)

				f.deeplinkBuilder.EXPECT().ManualAssetRefreshDeeplink(gomock.Any()).
					Return(&deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH,
					}, nil)
			},
			want: &networthFePb.GetNextNetWorthRefreshActionResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_MANUAL_ASSETS_REFRESH,
				},
				RefreshHeader: &networth_refresh.NetWorthRefreshHeader{
					NetWorthRefreshId: "net-worth-refresh-id-1",
					CurrentAssetName:  "Manual Assets",
					ProgressDetails: &networth_refresh.RefreshAssetProgressDetails{
						TotalAssetsToRefresh: 3,
						AssetsRefreshed:      3,
					},
					AssetDisplayInfo: map[string]*networth_refresh.RefreshAssetDisplayInfo{
						"Manual Assets": &networth_refresh.RefreshAssetDisplayInfo{
							Title: commontypes.GetTextFromStringFontColourFontStyle("OTHER ASSETS", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S),
							SkipDeeplink: &deeplinkPb.Deeplink{
								Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_SUCCESS_SCREEN,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "all assets refreshed, show success screen",
			args: args{
				ctx: context.Background(),
				req: &networthFePb.GetNextNetWorthRefreshActionRequest{
					RequestParams: &networth_refresh.NetWorthGetNextActionRequestParams{
						NetWorthRefreshId:  "net-worth-refresh-id-1",
						AssetRefreshInfoV2: assetRefreshInfo5,
					},
				},
			},
			setupMocks: func(f *fields) {
				f.networthClient.EXPECT().GetNetWorthRefreshSession(gomock.Any(), gomock.Any()).
					Return(&networthPb.GetNetWorthRefreshSessionResponse{
						Status: rpc.StatusOk(),
						NetWorthRefreshSession: &modelPb.NetWorthRefreshSession{
							Id:      "net-worth-refresh-id-1",
							ActorId: "actor-1",
							AssetRefreshDetails: []*modelPb.AssetRefreshDetail{
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
								},
								{
									AssetName:     modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF,
									RefreshStatus: modelPb.NetWorthAssetRefreshStatus_NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL,
								},
							},
						},
					}, nil)

				f.deeplinkBuilder.EXPECT().NetWorthRefreshSuccessScreen().
					Return(&deeplinkPb.Deeplink{
						Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_SUCCESS_SCREEN,
					}, nil)
			},
			want: &networthFePb.GetNextNetWorthRefreshActionResponse{
				RespHeader: &header.ResponseHeader{
					Status: rpc.StatusOk(),
				},
				NextAction: &deeplinkPb.Deeplink{
					Screen: deeplinkPb.Screen_NET_WORTH_REFRESH_SUCCESS_SCREEN,
				},
				RefreshHeader: &networth_refresh.NetWorthRefreshHeader{
					NetWorthRefreshId: "net-worth-refresh-id-1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			f := initFields(ctrl)
			tt.setupMocks(f)
			s := NewService(nil, f.networthConfig, f.networthClient, f.consentClient, f.sectionGenerator, f.dataFetcher, f.visualisationGeneratorFactory,
				f.formBuilderFactory, f.formInputValidator, f.assetDashboardGeneratorFactory, f.time, f.onbClient, f.connectedAccountClient, f.deeplinkBuilder,
				f.epfClient, f.mfExternalOrdersClient, f.creditReportClient, f.mockReleaseEvaluator, nil, nil,
				nil, nil, nil, nil, nil, nil, f.mockBroker, nil)
			got, err := s.GetNextNetWorthRefreshAction(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetNextNetWorthRefreshAction() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if diff := cmp.Diff(got, tt.want, protocmp.Transform()); diff != "" {
				t.Errorf("GetNextNetWorthRefreshAction(), diff: %v", diff)
			}
		})
	}
}

func fullScreenErrResponse() *networthFePb.GetNextNetWorthRefreshActionResponse {
	netWorthHubScreenDeeplink, _ := deeplink_builder.GetNetworthHubScreenDeeplink()
	return &networthFePb.GetNextNetWorthRefreshActionResponse{
		RespHeader: &header.ResponseHeader{
			Status:    rpc.StatusInternal(),
			ErrorView: deeplink_builder.FullScreenErrorViewSomethingWentWrongWithoutRetry(updateManualAssetsFailureMsg, gotItText, netWorthHubScreenDeeplink),
		},
	}
}

func getEpfOtpDeeplink(uanNumber, clientReqId string) *deeplinkPb.Deeplink {
	optScreenOptions := deeplink_builder.GetOtpScreenOptionsForSendingOtp(uanNumber, clientReqId, "", false)
	return &deeplinkPb.Deeplink{
		Screen:          deeplinkPb.Screen_EPF_PASSBOOK_IMPORT_OTP_SCREEN,
		ScreenOptionsV2: optScreenOptions,
	}
}
