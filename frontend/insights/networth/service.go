package networth

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/events"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget"
	connectedAccPkg "github.com/epifi/gamma/pkg/connectedaccount/securities"

	secrets2 "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/secrets"
	mfAnalyser "github.com/epifi/gamma/frontend/analyser/investments/mutualfunds"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder"
	networthFormBuilder "github.com/epifi/gamma/frontend/insights/networth/manual_forms/formbuilder/networth"
	wealthLandingDashboard "github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/dashboard"
	"github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable/dashboard"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/insights/networth/enums"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/frontend/insights/networth/generator/widget/impl/helper"
	"github.com/epifi/gamma/pkg/deeplinkv3"

	"context"
	"fmt"

	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	beCaPb "github.com/epifi/gamma/api/connected_account"
	"github.com/epifi/gamma/api/consent"
	creditReport "github.com/epifi/gamma/api/creditreportv2"
	feErrors "github.com/epifi/gamma/api/frontend/errors"
	"github.com/epifi/gamma/api/frontend/header"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	secretsFePb "github.com/epifi/gamma/api/frontend/insights/secrets"
	epfPb "github.com/epifi/gamma/api/insights/epf"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	modelPb "github.com/epifi/gamma/api/insights/networth/model"
	secretsEnumsPb "github.com/epifi/gamma/api/insights/secrets/enums"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	"github.com/epifi/gamma/api/typesv2/ui"
	typesUiPb "github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	networthConfig "github.com/epifi/gamma/frontend/insights/networth/config"
	"github.com/epifi/gamma/frontend/insights/networth/data_fetcher"
	networthDeeplink "github.com/epifi/gamma/frontend/insights/networth/deeplink"
	"github.com/epifi/gamma/frontend/insights/networth/generator/section"
	"github.com/epifi/gamma/frontend/insights/networth/generator/visualisation"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/asset_dashboard"
	"github.com/epifi/gamma/frontend/insights/networth/manual_forms/inputvalidator"
	"github.com/epifi/gamma/frontend/insights/networth/wealth_builder_components/scrollable"
	"github.com/epifi/gamma/pkg/feature/release"
)

const (
	NetWorthOutdated        = "Net Worth outdated! Refresh now."
	AssetsOutdatedMsg       = "Value looks outdated! Refresh now"
	LinkBrokenRedImage      = "https://epifi-icons.pointz.in/networth/link_broken_red.png"
	LinkBrokenYellowImage   = "https://epifi-icons.pointz.in/networth/link_broken_yellow.png"
	ColorSupportingCherry50 = "#F8E5EB"
	rightGreenChevron       = "https://epifi-icons.pointz.in/investment/right_chevron_sip_renew.png"
	addAssetsImage          = "https://epifi-icons.pointz.in/usstocks_images/plus-icon.png"
	shareImage              = "https://epifi-icons.pointz.in/magic_import/lens_share_image.png"
)

type Service struct {
	networthFePb.UnimplementedNetWorthServer
	feConfig                            *genconf.Config
	networthConfig                      *networthConfig.Config
	networthClient                      networthBePb.NetWorthClient
	consentClient                       consent.ConsentClient
	sectionGenerator                    section.IGenerator
	dataFetcher                         data_fetcher.NetWorthDataFetcher
	visualisationGeneratorFactory       visualisation.IGeneratorFactory
	formBuilderFactory                  networthFormBuilder.FormBuilderFactory
	formInputValidator                  inputvalidator.FormInputValidator
	assetDashboardGeneratorFactory      asset_dashboard.IAssetDashboardGeneratorFactory
	time                                datetime.Time
	onbClient                           onboarding.OnboardingClient
	connectedAccountClient              beCaPb.ConnectedAccountClient
	deeplinkBuilder                     deeplink_builder.IDeeplinkBuilder
	epfClient                           epfPb.EpfClient
	mfExternalClient                    mfExternalPb.MFExternalOrdersClient
	creditReportClient                  creditReport.CreditReportManagerClient
	releaseEvaluator                    release.IEvaluator
	moneySecretsFeClient                secretsFePb.SecretsClient
	formProcessor                       formbuilder.FormProcessor
	scrollableComponentGeneratorFactory scrollable.IWealthComponentGeneratorFactory
	analyticsCollector                  mfAnalyser.AnalyticsCollector
	dashboardComponentViewGetterFactory dashboard.IWealthDashboardComponentViewGetterFactory
	wealthBuilderLanding                wealthLandingDashboard.IWealthBuilderLanding
	widgetGeneratorFactory              widget.IWidgetGeneratorFactory
	variableGeneratorClient             analyserVariablePb.VariableGeneratorClient
	eventBroker                         events.Broker
	stockTxnsHandler                    connectedAccPkg.IMinimalStockTransactions
}

func NewService(
	feConfig *genconf.Config,
	networthConfig *networthConfig.Config,
	networthClient networthBePb.NetWorthClient,
	consentClient consent.ConsentClient,
	sectionGenerator section.IGenerator,
	dataFetcher data_fetcher.NetWorthDataFetcher,
	visualisationGeneratorFactory visualisation.IGeneratorFactory,
	formBuilderFactory networthFormBuilder.FormBuilderFactory,
	formInputValidator inputvalidator.FormInputValidator,
	assetDashboardGeneratorFactory asset_dashboard.IAssetDashboardGeneratorFactory,
	time datetime.Time,
	onbClient onboarding.OnboardingClient,
	connectedAccountClient beCaPb.ConnectedAccountClient,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
	epfClient epfPb.EpfClient,
	mfExternalClient mfExternalPb.MFExternalOrdersClient,
	creditReportClient creditReport.CreditReportManagerClient,
	releaseEvaluator release.IEvaluator,
	moneySecretsFeClient secretsFePb.SecretsClient,
	formProcessor formbuilder.FormProcessor,
	scrollableComponentGeneratorFactory scrollable.IWealthComponentGeneratorFactory,
	analyticsCollector mfAnalyser.AnalyticsCollector,
	dashboardComponentViewGetterFactory dashboard.IWealthDashboardComponentViewGetterFactory,
	wealthBuilderLanding wealthLandingDashboard.IWealthBuilderLanding,
	widgetGeneratorFactory widget.IWidgetGeneratorFactory,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	eventBroker events.Broker,
	stockTxnsHandler connectedAccPkg.IMinimalStockTransactions,
) *Service {
	return &Service{
		feConfig:                            feConfig,
		networthConfig:                      networthConfig,
		networthClient:                      networthClient,
		consentClient:                       consentClient,
		sectionGenerator:                    sectionGenerator,
		dataFetcher:                         dataFetcher,
		visualisationGeneratorFactory:       visualisationGeneratorFactory,
		formBuilderFactory:                  formBuilderFactory,
		formInputValidator:                  formInputValidator,
		assetDashboardGeneratorFactory:      assetDashboardGeneratorFactory,
		time:                                time,
		onbClient:                           onbClient,
		connectedAccountClient:              connectedAccountClient,
		deeplinkBuilder:                     deeplinkBuilder,
		epfClient:                           epfClient,
		mfExternalClient:                    mfExternalClient,
		creditReportClient:                  creditReportClient,
		releaseEvaluator:                    releaseEvaluator,
		moneySecretsFeClient:                moneySecretsFeClient,
		formProcessor:                       formProcessor,
		scrollableComponentGeneratorFactory: scrollableComponentGeneratorFactory,
		analyticsCollector:                  analyticsCollector,
		dashboardComponentViewGetterFactory: dashboardComponentViewGetterFactory,
		wealthBuilderLanding:                wealthBuilderLanding,
		widgetGeneratorFactory:              widgetGeneratorFactory,
		variableGeneratorClient:             variableGeneratorClient,
		eventBroker:                         eventBroker,
		stockTxnsHandler:                    stockTxnsHandler,
	}
}

// GetNetWorthDashboard rpc will get all the details for net worth dashboard.
// https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5591&mode=design&t=AwPidyMULqEY9jbe-0
// nolint: funlen
func (s *Service) GetNetWorthDashboard(ctx context.Context, req *networthFePb.GetNetWorthDashboardRequest) (*networthFePb.GetNetWorthDashboardResponse, error) {
	actorId := req.GetReq().GetAuth().GetActorId()
	networthDashboardConfig, err := s.networthConfig.GetNetworthDashboardConfig()
	if err != nil {
		logger.Error(ctx, "failed to get networth dashboard config", zap.Error(err))
		return s.getErrResponse("failed to get networth dashboard config"), nil
	}

	respHeader := &header.ResponseHeader{
		Status: rpcPb.StatusOk(),
	}

	if s.feConfig.NetworthConfig().IsNetworthDashboardFeedbackEnabled() {
		respHeader.FeedbackEngineInfo = &header.FeedbackEngineInfo{
			FlowIdDetails: &header.FlowIdentifierDetails{
				FlowIdentifierType: types.FeedbackFlowIdentifierType_FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF,
				FlowIdentifier:     types.FeedbackDropOffFlowIdentifier_FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_NETWORTH_DASHBOARD.String(),
			},
		}
	}
	var (
		visualisation   *networthFePb.NetWorthVisualisation
		sections        []*networthFePb.Section
		refreshBanner   *ui.IconTextComponent
		secretsSection  *networthFePb.SecretsSection
		connectAssetCTA *ui.IconTextComponent
	)

	dashboardType := enums.NetWorthDashBoardType(enums.NetWorthDashBoardType_value[req.GetDashboardType()])
	dashboardMainTitleText := networthDashboardConfig.GetTitle()
	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		dashboardMainTitleText = networthDashboardConfig.GetAssetsTitle()
	}
	g, gctx := errgroup.WithContext(ctx)
	g.Go(func() error {
		var sectionErr error
		visualisation, sections, refreshBanner, connectAssetCTA, sectionErr = s.getNetworthSections(gctx, actorId, networthDashboardConfig, dashboardType)
		if err != nil {
			return fmt.Errorf("failed to get networth sections: %w", sectionErr)
		}
		return nil
	})
	g.Go(func() error {
		var secretsErr error
		secretsSection, secretsErr = s.getNetworthDashboardMoneySecrets(gctx, req.GetReq(), dashboardType)
		if secretsErr != nil {
			// not returning  error here, if some issue in building in money secrets, it should not lead to entire page break
			logger.Error(ctx, "failed to get networth dashboard secrets", zap.Error(secretsErr))
		}
		return nil
	})

	if err := g.Wait(); err != nil {
		logger.Error(ctx, "failed to get networth dashboard details", zap.Error(err))
		return s.getErrResponse(err.Error()), nil
	}

	var components []*networthFePb.Component
	if secretsSection != nil {
		components = append(components, &networthFePb.Component{Component: &networthFePb.Component_SecretsSection{SecretsSection: secretsSection}})
	}
	for _, networthWidgetsSection := range sections {
		components = append(components, &networthFePb.Component{Component: &networthFePb.Component_Section{Section: networthWidgetsSection}})
	}
	return &networthFePb.GetNetWorthDashboardResponse{
		RespHeader:    respHeader,
		Title:         dashboardMainTitleText,
		Visualisation: visualisation,
		Sections:      sections,
		Footer: &commontypes.Text{
			FontColor: colors.ColorOnDarkMediumEmphasis,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: "Net Worth is calculated based on the information made available by you and may not be accurate.",
			},
			FontStyle: &commontypes.Text_StandardFontStyle{
				StandardFontStyle: commontypes.FontStyle_BODY_XS,
			},
		},
		RefreshBanner:    refreshBanner,
		Components:       components,
		ConnectAssetsCta: connectAssetCTA,
	}, nil
}

// nolint:funlen
func (s *Service) getNetworthSections(ctx context.Context, actorId string, networthDashboardConfig *networthFePb.NetWorthDashboardConfig, dashboardType enums.NetWorthDashBoardType) (visualisation *networthFePb.NetWorthVisualisation, networthSections []*networthFePb.Section, refreshBanner *ui.IconTextComponent, addAssetsCTA *ui.IconTextComponent, err error) {
	categoriesDataMap, err := s.dataFetcher.GetCategoriesValue(ctx, actorId, dashboardType)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get categories value: %w", err)
	}

	categoriesStatusMap, err := s.dataFetcher.GetCategoriesStatus(ctx, actorId, categoriesDataMap, dashboardType)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get categories status: %w", err)
	}
	visGenerator, err := s.visualisationGeneratorFactory.GetGenerator(networthFePb.VisualisationType_VISUALISATION_TYPE_ISLAND, networthDashboardConfig, 10)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to get visualisation generator: %w", err)
	}
	instrumentsRefreshDetailsRes, err := s.networthClient.GetNetWorthInstrumentsRefreshDetails(ctx, &networthBePb.GetNetWorthInstrumentsRefreshDetailsRequest{ActorId: actorId})
	if grpcErr := epifigrpc.RPCError(instrumentsRefreshDetailsRes, err); grpcErr != nil {
		logger.Error(ctx, "failed to get net worth instrument refresh details", zap.Error(err))
	}
	isRefreshRequiredAndCritical := s.isRefreshRequiredAndCritical(instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary())

	visualisation, err = visGenerator.Generate(ctx, actorId, categoriesStatusMap, isRefreshRequiredAndCritical, dashboardType)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to generate visualisation: %w", err)
	}
	var netWorthCategoryToRefreshDetailsMap map[networthFePb.NetworthCategory]*networthBePb.InstrumentRefreshDetails

	netWorthCategoryToRefreshDetailsMap = s.dataFetcher.ConvertRefreshDetailsToMap(ctx, instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary())
	onlyManualAssetsForRefresh := s.isOnlyManualAssetForRefresh(instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary())

	categoriesStatusMap, err = s.updateCategoryStatusMap(categoriesStatusMap, netWorthCategoryToRefreshDetailsMap)
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("failed to update CategoryStatusMap for networth refresh: %w", err)
	}
	generatedSection := &networthFePb.Section{}
	var sectionErr error
	for _, sectionConfig := range networthDashboardConfig.GetSections() {
		if dashboardType == enums.NetWorthDashBoardType_ASSETS && sectionConfig.GetSectionType() == networthFePb.NetworthSectionType_NETWORTH_SECTION_TYPE_LIABILITIES {
			continue
		}
		generatedSection, sectionErr = s.sectionGenerator.GenerateSection(ctx, actorId, sectionConfig, categoriesDataMap, categoriesStatusMap, netWorthCategoryToRefreshDetailsMap, onlyManualAssetsForRefresh, dashboardType)
		if sectionErr != nil {
			return nil, nil, nil, nil, fmt.Errorf("failed to generate net worth section %s: %w", sectionConfig.GetSectionType(), sectionErr)
		}
		networthSections = append(networthSections, generatedSection)
	}
	// show refresh banner only if NetWorth Refresh V2 is enabled and refresh is required
	if s.isNetWorthRefreshV2Enabled(ctx, actorId) && s.isNetWorthRefreshRequired(instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary()) {
		bgColor, textColor, leftImage := ColorSupportingCherry50, helper.ColorSupportingCherry700, LinkBrokenRedImage
		if s.isOnlyManualAssetForRefresh(instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary()) {
			bgColor, textColor, leftImage = colors.ColorOnLightHighEmphasis, colors.ColorSupportingAmber100, LinkBrokenYellowImage
		}
		netWorthRefreshBottomSheetScreenDeeplink, deeplinkErr := s.deeplinkBuilder.NetWorthRefreshBottomSheetScreen(ctx, instrumentsRefreshDetailsRes.GetInstrumentRefreshSummary(), actorId, dashboardType)
		if deeplinkErr != nil {
			return nil, nil, nil, nil, fmt.Errorf("failed to get net worth refresh bottom sheet screen deeplink: %w", deeplinkErr)
		}
		refreshBannerText := NetWorthOutdated
		if dashboardType == enums.NetWorthDashBoardType_ASSETS {
			refreshBannerText = AssetsOutdatedMsg
		}
		refreshBanner = ui.NewITC().WithTexts(commontypes.GetPlainStringText(refreshBannerText).WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(textColor)).
			WithContainerBackgroundColor(bgColor).WithContainerCornerRadius(16).
			WithLeftImageUrlHeightAndWidth(leftImage, 28, 28).WithLeftImagePadding(8).
			WithDeeplink(netWorthRefreshBottomSheetScreenDeeplink)
	} else {
		// TODO: remove this after testing in qa
		secretFooterV2, releaseErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_MONEY_SECRET_FOOTER_V2).WithActorId(actorId))
		if releaseErr != nil {
			logger.Error(ctx, "error while evaluating if actor is eligible for secret footer V2", zap.Error(releaseErr), zap.String(logger.ACTOR_ID_V2, actorId))
		}
		if secretFooterV2 {
			secretAnalyserDeeplink := secrets2.GetSecretAnalyserScreenDeeplinkWithLottie("TopPerformingMfScheme", "MfSecretsStoryLineCollection", "https://epifi-icons.pointz.in/insights/secrets/mf_report_card_ready_lottie_6s.json")
			bgColor, textColor, leftImage := colors.ColorOnLightHighEmphasis, colors.ColorSupportingAmber100, LinkBrokenYellowImage
			refreshBanner = ui.NewITC().WithTexts(commontypes.GetPlainStringText("Your mutual funds story is here").WithFontStyle(commontypes.FontStyle_SUBTITLE_S).WithFontColor(textColor)).
				WithContainerBackgroundColor(bgColor).WithContainerCornerRadius(16).
				WithLeftImageUrlHeightAndWidth(leftImage, 28, 28).WithLeftImagePadding(8).
				WithDeeplink(secretAnalyserDeeplink)
		}
	}

	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		generatedSection.AddMoreWidgets.Title = ""
		dl := &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_NET_WORTH_ADD_ASSETS_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&networth.NetworthAddAsetsScreenOptions{
				Header:      nil,
				NavBarTitle: nil,
				PageTitle: &commontypes.Text{
					FontColor: colors.ColorOnDarkHighEmphasis,
					DisplayValue: &commontypes.Text_PlainString{
						PlainString: "Add assets and track all in one place",
					},
					FontStyle: &commontypes.Text_StandardFontStyle{
						StandardFontStyle: commontypes.FontStyle_SUBTITLE_2,
					},
				},
				AddMoreWidgets: generatedSection.GetAddMoreWidgets(),
			}),
		}
		addAssetsCTA = ui.NewITC().
			WithTexts(commontypes.GetPlainStringText("Connect your assets").
				WithFontStyle(commontypes.FontStyle_BODY_3).
				WithFontColor(colors.ColorSnow)).
			WithLeftVisualElementUrlHeightAndWidth(addAssetsImage, 24, 24).
			WithLeftImagePadding(8).
			WithContainerBackgroundColor(colors.ColorLightPrimaryAction).
			WithContainerPadding(10, 16, 10, 16).
			WithContainerCornerRadius(20).WithDeeplink(dl)
	}

	return visualisation, networthSections, refreshBanner, addAssetsCTA, nil
}

// For d2h, the money secret is enabled for all users with an eligible app version.
// For core users, the money secret is enabled only if the user is eligible for release.
func (s *Service) isMoneySecretEnable(ctx context.Context, header *header.RequestHeader) (bool, error) {
	platform := header.GetAuth().GetDevice().GetPlatform()
	appversion := header.GetAuth().GetDevice().GetAppVersion()
	appVersionConfig := s.feConfig.FeatureReleaseConfig().FeatureConstraints().Get(types.Feature_FEATURE_MONEY_SECRET_HOME_SCREEN.String()).AppVersionConstraintConfig()
	if (platform == commontypes.Platform_ANDROID && int(appversion) < appVersionConfig.MinAndroidVersion()) || (platform == commontypes.Platform_IOS && int(appversion) < appVersionConfig.MinIOSVersion()) {
		return false, nil
	}
	actorId := header.GetAuth().GetActorId()
	if isLiteUser, errLite := s.IsFiLiteUser(ctx, actorId); errLite == nil && isLiteUser {
		return true, nil
	}
	isReleased, err := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_MONEY_SECRET_HOME_SCREEN).WithActorId(actorId))
	if err != nil {
		return false, fmt.Errorf("failed to evaluate if actor is eligible for NetWorth Dashboard Secrets: %w", err)
	}
	return isReleased, nil
}

func (s *Service) getNetworthDashboardMoneySecrets(ctx context.Context, header *header.RequestHeader, dashboardType enums.NetWorthDashBoardType) (*networthFePb.SecretsSection, error) {
	if dashboardType == enums.NetWorthDashBoardType_ASSETS {
		return nil, nil
	}
	isMoneySecretEnabled, err := s.isMoneySecretEnable(ctx, header)
	if err != nil {
		return nil, nil
	}
	if !isMoneySecretEnabled {
		return nil, nil
	}
	moneySecretsResp, err := s.moneySecretsFeClient.GetSecretSummaries(ctx, &secretsFePb.GetSecretSummariesRequest{
		Req:        header,
		Entrypoint: secretsEnumsPb.SecretSummariesEntrypoint_SECRETS_SUMMARIES_ENTRYPOINT_NETWORTH_DASHBOARD.String(),
	})
	if err = epifigrpc.RPCError(moneySecretsResp.GetRespHeader(), err); err != nil {
		return nil, fmt.Errorf("failed to get secret summaries: %w", err)
	}
	return &networthFePb.SecretsSection{
		Title:    typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Your Net worth Secrets ✨", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_M)),
		Subtitle: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("Uncover insights to grow your money", colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_BODY_XS)),
		Cta: typesUiPb.NewITC().WithTexts(commontypes.GetTextFromStringFontColourFontStyle("SEE ALL", colors.ColorLightPrimaryAction, commontypes.FontStyle_SUBTITLE_XS)).
			WithRightImageUrlHeightAndWidth(rightGreenChevron, 20, 20).
			WithDeeplink(&deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_SECRET_ANALYSER_LIBRARY_SCREEN}),
		SecretSummaries: moneySecretsResp.GetSecretSummaries(),
	}, nil
}

func (s *Service) updateCategoryStatusMap(
	categoriesStatusMap map[networthFePb.NetworthCategory]networthFePb.NetworthCategoryStatus,
	netWorthCategoryToRefreshDetailsMap map[networthFePb.NetworthCategory]*networthBePb.InstrumentRefreshDetails) (map[networthFePb.NetworthCategory]networthFePb.NetworthCategoryStatus, error) {

	for category, refreshDetailsMap := range netWorthCategoryToRefreshDetailsMap {

		// if refresh is in process & category status is uninitialized it means instrument is in process for the first time
		if refreshDetailsMap.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_IN_PROCESS {
			status, ok := categoriesStatusMap[category]
			if !ok {
				return nil, fmt.Errorf("no value found for category: %v in categoriesStatusMap", category)
			} else if status == networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_UNINITIALIZED {
				categoriesStatusMap[category] = networthFePb.NetworthCategoryStatus_NETWORTH_CATEGORY_STATUS_NOT_APPLICABLE
			}
		}
	}
	return categoriesStatusMap, nil
}

func (s *Service) isNetWorthRefreshV2Enabled(ctx context.Context, actorId string) bool {
	isNetWorthRefreshV2Enabled, releaseErr := s.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_NETWORTH_REFRESH_V2).WithActorId(actorId))
	if releaseErr != nil {
		logger.Error(ctx, "error while evaluating if actor is eligible for NetWorth Refresh V2", zap.Error(releaseErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return false
	}
	return isNetWorthRefreshV2Enabled
}

func (s *Service) isNetWorthRefreshRequired(instrumentsRefreshDetailsRes []*networthBePb.InstrumentRefreshDetails) bool {
	for _, refreshDetails := range instrumentsRefreshDetailsRes {
		if refreshDetails.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_REQUIRED {
			return true
		}
	}
	return false
}

// isOnlyManualAssetForRefresh returns true only if there is only manual assets to refresh in refresh flow, else false
func (s *Service) isOnlyManualAssetForRefresh(instrumentsRefreshDetails []*networthBePb.InstrumentRefreshDetails) bool {
	for _, refreshDetails := range instrumentsRefreshDetails {
		switch refreshDetails.GetAssetName() {
		case modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
			modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
			modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF:
			if refreshDetails.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_REQUIRED {
				return false
			}
		case modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS:
			if refreshDetails.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_REQUIRED {
				return true
			}
		}
	}
	return false
}

// isRefreshRequiredAndCritical returns true if any asset with otp flow is required for refresh
// e.g. EPF, Mutual Funds, etc. require otp for refresh, so they are critical for refresh, returns true
// whereas if there is only manual asset which is non otp flow, so it is less intense flow, returns false
func (s *Service) isRefreshRequiredAndCritical(instrumentsRefreshDetails []*networthBePb.InstrumentRefreshDetails) bool {
	for _, refreshDetails := range instrumentsRefreshDetails {
		switch refreshDetails.GetAssetName() {
		case modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_CREDIT_SCORE,
			modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS,
			modelPb.NetWorthRefreshAsset_NET_WORTH_REFRESH_ASSET_EPF:
			if refreshDetails.GetRefreshStatus() == networthBePb.RefreshStatus_REFRESH_STATUS_REQUIRED {
				return true
			}
		}
	}
	return false
}

func (s *Service) getErrResponse(errStr string) *networthFePb.GetNetWorthDashboardResponse {
	return &networthFePb.GetNetWorthDashboardResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusInternalWithDebugMsg(errStr),
			// add error view
			ErrorView: &feErrors.ErrorView{
				Type: feErrors.ErrorViewType_FULL_SCREEN,
				Options: &feErrors.ErrorView_FullScreenErrorView{
					FullScreenErrorView: &feErrors.FullScreenErrorView{
						Title:    "Something went wrong",
						Subtitle: "We were unable to load your island. Please try again in some time.",
						Ctas: []*feErrors.CTA{
							{
								Text:         "Ok, got it",
								Action:       networthDeeplink.HomeDeeplink(),
								DisplayTheme: feErrors.CTA_PRIMARY,
							},
						},
						ImageUrl: "https://epifi-icons.pointz.in/networth/thundercloud.png",
					},
				},
			},
		},
	}
}

// IsFiLiteUser returns if the actor id passed belongs to a fi lite user
func (s *Service) IsFiLiteUser(ctx context.Context, actorId string) (bool, error) {
	var getFeatResp, errGetFeat = s.onbClient.GetFeatureDetails(ctx, &onboarding.GetFeatureDetailsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(getFeatResp, errGetFeat); grpcErr != nil {
		return false, grpcErr
	}
	return getFeatResp.GetIsFiLiteUser(), nil
}
