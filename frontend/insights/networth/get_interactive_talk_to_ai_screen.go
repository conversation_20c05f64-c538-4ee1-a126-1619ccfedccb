// nolint: unused

package networth

import (
	"context"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"

	rpcPb "github.com/epifi/be-common/api/rpc"
	commonPb "github.com/epifi/be-common/api/typesv2/common"
	widgetUiPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	networthFePb "github.com/epifi/gamma/api/frontend/insights/networth"
	uiPb "github.com/epifi/gamma/api/frontend/insights/networth/ui"
	"github.com/epifi/gamma/api/insights/networth/enums"
	interactiveTalkToAiOptions "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	uiCommonPb "github.com/epifi/gamma/api/typesv2/ui"
)

var (
	chatgptIcon = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/Chatgpt.png"
	claudeIcon  = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/claude.png"
	geminiIcon  = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/gemini.png"

	fileIcon         = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/file_icon.png"
	chatIcon         = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/chat_icon.png"
	othersIcon       = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/others.png"
	sparkleIcon      = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/sparkle.png"
	copyIcon         = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/copy.png"
	closeIcon        = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/close_icon.png"
	arrowUpRightIcon = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/arrow_up_right.png"
	bottomSheetBg    = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/bottom_sheet_bg_2.png"
	greenDownChevron = "https://epifi-icons.pointz.in/networth/down_green_chevron.png"
	introAnimation   = "https://epifi-icons.pointz.in/networth/interactive_talk_to_ai/intro_animation.json"

	// TODO: move this to config
	wbEntrypointSubTitle           = "What do you want to know about your %s?"
	wbEntrypointSuggestedQuestions = []string{
		"Why is my net worth lower/higher than last month?",
		"Is my net worth beating inflation?",
		"How much has my net worth changed in the last year?",
		"In how much time will I reach 1 crore?",
		"Net worth trends in the last 1 year?",
		"Do I have an adequate emergency fund? If not, how much and where to park it.",
	}
	dailyTrackerEntrypointSuggestedQuestions = []string{
		"Which stock positions should I monitor tomorrow and why?",
		"Which stock had the worst 1-day performance vs its sector?",
		"If I had to raise ₹X today, which holdings are easiest to liquidate with minimal loss?",
	}
	weeklyTrackerEntrypointSuggestedQuestions = []string{
		"Why is my net worth lower/higher than last week?",
		"Which mutual funds lagged the benchmark this week and by how much?",
		"What are the top 3 wins and top 3 risks in my portfolio this week?",
	}
	mfReportEntrypointSuggestedQuestions = []string{
		"Which of my mutual funds are underperforming?",
		"Are any funds overlapping heavily in holdings? Suggest consolidation options.",
		"How much of my MF corpus is in small/mid/large cap? Is it balanced?",
		"Are there any exit loads or switch penalties I should be aware of before changes?",
		"Suggest a low-effort reallocation to reduce overlap and improve diversification",
	}
	indianStocksEntrypointSuggestedQuestions = []string{
		"Should I rebalance my stock portfolio now? If yes, outline the trades.",
		"If I want to reduce risk by 20%, which holdings should I trim?",
		"Identify multi-baggers from my portfolio which I should double down on",
	}
	bankAccEntrypointSuggestedQuestions = []string{
		"How have my spendings changed in the past 6 months?",
		"Can you build a subscription tracker for me?",
		"How do my spendings compare to other users?",
		"How much cash is idle across my bank accounts? Suggest where to move it.",
		"Recommend 3 short-term parking options for idle cash (risk/return tradeoffs).",
	}
	epfEntrypointSuggestedQuestions = []string{
		"What is the year on year growth rate for my EPF corpus?",
		"How much total interest have I earned on my EPF?",
		"How much corpus will I have at the time of retirement?",
		"For what months have the contributions been delayed?",
	}

	// default values for entrypoint
	defaultEntrypointSubTitle           = "What do you want to know about your money?"
	defaultEntrypointSuggestedQuestions = []string{
		"How is my portfolio performing?",
		"Which funds should I invest in?",
		"How can I diversify my investments?",
		"What's my asset allocation?",
		"How is my overall portfolio performing?",
		"What's my net worth breakdown?",
	}

	disclaimerText = "*Disclaimer: Fi MCP enables secure, encrypted transmission of your selected financial data to an AI app of your choice. Fi does not own, operate, or control these AI tools and is not responsible for their outputs or actions. All interactions with the AI tool are private and cannot be accessed or stored by Fi. Use of this service is entirely at your own discretion and risk. Fi assumes no liability once data is transmitted as per your instructions. Terms and Conditions apply."

	aiAppsInfos = []struct {
		name               string
		imgUrl             string
		androidPackageName string
		iosScheme          string
		iosAppstoreId      string
	}{
		{
			name:               "ChatGPT",
			imgUrl:             chatgptIcon,
			androidPackageName: "com.openai.chatgpt",
			iosScheme:          "chatgpt://",
			iosAppstoreId:      "id6448311069",
		},
		{
			name:               "Gemini",
			imgUrl:             geminiIcon,
			androidPackageName: "com.google.android.apps.bard",
			iosScheme:          "gemini://",
			iosAppstoreId:      "id6473141398",
		},
		{
			name:               "Claude",
			imgUrl:             claudeIcon,
			androidPackageName: "com.anthropic.claude",
			iosScheme:          "claude://",
			iosAppstoreId:      "id6473753684",
		},
	}

	fileItemsInfo = []struct {
		fileTypeEnum string
		fileName     string
		selectedFor  []interactiveTalkToAiOptions.Entrypoint
	}{
		{
			fileTypeEnum: enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_NET_WORTH_VALUES.String(),
			fileName:     "All holdings",
			// select by default and wb dashboard too
			selectedFor: []interactiveTalkToAiOptions.Entrypoint{
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_UNSPECIFIED,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WB_DASHBOARD,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_EPF_REPORT_PAGE,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_BANK_ACCOUNT_PAGE,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_INDIAN_STOCKS_DASHBOARD,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_ASSET_LANDING,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_MF_REPORT_PAGE,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_DAILY_PORTFOLIO_TRACKER,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER,
			},
		},
		{
			fileTypeEnum: enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_CREDIT_REPORT.String(),
			fileName:     "Credit Report",
			selectedFor: []interactiveTalkToAiOptions.Entrypoint{
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_UNSPECIFIED,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WB_DASHBOARD,
			},
		},
		{
			fileTypeEnum: enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_EPF_DETAILS.String(),
			fileName:     "EPF Details",
			selectedFor: []interactiveTalkToAiOptions.Entrypoint{
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_EPF_REPORT_PAGE,
			},
		},
		{
			fileTypeEnum: enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_MF_TRANSACTIONS.String(),
			fileName:     "Mutual Fund orders",
			selectedFor: []interactiveTalkToAiOptions.Entrypoint{
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_DAILY_PORTFOLIO_TRACKER,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER,
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_MF_REPORT_PAGE,
			},
		},
		{
			fileTypeEnum: enums.NetworthDataFileType_NETWORTH_DATA_FILE_TYPE_BANK_DETAILS_DATA.String(),
			fileName:     "Bank Transactions",
			selectedFor: []interactiveTalkToAiOptions.Entrypoint{
				interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_BANK_ACCOUNT_PAGE,
			},
		},
	}
)

// GetInteractiveTalkToAiScreen returns UI data for interactive talk to AI deeplink screen
// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-FFF--Wealth-Builder?node-id=17842-23805&t=PaH3BTqHoTjgUPSp-0
func (s *Service) GetInteractiveTalkToAiScreen(ctx context.Context, req *networthFePb.GetInteractiveTalkToAiScreenRequest) (*networthFePb.GetInteractiveTalkToAiScreenResponse, error) {
	// Build the interactive talk to AI component
	interactiveTalkToAiComponent, err := s.buildInteractiveTalkToAiComponent(ctx, req.GetEntrypoint())
	if err != nil {
		logger.Error(ctx, "failed to build interactive talk to AI component", zap.Error(err))
		return &networthFePb.GetInteractiveTalkToAiScreenResponse{
			RespHeader: &header.ResponseHeader{
				Status: rpcPb.StatusInternalWithDebugMsg("failed to build interactive talk to AI component"),
			},
		}, nil
	}

	return &networthFePb.GetInteractiveTalkToAiScreenResponse{
		RespHeader: &header.ResponseHeader{
			Status: rpcPb.StatusOk(),
		},
		InteractiveTalkToAiComponent: interactiveTalkToAiComponent,
	}, nil
}

// nolint: unparam
func (s *Service) buildInteractiveTalkToAiComponent(ctx context.Context, entrypoint string) (*uiPb.InteractiveTalkToAiComponent, error) {
	// Parse entrypoint to determine content customization
	var entrypointEnum interactiveTalkToAiOptions.Entrypoint
	if val, ok := interactiveTalkToAiOptions.Entrypoint_value[entrypoint]; ok {
		entrypointEnum = interactiveTalkToAiOptions.Entrypoint(val)
	} else {
		entrypointEnum = interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_UNSPECIFIED
	}
	// Close icon - using a simple structure
	closeIconComponent := commonPb.GetVisualElementFromUrlHeightAndWidth(closeIcon, 32, 32)

	// Customize content based on entrypoint
	var subtitle string
	var suggestedQuestions []string

	switch entrypointEnum {
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WB_DASHBOARD:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "Money")
		suggestedQuestions = wbEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_DAILY_PORTFOLIO_TRACKER:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "Money")
		suggestedQuestions = dailyTrackerEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_WEEKLY_PORTFOLIO_TRACKER:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "Money")
		suggestedQuestions = weeklyTrackerEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_MF_REPORT_PAGE:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "Mutual Funds")
		suggestedQuestions = mfReportEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_EPF_REPORT_PAGE:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "EPF")
		suggestedQuestions = epfEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_INDIAN_STOCKS_DASHBOARD:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "stocks")
		suggestedQuestions = indianStocksEntrypointSuggestedQuestions
	case interactiveTalkToAiOptions.Entrypoint_ENTRYPOINT_BANK_ACCOUNT_PAGE:
		subtitle = fmt.Sprintf(wbEntrypointSubTitle, "Bank Accounts")
		suggestedQuestions = bankAccEntrypointSuggestedQuestions
	default:
		subtitle = defaultEntrypointSubTitle
		suggestedQuestions = defaultEntrypointSuggestedQuestions
	}

	// Convert suggested questions to UI components
	var suggestedQuestionComponents []*uiCommonPb.IconTextComponent
	for _, question := range suggestedQuestions {
		suggestedQuestionComponent := uiCommonPb.NewITC().
			WithContainerBackgroundColor("#80FFFFFF").
			WithContainerCornerRadius(40).
			WithContainerPadding(8, 12, 8, 12).
			WithBorder("#FFFFFF", 1).
			WithLeftVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(sparkleIcon, 24, 24)).
			WithRightVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(copyIcon, 20, 20)).
			WithTexts(commonPb.GetTextFromStringFontColourFontStyle(question, "#6A6D70", commonPb.FontStyle_SUBTITLE_S))
		suggestedQuestionComponents = append(suggestedQuestionComponents, suggestedQuestionComponent)
	}

	// Header section
	headerSection := &uiPb.InteractiveTalkToAiComponent_HeaderSection{
		Title: uiCommonPb.NewITC().
			WithLeftVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(chatIcon, 20, 20)).
			WithTexts(commonPb.GetTextFromStringFontColourFontStyle("Talk to AI", "#007A56", commonPb.FontStyle_SUBTITLE_S)),
		Subtitle:           commonPb.GetTextFromStringFontColourFontStyle(subtitle, "#313234", commonPb.FontStyle_DISPLAY_XL),
		SuggestedQuestions: suggestedQuestionComponents,
	}

	// Choose an app text
	chooseAnApp := commonPb.GetTextFromStringFontColourFontStyle("Pick an AI app & ask about", "#313234", commonPb.FontStyle_SUBTITLE_S)

	selectedFilesText := uiCommonPb.NewITC().
		WithLeftVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(fileIcon, 20, 20)).
		WithRightVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(greenDownChevron, 20, 20)).
		WithTexts(commonPb.GetTextFromStringFontColourFontStyleFontAlignment(getSelectedFilesText(entrypointEnum), colors.ColorForest, commonPb.FontStyle_SUBTITLE_S, commonPb.Text_ALIGNMENT_LEFT))

	// Dropdown section with file items
	fileItems := make([]*uiPb.InteractiveTalkToAiComponent_FileItem, 0)
	for _, fileItemInfo := range fileItemsInfo {
		fileItems = append(fileItems, &uiPb.InteractiveTalkToAiComponent_FileItem{
			FileComponent: uiCommonPb.NewITC().
				WithLeftVisualElement(commonPb.GetVisualElementFromUrlHeightAndWidth(fileIcon, 20, 20)).
				WithTexts(commonPb.GetTextFromStringFontColourFontStyle(fileItemInfo.fileName, "#313234", commonPb.FontStyle_SUBTITLE_S)),
			NetWorthDataFileType: fileItemInfo.fileTypeEnum,
			IsSelected:           commonPb.BoolToBooleanEnum(lo.Contains(fileItemInfo.selectedFor, entrypointEnum)),
		})
	}
	dropdownSection := &uiPb.InteractiveTalkToAiComponent_DropdownSection{
		Title:                              commonPb.GetTextFromStringFontColourFontStyle("Select files to export to AI", "#313234", commonPb.FontStyle_HEADLINE_L),
		Subtitle:                           commonPb.GetTextFromStringFontColourFontStyle("For best results, Select the relevant file for specific topics and use ‘All assets’ for general questions", "#929599", commonPb.FontStyle_BODY_XS),
		FileItems:                          fileItems,
		BgColour:                           widgetUiPb.GetBlockBackgroundColour("#FFFFFF"),
		SelectAtLeastOneFileWarningMessage: commonPb.GetTextFromStringFontColourFontStyle("**Select at least one file to proceed", "#AA301F", commonPb.FontStyle_SUBTITLE_2XS),
		CloseCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CANCEL,
			DisplayTheme: deeplinkPb.Cta_SECONDARY,
			CtaText:      commonPb.GetTextFromHtmlStringFontColourFontStyle("Close", colors.ColorForest, commonPb.FontStyle_BUTTON_M),
			CtaStyle: &deeplinkPb.Cta_CustomStyles{
				BgColor: widgetUiPb.GetBlockBackgroundColour("#F6F9FD"),
			},
		},
		ConfirmCta: &deeplinkPb.Cta{
			Type:         deeplinkPb.Cta_CONTINUE,
			DisplayTheme: deeplinkPb.Cta_PRIMARY,
			CtaText:      commonPb.GetTextFromHtmlStringFontColourFontStyle("Confirm", colors.ColorSnow, commonPb.FontStyle_BUTTON_M),
		},
	}

	// AI options
	aiOptions := make([]*uiPb.InteractiveTalkToAiComponent_AiOption, 0)
	for idx, aiAppInfo := range aiAppsInfos {
		isSelected := false
		if idx == 0 {
			isSelected = true
		}
		aiOptions = append(aiOptions, &uiPb.InteractiveTalkToAiComponent_AiOption{
			Icon:       commonPb.GetVisualElementFromUrlHeightAndWidth(aiAppInfo.imgUrl, 56, 56),
			Name:       commonPb.GetTextFromStringFontColourFontStyle(aiAppInfo.name, "#313234", commonPb.FontStyle_SUBTITLE_S),
			IsSelected: isSelected,
			AndroidAiAppIdentifier: &uiPb.InteractiveTalkToAiComponent_AiOption_AndroidAiAppIdentifier{
				AndroidPackageName: aiAppInfo.androidPackageName,
			},
			IosAiAppIdentifier: &uiPb.InteractiveTalkToAiComponent_AiOption_IosAiAppIdentifier{
				Scheme:     aiAppInfo.iosScheme,
				AppStoreId: aiAppInfo.iosAppstoreId,
			},
		})
	}
	// append others at the end
	aiOptions = append(aiOptions, &uiPb.InteractiveTalkToAiComponent_AiOption{
		Icon:       commonPb.GetVisualElementFromUrlHeightAndWidth(othersIcon, 56, 56),
		Name:       commonPb.GetTextFromStringFontColourFontStyle("Others", "#313234", commonPb.FontStyle_SUBTITLE_S),
		IsSelected: false,
	})

	// Export CTA button
	appPlatform, _ := epificontext.AppPlatformAndVersion(ctx)
	ctaText := "Export & Ask"
	// Intro section - Lottie animation for first time users
	var introSection *commonPb.VisualElement
	introSection = commonPb.GetVisualElementLottieFromUrlHeightAndWidth(introAnimation, 200, 0).WithRepeatCount(1)
	// On Android we are managing this with aspect ratio, hence width and height are required.
	// But, on iOS the handling is little different
	if appPlatform == commonPb.Platform_ANDROID {
		ctaText += " $app_name$"
		introSection = commonPb.GetVisualElementLottieFromUrlHeightAndWidth(introAnimation, 200, 412).WithRepeatCount(1)
	}
	exportCta := &deeplinkPb.Cta{
		Type:               deeplinkPb.Cta_CONTINUE,
		Deeplink:           nil,
		DisplayTheme:       deeplinkPb.Cta_PRIMARY,
		Status:             deeplinkPb.Cta_CTA_STATUS_ENABLED,
		CtaTrailingImage:   commonPb.GetVisualElementFromUrlHeightAndWidth(arrowUpRightIcon, 24, 24),
		RightImgTxtPadding: 8,
		CtaText:            commonPb.GetTextFromStringFontColourFontStyle(ctaText, "#FFFFFF", commonPb.FontStyle_BUTTON_M),
	}

	// Disclaimer text
	disclaimerTextComponent := commonPb.GetTextFromStringFontColourFontStyleFontAlignment(disclaimerText, colors.ColorMonochromeAsh, commonPb.FontStyle_SUBTITLE_2XS, commonPb.Text_ALIGNMENT_CENTER)

	// Background image
	bgImage := commonPb.GetVisualElementImageFromUrl(bottomSheetBg)

	// Nested bottom sheet background color
	nestedBottomSheetBgColour := widgetUiPb.GetBlockBackgroundColour("#FFFFFF")

	return &uiPb.InteractiveTalkToAiComponent{
		CloseIcon:                 closeIconComponent,
		IntroSection:              introSection,
		HeaderSection:             headerSection,
		ChooseAnApp:               chooseAnApp,
		DropdownSection:           dropdownSection,
		AiOptions:                 aiOptions,
		ExportCta:                 exportCta,
		DisclaimerText:            disclaimerTextComponent,
		BgImage:                   bgImage,
		NestedBottomSheetBgColour: nestedBottomSheetBgColour,
		SelectedFilesText:         selectedFilesText,
	}, nil
}

func getSelectedFilesText(entrypoint interactiveTalkToAiOptions.Entrypoint) string {
	selectedFiles := make([]string, 0)
	for _, fileItemInfo := range fileItemsInfo {
		if lo.Contains(fileItemInfo.selectedFor, entrypoint) {
			selectedFiles = append(selectedFiles, fileItemInfo.fileName)
		}
	}
	if len(selectedFiles) == 0 {
		return ""
	}
	if len(selectedFiles) == 1 {
		return selectedFiles[0]
	}
	return fmt.Sprintf("%s + %d", selectedFiles[0], len(selectedFiles)-1)
}
