package dashboard

import (
	"context"
	"fmt"

	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth"
	"github.com/epifi/gamma/pkg/deeplinkv3"
	"github.com/epifi/gamma/pkg/feature/release"

	moneyPb "google.golang.org/genproto/googleapis/type/money"

	"github.com/epifi/be-common/pkg/errgroup"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	uiWidget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/be-common/pkg/colors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/frontend/app/apputils"
	"github.com/epifi/be-common/pkg/money"

	beCaPb "github.com/epifi/gamma/api/connected_account"
	connectedAccountEnumsPb "github.com/epifi/gamma/api/connected_account/enums"
	beCaExtPb "github.com/epifi/gamma/api/connected_account/external"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	feHomePb "github.com/epifi/gamma/api/frontend/home"
	homeOrchFePb "github.com/epifi/gamma/api/frontend/home/<USER>"
	feNetworthPb "github.com/epifi/gamma/api/frontend/insights/networth"
	networthBePb "github.com/epifi/gamma/api/insights/networth"
	"github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	insightsDeeplinkPb "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/deeplink_builder"
	homeTypesPb "github.com/epifi/gamma/api/typesv2/home"
	"github.com/epifi/gamma/api/typesv2/ui"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/home/<USER>"
)

type NetWorthDashboardComponentViewGetter struct {
	connectedAccountClient beCaPb.ConnectedAccountClient
	feConfig               *genconf.Config
	deeplinkBuilder        deeplink_builder.IDeeplinkBuilder
	onbClient              onboarding.OnboardingClient
	networthClient         networthBePb.NetWorthClient
	releaseEvaluator       release.IEvaluator
}

func NewNetWorthDashboardComponentViewGetter(connectedAccountClient beCaPb.ConnectedAccountClient,
	feConfig *genconf.Config,
	deeplinkBuilder deeplink_builder.IDeeplinkBuilder,
	onbClient onboarding.OnboardingClient,
	networthClient networthBePb.NetWorthClient,
	releaseEvaluator release.IEvaluator,
) *NetWorthDashboardComponentViewGetter {
	return &NetWorthDashboardComponentViewGetter{
		connectedAccountClient: connectedAccountClient,
		feConfig:               feConfig,
		deeplinkBuilder:        deeplinkBuilder,
		onbClient:              onbClient,
		networthClient:         networthClient,
		releaseEvaluator:       releaseEvaluator,
	}
}

type Aggregate struct {
	AggregateType feNetworthPb.NetworthAggregateType
	Value         *moneyPb.Money
}

const (
	fiLiteAddAccountsIcon    = "https://epifi-icons.pointz.in/home-v2/plusAddAccountsDashboardIconNonZeroState.png"
	fiLiteFooterRightGreen   = "https://epifi-icons.pointz.in/onboarding/lite/chevron_right_green.png"
	homeZeroStateImageUrl    = "https://epifi-icons.pointz.in/home-v2/PieChart.png"
	fiLiteAddInvestmentsIcon = "https://epifi-icons.pointz.in/home-v2/plusAddInvestmentsDashboardIconNonZeroState.png"
	fiLiteViewDetailsIcon    = "https://epifi-icons.pointz.in/home-v2/viewDetailsDashboardIconNonZeroState.png"
	talkToAiIcon             = "https://epifi-icons.pointz.in/home-v2/talk_to_ai2.png"

	wealthTitleString                          = "Your Wealth"
	connectAccountsString                      = "Connect accounts"
	dashboardIconsBgColourZeroStateDashboardV2 = colors.ColorGreyV2
	connectAccountsFooterString                = "You haven't connected any accounts"
	moneyPrecision                             = 2
	rupeeSymbol                                = "₹"
	zero                                       = "0"
	fiLiteDashboardOverlayId                   = "2d417a8c-03b4-4c10-b53e-c7d9461b63b3"
	fiLiteDashboardOverlayString               = "Ready to view your finances in one place?"
	fiLiteDashboardOverlayCtaString            = "Reveal now"
	trackNetWorthString                        = "Track Net Worth"
	fiLiteAddAccountsString                    = "Add accounts"
	fiLiteAddInvestmentsString                 = "Add assets"
	fiLiteViewDetailsString                    = "View details"
	fiLiteFooterPrivacyString                  = "••"
	fiLiteCardCenterString                     = "View all your assets\n& loans in one place"
	fiLiteMoneyPrecision                       = 1
)

var (
	wealthTitle = commontypes.GetTextFromStringFontColourFontStyle(wealthTitleString, colors.ColorSnow, commontypes.FontStyle_SUBTITLE_3)

	fiLiteFooterTextMap = map[string]*commontypes.Text{
		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_SAVINGS.String(): commontypes.GetTextFromStringFontColourFontStyle("A/C BALANCE", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),

		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_INVESTMENTS.String(): commontypes.GetTextFromStringFontColourFontStyle("INVESTMENTS ", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),

		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_ASSETS.String(): commontypes.GetTextFromStringFontColourFontStyle("ASSETS", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_OVERLINE_XS_CAPS),
	}

	footerTextMap = map[string]*commontypes.Text{
		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_SAVINGS.String(): commontypes.GetTextFromStringFontColourFontStyle("Savings accounts ", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_3),

		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_ASSETS.String(): commontypes.GetTextFromStringFontColourFontStyle("Assets ", colors.ColorOnDarkLowEmphasis, commontypes.FontStyle_SUBTITLE_3),

		feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_LIABILITIES.String(): commontypes.GetTextFromStringFontColourFontStyle("Loans & Liabilities ", colors.ColorOnDarkDisabled700, commontypes.FontStyle_SUBTITLE_3),
	}
	privacyToggleIcon = &feHomePb.Icon{
		VisualElement:        commontypes.GetVisualElementImageFromUrl(hideInfoIcon),
		IconImage:            commontypes.GetImageFromUrl(hideInfoIcon),
		IconImageOnSelection: commontypes.GetImageFromUrl(showInfoIcon),
		IconType:             homeTypesPb.IconType_DASHBOARD_SETTING,
		ActionType:           feHomePb.Icon_ACTION_TYPE_TOGGLE_PRIVACY,
		BgColour:             ui.GetBlockColor(dashboardIconsBgColorHideShowInfo),
	}
	addAccountsIcon = &feHomePb.Icon{
		VisualElement: commontypes.GetVisualElementImageFromUrl(fiLiteAddAccountsIcon),
		IconImage:     commontypes.GetImageFromUrl(fiLiteAddAccountsIcon),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: fiLiteAddAccountsString}, FontColor: colors.ColorLightPrimaryAction, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S}},
		IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action:        &feHomePb.Icon_Deeplink{Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW}},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: colors.ColorGreyV2}},
	}
	addInvestmentsIcon = &feHomePb.Icon{
		VisualElement: commontypes.GetVisualElementImageFromUrl(fiLiteAddInvestmentsIcon),
		IconImage:     commontypes.GetImageFromUrl(fiLiteAddInvestmentsIcon),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: fiLiteAddInvestmentsString}, FontColor: colors.ColorLightPrimaryAction, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S}},
		IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action:        &feHomePb.Icon_Deeplink{Deeplink: insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError()},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: colors.ColorGreyV2}},
	}
	viewDetailsIcon = &feHomePb.Icon{
		VisualElement: commontypes.GetVisualElementImageFromUrl(fiLiteViewDetailsIcon),
		IconImage:     commontypes.GetImageFromUrl(fiLiteViewDetailsIcon),
		Title:         &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: fiLiteViewDetailsString}, FontColor: colors.ColorLightPrimaryAction, FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S}},
		IconType:      homeTypesPb.IconType_DASHBOARD_ACTION,
		ActionType:    feHomePb.Icon_ACTION_TYPE_DEEPLINK,
		Action:        &feHomePb.Icon_Deeplink{Deeplink: insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError()},
		BgColour:      &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: colors.ColorGreyV2}},
	}
)

func (n *NetWorthDashboardComponentViewGetter) GetWealthDashboardComponentView(ctx context.Context, actorId string) (*homeOrchFePb.DashboardComponent_DashboardView, error) {
	var (
		netWorthSummary *networthBePb.GetNetWorthValueResponse
		onbDetails      *onboarding.GetDetailsResponse
	)

	eg, gctx := errgroup.WithContext(ctx)
	eg.Go(func() error {
		var err error
		netWorthSummary, err = n.networthClient.GetNetWorthValue(gctx, &networthBePb.GetNetWorthValueRequest{
			ActorId: actorId,
		})
		if err = epifigrpc.RPCError(netWorthSummary, err); err != nil {
			return fmt.Errorf("error while fetching net worth summary: %v", err)
		}
		return nil
	})
	eg.Go(func() error {
		var err error
		onbDetails, err = n.onbClient.GetDetails(gctx, &onboarding.GetDetailsRequest{ActorId: actorId})
		if err = epifigrpc.RPCError(onbDetails, err); err != nil {
			return fmt.Errorf("failed to get details from onboarding:  %v", err)
		}
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, fmt.Errorf("error while fetching net worth summary and refresh details: %v", err)
	}

	isWealthAnalyserUser := insightsDeeplinkPb.IsWealthAnalyserUser(onbDetails.GetDetails().GetFeatureDetails().GetFeatureInfo())
	networthHubScreenDeeplink, err := n.deeplinkBuilder.NetWorthAssetHubDeeplink(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to get asset screen deeplink: %v", err)
	}

	if onbDetails.GetDetails().GetFiLiteDetails().GetIsEnabled() == commontypes.BooleanEnum_TRUE {
		return n.getFiLiteUserScreen(ctx, actorId, netWorthSummary, networthHubScreenDeeplink)
	}

	totalSavingsAccountValue, _, totalAssetsValue, totalLiabilitysValue, err := getNetWorthAggregateTypesWithValues(netWorthSummary)
	if err != nil {
		return nil, fmt.Errorf("error while getNetWorthAggregateTypesWithValues: %v", err)
	}

	var connectedAccountNotFound = false
	// Get connected accounts when we only have savings account in assets and liabilities are also zero
	if totalSavingsAccountValue.GetUnits() == totalAssetsValue.GetUnits() && totalLiabilitysValue.GetUnits() == 0 {
		caResp, rpcErr := n.connectedAccountClient.GetAccounts(ctx, &beCaPb.GetAccountsRequest{
			ActorId: actorId,
			AccountFilterList: []beCaExtPb.AccountFilter{
				beCaExtPb.AccountFilter_ACCOUNT_FILTER_ACTIVE,
			},
			AccInstrumentTypeList: []connectedAccountEnumsPb.AccInstrumentType{
				connectedAccountEnumsPb.AccInstrumentType_ACC_INSTRUMENT_TYPE_DEPOSIT,
			},
		})
		if rpcErr = epifigrpc.RPCError(caResp, rpcErr); rpcErr != nil && !caResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("failed to get connected Accounts: %v", rpcErr)
		}
		connectedAccountNotFound = caResp.GetStatus().IsRecordNotFound()
	}

	aggregates := []Aggregate{
		{AggregateType: feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_SAVINGS, Value: totalSavingsAccountValue},
		{AggregateType: feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_ASSETS, Value: totalAssetsValue},
	}

	// if user has not connected account return connect accounts screen
	if connectedAccountNotFound {
		return n.getConnectAccountScreen(ctx, actorId, aggregates, networthHubScreenDeeplink), nil
	}
	// Create DashboardInfo for User with assets
	if isWealthAnalyserUser {
		return n.getDashboardWithAssets(ctx, actorId, totalAssetsValue, aggregates, networthHubScreenDeeplink), nil
	}
	// Create DashboardInfo for User with net worth
	return n.createDashboardUserWithNetWorth(ctx, actorId, totalAssetsValue, aggregates, networthHubScreenDeeplink), nil
}

func getNetWorthAggregateTypesWithValues(netWorthSummary *networthBePb.GetNetWorthValueResponse) (*moneyPb.Money, *moneyPb.Money, *moneyPb.Money, *moneyPb.Money, error) {
	totalSavingsAccountValue, err := netWorthSummary.GetTotalSavingsAccountValue()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error while calculating total Savings Account Value: %v", err)
	}

	totalAssetsValue, err := netWorthSummary.GetTotalAssetValue()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error while calculating total Assets Value: %v", err)
	}

	totalInvestmentValue, err := netWorthSummary.GetTotalInvestmentValue()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error while calculating total Investment Value: %v", err)
	}

	totalLiabilitiesValue, err := netWorthSummary.GetTotalLiabilityValue()
	if err != nil {
		return nil, nil, nil, nil, fmt.Errorf("error while calculating total Liability Value: %v", err)
	}

	return totalSavingsAccountValue, totalInvestmentValue, totalAssetsValue, totalLiabilitiesValue, nil
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50342&mode=design&t=jQOmQx5pdjcbpjbl-4
func (n *NetWorthDashboardComponentViewGetter) getConnectAccountScreen(ctx context.Context, actorId string, aggregates []Aggregate, networthHubScreenDeeplink *deeplinkPb.Deeplink) *homeOrchFePb.DashboardComponent_DashboardView {
	footerDashboardUI := n.getDashboardFooter(ctx, actorId, footerTextMap, true, aggregates)
	footerTicker := n.getDashboardFooterTickerFromFooter(ctx, actorId, footerDashboardUI, feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_UNSPECIFIED, aggregates)
	dashInfo := &feHomePb.HomeDashboard{
		Title: wealthTitle,
		Body: &feHomePb.HomeDashboard_Body{
			DashboardIcons: []*feHomePb.Icon{
				{
					VisualElement: commontypes.GetVisualElementImageFromUrl(zeroStateConnectAccountsIcon),
					IconImage: &commontypes.Image{
						ImageUrl: zeroStateConnectAccountsIcon,
					},
					Title: &commontypes.Text{
						FontColor: fiGreenColour,
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: connectAccountsString,
						},
						FontStyle: &commontypes.Text_StandardFontStyle{
							StandardFontStyle: commontypes.FontStyle_SUBTITLE_S,
						},
					},
					Action: &feHomePb.Icon_Deeplink{
						Deeplink: networthHubScreenDeeplink,
					},
					IconType:   homeTypesPb.IconType_CONNECT_ACCOUNT,
					ActionType: feHomePb.Icon_ACTION_TYPE_DEEPLINK,
					BgColour:   &ui.BackgroundColour{Colour: &ui.BackgroundColour_BlockColour{BlockColour: dashboardIconsBgColourZeroStateDashboardV2}},
				},
			},
			DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
		},
		Deeplink: networthHubScreenDeeplink,
		Footer:   footerDashboardUI,
		// Footer to show in privacy mode
		FooterTicker:        footerTicker,
		DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
		Shadow:              ui.GetDashboardShadow(),
		DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
		BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
	}
	return &homeOrchFePb.DashboardComponent_DashboardView{
		DashboardViewType: homeOrchFePb.DashboardComponent_DashboardView_DASHBOARD_VIEW_NET_WORTH,
		Id:                constants.NetworthDashboardComponentId.String(),
		DashboardInfo:     dashInfo,
		Background:        GetDashboardBackground(),
	}
}

//nolint:unparam
func (n *NetWorthDashboardComponentViewGetter) getDashboardFooterTickerFromFooter(
	ctx context.Context,
	actorId string,
	footer []*ui.IconTextComponent,
	tickerType feHomePb.HomeDashboard_FooterTicker_TickerItem_TickerType,
	aggregate []Aggregate,
) *feHomePb.HomeDashboard_FooterTicker {
	var tickerItems []*feHomePb.HomeDashboard_FooterTicker_TickerItem
	for _, i := range footer {
		HomeFooterTicker := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerContent: i,
			TickerType:    tickerType,
		}
		tickerItems = append(tickerItems, HomeFooterTicker)
	}
	var tickerItemsPrivate []*feHomePb.HomeDashboard_FooterTicker_TickerItem

	var privacyFooterTicker []*ui.IconTextComponent
	privacyFooterTicker = n.getDashboardPrivacyModeFooter(ctx, actorId, footerTextMap, aggregate)

	for _, i := range privacyFooterTicker {
		HomeFooterTicker := &feHomePb.HomeDashboard_FooterTicker_TickerItem{
			TickerContent: i,
			TickerType:    tickerType,
		}
		tickerItemsPrivate = append(tickerItemsPrivate, HomeFooterTicker)
	}

	footerTicker := &feHomePb.HomeDashboard_FooterTicker{
		TickerItemsPrivacy: tickerItemsPrivate,
		TickerItems:        tickerItems,
	}
	return footerTicker
}

// if connectedAccountNotFound, figma:https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50344&mode=design&t=jQOmQx5pdjcbpjbl-4
// else figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50370&mode=design&t=jQOmQx5pdjcbpjbl-4
func (n *NetWorthDashboardComponentViewGetter) getDashboardFooter(
	ctx context.Context,
	actorId string,
	footerToTextMap map[string]*commontypes.Text,
	connectedAccountNotFound bool,
	aggregates []Aggregate,
) []*ui.IconTextComponent {
	footer := make([]*ui.IconTextComponent, 0)
	if connectedAccountNotFound {
		return []*ui.IconTextComponent{
			{
				Texts: []*commontypes.Text{
					commontypes.GetTextFromStringFontColourFontStyle(connectAccountsFooterString, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_HEADLINE_S),
				},
			},
		}
	}
	talkToAiItc := n.talkToAiTicker(ctx, actorId)
	if talkToAiItc != nil {
		return talkToAiItc
	}
	instrumentsAdded := 0
	var instComp *ui.IconTextComponent
	for _, aggregate := range aggregates {
		if aggregate.Value.GetUnits() != 0 {
			instComp = &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					footerToTextMap[aggregate.AggregateType.String()],
					getFooterAmount(money.ToDisplayStringWithSuffixAndPrecision(aggregate.Value,
						false, true, moneyPrecision, money.IndianNumberSystem))},
			}
			instrumentsAdded++
			footer = append(footer, instComp)
		}
	}
	return footer
}

func getFooterAmount(amount string) *commontypes.Text {
	return commontypes.GetTextFromStringFontColourFontStyle(amount, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S)
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50398&mode=design&t=jQOmQx5pdjcbpjbl-4
func (n *NetWorthDashboardComponentViewGetter) getDashboardPrivacyModeFooter(
	ctx context.Context,
	actorId string,
	footerToTextMap map[string]*commontypes.Text,
	aggregates []Aggregate,
) []*ui.IconTextComponent {
	footer := make([]*ui.IconTextComponent, 0)
	instrumentsAdded := 0

	talkToAiItc := n.talkToAiTicker(ctx, actorId)
	if talkToAiItc != nil {
		return talkToAiItc
	}

	var instComp *ui.IconTextComponent
	for _, aggregate := range aggregates {
		if aggregate.Value.GetUnits() != 0 {
			instComp = &ui.IconTextComponent{
				Texts: []*commontypes.Text{
					footerToTextMap[aggregate.AggregateType.String()]},
			}
			instrumentsAdded++
			footer = append(footer, instComp)
		}
	}
	return footer
}

func (n *NetWorthDashboardComponentViewGetter) getDashboardWithAssets(
	ctx context.Context,
	actorId string,
	assetsAmount *moneyPb.Money,
	aggregates []Aggregate,
	networthHubScreenDeeplink *deeplinkPb.Deeplink,
) *homeOrchFePb.DashboardComponent_DashboardView {
	// filter out Liability from aggregates
	aggregates = filterOutLiabilitiesFromAggregates(aggregates)
	footerDashboardUI := n.getDashboardFooter(ctx, actorId, footerTextMap, false, aggregates)
	footerTicker := n.getDashboardFooterTickerFromFooter(ctx, actorId, footerDashboardUI, feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_UNSPECIFIED, aggregates)

	dashInfo := &feHomePb.HomeDashboard{
		Title: wealthTitle,
		Body: &feHomePb.HomeDashboard_Body{
			PrivacyModeImage: &commontypes.Image{
				ImageUrl: privacyImage,
			},
			MoneyValue:     getDashboardMoneyString(assetsAmount),
			DashboardState: feHomePb.HomeDashboard_Body_STATE_NON_ZERO,
			DashboardIcons: getNecessaryIcons(),
		},
		Footer: footerDashboardUI,
		// Footer to show in privacy mode
		PrivacyModeFooter:   n.getDashboardPrivacyModeFooter(ctx, actorId, footerTextMap, aggregates),
		FooterTicker:        footerTicker,
		DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
		Deeplink:            networthHubScreenDeeplink,
		Shadow:              ui.GetDashboardShadow(),
		DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
		BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
	}
	return &homeOrchFePb.DashboardComponent_DashboardView{
		DashboardViewType: homeOrchFePb.DashboardComponent_DashboardView_DASHBOARD_VIEW_NET_WORTH,
		Id:                constants.NetworthDashboardComponentId.String(),
		DashboardInfo:     dashInfo,
		Background:        GetDashboardBackground(),
	}
}

// Privacy Icon on Dashboard
// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50363&mode=design&t=jQOmQx5pdjcbpjbl-4
func getNecessaryIcons() []*feHomePb.Icon {
	return []*feHomePb.Icon{privacyToggleIcon}
}

func getDashboardMoneyString(amount *moneyPb.Money) []*commontypes.Text {

	if money.IsZero(amount) {
		return getZeroMoneyTexts()
	}
	value, precision := money.GetDisplayStringWithValueAndPrecision(amount, moneyPrecision,
		false, false, money.IndianNumberSystem)
	moneyTexts := make([]*commontypes.Text, 0)

	// Convert rupee symbol to text
	rupeeText := commontypes.GetTextFromStringFontColourFontStyle(rupeeSymbol, colors.ColorOnDarkDisabled700, commontypes.FontStyle_RUPEE_XL)
	moneyTexts = append(moneyTexts, rupeeText)

	amountText := commontypes.GetTextFromStringFontColourFontStyle(value, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_2XL)

	moneyTexts = append(moneyTexts, amountText)
	if precision != "" {
		precisionText := commontypes.GetTextFromStringFontColourFontStyle(precision, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_XL)
		moneyTexts = append(moneyTexts, precisionText)
	}

	return moneyTexts
}

func getZeroMoneyTexts() []*commontypes.Text {
	return []*commontypes.Text{
		commontypes.GetTextFromStringFontColourFontStyle(rupeeSymbol, colors.ColorOnDarkDisabled700, commontypes.FontStyle_RUPEE_XL),
		commontypes.GetTextFromStringFontColourFontStyle(zero, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_NUMBER_2XL),
	}
}

func filterOutLiabilitiesFromAggregates(aggregates []Aggregate) []Aggregate {
	var filteredAggregates []Aggregate
	for _, aggregate := range aggregates {
		if aggregate.AggregateType != feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_LIABILITIES {
			filteredAggregates = append(filteredAggregates, aggregate)
		}
	}
	return filteredAggregates
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50355&mode=design&t=jQOmQx5pdjcbpjbl-4
func (n *NetWorthDashboardComponentViewGetter) createDashboardUserWithNetWorth(
	ctx context.Context,
	actorId string,
	totalAssetsValue *moneyPb.Money,
	aggregates []Aggregate,
	networthHubScreenDeeplink *deeplinkPb.Deeplink,
) *homeOrchFePb.DashboardComponent_DashboardView {
	footerDashboardUI := n.getDashboardFooter(ctx, actorId, footerTextMap, false, aggregates)
	footerTicker := n.getDashboardFooterTickerFromFooter(ctx, actorId, footerDashboardUI, feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_UNSPECIFIED, aggregates)

	dashInfo := &feHomePb.HomeDashboard{
		Title: wealthTitle,
		Body: &feHomePb.HomeDashboard_Body{
			PrivacyModeImage: &commontypes.Image{
				ImageUrl: privacyImage,
			},
			MoneyValue:     getDashboardMoneyString(totalAssetsValue),
			DashboardState: feHomePb.HomeDashboard_Body_STATE_NON_ZERO,
			DashboardIcons: getNecessaryIcons(),
		},
		Footer: footerDashboardUI,
		// Footer to show in privacy mode
		PrivacyModeFooter:   n.getDashboardPrivacyModeFooter(ctx, actorId, footerTextMap, aggregates),
		FooterTicker:        footerTicker,
		DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
		Deeplink:            networthHubScreenDeeplink,
		Shadow:              ui.GetDashboardShadow(),
		DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
		BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
	}
	return &homeOrchFePb.DashboardComponent_DashboardView{
		DashboardViewType: homeOrchFePb.DashboardComponent_DashboardView_DASHBOARD_VIEW_NET_WORTH,
		Id:                constants.NetworthDashboardComponentId.String(),
		DashboardInfo:     dashInfo,
		Background:        GetDashboardBackground(),
	}
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=5097-50332&mode=design&t=jQOmQx5pdjcbpjbl-4
func (n *NetWorthDashboardComponentViewGetter) getFiLiteUserScreen(ctx context.Context, actorId string, netWorthSummary *networthBePb.GetNetWorthValueResponse, networthHubScreenDeeplink *deeplinkPb.Deeplink) (*homeOrchFePb.DashboardComponent_DashboardView, error) {
	if apputils.IsFeatureEnabledFromCtxDynamic(ctx, n.feConfig.NetworthConfig().NetworthD2HDashboardFeatureConfig()) {
		return getFiLiteNonZeroStateDashboard(netWorthSummary, networthHubScreenDeeplink)
	}

	footerDashboardUI := []*ui.IconTextComponent{
		{
			RightIcon: commontypes.GetImageFromUrl(fiLiteFooterRightGreen),
			Texts: []*commontypes.Text{
				commontypes.GetTextFromStringFontColourFontStyle(trackNetWorthString, colors.ColorForest, commontypes.FontStyle_SUBTITLE_S),
			},
			RightVisualElement: commontypes.GetVisualElementImageFromUrl(fiLiteFooterRightGreen),
		},
	}

	footerTicker := n.getDashboardFooterTickerFromFooter(ctx, actorId, footerDashboardUI, feHomePb.HomeDashboard_FooterTicker_TickerItem_TICKER_TYPE_UNSPECIFIED, nil)
	return &homeOrchFePb.DashboardComponent_DashboardView{
		DashboardViewType: homeOrchFePb.DashboardComponent_DashboardView_DASHBOARD_VIEW_NET_WORTH,
		Id:                constants.NetworthDashboardComponentId.String(),
		DashboardInfo: &feHomePb.HomeDashboard{
			Title: wealthTitle,
			Body: &feHomePb.HomeDashboard_Body{
				DashboardState: feHomePb.HomeDashboard_Body_STATE_ZERO,
				MoneyValueV2: []*ui.IconTextComponent{
					{
						Texts: []*commontypes.Text{
							commontypes.GetTextFromStringFontColourFontStyle(fiLiteCardCenterString, colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_HEADLINE_M),
						},
						Deeplink: networthHubScreenDeeplink,
					},
				},
			},
			Footer:              footerDashboardUI,
			FooterTicker:        footerTicker,
			DashboardFooter:     &feHomePb.HomeDashboard_DashboardFooterTicker{DashboardFooterTicker: footerTicker},
			Deeplink:            networthHubScreenDeeplink,
			Shadow:              ui.GetDashboardShadow(),
			ZeroStateImage:      commontypes.GetVisualElementImageFromUrl(homeZeroStateImageUrl),
			DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
			BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
		},
		Background: GetDashboardBackground(),
	}, nil
}

// figma: https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-19961&t=cviIK3Ufe87UtobX-1
func getFiLiteNonZeroStateDashboard(netWorthSummary *networthBePb.GetNetWorthValueResponse, networthHubScreenDeeplink *deeplinkPb.Deeplink) (*homeOrchFePb.DashboardComponent_DashboardView, error) {
	totalSavingsAccountValue, totalInvestmentsValue, totalAssetsValue, totalLiabilitiesValue, err := getNetWorthAggregateTypesWithValues(netWorthSummary)
	if err != nil {
		return nil, fmt.Errorf("error while getting net worth aggreagate types with values, err: %w", err)
	}

	aggregates := []Aggregate{
		{AggregateType: feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_SAVINGS, Value: totalSavingsAccountValue},
		{AggregateType: feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_INVESTMENTS, Value: totalInvestmentsValue},
		{AggregateType: feNetworthPb.NetworthAggregateType_NETWORTH_AGGREGATE_TYPE_ASSETS, Value: totalAssetsValue},
	}

	dashInfo := &feHomePb.HomeDashboard{
		Title: wealthTitle,
		Body: &feHomePb.HomeDashboard_Body{
			PrivacyModeImage: &commontypes.Image{
				ImageUrl: privacyImage,
			},
			MoneyValue:     getFiLiteDashboardMoneyText(totalAssetsValue),
			MoneyValueV2:   getFiLiteDashboardMoneyTextV2(totalAssetsValue),
			DashboardState: feHomePb.HomeDashboard_Body_STATE_NON_ZERO,
			DashboardIcons: getFiLiteDashboardIcons(totalSavingsAccountValue, totalInvestmentsValue, totalLiabilitiesValue),
		},
		DashboardFooter: &feHomePb.HomeDashboard_DashboardFooterHorizontal{
			DashboardFooterHorizontal: getDashboardHorizontalFooter(fiLiteFooterTextMap, aggregates),
		},
		Deeplink:            networthHubScreenDeeplink,
		Overlay:             getFiLiteNonZeroStateDashboardOverlay(totalSavingsAccountValue, totalInvestmentsValue, totalLiabilitiesValue),
		Shadow:              ui.GetDashboardShadow(),
		DashboardBackground: feHomePb.GetHomeDashboardSectionBackground(),
		BorderColor:         feHomePb.GetHomeDashboardBorderColor(),
	}
	return &homeOrchFePb.DashboardComponent_DashboardView{
		DashboardViewType: homeOrchFePb.DashboardComponent_DashboardView_DASHBOARD_VIEW_NET_WORTH,
		Id:                constants.NetworthDashboardComponentId.String(),
		DashboardInfo:     dashInfo,
		Background:        GetDashboardBackground(),
	}, nil
}

func getFiLiteDashboardMoneyText(netWorthAmount *moneyPb.Money) []*commontypes.Text {
	amountString := money.ToDisplayStringWithSuffixAndPrecision(netWorthAmount, true, true, fiLiteMoneyPrecision, money.IndianNumberSystem)
	return []*commontypes.Text{
		commontypes.GetTextFromStringFontColourFontStyle(amountString, colors.ColorSnow, commontypes.FontStyle_NUMBER_2XL),
	}
}

func getFiLiteDashboardMoneyTextV2(netWorthAmount *moneyPb.Money) []*ui.IconTextComponent {
	networthAmountString := money.ToDisplayStringWithSuffixAndPrecisionV2(netWorthAmount, false, true, fiLiteMoneyPrecision, false, money.IndianNumberSystem)
	// hide decimals when net worth is negative to avoid overflow of cta button on the right side
	if netWorthAmount.GetUnits() < 0 {
		networthAmountString = money.ToDisplayStringWithSuffixAndPrecisionV2(netWorthAmount, false, true, 0, false, money.IndianNumberSystem)
	}

	return []*ui.IconTextComponent{
		ui.NewITC().WithTexts(&commontypes.Text{
			FontColor: colors.ColorOnDarkDisabled700,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: money.CurrencyToSymbolMap[netWorthAmount.GetCurrencyCode()],
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_RUPEE_XL}}),
		ui.NewITC().WithTexts(&commontypes.Text{
			FontColor: colors.ColorSnow,
			DisplayValue: &commontypes.Text_PlainString{
				PlainString: networthAmountString,
			},
			FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_NUMBER_2XL},
		}),
	}
}

func getFiLiteDashboardIcons(totalSavingsAccountValue *moneyPb.Money, totalInvestmentsValue *moneyPb.Money, _ *moneyPb.Money) []*feHomePb.Icon {
	if totalSavingsAccountValue.GetUnits() == 0 {
		return []*feHomePb.Icon{
			privacyToggleIcon,
			addAccountsIcon,
		}
	}
	if totalInvestmentsValue.GetUnits() == 0 {
		return []*feHomePb.Icon{
			privacyToggleIcon,
			addInvestmentsIcon,
		}
	}
	return []*feHomePb.Icon{
		privacyToggleIcon,
		viewDetailsIcon,
	}
}

// figma: https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4012-8837&t=CHPav7fe2L5yRD99-1
func getFiLiteNonZeroStateDashboardOverlay(totalSavingsAccountValue *moneyPb.Money, totalInvestmentsValue *moneyPb.Money, totalLiabilitiesValue *moneyPb.Money) *feHomePb.HomeDashboard_Overlay {
	deeplink := insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError()

	// if user does not have any connected accounts or liabilities, we redirect to connect accounts flow
	if totalSavingsAccountValue.GetUnits() == 0 && totalInvestmentsValue.GetUnits() == 0 && totalLiabilitiesValue.GetUnits() == 0 {
		deeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_CONNECTED_ACCOUNTS_CONNECT_ACCOUNT_FLOW,
		}
	}

	return &feHomePb.HomeDashboard_Overlay{
		Id:       fiLiteDashboardOverlayId,
		BgColour: uiWidget.GetBlockBackgroundColour("#8F18191B"),
		Title:    commontypes.GetPlainStringText(fiLiteDashboardOverlayString).WithFontStyle(commontypes.FontStyle_HEADLINE_M).WithFontColor(colors.ColorSnow),
		Cta: &feHomePb.CTA{
			Text: &commontypes.Text{
				DisplayValue: &commontypes.Text_PlainString{
					PlainString: fiLiteDashboardOverlayCtaString,
				},
				FontColor: colors.ColorSnow,
				FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_BUTTON_S},
			},
			BgColor: ui.GetRadialGradient([]string{"#006D5B", colors.ColorLightPrimaryAction}),
			Action: &feHomePb.CTA_DeeplinkAction{
				DeeplinkAction: deeplink,
			},
		},
	}
}

func getDashboardHorizontalFooter(footerToTextMap map[string]*commontypes.Text, aggregates []Aggregate) *feHomePb.HomeDashboard_FooterHorizontal {
	footerItems := make([]*feHomePb.HomeDashboard_FooterHorizontal_FooterItem, 0)
	footerItemsPrivacy := make([]*feHomePb.HomeDashboard_FooterHorizontal_FooterItem, 0)

	for _, aggregate := range aggregates {
		footerItem := &feHomePb.HomeDashboard_FooterHorizontal_FooterItem{
			Title:    footerToTextMap[aggregate.AggregateType.String()],
			Value:    getFooterAmount(money.ToDisplayStringWithSuffixAndPrecisionV2(aggregate.Value, true, true, fiLiteMoneyPrecision, false, money.IndianNumberSystem)),
			Deeplink: insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError(),
		}
		footerItemPrivacy := &feHomePb.HomeDashboard_FooterHorizontal_FooterItem{
			Title:    footerToTextMap[aggregate.AggregateType.String()],
			Value:    getFooterPrivacyAmount(),
			Deeplink: insightsDeeplinkPb.GetNetworthHubScreenDeeplinkWithoutError(),
		}
		footerItems = append(footerItems, footerItem)
		footerItemsPrivacy = append(footerItemsPrivacy, footerItemPrivacy)
	}
	return &feHomePb.HomeDashboard_FooterHorizontal{
		FooterItems:        footerItems,
		FooterItemsPrivacy: footerItemsPrivacy,
	}
}

// talkToAiTicker creates a "Talk to AI" footer component that allows users to interact with AI about their financial data.
// The component is only shown when both AI icons and MCP (Model Context Protocol) feature flags are enabled,
// and when the AI deeplink is successfully generated.
//
// Parameters:
//   - ctx: Context for the operation
//   - actorId: The user's actor ID for feature flag evaluation
//
// Returns:
//   - []*ui.IconTextComponent: A slice containing the AI component if conditions are met, nil otherwise
func (n *NetWorthDashboardComponentViewGetter) talkToAiTicker(ctx context.Context, actorId string) []*ui.IconTextComponent {
	var aiDeeplink *deeplinkPb.Deeplink
	isInteractiveTalkToAIEnabled, err := n.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_INTERACTIVE_TALK_TO_AI_SCREEN).WithActorId(actorId))
	if err != nil {
		// not returning error here, as it can still show rest of the deeplinks
		logger.Error(ctx, "failed to check if INTERACTIVE_TALK_TO_AI_SCREEN is enabled", zap.Error(err))
	}
	if isInteractiveTalkToAIEnabled {
		aiDeeplink = &deeplinkPb.Deeplink{
			Screen: deeplinkPb.Screen_INTERACTIVE_TALK_TO_AI_SCREEN,
			ScreenOptionsV2: deeplinkv3.GetScreenOptionV2WithoutError(&networth.InteractiveTalkToAiScreenOptions{
				Entrypoint: networth.Entrypoint_ENTRYPOINT_WB_DASHBOARD.String(),
			}),
		}
	}
	if aiDeeplink == nil {
		// Check if AI icons feature flag is enabled for the user
		isAiIconEnabled, releaseErr := n.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_NET_WORTH_AI_ICONS).WithActorId(actorId))
		if releaseErr != nil {
			logger.Error(ctx, "error evaluating feature flag for AI icon", zap.Error(releaseErr))
		}
		// Check if MCP feature flag is enabled for the user
		isMcpEnabled, releaseErr := n.releaseEvaluator.Evaluate(ctx, release.NewCommonConstraintData(types.Feature_FEATURE_FI_MCP_TOTP_CODE).WithActorId(actorId))
		if releaseErr != nil {
			logger.Error(ctx, "error evaluating feature flag for MCP", zap.Error(releaseErr))
		}
		if isAiIconEnabled && isMcpEnabled {
			// Generate the deeplink to the AI bottom sheet screen
			aiDeeplink, err = n.deeplinkBuilder.ExportToAiBottomSheetScreen(ctx)
			if err != nil {
				logger.Error(ctx, "AI deeplink not found")
			}
		}
	}

	if aiDeeplink != nil {
		aiIconText := ui.NewITC().WithTexts(
			commontypes.GetTextFromStringFontColourFontStyle("Talk to AI about your money", colors.ColorOnDarkHighEmphasis, commontypes.FontStyle_SUBTITLE_S),
		).WithLeftVisualElementUrlHeightAndWidth(talkToAiIcon, 20, 20).WithDeeplink(aiDeeplink)

		return []*ui.IconTextComponent{aiIconText}
	}
	// Return nil if any of the required conditions are not met
	return nil
}

func getFooterPrivacyAmount() *commontypes.Text {
	return commontypes.GetTextFromStringFontColourFontStyle(fiLiteFooterPrivacyString, colors.ColorOnDarkMediumEmphasis, commontypes.FontStyle_SUBTITLE_S)
}
