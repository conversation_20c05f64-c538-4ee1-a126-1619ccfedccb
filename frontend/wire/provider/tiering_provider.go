package provider

import (
	events2 "github.com/epifi/be-common/pkg/events"
	dePb "github.com/epifi/gamma/api/frontend/dynamic_elements"
	"github.com/epifi/gamma/api/tiering"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	"github.com/epifi/gamma/frontend/config/genconf"
	"github.com/epifi/gamma/frontend/tiering/amb/data"
	"github.com/epifi/gamma/frontend/tiering/amb/ui"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	pkgRelease "github.com/epifi/gamma/pkg/feature/release"
)

// AMBDataProviderProvider provides the AMBDataProvider implementation
func AMBDataProviderProvider(
	tieringClient tiering.TieringClient,
	dataCollector tieringData.DataCollector,
	tieringPinotClient tieringPinotPb.EODBalanceClient,
	config *genconf.Config,
	broker events2.Broker,
	dynamicElementsClient dePb.DynamicElementsClient,
) data.AMBDataProvider {
	return data.NewAMBDataCollector(
		tieringClient,
		dataCollector,
		tieringPinotClient,
		config,
		broker,
		dynamicElementsClient,
	)
}

// AMBScreenBuilderProvider provides the AMBScreenBuilder implementation
func AMBScreenBuilderProvider(
	config *genconf.Config,
	releaseEvaluator pkgRelease.IEvaluator,
) ui.AMBScreenBuilder {
	return ui.NewAMBScreenBuilder(config, releaseEvaluator)
}
