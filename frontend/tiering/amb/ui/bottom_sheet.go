package ui

import (
	"context"
	"fmt"

	"github.com/samber/lo"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/gamma/api/frontend/deeplink"
	fePayTxnPb "github.com/epifi/gamma/api/frontend/pay/transaction"
	"github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/components"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/properties"
	"github.com/epifi/gamma/api/typesv2/ui/sdui/sections"
	"github.com/epifi/gamma/frontend/config/genconf"
	pkgUI "github.com/epifi/gamma/frontend/pkg/ui"
	"github.com/epifi/gamma/frontend/tiering/amb/models"
)

// nolint:dupl
// GetAMBBottomSheetSection creates the bottom sheet section for AMB information
// Now supports version-based bottomsheet enhancements with improved padding
func (b *AMBScreenBuilderImpl) GetAMBBottomSheetSection(ctx context.Context, config *genconf.Config, data *models.AMBScreenData, sheetType models.AMBBottomSheetType) *sections.Section {
	// Get top padding based on FEATURE_SHOW_UPDATED_BOTTOM_SHEET feature flag evaluation
	topPadding := pkgUI.GetBottomSheetTopPadding(ctx, b.releaseEvaluator, data.ActorId)

	whyMaintainAmbText := lo.Ternary(data.CurrentTier == external.Tier_TIER_FI_REGULAR, config.Tiering().AMBScreen().TextContent().WhyMaintainAMBCharges(), config.Tiering().AMBScreen().TextContent().WhyMaintainAMBRewards())
	switch sheetType {
	case models.AMB_SUMMARY:
		return &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(&sections.VerticalListSection{
								Components: []*components.Component{
									{
										Content: GetAnyWithoutError(
											GetTextFromStringFontColourFontStyle("What is Average Monthly Balance?", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
									},
									{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
									{
										Content: GetAnyWithoutError(
											commontypes.GetHtmlText(fmt.Sprintf("<span style='text-align: center; color: %s; font-family: system-ui; font-size: 13px;'>Average Monthly Balance is the <b>average of your daily end-of-the-day balances</b> throughout the month</span>", config.Tiering().AMBScreen().Colors().Subtitle()))),
									},
									{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
									{
										Content: GetAnyWithoutError(
											GetTextFromStringFontColourFontStyle("How is AMB calculated?", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
									},
									{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
									{
										Content: GetAnyWithoutError(commontypes.GetVisualElementFromUrlHeightAndWidth(config.Tiering().AMBScreen().Images().AMBAverageCalculation(), 42, 260)),
									},
									{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
									{
										Content: GetAnyWithoutError(
											GetTextFromStringFontColourFontStyle("Why maintain AMB?", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
									},
									{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
									{
										Content: GetAnyWithoutError(
											GetTextFromStringFontColourFontStyle(whyMaintainAmbText, config.Tiering().AMBScreen().Colors().Subtitle(), commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
									},
								},
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Padding: &properties.PaddingProperty{
													Bottom: 24,
												},
											},
										},
									},
								},
								ListElementOverlapProps: &properties.ListElementOverlapProps{
									OverlapDevicePixels: -16,
								},
							}),
						},
						{
							Content: GetAnyWithoutError(&sections.VerticalListSection{
								Components: []*components.Component{
									b.createAddMoneyButton(fmt.Sprintf("Add %s now", formatCurrency(data.AmountNeeded)), config.Tiering().AMBScreen().Colors().Primary(), config.Tiering().AMBScreen().Colors().White(), 12, 80, &deeplink.Deeplink{
										Screen: deeplink.Screen_TRANSFER_IN,
										ScreenOptions: &deeplink.Deeplink_TransferInScreenOptions{
											TransferInScreenOptions: &deeplink.TransferInScreenOptions{
												CustomAmount: typesv2.GetFromBeMoney(data.AmountNeeded),
												UiEntryPoint: fePayTxnPb.UIEntryPoint_AMB_DETAILS.String(),
											},
										},
									}, data, deeplink.Screen_SDUI_BOTTOM_SHEET, &sheetType),
								},
								VisualProperties: []*properties.VisualProperty{
									{
										Properties: &properties.VisualProperty_ContainerProperty{
											ContainerProperty: &properties.ContainerProperty{
												Padding: &properties.PaddingProperty{
													Top: 24,
												},
											},
										},
									},
								},
							}),
						},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Top:    topPadding,
										Bottom: 24,
										Left:   28,
										Right:  28,
									},
								},
							},
						},
					},
				},
			},
		}
	case models.CURRENT_AMB:
		return &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("Current AMB", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("Current AMB is the average of your end-of-the day balances every day this month until today.", config.Tiering().AMBScreen().Colors().Subtitle(), commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Top:    topPadding,
										Bottom: 24,
										Left:   28,
										Right:  28,
									},
								},
							},
						},
					},
				},
			},
		}
	case models.TARGET_AMB:
		targetAmbText := lo.Ternary(data.CurrentTier == external.Tier_TIER_FI_REGULAR, config.Tiering().AMBScreen().TextContent().RequiredAmbChargesText(), config.Tiering().AMBScreen().TextContent().RequiredAmbRewardsText())
		return &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("Required AMB", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle(targetAmbText, config.Tiering().AMBScreen().Colors().Subtitle(), commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Top:    topPadding,
										Bottom: 24,
										Left:   28,
										Right:  28,
									},
								},
							},
						},
					},
				},
			},
		}
	case models.BALANCE_REQUIRED:
		return &sections.Section{
			Content: &sections.Section_VerticalListSection{
				VerticalListSection: &sections.VerticalListSection{
					Components: []*components.Component{
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("Amount required to meet AMB", config.Tiering().AMBScreen().Colors().Content(), commontypes.FontStyle_HEADLINE_L, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("This is the amount you need to add today to meet the average monthly balance requirement of your account plan.", config.Tiering().AMBScreen().Colors().Subtitle(), commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_S})},
						{
							Content: GetAnyWithoutError(
								GetTextFromStringFontColourFontStyle("This amount will vary every day depending on your current balance and the number of days left in the month to meet your AMB criteria.", config.Tiering().AMBScreen().Colors().Subtitle(), commontypes.FontStyle_BODY_S, commontypes.Text_ALIGNMENT_CENTER)),
						},
						{Content: GetAnyWithoutError(&components.Spacer{SpacingValue: components.Spacing_SPACING_L})},
						b.createAddMoneyButton(fmt.Sprintf("Add %s now", formatCurrency(data.AmountNeeded)), config.Tiering().AMBScreen().Colors().Primary(), config.Tiering().AMBScreen().Colors().White(), 12, 80, &deeplink.Deeplink{
							Screen: deeplink.Screen_TRANSFER_IN,
							ScreenOptions: &deeplink.Deeplink_TransferInScreenOptions{
								TransferInScreenOptions: &deeplink.TransferInScreenOptions{
									CustomAmount: typesv2.GetFromBeMoney(data.AmountNeeded),
									UiEntryPoint: fePayTxnPb.UIEntryPoint_AMB_DETAILS.String(),
								},
							},
						}, data, deeplink.Screen_SDUI_BOTTOM_SHEET, &sheetType),
					},
					VisualProperties: []*properties.VisualProperty{
						{
							Properties: &properties.VisualProperty_ContainerProperty{
								ContainerProperty: &properties.ContainerProperty{
									Padding: &properties.PaddingProperty{
										Top:    topPadding,
										Bottom: 24,
										Left:   28,
										Right:  28,
									},
								},
							},
						},
					},
				},
			},
		}
	default:
		return nil
	}
}
