package models

import (
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/typesv2/common/ui/widget"
	"github.com/epifi/gamma/api/frontend/deeplink"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
)

// AMBBottomSheetType represents the type of AMB bottom sheet to render
// Enum values for clarity and type safety
// Add more as needed
//
//go:generate stringer -type=AMBBottomSheetType
type AMBBottomSheetType int

const (
	AMB_SUMMARY AMBBottomSheetType = iota
	CURRENT_AMB
	TARGET_AMB
	BALANCE_REQUIRED
)

// AnalyticsComponentContext represents the context of a UI component for analytics
type AnalyticsComponentContext struct {
	Component    string
	SubComponent string
	ActionType   string
	ScreenName   string
}

// AMBScreenData contains all dynamic data needed to build the AMB screen
type AMBScreenData struct {
	// User context
	ActorId string

	// Percentage values
	BannerCashbackPercent string // e.g. "3%"

	// Money amounts
	AmountNeeded  *moneyPb.Money // e.g. "₹7,595"
	CurrentAMB    *moneyPb.Money // e.g. "₹18,022"
	TargetAMB     *moneyPb.Money // e.g. "₹25,000"
	PenaltyAmount string         // e.g. "₹399"
	AMBRatio      float64

	// Progress information
	ProgressPercentage int32 // e.g. 72

	// Tier information
	CurrentTier    external.Tier // e.g. "Plus", "Prime", "Regular"
	PreviousTier   external.Tier // e.g. "Plus"
	TierChangeDate string        // e.g. "April 12"

	// History data
	HistoryEntries        []AMBHistoryEntry
	IsShortfallAchievable bool
	ShouldAddBalance      bool

	// Visual indicators
	CurrentAMBColor   string // Color for current AMB display
	AmountNeededColor string // Color for amount needed
	ProgressBarColor  string // Color for progress bar
	StatusBannerColor string // Color for status banner

	// Display names
	CurrentTierDisplayName  string // Formatted tier name
	PreviousTierDisplayName string // Formatted previous tier name

	// Text content
	StatusBannerText string // Message about AMB status
	AmountStatusText string // "Avoid charges" or "Avoid loss of rewards"
	DisclaimerText   string // Tier-specific disclaimer text
	NoAmbMessage     string // Message for basic tier

	// AMB On Track section content
	AMBOnTrackMainText        string // "Keep it up! Your AMB is on track at ₹28,987"
	AMBOnTrackProTipText      string // Pro tip text
	AMBOnTrackIconURL         string // Checkmark icon URL
	AMBOnTrackLottieURL       string // Lottie animation URL for overlay
	AMBOnTrackTextColor       string // Main text color
	AMBOnTrackProTipTextColor string // Pro tip text color

	// Image URLs
	StatusIconURL     string         // Status icon (check/warning)
	TierBadgeImageURL string         // Tier badge image
	DynamicBanner     *DynamicBanner // Dynamic banner image from FetchDynamicElements API

	// Tier badge gradient
	TierBadgeGradient          *widget.BackgroundColour // Tier badge gradient color
	ProgressBackgroundGradient *widget.BackgroundColour

	// TIER_FI_BASIC specific components
	ShouldShowBasicTierComponents bool // Single flag for both promo banner and CTA section
	ShouldUpsellForNoAmb          bool
	BasicTierUpgradeDeeplink      *deeplink.Deeplink
	// Dynamic tier upgrade information based on AMB
	RecommendedTier     external.Tier
	RecommendedTierName string

	// Section visibility flags
	ShouldShowAmountNeededSection bool
	ShouldShowAnnouncementSection bool
	ShouldShowProgressBar         bool
	ShouldShowNoAmbMessage        bool
	ShouldShowAMBOnTrackMessage   bool
	ShouldShowAMBOnTrackLottie    bool
	ShouldShowSpecialProTip       bool // Whether to show special pro tip based on tier-specific AMB ratio threshold
	TrialsResponse                *tieringPb.GetTrialDetailsResponse

	// Analytics properties (common base for all events)
	BaseAnalyticsProperties map[string]string
}

type DynamicBanner struct {
	Image    *commontypes.VisualElement
	Deeplink *deeplink.Deeplink
}

// GetAnalyticsProperties combines base analytics properties with component-specific context
func (d *AMBScreenData) GetAnalyticsProperties(context AnalyticsComponentContext) map[string]string {
	props := make(map[string]string)
	for k, v := range d.BaseAnalyticsProperties {
		props[k] = v
	}

	// Add component context to properties
	if context.Component != "" {
		props["component"] = context.Component
	}
	if context.SubComponent != "" {
		props["sub_component"] = context.SubComponent
	}
	if context.ActionType != "" {
		props["action_type"] = context.ActionType
	}
	if context.ScreenName != "" {
		props["redirection_screen_name"] = context.ScreenName
	}

	return props
}

// AMBHistoryEntry represents a single entry in the AMB history table
type AMBHistoryEntry struct {
	DateRange     string // e.g. "1 Jan – 15 Jan"
	Tier          string // e.g. "Regular", "Prime"
	AMBMaintained string // e.g. "₹4,865"
	AMBColor      string // Color for the AMB value (e.g., red or black)
}
