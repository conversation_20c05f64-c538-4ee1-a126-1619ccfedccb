package helper

import (
	"fmt"
	"time"

	gmoney "google.golang.org/genproto/googleapis/type/money"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	"github.com/epifi/gamma/frontend/tiering/hierarchy"
	"github.com/epifi/gamma/pkg/tiering"
)

func GetTieringFeEssentialsFromResp(tieringPitchResp *tieringPb.GetTieringPitchV2Response, configParamsResp *tieringPb.GetConfigParamsResponse) (*TieringFeEssentials, error) {
	currTier := tieringPitchResp.GetCurrentTier()
	nextHigherTiers := hierarchy.GetNextHighestTiers(currTier)
	latestMovementDetails := tiering.GetLatestMovementDetails(tieringPitchResp)

	isCurrentTierOptionsPresent := false
	isUserInGrace := false

	var (
		gracePeriodExpiry        time.Time
		currTierMinBalance       *gmoney.Money
		downgradedTierMinBalance *gmoney.Money
		criteriaMinValue         []*CriteriaMinValue
		tierCriteriaMinValuesMap = make(TierCriteriaMinValuesMap)
	)

	mmtDetails := tieringPitchResp.GetMovementDetailsList()
	for _, detail := range mmtDetails {
		criteriaMinValues, _ := GetAllCriteriaMinValuesFromOptions(detail.GetOptions())
		tierCriteriaMinValuesMap[detail.GetTierName()] = criteriaMinValues
		if detail.GetTierName() != currTier {
			continue
		}

		isCurrentTierOptionsPresent = true
		isUserInGrace = !detail.GetIsMovementAllowed()
		gracePeriodExpiry = detail.GetMovementTimestamp().AsTime()
		currTierMinBalance, _ = GetMinBalanceFromOptions(detail.GetOptions())
		criteriaMinValue, _ = GetAllCriteriaMinValuesFromOptions(detail.GetOptions())
	}

	if !isCurrentTierOptionsPresent && !currTier.IsSalaryRelatedTier() {
		return nil, fmt.Errorf("tier options not present for %s tier", currTier)
	}

	isUserInDowngradedWindow := false
	if tieringPitchResp.GetLastDowngradeDetails() != nil && !tieringPitchResp.GetLastDowngradeDetails().GetFromTier().IsSalaryOrSalaryLiteTier() {
		lastDowngradeTimestamp := tieringPitchResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime()
		lastUpgradeTimestamp := tieringPitchResp.GetLastUpgradeDetails().GetMovementTimestamp().AsTime()
		if tieringPitchResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime().After(lastUpgradeTimestamp) &&
			time.Since(lastDowngradeTimestamp) < configParamsResp.GetDowngradeWindowDuration().AsDuration() {
			isUserInDowngradedWindow = true
		}
	}

	isCurrentTierOptionsPresent = false
	previousTier := latestMovementDetails.GetFromTier()
	if tieringPitchResp.GetLastDowngradeDetails() != nil && previousTier != beTieringExtPb.Tier_TIER_UNSPECIFIED {
		for _, detail := range tieringPitchResp.GetMovementDetailsList() {
			if detail.GetTierName() != previousTier {
				continue
			}

			isCurrentTierOptionsPresent = true
			downgradedTierMinBalance, _ = GetMinBalanceFromOptions(detail.GetOptions())
		}

		if !isCurrentTierOptionsPresent && !previousTier.IsSalaryRelatedTier() {
			return nil, fmt.Errorf("tier options not present for %s tier", previousTier.String())
		}
	}

	return &TieringFeEssentials{
		CurrentTier:              tieringPitchResp.GetCurrentTier(),
		PreviousTier:             previousTier,
		NextHigherTiers:          nextHigherTiers,
		IsUserInDowngradedWindow: isUserInDowngradedWindow,
		IsUserInGrace:            isUserInGrace,
		CurrTierMinBal:           currTierMinBalance,
		DowngradedTierMinBal:     downgradedTierMinBalance,
		DowngradeWindowDuration:  configParamsResp.GetDowngradeWindowDuration().AsDuration(),
		GraceWindowDuration:      configParamsResp.GetGraceWindowDuration().AsDuration(),
		GracePeriodExpiry:        gracePeriodExpiry,
		LastMovementTimestamp:    latestMovementDetails.GetMovementTimestamp().AsTime(),
		LastUpgradeDetails:       tieringPitchResp.GetLastUpgradeDetails(),
		LastDowngradeDetails:     tieringPitchResp.GetLastDowngradeDetails(),
		TieringPitchResp:         tieringPitchResp,
		GetConfigParamsResp:      configParamsResp,
		IsMultipleWaysToEnterTieringEnabledForActor: configParamsResp.GetIsMultipleWaysToEnterTieringEnabledForActor(),
		CriteriaMinValues:        criteriaMinValue,
		TierCriteriaMinValuesMap: tierCriteriaMinValuesMap,
	}, nil
}

type TieringFeEssentials struct {
	CurrentTier     beTieringExtPb.Tier
	PreviousTier    beTieringExtPb.Tier
	NextHigherTiers []beTieringExtPb.Tier
	// true if user got downgraded recently - downgraded within DowngradeWindowDuration
	IsUserInDowngradedWindow bool
	IsUserInGrace            bool
	CurrTierMinBal           *gmoney.Money
	DowngradedTierMinBal     *gmoney.Money
	// duration for which last downgraded details should be shown for the user in app
	DowngradeWindowDuration                     time.Duration
	GraceWindowDuration                         time.Duration
	GracePeriodExpiry                           time.Time
	LastMovementTimestamp                       time.Time
	LastUpgradeDetails                          *beTieringExtPb.LatestMovementDetails
	LastDowngradeDetails                        *beTieringExtPb.LatestMovementDetails
	TieringPitchResp                            *tieringPb.GetTieringPitchV2Response
	GetConfigParamsResp                         *tieringPb.GetConfigParamsResponse
	IsMultipleWaysToEnterTieringEnabledForActor bool
	CriteriaMinValues                           []*CriteriaMinValue
	TierCriteriaMinValuesMap                    map[beTieringExtPb.Tier][]*CriteriaMinValue
	IsUSStocksAccountActive                     bool
}

type CriteriaMinValue struct {
	Criteria tieringEnumPb.CriteriaOptionType
	MinValue *gmoney.Money
}

type TierCriteriaMinValuesMap map[beTieringExtPb.Tier][]*CriteriaMinValue

func (t *TieringFeEssentials) GetCurrentTier() beTieringExtPb.Tier {
	if t != nil {
		return t.CurrentTier
	}
	return beTieringExtPb.Tier_TIER_UNSPECIFIED
}

func (t *TieringFeEssentials) GetPreviousTier() beTieringExtPb.Tier {
	if t != nil {
		return t.PreviousTier
	}
	return beTieringExtPb.Tier_TIER_UNSPECIFIED
}

func (t *TieringFeEssentials) GetNextHigherTiers() []beTieringExtPb.Tier {
	if t != nil {
		return t.NextHigherTiers
	}
	return nil
}

func (t *TieringFeEssentials) GetIsUserInDowngradedWindow() bool {
	if t != nil {
		return t.IsUserInDowngradedWindow
	}
	return false
}

func (t *TieringFeEssentials) GetIsUserInGrace() bool {
	if t != nil {
		return t.IsUserInGrace
	}
	return false
}

func (t *TieringFeEssentials) GetCurrTierMinBal() *gmoney.Money {
	if t != nil {
		return t.CurrTierMinBal
	}
	return nil
}

func (t *TieringFeEssentials) GetDowngradedTierMinBal() *gmoney.Money {
	if t != nil {
		return t.DowngradedTierMinBal
	}
	return nil
}

func (t *TieringFeEssentials) GetDowngradeWindowDuration() time.Duration {
	if t != nil {
		return t.DowngradeWindowDuration
	}
	return 0
}

func (t *TieringFeEssentials) GetGraceWindowDuration() time.Duration {
	if t != nil {
		return t.GraceWindowDuration
	}
	return 0
}

func (t *TieringFeEssentials) GetGracePeriodExpiry() time.Time {
	if t != nil {
		return t.GracePeriodExpiry
	}
	return time.Time{}
}

func (t *TieringFeEssentials) GetLastMovementTimestamp() time.Time {
	if t != nil {
		return t.LastMovementTimestamp
	}
	return time.Time{}
}

func (t *TieringFeEssentials) GetLastUpgradeDetails() *beTieringExtPb.LatestMovementDetails {
	if t != nil {
		return t.LastUpgradeDetails
	}
	return nil
}

func (t *TieringFeEssentials) GetLastDowngradeDetails() *beTieringExtPb.LatestMovementDetails {
	if t != nil {
		return t.LastDowngradeDetails
	}
	return nil
}

func (t *TieringFeEssentials) GetTieringPitchResp() *tieringPb.GetTieringPitchV2Response {
	if t != nil {
		return t.TieringPitchResp
	}
	return nil
}

func (t *TieringFeEssentials) GetGetConfigParamsResp() *tieringPb.GetConfigParamsResponse {
	if t != nil {
		return t.GetConfigParamsResp
	}
	return nil
}

func (t *TieringFeEssentials) GetIsMultipleWaysToEnterTieringEnabledForActor() bool {
	if t != nil {
		return t.IsMultipleWaysToEnterTieringEnabledForActor
	}
	return false
}
func (t *TieringFeEssentials) GetCriteriaMinValues() []*CriteriaMinValue {
	if t != nil {
		return t.CriteriaMinValues
	}
	return nil
}

func (t *TieringFeEssentials) GetTierCriteriaMinValuesMap() TierCriteriaMinValuesMap {
	if t != nil {
		return t.TierCriteriaMinValuesMap
	}
	return nil
}

func (t *TieringFeEssentials) GetIsUSStocksAccountActive() bool {
	if t != nil {
		return t.IsUSStocksAccountActive
	}
	return false
}
