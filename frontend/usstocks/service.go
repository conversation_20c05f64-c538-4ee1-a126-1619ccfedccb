package usstocks

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/shopspring/decimal"

	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/money"
	"github.com/epifi/be-common/pkg/retry"
	securitiesPb "github.com/epifi/gamma/api/securities/catalog"

	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	actorPb "github.com/epifi/gamma/api/actor"
	authPb "github.com/epifi/gamma/api/auth"
	bcPb "github.com/epifi/gamma/api/bankcust"
	fitttPb "github.com/epifi/gamma/api/fittt"
	errorsPb "github.com/epifi/gamma/api/frontend/errors"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	dynamicUIElementPb "github.com/epifi/gamma/api/investment/dynamic_ui_element"
	paypb "github.com/epifi/gamma/api/pay"
	payPb "github.com/epifi/gamma/api/pay/internationalfundtransfer"
	preApprovedLoanPb "github.com/epifi/gamma/api/preapprovedloan"
	fitttRulePb "github.com/epifi/gamma/api/rms/manager"
	savingsClientPb "github.com/epifi/gamma/api/savings"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	usersPb "github.com/epifi/gamma/api/user"
	userGroupPb "github.com/epifi/gamma/api/user/group"
	"github.com/epifi/gamma/api/user/onboarding"
	accountManagerPb "github.com/epifi/gamma/api/usstocks/account"
	catalogManagerPb "github.com/epifi/gamma/api/usstocks/catalog"
	orderManagerPb "github.com/epifi/gamma/api/usstocks/order"
	portfolioManagerPb "github.com/epifi/gamma/api/usstocks/portfolio"
	ussRewardsPb "github.com/epifi/gamma/api/usstocks/rewards"
	"github.com/epifi/gamma/frontend/config/genconf"
	tieringData "github.com/epifi/gamma/frontend/tiering/data_collector"
	"github.com/epifi/gamma/frontend/usstocks/activity"
	ussdropoff "github.com/epifi/gamma/frontend/usstocks/dropoff"
	orderDetails "github.com/epifi/gamma/frontend/usstocks/orderdetails"
	"github.com/epifi/gamma/frontend/wire/types"
	"github.com/epifi/gamma/pkg/feature/release"
	releaseGenConf "github.com/epifi/gamma/pkg/feature/release/config/genconf"
	iftPkg "github.com/epifi/gamma/pkg/internationalfundtransfer"
	usStocksPkg "github.com/epifi/gamma/pkg/usstocks"
)

type Service struct {
	usstocksFePb.UnimplementedUSStocksServer
	accountManagerClient accountManagerPb.AccountManagerClient
	config               *genconf.Config
	catalogManagerClient catalogManagerPb.CatalogManagerClient
	// Maintain a separate client for high-throughput streaming use cases so that other use cases are not affected.
	// With a single grpc ClientConn, when long-lived streaming use cases are started (eg. streaming USStocks price),
	// the number of unary messages that can be sent concurrently is reduced, and this affects other unary RPC use-cases.
	// Refer https://docs.google.com/document/d/1AXgVfwdDl-j7x_PmolN-5aD7LYj0FWrLkg22x30cLX8/edit?pli=1#heading=h.lb9ffn6w2ono
	// for a detailed analysis of the issue.
	catalogManagerStreamClient      catalogManagerPb.CatalogManagerClient
	orderManagerClient              orderManagerPb.OrderManagerClient
	portfolioManagerClient          portfolioManagerPb.PortfolioManagerClient
	savingsClient                   savingsClientPb.SavingsClient
	internationalFundTransferClient payPb.InternationalFundTransferClient
	authClient                      authPb.AuthClient
	priceUpdatesRetryStrategy       retry.RetryStrategy
	usersClient                     usersPb.UsersClient
	releaseEvaluator                *release.Evaluator
	abEvaluatorGeneric              *release.ABEvaluator[string]
	userGrpClient                   userGroupPb.GroupClient
	payClient                       paypb.PayClient
	bcClient                        bcPb.BankCustomerServiceClient
	eventBroker                     events.Broker
	dynamicUIElementClient          dynamicUIElementPb.DynamicUIElementServiceClient
	onboardingClient                onboarding.OnboardingClient
	accountActivityIterator         activity.ActivityListIterator
	accountBalanceClient            accountBalancePb.BalanceClient
	preApprovedLoanClient           preApprovedLoanPb.PreApprovedLoanClient
	ussRewardsClient                ussRewardsPb.UssRewardManagerClient
	bottomSheetGenerator            ussdropoff.IBottomSheetGenerator
	upiOnboardingClient             upiOnboardingPb.UpiOnboardingClient
	fitRmsClient                    fitttRulePb.RuleManagerClient
	fitClient                       fitttPb.FitttClient
	a2Form                          iftPkg.A2Form
	buySellDataProvider             orderDetails.BuySellDataProviderImpl
	sipProjectionCalculator         usStocksPkg.ISIPProjectionCalculator
	dataCollector                   tieringData.DataCollector
	actorClient                     actorPb.ActorClient
	securitiesClient                securitiesPb.SecuritiesCatalogClient
}

func NewUSStocksService(
	config *genconf.Config,
	accountManagerClient accountManagerPb.AccountManagerClient,
	internationalFundTransferClient payPb.InternationalFundTransferClient,
	catalogManagerClient catalogManagerPb.CatalogManagerClient,
	catalogManagerStreamClient types.USSCatalogMgrStreamClient,
	orderManagerClient orderManagerPb.OrderManagerClient,
	portfolioManagerClient portfolioManagerPb.PortfolioManagerClient,
	savingsClient savingsClientPb.SavingsClient,
	authClient authPb.AuthClient,
	priceUpdatesRetryStrategy retry.RetryStrategy,
	usersClient usersPb.UsersClient,
	releaseEvaluator *release.Evaluator,
	userGrpClient userGroupPb.GroupClient,
	payClient paypb.PayClient,
	bcClient bcPb.BankCustomerServiceClient,
	eventBroker events.Broker,
	dynamicUIElementClient dynamicUIElementPb.DynamicUIElementServiceClient,
	onboardingClient onboarding.OnboardingClient,
	accountActivityIterator activity.ActivityListIterator,
	accountBalanceClient accountBalancePb.BalanceClient,
	preApprovedLoanClient preApprovedLoanPb.PreApprovedLoanClient,
	ussRewardsClient ussRewardsPb.UssRewardManagerClient,
	bottomSheetGenerator ussdropoff.IBottomSheetGenerator,
	upiOnboardingClient upiOnboardingPb.UpiOnboardingClient,
	fitRmsClient fitttRulePb.RuleManagerClient,
	fitClient fitttPb.FitttClient,
	a2Form iftPkg.A2Form,
	buyDetailsGenerator *orderDetails.BuyDetailsGenerator,
	sellDetailsGenerator *orderDetails.SellDetailsGenerator,
	dataCollector tieringData.DataCollector,
	actorClient actorPb.ActorClient,
	securitiesClient securitiesPb.SecuritiesCatalogClient,
) *Service {
	return &Service{
		accountManagerClient:            accountManagerClient,
		config:                          config,
		internationalFundTransferClient: internationalFundTransferClient,
		catalogManagerClient:            catalogManagerClient,
		catalogManagerStreamClient:      catalogManagerStreamClient,
		orderManagerClient:              orderManagerClient,
		portfolioManagerClient:          portfolioManagerClient,
		savingsClient:                   savingsClient,
		authClient:                      authClient,
		priceUpdatesRetryStrategy:       priceUpdatesRetryStrategy,
		usersClient:                     usersClient,
		releaseEvaluator:                releaseEvaluator,
		userGrpClient:                   userGrpClient,
		payClient:                       payClient,
		bcClient:                        bcClient,
		eventBroker:                     eventBroker,
		dynamicUIElementClient:          dynamicUIElementClient,
		onboardingClient:                onboardingClient,
		accountActivityIterator:         accountActivityIterator,
		accountBalanceClient:            accountBalanceClient,
		preApprovedLoanClient:           preApprovedLoanClient,
		ussRewardsClient:                ussRewardsClient,
		bottomSheetGenerator:            bottomSheetGenerator,
		upiOnboardingClient:             upiOnboardingClient,
		fitRmsClient:                    fitRmsClient,
		fitClient:                       fitClient,
		a2Form:                          a2Form,
		buySellDataProvider: orderDetails.BuySellDataProviderImpl{
			BuyDataProvider:  buyDetailsGenerator,
			SellDataProvider: sellDetailsGenerator,
		},
		sipProjectionCalculator: usStocksPkg.NewSIPProjectionCalculator(
			catalogManagerClient,
			decimal.NewFromFloat(config.USStocks().SIPProjectionConfig().AnnualBankFDRate),
			decimal.NewFromFloat(config.USStocks().SIPProjectionConfig().Nifty50Value),
			accountManagerClient,
		),
		dataCollector:      dataCollector,
		actorClient:        actorClient,
		securitiesClient:   securitiesClient,
		abEvaluatorGeneric: getABEvaluatorOfFeature[string](actorClient, usersClient, userGrpClient, config.ABFeatureReleaseConfig(), func(str string) string { return str }),
	}
}

// getABEvaluatorOfFeature returns an instance of the AB evaluator to perform experiments of type ABExperiment
func getABEvaluatorOfFeature[ABExperiment any](
	actorClient actorPb.ActorClient, userClient usersPb.UsersClient, userGroupClient userGroupPb.GroupClient,
	abFeatureReleaseConf *releaseGenConf.ABFeatureReleaseConfig,
	strToExprFn func(str string) ABExperiment,
) *release.ABEvaluator[ABExperiment] {
	abEvaluator := release.NewABEvaluator[ABExperiment](
		abFeatureReleaseConf,
		release.NewConstraintFactoryImpl(
			release.NewAppVersionConstraint(),
			release.NewStickinessConstraint(),
			release.NewUserGroupConstraint(actorClient, userClient, userGroupClient),
		),
		strToExprFn,
	)

	return abEvaluator
}

var defaultBottomErrView = &errorsPb.ErrorView{
	Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
	Options: &errorsPb.ErrorView_BottomSheetErrorView{
		BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
			Title:    "Order unsuccessful",
			Subtitle: fmt.Sprintf("We were unable to process your order, please try again"),
		}},
}

var dayTradeLimitBreachedBottomErrView = &errorsPb.ErrorView{
	Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
	Options: &errorsPb.ErrorView_BottomSheetErrorView{
		BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
			Title:    "Order unsuccessful",
			Subtitle: fmt.Sprintf("We were unable to process your order as you have breached day trade limit of 3 day trades in past 5 days"),
		}},
}

var insufficientBuyingPowerErrView = &errorsPb.ErrorView{
	Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
	Options: &errorsPb.ErrorView_BottomSheetErrorView{
		BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
			Title:    "Order unsuccessful",
			Subtitle: fmt.Sprintf("We were unable to process your order as you have inusfficient buyin power. Please add funds to your wallet."),
		}},
}

func (s *Service) isCreditFrozen(ctx context.Context, actorId string) (bool, error) {
	savingsAccount, err := s.getSavingsAccountForActor(ctx, actorId)
	if err != nil {
		return false, errors.Wrap(err, "failed to get savings account by actor")
	}

	for _, r := range savingsAccount.GetConstraints().GetRestrictions() {
		if r == savingsClientPb.Restriction_RESTRICTION_CREDIT_FREEZE {
			return true, nil
		}
	}
	return false, nil
}

func (s *Service) hasNoBuyingPower(ctx context.Context, actorId string) (bool, error) {
	res, err := s.accountManagerClient.GetTradingAccountDetails(ctx, &accountManagerPb.GetTradingAccountDetailsRequest{
		Identifier: &accountManagerPb.TradingAccountIdentifier{
			Identifier: &accountManagerPb.TradingAccountIdentifier_ActorId{
				ActorId: actorId,
			},
		},
		Strategy: accountManagerPb.GetTradingAccountDetailsRequest_BEST_EFFORT,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		return false, errors.Wrapf(err, "error getting trading account details for actor id: %s", actorId)
	}
	return money.IsZero(res.GetTradingAccount().GetWalletDetails().GetBuyingPower()), nil
}

func getTechnicalIssueErrorView() *errorsPb.ErrorView {
	return &errorsPb.ErrorView{
		Type: errorsPb.ErrorViewType_BOTTOM_SHEET,
		Options: &errorsPb.ErrorView_BottomSheetErrorView{
			BottomSheetErrorView: &errorsPb.BottomSheetErrorView{
				Title:    "We’re facing a technical issue!",
				Subtitle: "Please try again later.",
				Ctas: []*errorsPb.CTA{
					{
						Type: errorsPb.CTA_DONE,
						Text: "Ok",
					},
				},
			},
		},
	}
}
