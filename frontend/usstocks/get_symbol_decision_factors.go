package usstocks

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/frontend/deeplink"
	"github.com/epifi/gamma/api/frontend/header"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	"github.com/epifi/gamma/api/typesv2/ui"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/frontend/config/genconf"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	"github.com/epifi/gamma/frontend/usstocks/utils"
	usStocksPkg "github.com/epifi/gamma/pkg/usstocks"
	pkgUtils "github.com/epifi/gamma/pkg/usstocks/utils"
)

var (
	commonRecommendationText1 = usstocksUi.GetText("of analysts recommend ", "#606265", commontypes.FontStyle_BODY_3_PARA)
	commonRecommendationText2 = usstocksUi.GetText("the stock", "#606265", commontypes.FontStyle_BODY_3_PARA)
)

const (
	// this is added to handle if opinion is 0.0X% then client library gives error while rendering
	MinPercentageForAnalystOpinion = 0.1
)

// Note: This RPC is exposed to web and doesn't require authentication necessarily

func (s *Service) GetSymbolDecisionFactors(ctx context.Context, req *usstocksFePb.GetSymbolDecisionFactorsRequest) (*usstocksFePb.GetSymbolDecisionFactorsResponse, error) {
	if s.config.USStocks().EnableStockPageUsingSecuritiesService() {
		return s.getSymbolDecisionFactorsV2(ctx, req)
	}
	feResp := &usstocksFePb.GetSymbolDecisionFactorsResponse{RespHeader: &header.ResponseHeader{}}
	stock, err := utils.GetStockById(ctx, s.catalogManagerClient, req.GetStockId())
	if err != nil {
		logger.Error(ctx, "error in while getting symbol decision factor", zap.Error(err), zap.String(logger.STOCK_ID, req.GetStockId()))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return feResp, nil
	}

	var industry *usstocksCatalogPb.MarketCategory
	// TODO(satyam) remove if condition when market category changes would be done for ETF
	if stock.GetStockType() == usstocksCatalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY {
		industry, err = getMarketCategory(ctx, s.catalogManagerClient, stock.GetIndustryId())
		if err != nil {
			logger.Error(ctx, "error in while getting symbol decision factor", zap.Error(err), zap.String(logger.STOCK_ID, req.GetStockId()), zap.String(logger.VENDOR_ID, stock.GetIndustryId()))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
			return feResp, nil
		}
	}
	if stock.GetStockType() == usstocksCatalogPb.StockType_STOCK_TYPE_ETF {
		snapshotResp, snapshotErr := s.catalogManagerClient.GetStocksPriceSnapshotsBySymbols(ctx, &usstocksCatalogPb.GetStocksPriceSnapshotsBySymbolsRequest{StockSymbols: []string{stock.GetSymbol()}})
		if te := epifigrpc.RPCError(snapshotResp, snapshotErr); te != nil {
			if snapshotResp.GetStatus().IsResourceExhausted() {
				logger.Error(ctx, "resource exhausted in GetStocksPriceSnapshotsBySymbols", zap.Error(te), zap.String(logger.STOCK_ID, req.GetStockId()))
				feResp.RespHeader.Status = rpc.StatusResourceExhausted()
				return feResp, nil
			}
			logger.Error(ctx, "error in GetStocksPriceSnapshotsBySymbols", zap.Error(te), zap.String(logger.STOCK_ID, req.GetStockId()))
			feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(te.Error())
			return feResp, nil
		}
		if stock.GetEtfFinancialInfo() == nil {
			stock.EtfFinancialInfo = &usstocksCatalogPb.ETFFinancialInfo{}
		}
		if stock.GetEtfFinancialInfo() != nil {
			stockSnapshotResp, present := snapshotResp.GetStockPriceSnapshots()[stock.GetSymbol()]
			if present {
				stock.EtfFinancialInfo.MarketVolume = stockSnapshotResp.GetDailyBar().GetVolume()
			}
		}
	}

	feResp.RespHeader.Status = rpc.StatusOk()
	platform := req.GetReq().GetAuth().GetDevice().GetPlatform()
	appVersion := req.GetReq().GetAuth().GetDevice().GetAppVersion()
	cards, err := s.getStockDetailCards(ctx, stock, industry, platform, appVersion)
	if err != nil {
		if errors.Is(err, epifierrors.ErrResourceExhausted) {
			logger.Error(ctx, "resource exhausted while fetching stock detail cards", zap.Error(err))
			feResp.RespHeader.Status = rpc.StatusResourceExhaustedWithDebugMsg(err.Error())
			return feResp, nil
		}
		logger.Error(ctx, "error while getting symbol decision factor cards", zap.Error(err), zap.String(logger.STOCK_ID, req.GetStockId()))
		feResp.RespHeader.Status = rpc.StatusInternalWithDebugMsg(err.Error())
		return feResp, nil
	}
	feResp.SymbolDecisionFactorsCards = cards
	return feResp, nil
}

func getMarketCategory(ctx context.Context, catalogManagerClient usstocksCatalogPb.CatalogManagerClient, vendorId string) (*usstocksCatalogPb.MarketCategory, error) {
	getMarketCatResp, err := catalogManagerClient.GetMarketCategories(ctx, &usstocksCatalogPb.GetMarketCategoriesRequest{
		VendorIds: []string{vendorId},
	})
	if te := epifigrpc.RPCError(getMarketCatResp, err); te != nil {
		return nil, fmt.Errorf("error getting market category for vendor id %s:%w", vendorId, te)
	}
	marketCategory, ok := getMarketCatResp.GetMarketCategories()[vendorId]
	if !ok {
		return nil, fmt.Errorf("vendor id not found in market categories: %s", vendorId)
	}
	return marketCategory, nil
}

func (s *Service) getStockDetailCards(ctx context.Context, stock *usstocksCatalogPb.Stock, industry *usstocksCatalogPb.MarketCategory, platform commontypes.Platform, version uint32) ([]*usstocksFePb.SymbolDecisionFactorsCard, error) {
	switch stock.GetStockType() {
	case usstocksCatalogPb.StockType_STOCK_TYPE_INDIVIDUAL_COMPANY:
		cards, err := s.getCardsForCompany(ctx, stock, industry, platform, version)
		if err != nil {
			return nil, errors.Wrap(err, "error in getCardsForCompany")
		}
		return cards, nil
	case usstocksCatalogPb.StockType_STOCK_TYPE_ETF:
		cards, err := s.getCardsForETF(ctx, stock)
		if err != nil {
			return nil, errors.Wrap(err, "error getting ETF cards")
		}
		return cards, nil
	default:
		return nil, fmt.Errorf("invalid stock type, stockType: %v", stock.GetStockType().String())
	}
}

func (s *Service) getCardsForCompany(ctx context.Context, stock *usstocksCatalogPb.Stock, industry *usstocksCatalogPb.MarketCategory, platform commontypes.Platform, version uint32) ([]*usstocksFePb.SymbolDecisionFactorsCard, error) {
	var cards []*usstocksFePb.SymbolDecisionFactorsCard

	sipProjectionWidget, err := s.sipProjectionCalculator.GetWidget(ctx, &usStocksPkg.GetWidgetRequest{StockId: stock.GetId()})
	if err != nil {
		return nil, errors.Wrapf(err, "error getting SIP projection widget for stock id %s", stock.GetId())
	}
	if sipProjectionWidget != nil {
		cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_SipProjectionWidget{
			SipProjectionWidget: sipProjectionWidget,
		}})
	}

	// TODO(Brijesh): Add highlights card when source is finalized
	// cards = append(cards, getHighlightsCard())
	if isAnnouncementsSupported(platform, version, s.config.USStocks().VersionSupport()) {
		announcementCard := getAnnouncementComponent(stock)
		if announcementCard != nil {
			cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_AnnouncementsComponent{
				AnnouncementsComponent: announcementCard,
			}})
		}
	}

	if s.config.USStocks().MorningStarBasedUIFlag().Disable() {
		logger.Info(ctx, "disabling cards for company using morningstar data")
		return cards, nil
	}

	analystOpinionCard := getAnalystOpinionCard(stock)
	if analystOpinionCard != nil {
		cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_AnalystOpinionCard{
			AnalystOpinionCard: analystOpinionCard,
		}})
	}

	analystOpinionCardV2 := getAnalystOpinionCardV2(stock)
	if analystOpinionCardV2 != nil {
		cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_AnalystOpinionCard_V2{
			AnalystOpinionCard_V2: analystOpinionCardV2,
		}})
	}

	financialsCard, err := getFinancialsCard(stock)
	if err != nil {
		return nil, fmt.Errorf("error getting financials card: %w", err)
	}
	if financialsCard != nil {
		cards = append(cards, financialsCard)
	}

	financialRatiosCard, err := getFinancialRatiosCard(stock, industry)
	if err != nil {
		return nil, fmt.Errorf("error getting financial ratios card: %w", err)
	}
	if financialRatiosCard != nil {
		cards = append(cards, financialRatiosCard)
	}

	cards = append(cards,
		&usstocksFePb.SymbolDecisionFactorsCard{
			Card: &usstocksFePb.SymbolDecisionFactorsCard_CompanyDetailsComponent{
				CompanyDetailsComponent: getCompanyDetailsComponent(stock),
			},
		},
	)
	// TODO(Brijesh): Add annual statements card when source is finalized
	// cards = append(cards, getCompanyAnnualStatement("2021-2022", "https://www.google.com"))
	return cards, nil
}

func getAnnouncementComponent(stock *usstocksCatalogPb.Stock) *usstocksFePb.AnnouncementsComponent {
	if stock.GetSymbol() == "AAPL" {
		return &usstocksFePb.AnnouncementsComponent{
			Heading: getTextForString("Announcements", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2, false),
			Announcements: []*usstocksFePb.Announcement{
				{
					Title:    getTextForString("Dividend declared at $1.60 per share for units bought before 23 Jan 2023", usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
					SubTitle: getTextForString("🎉 You qualify for a dividend of $0.07 receivable on 27th Jan 2023.", usstocksUi.DeepMint, commontypes.FontStyle_SUBTITLE_2, false),
					Footer:   getTextForString("27 JAN 2023", usstocksUi.MonochromeSlate, commontypes.FontStyle_OVERLINE_2XS_CAPS, false),
				},
			},
		}
	}

	if stock.GetSymbol() == "TSLA" {
		return &usstocksFePb.AnnouncementsComponent{
			Heading: getTextForString("Announcements", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2, false),
			Announcements: []*usstocksFePb.Announcement{
				{
					Title:    getTextForString("Dividend declared at $1.60 per share for units bought before 23 Jan 2023", usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
					SubTitle: getTextForString("🎉 You qualify for a dividend of $0.07 receivable on 27th Jan 2023.", usstocksUi.DeepMint, commontypes.FontStyle_SUBTITLE_2, false),
					Footer:   getTextForString("27 JAN 2023", usstocksUi.MonochromeSlate, commontypes.FontStyle_OVERLINE_2XS_CAPS, false),
				},
				{
					Title:    getTextForString("Dividend declared at $7.70 per share for units bought before 30 Jan 2023", usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
					SubTitle: getTextForString("🎉 You qualify for a dividend of $0.77 receivable on 31th Jan 2023.", usstocksUi.DeepMint, commontypes.FontStyle_SUBTITLE_2, false),
					Footer:   getTextForString("28 JAN 2023", usstocksUi.MonochromeSlate, commontypes.FontStyle_OVERLINE_2XS_CAPS, false),
				},
			},
		}
	}

	return nil
}

func isAnnouncementsSupported(platform commontypes.Platform, version uint32, support *genconf.VersionSupport) bool {
	switch platform {
	case commontypes.Platform_ANDROID:
		return version >= support.MinAndroidAppVersionToSupportAnnouncementsInSymbolDetails()
	case commontypes.Platform_IOS:
		return version >= support.MinIOSAppVersionToSupportAnnouncementsInSymbolDetails()
	default:
		return false
	}
}

func getCompanyDetailsComponent(stock *usstocksCatalogPb.Stock) *usstocksFePb.CompanyDetailsComponent {
	// add company market cap, HQ, website as display entries
	var displayEntries []*usstocksFePb.DisplayEntry
	if stock.GetCompanyInfo().GetMarketCap().GetMarketCapValue() != nil {
		companyMarketCap := &usstocksFePb.DisplayEntry{
			TitleText: getTextForString("MARKET CAP", "#8D8D8D", commontypes.FontStyle_SUBTITLE_3, false),
			ValueText: getTextForString(money.ToDisplayStringWithSuffixAndPrecision(stock.GetCompanyInfo().GetMarketCap().GetMarketCapValue(), true, true, 2, money.InternationalNumberSystem), usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
		}
		displayEntries = append(displayEntries, companyMarketCap)
	}

	if stock.GetCompanyInfo().GetCompanyAddress().GetCity() != "" {
		companyHQ := &usstocksFePb.DisplayEntry{
			TitleText: getTextForString("LOCATION", "#8D8D8D", commontypes.FontStyle_SUBTITLE_3, false),
			ValueText: getTextForString(stock.GetCompanyInfo().GetCompanyAddress().GetCity(), usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
		}
		displayEntries = append(displayEntries, companyHQ)
	}

	if stock.GetCompanyInfo().GetWebsiteUrl() != "" {
		companyWebsiteURL := &usstocksFePb.DisplayEntry{
			TitleText:         getTextForString("WEBSITE", "#8D8D8D", commontypes.FontStyle_SUBTITLE_3, false),
			ValueText:         getTextForString(getClickableString(stock.GetCompanyInfo().GetWebsiteUrl(), stock.GetStockBasicDetails().GetName().GetShortName()), usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
			ContainsHyperlink: true,
		}
		displayEntries = append(displayEntries, companyWebsiteURL)
	}
	return &usstocksFePb.CompanyDetailsComponent{
		// TODO(Brijesh): Populate short name and standard name correctly from VG in DB
		CompanyNameText: getTextForString(stock.GetStockBasicDetails().GetName().GetShortName(), usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2, false),
		SymbolText:      getTextForString(stock.GetSymbol(), "#8D8D8D", commontypes.FontStyle_SUBTITLE_3, false),
		DescriptionText: getTextForString(stock.GetStockBasicDetails().GetDescription().GetLongDescription(), usstocksUi.Night, commontypes.FontStyle_BODY_S, false),
		DisplayEntries:  displayEntries,
	}
}

func getClickableString(url string, display string) string {
	return fmt.Sprintf("^^%s^^%s^^", url, display)
}

func getHighlightsCard() *usstocksFePb.SymbolDecisionFactorsCard {
	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_HighlightsCard{
			HighlightsCard: &usstocksFePb.CompanyHighlightsCard{
				Title: &ui.IconTextComponent{
					// TODO(Brijesh): Update logo URL
					LeftIcon: &commontypes.Image{ImageUrl: "https://upload.wikimedia.org/wikipedia/commons/a/ab/Apple-logo.png"},
					Texts:    []*commontypes.Text{getTextForString("Highlights", usstocksUi.MonochromeNight, commontypes.FontStyle_SUBTITLE_2, false)},
				},
				Description: getTextForString("Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum Lorem ipsum", usstocksUi.MonochromeNight, commontypes.FontStyle_SUBTITLE_3, false),
			},
		}}
}

func getCompanyAnnualStatement(finicalYearOfStatement string, documentUrl string) *usstocksFePb.SymbolDecisionFactorsCard {
	return &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_CompanyAnnualStatementComponent{
		CompanyAnnualStatementComponent: &usstocksFePb.CompanyAnnualStatementComponent{
			TitleText:   getTextForString(finicalYearOfStatement, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2, false),
			DocumentUrl: documentUrl,
		},
	}}
}

// Buy % = (Buy+Outperform)/(Buy+Outperform+Hold+Underperform+Sell)
// Hold % = (Hold)/(Buy+Outperform+Hold+Underperform+Sell)
// Sell % = (Underperform+Sell)/(Buy+Outperform+Hold+Underperform+Sell)
// No Opinion needs to be excluded from these calculations.

// Returns the card if analyst opinion data is present
// If opinion data is too old, returns nil
func getAnalystOpinionCard(stock *usstocksCatalogPb.Stock) *usstocksFePb.AnalystOpinionCard {
	updatedTime := datetime.DateToTime(stock.GetEstimatesInfo().GetAnalystRecommendations().GetAsOfDate(), datetime.IST)
	if updatedTime.Add(30 * 24 * time.Hour).Before(time.Now()) {
		return nil
	}
	roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage, total := pkgUtils.GetBuyHoldSellAnalystPercentages(stock)
	if total == 0 {
		return nil
	}
	numAnalystsFooter := fmt.Sprintf("By %d independent analyst", total)
	if total > 1 {
		numAnalystsFooter = fmt.Sprintf("By %d independent analysts", total)
	}
	footer := fmt.Sprintf("Updated on %s • %s", updatedTime.Format("02 Jan 2006"), numAnalystsFooter)
	var expectedTargetPrice *ui.IconTextComponent
	targetPrice := stock.GetEstimatesInfo().GetAnalystEstimates().GetTargetPriceEstimates().GetPeriodicTargetPriceEstimates()
	if len(targetPrice) > 0 && (targetPrice[0].GetLow() != 0 || targetPrice[0].GetHigh() != 0) {
		expectedTargetPrice = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				// Expected target price $460.50 - $480.50
				getTextForString("Expected target price ", "#606265", commontypes.FontStyle_BODY_4_PARA, false),
				getTextForString(fmt.Sprintf("$%.2f - $%.2f", targetPrice[0].GetLow(), targetPrice[0].GetHigh()), "#606265", commontypes.FontStyle_NUMBERS_8, false),
			},
		}
	}
	return &usstocksFePb.AnalystOpinionCard{
		CardTitle: getTextForString("Analyst opinion", usstocksUi.MonochromeNight, commontypes.FontStyle_SUBTITLE_2, false),
		Source:    getTextForString("Source: Morningstar", usstocksUi.MonochromeAsh, commontypes.FontStyle_BODY_4_PARA, false),
		Recommendations: []*usstocksFePb.AnalystRecommendation{
			{
				RecommendationName:        "Buy",
				Color:                     "#4E9199",
				Percentage:                roundedBuyPercentage,
				RecommendationDescription: &ui.IconTextComponent{Texts: getRecommendationDescription(int(roundedBuyPercentage), "buying")},
			},
			{
				RecommendationName:        "Hold",
				Color:                     "#CDA428",
				Percentage:                roundedHoldPercentage,
				RecommendationDescription: &ui.IconTextComponent{Texts: getRecommendationDescription(int(roundedHoldPercentage), "holding")},
			},
			{
				RecommendationName:        "Sell",
				Color:                     "#9E5A57",
				Percentage:                roundedSellPercentage,
				RecommendationDescription: &ui.IconTextComponent{Texts: getRecommendationDescription(int(roundedSellPercentage), "selling")},
			},
		},
		Description: expectedTargetPrice,
		Footer:      usstocksUi.GetText(footer, usstocksUi.MonochromeAsh, commontypes.FontStyle_BODY_4_PARA),
	}
}

func getExpectedPriceForAnalystOpinionCard(stock *usstocksCatalogPb.Stock) *ui.IconTextComponent {
	var expectedTargetPrice *ui.IconTextComponent
	targetPrice := stock.GetEstimatesInfo().GetAnalystEstimates().GetTargetPriceEstimates().GetPeriodicTargetPriceEstimates()
	if len(targetPrice) > 0 && (targetPrice[0].GetLow() != 0 || targetPrice[0].GetHigh() != 0) {
		expectedTargetPrice = &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				// Expected target price $460.50 - $480.50
				getTextForString(fmt.Sprintf("Expected target price: %.2f - $%.2f", targetPrice[0].GetLow(), targetPrice[0].GetHigh()), "#6A6D70", commontypes.FontStyle_BODY_S, false),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{Height: 18},
		}
	}
	return expectedTargetPrice
}

func getAnalystOpinionCardV2(stock *usstocksCatalogPb.Stock) *usstocksFePb.AnalystOpinionCardV2 {
	updatedTime := datetime.DateToTime(stock.GetEstimatesInfo().GetAnalystRecommendations().GetAsOfDate(), datetime.IST)
	if updatedTime.Add(30 * 24 * time.Hour).Before(time.Now()) {
		return nil
	}
	roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage, total := pkgUtils.GetBuyHoldSellAnalystPercentages(stock)
	if total == 0 {
		return nil
	}
	updatedOnStr := fmt.Sprintf("Updated on %s", updatedTime.Format("02 Jan 2006"))
	expectedTargetPrice := getExpectedPriceForAnalystOpinionCard(stock)
	recommendationTitle, recommendationBgColor := getRecommendationAccordingToAnalystOpinion(roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage)
	recommendationTextsPair := &ui.VerticalKeyValuePair{
		Title:      recommendationTitle,
		Value:      expectedTargetPrice,
		HAlignment: ui.VerticalKeyValuePairHAlignment_VERTICAL_KEY_VALUE_PAIR_H_ALIGNMENT_LEFT,
	}
	if expectedTargetPrice != nil {
		recommendationTextsPair.VerticalPaddingBtwTitleValue = 8
	}
	return getAnalystOpinionCardV2Component(recommendationTextsPair, recommendationBgColor, updatedOnStr, roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage, total)
}

// nolint:funlen
func getAnalystOpinionCardV2Component(recommendationTextsPair *ui.VerticalKeyValuePair, recommendationBgColor, updatedOnStr string, roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage float32, total int32) *usstocksFePb.AnalystOpinionCardV2 {
	return &usstocksFePb.AnalystOpinionCardV2{
		RecommendationTextsPair:        recommendationTextsPair,
		RecommendationTextsViewBgColor: recommendationBgColor,
		Recommendations:                getRecommendationsForAnalystOpinionV2(roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage),
		AnalystsCount: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString(fmt.Sprintf("%d analysts", total), "#6A6D70", commontypes.FontStyle_SUBTITLE_S, false),
			},
			LeftImgTxtPadding: 4,
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#FFFFFF",
				Height:  24,
			},
			LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/usstocks_images/Users.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			}},
		},
		CardUpdatedOn: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString(updatedOnStr, "#6A6D70", commontypes.FontStyle_SUBTITLE_S, false),
			},
			LeftImgTxtPadding: 4,
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#FFFFFF",
				Height:  24,
			},
			LeftVisualElement: &commontypes.VisualElement{Asset: &commontypes.VisualElement_Image_{
				Image: &commontypes.VisualElement_Image{
					Source: &commontypes.VisualElement_Image_Url{Url: "https://epifi-icons.pointz.in/usstocks_images/Calendar.png"},
					Properties: &commontypes.VisualElementProperties{
						Width:  24,
						Height: 24,
					},
					ImageType: commontypes.ImageType_PNG,
				},
			}},
		},
		RecommendationSource: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString("Source: Morningstar", "#B2B5B9", commontypes.FontStyle_BODY_4_PARA, false),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#FFFFFF",
				Height:  16,
			},
		},
		InfoPopup: &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString("How to use?", "#00B899", commontypes.FontStyle_BODY_4_PARA, false),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				BgColor: "#FFFFFF",
				Height:  16,
			},
			Deeplink: getInfoPopupForAnalystOpinionV2(),
		},
	}
}

func getInfoPopupForAnalystOpinionV2() *deeplink.Deeplink {
	body := make([]*commontypes.Text, 0)
	for _, text := range AnalystRatingsInfoPopBodyText {
		body = append(body, commontypes.GetHtmlText(
			text,
		).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithBgColor(usstocksUi.Ivory))
	}
	return &deeplink.Deeplink{
		Screen: deeplink.Screen_INFORMATION_POPUP,
		ScreenOptions: &deeplink.Deeplink_InformationPopupOptions{
			InformationPopupOptions: &deeplink.InformationPopupOptions{
				IconUrl:      InfoLogoUrl,
				TextSubTitle: commontypes.GetHtmlText(AnalystRatingInfoPopupSubTitle).WithFontStyle(commontypes.FontStyle_SUBTITLE_M).WithBgColor("#FFFFFF"),
				TextTitle:    getTextForString(AnalystRatingsText, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2, false),
				BodyTexts:    body,
				BgColor:      "#FFFFFF",
			},
		},
	}
}

// if the opinion are same percentage then priority of result is BUY > SELL > HOLD
func getRecommendationAccordingToAnalystOpinion(roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage float32) (*ui.IconTextComponent, string) {
	if roundedBuyPercentage >= roundedHoldPercentage && roundedBuyPercentage >= roundedSellPercentage {
		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString(fmt.Sprintf("%.0f%% analysts say Buy", roundedBuyPercentage), "#648E4D", commontypes.FontStyle_SUBTITLE_L, false),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				Height: 24,
			},
		}, "#EDF5EB"
	} else if roundedSellPercentage >= roundedHoldPercentage && roundedSellPercentage >= roundedBuyPercentage {
		return &ui.IconTextComponent{
			Texts: []*commontypes.Text{
				getTextForString(fmt.Sprintf("%.0f%% analysts say Sell", roundedSellPercentage), "#D48647", commontypes.FontStyle_SUBTITLE_L, false),
			},
			ContainerProperties: &ui.IconTextComponent_ContainerProperties{
				Height: 24,
			},
		}, "#FBF3E6"
	}
	return &ui.IconTextComponent{
		Texts: []*commontypes.Text{
			getTextForString(fmt.Sprintf("%.0f%% analysts say Hold", roundedHoldPercentage), "#D2AC3D", commontypes.FontStyle_SUBTITLE_L, false),
		},
		ContainerProperties: &ui.IconTextComponent_ContainerProperties{
			Height: 24,
		},
	}, "#FFFCEB"
}

func getRecommendationsForAnalystOpinionV2(roundedBuyPercentage, roundedHoldPercentage, roundedSellPercentage float32) []*usstocksFePb.AnalystRecommendation {
	res := make([]*usstocksFePb.AnalystRecommendation, 0)
	if isAnalystOpinionGreaterThanThreshold(roundedBuyPercentage) {
		res = append(res,
			&usstocksFePb.AnalystRecommendation{
				Color:      "#648E4D",
				Percentage: roundedBuyPercentage,
				RecommendationDescription: &ui.IconTextComponent{
					Texts: []*commontypes.Text{getTextForString(fmt.Sprintf("Buy %.0f%%", roundedBuyPercentage), "#648E4D", commontypes.FontStyle_SUBTITLE_S, false)},
				},
			})
	}
	if isAnalystOpinionGreaterThanThreshold(roundedHoldPercentage) {
		res = append(res, &usstocksFePb.AnalystRecommendation{
			Color:      "#D2AC3D",
			Percentage: roundedHoldPercentage,
			RecommendationDescription: &ui.IconTextComponent{
				Texts: []*commontypes.Text{getTextForString(fmt.Sprintf("Hold %.0f%%", roundedHoldPercentage), "#D2AC3D", commontypes.FontStyle_SUBTITLE_S, false)},
			},
		})
	}
	if isAnalystOpinionGreaterThanThreshold(roundedSellPercentage) {
		res = append(res,
			&usstocksFePb.AnalystRecommendation{
				Color:      "#D48647",
				Percentage: roundedSellPercentage,
				RecommendationDescription: &ui.IconTextComponent{
					Texts: []*commontypes.Text{getTextForString(fmt.Sprintf("Sell %.0f%%", roundedSellPercentage), "#D48647", commontypes.FontStyle_SUBTITLE_S, false)},
				},
			})
	}
	return res
}
func isAnalystOpinionGreaterThanThreshold(analystRating float32) bool {
	return analystRating >= MinPercentageForAnalystOpinion
}
func getRecommendationDescription(percentage int, recommendation string) []*commontypes.Text {
	return []*commontypes.Text{
		getTextForString(fmt.Sprintf("%d%%", percentage)+" ", "#606265", commontypes.FontStyle_NUMBERS_7, false),
		commonRecommendationText1,
		getTextForString(recommendation+" ", "#606265", commontypes.FontStyle_NUMBERS_7, false),
		commonRecommendationText2,
	}
}
