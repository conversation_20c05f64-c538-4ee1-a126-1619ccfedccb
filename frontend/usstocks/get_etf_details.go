package usstocks

import (
	"context"
	"fmt"
	"math"
	"sort"
	"strconv"
	"strings"

	"github.com/pkg/errors"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/money"
	securitiesPb "github.com/epifi/gamma/api/securities/catalog"

	"github.com/epifi/gamma/api/frontend/deeplink"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	"github.com/epifi/gamma/frontend/deposit/ui"
	"github.com/epifi/gamma/frontend/deposit/utils"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
	usStocksPkg "github.com/epifi/gamma/pkg/usstocks"
)

const (
	totalColumnsForETFInfo = 2
	// minThresholdForHoldingPercentage refers minimum holdingPercentage of a constituent to show separate entry for that percentage
	minThresholdForHoldingPercentage = 1
	// minThresholdToShowOtherCategory refers minimum remaining percentage to show remaining percentage in 'other category'
	minThresholdToShowOtherCategory = 1

	sharpeRatioChartDesc = "Measures an ETF's risk-adjusted returns, indicating how well it performs relative to its level of volatility. Higher is better."
	battingAvgChartDesc  = "Percentage of time an ETF outperforms its benchmark index, indicating its consistency in generating positive returns. Higher is better."
)

var (
	constituentColors = []string{"#C0B7E1", "#BBC8E9", "#C0DAE0", "#EAD8A3", "#EFC0C0", "#D3B250", "#7FBECE", "#879EDB", "#9287BD", "#4F71AB", "#478295"}
)

type holding struct {
	constituentName   string
	holdingPercentage float32
}

func (s *Service) getCardsForETF(ctx context.Context, stock *usstocksCatalogPb.Stock) ([]*usstocksFePb.SymbolDecisionFactorsCard, error) {
	var cards []*usstocksFePb.SymbolDecisionFactorsCard

	sipProjectionWidget, err := s.sipProjectionCalculator.GetWidget(ctx, &usStocksPkg.GetWidgetRequest{StockId: stock.GetId()})
	if err != nil {
		return nil, errors.Wrapf(err, "error getting SIP projection widget for stock id %s", stock.GetId())
	}
	if sipProjectionWidget != nil {
		cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_SipProjectionWidget{
			SipProjectionWidget: sipProjectionWidget,
		}})
	}

	// card for ETF expense ratio, market cap and tracking info
	cards = append(cards, getETFInfoCard(stock)...)

	// card for performance metric, ETF summary and details
	cards = append(cards, getFinancialCard(stock), getETFSummary(stock), getETFDetailsCard(stock))
	return cards, nil
}

func (s *Service) getCardsForETFV2(ctx context.Context, security *securitiesPb.Security, securityListing *securitiesPb.SecurityListing) ([]*usstocksFePb.SymbolDecisionFactorsCard, error) {
	var cards []*usstocksFePb.SymbolDecisionFactorsCard

	sipProjectionWidget, err := s.sipProjectionCalculator.GetWidget(ctx, &usStocksPkg.GetWidgetRequest{StockId: securityListing.GetExternalId()})
	if err != nil {
		return nil, errors.Wrapf(err, "error getting SIP projection widget for stock id %s", securityListing.GetExternalId())
	}
	if sipProjectionWidget != nil {
		cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_SipProjectionWidget{
			SipProjectionWidget: sipProjectionWidget,
		}})
	}

	// card for ETF expense ratio, market cap and tracking info
	cards = append(cards, getETFInfoCardV2(security)...)

	// card for performance metric, ETF summary and details
	cards = append(cards, getETFFinancialCard(security), getETFSummaryV2(security), getETFDetailsCardV2(security, securityListing))
	return cards, nil
}

// nolint:dupl
func getETFInfoCardV2(security *securitiesPb.Security) []*usstocksFePb.SymbolDecisionFactorsCard {
	var cards []*usstocksFePb.SymbolDecisionFactorsCard
	// card for expense ratio
	netExpenseRatio := security.GetFinancialInfo().GetFinancialParameters().GetFundFinancialInfo().GetExpenseRatioPercentage()
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Expense ratio", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("THIS ETF", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(fmt.Sprintf("%.2f", netExpenseRatio)+"%", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Expense ratio", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Annual fee charged by an ETF, expressed as a percentage, covering operating expenses and management costs.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	})

	// card for market cap
	marketCap := security.GetFinancialInfo().GetMarketCap()
	marketVolume := security.GetFinancialInfo().GetFinancialParameters().GetFundFinancialInfo().GetTradingVolumeAverage()
	volumeStr := "-"
	if marketVolume > 0 {
		volumeStr = strconv.FormatFloat(marketVolume, 'f', 0, 64)
	}
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Market info", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("MARKET CAP", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(money.ToDisplayStringWithSuffixAndPrecision(marketCap, true, true, 2, money.InternationalNumberSystem), usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
					{
						TitleText: usstocksUi.GetText("VOLUME", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(volumeStr, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2), // TODO(satyam) return marketVolume in K/M/B suffixes
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Market info", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Market Cap of an ETF: Total value of all its shares outstanding.\n\nVolume of an ETF: Number of ETF units traded during the last trading day.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	})

	// card for tracking info
	trackingIndex := security.GetSecurityDetails().GetFundDetails().GetBenchmarkName()
	if strings.TrimSpace(trackingIndex) == "" {
		trackingIndex = "-"
	}
	etfIndexName, isPresent := eTFIndexNameMapping[strings.TrimSpace(trackingIndex)]
	if isPresent {
		trackingIndex = etfIndexName
	}

	trackingErrOneYr := security.GetFinancialInfo().GetFinancialParameters().GetFundFinancialInfo().GetTrackingErrorPercentage()
	trackingErrStr := "-"
	if trackingErrOneYr != 0 {
		trackingErrStr = fmt.Sprintf("%0.2f", trackingErrOneYr) + "%"
	}
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Tracking info", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("TRACKING INDEX", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(trackingIndex, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
					{
						TitleText: usstocksUi.GetText("1Y ERROR RATE", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(trackingErrStr, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:   utils.InfoIconPrimary,
					TextTitle: ui.GetText("Tracking Error", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Deviation of an ETF's performance from its underlying index due to various factors "+
						"such as fees, rebalancing, and market conditions. The tracking index represents the index which may be considered "+
						"the primary benchmark for that ETF. ", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor: "#FFFFFF",
				},
			},
		},
	})
	return cards
}

func getETFFinancialCard(security *securitiesPb.Security) *usstocksFePb.SymbolDecisionFactorsCard {
	var etfSharpeRatioTabs []*usstocksFePb.DateSeriesBarChartDataPoint

	sharpeRatioOneYrValue := security.GetFinancialInfo().GetFinancialParameters().GetFundFinancialInfo().GetSharpeRatio().GetOneYear()
	if sharpeRatioOneYrValue != 0 {
		etfSharpeRatioTabs = append(etfSharpeRatioTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "1Y",
			Value:        float64(sharpeRatioOneYrValue),
			BarColor:     getSharpeRatioBarUnFocusedColor(sharpeRatioOneYrValue),
			FocusColor:   getSharpeRatioBarFocusedColor(sharpeRatioOneYrValue),
		})
	}

	sharpeRatioThreeYrValue := security.GetFinancialInfo().GetFinancialParameters().GetFundFinancialInfo().GetSharpeRatio().GetThreeYear()
	if sharpeRatioThreeYrValue != 0 {
		etfSharpeRatioTabs = append(etfSharpeRatioTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "3Y",
			Value:        float64(sharpeRatioThreeYrValue),
			BarColor:     getSharpeRatioBarUnFocusedColor(sharpeRatioThreeYrValue),
			FocusColor:   getSharpeRatioBarFocusedColor(sharpeRatioThreeYrValue),
		})
	}

	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_FinancialsCard{
			FinancialsCard: &usstocksFePb.FinancialsCard{
				CardTitle: usstocksUi.GetText("Performance metrics", "#313234", commontypes.FontStyle_SUBTITLE_2),
				FinancialMetricTabs: []*usstocksFePb.FinancialMetricTab{
					{
						MetricName: usstocksUi.GetText("Sharpe ratio", "#606265", commontypes.FontStyle_SUBTITLE_S),
						FinancialMetricCharts: []*usstocksFePb.FinancialMetricChart{
							{
								MetricDataPoints: etfSharpeRatioTabs,
								Footer:           usstocksUi.GetText(sharpeRatioChartDesc, "#606265", commontypes.FontStyle_BODY_XS),
								Legends: []*usstocksFePb.Legend{
									{Text: usstocksUi.GetText("BAD <0", usstocksUi.DarkLemon, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("AVERAGE 0-2", usstocksUi.DarkMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("GOOD >2", usstocksUi.DeepMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
								},
							},
						},
					},
				},
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Performance metrics", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Sharpe Ratio: "+sharpeRatioChartDesc+"\n\n", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			}},
	}
}

func getETFSummaryV2(security *securitiesPb.Security) *usstocksFePb.SymbolDecisionFactorsCard {
	var etfSummaryMetricTab []*usstocksFePb.EtfSummaryMetricTab

	constituentHoldingsForTopStocks := getConstituentHoldingsV2(security.GetSecurityDetails().GetFundDetails().GetEtfHoldings().GetHoldings(), true)
	if len(constituentHoldingsForTopStocks) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Top 10 stocks", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForTopStocks,
		})
	}

	constituentHoldingsForSectors := getConstituentHoldingsV2(security.GetSecurityDetails().GetFundDetails().GetEquitySectorHoldings().GetEquitySectors(), false)
	if len(constituentHoldingsForSectors) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Sectors", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForSectors,
		})
	}

	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfSummaryCard{
			EtfSummaryCard: &usstocksFePb.EtfSummaryCard{
				CardTitle:            usstocksUi.GetText("ETF summary", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
				EtfSummaryMetricTabs: etfSummaryMetricTab,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("ETF summary", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Shows the breakdown of how much the ETF has invested in various companies and sectors.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	}
}

func getConstituentHoldingsV2(holdings map[string]float64, areConstituentCompanies bool) []*usstocksFePb.EtfConstituentHolding {
	var constituentNamePercentageList []*holding
	for constituentName, holdingPercentage := range holdings {
		constituentNamePercentageList = append(constituentNamePercentageList, &holding{
			constituentName:   constituentName,
			holdingPercentage: float32(holdingPercentage),
		})
	}
	sort.Slice(constituentNamePercentageList, func(i, j int) bool {
		return constituentNamePercentageList[i].holdingPercentage > constituentNamePercentageList[j].holdingPercentage
	})
	if areConstituentCompanies {
		// if constituent is company then for this will not hide any value
		return getEtfConstituentHoldings(constituentNamePercentageList, false)
	}
	return getEtfConstituentHoldings(constituentNamePercentageList, true)
}

func getETFDetailsCardV2(security *securitiesPb.Security, securityListing *securitiesPb.SecurityListing) *usstocksFePb.SymbolDecisionFactorsCard {
	etfName := security.GetSecurityDetails().GetFundDetails().GetFundName()
	etfDescription := security.GetSecurityDetails().GetFundDetails().GetFundDescription()
	if strings.TrimSpace(etfDescription) == "" {
		etfDescription = "-"
	}

	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_CompanyDetailsComponent{
			CompanyDetailsComponent: &usstocksFePb.CompanyDetailsComponent{
				CompanyNameText: usstocksUi.GetText(etfName, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				SymbolText:      usstocksUi.GetText(securityListing.GetSymbol(), "#8D8D8D", commontypes.FontStyle_OVERLINE_1),
				DescriptionText: usstocksUi.GetText(etfDescription, "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
				TotalColumns:    1,
			},
		},
	}
}

// nolint:funlen
func getETFInfoCard(stock *usstocksCatalogPb.Stock) []*usstocksFePb.SymbolDecisionFactorsCard {
	var cards []*usstocksFePb.SymbolDecisionFactorsCard
	// card for expense ratio
	netExpenseRatio := stock.GetEtfFinancialInfo().GetExpenseRatio().GetNetExpenseRatio()
	categoryName := stock.GetEtfFinancialInfo().GetCategoryName()
	if strings.TrimSpace(categoryName) == "" {
		categoryName = "-"
	}
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Expense ratio", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("THIS ETF", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(fmt.Sprintf("%.2f", netExpenseRatio)+"%", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
					{
						TitleText: usstocksUi.GetText("CATEGORY", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(categoryName, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Expense ratio", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Annual fee charged by an ETF, expressed as a percentage, covering operating expenses and management costs.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	})

	// card for market cap
	marketCap := stock.GetEtfFinancialInfo().GetMarketCapitalisation()
	marketVolume := stock.GetEtfFinancialInfo().GetMarketVolume()
	volumeStr := "-"
	if marketVolume > 0 {
		volumeStr = strconv.FormatInt(marketVolume, 10)
	}
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Market info", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("MARKET CAP", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(money.ToDisplayStringWithSuffixAndPrecision(marketCap, true, true, 2, money.InternationalNumberSystem), usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
					{
						TitleText: usstocksUi.GetText("VOLUME", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(volumeStr, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2), // TODO(satyam) return marketVolume in K/M/B suffixes
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Market info", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Market Cap of an ETF: Total value of all its shares outstanding.\n\nVolume of an ETF: Number of ETF units traded during the last trading day.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	})

	// card for tracking info
	trackingIndex := stock.GetEtfPerformanceMetrics().GetTrackingDetails().GetTrackingIndex()
	if strings.TrimSpace(trackingIndex) == "" {
		trackingIndex = "-"
	}
	etfIndexName, isPresent := eTFIndexNameMapping[strings.TrimSpace(trackingIndex)]
	if isPresent {
		trackingIndex = etfIndexName
	}

	trackingErrOneYr := stock.GetEtfPerformanceMetrics().GetTrackingDetails().GetTrackingErrorOneYear()
	trackingErrStr := "-"
	if trackingErrOneYr != 0 {
		trackingErrStr = fmt.Sprintf("%0.2f", trackingErrOneYr) + "%"
	}
	cards = append(cards, &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfInfoCard{
			EtfInfoCard: &usstocksFePb.EtfInfoCard{
				Header: usstocksUi.GetText("Tracking info", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				KeyValInfos: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("TRACKING INDEX", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(trackingIndex, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
					{
						TitleText: usstocksUi.GetText("1Y ERROR RATE", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(trackingErrStr, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
					},
				},
				TotalColumns: totalColumnsForETFInfo,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:   utils.InfoIconPrimary,
					TextTitle: ui.GetText("Tracking Error", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Deviation of an ETF's performance from its underlying index due to various factors "+
						"such as fees, rebalancing, and market conditions. The tracking index represents the index which may be considered "+
						"the primary benchmark for that ETF. ", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor: "#FFFFFF",
				},
			},
		},
	})
	return cards
}

func getETFDetailsCard(stock *usstocksCatalogPb.Stock) *usstocksFePb.SymbolDecisionFactorsCard {
	stockName := stock.GetStockBasicDetails().GetName().GetStandardName()
	stockDesc := stock.GetStockBasicDetails().GetDescription().GetLongDescription()
	if strings.TrimSpace(stockDesc) == "" {
		stockDesc = "-"
	}
	managers := stock.GetEtfFinancialInfo().GetManagersDetails().GetManagers()
	var managerNames []string
	for _, manager := range managers {
		managerNames = append(managerNames, manager.GetName())
	}

	managerNamesStr := strings.Join(managerNames, ", ")
	if strings.TrimSpace(managerNamesStr) == "" {
		managerNamesStr = "-"
	}
	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_CompanyDetailsComponent{
			CompanyDetailsComponent: &usstocksFePb.CompanyDetailsComponent{
				CompanyNameText: usstocksUi.GetText(stockName, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
				SymbolText:      usstocksUi.GetText(stock.GetSymbol(), "#8D8D8D", commontypes.FontStyle_OVERLINE_1),
				DescriptionText: usstocksUi.GetText(stockDesc, "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
				DisplayEntries: []*usstocksFePb.DisplayEntry{
					{
						TitleText: usstocksUi.GetText("MANAGERS", "#8D8D8D", commontypes.FontStyle_OVERLINE_XS_CAPS),
						ValueText: usstocksUi.GetText(managerNamesStr, "#333333", commontypes.FontStyle_BODY_3),
					},
				},
				TotalColumns: 1,
			},
		},
	}
}

// nolint:funlen
func getFinancialCard(stock *usstocksCatalogPb.Stock) *usstocksFePb.SymbolDecisionFactorsCard {
	var etfSharpeRatioTabs, etfBattingAvgTabs []*usstocksFePb.DateSeriesBarChartDataPoint

	sharpeRatioOneYrValue := stock.GetEtfPerformanceMetrics().GetSharpeRatio().GetOneYear()
	if sharpeRatioOneYrValue != 0 {
		etfSharpeRatioTabs = append(etfSharpeRatioTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "1Y",
			Value:        float64(sharpeRatioOneYrValue),
			BarColor:     getSharpeRatioBarUnFocusedColor(sharpeRatioOneYrValue),
			FocusColor:   getSharpeRatioBarFocusedColor(sharpeRatioOneYrValue),
		})
	}

	sharpeRatioThreeYrValue := stock.GetEtfPerformanceMetrics().GetSharpeRatio().GetThreeYear()
	if sharpeRatioThreeYrValue != 0 {
		etfSharpeRatioTabs = append(etfSharpeRatioTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "3Y",
			Value:        float64(sharpeRatioThreeYrValue),
			BarColor:     getSharpeRatioBarUnFocusedColor(sharpeRatioThreeYrValue),
			FocusColor:   getSharpeRatioBarFocusedColor(sharpeRatioThreeYrValue),
		})
	}

	sharpeRatioFiveYrValue := stock.GetEtfPerformanceMetrics().GetSharpeRatio().GetFiveYear()
	if sharpeRatioFiveYrValue != 0 {
		etfSharpeRatioTabs = append(etfSharpeRatioTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "5Y",
			Value:        float64(sharpeRatioFiveYrValue),
			BarColor:     getSharpeRatioBarUnFocusedColor(sharpeRatioFiveYrValue),
			FocusColor:   getSharpeRatioBarFocusedColor(sharpeRatioFiveYrValue),
		})
	}

	etfBattingAvgOneYrValue := stock.GetEtfPerformanceMetrics().GetBattingAverage().GetOneYear()
	if sharpeRatioOneYrValue != 0 {
		etfBattingAvgTabs = append(etfBattingAvgTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "1Y",
			Value:        float64(etfBattingAvgOneYrValue),
			BarColor:     getBattingAvgBarUnFocusedColor(etfBattingAvgOneYrValue),
			FocusColor:   getBattingAvgBarFocusedColor(etfBattingAvgOneYrValue),
		})
	}

	etfBattingAvgThreeYrValue := stock.GetEtfPerformanceMetrics().GetBattingAverage().GetThreeYear()
	if etfBattingAvgThreeYrValue != 0 {
		etfBattingAvgTabs = append(etfBattingAvgTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "3Y",
			Value:        float64(etfBattingAvgThreeYrValue),
			BarColor:     getBattingAvgBarUnFocusedColor(etfBattingAvgThreeYrValue),
			FocusColor:   getBattingAvgBarFocusedColor(etfBattingAvgThreeYrValue),
		})
	}

	etfBattingAvgFiveYrValue := stock.GetEtfPerformanceMetrics().GetBattingAverage().GetFiveYear()
	if etfBattingAvgFiveYrValue != 0 {
		etfBattingAvgTabs = append(etfBattingAvgTabs, &usstocksFePb.DateSeriesBarChartDataPoint{
			CategoryName: "5Y",
			Value:        float64(etfBattingAvgFiveYrValue),
			BarColor:     getBattingAvgBarUnFocusedColor(etfBattingAvgFiveYrValue),
			FocusColor:   getBattingAvgBarFocusedColor(etfBattingAvgFiveYrValue),
		})
	}

	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_FinancialsCard{
			FinancialsCard: &usstocksFePb.FinancialsCard{
				CardTitle: usstocksUi.GetText("Performance metrics", "#313234", commontypes.FontStyle_SUBTITLE_2),
				FinancialMetricTabs: []*usstocksFePb.FinancialMetricTab{
					{
						MetricName: usstocksUi.GetText("Sharpe ratio", "#606265", commontypes.FontStyle_SUBTITLE_S),
						FinancialMetricCharts: []*usstocksFePb.FinancialMetricChart{
							{
								MetricDataPoints: etfSharpeRatioTabs,
								Footer:           usstocksUi.GetText(sharpeRatioChartDesc, "#606265", commontypes.FontStyle_BODY_XS),
								Legends: []*usstocksFePb.Legend{
									{Text: usstocksUi.GetText("BAD <0", usstocksUi.DarkLemon, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("AVERAGE 0-2", usstocksUi.DarkMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("GOOD >2", usstocksUi.DeepMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
								},
							},
						},
					},
					{
						MetricName: usstocksUi.GetText("Batting average", "#606265", commontypes.FontStyle_SUBTITLE_S),
						FinancialMetricCharts: []*usstocksFePb.FinancialMetricChart{
							{
								MetricDataPoints: etfBattingAvgTabs,
								Footer:           usstocksUi.GetText(battingAvgChartDesc, "#606265", commontypes.FontStyle_BODY_XS),
								Legends: []*usstocksFePb.Legend{
									{Text: usstocksUi.GetText("BAD <50", usstocksUi.DarkLemon, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("AVERAGE 50-55", usstocksUi.DarkMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
									{Text: usstocksUi.GetText("GOOD >55", usstocksUi.DeepMint, commontypes.FontStyle_OVERLINE_2XS_CAPS)},
								},
							},
						},
					},
				},
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("Performance metrics", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Sharpe Ratio: "+sharpeRatioChartDesc+"\n\n"+"Batting Average: "+battingAvgChartDesc, "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			}},
	}
}

func getETFSummary(stock *usstocksCatalogPb.Stock) *usstocksFePb.SymbolDecisionFactorsCard {
	var etfSummaryMetricTab []*usstocksFePb.EtfSummaryMetricTab

	constituentHoldingsForTopStocks := getConstituentHoldings(stock.GetEtfHoldings().GetHoldings(), true)
	if len(constituentHoldingsForTopStocks) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Top 10 stocks", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForTopStocks,
		})
	}

	constituentHoldingsForSectors := getConstituentHoldings(stock.GetEtfFinancialInfo().GetGlobalEquitySectors().GetEquitySectors(), false)
	if len(constituentHoldingsForSectors) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Sectors", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForSectors,
		})
	}

	constituentHoldingsForInstruments := getInstruments(stock.GetEtfFinancialInfo().GetAssetAllocation())
	if len(constituentHoldingsForInstruments) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Instruments", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForInstruments,
		})
	}

	constituentHoldingsForMarketCap := getMarketCapDistribution(stock.GetEtfFinancialInfo().GetMarketCapDistribution())
	if len(constituentHoldingsForMarketCap) > 0 {
		etfSummaryMetricTab = append(etfSummaryMetricTab, &usstocksFePb.EtfSummaryMetricTab{
			MetricName:             usstocksUi.GetText("Market cap", "#606265", commontypes.FontStyle_SUBTITLE_S),
			EtfConstituentHoldings: constituentHoldingsForMarketCap,
		})
	}

	return &usstocksFePb.SymbolDecisionFactorsCard{
		Card: &usstocksFePb.SymbolDecisionFactorsCard_EtfSummaryCard{
			EtfSummaryCard: &usstocksFePb.EtfSummaryCard{
				CardTitle:            usstocksUi.GetText("ETF summary", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_M),
				EtfSummaryMetricTabs: etfSummaryMetricTab,
				InfoPopupOptions: &deeplink.InformationPopupOptions{
					IconUrl:      utils.InfoIconPrimary,
					TextTitle:    ui.GetText("ETF summary", "#333333", commontypes.FontStyle_SUBTITLE_2),
					TextSubTitle: ui.GetText("Shows the breakdown of how much the ETF has invested in various companies, sectors, instruments and across market capitalization.", "#8D8D8D", commontypes.FontStyle_BODY_3_PARA),
					BgColor:      "#FFFFFF",
				},
			},
		},
	}
}

func getConstituentHoldings(holdings map[string]float32, areConstituentCompanies bool) []*usstocksFePb.EtfConstituentHolding {
	var constituentNamePercentageList []*holding
	for constituentName, holdingPercentage := range holdings {
		// mapping with sector name, if constituentName is a company name then will not modify it
		sectorName, ok := EquitySectorNameMapping[constituentName]
		if ok {
			constituentName = sectorName
		}
		constituentNamePercentageList = append(constituentNamePercentageList, &holding{
			constituentName:   constituentName,
			holdingPercentage: holdingPercentage,
		})
	}
	sort.Slice(constituentNamePercentageList, func(i, j int) bool {
		return constituentNamePercentageList[i].holdingPercentage > constituentNamePercentageList[j].holdingPercentage
	})
	if areConstituentCompanies {
		// if constituent is company then for this will not hide any value
		return getEtfConstituentHoldings(constituentNamePercentageList, false)
	}
	return getEtfConstituentHoldings(constituentNamePercentageList, true)
}

func getInstruments(assetAllocation *usstocksCatalogPb.AssetAllocation) []*usstocksFePb.EtfConstituentHolding {
	var instrumentsNamePercentageList []*holding
	if assetAllocation.GetCashNet() > 0 {
		instrumentsNamePercentageList = append(instrumentsNamePercentageList, &holding{
			constituentName:   "Cash",
			holdingPercentage: assetAllocation.GetCashNet(),
		})
	}
	if assetAllocation.GetBondNet() > 0 {
		instrumentsNamePercentageList = append(instrumentsNamePercentageList, &holding{
			constituentName:   "Bond",
			holdingPercentage: assetAllocation.GetBondNet(),
		})
	}
	if assetAllocation.GetEquityNet() > 0 {
		instrumentsNamePercentageList = append(instrumentsNamePercentageList, &holding{
			constituentName:   "Equity",
			holdingPercentage: assetAllocation.GetEquityNet(),
		})
	}
	if assetAllocation.GetConvertibleNet() > 0 {
		instrumentsNamePercentageList = append(instrumentsNamePercentageList, &holding{
			constituentName:   "Convertible",
			holdingPercentage: assetAllocation.GetConvertibleNet(),
		})
	}
	if assetAllocation.GetOtherNet() > 0 {
		instrumentsNamePercentageList = append(instrumentsNamePercentageList, &holding{
			constituentName:   "Other",
			holdingPercentage: assetAllocation.GetOtherNet(),
		})
	}
	sort.Slice(instrumentsNamePercentageList, func(i, j int) bool {
		return instrumentsNamePercentageList[i].holdingPercentage > instrumentsNamePercentageList[j].holdingPercentage
	})
	return getEtfConstituentHoldings(instrumentsNamePercentageList, true)
}

func getMarketCapDistribution(marketCap *usstocksCatalogPb.EtfMarketCapDistribution) []*usstocksFePb.EtfConstituentHolding {
	var marketCapDistributionList []*holding
	if marketCap.GetGiantLongPercentage() > 0 {
		marketCapDistributionList = append(marketCapDistributionList, &holding{
			constituentName:   "Giant Cap",
			holdingPercentage: marketCap.GetGiantLongPercentage(),
		})
	}
	if marketCap.GetLargeLongPercentage() > 0 {
		marketCapDistributionList = append(marketCapDistributionList, &holding{
			constituentName:   "Large Cap",
			holdingPercentage: marketCap.GetLargeLongPercentage(),
		})
	}
	if marketCap.GetMidLongPercentage() > 0 {
		marketCapDistributionList = append(marketCapDistributionList, &holding{
			constituentName:   "Mid Cap",
			holdingPercentage: marketCap.GetMidLongPercentage(),
		})
	}
	if marketCap.GetSmallLongPercentage() > 0 {
		marketCapDistributionList = append(marketCapDistributionList, &holding{
			constituentName:   "Small Cap",
			holdingPercentage: marketCap.GetSmallLongPercentage(),
		})
	}
	if marketCap.GetMicroLongPercentage() > 0 {
		marketCapDistributionList = append(marketCapDistributionList, &holding{
			constituentName:   "Micro Cap",
			holdingPercentage: marketCap.GetMicroLongPercentage(),
		})
	}
	return getEtfConstituentHoldings(marketCapDistributionList, true)
}

func hideEntryForSmallPercentageValue(hideSmallPercentageValue bool, roundOffPercentage float32) bool {
	if hideSmallPercentageValue && roundOffPercentage < minThresholdForHoldingPercentage {
		// if percentage value is less than minThresholdForHoldingPercentage then will not show separated entry for this,
		return true
	}
	return false
}

// hideSmallPercentageValue hides the constituent and adds it to either the top constituent or to a "other" category constituent.
func getEtfConstituentHoldings(constituentNamePercentageList []*holding, hideSmallPercentageValue bool) []*usstocksFePb.EtfConstituentHolding {

	var constituentHoldings []*usstocksFePb.EtfConstituentHolding
	var otherCategoryHolding *usstocksFePb.EtfConstituentHolding
	var totalPercentage float32
	idx := 0
	for _, holdingVal := range constituentNamePercentageList {
		roundOffPercentage := float32(math.Round(float64(holdingVal.holdingPercentage*100)) / 100)
		if hideEntryForSmallPercentageValue(hideSmallPercentageValue, roundOffPercentage) {
			continue
		}
		totalPercentage += roundOffPercentage

		constituentHolding := &usstocksFePb.EtfConstituentHolding{
			ConstituentName:   usstocksUi.GetText(holdingVal.constituentName, usstocksUi.Night, commontypes.FontStyle_SUBTITLE_S),
			Color:             constituentColors[idx%len(constituentColors)],
			HoldingPercentage: roundOffPercentage,
		}
		idx += 1

		// using 'other' for OtherNet in asset allocation and 'others' for top 10 stocks
		if strings.EqualFold(strings.ToLower(holdingVal.constituentName), "others") ||
			strings.EqualFold(strings.ToLower(holdingVal.constituentName), "other") {
			otherCategoryHolding = constituentHolding
			continue
		}
		constituentHoldings = append(constituentHoldings, constituentHolding)
	}

	remainingPercentage := 100 - totalPercentage
	if remainingPercentage >= minThresholdToShowOtherCategory {
		// if remaining percentage value is higher than >0.01 % then if 'other' category already existed then we will
		// add remaining percentage also in 'other' category's percentage otherwise will create new entry for other category.
		if otherCategoryHolding != nil {
			otherCategoryHolding.HoldingPercentage += remainingPercentage
		} else {
			otherCategoryHolding = &usstocksFePb.EtfConstituentHolding{
				ConstituentName:   usstocksUi.GetText("Other", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_S),
				Color:             constituentColors[idx%len(constituentColors)],
				HoldingPercentage: remainingPercentage,
			}
		}
	} else if len(constituentHoldings) > 0 {
		// if remaining percentage value is very less (<0.01 %) then instead of creating new entry for 'other' we will add this in top most percentage value
		constituentHoldings[0].HoldingPercentage += remainingPercentage
		constituentHoldings[0].HoldingPercentage = float32(math.Round(float64(constituentHoldings[0].GetHoldingPercentage()*100)) / 100)
	}

	if otherCategoryHolding != nil {
		otherCategoryHolding.HoldingPercentage = float32(math.Round(float64(otherCategoryHolding.GetHoldingPercentage()*100)) / 100)
		constituentHoldings = append(constituentHoldings, otherCategoryHolding)
	}
	return constituentHoldings
}

func getSharpeRatioBarFocusedColor(sharpeRatioValue float32) string {
	switch {
	case sharpeRatioValue < 0:
		return usstocksUi.DarkLemon
	case sharpeRatioValue >= 0 && sharpeRatioValue <= 2:
		return usstocksUi.DarkMint
	default:
		return usstocksUi.DeepMint
	}
}

func getSharpeRatioBarUnFocusedColor(sharpeRatioValue float32) string {
	switch {
	case sharpeRatioValue < 0:
		return usstocksUi.DarkLemon
	case sharpeRatioValue >= 0 && sharpeRatioValue <= 2:
		return usstocksUi.DarkMint
	default:
		return usstocksUi.DeepMint
	}
}

func getBattingAvgBarFocusedColor(battingAvgValue float32) string {
	switch {
	case battingAvgValue < 50:
		return usstocksUi.DarkLemon
	case battingAvgValue >= 50 && battingAvgValue <= 55:
		return usstocksUi.DarkMint
	default:
		return usstocksUi.DeepMint
	}
}

func getBattingAvgBarUnFocusedColor(battingAvgValue float32) string {
	switch {
	case battingAvgValue < 50:
		return usstocksUi.DarkLemon
	case battingAvgValue >= 50 && battingAvgValue <= 55:
		return usstocksUi.DarkMint
	default:
		return usstocksUi.DeepMint
	}
}
