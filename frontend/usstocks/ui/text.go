package ui

import commontypes "github.com/epifi/be-common/api/typesv2/common"

const (
	USStocksTabHeader   = "US Stocks"
	USFundsTabHeader    = "US Funds"
	YourStocksTabHeader = "Your Stocks"
	WatchlistTabHeader  = "Watchlist"
	GrossMarginName     = "Gross Margin"
	GrossMarginText     = "Gross Margin is Revenue minus Cost of Goods Sold (COGS). A High Gross Margin indicates low production costs, resulting in larger profits for the company."
	EBITDAMarginName    = "EBITDAMargin"
	EBITDAMarginText    = "EBITDA Margin is expressed as a percentage. You get this by dividing EBITDA by Revenue. A high EBITDA Margin indicates profitability."
	NetMarginName       = "Net Margin"
	NetMarginText       = "Net Margin is a percentage that calculates the profitability of a company by dividing its net income by its revenue. A higher net margin indicates a more profitable company."
	RoeName             = "Roe"
	RoeText             = "ROE shows the profitability of a company against equity. It measures management efficiency & profitability relative to other companies. Higher ROE = more profitable company."
	RotcName            = "Rotc"
	RotcText            = "Return on Total Capital measures how efficiently a company generates returns from its total capital employed. Higher ROTC indicates better capital utilization & management performance."
	DebtToEquityName    = "Debt To Equity"
	DebtToEquityText    = "It shows a company's financial leverage & assesses if it's a worthy investment. A higher D/E indicates that the company has more debt than equity & may be a risky investment."
	EpsGrowthName       = "EPS Growth"
	EpsGrowthText       = "EPS growth compares a company's earnings per share between two time periods; usually past vs. present. Positive growth indicates increased earnings."
	RevenueGrowthName   = "Revenue Growth"
	RevenueGrowthText   = "It's the increase in a company's revenue over time — determined by comparing current revenue to the previous year. An increase in growth indicates rising sales & a positive outlook."
	PriceToEquityName   = "Price To Equity"
	PriceToEquityText   = "Price-to-Earnings (P/E) ratio compares a company's stock price to its earnings per share. Lower P/E = potentially undervalued stock or better value opportunity."
	PriceToBookName     = "Price To Book"
	PriceToBookText     = "Price-to-Book (P/B) ratio compares a company's market value to its book value. Lower P/B = stock trading below book value, potentially undervalued."
	DividendYieldName   = "Dividend Yield"
	DividendYieldText   = "Dividend yield measures annual dividends per share as percentage of stock price. Higher dividend yield = better income returns for shareholders."
	EvToEbitdaName      = "EV to EBITDA"
	EvToEbitdaText      = "EV/EBITDA ratio compares enterprise value to EBITDA. Lower EV/EBITDA = potentially undervalued company relative to operating earnings."
	RevenueName         = "Revenue Name"
	RevenueText         = "Revenue is the company makes from selling products or services. It’s what a lot of the company’s success is measured against."
	NetIncomeName       = "Net Income"
	NetIncomeText       = "Net Income, aka 'net profit', is a company's revenue, with expenses, tax, etc, subtracted from it. It's what the company has left after all the bills get paid."
	EbitdaName          = "EBITDA"
	EbitdaText          = "Earnings Before Interest, Taxes, Depreciation, and Amortization — represent a company’s operating profit. It’s a measure of financial performance by excluding non-operating expenses."
	AssetsName          = "Assets"
	AssetsText          = "A company's financial strength is shown on its balance sheet via its Assets. Current Assets can be easily converted to cash, while Non-current Assets (like land) are illiquid."
	LiabilitiesName     = "Liabilities"
	LiabilitiesText     = "Liabilities are obligations (like a loan/debt) that one company owes to another. Current liabilities get paid off within a year; non-current ones take more than a year."
	FreeCashFlowName    = "Free Cash Flow"
	FreeCashFlowText    = "It measures the amount of cash in-flow or out-flow a company generates during a time period. It shows their ability to generate cash used to make payments."
)

func GetText(title string, color string, style commontypes.FontStyle) *commontypes.Text {
	return &commontypes.Text{
		DisplayValue: &commontypes.Text_PlainString{
			PlainString: title,
		},
		FontColor: color,
		FontStyle: &commontypes.Text_StandardFontStyle{
			StandardFontStyle: style,
		},
	}
}
