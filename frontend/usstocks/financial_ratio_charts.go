package usstocks

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"fmt"
	"sort"

	datePb "google.golang.org/genproto/googleapis/type/date"

	"github.com/epifi/be-common/pkg/datetime"
	usstocksFePb "github.com/epifi/gamma/api/frontend/usstocks"
	types "github.com/epifi/gamma/api/typesv2"
	usstocksCatalogPb "github.com/epifi/gamma/api/usstocks/catalog"
	usstocksUi "github.com/epifi/gamma/frontend/usstocks/ui"
)

type ProfitabilityRatioMetric int

const (
	GrossMargin ProfitabilityRatioMetric = iota + 1
	EbitdaMargin
	NetMargin
)

type EfficiencyRatioMetric int

const (
	ROE EfficiencyRatioMetric = iota + 1
	ROIC
	ROTC
)

type GrowthRatioMetric int

const (
	EPSGrowth GrowthRatioMetric = iota + 1
	RevenueGrowth
)

type ValuationRatioMetric int

const (
	PriceToEPSRatio ValuationRatioMetric = iota + 1
	PriceToBookRatio
	DividendYield
	EVToEBITDA
	PriceToEarningsRatio
)

func getFinancialRatiosCard(stock *usstocksCatalogPb.Stock, marketCat *usstocksCatalogPb.MarketCategory) (*usstocksFePb.SymbolDecisionFactorsCard, error) {
	profitabilityRatioTabs, err := getProfitabilityRatioTabs(stock, marketCat)
	if err != nil {
		return nil, fmt.Errorf("error getting profitability ratio tabs: %w", err)
	}

	effRatioTabs, err := getEfficiencyRatioTabs(stock, marketCat)
	if err != nil {
		return nil, fmt.Errorf("error getting efficiency ratio tabs: %w", err)
	}

	finHealthRatioTabs, err := getFinancialHealthRatioTabs(stock, marketCat)
	if err != nil {
		return nil, fmt.Errorf("error getting financial health ratio tabs: %w", err)
	}

	growthRatioTabs, err := getGrowthRatioTabs(stock, marketCat)
	if err != nil {
		return nil, fmt.Errorf("error getting growth ratio tabs: %w", err)
	}

	/*
		TODO(Brijesh): Enable after fixing date received from VG
		valuationRatioTabs, err := getValuationRatioTabs(stock, marketCat)
		if err != nil {
			return nil, fmt.Errorf("error getting valuation ratio tabs: %w", err)
		}
	*/

	return &usstocksFePb.SymbolDecisionFactorsCard{Card: &usstocksFePb.SymbolDecisionFactorsCard_FinancialRatiosCard{
		FinancialRatiosCard: &usstocksFePb.FinancialRatiosCard{
			CardTitle:          usstocksUi.GetText("Ratios", usstocksUi.Night, commontypes.FontStyle_SUBTITLE_2),
			FinancialRatioTabs: append(profitabilityRatioTabs, append(effRatioTabs, append(finHealthRatioTabs, growthRatioTabs...)...)...),
		}},
	}, nil
}

// nolint: funlen
func getProfitabilityRatioTabs(stock *usstocksCatalogPb.Stock, marketCat *usstocksCatalogPb.MarketCategory) ([]*usstocksFePb.FinancialRatioTab, error) {
	var (
		gmQuarterly, gmYearly, emQuarterly, emYearly, nmQuarterly, nmYearly []*usstocksFePb.DateSeriesLineChartData
		err                                                                 error
	)

	companyName := stock.GetStockBasicDetails().GetName().GetShortName()

	stockQuarterlyProfitabilityRatios := stock.GetFinancialInfo().GetQuarterlyProfitabilityRatios()
	marketCatQuarterlyProfitabilityRatios := marketCat.GetFinancialInfo().GetQuarterlyProfitabilityRatios()
	if len(stockQuarterlyProfitabilityRatios) != 0 {
		gmQuarterly, err = getProfitabilityRatios(stockQuarterlyProfitabilityRatios, companyName,
			marketCatQuarterlyProfitabilityRatios, GrossMargin, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly gross margin: %w", err)
		}
		emQuarterly, err = getProfitabilityRatios(stockQuarterlyProfitabilityRatios, companyName,
			marketCatQuarterlyProfitabilityRatios, EbitdaMargin, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly ebitda margin: %w", err)
		}
		nmQuarterly, err = getProfitabilityRatios(stockQuarterlyProfitabilityRatios, companyName,
			marketCatQuarterlyProfitabilityRatios, NetMargin, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly net margin: %w", err)
		}
	}

	stockYearlyProfitabilityRatios := stock.GetFinancialInfo().GetYearlyProfitabilityRatios()
	marketCatYearlyProfitabilityRatios := marketCat.GetFinancialInfo().GetYearlyProfitabilityRatios()
	if len(stockYearlyProfitabilityRatios) != 0 {
		gmYearly, err = getProfitabilityRatios(stockYearlyProfitabilityRatios, companyName,
			marketCatYearlyProfitabilityRatios, GrossMargin, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly gross margin: %w", err)
		}
		emYearly, err = getProfitabilityRatios(stockYearlyProfitabilityRatios, companyName,
			marketCatYearlyProfitabilityRatios, EbitdaMargin, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly ebitda margin: %w", err)
		}
		nmYearly, err = getProfitabilityRatios(stockYearlyProfitabilityRatios, companyName,
			marketCatYearlyProfitabilityRatios, NetMargin, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly net margin: %w", err)
		}
	}

	var tabs []*usstocksFePb.FinancialRatioTab
	gmTab := getFinancialRatioTab(
		"Gross Margin",
		"Gross Margin is Revenue minus Cost of Goods Sold (COGS). A High Gross Margin indicates low production costs, resulting in larger profits for the company.",
		gmQuarterly,
		gmYearly,
	)
	if gmTab != nil {
		tabs = append(tabs, gmTab)
	}

	emTab := getFinancialRatioTab(
		"EBITDA Margin",
		"EBITDA Margin is expressed as a percentage. You get this by dividing EBITDA by Revenue. A high EBITDA Margin indicates profitability.",
		emQuarterly,
		emYearly,
	)
	if emTab != nil {
		tabs = append(tabs, emTab)
	}

	nmTab := getFinancialRatioTab(
		"Net Margin",
		"Net Margin is a percentage that calculates the profitability of a company by dividing its net income by its revenue. A higher net margin indicates a more profitable company.",
		nmQuarterly,
		nmYearly,
	)
	if nmTab != nil {
		tabs = append(tabs, nmTab)
	}
	return tabs, nil
}

func getProfitabilityRatios(stockProfRatio []*usstocksCatalogPb.ProfitabilityRatio,
	companyName string, marketCatProfRatio []*usstocksCatalogPb.ProfitabilityRatio,
	metric ProfitabilityRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartData, error) {
	stockDataPoints, err := getProfitabilityRatiosDataPoints(stockProfRatio, metric, period)
	if err != nil {
		return nil, fmt.Errorf("error getting stock data points: %w", err)
	}
	marketCatDataPoints, err := getProfitabilityRatiosDataPoints(marketCatProfRatio, metric, period)
	if err != nil {
		return nil, fmt.Errorf("error getting market category data points: %w", err)
	}

	return getDateSeriesLineChartData(companyName, stockDataPoints, marketCatDataPoints), nil
}

func getProfitabilityRatiosDataPoints(profRatios []*usstocksCatalogPb.ProfitabilityRatio, metric ProfitabilityRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	var dataPoints []*usstocksFePb.DateSeriesLineChartDataPoint

	sort.SliceStable(profRatios, func(i, j int) bool {
		if profRatios[i].GetReportDate() != nil && profRatios[j].GetReportDate() != nil {
			return datetime.DateToTimeV2(profRatios[i].GetReportDate(), nil).
				Before(datetime.DateToTimeV2(profRatios[j].GetReportDate(), nil))
		}
		return datetime.DateToTimeV2(profRatios[i].GetPeriodEndingDate(), nil).
			Before(datetime.DateToTimeV2(profRatios[j].GetPeriodEndingDate(), nil))
	})

	for _, val := range profRatios {
		var value float64
		switch metric {
		case GrossMargin:
			value = val.GetGrossMargin()
		case EbitdaMargin:
			value = val.GetEbitdaMargin()
		case NetMargin:
			value = val.GetNetMargin()
		default:
			return nil, fmt.Errorf("invalid datapoint type")
		}

		dataPoint, err := getLineChartDataPoint(value, []*datePb.Date{val.GetReportDate(), val.GetPeriodEndingDate()}, period)
		if err != nil {
			return nil, fmt.Errorf("error getting data point: %w", err)
		}
		if dataPoint == nil {
			continue
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints, nil
}

func getEfficiencyRatioTabs(stock *usstocksCatalogPb.Stock, marketCat *usstocksCatalogPb.MarketCategory) ([]*usstocksFePb.FinancialRatioTab, error) {
	var (
		roeQuarterly, roeYearly, roicQuarterly, roicYearly []*usstocksFePb.DateSeriesLineChartData
		err                                                error
	)
	companyName := stock.GetStockBasicDetails().GetName().GetShortName()

	stockQuarterlyEfficiencyRatios := stock.GetFinancialInfo().GetQuarterlyEfficiencyRatios()
	marketCatQuarterlyEfficiencyRatios := marketCat.GetFinancialInfo().GetQuarterlyEfficiencyRatios()
	if len(stockQuarterlyEfficiencyRatios) != 0 {
		roeQuarterly, err = getEfficiencyRatios(stockQuarterlyEfficiencyRatios, companyName,
			marketCatQuarterlyEfficiencyRatios, ROE, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly roe: %w", err)
		}
		roicQuarterly, err = getEfficiencyRatios(stockQuarterlyEfficiencyRatios, companyName,
			marketCatQuarterlyEfficiencyRatios, ROIC, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly roic: %w", err)
		}
	}

	stockYearlyEfficiencyRatios := stock.GetFinancialInfo().GetYearlyEfficiencyRatios()
	marketCatYearlyEfficiencyRatios := marketCat.GetFinancialInfo().GetYearlyEfficiencyRatios()
	if len(stockYearlyEfficiencyRatios) != 0 {
		roeYearly, err = getEfficiencyRatios(stockYearlyEfficiencyRatios, companyName,
			marketCatYearlyEfficiencyRatios, ROE, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly roe: %w", err)
		}

		roicYearly, err = getEfficiencyRatios(stockYearlyEfficiencyRatios, companyName,
			marketCatYearlyEfficiencyRatios, ROIC, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly roic: %w", err)
		}
	}

	var tabs []*usstocksFePb.FinancialRatioTab
	roeTab := getFinancialRatioTab(
		"ROE",
		"ROE shows the profitability of a company against equity. It measures management efficiency & profitability relative to other companies. Higher ROE = more profitable company.",
		roeQuarterly,
		roeYearly,
	)
	if roeTab != nil {
		tabs = append(tabs, roeTab)
	}

	roicTab := getFinancialRatioTab(
		"ROIC",
		"It shows a company's efficiency in generating profits from its invested capital. It's calculated by dividing net operating income by total capital. High ROIC = High efficiency.",
		roicQuarterly,
		roicYearly,
	)
	if roicTab != nil {
		tabs = append(tabs, roicTab)
	}
	return tabs, nil
}

func getEfficiencyRatios(stockEffRatio []*usstocksCatalogPb.EfficiencyRatio,
	companyName string, marketCatEffRatio []*usstocksCatalogPb.EfficiencyRatio,
	metric EfficiencyRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartData, error) {
	stockDataPoints, err := getEfficiencyRatiosDataPoints(stockEffRatio, metric, period)
	if err != nil {
		return nil, fmt.Errorf("error getting stock data points: %w", err)
	}
	marketCatDataPoints, err := getEfficiencyRatiosDataPoints(marketCatEffRatio, metric, period)
	if err != nil {
		return nil, fmt.Errorf("error getting market category data points: %w", err)
	}

	return getDateSeriesLineChartData(companyName, stockDataPoints, marketCatDataPoints), nil
}

// nolint:dupl
func getEfficiencyRatiosDataPoints(effRatios []*usstocksCatalogPb.EfficiencyRatio, metric EfficiencyRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	var dataPoints []*usstocksFePb.DateSeriesLineChartDataPoint

	sort.SliceStable(effRatios, func(i, j int) bool {
		if effRatios[i].GetReportDate() != nil && effRatios[j].GetReportDate() != nil {
			return datetime.DateToTimeV2(effRatios[i].GetReportDate(), nil).
				Before(datetime.DateToTimeV2(effRatios[j].GetReportDate(), nil))
		}
		return datetime.DateToTimeV2(effRatios[i].GetPeriodEndingDate(), nil).
			Before(datetime.DateToTimeV2(effRatios[j].GetPeriodEndingDate(), nil))
	})

	for _, val := range effRatios {
		var value float64
		switch metric {
		case ROE:
			value = val.GetRoe()
		case ROIC:
			value = val.GetRoic()
		default:
			return nil, fmt.Errorf("invalid datapoint type")
		}

		dataPoint, err := getLineChartDataPoint(value, []*datePb.Date{val.GetReportDate(), val.GetPeriodEndingDate()}, period)
		if err != nil {
			return nil, fmt.Errorf("error getting data point: %w", err)
		}
		if dataPoint == nil {
			continue
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints, nil
}

func getFinancialHealthRatioTabs(stock *usstocksCatalogPb.Stock,
	marketCat *usstocksCatalogPb.MarketCategory) ([]*usstocksFePb.FinancialRatioTab, error) {
	var (
		debtToEquityQuarterly, debtToEquityYearly []*usstocksFePb.DateSeriesLineChartData
		err                                       error
	)

	companyName := stock.GetStockBasicDetails().GetName().GetShortName()

	stockQuarterlyFinancialHealthRatios := stock.GetFinancialInfo().GetQuarterlyFinancialHealthRatios()
	marketCatQuarterlyFinancialHealthRatios := marketCat.GetFinancialInfo().GetQuarterlyFinancialHealthRatios()
	if len(stockQuarterlyFinancialHealthRatios) != 0 {
		debtToEquityQuarterly, err = getFinancialHealthRatios(stockQuarterlyFinancialHealthRatios, companyName,
			marketCatQuarterlyFinancialHealthRatios, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly debt to equity: %w", err)
		}
	}

	stockYearlyFinancialHealthRatios := stock.GetFinancialInfo().GetYearlyFinancialHealthRatios()
	marketCatYearlyFinancialHealthRatios := marketCat.GetFinancialInfo().GetYearlyFinancialHealthRatios()
	if len(stockYearlyFinancialHealthRatios) != 0 {
		debtToEquityYearly, err = getFinancialHealthRatios(stockYearlyFinancialHealthRatios, companyName,
			marketCatYearlyFinancialHealthRatios, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly debt to equity: %w", err)
		}
	}

	var tabs []*usstocksFePb.FinancialRatioTab
	dteTab := getFinancialRatioTab(
		"Debt To Equity",
		"It shows a company's financial leverage & assesses if it's a worthy investment. A higher D/E indicates that the company has more debt than equity & may be a risky investment.",
		debtToEquityQuarterly,
		debtToEquityYearly,
	)
	if dteTab != nil {
		tabs = append(tabs, dteTab)
	}
	return tabs, nil
}

func getFinancialHealthRatios(stockFinHealthRatio []*usstocksCatalogPb.FinancialHealthRatio,
	companyName string, marketCatFinHealthRatio []*usstocksCatalogPb.FinancialHealthRatio,
	period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartData, error) {
	stockDataPoints, err := getFinancialHealthRatiosDataPoints(stockFinHealthRatio, period)
	if err != nil {
		return nil, fmt.Errorf("error getting stock data points: %w", err)
	}
	marketCatDataPoints, err := getFinancialHealthRatiosDataPoints(marketCatFinHealthRatio, period)
	if err != nil {
		return nil, fmt.Errorf("error getting market category data points: %w", err)
	}

	return getDateSeriesLineChartData(companyName, stockDataPoints, marketCatDataPoints), nil
}

// nolint: dupl
func getFinancialHealthRatiosDataPoints(financialHealthRatios []*usstocksCatalogPb.FinancialHealthRatio, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	var dataPoints []*usstocksFePb.DateSeriesLineChartDataPoint

	sort.SliceStable(financialHealthRatios, func(i, j int) bool {
		if financialHealthRatios[i].GetReportDate() != nil && financialHealthRatios[j].GetReportDate() != nil {
			return datetime.DateToTimeV2(financialHealthRatios[i].GetReportDate(), nil).
				Before(datetime.DateToTimeV2(financialHealthRatios[j].GetReportDate(), nil))
		}
		return datetime.DateToTimeV2(financialHealthRatios[i].GetPeriodEndingDate(), nil).
			Before(datetime.DateToTimeV2(financialHealthRatios[j].GetPeriodEndingDate(), nil))
	})

	for _, val := range financialHealthRatios {
		dataPoint, err := getLineChartDataPoint(val.GetTotalDebtToEquity(), []*datePb.Date{val.GetReportDate(), val.GetPeriodEndingDate()}, period)
		if err != nil {
			return nil, fmt.Errorf("error getting data point: %w", err)
		}
		if dataPoint == nil {
			continue
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints, nil
}

func getGrowthRatioTabs(stock *usstocksCatalogPb.Stock, marketCat *usstocksCatalogPb.MarketCategory) ([]*usstocksFePb.FinancialRatioTab, error) {
	var (
		epsQuarterly, revenueQuarterly, epsYearly, revenueYearly []*usstocksFePb.DateSeriesLineChartData
		err                                                      error
	)

	companyName := stock.GetStockBasicDetails().GetName().GetShortName()

	stockQuarterlyGrowthRatios := stock.GetFinancialInfo().GetQuarterlyGrowthRatios()
	marketCatQuarterlyGrowthRatios := marketCat.GetFinancialInfo().GetQuarterlyGrowthRatios()
	if len(stockQuarterlyGrowthRatios) != 0 {
		epsQuarterly, err = getGrowthRatios(stockQuarterlyGrowthRatios, companyName,
			marketCatQuarterlyGrowthRatios, EPSGrowth, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly equity per share growth: %w", err)
		}
		revenueQuarterly, err = getGrowthRatios(stockQuarterlyGrowthRatios, companyName,
			marketCatQuarterlyGrowthRatios, RevenueGrowth, Quarterly)
		if err != nil {
			return nil, fmt.Errorf("error getting quarterly revenue growth: %w", err)
		}
	}

	stockYearlyFinancialHealthRatios := stock.GetFinancialInfo().GetYearlyGrowthRatios()
	marketCatYearlyFinancialHealthRatios := marketCat.GetFinancialInfo().GetYearlyGrowthRatios()
	if len(stockYearlyFinancialHealthRatios) != 0 {
		epsYearly, err = getGrowthRatios(stockYearlyFinancialHealthRatios, companyName,
			marketCatYearlyFinancialHealthRatios, EPSGrowth, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly equity per share growth: %w", err)
		}
		revenueYearly, err = getGrowthRatios(stockYearlyFinancialHealthRatios, companyName,
			marketCatYearlyFinancialHealthRatios, RevenueGrowth, Yearly)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly revenue growth: %w", err)
		}
	}

	var tabs []*usstocksFePb.FinancialRatioTab
	epsTab := getFinancialRatioTab(
		"EPS Growth",
		"EPS growth compares a company's earnings per share between two time periods; usually past vs. present. Positive growth indicates increased earnings.",
		epsQuarterly,
		epsYearly,
	)
	if epsTab != nil {
		tabs = append(tabs, epsTab)
	}

	revenueTab := getFinancialRatioTab(
		"Revenue Growth",
		"It's the increase in a company's revenue over time — determined by comparing current revenue to the previous year. An increase in growth indicates rising sales & a positive outlook.",
		revenueQuarterly,
		revenueYearly,
	)
	if revenueTab != nil {
		tabs = append(tabs, revenueTab)
	}

	return tabs, nil
}

func getGrowthRatios(stockGrowthRatio []*usstocksCatalogPb.GrowthRatio,
	companyName string, marketCatGrowthRatio []*usstocksCatalogPb.GrowthRatio,
	datapointType GrowthRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartData, error) {
	stockDataPoints, err := getGrowthRatiosDataPoints(stockGrowthRatio, datapointType, period)
	if err != nil {
		return nil, fmt.Errorf("error getting stock data points: %w", err)
	}
	marketCatDataPoints, err := getGrowthRatiosDataPoints(marketCatGrowthRatio, datapointType, period)
	if err != nil {
		return nil, fmt.Errorf("error getting market category data points: %w", err)
	}

	return getDateSeriesLineChartData(companyName, stockDataPoints, marketCatDataPoints), nil
}

// nolint:dupl
func getGrowthRatiosDataPoints(growthRatios []*usstocksCatalogPb.GrowthRatio, metric GrowthRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	var dataPoints []*usstocksFePb.DateSeriesLineChartDataPoint

	sort.SliceStable(growthRatios, func(i, j int) bool {
		if growthRatios[i].GetReportDate() != nil && growthRatios[j].GetReportDate() != nil {
			return datetime.DateToTimeV2(growthRatios[i].GetReportDate(), nil).
				Before(datetime.DateToTimeV2(growthRatios[j].GetReportDate(), nil))
		}
		return datetime.DateToTimeV2(growthRatios[i].GetPeriodEndingDate(), nil).
			Before(datetime.DateToTimeV2(growthRatios[j].GetPeriodEndingDate(), nil))
	})

	for _, val := range growthRatios {
		var value float64
		switch metric {
		case EPSGrowth:
			value = val.GetDilutedEpsGrowth()
		case RevenueGrowth:
			value = val.GetRevenueGrowth()
		default:
			return nil, fmt.Errorf("invalid datapoint type")
		}

		dataPoint, err := getLineChartDataPoint(value, []*datePb.Date{val.GetReportDate(), val.GetPeriodEndingDate()}, period)
		if err != nil {
			return nil, fmt.Errorf("error getting data point: %w", err)
		}
		if dataPoint == nil {
			continue
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints, nil
}

// nolint: unused
func getValuationRatioTabs(stock *usstocksCatalogPb.Stock,
	marketCat *usstocksCatalogPb.MarketCategory) ([]*usstocksFePb.FinancialRatioTab, error) {
	var (
		p2eYearly, p2bYearly, dyYearly, ev2ebitdaYearly []*usstocksFePb.DateSeriesLineChartData
		err                                             error
	)
	companyName := stock.GetStockBasicDetails().GetName().GetShortName()

	stockYearlyValuationRatios := stock.GetFinancialInfo().GetYearlyValuationRatios()
	marketCatYearlyValuationRatios := marketCat.GetFinancialInfo().GetYearlyValuationRatios()
	if len(stockYearlyValuationRatios) != 0 {
		p2eYearly, err = getValuationRatios(stockYearlyValuationRatios, companyName,
			marketCatYearlyValuationRatios, PriceToEPSRatio)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly price to eps: %w", err)
		}
		p2bYearly, err = getValuationRatios(stockYearlyValuationRatios, companyName,
			marketCatYearlyValuationRatios, PriceToBookRatio)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly price to book: %w", err)
		}
		dyYearly, err = getValuationRatios(stockYearlyValuationRatios, companyName,
			marketCatYearlyValuationRatios, DividendYield)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly dividend yield: %w", err)
		}
		ev2ebitdaYearly, err = getValuationRatios(stockYearlyValuationRatios, companyName,
			marketCatYearlyValuationRatios, EVToEBITDA)
		if err != nil {
			return nil, fmt.Errorf("error getting yearly ev to ebitda: %w", err)
		}
	}

	var tabs []*usstocksFePb.FinancialRatioTab
	p2eTab := getFinancialRatioTab("Price to Equity", "", nil, p2eYearly)
	if p2eTab != nil {
		tabs = append(tabs, p2eTab)
	}
	p2bTab := getFinancialRatioTab("Price to Book", "", nil, p2bYearly)
	if p2bTab != nil {
		tabs = append(tabs, p2bTab)
	}
	dyTab := getFinancialRatioTab("Dividend Yield", "", nil, dyYearly)
	if dyTab != nil {
		tabs = append(tabs, dyTab)
	}
	evToEbitdaTab := getFinancialRatioTab("EV to EBITDA", "", nil, ev2ebitdaYearly)
	if evToEbitdaTab != nil {
		tabs = append(tabs, evToEbitdaTab)
	}
	return tabs, nil
}

// nolint: unused
func getValuationRatios(stockValuationRatio []*usstocksCatalogPb.ValuationRatio,
	companyName string, marketCatValuationRatio []*usstocksCatalogPb.ValuationRatio,
	datapointType ValuationRatioMetric) ([]*usstocksFePb.DateSeriesLineChartData, error) {
	stockDataPoints, err := getValuationRatiosDataPoints(stockValuationRatio, datapointType, Yearly)
	if err != nil {
		return nil, fmt.Errorf("error getting stock data points: %w", err)
	}
	marketCatDataPoints, err := getValuationRatiosDataPoints(marketCatValuationRatio, datapointType, Yearly)
	if err != nil {
		return nil, fmt.Errorf("error getting market category data points: %w", err)
	}

	return getDateSeriesLineChartData(companyName, stockDataPoints, marketCatDataPoints), nil
}

// nolint: unused
func getValuationRatiosDataPoints(valuationRatios []*usstocksCatalogPb.ValuationRatio, metric ValuationRatioMetric, period ReportingPeriod) ([]*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	var dataPoints []*usstocksFePb.DateSeriesLineChartDataPoint

	sort.SliceStable(valuationRatios, func(i, j int) bool {
		return datetime.DateToTimeV2(valuationRatios[i].GetAsOfDate(), nil).
			Before(datetime.DateToTimeV2(valuationRatios[j].GetAsOfDate(), nil))
	})

	for _, val := range valuationRatios {
		var value float64
		switch metric {
		case PriceToEPSRatio:
			value = val.GetPriceToEps()
		case PriceToBookRatio:
			value = val.GetPriceToBook()
		case DividendYield:
			value = val.GetDividendYield()
		case EVToEBITDA:
			value = val.GetEvToEbitda()
		default:
			return nil, fmt.Errorf("invalid datapoint type")
		}

		dataPoint, err := getLineChartDataPoint(value, []*datePb.Date{val.GetAsOfDate()}, period)
		if err != nil {
			return nil, fmt.Errorf("error getting data point: %w", err)
		}
		if dataPoint == nil {
			continue
		}
		dataPoints = append(dataPoints, dataPoint)
	}
	return dataPoints, nil
}

func getDateSeriesLineChartData(companyName string, stockDataPoints, marketCatDataPoints []*usstocksFePb.DateSeriesLineChartDataPoint) []*usstocksFePb.DateSeriesLineChartData {
	if len(stockDataPoints) == 0 {
		return nil
	}
	lineChartData := []*usstocksFePb.DateSeriesLineChartData{
		{
			SeriesName:       companyName,
			SeriesColor:      "#5684AE",
			SeriesDataPoints: stockDataPoints,
		},
	}
	if len(marketCatDataPoints) == 0 {
		return lineChartData
	}
	lineChartData = append(lineChartData, &usstocksFePb.DateSeriesLineChartData{
		SeriesName:       "Category Average",
		SeriesColor:      "#D9DEE3",
		SeriesDataPoints: marketCatDataPoints,
	})
	return lineChartData
}

func getFinancialRatioTab(metricDisplayName, footerText string, quarterlyChartData, yearlyChartData []*usstocksFePb.DateSeriesLineChartData) *usstocksFePb.FinancialRatioTab {
	// Show only last 6 quarters of datapoints available
	if len(quarterlyChartData) > 0 && len(quarterlyChartData[0].GetSeriesDataPoints()) > 6 {
		quarterlyChartData[0].SeriesDataPoints = quarterlyChartData[0].GetSeriesDataPoints()[len(quarterlyChartData[0].GetSeriesDataPoints())-6:]
		// non-company series data are not truncated here, they are taken care of when matching company and category datapoints
	}

	// Show only last 5 years of datapoints available
	if len(yearlyChartData) > 0 && len(yearlyChartData[0].GetSeriesDataPoints()) > 5 {
		yearlyChartData[0].SeriesDataPoints = yearlyChartData[0].GetSeriesDataPoints()[len(yearlyChartData[0].GetSeriesDataPoints())-5:]
		// non-company series data are not truncated here, they are taken care of when matching company and category datapoints
	}
	quarterlyChartData = matchCompanyAndCategoryDatapoints(quarterlyChartData)
	yearlyChartData = matchCompanyAndCategoryDatapoints(yearlyChartData)

	var chartsForMetric []*usstocksFePb.FinancialRatioChart
	switch {
	case len(quarterlyChartData) != 0 && len(yearlyChartData) != 0:
		quarterlyMetricChart := &usstocksFePb.FinancialRatioChart{
			ReportingFrequency:   usstocksUi.GetText(QuarterlyText, usstocksUi.MonochromeNight, commontypes.FontStyle_OVERLINE_XS_CAPS),
			MultiRatioDataPoints: quarterlyChartData,
			Footer:               usstocksUi.GetText(footerText, "#606265", commontypes.FontStyle_BODY_XS),
		}
		yearlyMetricChart := &usstocksFePb.FinancialRatioChart{
			ReportingFrequency:   usstocksUi.GetText(YearlyText, usstocksUi.MonochromeNight, commontypes.FontStyle_OVERLINE_XS_CAPS),
			MultiRatioDataPoints: yearlyChartData,
			Footer:               usstocksUi.GetText(footerText, "#606265", commontypes.FontStyle_BODY_XS),
		}
		chartsForMetric = []*usstocksFePb.FinancialRatioChart{quarterlyMetricChart, yearlyMetricChart}
	case len(quarterlyChartData) != 0:
		quarterlyMetricChart := &usstocksFePb.FinancialRatioChart{
			ReportingFrequency:   usstocksUi.GetText(QuarterlyText, usstocksUi.MonochromeNight, commontypes.FontStyle_OVERLINE_XS_CAPS),
			MultiRatioDataPoints: quarterlyChartData,
			Footer:               usstocksUi.GetText(footerText, "#606265", commontypes.FontStyle_BODY_XS),
		}
		chartsForMetric = []*usstocksFePb.FinancialRatioChart{quarterlyMetricChart}
	case len(yearlyChartData) != 0:
		yearlyMetricChart := &usstocksFePb.FinancialRatioChart{
			MultiRatioDataPoints: yearlyChartData,
			Footer:               usstocksUi.GetText(footerText, "#606265", commontypes.FontStyle_BODY_XS),
		}
		chartsForMetric = []*usstocksFePb.FinancialRatioChart{yearlyMetricChart}
	default:
		// Handling for when neither quarterly nor yearly metrics are available
		return nil
	}
	return &usstocksFePb.FinancialRatioTab{
		RatioName:            usstocksUi.GetText(metricDisplayName, usstocksUi.MonochromeNight, commontypes.FontStyle_SUBTITLE_3),
		FinancialRatioCharts: chartsForMetric,
	}
}

// chart data is expected to have 2 series of datapoints: 0=company datapoints, 1=category datapoints
func matchCompanyAndCategoryDatapoints(chartData []*usstocksFePb.DateSeriesLineChartData) []*usstocksFePb.DateSeriesLineChartData {
	if len(chartData) <= 1 {
		return chartData
	}
	var (
		matchedChartData  []*usstocksFePb.DateSeriesLineChartData
		companyCategories = map[string]bool{}
	)
	matchedChartData = append(matchedChartData, chartData[0])
	for _, datapoint := range chartData[0].GetSeriesDataPoints() {
		companyCategories[datapoint.GetCategoryName()] = true
	}
	for _, nonCompanySeries := range chartData[1:] {
		var filteredDatapoints []*usstocksFePb.DateSeriesLineChartDataPoint
		for _, datapoint := range nonCompanySeries.GetSeriesDataPoints() {
			if companyCategories[datapoint.GetCategoryName()] {
				filteredDatapoints = append(filteredDatapoints, datapoint)
			}
		}
		if len(filteredDatapoints) > 0 {
			filteredSeriesData := &usstocksFePb.DateSeriesLineChartData{
				SeriesName:       nonCompanySeries.GetSeriesName(),
				SeriesColor:      nonCompanySeries.GetSeriesColor(),
				SeriesDataPoints: filteredDatapoints,
			}
			matchedChartData = append(matchedChartData, filteredSeriesData)
		}
	}
	return matchedChartData
}

func getLineChartDataPoint(value float64, dates []*datePb.Date, period ReportingPeriod) (*usstocksFePb.DateSeriesLineChartDataPoint, error) {
	if value == 0 {
		return nil, nil
	}
	date, err := getValidDateFromMultipleDates(dates)
	if err != nil {
		return nil, err
	}

	periodName, err := getPeriodName(date, period)
	if err != nil {
		return nil, err
	}

	var reportingDate *types.Date
	switch period {
	case Quarterly:
		reportingDate = &types.Date{Year: date.GetYear(), Month: date.GetMonth(), Day: 28}
	case Yearly:
		reportingDate = &types.Date{Year: date.GetYear(), Month: 12, Day: 31}
	default:
		reportingDate = &types.Date{Year: date.GetYear(), Month: date.GetMonth(), Day: date.GetDay()}
	}

	return &usstocksFePb.DateSeriesLineChartDataPoint{
		CategoryName: periodName,
		Date:         reportingDate,
		Value:        value,
	}, nil
}
