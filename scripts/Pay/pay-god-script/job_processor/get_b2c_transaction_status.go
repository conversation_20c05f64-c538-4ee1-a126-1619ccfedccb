package job_processor

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/epifi/be-common/pkg/cfg"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	"github.com/epifi/be-common/pkg/epifigrpc"

	"github.com/epifi/gamma/api/order/payment"
	vgB2CPb "github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"

	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

type GetB2CTransactionStatusProcessor struct {
	conf *config.Config
	args *GetB2CTransactionStatusArgs
}

type GetB2CTransactionStatusArgs struct {
	RefID string `json:"ref_id"`
}

func NewGetB2CTransactionStatusProcessor(conf *config.Config) (JobProcessor, func(), error) {
	return &GetB2CTransactionStatusProcessor{
		conf: conf,
	}, func() {}, nil
}

func (p *GetB2CTransactionStatusProcessor) ParseAndStoreArgs(input *JobRequest) error {
	var args GetB2CTransactionStatusArgs
	if err := json.Unmarshal([]byte(input.Args1), &args); err != nil {
		return fmt.Errorf("failed to unmarshal args: %w", err)
	}
	p.args = &args
	return nil
}

func (p *GetB2CTransactionStatusProcessor) DoJob(ctx context.Context) error {
	if p == nil || p.args == nil || p.args.RefID == "" {
		return fmt.Errorf("ref id is required")
	}

	vgConn := epifigrpc.NewConnByService(cfg.VENDOR_GATEWAY_SERVICE)
	defer epifigrpc.CloseConn(vgConn)

	vgB2cPaymentClient := vgB2CPb.NewPaymentClient(vgConn)

	req := &vgB2CPb.GetB2CTransactionStatusRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		},
		Protocol:          payment.PaymentProtocol_IMPS,
		OriginalRequestId: p.args.RefID,
	}

	resp, err := vgB2cPaymentClient.GetB2CTransactionStatus(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to get b2c transaction status: %w", err)
	}

	log.Printf("b2c txn status: %s", resp)
	return nil
}
