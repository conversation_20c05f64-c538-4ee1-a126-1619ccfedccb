package job_processor

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/grpc/metadata"

	"github.com/google/uuid"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	commonPb "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	paymentPb "github.com/epifi/gamma/api/order/payment"
	payPb "github.com/epifi/gamma/api/pay"
	vendorgateway2 "github.com/epifi/gamma/api/vendorgateway"
	"github.com/epifi/gamma/scripts/Pay/pay-god-script/config"
)

// B2CFundTransferProcessor handles B2C fund transfer testing
type B2CFundTransferProcessor struct {
	payClient *payPb.PayClient
	conf      *config.Config
}

// NewB2CFundTransferProcessor creates a new processor with required dependencies
// nolint: staticcheck
func NewB2CFundTransferProcessor(conf *config.Config) (JobProcessor, error, func()) {
	payConn := epifigrpc.NewConnByService(cfg.PAY_SERVICE)

	cleanupFn := func() {
		if payConn != nil {
			// nolint:errcheck,gosec
			payConn.Close()
		}
	}

	payClient := payPb.NewPayClient(payConn)

	return &B2CFundTransferProcessor{
		payClient: &payClient,
		conf:      conf,
	}, nil, cleanupFn
}

// ParseAndStoreArgs parses and validates input arguments
// No JSON input required - all parameters come from config
// Expected usage: -JobName='B2C_FUND_TRANSFER'
func (j *B2CFundTransferProcessor) ParseAndStoreArgs(input *JobRequest) error {
	// Validate config exists
	if j.conf.B2CFundTransferConfig == nil {
		return fmt.Errorf("B2CFundTransferConfig not found in config")
	}

	config := j.conf.B2CFundTransferConfig

	if !config.EnableJob {
		return fmt.Errorf("B2C Fund Transfer job is disabled in config")
	}

	// Validate all required config fields
	if config.PayerActorId == "" {
		return fmt.Errorf("PayerActorId is required in config")
	}
	if config.PayerPiId == "" {
		return fmt.Errorf("PayerPiId is required in config")
	}
	if config.PayeeActorId == "" {
		return fmt.Errorf("PayeeActorId is required in config")
	}
	if config.PayeePiId == "" {
		return fmt.Errorf("PayeePiId is required in config")
	}
	if config.Amount <= 0 {
		return fmt.Errorf("amount must be greater than 0 in config")
	}
	if config.Remarks == "" {
		return fmt.Errorf("remarks is required in config")
	}

	logger.InfoNoCtx("B2C Fund Transfer Config Loaded:")
	logger.InfoNoCtx("PayerActorId", zap.String("PayerActorId", config.PayerActorId))
	logger.InfoNoCtx("PayerPiId", zap.String("PayerPiId", config.PayerPiId))
	logger.InfoNoCtx("PayeeActorId", zap.String("PayeeActorId", config.PayeeActorId))
	logger.InfoNoCtx("PayeePiId", zap.String("PayeePiId", config.PayeePiId))
	logger.InfoNoCtx("Amount", zap.Int64("Amount", config.Amount))
	logger.InfoNoCtx("Remarks", zap.String("Remarks", config.Remarks))

	return nil
}

// DoJob executes the main job logic
func (j *B2CFundTransferProcessor) DoJob(ctx context.Context) error {
	logger.Info(ctx, "Starting B2C Fund Transfer processor")

	// Create a new context with trace_id and actor_id
	transferCtx := epificontext.WithTraceId(ctx, metadata.MD{})
	transferCtx = epificontext.CtxWithActorId(transferCtx, j.conf.B2CFundTransferConfig.PayerActorId)

	// Generate unique client request ID
	clientRequestId := uuid.New().String()

	// Create MakeB2CFundTransfer request
	makeB2CFundTransferReq := &payPb.MakeB2CFundTransferRequest{
		PayerActorId: j.conf.B2CFundTransferConfig.PayerActorId,
		PayeeActorId: j.conf.B2CFundTransferConfig.PayeeActorId,
		Amount: &gmoney.Money{
			CurrencyCode: moneyPkg.RupeeCurrencyCode,
			Units:        j.conf.B2CFundTransferConfig.Amount,
			Nanos:        0,
		},
		ClientRequestId: &celestialPb.ClientReqId{
			Id:     clientRequestId,
			Client: workflowPb.Client_PAY,
		},
		PiFrom:            j.conf.B2CFundTransferConfig.PayerPiId,
		PiTo:              j.conf.B2CFundTransferConfig.PayeePiId,
		Remarks:           j.conf.B2CFundTransferConfig.Remarks,
		PreferredProtocol: paymentPb.PaymentProtocol_INTRA_BANK,
		Partner:           vendorgateway.Vendor_FEDERAL_BANK,
		EntityOwnership:   commonPb.Ownership_EPIFI_TECH,
		RequestSource:     vendorgateway2.RequestSource_REQUEST_SOURCE_BILLPAY_RECHARGE,
	}

	// Call MakeB2CFundTransfer
	makeB2CFundTransferResp, err := (*j.payClient).MakeB2CFundTransfer(transferCtx, makeB2CFundTransferReq)
	if err != nil {
		logger.Error(transferCtx, "Failed to call MakeB2CFundTransfer", zap.Error(err))
		return fmt.Errorf("error calling MakeB2CFundTransfer: %w", err)
	}

	switch {
	case makeB2CFundTransferResp.GetStatus().IsSuccess():
		logger.Info(ctx, "MakeB2CFundTransfer successful")
	case makeB2CFundTransferResp.GetStatus().IsAlreadyExists():
		logger.Info(ctx, "MakeB2CFundTransfer already exists")
	default:
		logger.Error(transferCtx, "MakeB2CFundTransfer failed",
			zap.Any("status", makeB2CFundTransferResp.GetStatus()))
		return fmt.Errorf("MakeB2CFundTransfer failed with status: %v", makeB2CFundTransferResp.GetStatus())
	}

	// Now call enquiry API for 10 times with 4 seconds interval
	return j.enquireFundTransferStatus(transferCtx, clientRequestId)
}

// enquireFundTransferStatus calls GetFundTransferStatus multiple times to check status
func (j *B2CFundTransferProcessor) enquireFundTransferStatus(ctx context.Context, clientRequestId string) error {
	logger.Info(ctx, "Starting fund transfer status enquiry", zap.String("client_request_id", clientRequestId))

	getFundTransferStatusReq := &payPb.GetFundTransferStatusRequest{
		Identifier: &payPb.GetFundTransferStatusRequest_ClientReqId{
			ClientReqId: &celestialPb.ClientReqId{
				Id:     clientRequestId,
				Client: workflowPb.Client_PAY,
			},
		},
	}

	// Poll for status 10 times with 4 second intervals
	for attempt := 1; attempt <= 10; attempt++ {
		logger.Info(ctx, "Checking fund transfer status", zap.Int("attempt", attempt))

		getFundTransferStatusResp, err := (*j.payClient).GetFundTransferStatus(ctx, getFundTransferStatusReq)
		if err != nil {
			logger.Error(ctx, "Failed to call GetFundTransferStatus",
				zap.Int("attempt", attempt),
				zap.Error(err))
		} else {
			status := getFundTransferStatusResp.GetStatus()

			switch {
			case status.IsSuccess() && getFundTransferStatusResp.GetStatus().GetCode() == uint32(payPb.GetFundTransferStatusResponse_OK):
				logger.Info(ctx, "Fund transfer completed successfully",
					zap.Int("attempt", attempt),
					zap.String("client_request_id", clientRequestId))
				return nil

			case getFundTransferStatusResp.GetStatus().GetCode() == uint32(payPb.GetFundTransferStatusResponse_IN_PROGRESS):
				logger.Info(ctx, "Fund transfer is in progress",
					zap.Int("attempt", attempt))

			case getFundTransferStatusResp.GetStatus().GetCode() == uint32(payPb.GetFundTransferStatusResponse_FAILED):
				logger.Error(ctx, "Fund transfer failed",
					zap.Int("attempt", attempt),
					zap.String("client_request_id", clientRequestId),
					zap.String("pay_error_code", getFundTransferStatusResp.GetPayErrorCode()))
				return fmt.Errorf("fund transfer failed: %s", getFundTransferStatusResp.GetPayErrorCode())

			case status.IsRecordNotFound():
				logger.Error(ctx, "Fund transfer record not found",
					zap.Int("attempt", attempt),
					zap.String("client_request_id", clientRequestId))

			default:
				logger.Error(ctx, "Unexpected fund transfer status",
					zap.Int("attempt", attempt),
					zap.String("client_request_id", clientRequestId),
					zap.Any("status", status))
			}
		}

		// Wait 4 seconds before next attempt (except on last attempt)
		if attempt < 10 {
			logger.Info(ctx, "Waiting 4 seconds before next attempt...")
			time.Sleep(4 * time.Second)
		}
	}

	logger.Error(ctx, "Fund transfer status check timed out after 10 attempts",
		zap.String("client_request_id", clientRequestId))
	return fmt.Errorf("fund transfer status check timed out after 10 attempts")
}
