# Pay God Script

This tool provides a collection of utility jobs for various tasks in the Fi Money platform, including data exports, account services, payment processing, and UPI functionality.

## Usage

```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=[JOB_NAME] -args1='[JOB_ARGS]' [-args2='[EXTRA_ARGS]' -FileInput1=/path/to/input.csv]
```

## Available Jobs

### UPI_LINK_ACCOUNT

Links a UPI account to FEDERAL_BANK vendor.

**Input options**:

1. **Single account (JSON):**
   - `actor_id`: The actor ID of the user
   - `upi_account_id`: The UPI account ID to be linked

   **Example**:
   ```bash
   ./pay-god-script -env=[qa|staging|uat|prod] -job=UPI_LINK_ACCOUNT -args1='{"actor_id":"user-123","upi_account_id":"account-123"}'
   ```

2. **Multiple accounts (CSV file):**
   - CSV file with columns:
     - `actor_id`: The actor ID of the user (optional if firehose_id is provided)
     - `firehose_id`: The firehose ID of the user (optional if actor_id is provided)
     - `upi_account_id`: The UPI account ID to be linked (required)

   **Note:** Either `actor_id` or `firehose_id` must be provided for each row.

   **Example CSV file structure:**
   ```
   actor_id,firehose_id,upi_account_id
   user-123,,account-123
   ,firehose-456,account-456
   user-789,,account-789
   ```

   **Example usage:**
   ```bash
   ./pay-god-script -env=[qa|staging|uat|prod] -job=UPI_LINK_ACCOUNT -Args3=/path/to/upi_accounts.csv
   ```

   The job will:
   - Process all accounts in the CSV file
   - Convert firehose IDs to actor IDs automatically
   - Fetch user device platform and version information
   - Link each UPI account with appropriate delays between requests
   - Provide a summary of successes and failures

### UPI_LINK_INTERNAL_ACCOUNT

Links an internal account for a user via UPI.

**Required parameters**:
- `actor_ids`: Array of actor IDs for users whose internal accounts should be linked

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=UPI_LINK_INTERNAL_ACCOUNT -args1='{"actor_ids": ["actor-123", "actor-456"]}'
```

### DATA_EXPORT

Exports data based on configured use cases. This job supports different data export use cases defined in the configuration.

**Optional parameters**:
- `use_case_name`: Name of the data export use case to execute (defaults to the first use case if not provided)

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=DATA_EXPORT -args1='{"use_case_name": "ManualInterventionOrders"}'
```

**Use cases include**:
- `ManualInterventionOrders`: Exports orders in MANUAL_INTERVENTION status with transaction details
- `UpiOffAppCreditTransactions`: Exports UPI off-app credit transactions with transaction details

### GET_ORDER_TRANSACTION_DETAILS

Exports order and transaction details for specific orders.

**Required parameters**:
- CSV file with order IDs (header: "order_ids")
- `receiver_mail`: Email address to send the export to

**Optional parameters**:
- `fields`: Array of fields to include in the export (defaults to: order_id, req_id, transaction_id, utr, pi_from, pi_to)

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=GET_ORDER_TRANSACTION_DETAILS -args1='{"fields": ["order_id", "req_id", "transaction_id", "utr", "pi_from", "pi_to"], "receiver_mail": "<EMAIL>"}' -FileInput1=/path/to/order_ids.csv
```

### INVOKE_BALANCE_APIS

Invokes the GetBalance API for specified actors.

**Required parameters**:
- `actor_ids`: Array of actor IDs

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_BALANCE_APIS -args1='{"actor_ids": ["actor-123", "actor-456"]}'
```

### POOL_ACCOUNT_STATEMENT_GENERATOR

Generates account statements for pool accounts.

**Required parameters**:
- `email`: Email address to send the statements to

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=POOL_ACCOUNT_STATEMENT_GENERATOR -args1='{"email": "<EMAIL>"}'
```

### INVOKE_PG_RECON

Triggers payment gateway reconciliation processes.

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_PG_RECON
```

### INVOKE_LIST_ACCOUNT_PROVIDERS

Lists account providers for a specified phone number.

**Required parameters**:
- `phone_number`: Phone number to query

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_LIST_ACCOUNT_PROVIDERS -args1='{"phone_number": "9********0"}'
```

### INVOKE_VALIDATE_ADDRESS

Validates an address against provided details.

**Required parameters**:
- Validation details including address components

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_VALIDATE_ADDRESS -args1='{"address_details": {...}}'
```

### INVOKE_GET_BENEFICIARY_NAME_DETAILS

Fetches beneficiary name details for an account.

**Required parameters**:
- `account_number`: Account number
- `ifsc_code`: IFSC code

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_GET_BENEFICIARY_NAME_DETAILS -args1='{"account_number": "**********", "ifsc_code": "ABCD0001234"}'
```

### UPDATE_VPA_MERCHANT_INFO

Updates merchant information for virtual payment addresses.

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=UPDATE_VPA_MERCHANT_INFO
```

### RECON_PG_MANDATE_STATUS

Reconciles payment gateway mandate statuses.

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=RECON_PG_MANDATE_STATUS
```

### GET_AVERAGE_EOD_BALANCE

Calculates average end-of-day balance for specified accounts.

**Required parameters**:
- Account details and date range

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=GET_AVERAGE_EOD_BALANCE -args1='{"account_details": {...}}'
```

### INVOKE_ACCOUNT_STATEMENT_API

Invokes account statement API for specified actors.

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=INVOKE_ACCOUNT_STATEMENT_API -args1='{"actor_details": {...}}'
```

### CHECK_ACTOR_ELIGIBILITY_FOR_CASHBACK

Checks actor eligibility for cashback rewards based on tier using the tiering service.

**Input options:**

1. **JSON Array Format (Args1):**
   - `actors`: Array of actor objects with the following fields:
     - `actor_id`: Actor ID (required)
     - `tier`: Tier string (required) - e.g., "TIER_FI_PLUS", "TIER_FI_INFINITE", "TIER_STANDARD"
     - `reward_month_year`: Month and year for reward eligibility check (optional, format: "YYYY-MM")

   **Example**:
   ```bash
   ./pay-god-script -env=[qa|staging|uat|prod] -job=CHECK_ACTOR_ELIGIBILITY_FOR_CASHBACK -args1='{"actors": [{"actor_id": "********-1234-1234-1234-**********12", "tier": "TIER_FI_PLUS", "reward_month_year": "2024-01"}, {"actor_id": "********-8765-8765-8765-********0987", "tier": "TIER_FI_INFINITE", "reward_month_year": "2024-02"}]}'
   ```

2. **CSV File Format (Args3):**
   - CSV file with columns:
     - `actor_id`: Actor ID (required)
     - `tier`: Tier string (required)
     - `reward_month_year`: Month and year for reward eligibility check (optional)

   **Example CSV file structure:**
   ```
   actor_id,tier,reward_month_year
   ********-1234-1234-1234-**********12,TIER_FI_PLUS,2024-01
   ********-8765-8765-8765-********0987,TIER_FI_INFINITE,2024-02
   11111111-2222-3333-4444-555555555555,TIER_STANDARD,
   22222222-3333-4444-5555-666666666666,TIER_FI_PLUS,2024-01
   ```

   **Example usage**:
   ```bash
   ./pay-god-script -env=[qa|staging|uat|prod] -job=CHECK_ACTOR_ELIGIBILITY_FOR_CASHBACK -Args3=/path/to/actors.csv
   ```

**Features**:
- Support for either JSON array input or CSV file input (not both simultaneously)
- Individual tier and reward month specification per actor
- Concurrent processing with rate limiting (10 requests/second)
- Proper context management with actor-id and trace-id
- Comprehensive error handling and logging
- Final summary with success/failure counts per tier and list of failed actor IDs

The job will:
- Parse actors from either JSON or CSV input format
- Validate all actor data including tier strings and reward month format
- Process actors concurrently with 10 requests/second rate limiting
- Call the tiering service `CheckIfActorIsEligibleForCashbackReward` RPC for each actor
- Log eligibility results for each actor with their specific tier and reward month
- Provide a final summary with success/failure counts broken down by tier and list of failed actor IDs

### UPDATE_MIN_REQUIRED_SALARY_OF_EMPLOYER

Updates the minimum required salary setting for employers.

**Required parameters**:
- Employer details and salary threshold

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=UPDATE_MIN_REQUIRED_SALARY_OF_EMPLOYER -args1='{"employer_details": {...}}'
```

### REQ_CHECK_TXN_STATUS

Invokes the ReqCheckTxnStatus API to check the status of a UPI transaction at the vendor gateway level.

**Required parameters**:
- `org_txn_id`: The organization transaction ID (req_id) for the transaction to check
- `actor_id`: The actor ID on whose behalf the transaction status check is being made

**Example**:
```bash
./pay-god-script -env=[qa|staging|uat|prod] -job=REQ_CHECK_TXN_STATUS -args1='{"org_txn_id": "TXN********9", "actor_id": "ACTOR789012"}'
```

**Features**:
- Auto-populates transaction details by looking up the transaction using org_txn_id
- Determines the correct transaction type (PAY/COLLECT) automatically
- Uses the actor_id to fetch the internal VPA for the request
- Provides detailed status information including payer/payee status codes and descriptions
- All requests are properly traced and logged with actor context for better debugging

### GET_B2C_TRANSACTION_STATUS

Invokes the GetB2CTransactionStatus API to check the status of a B2C transaction at the vendor gateway level.

**Required parameters**:
- `ref_id`: The reference ID for the transaction to check

**Example**:
`./pay-god-script -env=[qa|staging|uat|prod] -job=GET_B2C_TRANSACTION_STATUS -args1='{"ref_id": "B2C12345"}'`

## Configuration

The script uses configuration files in YAML format:

1. Base configuration in `pay-god-script-params.yml`
2. Environment-specific overrides in files like `pay-god-script-params-qa.yml`

### Data Export Configuration

Data export jobs use a hierarchical configuration structure:

```yaml
DataExport:
  ExportUseCases:
    UseCaseName:
      Email:
        # Email configuration
      Query: # SQL/Presto query
      CSV:
        # CSV configuration
```

## Implementation Details

The script follows a processor pattern where each job:

1. Parses and validates input arguments
2. Performs the required actions
3. Returns results (often via email for exports)

Error handling and logging are built into each processor to ensure robustness.

### TIERING_ABUSER_DB_OPS

**Purpose**: Perform operations on the tiering_abuser table with two distinct operation types: Add records from CSV data or soft delete records for a specified month.

**Operation Types**:

#### 1. Add Operation
- **Purpose**: Add new tiering abuser records from CSV data
- **Args1**: "Add" (plain string)
- **Args3**: CSV file path
- **Args2**: Not used

#### 2. SoftDelete Operation  
- **Purpose**: Soft delete existing records for a specified month
- **Args1**: "SoftDelete" (plain string)
- **Args2**: Month in format "2024-01"
- **Args3**: Not used

**CSV Structure** (for Add operation only):
The CSV file should contain the following columns:
```
actor_id,plus_abuser,infinite_abuser,prime_abuser,plus_tier_criteria,infinite_tier_criteria,prime_tier_criteria,month
```

**Column Descriptions**:
- `actor_id`: User ID (string, required)
- `plus_abuser`: Flag indicating if user abused Plus tier criteria (0 or 1)
- `infinite_abuser`: Flag indicating if user abused Infinite tier criteria (0 or 1)  
- `prime_abuser`: Flag indicating if user abused Prime tier criteria (0 or 1)
- `plus_tier_criteria`: Criteria description for Plus tier abuse (string, can be empty)
- `infinite_tier_criteria`: Criteria description for Infinite tier abuse (string, can be empty)
- `prime_tier_criteria`: Criteria description for Prime tier abuse (string, can be empty)
- `month`: Month in format "2025-06-01" (YYYY-MM-DD format, parsed in IST timezone)

**Validation Rules**:
- For Add: `actor_id` and `month` are required fields in CSV
- For SoftDelete: Month in Args2 must be in "2024-01" format  
- All abuser flags must be either 0 or 1
- Tier criteria can be empty even when abuser flag is 1

**Usage Examples**:

1. **Add Operation** (Add records from CSV):
   ```bash
   ./pay-god-script -env=qa -job=TIERING_ABUSER_DB_OPS -args1='Add' -args3='/path/to/tiering_abuser_data.csv'
   ```

2. **SoftDelete Operation** (Soft delete records for specified month):
   ```bash
   ./pay-god-script -env=qa -job=TIERING_ABUSER_DB_OPS -args1='SoftDelete' -args2='2024-01'
   ```

**Example CSV Content** (for Add operation):
```csv
actor_id,plus_abuser,infinite_abuser,prime_abuser,plus_tier_criteria,infinite_tier_criteria,prime_tier_criteria,month
user123,1,0,0,Insufficient balance maintained,,,2025-06-01
user456,0,1,1,,Poor spending pattern,High transaction frequency,2025-06-01
user789,1,1,0,Late payments,Multiple overdrafts,,2025-06-01
user000,0,0,0,,,,,2025-06-01
```
