Aws:
  Region: "ap-south-1"
Logging:
  Level: "info"
  Format: "json"

Secrets:
  Ids:
    PrestoSecretsKey: "prod/banking/presto"

PrestoConfig:
  Username: "analytics_user"
  Password: "analytics_user_pass"
  Host: "presto-master-dp-ext.data-prod.epifi.in"
  Port: 8889
  Catalog: "hive"
  Schema: "federal_epifi"
  LogConfig: true

DataExport:
  ExportUseCases:
    ManualInterventionOrders:
      SourceDBType: "PRESTO"
      Email:
        SES:
          From: "<EMAIL>"
          Recipients:
            To:
              - "<EMAIL>"
            Cc:
              - "<EMAIL>"
          Template:
            Subject: "Orders Stuck in Manual Intervention - Daily Report"
            Body: |
              Hi Team,

              Please find attached the list of orders that are currently stuck in MANUAL_INTERVENTION status.

              The report includes:
              - Transaction ID, status,
              - Creation time
              - Payment protocol

              Kindly review and take necessary action.

              Regards,
              Fi Team
      Query: |
        SELECT
          id,
          status,
          created_at,
          payment_protocol
        FROM federal_epifi.transactions
        WHERE status = 'MANUAL_INTERVENTION'
        LIMIT 1000
      CSV:
        Filename: "manual_intervention_orders.csv"
        Headers:
          - "id"
          - "status"
          - "created_at"
          - "payment_protocol"

OrderTransactionDetailsConfig:
  SenderEmail: "<EMAIL>"
  SenderName: "Order Transaction Details Service"

B2CFundTransferConfig:
  EnableJob: false
  PayerActorId: "actor-recharges-federal-pool-account"
  PayerPiId: "paymentinstrument-recharges-federal-pool-account"
  PayeeActorId: "AC220724rLMWwuWMT+Op2EhdfJR3GA=="
  PayeePiId: "PI220806vTJEJdvmSQeXZwGpx+O47w=="
  Amount: 1
  Remarks: "B2C Fund Transfer Test"
