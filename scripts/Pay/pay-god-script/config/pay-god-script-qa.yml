Environment: "qa"

TemporalSecrets:
  TemporalCodecAesKeySecret: "qa/temporal/codec-encryption-key"

PrestoConfigWrapper:
  PrestoSecretsPath: "qa/presto"
  PrestoConfig:
    Host: "presto-dp-emr.data-dev.pointz.in"
    Port: 8889
    Catalog: "hive"
    Schema: "federal_epifi"
    LogConfig: true

DataExport:
  PrestoSecrets:
    PrestoSecretKey: "qa/presto"


CurrentAccountStatementGenParams:
  SenderEmail: "<EMAIL>"
  SenderName: "Current Account Statement Job"
  UsecaseToReceiverDetailsMap:
    "STOCK_GUARDIAN_TSP":
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Rohan Chougule"

RiskyVPABucket: "epifi-qa-pay-scripts-data"

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "qa/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "qa/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-stockguardian_MbZRPgHhafW":
    AuthParam: "qa/vendorgateway/razorpay-stock-guardian-loans-api-key"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/qa/"
  SSLRootCert: "qa/cockroach/ca.crt"
  SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DbType: "CRDB"
    AppName: "order"
    StatementTimeout: 1s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 40
    MaxIdleConn: 10
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

  LIQUILOANS_PL:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DbType: "CRDB"
    AppName: "pay"
    StatementTimeout: 10s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DBType: "CRDB"
    StatementTimeout: 10s
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "qa/cockroach/ca.crt"
    SSLClientCert: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "qa/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    AppName: "order"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "qa-recurringpayment-failed-enach-transaction-queue"

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "qa/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

B2CFundTransferConfig:
  PayerActorId: "actor-recharges-federal-pool-account"
  PayerPiId: "paymentinstrument-recharges-federal-pool-account"
  PayeeActorId: "AC220724rLMWwuWMT+Op2EhdfJR3GA=="
  PayeePiId: "PI220806vTJEJdvmSQeXZwGpx+O47w=="
  Amount: 1
  Remarks: "B2C Fund Transfer Test"
