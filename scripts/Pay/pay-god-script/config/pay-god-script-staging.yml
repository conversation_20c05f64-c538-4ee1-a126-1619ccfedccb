Environment: "staging"

TemporalSecrets:
  TemporalCodecAesKeySecret: "staging/temporal/codec-encryption-key"
CurrentAccountStatementGenParams:
  SenderEmail: "<EMAIL>"
  SenderName: "Current Account Statement Job"
  UsecaseToReceiverDetailsMap:
    "STOCK_GUARDIAN_TSP":
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Sundeep Chand"

RiskyVPABucket: "epifi-staging-pay-scripts-data"

PgProgramToAuthSecretMap:
  "RAZORPAY:EPIFI_TECH:paymentinstrument-creditcard-federal-pool-account-2":
    AuthParam: "staging/vendorgateway/razorpay-federal-secured-cards-api-key"
  "RAZORPAY:LIQUILOANS_PL:PILEcFKdcmQFWa8BfipSET/Q230915==":
    AuthParam: "staging/vendorgateway/razorpay-liquiloans-loans-api-key"
  "RAZORPAY:STOCK_GUARDIAN_TSP:paymentinstrument-creditcard-federal-pool-account-1":
    AuthParam: "staging/vendorgateway/razorpay-stock-guardian-loans-api-key"

EpifiDb:
  DbType: "CRDB"
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLCertPath: "./crdb/staging/"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

UsecaseDbConfigMap:
  EPIFI_TECH:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    AppName: "pay"
    DbType: "CRDB"
    StatementTimeout: 1s
    Username: "epifi_dev_user"
    Password: ""
    Name: "epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
    MaxOpenConn: 5
    MaxIdleConn: 2
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  LIQUILOANS_PL:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DbType: "CRDB"
    AppName: "pay"
    StatementTimeout: 10s
    Username: "liquiloans_epifi_dev_user"
    Password: ""
    Name: "liquiloans_epifi"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.liquiloans_epifi_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.liquiloans_epifi_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true
  STOCK_GUARDIAN_TSP:USE_CASE_RECURRING_PAYMENTS_VENDOR_DETAILS:
    DBType: "CRDB"
    StatementTimeout: 10s
    Username: "stockguardian_tsp_crdb_dev_user"
    Password: ""
    Name: "stockguardian_tsp_crdb"
    EnableDebug: true
    SSLMode: "verify-full"
    SSLRootCert: "staging/cockroach/ca.crt"
    SSLClientCert: "staging/cockroach/client.stockguardian_tsp_crdb_dev_user.crt"
    SSLClientKey: "staging/cockroach/client.stockguardian_tsp_crdb_dev_user.key"
    MaxOpenConn: 20
    MaxIdleConn: 4
    MaxConnTtl: "30m"
    AppName: "pay"
    GormV2:
      LogLevelGormV2: "INFO"
      SlowQueryLogThreshold: 200ms
      UseInsecureLog: true

FetchAndCreateFailedEnachTransactionPublisher:
  QueueName: "staging-recurringpayment-failed-enach-transaction-queue"

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "staging/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

B2CFundTransferConfig:
  EnableJob: true
  PayerActorId: "actor-recharges-federal-pool-account"
  PayerPiId: "paymentinstrument-recharges-federal-pool-account"
  PayeeActorId: "ACta2uuEtAZa250725"
  PayeePiId: "PICQZei7N8QsxZ250729_5feFb8zhrk"
  Amount: 1
  Remarks: "B2C Fund Transfer Test"
