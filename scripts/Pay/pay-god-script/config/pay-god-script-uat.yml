Environment: "statement"

TemporalSecrets:
  TemporalCodecAesKeySecret: "uat/temporal/codec-encryption-key"

CurrentAccountStatementGenParams:
  SenderEmail: "<EMAIL>"
  SenderName: "Current Account Statement Job"
  UsecaseToReceiverDetailsMap:
    "STOCK_GUARDIAN_TSP":
      AccountNumbers:
        - "**************"
      ReceiverDetails:
        - ReceiverEmailId: "<EMAIL>"
          ReceiverName: "Sundeep Chand"

TieringDb:
  DbType: "PGDB"
  AppName: "tiering"
  StatementTimeout: 1m
  Name: "tiering"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 10
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  SecretName: "uat/rds/epifimetis/tiering_dev_user"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

B2CFundTransferConfig:
  RechargePoolAccountActorId: "actor-recharges-federal-pool-account"
  PayerPiId: "paymentinstrument-recharges-federal-pool-account"
  PayerActorId: "AC220724rLMWwuWMT+Op2EhdfJR3GA=="
  PayeePiId: "PI220806vTJEJdvmSQeXZwGpx+O47w=="
  Amount: 1
  Remarks: "B2C Fund Transfer Test"
