// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	faas2 "github.com/epifi/be-common/pkg/epifitemporal/faas"
	"github.com/epifi/be-common/pkg/epifitemporal/namespace"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/faas"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/accounts/operstatus"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/card/control"
	"github.com/epifi/gamma/api/card/provisioning"
	"github.com/epifi/gamma/api/card/tokenizer_proxy"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/firefly/lms"
	"github.com/epifi/gamma/api/firefly/pinot"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pan"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/rewards"
	"github.com/epifi/gamma/api/rewards/projector"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	creditcard2 "github.com/epifi/gamma/api/simulator/lending/creditcard"
	"github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tokenizer"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/onboarding"
	creditcard3 "github.com/epifi/gamma/api/vendorgateway/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/currencyinsights"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditline"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/card"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/payment/b2c"
	savings2 "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	pan2 "github.com/epifi/gamma/api/vendorgateway/pan"
	dao3 "github.com/epifi/gamma/auth/orchestrator/dao"
	"github.com/epifi/gamma/card/dao"
	types4 "github.com/epifi/gamma/card/wire/types"
	impl2 "github.com/epifi/gamma/firefly/accounting/dao/impl"
	impl3 "github.com/epifi/gamma/firefly/billing/dao/impl"
	comms2 "github.com/epifi/gamma/firefly/comms"
	dao4 "github.com/epifi/gamma/firefly/dao"
	"github.com/epifi/gamma/firefly/dao/impl"
	helper2 "github.com/epifi/gamma/firefly/helper"
	types2 "github.com/epifi/gamma/firefly/wire/types"
	"github.com/epifi/gamma/scripts/cc_firefly/config"
	"github.com/epifi/gamma/scripts/cc_firefly/helper"
	"github.com/epifi/gamma/scripts/cc_firefly/job"
	types3 "github.com/epifi/gamma/scripts/cc_firefly/wire/types"
	dao2 "github.com/epifi/gamma/user/onboarding/dao"
	"github.com/rudderlabs/analytics-go"
	"github.com/slack-go/slack"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialiseJobRegistry(ctx context.Context, db *gorm.DB, dcDb dao.DebitCardPGDB, s3Client s3.S3Client, rudderClient analytics.Client, conf *config.Config, fireflyClient firefly.FireflyClient, client *slack.Client, commsClient comms.CommsClient, userClient user.UsersClient, ccVgClient creditcard.CreditCardClient, accountingClient accounting.AccountingClient, doOnceManager once.DoOnce, piClient paymentinstrument.PiClient, epifiDb types.EpifiCRDB, ccDb types.CreditCardPGDB, billingClient billing.BillingClient, actorClient actor.ActorClient, paymentClient payment.PaymentClient, savingsClient savings.SavingsClient, dcDocsS3Client helper.DcDocsS3Client, orderClient order.OrderServiceClient, bankCustClient bankcust.BankCustomerServiceClient, orderUpdateEventPublisher job.OrderUpdateEventPublisher, rewardsClient rewards.RewardsGeneratorClient, ccPinotClient pinot.TxnAggregatesClient, ccStageEventUpdatePublisher job.StageUpdateEventPublisher, ccDocsS3Client job.CcDocsS3Client, ccWorkflowReportingConfig *config.CcWorkflowReportingJobConfig, debitCardDataS3Client helper.DcDataS3Client, cryptor helper.PgpCryptor, panClient pan.PanClient, operationalServiceClient operstatus.OperationalStatusServiceClient, CelestialClient celestial.CelestialClient, cardProvisioningClient provisioning.CardProvisioningClient, TieringClient tiering.TieringClient, payClient pay.PayClient, cardControlClient control.CardControlClient, depositClient deposit.DepositClient, obPaymentClient b2c.PaymentClient, creditLineClient creditline.CreditLineClient, cleClient credit_limit_estimator.CreditLimitEstimatorClient, palClient preapprovedloan.PreApprovedLoanClient, profileClient profile.ProfileClient, simulatorLendingCreditCard creditcard2.CreditCardClient, awsConf aws.Config, cryptorStore *cryptormap.InMemoryCryptorStore, lmsClient lms.LoanManagementSystemClient, rewardsProjectionClient projector.ProjectorServiceClient, docsClient docs.DocsClient, eKycVgClient ekyc.EKYCClient, panVgClient pan2.PANClient, kycClient kyc.KycClient, onboardingClient onboarding.OnboardingClient, authClient auth.AuthClient, vgSavingsClient savings2.SavingsClient, onbDao dao2.OnboardingDao, ffTemporalClient helper.FFTemporalClient, authReqDao dao3.AuthRequestsDao, authReqStageDao dao3.AuthRequestStagesDao, authTemporalClient job.AuthTemporalClient, epifiDbV2 job.EpifiDbV2, tokenizerClient tokenizer.TokenizerClient, fireflyRedisStore types2.FireflyRedisStore, manualGiveawayEventPublisher types3.RewardsManualGiveawayEventPublisher, dcTxnExecutor job.DcIdempotentTxnExecutor, dbHandle *dynamodb.Client, segmentClient segment.SegmentationServiceClient, debitCardTemporalClient job.DebitCardTemporalClient, redisClient types4.CardsRedisStore, tokenizerProxyClient tokenizer_proxy.TokenizerProxyClient, currencyInsightsVgClient currencyinsights.ServiceClient, cpVgClient card.CardProvisioningClient, opsVgClient accounts.AccountsClient, ccVgClientV2 creditcard3.CreditCardClient, vgCustomerClient customer.CustomerClient) (*job.Registry, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	crdbCreditCardOfferEligibilityCriteriaDao := impl.NewCrdbCreditCardOfferEligibilityCriteriaDao(db, domainIdGenerator)
	rudderStackBroker := events.NewRudderStackBroker(rudderClient)
	ccCacheStorage := types3.CcCacheStorageProvider(conf, fireflyRedisStore)
	creditCardOfferCacheConfig := types3.CreditCardOfferCacheConfigProvider(conf)
	crdbCreditCardOfferDao := impl.NewCrdbCreditCardOfferDao(db, domainIdGenerator)
	creditCardOfferDaoCache := dao4.NewCreditCardOfferDaoCache(ccCacheStorage, creditCardOfferCacheConfig, crdbCreditCardOfferDao)
	creditCardOffersDao := dao4.ProvideCreditCardOfferDao(creditCardOfferDaoCache)
	cardsCacheStorage := types3.DcCacheStorageProvider(conf, redisClient)
	cardsCacheConfig := types3.CardDaoCacheConfigProvider(conf)
	cardDaoCRDB := dao.NewCardDaoCRDB(dcDb)
	cardDaoCache := dao.NewCardDaoCache(cardsCacheStorage, cardsCacheConfig, cardDaoCRDB)
	cardRequestCacheConfig := types3.CardRequestCacheConfigProvider(conf)
	cardRequestDao := impl.NewCrdbCardRequest(db, domainIdGenerator)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper2.NewCommsDataHelper(fireflyClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnceManager, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao4.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, rudderStackBroker, asyncProcessor)
	daoCardRequestDao := dao4.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	cardRequestStageDao := impl.NewCrdbCardRequestStage(db, domainIdGenerator)
	helperHelper := helper.NewHelper(s3Client, crdbCreditCardOfferEligibilityCriteriaDao, rudderStackBroker, actorClient, paymentClient, savingsClient, conf, dcDocsS3Client, orderClient, bankCustClient, creditCardOffersDao, debitCardDataS3Client, cryptor, cardDaoCache, operationalServiceClient, CelestialClient, TieringClient, commsClient, cardControlClient, client, piClient, accountingClient, dcDb, profileClient, cryptorStore, onbDao, daoCardRequestDao, cardRequestStageDao, ffTemporalClient, authReqDao, authReqStageDao)
	processFiEligibleBaseJob := job.NewProcessFiEligibleBaseJob(helperHelper)
	creditCardDao := impl.NewCrdbCreditCard(db, domainIdGenerator)
	generateCcQrJob := job.NewGenerateCcQrJob(conf, userClient, creditCardDao, daoCardRequestDao, s3Client, creditLineClient, cleClient, palClient)
	initiateCardRequestJob := job.NewInitiateCardRequestJob(fireflyClient)
	physicalCardReconJob := job.NewPhysicalCardReconJob(fireflyClient, daoCardRequestDao, cardRequestStageDao, creditCardDao, client)
	ccCommsTestJob := job.NewCcCommsTestJob(asyncProcessor, fireflyClient, userClient, ccVgClient, accountingClient)
	dcExpiryUpdateJob := job.NewDCExpiryUpdateJob(conf, cardDaoCache, piClient)
	dcTxnDataFetchJob := job.NewDcTxnDataFetchJob(helperHelper, epifiDb)
	dcTxnPublishJob := job.NewDcTxnPublishJob(helperHelper, orderUpdateEventPublisher, epifiDb)
	processCardForexTxnRefundJob := job.NewProcessCardForexTxnRefundJob(helperHelper, epifiDb, dcDb, profileClient)
	dcFiPlusForexRefundNotificationReplayJob := job.NewDcFiPlusForexRefundNotificationReplayJob(epifiDb, helperHelper)
	cardTrackingCardRequestBackfillJob := job.NewCardTrackingCardRequestBackfillJob(s3Client, daoCardRequestDao, cardRequestStageDao, creditCardDao)
	ccStatementJob := job.NewCcStatementJob(fireflyClient, rewardsClient, billingClient, ccPinotClient, accountingClient)
	triggerWelcomeOfferJob := job.NewTriggerWelcomeOfferJob(daoCardRequestDao, ccStageEventUpdatePublisher, rewardsClient)
	maskCardNumberJob := job.NewMaskCardNumberJob(conf, creditCardDao)
	dcTxnDataFetchWithCountryCodeJob := job.NewDcTxnDataFetchWithCountryCodeJob(helperHelper)
	ccWorkflowReportingJob := job.NewCcWorkflowReportingJob(ccDocsS3Client, daoCardRequestDao, cardRequestStageDao, creditCardDao, ccWorkflowReportingConfig, client)
	disableOffersJob := job.NewDisableOffersJob(bankCustClient, s3Client, creditCardOffersDao, daoCardRequestDao)
	addFiRejectedUsersJob := job.NewAddFiRejectedUsersJob(bankCustClient, s3Client, crdbCreditCardOfferEligibilityCriteriaDao)
	extendOfferExpiryJob := job.NewExtendOfferExpiryJob(bankCustClient, s3Client, creditCardOffersDao, daoCardRequestDao)
	processCardDeclineDataJob := job.NewProcessCardDeclineDataJob(helperHelper)
	updateCardRequestsJob := job.NewUpdateCardRequestsJob(daoCardRequestDao, creditCardOffersDao)
	creditCardClosureIntimationJob := job.NewCreditCardClosureIntimationJob(asyncProcessor, creditCardDao)
	uploadFileToS3BucketJob := job.NewUploadFileToS3BucketJob(conf, dcDocsS3Client, ccDocsS3Client)
	getPanAadhaarLinkStatusJob := job.NewGetPanAadhaarLinkStatusJob(panClient)
	dcVendorIdToAccNumberMapJob := job.NewDcVendorIdToAccNumberMapJob(helperHelper)
	forexRefundReconJob := job.NewForexRefundReconJob(helperHelper, epifiDb)
	cbsIdPopulationForTxnIdJob := job.NewCbsIdPopulationForTxnIdJob(helperHelper, epifiDb)
	dcCardCreationRetryJob := job.NewDcCardCreationRetryJob(helperHelper, cardProvisioningClient)
	creditAccountDaoCRDB := impl2.NewCreditAccountDaoCRDB(db, domainIdGenerator)
	statementAlertingJob := job.NewStatementAlertingJob(creditAccountDaoCRDB, daoCardRequestDao, creditCardDao, cardRequestStageDao, ccWorkflowReportingConfig, client, ccDocsS3Client, CelestialClient)
	reconUnsentRefunds := job.NewReconUnsentRefunds(helperHelper, epifiDb)
	physicalCardDispatchRequestDaoCRDB := dao.NewPhysicalCardDispatchRequestDaoCRDB(dcDb)
	dcPhysicalCardDispatchReconJob := job.NewDcPhysicalCardDispatchReconJob(physicalCardDispatchRequestDaoCRDB, cardDaoCache, CelestialClient, userClient, payClient, orderClient, debitCardTemporalClient)
	dcContactlessSwitchOffJob := job.NewDcContactlessSwitchOffJob(helperHelper)
	processedRefundsRecon := job.NewProcessedRefundsRecon(helperHelper, epifiDb)
	forexRefundAlertingJob := job.NewForexRefundAlertingJob(helperHelper, epifiDb, dcDb)
	updatePaymentInstrumentJob := job.NewUpdatePaymentInstrumentJob(piClient)
	ccCardRequestRewardJob := job.NewCcCardRequestRewardJob(daoCardRequestDao, rewardsClient)
	dcPhysicalCardDispatchFailureAlertJob := job.NewDcPhysicalCardDispatchFailureAlertJob(physicalCardDispatchRequestDaoCRDB, client)
	triggerStatementGenerationWorkflowJob := job.NewTriggerStatementGenerationWorkflowJob(creditAccountDaoCRDB, creditCardDao, fireflyClient, ccDocsS3Client)
	addCCTransactionJob := job.NewAddCCTransactionJob(simulatorLendingCreditCard, accountingClient, fireflyClient)
	faaSExecutor, err := FireflyFaasExecutorProvider(ctx, awsConf, conf)
	if err != nil {
		return nil, err
	}
	triggerReconcileWorkflowsJob := job.NewTriggerReconcileWorkflowsJob(faaSExecutor)
	statementDueDateMismatchIssueJob := job.NewStatementDueDateMismatchIssueJob(creditAccountDaoCRDB, creditCardDao, client, ccVgClient)
	updateCardRequestAndStageStatusJob := job.NewUpdateCardRequestAndStageStatusJob(daoCardRequestDao, cardRequestStageDao, CelestialClient, client, ccDocsS3Client)
	manualStatementGenerationJob := job.NewManualStatementGenerationJob(creditAccountDaoCRDB, daoCardRequestDao, creditCardDao, cardRequestStageDao, ccDocsS3Client, billingClient, ccVgClient, lmsClient, accountingClient, actorClient, rewardsProjectionClient, rewardsClient, ccPinotClient, docsClient, conf, commsClient, userClient)
	decryptVgFilesJob := job.NewDecryptVgFilesJob(conf, s3Client, cryptorStore)
	fetchWelcomeOfferRewardIdJob := job.NewFetchWelcomeOfferRewardIdJob(daoCardRequestDao, client)
	offerInvalidationJob := job.NewOfferInvalidationJob(bankCustClient, kycClient, eKycVgClient, userClient, fireflyClient, panVgClient, creditCardOffersDao, client, ccDocsS3Client, crdbCreditCardOfferEligibilityCriteriaDao, onboardingClient)
	creditCardPaymentDaoCRDB := impl3.NewCrdbCreditCardPaymentDao(db, domainIdGenerator)
	billEraserReconJob := job.NewBillEraserReconJob(db, helperHelper, creditCardPaymentDaoCRDB, accountingClient, ccVgClient, creditCardDao, daoCardRequestDao)
	ccSecuredDepositIdJob := job.NewCcSecuredDepositIdJob(depositClient, authClient, actorClient, vgSavingsClient, client)
	updateAtmLimitJob := job.NewUpdateAtmLimitJob(creditCardDao, creditAccountDaoCRDB, ccVgClient, db)
	updateRewardInfoInBillJob := job.NewUpdateRewardInfoInBillJob(daoCardRequestDao, billingClient, rewardsProjectionClient, fireflyClient)
	filiteResetJob := job.NewFiliteResetJob(helperHelper, ccDb, onboardingClient, CelestialClient, ccDocsS3Client)
	dedupeCheckJob := job.NewDedupeCheckJob(userClient, tokenizerProxyClient)
	terminateAuthWorkflowsJob := job.NewTerminateAuthWorkflowsJob(ccDocsS3Client, helperHelper, CelestialClient, authTemporalClient)
	ccOfferAddJob := job.NewCcOfferAddJob(creditCardOffersDao, crdbCreditCardOfferEligibilityCriteriaDao, actorClient)
	invalidOnboardingDetectionJob := job.NewInvalidOnboardingDetectionJob(ccDb, bankCustClient, userClient, onboardingClient, kycClient, eKycVgClient, panVgClient, ccDocsS3Client, creditLineClient)
	userOnboardingDataPopulationJob := job.NewUserOnboardingDataPopulationJob(ccDocsS3Client, onboardingClient, userClient, bankCustClient, fireflyClient, accountingClient, billingClient)
	ccUpdateCustomerDetailsAtM2PJob := job.NewCcUpdateCustomerDetailsAtM2PJob(actorClient, daoCardRequestDao, ccVgClient, userClient)
	blockAndReissueNewCCJob := job.NewBlockAndReissueNewCCJob(daoCardRequestDao, fireflyClient)
	registerCustomerForCCJob := job.NewRegisterCustomerForCCJob(daoCardRequestDao, creditCardDao, creditAccountDaoCRDB, fireflyClient, actorClient, userClient, onboardingClient, bankCustClient, ccVgClient, accountingClient)
	updateCardDetailsAtBankJob := job.NewUpdateCardDetailsAtBankJob(daoCardRequestDao, creditCardDao, creditAccountDaoCRDB, ccVgClient)
	dcRenewCardJob := job.NewDcRenewCardJob(cardProvisioningClient, cardDaoCache)
	dcBlockCardJob := job.NewDcBlockCardJob(cardControlClient)
	imitateBlockAndReissueNewCCJob := job.NewImitateBlockAndReissueNewCCJob(daoCardRequestDao, creditCardDao, fireflyClient, actorClient, bankCustClient, ccVgClient, userClient, piClient)
	creditCardTransactionDaoCRDB := impl2.NewCreditCardTransactionDaoCRDB(db, domainIdGenerator)
	processCardTransaction := job.NewProcessCardTransaction(creditCardTransactionDaoCRDB, faaSExecutor, ccDocsS3Client, creditAccountDaoCRDB, creditCardDao)
	deleteDcDynamoDataJob := job.NewDeleteDcDynamoDataJob(tokenizerProxyClient, cardDaoCache, epifiDbV2)
	changeDcPinValidationParamsJob := job.NewChangeDcPinValidationParamsJob()
	cardTrackingRequestDaoCRDB := dao.NewCardTrackingRequestDaoCRDB(dcDb)
	getActorCcShippingAddressJob := job.NewGetActorCcShippingAddressJob(daoCardRequestDao, userClient, creditAccountDaoCRDB, epifiDbV2, cardTrackingRequestDaoCRDB, bankCustClient, cardRequestStageDao, cardDaoCache, physicalCardDispatchRequestDaoCRDB, s3Client)
	triggerWelcomeRewardsJob := job.NewTriggerWelcomeRewardsJob(daoCardRequestDao, cardRequestStageDao, creditCardDao, accountingClient, rewardsClient, ccVgClient)
	dcPhysicalCardChargesReversalJob := job.NewDcPhysicalCardChargesReversalJob(cardDaoCache, physicalCardDispatchRequestDaoCRDB, orderClient, actorClient, manualGiveawayEventPublisher, conf, client, dcTxnExecutor, CelestialClient, debitCardTemporalClient, helperHelper)
	ccReconJob := job.NewCCReconJob(creditAccountDaoCRDB, ffTemporalClient)
	rotateTokenizerKeysJob := job.NewRotateTokenizerKeysJob(conf, dbHandle, tokenizerProxyClient)
	generateStatementJob := job.NewGenerateStatementJob(creditAccountDaoCRDB, simulatorLendingCreditCard)
	activateCreditCardJob := job.NewActivateCreditCardJob(daoCardRequestDao, creditCardDao)
	triggerFeeWaiverJob := job.NewTriggerFeeWaiverJob(creditCardTransactionDaoCRDB, daoCardRequestDao, ffTemporalClient, creditAccountDaoCRDB, rewardsClient, ccStageEventUpdatePublisher, creditCardDao)
	bypassCcOnboardingJob := job.NewBypassCcOnboardingJob(simulatorLendingCreditCard, daoCardRequestDao, creditCardDao, userClient, actorClient, bankCustClient, creditAccountDaoCRDB)
	deleteCardRequestJob := job.NewDeleteCardRequestJob(daoCardRequestDao, creditCardTransactionDaoCRDB)
	triggerProcessCcTxnWfJob := job.NewTriggerProcessCcTxnWfJob(ffTemporalClient, creditCardTransactionDaoCRDB)
	sendInAppNotificationJob := job.NewSendInAppNotificationJob(conf, segmentClient, actorClient, commsClient)
	sendDpdEventsJob := job.NewSendDpdEventsJob(rudderStackBroker, ccDocsS3Client)
	initiateAmcReportGenerationJob := job.NewInitiateAmcReportGeneration(CelestialClient)
	correctForexRefundDbJob := job.NewCorrectForexRefundDbJob(helperHelper, epifiDb, dcDb, orderClient, orderUpdateEventPublisher, paymentClient)
	deleteAmcCardRequestJob := job.NewDeleteAmcCardRequestJob(helperHelper, dcDb)
	visaApiTestJob := job.NewVisaApiTestJob(currencyInsightsVgClient)
	investigateAwbMismatchJob := job.NewInvestigateAwbMismatchJob(cardDaoCache, helperHelper)
	dcOrderPhysicalCardWithChargesJob := job.NewDcOrderPhysicalCardWithChargesJob(cardProvisioningClient)
	fetchDueJob := job.NewFetchDueJob(creditAccountDaoCRDB, creditCardDao, client, ccVgClient)
	creatVendorRepaymentRecordJob := job.NewCreatVendorRepaymentRecordJob(accountingClient, billingClient, ccVgClient, fireflyClient)
	ccSmsScriptJob := job.NewCcSmsScriptJob(fireflyClient, commsClient, userClient, bankCustClient)
	correctForexTcsClashJob := job.NewCorrectForexTcsClashJob(dcDb, orderClient, orderUpdateEventPublisher, paymentClient)
	collectDCIssuanceApiTestJob := job.NewCollectDCIssuanceApiTestJob(cpVgClient)
	processKycExpiryForCcJob := job.NewProcessKycExpiryForCcJob(creditAccountDaoCRDB, creditCardDao, opsVgClient, userClient, savingsClient, client, helperHelper)
	enquireDcChargesCollectionStsJob := job.NewEnquireDcChargesCollectionStsJob(cpVgClient, savingsClient)
	publishCcDelStateUpdateEventJob := job.NewPublishCcDelStateUpdateEventJob(ccVgClientV2)
	ccCxMigrationSmsJob := job.NewCcCxMigrationSmsJob(creditCardDao, creditAccountDaoCRDB, userClient, commsClient)
	fetchCustJob := job.NewFetchCustJob(vgCustomerClient, bankCustClient, authClient, userClient)
	registry := job.NewRegistry(processFiEligibleBaseJob, generateCcQrJob, initiateCardRequestJob, physicalCardReconJob, ccCommsTestJob, dcExpiryUpdateJob, dcTxnDataFetchJob, dcTxnPublishJob, processCardForexTxnRefundJob, dcFiPlusForexRefundNotificationReplayJob, cardTrackingCardRequestBackfillJob, ccStatementJob, triggerWelcomeOfferJob, maskCardNumberJob, dcTxnDataFetchWithCountryCodeJob, ccWorkflowReportingJob, disableOffersJob, addFiRejectedUsersJob, extendOfferExpiryJob, processCardDeclineDataJob, updateCardRequestsJob, creditCardClosureIntimationJob, uploadFileToS3BucketJob, getPanAadhaarLinkStatusJob, dcVendorIdToAccNumberMapJob, forexRefundReconJob, cbsIdPopulationForTxnIdJob, dcCardCreationRetryJob, statementAlertingJob, reconUnsentRefunds, dcPhysicalCardDispatchReconJob, dcContactlessSwitchOffJob, processedRefundsRecon, forexRefundAlertingJob, updatePaymentInstrumentJob, ccCardRequestRewardJob, dcPhysicalCardDispatchFailureAlertJob, triggerStatementGenerationWorkflowJob, addCCTransactionJob, triggerReconcileWorkflowsJob, statementDueDateMismatchIssueJob, updateCardRequestAndStageStatusJob, manualStatementGenerationJob, decryptVgFilesJob, fetchWelcomeOfferRewardIdJob, offerInvalidationJob, billEraserReconJob, ccSecuredDepositIdJob, updateAtmLimitJob, updateRewardInfoInBillJob, filiteResetJob, dedupeCheckJob, terminateAuthWorkflowsJob, ccOfferAddJob, invalidOnboardingDetectionJob, userOnboardingDataPopulationJob, ccUpdateCustomerDetailsAtM2PJob, blockAndReissueNewCCJob, registerCustomerForCCJob, updateCardDetailsAtBankJob, dcRenewCardJob, dcBlockCardJob, imitateBlockAndReissueNewCCJob, processCardTransaction, deleteDcDynamoDataJob, changeDcPinValidationParamsJob, getActorCcShippingAddressJob, triggerWelcomeRewardsJob, dcPhysicalCardChargesReversalJob, ccReconJob, rotateTokenizerKeysJob, generateStatementJob, activateCreditCardJob, triggerFeeWaiverJob, bypassCcOnboardingJob, deleteCardRequestJob, triggerProcessCcTxnWfJob, sendInAppNotificationJob, sendDpdEventsJob, initiateAmcReportGenerationJob, correctForexRefundDbJob, deleteAmcCardRequestJob, visaApiTestJob, investigateAwbMismatchJob, dcOrderPhysicalCardWithChargesJob, fetchDueJob, creatVendorRepaymentRecordJob, ccSmsScriptJob, correctForexTcsClashJob, collectDCIssuanceApiTestJob, processKycExpiryForCcJob, enquireDcChargesCollectionStsJob, publishCcDelStateUpdateEventJob, ccCxMigrationSmsJob, fetchCustJob)
	return registry, nil
}

// wire.go:

func FireflyFaasExecutorProvider(ctx context.Context, awsConf aws.Config, conf *config.Config) (faas.FaaSExecutor, error) {
	return faas2.NewFaaSExecutor(ctx, sqs.NewFromConfig(awsConf), namespace.Firefly, conf.ProcrastinatorWorkflowPublisher)
}
