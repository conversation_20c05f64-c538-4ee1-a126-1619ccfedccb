// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/api/cx/ticket"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/cx/dispute/dao"
	"github.com/epifi/gamma/cx/dispute/helper"
	types2 "github.com/epifi/gamma/cx/wire/types"
	"github.com/epifi/gamma/scripts/dispute_daily_dump/config"
	"github.com/epifi/gamma/scripts/dispute_daily_dump/processor"
)

// Injectors from wire.go:

func InitializeDisputeProcessor(db types.SherlockPGDB, conf *config.Config, createDisputeTicketPub types2.DisputeCreateTicketPublisher, ticketClient ticket.TicketClient, userClient user.UsersClient, disputeHelper helper.IDisputeHelper) *processor.DisputeProcessor {
	disputeDao := dao.NewDisputeDao(db)
	disputeProcessor := processor.NewDisputeProcessor(conf, disputeDao, ticketClient, createDisputeTicketPub, disputeHelper, userClient)
	return disputeProcessor
}
