// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	"github.com/epifi/gamma/inapphelp/faq/processor/dao"
	dao2 "github.com/epifi/gamma/inapphelp/faq/serving/dao"
	"github.com/epifi/gamma/scripts/refresh_faq/config"
	"github.com/epifi/gamma/scripts/refresh_faq/processor"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeRefreshFAQ(db *gorm.DB, scClient solutions.SolutionsClient, faqPub queue.Publisher, faqConfig *config.FaqConfig) *processor.RefreshFAQ {
	inapphelpPGDB := pgdbProvider(db)
	persistFAQData := dao.NewPersistFAQData(inapphelpPGDB)
	fetchFAQData := dao2.NewFetchFAQData(inapphelpPGDB)
	refreshFAQ := processor.NewRefreshFAQ(persistFAQData, scClient, fetchFAQData, faqPub, faqConfig)
	return refreshFAQ
}

// wire.go:

func pgdbProvider(db *gorm.DB) types.InapphelpPGDB {
	return types.InapphelpPGDB(db)
}
