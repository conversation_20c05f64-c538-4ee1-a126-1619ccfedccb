// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/aws/s3"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/inapphelp/faq/serving/dao"
	dao2 "github.com/epifi/gamma/inapphelp/recent_activity/dao"
	"github.com/epifi/gamma/scripts/refresh_related_faq/config"
	"github.com/epifi/gamma/scripts/refresh_related_faq/processor"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeRefreshRelatedFAQ(db *gorm.DB, s3Client s3.S3Client, relatedFaqCount int, relatedFaqConfig *config.RelatedFaqConfig, txnExecutor storagev2.TxnExecutor) *processor.RefreshRelatedFAQ {
	inapphelpPGDB := pgdbProvider(db)
	fetchFAQData := dao.NewFetchFAQData(inapphelpPGDB)
	relatedFaqDao := dao2.NewRelatedFaqDao(inapphelpPGDB, relatedFaqCount)
	refreshRelatedFAQ := processor.NewRefreshRelatedFAQ(fetchFAQData, relatedFaqDao, s3Client, txnExecutor, relatedFaqConfig)
	return refreshRelatedFAQ
}

// wire.go:

func pgdbProvider(db *gorm.DB) types.InapphelpPGDB {
	return types.InapphelpPGDB(db)
}
