package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/gocarina/gocsv"
	"github.com/google/uuid"
	"go.uber.org/ratelimit"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"
	accountsOperStatusPb "github.com/epifi/gamma/api/accounts/operstatus"
	bankcust2 "github.com/epifi/gamma/api/bankcust"
	savingsPb "github.com/epifi/gamma/api/savings"
	tieringPb "github.com/epifi/gamma/api/tiering"
	"github.com/epifi/gamma/api/tiering/external"
	account1 "github.com/epifi/gamma/api/typesv2/account"
	pkg_savings "github.com/epifi/gamma/pkg/savings"
	"github.com/epifi/gamma/pkg/tiering"
	helper2 "github.com/epifi/gamma/tiering/helper"
)

type PublishSavingsTierUpdateEvent struct {
	savingsTierUpdateEventPublisher queue.DelayPublisher
	tieringClient                   tieringPb.TieringClient
	savingsClient                   savingsPb.SavingsClient
	accountsClient                  accountsOperStatusPb.OperationalStatusServiceClient
	bcClient                        bankcust2.BankCustomerServiceClient
}

// parseActorIds parses actor IDs from FilePath (CSV) or Args1 (comma-separated)
// Arguments:
//   - Args1 ( optional ) : Comma-separated list of actor IDs (alternative: provide CSV file via FilePath with column "Actor_id")
//   - Args2 : OperationalStatusDataFreshness enum value (e.g.,"DATA_FRESHNESS_UNSPECIFIED" ,  "DATA_FRESHNESS_REAL_TIME", "DATA_FRESHNESS_RECENT", "DATA_FRESHNESS_10_MIN_STALE", "DATA_FRESHNESS_LAST_KNOWN").
//     Used to evaluate whether to use scheme code stored in our db or make a vendor call to get the scheme code .
//     DATA_FRESHNESS_UNSPECIFIED : won't check scheme code , simply publish .
//
// Note : csv column name should be Actor_id
func parseActorIds(ctx context.Context, request *JobRequest) ([]string, error) {
	var actorIdList []string
	switch {
	case request.FilePath != "":
		file, err := os.Open(request.FilePath)
		if err != nil {
			logger.Error(ctx, "failed to open CSV file", zap.Error(err), zap.String("file_path", request.FilePath))
			return nil, fmt.Errorf("failed to open CSV file: %w", err)
		}
		defer func() {
			if err := file.Close(); err != nil {
				logger.Error(ctx, "error closing file", zap.Error(err), zap.String("file_path", request.FilePath))
				fmt.Printf("warning: error closing file: %v\n", err)
			}
		}()
		var rows []struct {
			ActorId string `csv:"Actor_id"`
		}
		if err := gocsv.UnmarshalFile(file, &rows); err != nil {
			logger.Error(ctx, "failed to parse CSV file", zap.Error(err), zap.String("file_path", request.FilePath))
			return nil, fmt.Errorf("failed to parse CSV file: %w", err)
		}
		for _, row := range rows {
			actorId := strings.TrimSpace(row.ActorId)
			if actorId != "" {
				actorIdList = append(actorIdList, actorId)
			}
		}
	default:
		actorIdList = getActorIdsAfterCleaning(request.Args1, ",")
	}
	if len(actorIdList) == 0 {
		logger.Error(ctx, "no actor id found", zap.String("Arg1", request.Args1), zap.String("file_path", request.FilePath))
		return nil, fmt.Errorf("actorIdList is empty (provide via CSV file or Args1 comma-separated)")
	}
	return actorIdList, nil
}

func (a *PublishSavingsTierUpdateEvent) checkSchemeCodeMismatch(ctx context.Context, actorId string, currentTier external.Tier, dataFreshnessEnum accountsOperStatusPb.GetOperationalStatusRequest_DataFreshness) (bool, error) {
	// If dataFreshnessEnum is UNSPECIFIED, skip checks and return true to allow publishing
	if dataFreshnessEnum == accountsOperStatusPb.GetOperationalStatusRequest_DATA_FRESHNESS_UNSPECIFIED {
		logger.Info(ctx, "dataFreshnessEnum is UNSPECIFIED, skipping scheme code checks", zap.String("actorId", actorId))
		return true, nil
	}

	// 1. Get Savings Account
	getAccountResp, getAccountErr := a.savingsClient.GetAccount(ctx, &savingsPb.GetAccountRequest{
		Identifier: &savingsPb.GetAccountRequest_ActorUniqueAccountIdentifier{ActorUniqueAccountIdentifier: &savingsPb.ActorUniqueAccountIdentifier{
			ActorId:                actorId,
			AccountProductOffering: account1.AccountProductOffering_APO_REGULAR,
			PartnerBank:            commonvgpb.Vendor_FEDERAL_BANK,
		}},
	})
	if getAccountErr != nil {
		logger.Error(ctx, "failed to get savings account", zap.Error(getAccountErr))
		return false, getAccountErr
	}
	account := getAccountResp.GetAccount()
	if account == nil {
		logger.Error(ctx, "account not found in getAccountResp")
		return false, fmt.Errorf("account not found")
	}
	accountId := account.GetId()

	// 2. Get Operational Status
	operStatusResp, operStatusErr := a.accountsClient.GetOperationalStatus(ctx, &accountsOperStatusPb.GetOperationalStatusRequest{
		AccountIdentifier: &accountsOperStatusPb.GetOperationalStatusRequest_SavingsAccountId{SavingsAccountId: accountId},
		DataFreshness:     dataFreshnessEnum,
	})
	if rpcErr := epifigrpc.RPCError(operStatusResp, operStatusErr); rpcErr != nil {
		logger.Error(ctx, "failed to get operational status", zap.Error(rpcErr), zap.String("accountId", accountId))
		return false, rpcErr
	}
	// 3. Get KYC details from Bank Customer
	bcResp, bcErr := a.bcClient.GetBankCustomer(ctx, &bankcust2.GetBankCustomerRequest{
		Vendor: commonvgpb.Vendor_FEDERAL_BANK,
		Identifier: &bankcust2.GetBankCustomerRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(bcResp, bcErr); rpcErr != nil {
		logger.Error(ctx, "failed to get bank customer", zap.Error(rpcErr))
		return false, rpcErr
	}

	kycDetails := bcResp.GetBankCustomer().GetKycInfo()
	if kycDetails == nil {
		logger.Error(ctx, "kyc details not found in bank customer response")
		return false, fmt.Errorf("kyc details not found")
	}

	// 4. Get Scheme Code from Tier and KYC
	computedSchemeCode, _, schemeCodeErr := pkg_savings.GetSchemeCodeAndSKU(ctx, currentTier, kycDetails.GetKycLevel(), commonvgpb.Vendor_FEDERAL_BANK)
	if schemeCodeErr != nil {
		logger.Error(ctx, "failed to get scheme code from tier and kyc", zap.Error(schemeCodeErr), zap.String("tier", currentTier.String()), zap.String("kycLevel", kycDetails.GetKycLevel().String()))
		return false, schemeCodeErr
	}
	// 5. Compare with account's scheme code
	var accountSchemeCode string
	vendorResp := operStatusResp.GetOperationalStatusInfo().GetVendorResponse()
	switch v := vendorResp.GetVendorResponse().(type) {
	case *accountsOperStatusPb.OperationalStatusVendorResponse_FederalAccountStatusEnquiryResponse:
		if v.FederalAccountStatusEnquiryResponse != nil {
			accountSchemeCode = v.FederalAccountStatusEnquiryResponse.GetSchemeCode()
		}
	case *accountsOperStatusPb.OperationalStatusVendorResponse_FederalAccountStatusCallBackData:
		if v.FederalAccountStatusCallBackData != nil {
			accountSchemeCode = v.FederalAccountStatusCallBackData.GetSchemeCode()
		}
	default:
		logger.Error(ctx, "Unknown or missing vendor response type for scheme code check")
		return false, nil
	}
	if computedSchemeCode == accountSchemeCode {
		logger.Info(ctx, "scheme code matches, skipping publish", zap.String("actorId", actorId), zap.String("schemeCode", computedSchemeCode))
		return false, nil
	}
	logger.Info(ctx, "scheme code mismatch, will publish", zap.String("actorId", actorId), zap.String("computedSchemeCode", computedSchemeCode), zap.String("accountSchemeCode", accountSchemeCode))
	return true, nil
}

func (a *PublishSavingsTierUpdateEvent) DoJob(_ context.Context, request *JobRequest) error {
	ctx := context.Background()

	actorIdList, err := parseActorIds(ctx, request)
	if err != nil {
		return err
	}

	// Convert dataFreshness string to enum
	dataFreshnessEnumVal, ok := accountsOperStatusPb.GetOperationalStatusRequest_DataFreshness_value[request.Args2]
	if !ok {
		logger.Error(ctx, "invalid dataFreshness value provided", zap.String("dataFreshness", request.Args2))
	}
	dataFreshnessEnum := accountsOperStatusPb.GetOperationalStatusRequest_DataFreshness(dataFreshnessEnumVal)

	const (
		rateLimit         = 9
		rateLimitDuration = 2 * time.Second
	)

	// Initialize rate limiter (used for vendor API calls)
	rl := ratelimit.New(rateLimit, ratelimit.Per(rateLimitDuration))

	var failedActors []string
	var wg sync.WaitGroup
	failedIdsCh := make(chan string, len(actorIdList))

	for idx, actorId := range actorIdList {
		wg.Add(1)
		// nocustomlint:goroutine
		go func(idx int, actorId string) {
			defer wg.Done()
			rl.Take() // Rate limiting at the beginning of each goroutine

			if idx%50 == 0 {
				logger.InfoNoCtx(fmt.Sprintf("index checkpoint for actor_ids (idx modulo 50), idx: %d", idx))
			}

			if err := a.processActorWithTimeout(ctx, actorId, dataFreshnessEnum); err != nil {
				failedIdsCh <- actorId
			}
		}(idx, actorId)
	}

	// nocustomlint:goroutine
	go func() {
		// nocustomlint:waitgroup
		wg.Wait()
		close(failedIdsCh)
	}()

	for failedId := range failedIdsCh {
		failedActors = append(failedActors, failedId)
	}

	fmt.Println("---------------------------------------------------------")
	println("Failed Actors:", len(failedActors))
	for _, actorId := range failedActors {
		println(actorId)
	}
	fmt.Println("---------------------------------------------------------")

	return nil
}

func (a *PublishSavingsTierUpdateEvent) processActorWithTimeout(ctx context.Context, actorId string, dataFreshnessEnum accountsOperStatusPb.GetOperationalStatusRequest_DataFreshness) error {
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 1*time.Minute)
	defer cancel() // ✅ Proper cleanup with defer

	ctxWithTimeout = epificontext.WithTraceId(ctxWithTimeout, nil)
	actorCtx := epificontext.CtxWithActorId(ctxWithTimeout, actorId)

	// 1. Get Tiering Pitch
	tieringPitchResp, tieringPitchErr := a.tieringClient.GetTieringPitchV2(actorCtx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if rpcErr := epifigrpc.RPCError(tieringPitchResp, tieringPitchErr); rpcErr != nil {
		logger.Error(actorCtx, "failed to get tiering pitch", zap.Error(rpcErr))
		return rpcErr
	}
	currentTier := tieringPitchResp.GetCurrentTier()

	// 2. Check scheme code mismatch logic
	mismatch, checkErr := a.checkSchemeCodeMismatch(actorCtx, actorId, currentTier, dataFreshnessEnum)
	if checkErr != nil {
		return checkErr
	}
	if !mismatch {
		// No mismatch and not UNSPECIFIED, skip publishing (not an error)
		return nil
	}

	// 3. Publish event
	latestMovement := tiering.GetLatestMovementDetails(tieringPitchResp)
	var movementType external.TierMovementType
	if latestMovement != nil && latestMovement.GetFromTier() == external.Tier_TIER_UNSPECIFIED {
		movementType = external.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE
	} else {
		var err error
		movementType, err = helper2.GetMovementTypeFromStartAndEndTiersExt(latestMovement.GetFromTier(), tieringPitchResp.GetCurrentTier())
		if err != nil {
			logger.Error(actorCtx, "failed to get movement type from start and end tiers", zap.Error(err))
			return err
		}
	}

	// Publishing with 6 minutes delay as the current queues which read from operational-status topic read with a 5 minute delay.
	// Getting that delay removed. Though, this is a stopgap solution for now: https://github.com/epiFi/infra/pull/4807
	_, publishErr := a.savingsTierUpdateEventPublisher.PublishWithDelay(actorCtx, &external.TierUpdateEvent{
		EventId:           uuid.New().String(),
		ActorId:           actorId,
		FromTier:          latestMovement.GetFromTier(),
		ToTier:            tieringPitchResp.GetCurrentTier(),
		MovementType:      movementType,
		EventTimestamp:    timestamppb.Now(),
		MovementTimestamp: latestMovement.GetMovementTimestamp(),
	}, 6*time.Minute)
	if publishErr != nil {
		logger.Error(actorCtx, "failed to publish savings tier update event", zap.Error(publishErr))
		return publishErr
	}

	return nil
}
