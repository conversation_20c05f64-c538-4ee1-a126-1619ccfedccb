// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/pkg/airflow/wrapper"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	"github.com/epifi/gamma/preapprovedloan/wire/types"
	"github.com/epifi/gamma/scripts/pl_analytics/job"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/abfl"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/common"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/federal"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/idfc"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/lenden"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/liquiloans"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/moneyview"
	"github.com/epifi/gamma/scripts/pl_analytics/job/vendors/stockguardian"
	"github.com/epifi/gamma/vendormapping/dao"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialiseJobRegistry(userClient user.UsersClient, actorClient actor.ActorClient, bankCustClient bankcust.BankCustomerServiceClient, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], s3Client s3.S3Client, palVgClient preapprovedloan.PreApprovedLoanClient, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], awsConf aws.Config, vendorMappingDao dao.IVendorMappingDAO, cryptorStore *cryptormap.InMemoryCryptorStore, airflowWrapper wrapper.IAirflowApiWrapper, store types.FireflyRedisStore) *job.Registry {
	helper := job.NewHelper(s3Client)
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types.CacheStorageProvider(store)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanOffersDaoMultiDB := impl.NewCrdbLoanOffersDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	federalFederal := federal.NewFederal(bankCustClient, crdbLoanOfferEligibilityCriteriaDaoMultiDB, actorClient, palVgClient, userClient, crdbLoanOffersDaoMultiDB, txnExecutorProvider, dbConnProvider, vendorMappingDao)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	liquiLoans := liquiloans.NewLiquiloans(s3Client, bankCustClient, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanApplicantDaoMultiDB, txnExecutorProvider, crdbLoanOffersDaoMultiDB, dbConnProvider, vendorMappingDao)
	defaultTime := datetime.NewDefaultTime()
	processor := idfc.NewProcessor(defaultTime, crdbLoanApplicantDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutorProvider, bankCustClient, s3Client, dbConnProvider, vendorMappingDao, airflowWrapper)
	moneyviewProcessor := moneyview.NewProcessor(defaultTime, crdbLoanApplicantDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutorProvider, bankCustClient, s3Client, dbConnProvider, vendorMappingDao)
	abflProcessor := abfl.NewProcessor(defaultTime, crdbLoanApplicantDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutorProvider, bankCustClient, s3Client, dbConnProvider, vendorMappingDao, airflowWrapper)
	stockguardianProcessor := stockguardian.NewProcessor(defaultTime, crdbLoanApplicantDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutorProvider, bankCustClient, s3Client, dbConnProvider, vendorMappingDao, airflowWrapper)
	commonProcessor := common.NewCommonProcessor(crdbLoanApplicantDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, vendorMappingDao)
	lendenProcessor := lenden.NewProcessor(commonProcessor)
	vendors := job.NewVendors(federalFederal, liquiLoans, processor, moneyviewProcessor, abflProcessor, stockguardianProcessor, lendenProcessor)
	processFiEligibleBaseJob := job.NewProcessFiEligibleBaseJob(bankCustClient, helper, vendors)
	processApprovedBaseFromVendorJob := job.NewProcessApprovedBaseFromVendorJob(bankCustClient, helper, vendors)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	disableOffersJob := job.NewDisableOffersJob(bankCustClient, s3Client, crdbLoanOffersDaoMultiDB, crdbLoanRequestsDaoMultiDB, vendorMappingDao)
	extendExpiryFedOffersBatchJob := job.NewExtendExpiryFedOffersBatchJob(bankCustClient, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, palVgClient, dbConnProvider, domainIdGenerator)
	updateVendorReqIdJob := job.NewUpdateVendorReqIdJob(helper, crdbLoanApplicantDaoMultiDB)
	cryptS3DataJob := job.NewCryptS3DataJob(awsConf, helper, cryptorStore)
	generateVendorBureauFileJob := job.NewGenerateVendorBureauFileJob(awsConf, helper)
	processVendorPrequalifiedBaseJob := job.NewProcessVendorPrequalifiedBaseJob(helper, vendors)
	expireLoecJob := job.NewExpireLoecJob(bankCustClient, s3Client, crdbLoanRequestsDaoMultiDB, vendorMappingDao, crdbLoanOfferEligibilityCriteriaDaoMultiDB)
	registry := job.NewRegistry(processFiEligibleBaseJob, processApprovedBaseFromVendorJob, disableOffersJob, extendExpiryFedOffersBatchJob, updateVendorReqIdJob, cryptS3DataJob, generateVendorBureauFileJob, processVendorPrequalifiedBaseJob, expireLoecJob)
	return registry
}
