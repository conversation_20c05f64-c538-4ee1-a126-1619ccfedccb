// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/epifi/be-common/api/celestial"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/crypto/cryptormap"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/goxmldsig"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/queue"
	"github.com/epifi/be-common/pkg/redactor/httpcontentredactor"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/be-common/pkg/vendorapi"
	genconf3 "github.com/epifi/be-common/pkg/vendorapi/config/genconf"
	"github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/auth"
	"github.com/epifi/gamma/api/auth/developer"
	"github.com/epifi/gamma/api/auth/liveness"
	developer2 "github.com/epifi/gamma/api/auth/liveness/developer"
	location2 "github.com/epifi/gamma/api/auth/location"
	"github.com/epifi/gamma/api/auth/orchestrator"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/bre"
	"github.com/epifi/gamma/api/collection"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/connected_account/analytics"
	"github.com/epifi/gamma/api/consent"
	"github.com/epifi/gamma/api/credit_limit_estimator"
	"github.com/epifi/gamma/api/creditreportv2"
	"github.com/epifi/gamma/api/docs"
	"github.com/epifi/gamma/api/docs/esign"
	"github.com/epifi/gamma/api/employment"
	"github.com/epifi/gamma/api/frontend/credit_report"
	preapprovedloan2 "github.com/epifi/gamma/api/frontend/preapprovedloan"
	"github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/api/kyc"
	"github.com/epifi/gamma/api/kyc/vkyc"
	"github.com/epifi/gamma/api/nudge"
	"github.com/epifi/gamma/api/order"
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/paymentinstrument"
	"github.com/epifi/gamma/api/paymentinstrument/account_pi"
	preapprovedloan3 "github.com/epifi/gamma/api/preapprovedloan"
	"github.com/epifi/gamma/api/preapprovedloan/cx"
	"github.com/epifi/gamma/api/risk/profile"
	"github.com/epifi/gamma/api/salaryprogram"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/segment"
	esign2 "github.com/epifi/gamma/api/stockguardian/sgapigateway/esign"
	"github.com/epifi/gamma/api/stockguardian/sgapigateway/lms"
	"github.com/epifi/gamma/api/user"
	credit_report2 "github.com/epifi/gamma/api/user/credit_report"
	developer3 "github.com/epifi/gamma/api/user/developer"
	"github.com/epifi/gamma/api/user/location"
	"github.com/epifi/gamma/api/user/obfuscator"
	"github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/api/vendorgateway/ekyc"
	"github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics"
	"github.com/epifi/gamma/api/vendorgateway/lending/digitap"
	"github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/idfc"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans"
	"github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview"
	"github.com/epifi/gamma/api/vendorgateway/lending/securedloans/fiftyfin"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/accounts"
	"github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	savings2 "github.com/epifi/gamma/api/vendorgateway/openbanking/savings"
	"github.com/epifi/gamma/api/vendorgateway/pan"
	http2 "github.com/epifi/gamma/api/vendors/http"
	impl2 "github.com/epifi/gamma/collection/dao/impl"
	"github.com/epifi/gamma/collection/dao/utils"
	dao2 "github.com/epifi/gamma/creditreportv2/dao"
	impl3 "github.com/epifi/gamma/leads/dao/impl"
	utils2 "github.com/epifi/gamma/leads/dao/utils"
	"github.com/epifi/gamma/pkg/persistentqueue"
	"github.com/epifi/gamma/preapprovedloan/config/common"
	"github.com/epifi/gamma/preapprovedloan/config/common/genconf"
	genconf2 "github.com/epifi/gamma/preapprovedloan/config/worker/genconf"
	"github.com/epifi/gamma/preapprovedloan/dao/impl"
	events2 "github.com/epifi/gamma/preapprovedloan/events"
	"github.com/epifi/gamma/preapprovedloan/helper"
	"github.com/epifi/gamma/preapprovedloan/helper/agreement"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	abfl2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/abfl"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/baseprovider"
	fiftyfin3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/fiftyfin"
	finflux2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/finflux"
	idfc3 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/idfc"
	lenden2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/lenden"
	liquiloans2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/liquiloans"
	moneyview2 "github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/moneyview"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers/stockguardian"
	"github.com/epifi/gamma/preapprovedloan/userdata"
	fiftyfin2 "github.com/epifi/gamma/preapprovedloan/vendor_data_provider/fiftyfin"
	types3 "github.com/epifi/gamma/preapprovedloan/wire/types"
	"github.com/epifi/gamma/scripts/pal/config"
	helper2 "github.com/epifi/gamma/scripts/pal/helper"
	"github.com/epifi/gamma/scripts/pal/job"
	"github.com/epifi/gamma/scripts/pal/job/audit/purge"
	"github.com/epifi/gamma/scripts/pal/job/credit_report_purge"
	"github.com/epifi/gamma/scripts/pal/job/lamf"
	"github.com/epifi/gamma/scripts/pal/job/lms_data_difference"
	"github.com/epifi/gamma/scripts/pal/job/vendors"
	idfc2 "github.com/epifi/gamma/scripts/pal/job/vendors/idfc"
	types2 "github.com/epifi/gamma/scripts/pal/wire/type"
	config2 "github.com/epifi/gamma/vendorgateway/config"
	"github.com/epifi/gamma/vendormapping/dao"
	"github.com/google/wire"
	"go.temporal.io/sdk/client"
	"gorm.io/gorm"
	"net/http"
)

// Injectors from wire.go:

func InitialiseJobRegistry(crdb types.LoansFederalPGDB, dbConnProvider *storagev2.DBResourceProvider[*gorm.DB], epifiDb types.EpifiCRDB, pgdb types.PostgresPGDB, pgdbTxnExecutor types.PGDBTxnExecutor, palVgClient preapprovedloan.PreApprovedLoanClient, accountVgClient accounts.AccountsClient, userClient user.UsersClient, actorClient actor.ActorClient, bankCustClient bankcust.BankCustomerServiceClient, authClient auth.AuthClient, commsClient comms.CommsClient, savingsClient savings.SavingsClient, s3Client s3.S3Client, broker events.Broker, conf *config.Config, txnExecutorProvider *storagev2.DBResourceProvider[storagev2.IdempotentTxnExecutor], celestialClient celestial.CelestialClient, lvClient liveness.LivenessClient, vkycClient vkyc.VKYCClient, kycClient kyc.KycClient, vgCustomerClient customer.CustomerClient, eSignClient esign.ESignClient, orderClient order.OrderServiceClient, payClient pay.PayClient, piClient paymentinstrument.PiClient, accountPiClient account_pi.AccountPIRelationClient, paymentClient payment.PaymentClient, savingsVgClient savings2.SavingsClient, llPalVgClient liquiloans.LiquiloansClient, profileClient profile.ProfileClient, authOrchClient orchestrator.OrchestratorClient, persistentQueue persistentqueue.PersistentQueue, salaryClient salaryprogram.SalaryProgramClient, employmentClient employment.EmploymentClient, fePalClient preapprovedloan2.PreApprovedLoanClient, creditReportClient credit_report.CreditReportClient, authDb types.AuthPGDB, awsConf aws.Config, idfcVgClient idfc.IdfcClient, palClient preapprovedloan3.PreApprovedLoanClient, creditReportManager credit_report2.CreditReportManagerClient, creditReportManagerV2 creditreportv2.CreditReportManagerClient, onbClient onboarding.OnboardingClient, obfuscatorClient obfuscator.ObfuscatorClient, accountBalanceClient balance.BalanceClient, collectionClient collection.CollectionClient, docsClient docs.DocsClient, panVgClient pan.PANClient, fiftyFinVgClient fiftyfin.FiftyFinClient, segmentationServiceClient segment.SegmentationServiceClient, limitEstimatorClient credit_limit_estimator.CreditLimitEstimatorClient, abflClient abfl.AbflClient, provider types2.CrdbConnProvider, commonGenConf *genconf.CreditReportConfig, dataDevS3Client types2.DataDevS3Client, cryptorStore *cryptormap.InMemoryCryptorStore, mfExternalOrderClient external.MFExternalOrdersClient, userLocationClient location.LocationClient, eKycVgClient ekyc.EKYCClient, moneyviewVgPbClient moneyview.MoneyviewClient, nudgeClient nudge.NudgeServiceClient, vendorMappingDao dao.IVendorMappingDAO, cibilReportPublisher queue.Publisher, temporalClient client.Client, credgenicsClient credgenics.CredgenicsClient, redisClient types3.FireflyRedisStore, palS3client types2.PalS3Client, palCxClient cx.CxClient, consentClient consent.ConsentClient, devAuthClient developer.DevAuthClient, devLivenessClient developer2.DevLivenessClient, devUserClient developer3.DevUserClient, sgEsignApiGateway esign2.EsignClient, finFluxVgClient finflux.FinfluxClient, sgLmsApiGateway lms.LmsClient, lendenClient lenden.LendenClient, analyticsClient analytics.AnalyticsClient, breClient bre.BreClient, workerGenConf *genconf2.Config, caPgdb types.ConnectedAccountPGDB, locationClient location2.LocationClient, digitapVgClient digitap.DigitapServiceClient, vendorApiGenConf *genconf3.Config, dsaReportS3Client types2.DSAReportS3Client) *job.Registry {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	lendingCacheStorage := types3.CacheStorageProvider(redisClient)
	crdbLoanAccountsDaoMultiDB := impl.NewCrdbLoanAccountsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	crdbLoanPaymentRequestsDao := impl.NewCrdbLoanPaymentRequestsDao(dbConnProvider, domainIdGenerator)
	defaultTime := datetime.NewDefaultTime()
	crdbLoanStepExecutionsDaoMultiDB := impl.NewCrdbLoanStepExecutionsDaoMultiDB(dbConnProvider, domainIdGenerator)
	notification := notificationConfigProvider(conf)
	crdbLoanActivityDaoMultiDB := impl.NewCrdbLoanActivityDaoMultiDB(dbConnProvider, domainIdGenerator)
	crdbLoanApplicantDaoMultiDB := impl.NewCrdbLoanApplicantDaoMultiDB(dbConnProvider, domainIdGenerator)
	preApprovedLoanS3Client := preApprovedLoanS3ClientProvider(awsConf, conf)
	crdbLoanRequestsDaoMultiDB := impl.NewCrdbLoanRequestsDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	rpcHelper := helper.NewRpcHelper(palVgClient, actorClient, userClient, savingsClient, celestialClient, lvClient, kycClient, vgCustomerClient, authClient, eSignClient, defaultTime, commsClient, crdbLoanStepExecutionsDaoMultiDB, notification, orderClient, payClient, piClient, accountPiClient, paymentClient, savingsVgClient, crdbLoanActivityDaoMultiDB, llPalVgClient, crdbLoanApplicantDaoMultiDB, profileClient, bankCustClient, salaryClient, idfcVgClient, accountVgClient, creditReportManager, creditReportManagerV2, commonGenConf, onbClient, obfuscatorClient, accountBalanceClient, panVgClient, fiftyFinVgClient, segmentationServiceClient, preApprovedLoanS3Client, limitEstimatorClient, mfExternalOrderClient, userLocationClient, broker, crdbLoanRequestsDaoMultiDB, sgEsignApiGateway, locationClient)
	liquiLoansPrepaymentJob := job.NewLiquiLoansPrepaymentJob(s3Client, llPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDao, rpcHelper)
	crdbLoanOfferEligibilityCriteriaDaoMultiDB := impl.NewCrdbLoanOfferEligibilityCriteriaDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	helperHelper := helper2.NewHelper(s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, broker, bankCustClient, actorClient, userClient, vkycClient, authOrchClient, lvClient, llPalVgClient, piClient, accountPiClient, palVgClient, cryptorStore)
	processFiEligibleBaseJob := job.NewProcessFiEligibleBaseJob(helperHelper)
	crdbLoanOffersDaoMultiDB := impl.NewCrdbLoanOffersDaoMultiDB(dbConnProvider, domainIdGenerator, lendingCacheStorage)
	db := types.LoansFederalPGDBGormDBProvider(crdb)
	txnExecutor := newGormTxnExecutorProvider(db)
	processEligibleBaseFromVendorJob := job.NewProcessEligibleBaseFromVendorJob(bankCustClient, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutor, actorClient, userClient, palVgClient, helperHelper)
	deleteRiskyUsersJob := job.NewDeleteRiskyUsersJob(db)
	retryFetchDetailsV2Job := job.NewRetryFetchDetailsV2Job(palVgClient)
	getStatementJob := job.NewGetStatementJob(accountVgClient, authClient, actorClient, userClient, crdbLoanAccountsDaoMultiDB, helperHelper, s3Client, bankCustClient, savingsClient)
	populateLecFromVendorFileJob := job.NewPopulateLecFromVendorFileJob(helperHelper)
	federal := vendors.NewFederal(bankCustClient, crdbLoanOfferEligibilityCriteriaDaoMultiDB)
	liquiLoans := vendors.NewLiquiloans(s3Client, bankCustClient, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanApplicantDaoMultiDB, txnExecutor, txnExecutorProvider, llPalVgClient)
	processor := idfc2.NewProcessor(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDao, orderClient)
	jobVendors := job.NewVendors(federal, liquiLoans, processor)
	populateLecFromVendorJob := job.NewPopulateLecFromVendorJob(bankCustClient, helperHelper, jobVendors)
	multiDbDoOnce := newMultiDbDoOnce(dbConnProvider)
	commsHelper := helper.NewCommsHelper(commsClient, rpcHelper)
	sendEmiCommsJob := job.NewSendEmiCommsJob(dbConnProvider, actorClient, savingsClient, crdbLoanAccountsDaoMultiDB, accountBalanceClient, broker, multiDbDoOnce, commsHelper, conf, nudgeClient)
	createNewOfferJob := job.NewCreateNewOfferJob(crdbLoanOffersDaoMultiDB, crdbLoanApplicantDaoMultiDB, txnExecutor, txnExecutorProvider, dbConnProvider, crdbLoanOfferEligibilityCriteriaDaoMultiDB)
	extendExpiryFedOffersJob := job.NewExtendExpiryFedOffersJob(bankCustClient, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutor, actorClient, userClient, palVgClient, helperHelper)
	processLlEligibleBaseJob := job.NewProcessLlEligibleBaseJob(s3Client, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, txnExecutor, txnExecutorProvider)
	creditReportDaoCrdb := dao2.NewCreditReportDaoCrdb(epifiDb)
	creditReportFlattenDaoPg := dao2.NewCreditReportFlattenDaoPg(pgdb)
	creditReportFlattenAdhocJob := job.NewCreditReportFlattenAdhocJob(bankCustClient, creditReportDaoCrdb, creditReportFlattenDaoPg)
	creditReportFlattenJob := job.NewCreditReportFlattenJob(creditReportDaoCrdb, creditReportFlattenDaoPg)
	updateInstallmentInfoJob := job.NewUpdateInstallmentInfoJob(db, actorClient, userClient, palVgClient, helperHelper)
	fetchLlRejectedBaseJob := job.NewFetchLlRejectedBaseJob(crdbLoanStepExecutionsDaoMultiDB, crdbLoanApplicantDaoMultiDB, dbConnProvider)
	crdbLoanInstallmentInfoDaoMultiDB := impl.NewCrdbLoanInstallmentInfoDaoMultiDB(dbConnProvider, domainIdGenerator)
	triggerLlActivityJob := job.NewTriggerLlActivityJob(crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, txnExecutorProvider, llPalVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	setLoanProgramInLoanOffersJob := job.NewSetLoanProgramInLoanOffersJob(crdbLoanOffersDaoMultiDB, dbConnProvider)
	backfillCkycVideoPQJob := job.NewBackfillCkycVideoPQJob(dbConnProvider, crdbLoanAccountsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, persistentQueue, txnExecutor, lvClient, helperHelper)
	loanAccountReconJob := job.NewLoanAccountReconJob(accountVgClient, authClient, actorClient, userClient, paymentClient, orderClient, savingsClient, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutor, txnExecutorProvider, helperHelper)
	cryptS3DataJob := job.NewCryptS3DataJob(awsConf, cryptorStore)
	crdbLoanInstallmentPayoutDao := impl.NewCrdbLoanInstallmentPayoutDao(dbConnProvider, domainIdGenerator)
	repaymentCaptureJob := job.NewRepaymentCaptureJob(dbConnProvider, helperHelper, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDao, crdbLoanActivityDaoMultiDB, txnExecutorProvider, llPalVgClient, palClient, collectionClient, celestialClient)
	loanActivityBackFillJob := job.NewLoanActivityBackFillJob(accountVgClient, authClient, actorClient, userClient, paymentClient, orderClient, savingsClient, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, txnExecutor, txnExecutorProvider, helperHelper)
	generateVendorBureauFileJob := job.NewGenerateVendorBureauFileJob(awsConf)
	disableOffersJob := job.NewDisableOffersJob(bankCustClient, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanRequestsDaoMultiDB, txnExecutor, actorClient, userClient, palVgClient, helperHelper)
	loanSchemeBackFillJob := job.NewLoanSchemeBackFillJob(dbConnProvider, helperHelper, crdbLoanOfferEligibilityCriteriaDaoMultiDB)
	syncLoanAccountJob := job.NewSyncLoanAccountJob(dbConnProvider, actorClient, userClient, crdbLoanStepExecutionsDaoMultiDB, palVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, txnExecutorProvider, helperHelper)
	deactivateAllOffersJob := job.NewDeactivateAllOffersJob(crdbLoanRequestsDaoMultiDB, dbConnProvider)
	deactivateOfferIfLoanTakenJob := job.NewDeactivateOfferIfLoanTakenJob(db, dbConnProvider)
	cleanUpPayoutsJob := job.NewCleanUpPayoutsJob(dbConnProvider, helperHelper, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDao, crdbLoanAccountsDaoMultiDB, txnExecutorProvider, palVgClient, palClient)
	resolveLOECJob := job.NewResolveLOECJob(bankCustClient, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB, txnExecutor, actorClient, userClient, palVgClient, helperHelper)
	siCollectionJob := job.NewSiCollectionJob(awsConf, palClient, crdbLoanAccountsDaoMultiDB)
	backfillLoanAccountIdJob := job.NewBackfillLoanAccountIdJob(dbConnProvider, crdbLoanInstallmentPayoutDao, txnExecutor, crdbLoanStepExecutionsDaoMultiDB, helperHelper, s3Client, crdbLoanApplicantDaoMultiDB, crdbLoanRequestsDaoMultiDB, userClient, vendorMappingDao, llPalVgClient)
	resolveEMIDefaultsJob := job.NewResolveEMIDefaultsJob(crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDao, crdbLoanInstallmentInfoDaoMultiDB)
	agreementProvider := agreement.NewAgreementProvider(rpcHelper, abflClient, crdbLoanStepExecutionsDaoMultiDB)
	esEsignTriggerJob := job.NewEsEsignTriggerJob(palClient, agreementProvider, docsClient, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB)
	createIdfcCugOffersJob := job.NewCreateIdfcCugOffersJob(crdbLoanOffersDaoMultiDB, crdbLoanApplicantDaoMultiDB, txnExecutor, txnExecutorProvider, dbConnProvider)
	deleteLoanAccountsJob := job.NewDeleteLoanAccountsJob(dbConnProvider)
	getAddressJob := job.NewGetAddressJob(dbConnProvider)
	fixLoanApplicantJob := job.NewFixLoanApplicantJob(dbConnProvider, bankCustClient, s3Client, crdbLoanOffersDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	resetChargesForSubventionUsers := job.NewResetChargesForSubventionUsers(crdbLoanInstallmentPayoutDao, crdbLoanInstallmentInfoDaoMultiDB, dbConnProvider)
	backfillUTRJob := job.NewBackfillUTRJob(dbConnProvider, orderClient)
	bureauReportingSubventionJob := job.NewBureauReportingSubventionJob(palClient, crdbLoanAccountsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, s3Client, rpcHelper)
	updateVendorReqIdIdfcJob := job.NewUpdateVendorReqIdIdfcJob(helperHelper, crdbLoanApplicantDaoMultiDB)
	brePipingJob := job.NewBrePipingJob(conf, helperHelper, crdbLoanOfferEligibilityCriteriaDaoMultiDB, dataDevS3Client, txnExecutorProvider)
	syncLoanAccountLLJob := job.NewSyncLoanAccountLLJob(llPalVgClient, dbConnProvider, crdbLoanStepExecutionsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, helperHelper, txnExecutorProvider)
	deleteDuplicateLoanLiiJob := job.NewDeleteDuplicateLoanLiiJob(dbConnProvider)
	deactivateFederalOffersJob := job.NewDeactivateFederalOffersJob(bankCustClient, kycClient, eKycVgClient, userClient, panVgClient, onbClient, s3Client, crdbLoanOffersDaoMultiDB)
	processScrubDataJob := job.NewProcessScrubDataJob(awsConf)
	processEncryptedScrubDataJob := job.NewProcessEncryptedScrubDataJob(awsConf, cryptorStore)
	processPrepaymentAtVendorJob := job.NewProcessPrepaymentAtVendorJob(jobVendors)
	mvPanDedupeJob := job.NewMvPanDedupeJob(s3Client, helperHelper, moneyviewVgPbClient)
	fetchRepaymentScheduleJob := job.NewFetchRepaymentScheduleJob(jobVendors)
	fetchLoanStatusJob := job.NewFetchLoanStatusJob(jobVendors)
	lamfResetFailedOfferGenLseJob := job.NewLamfResetFailedOfferGenLseJob(dbConnProvider, provider, temporalClient, celestialClient, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB)
	backfillLoanActivitiesUTRJob := job.NewBackfillLoanActivitiesUTRJob(dbConnProvider, crdbLoanAccountsDaoMultiDB, llPalVgClient, helperHelper, vendorMappingDao, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, s3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, payClient, crdbLoanActivityDaoMultiDB)
	deleteUserLoanDataJob := job.NewDeleteUserLoanDataJob(crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, dbConnProvider)
	collectionsSyncLeadJob := job.NewCollectionsSyncLeadJob(temporalClient)
	normalizeCibilJob := job.NewNormalizeCibilJob(awsConf)
	creditReportDownloadDaoCrdb := dao2.NewCreditReportDownloadDaoCrdb(epifiDb)
	creditReportUserSubscriptionDetailDaoCrdb := dao2.NewCreditReportUserSubscriptionDetailDaoCrdb(epifiDb)
	backfillCibilCustIdMismatch := job.NewBackfillCibilCustIdMismatch(epifiDb, creditReportDownloadDaoCrdb, creditReportUserSubscriptionDetailDaoCrdb)
	backfillCibilReportsJob := job.NewBackfillCibilReportsJob(pgdb, cibilReportPublisher)
	reconLoanPreClosureJob := job.NewReconLoanPreClosureJob(crdbLoanPaymentRequestsDao, crdbLoanAccountsDaoMultiDB, palClient)
	lamfStuckUserManualResolution := lamf.NewLamfStuckUserManualResolution(temporalClient, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanAccountsDaoMultiDB, fiftyFinVgClient, celestialClient, conf, txnExecutorProvider)
	dbProvider := utils.NewDBProvider(dbConnProvider)
	leadDaoPgdb := impl2.NewLeadDaoPgdb(dbProvider, domainIdGenerator)
	allocationDaoPgdb := impl2.NewAllocationDaoPgdb(dbProvider, domainIdGenerator)
	collectionsAllocationSyncJob := job.NewCollectionsAllocationSyncJob(leadDaoPgdb, allocationDaoPgdb, credgenicsClient)
	processBillzyPaymentsJob := job.NewProcessBillzyPaymentsJob(awsConf, collectionClient, palClient)
	lms_data_differenceJob := lms_data_difference.NewJob(palClient, crdbLoanAccountsDaoMultiDB)
	fillFederalDPDUserDataJob := job.NewFederalDPDUserDataJob(vendorMappingDao, creditReportDaoCrdb, awsConf, helperHelper)
	uploadITRFileToSFTPJob := job.NewUploadITRFileToSFTPJob(awsConf, conf, crdbLoanApplicantDaoMultiDB, dbConnProvider, crdbLoanRequestsDaoMultiDB)
	createInterestAccountPaymentInstrumentJob := lamf.NewCreateInterestAccountPaymentInstrumentJob(dbConnProvider, piClient, accountPiClient)
	creditReportDataRecoveryJob := job.NewCreditReportDataRecoveryJob(epifiDb, pgdb)
	autoPayJob := job.NewAutoPayJob(crdbLoanAccountsDaoMultiDB, palClient)
	postNachPaymentsToCredgenicsJob := job.NewPostNachPaymentsToCredgenicsJob(palS3client, collectionClient, crdbLoanAccountsDaoMultiDB)
	softDeleteCreditReportDataJob := credit_report_purge.NewSoftDeleteCreditReportDataJob(epifiDb, pgdb, conf)
	softDeleteOldCreditReportDataJob := credit_report_purge.NewSoftDeleteOldCreditReportDataJob(conf, epifiDb, pgdb)
	hardDeleteFlattenedCreditReportDataJob := credit_report_purge.NewHardDeleteFlattenedCreditReportDataJob(epifiDb, pgdb, conf)
	hardDeleteFlattenedCreditReportData2Job := credit_report_purge.NewHardDeleteFlattenedCreditReportData2Job(pgdb, conf)
	oneTimeJob := job.NewOneTimeJob(s3Client, crdbLoanAccountsDaoMultiDB, creditReportManagerV2, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, vendorMappingDao)
	updateCreditReportDownloadDataJob := credit_report_purge.NewUpdateCreditReportDownloadDataJob(epifiDb)
	applyOfferDiscountAfterUserDropoffJob := job.NewApplyOfferDiscountAfterUserDropoffJob(crdbLoanOffersDaoMultiDB, crdbLoanRequestsDaoMultiDB, dbConnProvider, broker)
	softDeleteCibilReportDataJob := credit_report_purge.NewSoftDeleteCibilReportDataJob(pgdb, conf)
	hardDeleteFlattenedCibilReportDataJob := credit_report_purge.NewHardDeleteFlattenedCibilReportDataJob(pgdb, conf)
	deactivateAllLoanApplicationJob := job.NewDeactivateAllLoanApplicationJob(dbConnProvider, crdbLoanRequestsDaoMultiDB, palCxClient, crdbLoanStepExecutionsDaoMultiDB)
	purgeCkycDataJob := job.NewPurgeCkycDataJob(dbConnProvider, crdbLoanStepExecutionsDaoMultiDB)
	abflRegulatoryDataJob := job.NewAbflRegulatoryDataJob(crdbLoanAccountsDaoMultiDB, dbConnProvider, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, consentClient, userClient, palS3client, devAuthClient, devLivenessClient, devUserClient)
	inMemoryCacheService := cache.NewInMemoryCacheService()
	llLoanDataProvider := liquiloans2.NewLiquiloansProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, inMemoryCacheService)
	federalLoanDataProvider := providers.NewFederalDataProvider(palVgClient, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB, lendingCacheStorage)
	llEsProvider := liquiloans2.NewLlEsProvider(llLoanDataProvider, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentPayoutDao)
	idfcProvider := idfc3.NewProvider(idfcVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, inMemoryCacheService, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoMultiDB, rpcHelper)
	llFiliteProvider := liquiloans2.NewLlFiliteProvider(crdbLoanStepExecutionsDaoMultiDB)
	llLoanFLDGDataProvider := liquiloans2.NewLiquiloansFLDGProvider(llPalVgClient, crdbLoanInstallmentInfoDaoMultiDB, defaultTime)
	lamfVendorDataProvider := fiftyfin2.NewLamfVendorDataProvider(fiftyFinVgClient, lendingCacheStorage)
	baseProvider := fiftyfin3.NewBaseProvider(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanApplicantDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanInstallmentPayoutDao, crdbLoanPaymentRequestsDao, defaultTime, inMemoryCacheService, lamfVendorDataProvider)
	abflDataProvider := abfl2.NewAbflDataProvider(abflClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB)
	finfluxBaseProvider := finflux2.NewFinfluxBaseProvider(finFluxVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanPaymentRequestsDao, crdbLoanInstallmentPayoutDao, crdbLoanInstallmentInfoDaoMultiDB)
	baseLoanDataProvider := baseprovider.NewBaseLoanDataProvider(crdbLoanAccountsDaoMultiDB, crdbLoanActivityDaoMultiDB)
	sGdataProvider := stockguardian.NewSGdataProvider(sgLmsApiGateway, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB)
	lendenProvider := lenden2.NewProvider(lendenClient, crdbLoanAccountsDaoMultiDB, palClient)
	mvLoanDataProvider := moneyview2.NewMvLoanDataProvider(moneyviewVgPbClient)
	abflPwaJourneyDataProvider := abfl2.NewABFLPwaJourneyDataProvider(abflClient, crdbLoanRequestsDaoMultiDB)
	factory := loandataprovider.NewFactory(llLoanDataProvider, federalLoanDataProvider, llEsProvider, idfcProvider, llFiliteProvider, llLoanFLDGDataProvider, baseProvider, abflDataProvider, finfluxBaseProvider, baseLoanDataProvider, sGdataProvider, lendenProvider, mvLoanDataProvider, abflPwaJourneyDataProvider)
	loanAccountCreationLAMFJob := lamf.NewLoanAccountCreationLAMFJob(fiftyFinVgClient, crdbLoanAccountsDaoMultiDB, crdbLoanInstallmentInfoDaoMultiDB, crdbLoanRequestsDaoMultiDB, crdbLoanActivityDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, helperHelper, dbConnProvider, crdbLoanApplicantDaoMultiDB, txnExecutorProvider, crdbLoanInstallmentPayoutDao, piClient, accountPiClient, factory)
	storeExperianConsentsJob := job.NewStoreExperianConsentsJob(preApprovedLoanS3Client, epifiDb, rpcHelper, conf)
	updateLrLseStatusJob := job.NewUpdateLrLseStatusJob(palVgClient, crdbLoanRequestsDaoMultiDB, crdbLoanStepExecutionsDaoMultiDB, db, helperHelper, txnExecutorProvider, dbConnProvider)
	syncAllActiveLeadsCollectionsJob := job.NewSyncAllActiveLeadsCollectionsJob(celestialClient)
	updateLoanOffersJob := job.NewUpdateLoanOffersJob(crdbLoanOffersDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, db, txnExecutorProvider, dbConnProvider)
	initiateAnalysisJob := job.NewInitiateAnalysisJob(s3Client, helperHelper, analyticsClient, caPgdb, vendorMappingDao)
	acqEventPublisherImpl := events2.NewAcqEventPublisher(savingsClient, mfExternalOrderClient, broker)
	updateAbflLoanStatus := job.NewUpdateAbflLoanStatus(helperHelper, crdbLoanRequestsDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanAccountsDaoMultiDB, palS3client, broker, rpcHelper, crdbLoanOfferEligibilityCriteriaDaoMultiDB, acqEventPublisherImpl)
	commonUserDataProvider := userdata.NewCommonUserDataProvider(rpcHelper)
	backfillRejectedBreDataJob := job.NewBackfillRejectedBreDataJob(breClient, commonUserDataProvider, workerGenConf, dataDevS3Client, crdbLoanOfferEligibilityCriteriaDaoMultiDB, onbClient, savingsClient)
	epfoDataFetchJob := job.NewEpfoDataFetchJob(conf, digitapVgClient, commsClient)
	string2 := envProvider(conf)
	httpClient := SecureHttpClientProviderNew(vendorApiGenConf, string2, conf)
	signingContext := NilSigningContextProvider()
	httpContentRedactor := httpcontentredactor.GetInstance()
	httpRequestHandler := vendorapi.New(httpClient, signingContext, httpContentRedactor, vendorApiGenConf, string2)
	fedLentraCugTestingJob := job.NewFedLentraCugTestingJob(httpRequestHandler, httpClient, conf, palVgClient)
	retryDedupeJob := job.NewRetryDedupeJob(crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanRequestsDaoMultiDB, dbConnProvider, commonUserDataProvider, moneyviewVgPbClient, celestialClient, userClient, txnExecutorProvider, palClient, broker)
	loanFunnelTrackingJob := job.NewLoanFunnelTrackingJob(conf, userClient, crdbLoanStepExecutionsDaoMultiDB, crdbLoanOffersDaoMultiDB, crdbLoanOfferEligibilityCriteriaDaoMultiDB, dbConnProvider, dsaReportS3Client, epifiDb, commsClient)
	updateFederalLoanRequestVendorIdJob := job.NewUpdateFederalLoanRequestVendorIdJob(crdbLoanRequestsDaoMultiDB, crdbLoanOffersDaoMultiDB, palClient, txnExecutorProvider)
	loansOnboardingJob := job.NewLoansOnboardingJob(fePalClient, creditReportClient, dbConnProvider, authClient, epifiDb, authDb)
	getSoaStatementJob := job.NewGetSoaStatementJob(crdbLoanRequestsDaoMultiDB, helperHelper, llPalVgClient)
	updateLoanRequestSubStatusNonProd := job.NewUpdateLoanRequestSubStatusNonProd(crdbLoanRequestsDaoMultiDB, helperHelper)
	loecBackfilling := job.NewLOECBackfilling(dbConnProvider, crdbLoanOfferEligibilityCriteriaDaoMultiDB, crdbLoanOffersDaoMultiDB)
	utilsDBProvider := utils2.NewDBProvider(dbConnProvider)
	userLeadDao := impl3.NewUserLeadDao(utilsDBProvider, domainIdGenerator)
	refreshUserLeadJob := job.NewRefreshUserLeadJob(dbConnProvider, helperHelper, userLeadDao, txnExecutorProvider, palClient)
	lenderDataJob := purge.NewLenderDataJob(dbConnProvider, crdbLoanStepExecutionsDaoMultiDB, crdbLoanRequestsDaoMultiDB)
	registry := job.NewRegistry(liquiLoansPrepaymentJob, processFiEligibleBaseJob, processEligibleBaseFromVendorJob, deleteRiskyUsersJob, retryFetchDetailsV2Job, getStatementJob, populateLecFromVendorFileJob, populateLecFromVendorJob, sendEmiCommsJob, createNewOfferJob, extendExpiryFedOffersJob, processLlEligibleBaseJob, creditReportFlattenAdhocJob, creditReportFlattenJob, updateInstallmentInfoJob, fetchLlRejectedBaseJob, triggerLlActivityJob, setLoanProgramInLoanOffersJob, backfillCkycVideoPQJob, loanAccountReconJob, cryptS3DataJob, repaymentCaptureJob, loanActivityBackFillJob, generateVendorBureauFileJob, disableOffersJob, loanSchemeBackFillJob, syncLoanAccountJob, deactivateAllOffersJob, deactivateOfferIfLoanTakenJob, cleanUpPayoutsJob, resolveLOECJob, siCollectionJob, backfillLoanAccountIdJob, resolveEMIDefaultsJob, esEsignTriggerJob, createIdfcCugOffersJob, deleteLoanAccountsJob, getAddressJob, fixLoanApplicantJob, resetChargesForSubventionUsers, backfillUTRJob, bureauReportingSubventionJob, updateVendorReqIdIdfcJob, brePipingJob, syncLoanAccountLLJob, deleteDuplicateLoanLiiJob, deactivateFederalOffersJob, processScrubDataJob, processEncryptedScrubDataJob, processPrepaymentAtVendorJob, mvPanDedupeJob, fetchRepaymentScheduleJob, fetchLoanStatusJob, lamfResetFailedOfferGenLseJob, backfillLoanActivitiesUTRJob, deleteUserLoanDataJob, collectionsSyncLeadJob, normalizeCibilJob, backfillCibilCustIdMismatch, backfillCibilReportsJob, reconLoanPreClosureJob, lamfStuckUserManualResolution, collectionsAllocationSyncJob, processBillzyPaymentsJob, lms_data_differenceJob, fillFederalDPDUserDataJob, uploadITRFileToSFTPJob, createInterestAccountPaymentInstrumentJob, creditReportDataRecoveryJob, autoPayJob, postNachPaymentsToCredgenicsJob, softDeleteCreditReportDataJob, softDeleteOldCreditReportDataJob, hardDeleteFlattenedCreditReportDataJob, hardDeleteFlattenedCreditReportData2Job, oneTimeJob, updateCreditReportDownloadDataJob, applyOfferDiscountAfterUserDropoffJob, softDeleteCibilReportDataJob, hardDeleteFlattenedCibilReportDataJob, deactivateAllLoanApplicationJob, purgeCkycDataJob, abflRegulatoryDataJob, loanAccountCreationLAMFJob, storeExperianConsentsJob, updateLrLseStatusJob, syncAllActiveLeadsCollectionsJob, updateLoanOffersJob, initiateAnalysisJob, updateAbflLoanStatus, backfillRejectedBreDataJob, epfoDataFetchJob, fedLentraCugTestingJob, retryDedupeJob, loanFunnelTrackingJob, updateFederalLoanRequestVendorIdJob, loansOnboardingJob, getSoaStatementJob, updateLoanRequestSubStatusNonProd, loecBackfilling, refreshUserLeadJob, lenderDataJob)
	return registry
}

// wire.go:

func newGormTxnExecutorProvider(dbConn *gorm.DB) storagev2.TxnExecutor {
	return storagev2.NewGormTxnExecutor(dbConn)
}

func notificationConfigProvider(conf *config.Config) *common.Notification {
	return conf.Notification
}

func envProvider(conf *config.Config) string {
	return conf.Application.Environment
}

func nilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func preApprovedLoanS3ClientProvider(awsConf aws.Config, conf *config.Config) types3.PreApprovedLoanS3Client {
	return s3.NewClient(awsConf, conf.Aws.S3.PreapprovedloanBucketName)
}

func newMultiDbDoOnce(dbResourceProvider *storagev2.DBResourceProvider[*gorm.DB]) once.MultiDbDoOnce {
	return once.NewMultiDbDoOnce(dbResourceProvider)
}

var SecureHttpClientNilSignCtxWireSet = wire.NewSet(SecureHttpClientProviderNew, NilSigningContextProvider)

func NilSigningContextProvider() *dsig.SigningContext {
	return nil
}

func getHttpClient(gconf *genconf3.Config, env string, conf *config.Config, insecure ...bool) *http.Client {
	insecureSkipVerifyTLS := false
	if len(insecure) == 1 && insecure[0] {
		insecureSkipVerifyTLS = true
	}
	if cfg.IsSimulatedEnv(env) || insecureSkipVerifyTLS {
		return http2.NewHttpClient(gconf.HttpClientConfig())
	}

	cert, key := cfg.GetCertAndKeysFromSecret("",
		conf.Secrets.Ids[config2.EpiFiFederalClientSslCert], conf.Secrets.Ids[config2.EpiFiFederalClientSslKey])

	return http2.NewHttpClientWithSSLCert(cert, key, gconf.HttpClientConfig())
}

func SecureHttpClientProviderNew(gconf *genconf3.Config, env string, conf *config.Config) *http.Client {
	return getHttpClient(gconf, env, conf, false)
}
