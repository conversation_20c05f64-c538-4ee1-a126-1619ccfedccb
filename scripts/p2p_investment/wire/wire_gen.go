// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/bankcust"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/p2pinvestment"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/investments/p2p"
	"github.com/epifi/gamma/scripts/p2p_investment/config"
	"github.com/epifi/gamma/scripts/p2p_investment/job"
)

// Injectors from wire.go:

func InitialiseJobRegistry(epifiDb types.EpifiCRDB, userClient user.UsersClient, actorClient actor.ActorClient, bankCustClient bankcust.BankCustomerServiceClient, commsClient comms.CommsClient, conf *config.Config, awsSession *session.Session, p2pClient p2pinvestment.P2PInvestmentClient, p2pVgClient p2p.P2PClient) *job.Registry {
	registry := job.NewRegistry()
	return registry
}
