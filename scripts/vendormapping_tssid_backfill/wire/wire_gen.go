// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/gamma/scripts/vendormapping_tssid_backfill/processor"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeBackFillProcessor(db *gorm.DB) *processor.BackFillProcessor {
	backFillProcessor := processor.NewBackFillProcessor(db)
	return backFillProcessor
}
