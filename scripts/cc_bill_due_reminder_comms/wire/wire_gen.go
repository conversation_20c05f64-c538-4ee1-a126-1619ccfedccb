// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/counter/once/v2"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/gamma/api/actor"
	"github.com/epifi/gamma/api/comms"
	"github.com/epifi/gamma/api/deposit"
	"github.com/epifi/gamma/api/firefly"
	"github.com/epifi/gamma/api/firefly/accounting"
	"github.com/epifi/gamma/api/firefly/billing"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/vendorgateway/lending/creditcard"
	"github.com/epifi/gamma/firefly/accounting/dao/impl"
	comms2 "github.com/epifi/gamma/firefly/comms"
	"github.com/epifi/gamma/firefly/dao"
	impl2 "github.com/epifi/gamma/firefly/dao/impl"
	"github.com/epifi/gamma/firefly/helper"
	types2 "github.com/epifi/gamma/firefly/wire/types"
	"github.com/epifi/gamma/scripts/cc_bill_due_reminder_comms/config"
	"github.com/epifi/gamma/scripts/cc_bill_due_reminder_comms/job"
	types3 "github.com/epifi/gamma/scripts/cc_bill_due_reminder_comms/wire/types"
	"github.com/rudderlabs/analytics-go"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitialiseJob(db *gorm.DB, conf *config.Config, actorClient actor.ActorClient, depositClient deposit.DepositClient, fireflyClient firefly.FireflyClient, accountingClient accounting.AccountingClient, commsClient comms.CommsClient, userClient user.UsersClient, ccVgClient creditcard.CreditCardClient, doOnceManager once.DoOnce, epifiDb types.EpifiCRDB, ccDb types.CreditCardPGDB, billingClient billing.BillingClient, fireflyRedisStore types2.FireflyRedisStore, rudderClient analytics.Client) (*job.CcBillDueCommsJob, error) {
	clock := idgen.NewClock()
	domainIdGenerator := idgen.NewDomainIdGenerator(clock)
	creditAccountDaoCRDB := impl.NewCreditAccountDaoCRDB(db, domainIdGenerator)
	ccCacheStorage := types3.CcCacheStorageProvider(conf, fireflyRedisStore)
	cardRequestCacheConfig := types3.CardRequestCacheConfigProvider(conf)
	cardRequestDao := impl2.NewCrdbCardRequest(db, domainIdGenerator)
	rudderStackBroker := events.NewRudderStackBroker(rudderClient)
	atmTxnSuccessRule := comms2.NewAtmTxnSuccessRule()
	atmTxnFailureRule := comms2.NewAtmTxnFailureRule()
	crossBorderTxnSuccessRule := comms2.NewCrossBorderTxnSuccessRule()
	txnSuccessRule := comms2.NewTxnSuccessRule()
	commsDataHelper := helper.NewCommsDataHelper(fireflyClient, ccVgClient, actorClient, userClient, accountingClient, billingClient, depositClient)
	txnFailureRule := comms2.NewTxnFailureRule(commsDataHelper)
	txnReversalSuccessRule := comms2.NewTxnReversalSuccessRule()
	onlineSettingsEnabledRule := comms2.NewOnlineSettingsEnabledRule()
	onlineSettingsDisabledRule := comms2.NewOnlineSettingsDisabledRule()
	posSettingsEnabledRule := comms2.NewPosSettingsEnabledRule()
	posSettingsDisabledRule := comms2.NewPosSettingsDisabledRule()
	internationalSettingsEnabledRule := comms2.NewInternationalSettingsEnabledRule()
	internationalSettingsDisabledRule := comms2.NewInternationalSettingsDisabledRule()
	contactlessSettingsEnabledRule := comms2.NewContactlessSettingsEnabledRule()
	contactlessSettingsDisabledRule := comms2.NewContactlessSettingsDisabledRule()
	cardUnfreezeSuccessRule := comms2.NewCardUnfreezeSuccessRule(commsDataHelper)
	creditCardPosPurchaseLimitChangedRule := comms2.NewCreditCardPosPurchaseLimitChangedRule(commsDataHelper)
	creditCardOnlinePurchaseLimitChangedRule := comms2.NewCreditCardOnlinePurchaseLimitChangedRule(commsDataHelper)
	cardFreezeSuccessRule := comms2.NewCardFreezeSuccessRule(commsDataHelper)
	creditCardCardUsageChangeFailureRule := comms2.NewCreditCardCardUsageChangeFailureRule(commsDataHelper)
	creditCardContactlessPurchaseLimitChangedRule := comms2.NewCreditCardContactlessPurchaseLimitChangedRule(commsDataHelper)
	cardFreezeFailureRule := comms2.NewCardFreezeFailureRule(commsDataHelper)
	creditCardLimitChangeFailureRule := comms2.NewCreditCardLimitChangeFailureRule(commsDataHelper)
	creditCardPinChangeSuccessRule := comms2.NewCreditCardPinChangeSuccessRule(commsDataHelper)
	cardUnFreezeFailureRule := comms2.NewCardUnFreezeFailureRule(commsDataHelper)
	cardActivationFailureRule := comms2.NewCardActivationFailureRule()
	cardActivationSuccessRule := comms2.NewCardActivationSuccessRule()
	cardReissueSuccessRule := comms2.NewCardReissueSuccessRule()
	issuePhysicalCardSuccessRule := comms2.NewIssuePhysicalCardSuccessRule()
	txnFailurePinRetriesExceededRule := comms2.NewTxnFailurePinRetriesExceededRule(commsDataHelper)
	paymentReminderWithInterestRule := comms2.NewPaymentReminderWithInterestRule(commsDataHelper)
	paymentReminderWithoutInterestRule := comms2.NewPaymentReminderWithoutInterestRule(commsDataHelper)
	paymentReminderBeforeDueDateRule := comms2.NewPaymentReminderBeforeDueDateRule(commsDataHelper)
	statementGeneratedRule := comms2.NewStatementGeneratedRule(commsDataHelper)
	cashWithdrawalFeeRule := comms2.NewCashWithdrawalFeeRule(commsDataHelper)
	successfulRepaymentRule := comms2.NewSuccessfulRepaymentRule(commsDataHelper)
	creditUtilisationReachingThresholdRule := comms2.NewCreditUtilisationReachingThresholdRule(commsDataHelper)
	unpaidDuesFeesRule := comms2.NewUnpaidDuesFeesRule(commsDataHelper)
	joiningFeeRule := comms2.NewJoiningFeeRule(commsDataHelper)
	genericCreditRule := comms2.NewGenericCreditRule(commsDataHelper)
	cardIssueSuccessWithLimitRule := comms2.NewCardIssueSuccessWithLimitRule(commsDataHelper)
	creditCardDispatchedRule := comms2.NewCreditCardDispatchedRule(commsDataHelper)
	welcomeOfferClaimRule := comms2.NewWelcomeOfferClaimRule(commsDataHelper)
	cardNotActivatedRule := comms2.NewCardNotActivatedRule(commsDataHelper)
	creditCardClosedRule := comms2.NewCreditCardClosedRule()
	securedCardFdCreationSuccessRule := comms2.NewSecuredCardFdCreationSuccessRule(commsDataHelper)
	securedCardFdLienMarkingSuccessSuccessRule := comms2.NewSecuredCardFdLienMarkingSuccessSuccessRule(commsDataHelper)
	ccProfileValidationSuccessRule := comms2.NewCCProfileValidationSuccessRule()
	ccProfileValidationFailureRule := comms2.NewCCProfileValidationFailureRule()
	creditCardPaymentReminderWhatsAppOnBillGenDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnBillGenDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppBeforeDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppBeforeDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppOnDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppOnDueDayRule(commsDataHelper)
	creditCardPaymentReminderWhatsAppAfterDueDayRule := comms2.NewCreditCardPaymentReminderWhatsAppAfterDueDayRule(commsDataHelper)
	creditCardDeliveredRule := comms2.NewCreditCardDeliveredRule(commsDataHelper)
	creditCardReturnedRule := comms2.NewCreditCardReturnedRule(commsDataHelper)
	creditCardOutForDeliveryRule := comms2.NewCreditCardOutForDeliveryRule(commsDataHelper)
	emiCreatedRule := comms2.NewEmiCreatedRule(commsDataHelper)
	emiClosedRule := comms2.NewEmiClosedRule()
	emiPreClosedRule := comms2.NewEmiPreClosedRule()
	emiCancelledRule := comms2.NewEmiCancelledRule()
	webEligibilityDropOffSmsRule := comms2.NewWebEligibilityDropOffSmsRule(commsDataHelper)
	webEligibilityDropOffEmailRule := comms2.NewWebEligibilityDropOffEmailRule(commsDataHelper)
	webEligibilityDropOffWhatsappRule := comms2.NewWebEligibilityDropOffWhatsappRule(commsDataHelper)
	creditCardWelcomeRule := comms2.NewCreditCardWelcomeRule()
	magnifiWelcomeRule := comms2.NewMagnifiWelcomeRule()
	frmDeclineTxnRule := comms2.NewFrmDeclineTxnRule()
	ccDeactivateInXDaysRule := comms2.NewCcDeactivateInXDaysRule()
	asyncProcessor := comms2.NewAsyncProcessor(commsClient, actorClient, doOnceManager, atmTxnSuccessRule, atmTxnFailureRule, crossBorderTxnSuccessRule, txnSuccessRule, txnFailureRule, txnReversalSuccessRule, onlineSettingsEnabledRule, onlineSettingsDisabledRule, posSettingsEnabledRule, posSettingsDisabledRule, internationalSettingsEnabledRule, internationalSettingsDisabledRule, contactlessSettingsEnabledRule, contactlessSettingsDisabledRule, cardUnfreezeSuccessRule, creditCardPosPurchaseLimitChangedRule, creditCardOnlinePurchaseLimitChangedRule, cardFreezeSuccessRule, creditCardCardUsageChangeFailureRule, creditCardContactlessPurchaseLimitChangedRule, cardFreezeFailureRule, creditCardLimitChangeFailureRule, creditCardPinChangeSuccessRule, cardUnFreezeFailureRule, cardActivationFailureRule, cardActivationSuccessRule, cardReissueSuccessRule, issuePhysicalCardSuccessRule, txnFailurePinRetriesExceededRule, paymentReminderWithInterestRule, paymentReminderWithoutInterestRule, paymentReminderBeforeDueDateRule, statementGeneratedRule, cashWithdrawalFeeRule, successfulRepaymentRule, creditUtilisationReachingThresholdRule, unpaidDuesFeesRule, joiningFeeRule, genericCreditRule, cardIssueSuccessWithLimitRule, creditCardDispatchedRule, welcomeOfferClaimRule, cardNotActivatedRule, creditCardClosedRule, securedCardFdCreationSuccessRule, securedCardFdLienMarkingSuccessSuccessRule, ccProfileValidationSuccessRule, ccProfileValidationFailureRule, creditCardPaymentReminderWhatsAppOnBillGenDayRule, creditCardPaymentReminderWhatsAppBeforeDueDayRule, creditCardPaymentReminderWhatsAppOnDueDayRule, creditCardPaymentReminderWhatsAppAfterDueDayRule, creditCardDeliveredRule, creditCardReturnedRule, creditCardOutForDeliveryRule, emiCreatedRule, emiClosedRule, emiPreClosedRule, emiCancelledRule, webEligibilityDropOffSmsRule, webEligibilityDropOffEmailRule, webEligibilityDropOffWhatsappRule, creditCardWelcomeRule, magnifiWelcomeRule, frmDeclineTxnRule, ccDeactivateInXDaysRule)
	cardRequestDaoWithInstrumentation := dao.NewCardRequestDaoWithInstrumentation(ccCacheStorage, cardRequestCacheConfig, cardRequestDao, rudderStackBroker, asyncProcessor)
	daoCardRequestDao := dao.ProvideCardRequestDao(cardRequestDaoWithInstrumentation)
	creditCardDao := impl2.NewCrdbCreditCard(db, domainIdGenerator)
	cardRequestStageDao := impl2.NewCrdbCardRequestStage(db, domainIdGenerator)
	ccBillDueCommsJob := job.NewCcBillDueCommsJob(creditAccountDaoCRDB, daoCardRequestDao, creditCardDao, cardRequestStageDao, ccVgClient, asyncProcessor, billingClient, userClient, commsClient)
	return ccBillDueCommsJob, nil
}
