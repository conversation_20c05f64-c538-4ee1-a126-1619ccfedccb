// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/gamma/api/order/payment"
	"github.com/epifi/gamma/api/vendorgateway/cx/freshdesk"
	"github.com/epifi/gamma/scripts/cx_issue_recategorization_backfill/config"
	"github.com/epifi/gamma/scripts/cx_issue_recategorization_backfill/helper"
)

// Injectors from wire.go:

func InitializeBackFillProcessor(conf *config.Config, fdClient freshdesk.FreshdeskClient, payClient payment.PaymentClient, oldToNewMapping map[helper.IssueCategory]*helper.IssueCategory, opts ...helper.BackFillProcessorImplOption) *helper.BackFillProcessorImpl {
	txnCategoriesBackFillHelper := helper.NewTxnCategoriesBackFillHelper(payClient, oldToNewMapping)
	backFillProcessorImpl := helper.NewBackFillProcessorImpl(conf, fdClient, txnCategoriesBackFillHelper, oldToNewMapping, opts...)
	return backFillProcessorImpl
}

func InitializeCsvFileParser() *helper.CsvParser {
	csvParser := helper.NewCsvParser()
	return csvParser
}
