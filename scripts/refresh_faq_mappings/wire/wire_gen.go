// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package wire

import (
	"github.com/epifi/be-common/pkg/cmd/types"
	"github.com/epifi/be-common/pkg/storage/v2"
	"github.com/epifi/gamma/api/vendorgateway/cx/solutions"
	"github.com/epifi/gamma/inapphelp/entity_mapping/dao"
	dao2 "github.com/epifi/gamma/inapphelp/faq/serving/dao"
	"github.com/epifi/gamma/scripts/refresh_faq_mappings/processor"
	"gorm.io/gorm"
)

// Injectors from wire.go:

func InitializeRefreshFaqMappingsProcessor(db *gorm.DB, txnExecutor storagev2.TxnExecutor, solutionsClient solutions.SolutionsClient) *processor.RefreshFaqMappingsProcessor {
	inapphelpPGDB := pgdbProvider(db)
	inAppHelpEntityMappingDao := dao.NewInAppHelpEntityMappingDao(inapphelpPGDB)
	inAppHelpAppClientConfigMappingDao := dao.NewInAppHelpAppClientConfigMappingDao(inapphelpPGDB)
	fetchFAQData := dao2.NewFetchFAQData(inapphelpPGDB)
	refreshFaqMappingsProcessor := processor.NewRefreshFaqMappingsProcessor(inAppHelpEntityMappingDao, inAppHelpAppClientConfigMappingDao, txnExecutor, solutionsClient, fetchFAQData)
	return refreshFaqMappingsProcessor
}

// wire.go:

func pgdbProvider(db *gorm.DB) types.InapphelpPGDB {
	return types.InapphelpPGDB(db)
}
