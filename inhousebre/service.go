package inhousebre

import (
	"context"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"
	"strings"

	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/encoding/protojson"
	structPb "google.golang.org/protobuf/types/known/structpb"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	creditReport "github.com/epifi/gamma/api/creditreportv2"
	epfoPb "github.com/epifi/gamma/api/epfo"
	inhousebrePb "github.com/epifi/gamma/api/inhousebre"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	fennelVgPb "github.com/epifi/gamma/api/vendorgateway/fennel"
	"github.com/epifi/gamma/featurestore"
	fsModel "github.com/epifi/gamma/featurestore/model"
	"github.com/epifi/gamma/pkg/creditreport"
)

const (
	workflow                    = "InhouseBre"
	CreditReportFeatureV3Prefix = "CreditReportsLendingRawV3"
	CibilPhoneMatchFeatureKey   = "CibilReportMetadata.phone_number_match"
	deviceLendingRisk           = "DeviceLendingRisk"
)

type Service struct {
	inhousebrePb.UnimplementedBreServer
	featureStoreFactory featurestore.IFactory
	epfoClient          epfoPb.EpfoClient
	creditReportClient  creditReport.CreditReportManagerClient
	fennelVgClient      fennelVgPb.FennelFeatureStoreClient
	userClient          user.UsersClient
}

func NewService(
	featureStoreFactory featurestore.IFactory,
	epfoClient epfoPb.EpfoClient,
	creditReportClient creditReport.CreditReportManagerClient,
	fennelVgClient fennelVgPb.FennelFeatureStoreClient,
	userClient user.UsersClient,
) *Service {
	return &Service{
		featureStoreFactory: featureStoreFactory,
		epfoClient:          epfoClient,
		creditReportClient:  creditReportClient,
		fennelVgClient:      fennelVgClient,
		userClient:          userClient,
	}
}

// nolint:gocritic
func (s *Service) GetFeaturesData(ctx context.Context, req *inhousebrePb.GetFeaturesDataRequest) (*inhousebrePb.GetFeaturesDataResponse, error) {
	var (
		res             = &inhousebrePb.GetFeaturesDataResponse{}
		actorId         string
		creditReportRaw string
	)
	if len(req.GetRequestIdentifiersList()) > 0 && req.GetRequestIdentifiersList()[0].GetIdentifiers().GetActorId() != "" {
		actorId = req.GetRequestIdentifiersList()[0].GetIdentifiers().GetActorId()
	}
	ctx = epificontext.CtxWithActorId(ctx, actorId)
	logger.Info(ctx, "received request for GetFeaturesData", zap.String(logger.ACTOR_ID_V2, actorId))

	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if rpcErr := epifigrpc.RPCError(userResp, err); rpcErr != nil {
		logger.Error(ctx, "error while getting user from userClient",
			zap.String(logger.ACTOR_ID_V2, actorId),
			zap.Error(rpcErr))
		return nil, status.Errorf(codes.Internal, "error while getting user for actor %s from userClient: %s", actorId, rpcErr)
	}
	if userResp.GetUser().GetProfile().GetPhoneNumber() == nil {
		logger.Error(ctx, "phone number not found for actor",
			zap.String(logger.ACTOR_ID_V2, actorId))
		return nil, status.Errorf(codes.NotFound, "phone number not found for actorId: %s", actorId)
	}
	phoneNumber := strconv.FormatUint(userResp.GetUser().GetProfile().GetPhoneNumber().GetNationalNumber(), 10)

	identifierList, identifierTypeList, err := s.getIdentifiersList(ctx, req.GetRequestIdentifiersList(), phoneNumber, req.GetFeatureNameList())
	if err != nil {
		logger.Error(ctx, "error in getting identifiers list", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		code := codes.Internal
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			code = codes.InvalidArgument
		}
		return nil, status.Errorf(code, "error in getting identifiers list: %v", err)
	}

	isCreditReportV3Enabled := false
	for _, featureName := range req.GetFeatureNameList() {
		if strings.Contains(featureName, CreditReportFeatureV3Prefix) {
			isCreditReportV3Enabled = true
			break
		}
	}

	if isCreditReportV3Enabled {
		identifierTypeList = append(identifierTypeList, fsModel.CREDIT_REPORT_DATA_RAW)
		for ind, identifier := range identifierList {
			if identifier.ActorId != "" {
				crResp, crErr := s.creditReportClient.GetCreditReport(ctx, &creditReport.GetCreditReportRequest{
					ActorId: actorId,
				})
				if grpcErr := epifigrpc.RPCError(crResp, crErr); grpcErr != nil && !crResp.GetStatus().IsRecordNotFound() {
					logger.Error(ctx, "error in fetching credit report", zap.Error(grpcErr))
					return nil, status.Errorf(codes.Internal, fmt.Sprintf("error in getting credit report: %v", grpcErr))
				}
				logger.Info(ctx, "fetched credit report data availability status", zap.String(logger.ACTOR_ID_V2, actorId), zap.String("DATA_AVAILABILITY_STATUS", crResp.GetDataAvailabilityStatus().String()))

				switch crResp.GetDataAvailabilityStatus() {
				case creditReport.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_FOUND:
					logger.Info(ctx, "credit report found for the user", zap.String(logger.ACTOR_ID_V2, actorId))
					creditReportRaw = creditreport.StringifyRawReport(ctx, crResp.GetCreditReport().GetCreditReportDataRaw())
					identifierList[ind].CreditReportDataRaw = creditReportRaw
					res.ExperianReportDataAvailabilityStatus = crResp.GetDataAvailabilityStatus().String()
				case creditReport.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_NOT_FOUND, creditReport.DataAvailabilityStatus_DATA_AVAILABILITY_STATUS_FOUND_WITHOUT_HISTORY:
					logger.Info(ctx, "credit report is not present for this user, ntc case", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.PAYLOAD, crResp))
					res.ExperianReportDataAvailabilityStatus = crResp.GetDataAvailabilityStatus().String()
					return res, nil
				default:
					logger.Info(ctx, "credit report is not present for this user in GetFeaturesData", zap.String(logger.ACTOR_ID_V2, actorId), zap.Any(logger.PAYLOAD, crResp))
					return nil, status.Errorf(codes.NotFound, fmt.Sprintf("credit report not found, err: %v", err))
				}
			}
		}
	}

	featureStoreClient, err := s.featureStoreFactory.GetFeatureStoreClient(commonvgpb.Vendor_FENNEL_FEATURE_STORE)
	if err != nil {
		logger.Error(ctx, "failed to get feature store client", zap.Error(err))
		return nil, status.Errorf(codes.Internal, fmt.Sprintf("failed to get feature store client, err: %v", err))
	}
	featuresDataRes, err := featureStoreClient.ExtractFeature(ctx, &fsModel.ExtractFeatureRequest{
		FeatureNameList:    req.GetFeatureNameList(),
		IdentifierList:     identifierList,
		IdentifierTypeList: identifierTypeList,
		Workflow:           workflow,
	})
	if err != nil {
		logger.Error(ctx, "error in extracting features data", zap.Error(err))
		return nil, status.Errorf(codes.Internal, fmt.Sprintf("error in extracting features data, err: %v", err))
	}
	for _, featuresData := range featuresDataRes.FeaturesSetList {
		res.FeaturesResponseDataList = append(res.FeaturesResponseDataList, &inhousebrePb.FeaturesResponseData{
			Identifiers:     getResponseIdentifier(featuresData.Identifier),
			FeatureValueMap: getResponseFeatureValueMap(featuresData.FeatureList),
		})
	}

	// log the feature names that are coming as null
	for _, data := range res.FeaturesResponseDataList {
		var nullFeatures []string
		for featureKey, featureVal := range data.GetFeatureValueMap() {
			switch featureVal.GetKind().(type) {
			case *structPb.Value_NullValue:
				nullFeatures = append(nullFeatures, featureKey)
			}
		}
		if len(nullFeatures) == len(req.GetFeatureNameList()) {
			logger.Error(ctx, fmt.Sprintf("got null feature values from feature store : %v", nullFeatures), zap.String(logger.ACTOR_ID_V2, actorId))
		}
	}
	return res, nil
}

func (s *Service) getIdentifiersList(ctx context.Context, reqIdentifierList []*inhousebrePb.RequestIdentifiers, phoneNumber string, featureNames []string) ([]fsModel.Identifier, []fsModel.IdentifierType, error) {
	var (
		identifierList     []fsModel.Identifier
		identifierTypeList []fsModel.IdentifierType
		userDeviceAppsJson string
		actorId            string
	)
	if len(reqIdentifierList) == 0 {
		return nil, nil, errors.Wrap(epifierrors.ErrInvalidArgument, "empty request sent for fetching data")
	}
	actorId = reqIdentifierList[0].GetIdentifiers().GetActorId()

	// Check if we need to fetch device apps data
	if len(featureNames) > 0 && strings.Contains(featureNames[0], deviceLendingRisk) {
		devicePropsResponse, devicePropsError := s.userClient.GetUserDeviceProperties(ctx, &user.GetUserDevicePropertiesRequest{
			ActorId: actorId,
			PropertyTypes: []typesPb.DeviceProperty{
				typesPb.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO,
			},
		})
		if devicePropsError = epifigrpc.RPCError(devicePropsResponse, devicePropsError); devicePropsError != nil && !devicePropsResponse.GetStatus().IsRecordNotFound() {
			return nil, nil, errors.Wrap(devicePropsError, "failed to fetch device properties from user service")
		}
		userDeviceAppsJson = extractPropertyValues(ctx, devicePropsResponse.GetUserDevicePropertyList())
		identifierTypeList = append(identifierTypeList, fsModel.USER_DEVICE_APPS_JSON)
	}

	if actorId != "" {
		identifierTypeList = append(identifierTypeList, fsModel.ACTOR_ID)
	}
	if reqIdentifierList[0].GetIdentifiers().GetAccountId() != "" {
		identifierTypeList = append(identifierTypeList, fsModel.ACCOUNT_ID)
	}
	if reqIdentifierList[0].GetIdentifiers().GetModelName() != "" {
		identifierTypeList = append(identifierTypeList, fsModel.MODEL_NAME)
	}
	if phoneNumber != "" {
		identifierTypeList = append(identifierTypeList, fsModel.PHONE_NUMBER)
	}

	for _, reqIdentifier := range reqIdentifierList {
		identifierList = append(identifierList, fsModel.Identifier{
			ActorId:            reqIdentifier.GetIdentifiers().GetActorId(),
			AccountId:          reqIdentifier.GetIdentifiers().GetAccountId(),
			ModelName:          reqIdentifier.GetIdentifiers().GetModelName(),
			PhoneNumber:        phoneNumber,
			UserDeviceAppsJson: userDeviceAppsJson,
		})
	}
	return identifierList, identifierTypeList, nil
}

func getResponseIdentifier(identifier fsModel.Identifier) *inhousebrePb.Identifiers {
	return &inhousebrePb.Identifiers{
		ActorId:   identifier.ActorId,
		AccountId: identifier.AccountId,
		ModelName: identifier.ModelName,
	}
}

func getResponseFeatureValueMap(featureList []fsModel.Feature) map[string]*structPb.Value {
	featureValueMap := make(map[string]*structPb.Value)
	for _, feature := range featureList {
		featureValueMap[feature.Name] = feature.Value
	}
	return featureValueMap
}

func (s *Service) sendResponse(w http.ResponseWriter, statusCode int, resp string) {
	w.WriteHeader(statusCode)
	_, _ = w.Write([]byte(resp))
}

func (s *Service) GetEpfoDataV1(w http.ResponseWriter, request *http.Request) {
	ctx := context.Background()
	reqData, err := ioutil.ReadAll(request.Body)
	if err != nil {
		logger.ErrorNoCtx("failed to read request body", zap.Error(err))
		s.sendResponse(w, http.StatusInternalServerError, "failed to read request body")
		return
	}
	req := &inhousebrePb.GetEpfoDataRequest{}
	reqPbErr := protojson.Unmarshal(reqData, req)
	if reqPbErr != nil {
		logger.ErrorNoCtx("error parsing actor id in GetEpfoDataRequest", zap.Error(reqPbErr))
		s.sendResponse(w, http.StatusInternalServerError, "error parsing actor id in GetEpfoDataRequest")
		return
	}

	epfoRes, epfoErr := s.epfoClient.GetEpfoData(ctx, &epfoPb.GetEpfoDataRequest{
		ActorId: req.GetActorId(),
		Vendor:  epfoPb.Vendor_VENDOR_DIGITAP,
		Client:  epfoPb.Client_CLIENT_LOANS,
	})
	if epfoRes.GetStatus().IsRecordNotFound() {
		logger.ErrorNoCtx("no epfo record found for user")
		// in this case, response is empty json "{}"
		s.sendResponse(w, http.StatusOK, epfoRes.GetRawEpfoData())
		return
	}
	if te := epifigrpc.RPCError(epfoRes, epfoErr); te != nil {
		logger.Error(ctx, "failed to get response from GetEpfoData at EPFO service via digitap", zap.Error(te))
		epfoRes, epfoErr = s.epfoClient.GetEpfoData(ctx, &epfoPb.GetEpfoDataRequest{
			ActorId: req.GetActorId(),
			Vendor:  epfoPb.Vendor_VENDOR_KARZA,
			Client:  epfoPb.Client_CLIENT_LOANS,
		})
		if epfoRes.GetStatus().IsRecordNotFound() {
			logger.ErrorNoCtx("no epfo record found for user")
			// in this case, response is empty json "{}"
			s.sendResponse(w, http.StatusOK, epfoRes.GetRawEpfoData())
			return
		}
		if te = epifigrpc.RPCError(epfoRes, epfoErr); te != nil {
			logger.Error(ctx, "failed to get response from GetEpfoData at EPFO service via karza", zap.Error(te))
			s.sendResponse(w, http.StatusInternalServerError, "failed to get response from GetEpfoData at EPFO service")
		}
	}
	if te := epifigrpc.RPCError(epfoRes, epfoErr); te != nil {
		logger.ErrorNoCtx("failed to get response from GetEpfoData at EPFO service", zap.Error(te))
		s.sendResponse(w, http.StatusInternalServerError, "failed to get response from GetEpfoData at EPFO service")
		return
	}
	s.sendResponse(w, http.StatusOK, epfoRes.GetRawEpfoData())
}

func (s *Service) GetEpfoData(ctx context.Context, req *inhousebrePb.GetEpfoDataRequest) (*inhousebrePb.GetEpfoDataResponse, error) {
	res := &inhousebrePb.GetEpfoDataResponse{}

	epfoRes, epfoErr := s.epfoClient.GetEpfoData(ctx, &epfoPb.GetEpfoDataRequest{
		ActorId: req.GetActorId(),
		Vendor:  epfoPb.Vendor_VENDOR_DIGITAP,
		Client:  epfoPb.Client_CLIENT_LOANS,
	})
	if te := epifigrpc.RPCError(epfoRes, epfoErr); te != nil {
		logger.Error(ctx, "failed to get response from GetEpfoData at EPFO service via digitap", zap.Error(te))
		epfoRes, epfoErr = s.epfoClient.GetEpfoData(ctx, &epfoPb.GetEpfoDataRequest{
			ActorId: req.GetActorId(),
			Vendor:  epfoPb.Vendor_VENDOR_KARZA,
			Client:  epfoPb.Client_CLIENT_LOANS,
		})
		if te = epifigrpc.RPCError(epfoRes, epfoErr); te != nil {
			logger.Error(ctx, "failed to get response from GetEpfoData at EPFO service via karza", zap.Error(te))
			return nil, status.Errorf(codes.Internal, fmt.Sprintf("failed to get response from GetEpfoData at EPFO service via karza, err: %v", te))
		}
	}

	res.RawEpfoData = epfoRes.GetRawEpfoData()
	return res, nil
}

// GetCibilReportFeatures retrieves CIBIL report features for a given actor.
// It fetches features from the credit report service and performs additional validation
// on phone number match feature if present.
// Returns features map and CIBIL report availability status.
// If phone number match validation fails, it logs the error but does not fail the request.
func (s *Service) GetCibilReportFeatures(ctx context.Context, req *inhousebrePb.GetCibilReportFeaturesRequest) (*inhousebrePb.GetCibilReportFeaturesResponse, error) {
	ctx = epificontext.CtxWithActorId(ctx, req.GetActorId())
	logger.Info(ctx, "received request for GetCibilReportFeatures for inhousebre", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

	cibilFeaturesRes, err := s.creditReportClient.GetCibilReportFeatures(ctx, &creditReport.GetCibilReportFeaturesRequest{
		ActorId:       req.GetActorId(),
		FeaturesNames: req.GetFeaturesNames(),
	})
	if te := epifigrpc.RPCError(cibilFeaturesRes, err); te != nil {
		logger.Error(ctx, "failed to get cibil features from credit report v2 service", zap.Error(te), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, status.Errorf(codes.Internal, fmt.Sprintf("failed to get cibil features from credit report v2 service, msg: %v, %v", cibilFeaturesRes.GetStatus().GetDebugMessage(), cibilFeaturesRes.GetStatus().GetShortMessage()))
	}

	// All other features expect for phone number match are calculated from the raw report sent in request
	// For this feature, feature store fetches user's number for synced actor and the one in raw credit report.
	// If feature store is not able to fetch user's phone number, the feature value will be null, in which case, the
	// backend system throws an alert
	if phoneNumberMatch, ok := cibilFeaturesRes.GetFeatureValueMap()[CibilPhoneMatchFeatureKey]; ok {
		switch phoneNumberMatch.GetKind().(type) {
		case *structPb.Value_NullValue:
			logger.Error(ctx, "phone number match feature not found in feature store response", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		case *structPb.Value_ListValue:
			if _, isNullPresent := phoneNumberMatch.GetListValue().GetValues()[0].GetKind().(*structPb.Value_NullValue); isNullPresent {
				logger.Error(ctx, "phone number match feature not found in feature store response", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
			}
		default:
			// do nothing
		}
	}

	return &inhousebrePb.GetCibilReportFeaturesResponse{
		FeatureValueMap:                   cibilFeaturesRes.GetFeatureValueMap(),
		CibilReportDataAvailabilityStatus: cibilFeaturesRes.GetCreditReportDataAvailabilityStatus().String(),
	}, nil
}

func (s *Service) GetPdScore(ctx context.Context, req *inhousebrePb.GetPdScoreRequest) (*inhousebrePb.GetPdScoreResponse, error) {
	bureau, err := getBureauEnum(req.GetBureau())
	if err != nil {
		logger.Error(ctx, "error getting bureau", zap.Error(err))
		return nil, status.Errorf(codes.InvalidArgument, fmt.Sprintf("invalid bureau: %v", req.GetBureau()))
	}

	crResp, crErr := s.creditReportClient.GetCreditReports(ctx, &creditReport.GetCreditReportsRequest{
		ActorId: req.GetActorId(),
		Vendor:  []commonvgpb.Vendor{bureau},
	})
	if grpcErr := epifigrpc.RPCError(crResp, crErr); grpcErr != nil {
		logger.Error(ctx, "error in fetching credit report", zap.Error(grpcErr))
		return nil, status.Errorf(codes.Internal, fmt.Sprintf("error in getting credit report: %v", grpcErr))
	}

	if len(crResp.GetCreditReports()) == 0 {
		logger.Error(ctx, "credit report is not present for this user", zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return nil, status.Errorf(codes.NotFound, "credit report not found")
	}

	resp, err := s.fennelVgClient.GetPdScore(ctx, &fennelVgPb.GetPdScoreRequest{
		Header:          &commonvgpb.RequestHeader{Vendor: bureau},
		ActorId:         req.GetActorId(),
		RawCreditReport: crResp.GetCreditReports()[0].GetCreditReportRaw().GetRawReport(),
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		return nil, status.Errorf(codes.Internal, fmt.Sprint("error getting pd score from DS api, err: %w", err))
	}

	return &inhousebrePb.GetPdScoreResponse{
		PdScore:        resp.GetScore(),
		PdScoreVersion: resp.GetModelVersion(),
	}, nil
}

func getBureauEnum(bureau string) (commonvgpb.Vendor, error) {
	switch strings.ToLower(bureau) {
	case "cibil":
		return commonvgpb.Vendor_CIBIL, nil
	case "experian":
		return commonvgpb.Vendor_EXPERIAN, nil
	}
	return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unknown bureau: %v", bureau)
}

func extractPropertyValues(ctx context.Context, properties []*user.UserDeviceProperty) string {
	for _, prop := range properties {
		if prop.GetDeviceProperty() == typesPb.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO {
			appsInfo := prop.GetPropertyValue()
			if appsInfo != nil {
				// Marshal to JSON
				appsInfoBytes, err := protojson.Marshal(appsInfo)
				if err != nil {
					logger.Error(ctx, "failed to marshal apps info to JSON", zap.Error(err))
					return "{}"
				}
				return string(appsInfoBytes)
			}
		}
	}
	return "{}"
}
