package developer

import (
	"fmt"

	"github.com/epifi/gamma/api/billpay/developer"
	"github.com/epifi/gamma/billpay/developer/processor"
)

type DevFactory struct {
	rechargeOrdersProcessor      *processor.RechargeOrdersProcessor
	rechargeOrderStagesProcessor *processor.RechargeOrderStagesProcessor
}

func NewDevFactory(
	rechargeOrdersProcessor *processor.RechargeOrdersProcessor,
	rechargeOrderStagesProcessor *processor.RechargeOrderStagesProcessor,
) *DevFactory {
	return &DevFactory{
		rechargeOrdersProcessor:      rechargeOrdersProcessor,
		rechargeOrderStagesProcessor: rechargeOrderStagesProcessor,
	}
}

func (d *DevFactory) getParameterListProcessor(entity developer.BillpayEntity) (processor.ParameterFetcher, error) {
	switch entity {
	case developer.BillpayEntity_RECHARGE_ORDERS:
		return d.rechargeOrdersProcessor, nil
	case developer.BillpayEntity_RECHARGE_ORDER_STAGES:
		return d.rechargeOrderStagesProcessor, nil
	}
	return nil, fmt.Errorf("no valid implementation found for entity: %s", entity.String())
}

func (d *DevFactory) getDataProcessor(entity developer.BillpayEntity) (processor.DataFetcher, error) {
	switch entity {
	case developer.BillpayEntity_RECHARGE_ORDERS:
		return d.rechargeOrdersProcessor, nil
	case developer.BillpayEntity_RECHARGE_ORDER_STAGES:
		return d.rechargeOrderStagesProcessor, nil
	}
	return nil, fmt.Errorf("no valid implementation found for entity: %s", entity.String())
}
