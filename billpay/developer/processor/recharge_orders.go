package processor

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/proto/json"

	billpaypb "github.com/epifi/gamma/api/billpay"
	"github.com/epifi/gamma/api/billpay/developer"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/billpay/dao"
)

type RechargeOrdersProcessor struct {
	rechargeOrderDao dao.RechargeOrderDao
}

func NewRechargeOrdersProcessor(rechargeOrderDao dao.RechargeOrderDao) *RechargeOrdersProcessor {
	return &RechargeOrdersProcessor{
		rechargeOrderDao: rechargeOrderDao,
	}
}

func (d *RechargeOrdersProcessor) FetchParamList(ctx context.Context, entity developer.BillpayEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            RechargeOrderId,
			Label:           "Recharge Order ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ClientRequestId,
			Label:           "Client Request ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ActorId,
			Label:           "Actor ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            AccountType,
			Label:           "Account Type (used with Actor ID)",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getRechargeAccountTypeList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            AccountIdentifier,
			Label:           "Account Identifier (used with Actor ID)",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *RechargeOrdersProcessor) FetchData(ctx context.Context, entity developer.BillpayEntity, filters []*db_state.Filter) (string, error) {
	filterMap := make(map[string]string)
	for _, filter := range filters {
		filterMap[filter.GetParameterName()] = filter.GetStringValue()
	}

	var orders []*billpaypb.RechargeOrder
	var err error

	// Use specific DAO methods based on available filters
	if rechargeOrderId, ok := filterMap[RechargeOrderId]; ok && rechargeOrderId != "" {
		// Use GetById for specific recharge order
		order, err := d.rechargeOrderDao.GetById(ctx, nil, rechargeOrderId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "", epifierrors.ErrRecordNotFound
			}
			return "", fmt.Errorf("failed to fetch recharge order by ID: %w", err)
		}
		orders = []*billpaypb.RechargeOrder{order}
	} else if clientRequestId, ok := filterMap[ClientRequestId]; ok && clientRequestId != "" {
		// Use GetByClientRequestId for client request ID
		order, err := d.rechargeOrderDao.GetByClientRequestId(ctx, nil, clientRequestId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "", epifierrors.ErrRecordNotFound
			}
			return "", fmt.Errorf("failed to fetch recharge order by client request ID: %w", err)
		}
		orders = []*billpaypb.RechargeOrder{order}
	} else if actorId, ok := filterMap[ActorId]; ok && actorId != "" {
		// Check if account identifier is also provided
		if accountIdentifier, hasAccountId := filterMap[AccountIdentifier]; hasAccountId && accountIdentifier != "" {
			// Use GetByActorIdAndAccountDetails
			accountType := enums.RechargeAccountType_RECHARGE_ACCOUNT_TYPE_MOBILE // Default to mobile
			if accountTypeStr, hasAccountType := filterMap[AccountType]; hasAccountType && accountTypeStr != "" {
				accountType = enums.RechargeAccountType(enums.RechargeAccountType_value[accountTypeStr])
			}
			orders, err = d.rechargeOrderDao.GetByActorIdAndAccountDetails(ctx, actorId, accountType, accountIdentifier)
		} else {
			// Use GetByActorId with pagination
			orders, _, err = d.rechargeOrderDao.GetByActorId(ctx, nil, actorId, nil, pageSize, nil)
		}
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "", epifierrors.ErrRecordNotFound
			}
			return "", fmt.Errorf("failed to fetch recharge orders by actor ID: %w", err)
		}
	} else {
		return "", fmt.Errorf("at least one of recharge_order_id, client_request_id, or actor_id must be provided")
	}

	// Convert to JSON
	jsonBytes, err := json.Marshal(orders)
	if err != nil {
		return "", fmt.Errorf("failed to marshal recharge orders to JSON: %w", err)
	}

	return string(jsonBytes), nil
}

func getRechargeAccountTypeList() []string {
	var accountTypes []string
	for _, accountType := range enums.RechargeAccountType_value {
		if accountType == 0 {
			continue
		}
		accountTypes = append(accountTypes, enums.RechargeAccountType(accountType).String())
	}
	return accountTypes
}
