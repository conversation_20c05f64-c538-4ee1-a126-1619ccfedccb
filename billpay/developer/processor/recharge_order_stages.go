package processor

import (
	"context"
	"errors"
	"fmt"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/proto/json"

	billpaypb "github.com/epifi/gamma/api/billpay"
	"github.com/epifi/gamma/api/billpay/developer"
	"github.com/epifi/gamma/api/billpay/enums"
	"github.com/epifi/gamma/api/cx/developer/db_state"
	"github.com/epifi/gamma/billpay/dao"
)

type RechargeOrderStagesProcessor struct {
	rechargeOrderStageDao dao.RechargeOrderStageDao
}

func NewRechargeOrderStagesProcessor(rechargeOrderStageDao dao.RechargeOrderStageDao) *RechargeOrderStagesProcessor {
	return &RechargeOrderStagesProcessor{
		rechargeOrderStageDao: rechargeOrderStageDao,
	}
}

func (d *RechargeOrderStagesProcessor) FetchParamList(ctx context.Context, entity developer.BillpayEntity) ([]*db_state.ParameterMeta, error) {
	paramList := []*db_state.ParameterMeta{
		{
			Name:            RechargeOrderStageId,
			Label:           "Recharge Order Stage ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            RechargeOrderId,
			Label:           "Recharge Order ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            ClientRequestId,
			Label:           "Client Request ID",
			Type:            db_state.ParameterDataType_STRING,
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
		{
			Name:            Stage,
			Label:           "Stage",
			Type:            db_state.ParameterDataType_DROPDOWN,
			Options:         getRechargeStageList(),
			ParameterOption: db_state.ParameterOption_OPTIONAL,
		},
	}
	return paramList, nil
}

func (d *RechargeOrderStagesProcessor) FetchData(ctx context.Context, entity developer.BillpayEntity, filters []*db_state.Filter) (string, error) {
	filterMap := make(map[string]string)
	for _, filter := range filters {
		filterMap[filter.GetParameterName()] = filter.GetStringValue()
	}

	var stages []*billpaypb.RechargeOrderStage
	var err error

	// Use specific DAO methods based on available filters
	if stageId, ok := filterMap[RechargeOrderStageId]; ok && stageId != "" {
		// Use GetById for specific stage
		stage, err := d.rechargeOrderStageDao.GetById(ctx, nil, stageId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "", epifierrors.ErrRecordNotFound
			}
			return "", fmt.Errorf("failed to fetch recharge order stage by ID: %w", err)
		}
		stages = []*billpaypb.RechargeOrderStage{stage}
	} else if clientRequestId, ok := filterMap[ClientRequestId]; ok && clientRequestId != "" {
		// Use GetByClientRequestId for client request ID
		stage, err := d.rechargeOrderStageDao.GetByClientRequestId(ctx, nil, clientRequestId)
		if err != nil {
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return "", epifierrors.ErrRecordNotFound
			}
			return "", fmt.Errorf("failed to fetch recharge order stage by client request ID: %w", err)
		}
		stages = []*billpaypb.RechargeOrderStage{stage}
	} else if rechargeOrderId, ok := filterMap[RechargeOrderId]; ok && rechargeOrderId != "" {
		// Check if stage is also provided
		if stageStr, hasStage := filterMap[Stage]; hasStage && stageStr != "" {
			// Use GetByRechargeOrderIdAndStage
			stage := enums.RechargeStage(enums.RechargeStage_value[stageStr])
			singleStage, err := d.rechargeOrderStageDao.GetByRechargeOrderIdAndStage(ctx, nil, rechargeOrderId, stage)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					return "", epifierrors.ErrRecordNotFound
				}
				return "", fmt.Errorf("failed to fetch recharge order stage by order ID and stage: %w", err)
			}
			stages = []*billpaypb.RechargeOrderStage{singleStage}
		} else {
			// Use GetByRechargeOrderId to get all stages for the order
			stages, err = d.rechargeOrderStageDao.GetByRechargeOrderId(ctx, nil, rechargeOrderId)
			if err != nil {
				if errors.Is(err, epifierrors.ErrRecordNotFound) {
					return "", epifierrors.ErrRecordNotFound
				}
				return "", fmt.Errorf("failed to fetch recharge order stages by order ID: %w", err)
			}
		}
	} else {
		return "", fmt.Errorf("at least one of recharge_order_stage_id, client_request_id, or recharge_order_id must be provided")
	}

	// Convert to JSON
	jsonBytes, err := json.Marshal(stages)
	if err != nil {
		return "", fmt.Errorf("failed to marshal recharge order stages to JSON: %w", err)
	}

	return string(jsonBytes), nil
}

func getRechargeStageList() []string {
	var stages []string
	for _, stage := range enums.RechargeStage_value {
		if stage == 0 {
			continue
		}
		stages = append(stages, enums.RechargeStage(stage).String())
	}
	return stages
}
