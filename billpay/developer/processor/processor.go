package processor

import (
	"context"

	"github.com/epifi/gamma/api/billpay/developer"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type ParameterFetcher interface {
	FetchParamList(ctx context.Context, entity developer.BillpayEntity) ([]*cxDsPb.ParameterMeta, error)
}

type DataFetcher interface {
	FetchData(ctx context.Context, entity developer.BillpayEntity, filters []*cxDsPb.Filter) (string, error)
}
