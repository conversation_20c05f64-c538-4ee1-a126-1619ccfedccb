package developer

import (
	"context"
	"errors"
	"sort"

	"go.uber.org/zap"
	gormv2 "gorm.io/gorm"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/gamma/api/billpay/developer"
	cxDsPb "github.com/epifi/gamma/api/cx/developer/db_state"
)

type BillpayDevService struct {
	billpayDevFactory *DevFactory
}

func NewBillpayDevService(billpayDevFactory *DevFactory) *BillpayDevService {
	return &BillpayDevService{
		billpayDevFactory: billpayDevFactory,
	}
}

func (s *BillpayDevService) GetEntityList(_ context.Context, _ *cxDsPb.GetEntityListRequest) (*cxDsPb.GetEntityListResponse, error) {
	var entityList []string
	for _, entityEnumNum := range developer.BillpayEntity_value {
		if entityEnumNum == 0 {
			continue
		}
		entityList = append(entityList, developer.BillpayEntity(entityEnumNum).String())
	}
	sort.Strings(entityList)
	return &cxDsPb.GetEntityListResponse{
		Status:     rpc.StatusOk(),
		EntityList: entityList,
	}, nil
}

func (s *BillpayDevService) GetParameterList(ctx context.Context, req *cxDsPb.GetParameterListRequest) (*cxDsPb.GetParameterListResponse, error) {
	ent, ok := developer.BillpayEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetParameterListResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	paramFetcher, err := s.billpayDevFactory.getParameterListProcessor(developer.BillpayEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpc.StatusInternal()}, nil
	}
	paramList, err := paramFetcher.FetchParamList(ctx, developer.BillpayEntity(ent))
	if err != nil {
		logger.Error(ctx, "unable to fetch parameter list", zap.Error(err))
		return &cxDsPb.GetParameterListResponse{Status: rpc.StatusInternal()}, nil
	}
	return &cxDsPb.GetParameterListResponse{
		Status:        rpc.StatusOk(),
		ParameterList: paramList,
	}, nil
}

func (s *BillpayDevService) GetData(ctx context.Context, req *cxDsPb.GetDataRequest) (*cxDsPb.GetDataResponse, error) {
	ent, ok := developer.BillpayEntity_value[req.GetEntity()]
	if !ok {
		return &cxDsPb.GetDataResponse{
			Status: rpc.StatusInvalidArgumentWithDebugMsg("entity type passed is not available"),
		}, nil
	}
	dataFetcher, err := s.billpayDevFactory.getDataProcessor(developer.BillpayEntity(ent))
	if err != nil {
		logger.Error(ctx, "entity type passed implementation is not available", zap.Error(err))
		return &cxDsPb.GetDataResponse{Status: rpc.StatusInternal()}, nil
	}
	jsonResp, err := dataFetcher.FetchData(ctx, developer.BillpayEntity(ent), req.GetFilters())
	if err != nil {
		logger.Error(ctx, "unable to get data", zap.Error(err))
		if errors.Is(err, gormv2.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &cxDsPb.GetDataResponse{Status: rpc.StatusRecordNotFound()}, nil
		}
		return &cxDsPb.GetDataResponse{Status: rpc.StatusInternal()}, nil
	}
	return &cxDsPb.GetDataResponse{
		Status:       rpc.StatusOk(),
		JsonResponse: jsonResp,
	}, nil
}
