package tiering

import (
	"fmt"

	"github.com/epifi/be-common/pkg/money"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	"github.com/epifi/gamma/api/tiering/external"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

// GetTargetAMBFromTieringPitch extracts the target AMB value for a given tier from the tiering pitch response
func GetTargetAMBFromTieringPitch(tieringPitchResp *tieringPb.GetTieringPitchV2Response, regularTierParams *tieringPb.RegularTierConfigParams, trialDetailsResp *tieringPb.GetTrialDetailsResponse) (float64, error) {
	if tieringPitchResp == nil {
		return 0, fmt.Errorf("tiering pitch response is nil")
	}

	currentTier := tieringPitchResp.GetCurrentTier()

	switch currentTier {
	case external.Tier_TIER_FI_REGULAR:
		regularTierMinAmb := regularTierParams.GetMinBalanceForRegularTier()
		return money.ToDecimal(regularTierMinAmb).InexactFloat64(), nil
	case external.Tier_TIER_FI_BASIC, external.Tier_TIER_FI_SALARY_BASIC, external.Tier_TIER_FI_SALARY:
		return 0, nil
	default:
		// For other tiers, extract the criteria values from the movement details
		mmtDetails := tieringPitchResp.GetMovementDetailsList()
		for _, detail := range mmtDetails {
			if detail.GetTierName() != currentTier {
				continue
			}

			// Use the new pkg function to get all criteria min values
			criteriaMinValues, err := GetAllCriteriaMinValuesFromOptions(detail.GetOptions())
			if err != nil {
				continue // Try the next detail if this one has no criteria
			}

			// get target AMB from Trials criteria if trial is active and current tier is trial tier
			criteriaToGetTargetAmb := tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC
			if trialDetailsResp.GetTrialDetails().IsTrialPeriodActive() && currentTier == trialDetailsResp.GetTrialDetails().GetTier() {
				criteriaToGetTargetAmb = tieringEnumPb.CriteriaOptionType_BALANCE_TRIAL_AND_KYC
			}

			// Find the balance criteria
			for _, criteriaValue := range criteriaMinValues {
				if criteriaValue.Criteria == criteriaToGetTargetAmb && criteriaValue.MinValue != nil {
					minValue, _ := money.ToDecimal(criteriaValue.MinValue).Float64()
					return minValue, nil
				}
			}
		}
	}
	return 0, pkgErrors.ErrTierHasNoMinBalanceCriteria
}
