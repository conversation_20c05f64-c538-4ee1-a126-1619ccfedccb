package tiering

import (
	"fmt"

	"golang.org/x/text/cases"
	"golang.org/x/text/language"

	widgetPb "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	beTieringPb "github.com/epifi/gamma/api/tiering"
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	beTieringExtPb "github.com/epifi/gamma/api/tiering/external"
	pkgErrors "github.com/epifi/gamma/pkg/tiering/errors"
)

// GetAllCriteriaMinValuesFromOptions extracts all criteria minimum values from a list of tiering options
func GetAllCriteriaMinValuesFromOptions(tierOptions []*tieringCriteriaPb.Option) ([]*CriteriaMinValue, error) {
	var criteriaMinValues []*CriteriaMinValue

	for _, option := range tierOptions {
		for _, action := range option.GetActions() {
			ac := action.GetActionDetails().GetCriteria()
			switch actionCriteria := ac.(type) {
			case *tieringCriteriaPb.QualifyingCriteria_UsStocksSip:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_US_STOCKS_SIP_AND_KYC,
						MinValue: actionCriteria.UsStocksSip.GetMinWalletAddFunds(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_Deposits:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_DEPOSITS_AND_KYC,
						MinValue: actionCriteria.Deposits.GetMinDepositsAmount(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_BalanceV2:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_BALANCE_V2_AND_KYC,
						MinValue: actionCriteria.BalanceV2.GetMinBalanceForUpgrade(),
					})
			case *tieringCriteriaPb.QualifyingCriteria_BalanceTrial:
				criteriaMinValues = append(criteriaMinValues,
					&CriteriaMinValue{
						Criteria: tieringEnumPb.CriteriaOptionType_BALANCE_TRIAL_AND_KYC,
						MinValue: actionCriteria.BalanceTrial.GetMinBalanceForTrial(),
					})
			}
		}
	}

	if len(criteriaMinValues) > 0 {
		return criteriaMinValues, nil
	}

	return nil, pkgErrors.ErrTierHasNoMinBalanceCriteria
}

// DetermineBalanceBucket determines the balance bucket the amount falls into
// bucketRanges sorted in ascending order and define the lower bounds of each bucket.
// The returned string is of the form "lower-upper" (both inclusive).
// Eg. if the amount is 3001, we consider the amount to be in 3001 - 5001 because it checks if the amount is between 3001 and 5000 (both inclusive) and returns '3001-5000'
func DetermineBalanceBucket(amount int64) string {
	var bucketRanges = []int64{
		0, 501, 1001, 3001, 5001, 7501, 10001, 20001, 25001, 30001, 40001, 45001, 50001, 100001,
	}
	for bucketIdx, upperBound := range bucketRanges {
		if amount < upperBound {
			if bucketIdx == 0 {
				return fmt.Sprintf("%d", bucketRanges[0])
			}
			lowerBound := bucketRanges[bucketIdx-1]
			return fmt.Sprintf("%d-%d", lowerBound, upperBound-1)
		}
	}
	return fmt.Sprintf("%d+", bucketRanges[len(bucketRanges)-1]-1)
}

var (
	regularDisplayString     = "REGULAR"
	basicDisplayString       = "STANDARD"
	plusDisplayString        = "PLUS"
	infiniteDisplayString    = "INFINITE"
	salaryDisplayString      = "SALARY"
	salaryBasicDisplayString = "SALARY BASIC"
	salaryLiteDisplayString  = "SALARY LITE"
	aaSalaryDisplayString    = "PRIME"
)

func GetTitleCaseDisplayString(tier beTieringExtPb.Tier) (string, error) {
	caser := cases.Title(language.English)
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_REGULAR:
		return caser.String(regularDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return caser.String(basicDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return caser.String(plusDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return caser.String(infiniteDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return caser.String(salaryDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return caser.String(salaryLiteDisplayString), nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return caser.String(salaryBasicDisplayString), nil
	default:
		if tier.IsAaSalaryTier() {
			return caser.String(aaSalaryDisplayString), nil
		}
		return "", fmt.Errorf("%s tier is not handled for display strings", tier.String())
	}
}

func GetCapitalCaseDisplayString(tier beTieringExtPb.Tier) (string, error) {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_BASIC:
		return basicDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return plusDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return infiniteDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY:
		return salaryDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return salaryDisplayString, nil
	case beTieringExtPb.Tier_TIER_FI_SALARY_LITE:
		return salaryLiteDisplayString, nil
	default:
		if tier.IsAaSalaryTier() {
			return aaSalaryDisplayString, nil
		}
		return "", fmt.Errorf("%s tier is not handled for display strings", tier.String())
	}
}

func GetGradientColorStopsForTier(tier beTieringExtPb.Tier) []*widgetPb.ColorStop {
	switch tier {
	case beTieringExtPb.Tier_TIER_FI_PLUS:
		return []*widgetPb.ColorStop{{Color: "#DBB295", StopPercentage: -34}, {Color: "#84432E", StopPercentage: 105}}
	case beTieringExtPb.Tier_TIER_FI_INFINITE:
		return []*widgetPb.ColorStop{{Color: "#DBE7F3", StopPercentage: -58}, {Color: "#3C5D7E", StopPercentage: 102}}
	case beTieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3:
		return []*widgetPb.ColorStop{{Color: "#4F71AB", StopPercentage: 2}, {Color: "#17478A", StopPercentage: 113}}
	case beTieringExtPb.Tier_TIER_FI_SALARY, beTieringExtPb.Tier_TIER_FI_SALARY_BASIC:
		return []*widgetPb.ColorStop{{Color: "#F6E1C1", StopPercentage: 2}, {Color: "#C0723D", StopPercentage: 105}}

	default:
		return nil
	}
}

func GetLatestMovementDetails(tieringPitchResp *beTieringPb.GetTieringPitchV2Response) *beTieringExtPb.LatestMovementDetails {
	var latestMovementDetails *beTieringExtPb.LatestMovementDetails
	if tieringPitchResp.GetLastDowngradeDetails().GetMovementTimestamp().AsTime().After(tieringPitchResp.GetLastUpgradeDetails().GetMovementTimestamp().AsTime()) {
		latestMovementDetails = tieringPitchResp.GetLastDowngradeDetails()
	} else {
		latestMovementDetails = tieringPitchResp.GetLastUpgradeDetails()
	}

	return latestMovementDetails
}
