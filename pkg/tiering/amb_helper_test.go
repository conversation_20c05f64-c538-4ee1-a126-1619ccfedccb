package tiering

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringCriteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	"github.com/epifi/gamma/api/tiering/external"
)

func TestGetTargetAMBFromTieringPitch(t *testing.T) {
	// Create a sample tiering pitch response
	regularTierMinBalance := &gmoney.Money{CurrencyCode: "INR", Units: 5000}

	configParamsResp := &tieringPb.GetConfigParamsResponse{
		RegularTierConfigParams: &tieringPb.RegularTierConfigParams{
			MinBalanceForRegularTier: regularTierMinBalance,
		},
	}

	// Create criteria with balance value
	balanceAction := &tieringCriteriaPb.Action{
		ActionDetails: &tieringCriteriaPb.QualifyingCriteria{
			Criteria: &tieringCriteriaPb.QualifyingCriteria_BalanceV2{
				BalanceV2: &tieringCriteriaPb.BalanceV2{
					MinBalanceForUpgrade: &gmoney.Money{CurrencyCode: "INR", Units: 25000},
				},
			},
		},
	}

	balanceTrialAction := &tieringCriteriaPb.Action{
		ActionDetails: &tieringCriteriaPb.QualifyingCriteria{
			Criteria: &tieringCriteriaPb.QualifyingCriteria_BalanceTrial{
				BalanceTrial: &tieringCriteriaPb.BalanceTrial{
					MinBalanceForTrial: &gmoney.Money{CurrencyCode: moneyPkg.RupeeCurrencyCode, Units: 12500},
				},
			},
		},
	}

	// Create an balanceOption with the balance action
	balanceOption := &tieringCriteriaPb.Option{
		Actions: []*tieringCriteriaPb.Action{balanceAction},
	}

	// Create a trialOption with the balance trial action
	trialOption := &tieringCriteriaPb.Option{
		Actions: []*tieringCriteriaPb.Action{balanceTrialAction},
	}

	// Test cases
	testCases := []struct {
		name             string
		tieringPitchResp *tieringPb.GetTieringPitchV2Response
		configParamsResp *tieringPb.GetConfigParamsResponse
		trialsResponse   *tieringPb.GetTrialDetailsResponse
		expectedAMB      float64
		expectError      bool
	}{
		{
			name: "should return successfully for regular tier",
			tieringPitchResp: &tieringPb.GetTieringPitchV2Response{
				CurrentTier: external.Tier_TIER_FI_REGULAR,
			},
			configParamsResp: configParamsResp,
			expectedAMB:      5000,
			expectError:      false,
		},
		{
			name: "should return successfully for basic tier",
			tieringPitchResp: &tieringPb.GetTieringPitchV2Response{
				CurrentTier: external.Tier_TIER_FI_BASIC,
			},
			configParamsResp: configParamsResp,
			expectedAMB:      0,
			expectError:      false,
		},
		{
			name: "should return succesfully for plus Tier with Criteria",
			tieringPitchResp: &tieringPb.GetTieringPitchV2Response{
				CurrentTier: external.Tier_TIER_FI_PLUS,
				MovementDetailsList: []*external.MovementExternalDetails{
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{balanceOption},
					},
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{trialOption},
					},
				},
			},
			configParamsResp: configParamsResp,
			expectedAMB:      25000,
			expectError:      false,
		},
		{
			name: "should return succesfully for plus Tier with Criteria for user on trial",
			tieringPitchResp: &tieringPb.GetTieringPitchV2Response{
				CurrentTier: external.Tier_TIER_FI_PLUS,
				MovementDetailsList: []*external.MovementExternalDetails{
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{balanceOption},
					},
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{trialOption},
					},
				},
			},
			configParamsResp: configParamsResp,
			trialsResponse: &tieringPb.GetTrialDetailsResponse{
				Status: rpcPb.StatusOk(),
				TrialDetails: &external.TrialDetails{
					Tier:           external.Tier_TIER_FI_PLUS,
					OptedAt:        timestamppb.New(time.Now().Add(-2 * 24 * time.Hour)),
					TrialStartTime: timestamppb.New(time.Now().Add(-2 * 24 * time.Hour)),
					TrialEndTime:   timestamppb.New(time.Now().Add(2 * 24 * time.Hour)),
				},
				IsEligibleForTrial: false,
				EligibleTrialTier:  0,
			},
			expectedAMB: 12500,
			expectError: false,
		},
		{
			name: "should return error for no criteria",
			tieringPitchResp: &tieringPb.GetTieringPitchV2Response{
				CurrentTier: external.Tier_TIER_FI_PLUS,
				MovementDetailsList: []*external.MovementExternalDetails{
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{},
					},
					{
						TierName: external.Tier_TIER_FI_PLUS,
						Options:  []*tieringCriteriaPb.Option{trialOption},
					},
				},
			},
			configParamsResp: configParamsResp,
			expectedAMB:      0, // Fallback value
			expectError:      true,
		},
		{
			name:             "should error out for empty tiering pitch",
			tieringPitchResp: nil,
			configParamsResp: configParamsResp,
			expectedAMB:      0,
			expectError:      true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			amb, err := GetTargetAMBFromTieringPitch(tc.tieringPitchResp, tc.configParamsResp.GetRegularTierConfigParams(), tc.trialsResponse)

			if tc.expectError {
				assert.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.InDelta(t, tc.expectedAMB, amb, 0.01)
			}
		})
	}
}
