//go:generate gen_queue_pb

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/risk/case_management/consumer.proto

package case_management

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	s3 "github.com/epifi/gamma/api/aws/s3"
	event "github.com/epifi/gamma/api/cx/call_routing/event"
	ticket "github.com/epifi/gamma/api/cx/ticket"
	enums "github.com/epifi/gamma/api/risk/case_management/enums"
	form "github.com/epifi/gamma/api/risk/case_management/form"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CXTicketUpdateEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// cx ticket with updated fields
	Ticket *ticket.Ticket `protobuf:"bytes,2,opt,name=ticket,proto3" json:"ticket,omitempty"`
}

func (x *CXTicketUpdateEvent) Reset() {
	*x = CXTicketUpdateEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CXTicketUpdateEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CXTicketUpdateEvent) ProtoMessage() {}

func (x *CXTicketUpdateEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CXTicketUpdateEvent.ProtoReflect.Descriptor instead.
func (*CXTicketUpdateEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *CXTicketUpdateEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *CXTicketUpdateEvent) GetTicket() *ticket.Ticket {
	if x != nil {
		return x.Ticket
	}
	return nil
}

type ProcessCXTicketUpdateEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCXTicketUpdateEventResponse) Reset() {
	*x = ProcessCXTicketUpdateEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCXTicketUpdateEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCXTicketUpdateEventResponse) ProtoMessage() {}

func (x *ProcessCXTicketUpdateEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCXTicketUpdateEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCXTicketUpdateEventResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *ProcessCXTicketUpdateEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type RiskCasesIngestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// we will receive a batch of risk cases to be ingested with each event
	RiskCases []*RiskCase `protobuf:"bytes,2,rep,name=risk_cases,json=riskCases,proto3" json:"risk_cases,omitempty"`
}

func (x *RiskCasesIngestEvent) Reset() {
	*x = RiskCasesIngestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskCasesIngestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskCasesIngestEvent) ProtoMessage() {}

func (x *RiskCasesIngestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskCasesIngestEvent.ProtoReflect.Descriptor instead.
func (*RiskCasesIngestEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *RiskCasesIngestEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *RiskCasesIngestEvent) GetRiskCases() []*RiskCase {
	if x != nil {
		return x.RiskCases
	}
	return nil
}

type AddCasesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AddCasesResponse) Reset() {
	*x = AddCasesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddCasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddCasesResponse) ProtoMessage() {}

func (x *AddCasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddCasesResponse.ProtoReflect.Descriptor instead.
func (*AddCasesResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{3}
}

func (x *AddCasesResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type FrmIngestAlertsEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// we will receive a batch of alerts to be ingested with each event
	AlertPayload []*FrmIngestAlertsEvent_AlertPayload `protobuf:"bytes,2,rep,name=alert_payload,json=alertPayload,proto3" json:"alert_payload,omitempty"`
	// originating point for the alert
	Provenance enums.Provenance `protobuf:"varint,3,opt,name=provenance,proto3,enum=risk.case_management.enums.Provenance" json:"provenance,omitempty"`
}

func (x *FrmIngestAlertsEvent) Reset() {
	*x = FrmIngestAlertsEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FrmIngestAlertsEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrmIngestAlertsEvent) ProtoMessage() {}

func (x *FrmIngestAlertsEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrmIngestAlertsEvent.ProtoReflect.Descriptor instead.
func (*FrmIngestAlertsEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{4}
}

func (x *FrmIngestAlertsEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *FrmIngestAlertsEvent) GetAlertPayload() []*FrmIngestAlertsEvent_AlertPayload {
	if x != nil {
		return x.AlertPayload
	}
	return nil
}

func (x *FrmIngestAlertsEvent) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

type DSAlertEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawAlert *RawAlert `protobuf:"bytes,1,opt,name=raw_alert,json=rawAlert,proto3" json:"raw_alert,omitempty"`
}

func (x *DSAlertEvent) Reset() {
	*x = DSAlertEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DSAlertEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DSAlertEvent) ProtoMessage() {}

func (x *DSAlertEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DSAlertEvent.ProtoReflect.Descriptor instead.
func (*DSAlertEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{5}
}

func (x *DSAlertEvent) GetRawAlert() *RawAlert {
	if x != nil {
		return x.RawAlert
	}
	return nil
}

type AddAlertsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *AddAlertsResponse) Reset() {
	*x = AddAlertsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddAlertsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddAlertsResponse) ProtoMessage() {}

func (x *AddAlertsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddAlertsResponse.ProtoReflect.Descriptor instead.
func (*AddAlertsResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{6}
}

func (x *AddAlertsResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type FormSubmissionEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	FormId        string                       `protobuf:"bytes,2,opt,name=form_id,json=formId,proto3" json:"form_id,omitempty"`
	// list of responses submitted by user for set of questions provided
	QuestionResponses []*form.QuestionResponse `protobuf:"bytes,3,rep,name=question_responses,json=questionResponses,proto3" json:"question_responses,omitempty"`
}

func (x *FormSubmissionEvent) Reset() {
	*x = FormSubmissionEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FormSubmissionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FormSubmissionEvent) ProtoMessage() {}

func (x *FormSubmissionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FormSubmissionEvent.ProtoReflect.Descriptor instead.
func (*FormSubmissionEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{7}
}

func (x *FormSubmissionEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *FormSubmissionEvent) GetFormId() string {
	if x != nil {
		return x.FormId
	}
	return ""
}

func (x *FormSubmissionEvent) GetQuestionResponses() []*form.QuestionResponse {
	if x != nil {
		return x.QuestionResponses
	}
	return nil
}

type ProcessFormSubmissionResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessFormSubmissionResponse) Reset() {
	*x = ProcessFormSubmissionResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessFormSubmissionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessFormSubmissionResponse) ProtoMessage() {}

func (x *ProcessFormSubmissionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessFormSubmissionResponse.ProtoReflect.Descriptor instead.
func (*ProcessFormSubmissionResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{8}
}

func (x *ProcessFormSubmissionResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type ProcessCallRoutingEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessCallRoutingEventResponse) Reset() {
	*x = ProcessCallRoutingEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessCallRoutingEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessCallRoutingEventResponse) ProtoMessage() {}

func (x *ProcessCallRoutingEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessCallRoutingEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessCallRoutingEventResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{9}
}

func (x *ProcessCallRoutingEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// BatchRuleEngineEvent will consist of rule hits for multiple rules.
type BatchRuleEngineEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// Single record will be all hits from a rule.
	Records []*s3.Record `protobuf:"bytes,2,rep,name=records,json=Records,proto3" json:"records,omitempty"`
}

func (x *BatchRuleEngineEvent) Reset() {
	*x = BatchRuleEngineEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchRuleEngineEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchRuleEngineEvent) ProtoMessage() {}

func (x *BatchRuleEngineEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchRuleEngineEvent.ProtoReflect.Descriptor instead.
func (*BatchRuleEngineEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{10}
}

func (x *BatchRuleEngineEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *BatchRuleEngineEvent) GetRecords() []*s3.Record {
	if x != nil {
		return x.Records
	}
	return nil
}

type ProcessBatchRuleEngineEventResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *ProcessBatchRuleEngineEventResponse) Reset() {
	*x = ProcessBatchRuleEngineEventResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProcessBatchRuleEngineEventResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProcessBatchRuleEngineEventResponse) ProtoMessage() {}

func (x *ProcessBatchRuleEngineEventResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProcessBatchRuleEngineEventResponse.ProtoReflect.Descriptor instead.
func (*ProcessBatchRuleEngineEventResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{11}
}

func (x *ProcessBatchRuleEngineEventResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

// Event for ingesting transaction blocks
type RiskSignalIngestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Alert         *RiskSignalIngestEvent_Alert `protobuf:"bytes,4,opt,name=alert,proto3" json:"alert,omitempty"`
}

func (x *RiskSignalIngestEvent) Reset() {
	*x = RiskSignalIngestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSignalIngestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSignalIngestEvent) ProtoMessage() {}

func (x *RiskSignalIngestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSignalIngestEvent.ProtoReflect.Descriptor instead.
func (*RiskSignalIngestEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{12}
}

func (x *RiskSignalIngestEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *RiskSignalIngestEvent) GetAlert() *RiskSignalIngestEvent_Alert {
	if x != nil {
		return x.Alert
	}
	return nil
}

// Response for transaction block ingestion
type RiskSignalIngestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Response header with status of the message consumption
	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *RiskSignalIngestResponse) Reset() {
	*x = RiskSignalIngestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSignalIngestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSignalIngestResponse) ProtoMessage() {}

func (x *RiskSignalIngestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSignalIngestResponse.ProtoReflect.Descriptor instead.
func (*RiskSignalIngestResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{13}
}

func (x *RiskSignalIngestResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type RiskAlertIngestEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Alert         *RiskAlertIngestEvent_Alert  `protobuf:"bytes,4,opt,name=alert,proto3" json:"alert,omitempty"`
}

func (x *RiskAlertIngestEvent) Reset() {
	*x = RiskAlertIngestEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAlertIngestEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAlertIngestEvent) ProtoMessage() {}

func (x *RiskAlertIngestEvent) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAlertIngestEvent.ProtoReflect.Descriptor instead.
func (*RiskAlertIngestEvent) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{14}
}

func (x *RiskAlertIngestEvent) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *RiskAlertIngestEvent) GetAlert() *RiskAlertIngestEvent_Alert {
	if x != nil {
		return x.Alert
	}
	return nil
}

type RiskAlertIngestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResponseHeader *queue.ConsumerResponseHeader `protobuf:"bytes,1,opt,name=response_header,json=responseHeader,proto3" json:"response_header,omitempty"`
}

func (x *RiskAlertIngestResponse) Reset() {
	*x = RiskAlertIngestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAlertIngestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAlertIngestResponse) ProtoMessage() {}

func (x *RiskAlertIngestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAlertIngestResponse.ProtoReflect.Descriptor instead.
func (*RiskAlertIngestResponse) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{15}
}

func (x *RiskAlertIngestResponse) GetResponseHeader() *queue.ConsumerResponseHeader {
	if x != nil {
		return x.ResponseHeader
	}
	return nil
}

type FrmIngestAlertsEvent_AlertPayload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to Payload:
	//
	//	*FrmIngestAlertsEvent_AlertPayload_DsAlertEvent
	Payload isFrmIngestAlertsEvent_AlertPayload_Payload `protobuf_oneof:"payload"`
}

func (x *FrmIngestAlertsEvent_AlertPayload) Reset() {
	*x = FrmIngestAlertsEvent_AlertPayload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FrmIngestAlertsEvent_AlertPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FrmIngestAlertsEvent_AlertPayload) ProtoMessage() {}

func (x *FrmIngestAlertsEvent_AlertPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FrmIngestAlertsEvent_AlertPayload.ProtoReflect.Descriptor instead.
func (*FrmIngestAlertsEvent_AlertPayload) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{4, 0}
}

func (m *FrmIngestAlertsEvent_AlertPayload) GetPayload() isFrmIngestAlertsEvent_AlertPayload_Payload {
	if m != nil {
		return m.Payload
	}
	return nil
}

func (x *FrmIngestAlertsEvent_AlertPayload) GetDsAlertEvent() *DSAlertEvent {
	if x, ok := x.GetPayload().(*FrmIngestAlertsEvent_AlertPayload_DsAlertEvent); ok {
		return x.DsAlertEvent
	}
	return nil
}

type isFrmIngestAlertsEvent_AlertPayload_Payload interface {
	isFrmIngestAlertsEvent_AlertPayload_Payload()
}

type FrmIngestAlertsEvent_AlertPayload_DsAlertEvent struct {
	// Payload schema for data-science alerts
	DsAlertEvent *DSAlertEvent `protobuf:"bytes,1,opt,name=ds_alert_event,json=dsAlertEvent,proto3,oneof"`
}

func (*FrmIngestAlertsEvent_AlertPayload_DsAlertEvent) isFrmIngestAlertsEvent_AlertPayload_Payload() {
}

// List of transaction blocks to ingest
type RiskSignalIngestEvent_Alert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawAlert   *RawAlert        `protobuf:"bytes,2,opt,name=raw_alert,json=rawAlert,proto3" json:"raw_alert,omitempty"`
	Provenance enums.Provenance `protobuf:"varint,3,opt,name=provenance,proto3,enum=risk.case_management.enums.Provenance" json:"provenance,omitempty"`
}

func (x *RiskSignalIngestEvent_Alert) Reset() {
	*x = RiskSignalIngestEvent_Alert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskSignalIngestEvent_Alert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskSignalIngestEvent_Alert) ProtoMessage() {}

func (x *RiskSignalIngestEvent_Alert) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskSignalIngestEvent_Alert.ProtoReflect.Descriptor instead.
func (*RiskSignalIngestEvent_Alert) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{12, 0}
}

func (x *RiskSignalIngestEvent_Alert) GetRawAlert() *RawAlert {
	if x != nil {
		return x.RawAlert
	}
	return nil
}

func (x *RiskSignalIngestEvent_Alert) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

type RiskAlertIngestEvent_Alert struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RawAlert   *RawAlert        `protobuf:"bytes,2,opt,name=raw_alert,json=rawAlert,proto3" json:"raw_alert,omitempty"`
	Provenance enums.Provenance `protobuf:"varint,3,opt,name=provenance,proto3,enum=risk.case_management.enums.Provenance" json:"provenance,omitempty"`
}

func (x *RiskAlertIngestEvent_Alert) Reset() {
	*x = RiskAlertIngestEvent_Alert{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_risk_case_management_consumer_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RiskAlertIngestEvent_Alert) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RiskAlertIngestEvent_Alert) ProtoMessage() {}

func (x *RiskAlertIngestEvent_Alert) ProtoReflect() protoreflect.Message {
	mi := &file_api_risk_case_management_consumer_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RiskAlertIngestEvent_Alert.ProtoReflect.Descriptor instead.
func (*RiskAlertIngestEvent_Alert) Descriptor() ([]byte, []int) {
	return file_api_risk_case_management_consumer_proto_rawDescGZIP(), []int{14, 0}
}

func (x *RiskAlertIngestEvent_Alert) GetRawAlert() *RawAlert {
	if x != nil {
		return x.RawAlert
	}
	return nil
}

func (x *RiskAlertIngestEvent_Alert) GetProvenance() enums.Provenance {
	if x != nil {
		return x.Provenance
	}
	return enums.Provenance(0)
}

var File_api_risk_case_management_consumer_proto protoreflect.FileDescriptor

var file_api_risk_case_management_consumer_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x1a,
	0x13, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x77, 0x73, 0x2f, 0x73, 0x33, 0x2f, 0x73, 0x33, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2f,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1a, 0x61, 0x70, 0x69,
	0x2f, 0x63, 0x78, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x2f, 0x74, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2a, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f,
	0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2c, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x66, 0x6f, 0x72, 0x6d, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x72,
	0x69, 0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2f, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x01, 0x0a,
	0x13, 0x43, 0x58, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71,
	0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x29, 0x0a, 0x06, 0x74, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x78, 0x2e, 0x74,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x2e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x74, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x22, 0x6c, 0x0a, 0x22, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43,
	0x58, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x22, 0xa6, 0x01, 0x0a, 0x14, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x73,
	0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73,
	0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x49, 0x0a, 0x0a, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x63, 0x61, 0x73, 0x65, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b,
	0x43, 0x61, 0x73, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10, 0x0a,
	0x52, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x73, 0x22, 0x5a, 0x0a, 0x10, 0x41,
	0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xf4, 0x02, 0x0a, 0x14, 0x46, 0x72, 0x6d, 0x49,
	0x6e, 0x67, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x68, 0x0a, 0x0d, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x70,
	0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x46, 0x72, 0x6d, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x50, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x92, 0x01, 0x04, 0x08, 0x01, 0x10,
	0x0a, 0x52, 0x0c, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12,
	0x46, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x1a, 0x65, 0x0a, 0x0c, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x4a, 0x0a, 0x0e, 0x64, 0x73, 0x5f, 0x61, 0x6c,
	0x65, 0x72, 0x74, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x44, 0x53, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x0c, 0x64, 0x73, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x42, 0x09, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x22, 0x4b,
	0x0a, 0x0c, 0x44, 0x53, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x3b,
	0x0a, 0x09, 0x72, 0x61, 0x77, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72,
	0x74, 0x52, 0x08, 0x72, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x22, 0x5b, 0x0a, 0x11, 0x41,
	0x64, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xd8, 0x01, 0x0a, 0x13, 0x46, 0x6f, 0x72,
	0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x66, 0x6f, 0x72, 0x6d, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x06, 0x66, 0x6f, 0x72, 0x6d, 0x49, 0x64, 0x12, 0x5a, 0x0a, 0x12, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x66, 0x6f, 0x72, 0x6d, 0x2e,
	0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x52, 0x11, 0x71, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x73, 0x22, 0x67, 0x0a, 0x1d, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6f,
	0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x69, 0x0a, 0x1f,
	0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x6f, 0x75, 0x74, 0x69,
	0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x85, 0x01, 0x0a, 0x14, 0x42, 0x61, 0x74, 0x63,
	0x68, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x28, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x73, 0x33, 0x2e,
	0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x52, 0x07, 0x52, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x22,
	0x6d, 0x0a, 0x23, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52,
	0x75, 0x6c, 0x65, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb4,
	0x02, 0x0a, 0x15, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x67,
	0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x47, 0x0a,
	0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e,
	0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52,
	0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x1a, 0x8c, 0x01, 0x0a, 0x05, 0x41, 0x6c, 0x65, 0x72, 0x74,
	0x12, 0x3b, 0x0a, 0x09, 0x72, 0x61, 0x77, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x52, 0x08, 0x72, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x46, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50,
	0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x62, 0x0a, 0x18, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0xb2, 0x02, 0x0a, 0x14, 0x52, 0x69,
	0x73, 0x6b, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x46, 0x0a, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69,
	0x73, 0x6b, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x05, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x1a,
	0x8c, 0x01, 0x0a, 0x05, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x3b, 0x0a, 0x09, 0x72, 0x61, 0x77,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x61, 0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x52, 0x08, 0x72, 0x61,
	0x77, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x12, 0x46, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x22, 0x61,
	0x0a, 0x17, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x6e, 0x67, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x46, 0x0a, 0x0f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75,
	0x6d, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x32, 0xca, 0x07, 0x0a, 0x1a, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72,
	0x12, 0x5e, 0x0a, 0x08, 0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x12, 0x2a, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x65, 0x73, 0x49, 0x6e, 0x67,
	0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x26, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x41, 0x64, 0x64, 0x43, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x60, 0x0a, 0x09, 0x41, 0x64, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x12, 0x2a, 0x2e,
	0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x46, 0x72, 0x6d, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x73, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x27, 0x2e, 0x72, 0x69, 0x73, 0x6b,
	0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x2e, 0x41, 0x64, 0x64, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x77, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6f, 0x72,
	0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x33, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x46, 0x6f, 0x72, 0x6d, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x17, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61, 0x6c, 0x6c, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e,
	0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x27, 0x2e, 0x63, 0x78, 0x2e, 0x63, 0x61, 0x6c, 0x6c,
	0x5f, 0x72, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x43,
	0x61, 0x6c, 0x6c, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a,
	0x35, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x61,
	0x6c, 0x6c, 0x52, 0x6f, 0x75, 0x74, 0x69, 0x6e, 0x67, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x67, 0x69, 0x6e,
	0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x42, 0x61,
	0x74, 0x63, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x1a, 0x39, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x42, 0x61, 0x74, 0x63, 0x68, 0x52, 0x75, 0x6c, 0x65, 0x45, 0x6e, 0x67, 0x69, 0x6e, 0x65,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x81, 0x01,
	0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x58, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x29, 0x2e, 0x72,
	0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x2e, 0x43, 0x58, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x38, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x43, 0x58, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x75, 0x0a, 0x16, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b,
	0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x2b, 0x2e, 0x72, 0x69,
	0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x67,
	0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x2e, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e,
	0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e,
	0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x74, 0x0a, 0x15, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x69, 0x73, 0x6b, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x2b, 0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67,
	0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x1a, 0x2e,
	0x2e, 0x72, 0x69, 0x73, 0x6b, 0x2e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x6c,
	0x49, 0x6e, 0x67, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x62,
	0x0a, 0x2f, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x72, 0x69, 0x73,
	0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5a, 0x2f, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x69,
	0x73, 0x6b, 0x2f, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_risk_case_management_consumer_proto_rawDescOnce sync.Once
	file_api_risk_case_management_consumer_proto_rawDescData = file_api_risk_case_management_consumer_proto_rawDesc
)

func file_api_risk_case_management_consumer_proto_rawDescGZIP() []byte {
	file_api_risk_case_management_consumer_proto_rawDescOnce.Do(func() {
		file_api_risk_case_management_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_risk_case_management_consumer_proto_rawDescData)
	})
	return file_api_risk_case_management_consumer_proto_rawDescData
}

var file_api_risk_case_management_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_api_risk_case_management_consumer_proto_goTypes = []interface{}{
	(*CXTicketUpdateEvent)(nil),                 // 0: risk.case_management.CXTicketUpdateEvent
	(*ProcessCXTicketUpdateEventResponse)(nil),  // 1: risk.case_management.ProcessCXTicketUpdateEventResponse
	(*RiskCasesIngestEvent)(nil),                // 2: risk.case_management.RiskCasesIngestEvent
	(*AddCasesResponse)(nil),                    // 3: risk.case_management.AddCasesResponse
	(*FrmIngestAlertsEvent)(nil),                // 4: risk.case_management.FrmIngestAlertsEvent
	(*DSAlertEvent)(nil),                        // 5: risk.case_management.DSAlertEvent
	(*AddAlertsResponse)(nil),                   // 6: risk.case_management.AddAlertsResponse
	(*FormSubmissionEvent)(nil),                 // 7: risk.case_management.FormSubmissionEvent
	(*ProcessFormSubmissionResponse)(nil),       // 8: risk.case_management.ProcessFormSubmissionResponse
	(*ProcessCallRoutingEventResponse)(nil),     // 9: risk.case_management.ProcessCallRoutingEventResponse
	(*BatchRuleEngineEvent)(nil),                // 10: risk.case_management.BatchRuleEngineEvent
	(*ProcessBatchRuleEngineEventResponse)(nil), // 11: risk.case_management.ProcessBatchRuleEngineEventResponse
	(*RiskSignalIngestEvent)(nil),               // 12: risk.case_management.RiskSignalIngestEvent
	(*RiskSignalIngestResponse)(nil),            // 13: risk.case_management.RiskSignalIngestResponse
	(*RiskAlertIngestEvent)(nil),                // 14: risk.case_management.RiskAlertIngestEvent
	(*RiskAlertIngestResponse)(nil),             // 15: risk.case_management.RiskAlertIngestResponse
	(*FrmIngestAlertsEvent_AlertPayload)(nil),   // 16: risk.case_management.FrmIngestAlertsEvent.AlertPayload
	(*RiskSignalIngestEvent_Alert)(nil),         // 17: risk.case_management.RiskSignalIngestEvent.Alert
	(*RiskAlertIngestEvent_Alert)(nil),          // 18: risk.case_management.RiskAlertIngestEvent.Alert
	(*queue.ConsumerRequestHeader)(nil),         // 19: queue.ConsumerRequestHeader
	(*ticket.Ticket)(nil),                       // 20: cx.ticket.Ticket
	(*queue.ConsumerResponseHeader)(nil),        // 21: queue.ConsumerResponseHeader
	(*RiskCase)(nil),                            // 22: risk.case_management.RiskCase
	(enums.Provenance)(0),                       // 23: risk.case_management.enums.Provenance
	(*RawAlert)(nil),                            // 24: risk.case_management.RawAlert
	(*form.QuestionResponse)(nil),               // 25: risk.case_management.form.QuestionResponse
	(*s3.Record)(nil),                           // 26: aws.s3.Record
	(*event.CallRoutingEvent)(nil),              // 27: cx.call_routing.event.CallRoutingEvent
}
var file_api_risk_case_management_consumer_proto_depIdxs = []int32{
	19, // 0: risk.case_management.CXTicketUpdateEvent.request_header:type_name -> queue.ConsumerRequestHeader
	20, // 1: risk.case_management.CXTicketUpdateEvent.ticket:type_name -> cx.ticket.Ticket
	21, // 2: risk.case_management.ProcessCXTicketUpdateEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 3: risk.case_management.RiskCasesIngestEvent.request_header:type_name -> queue.ConsumerRequestHeader
	22, // 4: risk.case_management.RiskCasesIngestEvent.risk_cases:type_name -> risk.case_management.RiskCase
	21, // 5: risk.case_management.AddCasesResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 6: risk.case_management.FrmIngestAlertsEvent.request_header:type_name -> queue.ConsumerRequestHeader
	16, // 7: risk.case_management.FrmIngestAlertsEvent.alert_payload:type_name -> risk.case_management.FrmIngestAlertsEvent.AlertPayload
	23, // 8: risk.case_management.FrmIngestAlertsEvent.provenance:type_name -> risk.case_management.enums.Provenance
	24, // 9: risk.case_management.DSAlertEvent.raw_alert:type_name -> risk.case_management.RawAlert
	21, // 10: risk.case_management.AddAlertsResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 11: risk.case_management.FormSubmissionEvent.request_header:type_name -> queue.ConsumerRequestHeader
	25, // 12: risk.case_management.FormSubmissionEvent.question_responses:type_name -> risk.case_management.form.QuestionResponse
	21, // 13: risk.case_management.ProcessFormSubmissionResponse.response_header:type_name -> queue.ConsumerResponseHeader
	21, // 14: risk.case_management.ProcessCallRoutingEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 15: risk.case_management.BatchRuleEngineEvent.request_header:type_name -> queue.ConsumerRequestHeader
	26, // 16: risk.case_management.BatchRuleEngineEvent.records:type_name -> aws.s3.Record
	21, // 17: risk.case_management.ProcessBatchRuleEngineEventResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 18: risk.case_management.RiskSignalIngestEvent.request_header:type_name -> queue.ConsumerRequestHeader
	17, // 19: risk.case_management.RiskSignalIngestEvent.alert:type_name -> risk.case_management.RiskSignalIngestEvent.Alert
	21, // 20: risk.case_management.RiskSignalIngestResponse.response_header:type_name -> queue.ConsumerResponseHeader
	19, // 21: risk.case_management.RiskAlertIngestEvent.request_header:type_name -> queue.ConsumerRequestHeader
	18, // 22: risk.case_management.RiskAlertIngestEvent.alert:type_name -> risk.case_management.RiskAlertIngestEvent.Alert
	21, // 23: risk.case_management.RiskAlertIngestResponse.response_header:type_name -> queue.ConsumerResponseHeader
	5,  // 24: risk.case_management.FrmIngestAlertsEvent.AlertPayload.ds_alert_event:type_name -> risk.case_management.DSAlertEvent
	24, // 25: risk.case_management.RiskSignalIngestEvent.Alert.raw_alert:type_name -> risk.case_management.RawAlert
	23, // 26: risk.case_management.RiskSignalIngestEvent.Alert.provenance:type_name -> risk.case_management.enums.Provenance
	24, // 27: risk.case_management.RiskAlertIngestEvent.Alert.raw_alert:type_name -> risk.case_management.RawAlert
	23, // 28: risk.case_management.RiskAlertIngestEvent.Alert.provenance:type_name -> risk.case_management.enums.Provenance
	2,  // 29: risk.case_management.RiskCaseManagementConsumer.AddCases:input_type -> risk.case_management.RiskCasesIngestEvent
	4,  // 30: risk.case_management.RiskCaseManagementConsumer.AddAlerts:input_type -> risk.case_management.FrmIngestAlertsEvent
	7,  // 31: risk.case_management.RiskCaseManagementConsumer.ProcessFormSubmission:input_type -> risk.case_management.FormSubmissionEvent
	27, // 32: risk.case_management.RiskCaseManagementConsumer.ProcessCallRoutingEvent:input_type -> cx.call_routing.event.CallRoutingEvent
	10, // 33: risk.case_management.RiskCaseManagementConsumer.ProcessBatchRuleEngineEvent:input_type -> risk.case_management.BatchRuleEngineEvent
	0,  // 34: risk.case_management.RiskCaseManagementConsumer.ProcessCXTicketUpdateEvent:input_type -> risk.case_management.CXTicketUpdateEvent
	12, // 35: risk.case_management.RiskCaseManagementConsumer.ProcessRiskSignalEvent:input_type -> risk.case_management.RiskSignalIngestEvent
	12, // 36: risk.case_management.RiskCaseManagementConsumer.ProcessRiskAlertEvent:input_type -> risk.case_management.RiskSignalIngestEvent
	3,  // 37: risk.case_management.RiskCaseManagementConsumer.AddCases:output_type -> risk.case_management.AddCasesResponse
	6,  // 38: risk.case_management.RiskCaseManagementConsumer.AddAlerts:output_type -> risk.case_management.AddAlertsResponse
	8,  // 39: risk.case_management.RiskCaseManagementConsumer.ProcessFormSubmission:output_type -> risk.case_management.ProcessFormSubmissionResponse
	9,  // 40: risk.case_management.RiskCaseManagementConsumer.ProcessCallRoutingEvent:output_type -> risk.case_management.ProcessCallRoutingEventResponse
	11, // 41: risk.case_management.RiskCaseManagementConsumer.ProcessBatchRuleEngineEvent:output_type -> risk.case_management.ProcessBatchRuleEngineEventResponse
	1,  // 42: risk.case_management.RiskCaseManagementConsumer.ProcessCXTicketUpdateEvent:output_type -> risk.case_management.ProcessCXTicketUpdateEventResponse
	13, // 43: risk.case_management.RiskCaseManagementConsumer.ProcessRiskSignalEvent:output_type -> risk.case_management.RiskSignalIngestResponse
	13, // 44: risk.case_management.RiskCaseManagementConsumer.ProcessRiskAlertEvent:output_type -> risk.case_management.RiskSignalIngestResponse
	37, // [37:45] is the sub-list for method output_type
	29, // [29:37] is the sub-list for method input_type
	29, // [29:29] is the sub-list for extension type_name
	29, // [29:29] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_api_risk_case_management_consumer_proto_init() }
func file_api_risk_case_management_consumer_proto_init() {
	if File_api_risk_case_management_consumer_proto != nil {
		return
	}
	file_api_risk_case_management_alert_proto_init()
	file_api_risk_case_management_risk_case_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_risk_case_management_consumer_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CXTicketUpdateEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCXTicketUpdateEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskCasesIngestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddCasesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FrmIngestAlertsEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DSAlertEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddAlertsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FormSubmissionEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessFormSubmissionResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessCallRoutingEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchRuleEngineEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProcessBatchRuleEngineEventResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSignalIngestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSignalIngestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAlertIngestEvent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAlertIngestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FrmIngestAlertsEvent_AlertPayload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskSignalIngestEvent_Alert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_risk_case_management_consumer_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RiskAlertIngestEvent_Alert); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_risk_case_management_consumer_proto_msgTypes[16].OneofWrappers = []interface{}{
		(*FrmIngestAlertsEvent_AlertPayload_DsAlertEvent)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_risk_case_management_consumer_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_risk_case_management_consumer_proto_goTypes,
		DependencyIndexes: file_api_risk_case_management_consumer_proto_depIdxs,
		MessageInfos:      file_api_risk_case_management_consumer_proto_msgTypes,
	}.Build()
	File_api_risk_case_management_consumer_proto = out.File
	file_api_risk_case_management_consumer_proto_rawDesc = nil
	file_api_risk_case_management_consumer_proto_goTypes = nil
	file_api_risk_case_management_consumer_proto_depIdxs = nil
}
