// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/risk/case_management/consumer.proto

package case_management

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/risk/case_management/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.Provenance(0)
)

// Validate checks the field values on CXTicketUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CXTicketUpdateEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CXTicketUpdateEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CXTicketUpdateEventMultiError, or nil if none found.
func (m *CXTicketUpdateEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *CXTicketUpdateEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CXTicketUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CXTicketUpdateEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CXTicketUpdateEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTicket()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CXTicketUpdateEventValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CXTicketUpdateEventValidationError{
					field:  "Ticket",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTicket()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CXTicketUpdateEventValidationError{
				field:  "Ticket",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CXTicketUpdateEventMultiError(errors)
	}

	return nil
}

// CXTicketUpdateEventMultiError is an error wrapping multiple validation
// errors returned by CXTicketUpdateEvent.ValidateAll() if the designated
// constraints aren't met.
type CXTicketUpdateEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CXTicketUpdateEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CXTicketUpdateEventMultiError) AllErrors() []error { return m }

// CXTicketUpdateEventValidationError is the validation error returned by
// CXTicketUpdateEvent.Validate if the designated constraints aren't met.
type CXTicketUpdateEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CXTicketUpdateEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CXTicketUpdateEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CXTicketUpdateEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CXTicketUpdateEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CXTicketUpdateEventValidationError) ErrorName() string {
	return "CXTicketUpdateEventValidationError"
}

// Error satisfies the builtin error interface
func (e CXTicketUpdateEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCXTicketUpdateEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CXTicketUpdateEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CXTicketUpdateEventValidationError{}

// Validate checks the field values on ProcessCXTicketUpdateEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessCXTicketUpdateEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCXTicketUpdateEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessCXTicketUpdateEventResponseMultiError, or nil if none found.
func (m *ProcessCXTicketUpdateEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCXTicketUpdateEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCXTicketUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCXTicketUpdateEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCXTicketUpdateEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCXTicketUpdateEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCXTicketUpdateEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessCXTicketUpdateEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessCXTicketUpdateEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCXTicketUpdateEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCXTicketUpdateEventResponseMultiError) AllErrors() []error { return m }

// ProcessCXTicketUpdateEventResponseValidationError is the validation error
// returned by ProcessCXTicketUpdateEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCXTicketUpdateEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCXTicketUpdateEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCXTicketUpdateEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCXTicketUpdateEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCXTicketUpdateEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCXTicketUpdateEventResponseValidationError) ErrorName() string {
	return "ProcessCXTicketUpdateEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCXTicketUpdateEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCXTicketUpdateEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCXTicketUpdateEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCXTicketUpdateEventResponseValidationError{}

// Validate checks the field values on RiskCasesIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskCasesIngestEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskCasesIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskCasesIngestEventMultiError, or nil if none found.
func (m *RiskCasesIngestEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskCasesIngestEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskCasesIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskCasesIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskCasesIngestEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := len(m.GetRiskCases()); l < 1 || l > 10 {
		err := RiskCasesIngestEventValidationError{
			field:  "RiskCases",
			reason: "value must contain between 1 and 10 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetRiskCases() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RiskCasesIngestEventValidationError{
						field:  fmt.Sprintf("RiskCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RiskCasesIngestEventValidationError{
						field:  fmt.Sprintf("RiskCases[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RiskCasesIngestEventValidationError{
					field:  fmt.Sprintf("RiskCases[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RiskCasesIngestEventMultiError(errors)
	}

	return nil
}

// RiskCasesIngestEventMultiError is an error wrapping multiple validation
// errors returned by RiskCasesIngestEvent.ValidateAll() if the designated
// constraints aren't met.
type RiskCasesIngestEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskCasesIngestEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskCasesIngestEventMultiError) AllErrors() []error { return m }

// RiskCasesIngestEventValidationError is the validation error returned by
// RiskCasesIngestEvent.Validate if the designated constraints aren't met.
type RiskCasesIngestEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskCasesIngestEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskCasesIngestEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskCasesIngestEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskCasesIngestEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskCasesIngestEventValidationError) ErrorName() string {
	return "RiskCasesIngestEventValidationError"
}

// Error satisfies the builtin error interface
func (e RiskCasesIngestEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskCasesIngestEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskCasesIngestEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskCasesIngestEventValidationError{}

// Validate checks the field values on AddCasesResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddCasesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddCasesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddCasesResponseMultiError, or nil if none found.
func (m *AddCasesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddCasesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddCasesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddCasesResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddCasesResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddCasesResponseMultiError(errors)
	}

	return nil
}

// AddCasesResponseMultiError is an error wrapping multiple validation errors
// returned by AddCasesResponse.ValidateAll() if the designated constraints
// aren't met.
type AddCasesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddCasesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddCasesResponseMultiError) AllErrors() []error { return m }

// AddCasesResponseValidationError is the validation error returned by
// AddCasesResponse.Validate if the designated constraints aren't met.
type AddCasesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddCasesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddCasesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddCasesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddCasesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddCasesResponseValidationError) ErrorName() string { return "AddCasesResponseValidationError" }

// Error satisfies the builtin error interface
func (e AddCasesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddCasesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddCasesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddCasesResponseValidationError{}

// Validate checks the field values on FrmIngestAlertsEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FrmIngestAlertsEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FrmIngestAlertsEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FrmIngestAlertsEventMultiError, or nil if none found.
func (m *FrmIngestAlertsEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *FrmIngestAlertsEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FrmIngestAlertsEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FrmIngestAlertsEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FrmIngestAlertsEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if l := len(m.GetAlertPayload()); l < 1 || l > 10 {
		err := FrmIngestAlertsEventValidationError{
			field:  "AlertPayload",
			reason: "value must contain between 1 and 10 items, inclusive",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetAlertPayload() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FrmIngestAlertsEventValidationError{
						field:  fmt.Sprintf("AlertPayload[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FrmIngestAlertsEventValidationError{
						field:  fmt.Sprintf("AlertPayload[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FrmIngestAlertsEventValidationError{
					field:  fmt.Sprintf("AlertPayload[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Provenance

	if len(errors) > 0 {
		return FrmIngestAlertsEventMultiError(errors)
	}

	return nil
}

// FrmIngestAlertsEventMultiError is an error wrapping multiple validation
// errors returned by FrmIngestAlertsEvent.ValidateAll() if the designated
// constraints aren't met.
type FrmIngestAlertsEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FrmIngestAlertsEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FrmIngestAlertsEventMultiError) AllErrors() []error { return m }

// FrmIngestAlertsEventValidationError is the validation error returned by
// FrmIngestAlertsEvent.Validate if the designated constraints aren't met.
type FrmIngestAlertsEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FrmIngestAlertsEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FrmIngestAlertsEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FrmIngestAlertsEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FrmIngestAlertsEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FrmIngestAlertsEventValidationError) ErrorName() string {
	return "FrmIngestAlertsEventValidationError"
}

// Error satisfies the builtin error interface
func (e FrmIngestAlertsEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFrmIngestAlertsEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FrmIngestAlertsEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FrmIngestAlertsEventValidationError{}

// Validate checks the field values on DSAlertEvent with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DSAlertEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DSAlertEvent with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DSAlertEventMultiError, or
// nil if none found.
func (m *DSAlertEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *DSAlertEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRawAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DSAlertEventValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DSAlertEventValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRawAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DSAlertEventValidationError{
				field:  "RawAlert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DSAlertEventMultiError(errors)
	}

	return nil
}

// DSAlertEventMultiError is an error wrapping multiple validation errors
// returned by DSAlertEvent.ValidateAll() if the designated constraints aren't met.
type DSAlertEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DSAlertEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DSAlertEventMultiError) AllErrors() []error { return m }

// DSAlertEventValidationError is the validation error returned by
// DSAlertEvent.Validate if the designated constraints aren't met.
type DSAlertEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DSAlertEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DSAlertEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DSAlertEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DSAlertEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DSAlertEventValidationError) ErrorName() string { return "DSAlertEventValidationError" }

// Error satisfies the builtin error interface
func (e DSAlertEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDSAlertEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DSAlertEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DSAlertEventValidationError{}

// Validate checks the field values on AddAlertsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AddAlertsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AddAlertsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AddAlertsResponseMultiError, or nil if none found.
func (m *AddAlertsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AddAlertsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AddAlertsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AddAlertsResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AddAlertsResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AddAlertsResponseMultiError(errors)
	}

	return nil
}

// AddAlertsResponseMultiError is an error wrapping multiple validation errors
// returned by AddAlertsResponse.ValidateAll() if the designated constraints
// aren't met.
type AddAlertsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AddAlertsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AddAlertsResponseMultiError) AllErrors() []error { return m }

// AddAlertsResponseValidationError is the validation error returned by
// AddAlertsResponse.Validate if the designated constraints aren't met.
type AddAlertsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AddAlertsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AddAlertsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AddAlertsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AddAlertsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AddAlertsResponseValidationError) ErrorName() string {
	return "AddAlertsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AddAlertsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAddAlertsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AddAlertsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AddAlertsResponseValidationError{}

// Validate checks the field values on FormSubmissionEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FormSubmissionEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FormSubmissionEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FormSubmissionEventMultiError, or nil if none found.
func (m *FormSubmissionEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *FormSubmissionEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FormSubmissionEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FormSubmissionEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FormSubmissionEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if utf8.RuneCountInString(m.GetFormId()) < 1 {
		err := FormSubmissionEventValidationError{
			field:  "FormId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetQuestionResponses() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FormSubmissionEventValidationError{
						field:  fmt.Sprintf("QuestionResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FormSubmissionEventValidationError{
						field:  fmt.Sprintf("QuestionResponses[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FormSubmissionEventValidationError{
					field:  fmt.Sprintf("QuestionResponses[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return FormSubmissionEventMultiError(errors)
	}

	return nil
}

// FormSubmissionEventMultiError is an error wrapping multiple validation
// errors returned by FormSubmissionEvent.ValidateAll() if the designated
// constraints aren't met.
type FormSubmissionEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FormSubmissionEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FormSubmissionEventMultiError) AllErrors() []error { return m }

// FormSubmissionEventValidationError is the validation error returned by
// FormSubmissionEvent.Validate if the designated constraints aren't met.
type FormSubmissionEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FormSubmissionEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FormSubmissionEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FormSubmissionEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FormSubmissionEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FormSubmissionEventValidationError) ErrorName() string {
	return "FormSubmissionEventValidationError"
}

// Error satisfies the builtin error interface
func (e FormSubmissionEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFormSubmissionEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FormSubmissionEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FormSubmissionEventValidationError{}

// Validate checks the field values on ProcessFormSubmissionResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessFormSubmissionResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessFormSubmissionResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessFormSubmissionResponseMultiError, or nil if none found.
func (m *ProcessFormSubmissionResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessFormSubmissionResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessFormSubmissionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessFormSubmissionResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessFormSubmissionResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessFormSubmissionResponseMultiError(errors)
	}

	return nil
}

// ProcessFormSubmissionResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessFormSubmissionResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessFormSubmissionResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessFormSubmissionResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessFormSubmissionResponseMultiError) AllErrors() []error { return m }

// ProcessFormSubmissionResponseValidationError is the validation error
// returned by ProcessFormSubmissionResponse.Validate if the designated
// constraints aren't met.
type ProcessFormSubmissionResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessFormSubmissionResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessFormSubmissionResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessFormSubmissionResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessFormSubmissionResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessFormSubmissionResponseValidationError) ErrorName() string {
	return "ProcessFormSubmissionResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessFormSubmissionResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessFormSubmissionResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessFormSubmissionResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessFormSubmissionResponseValidationError{}

// Validate checks the field values on ProcessCallRoutingEventResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProcessCallRoutingEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessCallRoutingEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ProcessCallRoutingEventResponseMultiError, or nil if none found.
func (m *ProcessCallRoutingEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessCallRoutingEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessCallRoutingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessCallRoutingEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessCallRoutingEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessCallRoutingEventResponseMultiError(errors)
	}

	return nil
}

// ProcessCallRoutingEventResponseMultiError is an error wrapping multiple
// validation errors returned by ProcessCallRoutingEventResponse.ValidateAll()
// if the designated constraints aren't met.
type ProcessCallRoutingEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessCallRoutingEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessCallRoutingEventResponseMultiError) AllErrors() []error { return m }

// ProcessCallRoutingEventResponseValidationError is the validation error
// returned by ProcessCallRoutingEventResponse.Validate if the designated
// constraints aren't met.
type ProcessCallRoutingEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessCallRoutingEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessCallRoutingEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessCallRoutingEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessCallRoutingEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessCallRoutingEventResponseValidationError) ErrorName() string {
	return "ProcessCallRoutingEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessCallRoutingEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessCallRoutingEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessCallRoutingEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessCallRoutingEventResponseValidationError{}

// Validate checks the field values on BatchRuleEngineEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BatchRuleEngineEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BatchRuleEngineEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BatchRuleEngineEventMultiError, or nil if none found.
func (m *BatchRuleEngineEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *BatchRuleEngineEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BatchRuleEngineEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BatchRuleEngineEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BatchRuleEngineEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetRecords() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, BatchRuleEngineEventValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, BatchRuleEngineEventValidationError{
						field:  fmt.Sprintf("Records[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return BatchRuleEngineEventValidationError{
					field:  fmt.Sprintf("Records[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return BatchRuleEngineEventMultiError(errors)
	}

	return nil
}

// BatchRuleEngineEventMultiError is an error wrapping multiple validation
// errors returned by BatchRuleEngineEvent.ValidateAll() if the designated
// constraints aren't met.
type BatchRuleEngineEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BatchRuleEngineEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BatchRuleEngineEventMultiError) AllErrors() []error { return m }

// BatchRuleEngineEventValidationError is the validation error returned by
// BatchRuleEngineEvent.Validate if the designated constraints aren't met.
type BatchRuleEngineEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BatchRuleEngineEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BatchRuleEngineEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BatchRuleEngineEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BatchRuleEngineEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BatchRuleEngineEventValidationError) ErrorName() string {
	return "BatchRuleEngineEventValidationError"
}

// Error satisfies the builtin error interface
func (e BatchRuleEngineEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBatchRuleEngineEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BatchRuleEngineEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BatchRuleEngineEventValidationError{}

// Validate checks the field values on ProcessBatchRuleEngineEventResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ProcessBatchRuleEngineEventResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProcessBatchRuleEngineEventResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ProcessBatchRuleEngineEventResponseMultiError, or nil if none found.
func (m *ProcessBatchRuleEngineEventResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProcessBatchRuleEngineEventResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProcessBatchRuleEngineEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProcessBatchRuleEngineEventResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProcessBatchRuleEngineEventResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProcessBatchRuleEngineEventResponseMultiError(errors)
	}

	return nil
}

// ProcessBatchRuleEngineEventResponseMultiError is an error wrapping multiple
// validation errors returned by
// ProcessBatchRuleEngineEventResponse.ValidateAll() if the designated
// constraints aren't met.
type ProcessBatchRuleEngineEventResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProcessBatchRuleEngineEventResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProcessBatchRuleEngineEventResponseMultiError) AllErrors() []error { return m }

// ProcessBatchRuleEngineEventResponseValidationError is the validation error
// returned by ProcessBatchRuleEngineEventResponse.Validate if the designated
// constraints aren't met.
type ProcessBatchRuleEngineEventResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProcessBatchRuleEngineEventResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProcessBatchRuleEngineEventResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProcessBatchRuleEngineEventResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProcessBatchRuleEngineEventResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProcessBatchRuleEngineEventResponseValidationError) ErrorName() string {
	return "ProcessBatchRuleEngineEventResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProcessBatchRuleEngineEventResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProcessBatchRuleEngineEventResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProcessBatchRuleEngineEventResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProcessBatchRuleEngineEventResponseValidationError{}

// Validate checks the field values on RiskSignalIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSignalIngestEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSignalIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSignalIngestEventMultiError, or nil if none found.
func (m *RiskSignalIngestEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSignalIngestEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSignalIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSignalIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSignalIngestEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSignalIngestEventValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSignalIngestEventValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSignalIngestEventValidationError{
				field:  "Alert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskSignalIngestEventMultiError(errors)
	}

	return nil
}

// RiskSignalIngestEventMultiError is an error wrapping multiple validation
// errors returned by RiskSignalIngestEvent.ValidateAll() if the designated
// constraints aren't met.
type RiskSignalIngestEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSignalIngestEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSignalIngestEventMultiError) AllErrors() []error { return m }

// RiskSignalIngestEventValidationError is the validation error returned by
// RiskSignalIngestEvent.Validate if the designated constraints aren't met.
type RiskSignalIngestEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSignalIngestEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSignalIngestEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSignalIngestEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSignalIngestEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSignalIngestEventValidationError) ErrorName() string {
	return "RiskSignalIngestEventValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSignalIngestEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSignalIngestEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSignalIngestEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSignalIngestEventValidationError{}

// Validate checks the field values on RiskSignalIngestResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSignalIngestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSignalIngestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSignalIngestResponseMultiError, or nil if none found.
func (m *RiskSignalIngestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSignalIngestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSignalIngestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSignalIngestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSignalIngestResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskSignalIngestResponseMultiError(errors)
	}

	return nil
}

// RiskSignalIngestResponseMultiError is an error wrapping multiple validation
// errors returned by RiskSignalIngestResponse.ValidateAll() if the designated
// constraints aren't met.
type RiskSignalIngestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSignalIngestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSignalIngestResponseMultiError) AllErrors() []error { return m }

// RiskSignalIngestResponseValidationError is the validation error returned by
// RiskSignalIngestResponse.Validate if the designated constraints aren't met.
type RiskSignalIngestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSignalIngestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSignalIngestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSignalIngestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSignalIngestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSignalIngestResponseValidationError) ErrorName() string {
	return "RiskSignalIngestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSignalIngestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSignalIngestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSignalIngestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSignalIngestResponseValidationError{}

// Validate checks the field values on RiskAlertIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskAlertIngestEvent) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskAlertIngestEvent with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskAlertIngestEventMultiError, or nil if none found.
func (m *RiskAlertIngestEvent) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskAlertIngestEvent) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRequestHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskAlertIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskAlertIngestEventValidationError{
					field:  "RequestHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRequestHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskAlertIngestEventValidationError{
				field:  "RequestHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskAlertIngestEventValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskAlertIngestEventValidationError{
					field:  "Alert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskAlertIngestEventValidationError{
				field:  "Alert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskAlertIngestEventMultiError(errors)
	}

	return nil
}

// RiskAlertIngestEventMultiError is an error wrapping multiple validation
// errors returned by RiskAlertIngestEvent.ValidateAll() if the designated
// constraints aren't met.
type RiskAlertIngestEventMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskAlertIngestEventMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskAlertIngestEventMultiError) AllErrors() []error { return m }

// RiskAlertIngestEventValidationError is the validation error returned by
// RiskAlertIngestEvent.Validate if the designated constraints aren't met.
type RiskAlertIngestEventValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskAlertIngestEventValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskAlertIngestEventValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskAlertIngestEventValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskAlertIngestEventValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskAlertIngestEventValidationError) ErrorName() string {
	return "RiskAlertIngestEventValidationError"
}

// Error satisfies the builtin error interface
func (e RiskAlertIngestEventValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskAlertIngestEvent.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskAlertIngestEventValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskAlertIngestEventValidationError{}

// Validate checks the field values on RiskAlertIngestResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskAlertIngestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskAlertIngestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskAlertIngestResponseMultiError, or nil if none found.
func (m *RiskAlertIngestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskAlertIngestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResponseHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskAlertIngestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskAlertIngestResponseValidationError{
					field:  "ResponseHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResponseHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskAlertIngestResponseValidationError{
				field:  "ResponseHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return RiskAlertIngestResponseMultiError(errors)
	}

	return nil
}

// RiskAlertIngestResponseMultiError is an error wrapping multiple validation
// errors returned by RiskAlertIngestResponse.ValidateAll() if the designated
// constraints aren't met.
type RiskAlertIngestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskAlertIngestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskAlertIngestResponseMultiError) AllErrors() []error { return m }

// RiskAlertIngestResponseValidationError is the validation error returned by
// RiskAlertIngestResponse.Validate if the designated constraints aren't met.
type RiskAlertIngestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskAlertIngestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskAlertIngestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskAlertIngestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskAlertIngestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskAlertIngestResponseValidationError) ErrorName() string {
	return "RiskAlertIngestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e RiskAlertIngestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskAlertIngestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskAlertIngestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskAlertIngestResponseValidationError{}

// Validate checks the field values on FrmIngestAlertsEvent_AlertPayload with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *FrmIngestAlertsEvent_AlertPayload) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FrmIngestAlertsEvent_AlertPayload
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FrmIngestAlertsEvent_AlertPayloadMultiError, or nil if none found.
func (m *FrmIngestAlertsEvent_AlertPayload) ValidateAll() error {
	return m.validate(true)
}

func (m *FrmIngestAlertsEvent_AlertPayload) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.Payload.(type) {
	case *FrmIngestAlertsEvent_AlertPayload_DsAlertEvent:
		if v == nil {
			err := FrmIngestAlertsEvent_AlertPayloadValidationError{
				field:  "Payload",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetDsAlertEvent()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FrmIngestAlertsEvent_AlertPayloadValidationError{
						field:  "DsAlertEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FrmIngestAlertsEvent_AlertPayloadValidationError{
						field:  "DsAlertEvent",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetDsAlertEvent()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FrmIngestAlertsEvent_AlertPayloadValidationError{
					field:  "DsAlertEvent",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FrmIngestAlertsEvent_AlertPayloadMultiError(errors)
	}

	return nil
}

// FrmIngestAlertsEvent_AlertPayloadMultiError is an error wrapping multiple
// validation errors returned by
// FrmIngestAlertsEvent_AlertPayload.ValidateAll() if the designated
// constraints aren't met.
type FrmIngestAlertsEvent_AlertPayloadMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FrmIngestAlertsEvent_AlertPayloadMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FrmIngestAlertsEvent_AlertPayloadMultiError) AllErrors() []error { return m }

// FrmIngestAlertsEvent_AlertPayloadValidationError is the validation error
// returned by FrmIngestAlertsEvent_AlertPayload.Validate if the designated
// constraints aren't met.
type FrmIngestAlertsEvent_AlertPayloadValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) ErrorName() string {
	return "FrmIngestAlertsEvent_AlertPayloadValidationError"
}

// Error satisfies the builtin error interface
func (e FrmIngestAlertsEvent_AlertPayloadValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFrmIngestAlertsEvent_AlertPayload.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FrmIngestAlertsEvent_AlertPayloadValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FrmIngestAlertsEvent_AlertPayloadValidationError{}

// Validate checks the field values on RiskSignalIngestEvent_Alert with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskSignalIngestEvent_Alert) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskSignalIngestEvent_Alert with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskSignalIngestEvent_AlertMultiError, or nil if none found.
func (m *RiskSignalIngestEvent_Alert) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskSignalIngestEvent_Alert) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRawAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskSignalIngestEvent_AlertValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskSignalIngestEvent_AlertValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRawAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskSignalIngestEvent_AlertValidationError{
				field:  "RawAlert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	if len(errors) > 0 {
		return RiskSignalIngestEvent_AlertMultiError(errors)
	}

	return nil
}

// RiskSignalIngestEvent_AlertMultiError is an error wrapping multiple
// validation errors returned by RiskSignalIngestEvent_Alert.ValidateAll() if
// the designated constraints aren't met.
type RiskSignalIngestEvent_AlertMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskSignalIngestEvent_AlertMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskSignalIngestEvent_AlertMultiError) AllErrors() []error { return m }

// RiskSignalIngestEvent_AlertValidationError is the validation error returned
// by RiskSignalIngestEvent_Alert.Validate if the designated constraints
// aren't met.
type RiskSignalIngestEvent_AlertValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskSignalIngestEvent_AlertValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskSignalIngestEvent_AlertValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskSignalIngestEvent_AlertValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskSignalIngestEvent_AlertValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskSignalIngestEvent_AlertValidationError) ErrorName() string {
	return "RiskSignalIngestEvent_AlertValidationError"
}

// Error satisfies the builtin error interface
func (e RiskSignalIngestEvent_AlertValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskSignalIngestEvent_Alert.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskSignalIngestEvent_AlertValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskSignalIngestEvent_AlertValidationError{}

// Validate checks the field values on RiskAlertIngestEvent_Alert with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RiskAlertIngestEvent_Alert) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RiskAlertIngestEvent_Alert with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RiskAlertIngestEvent_AlertMultiError, or nil if none found.
func (m *RiskAlertIngestEvent_Alert) ValidateAll() error {
	return m.validate(true)
}

func (m *RiskAlertIngestEvent_Alert) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRawAlert()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, RiskAlertIngestEvent_AlertValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, RiskAlertIngestEvent_AlertValidationError{
					field:  "RawAlert",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRawAlert()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return RiskAlertIngestEvent_AlertValidationError{
				field:  "RawAlert",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Provenance

	if len(errors) > 0 {
		return RiskAlertIngestEvent_AlertMultiError(errors)
	}

	return nil
}

// RiskAlertIngestEvent_AlertMultiError is an error wrapping multiple
// validation errors returned by RiskAlertIngestEvent_Alert.ValidateAll() if
// the designated constraints aren't met.
type RiskAlertIngestEvent_AlertMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RiskAlertIngestEvent_AlertMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RiskAlertIngestEvent_AlertMultiError) AllErrors() []error { return m }

// RiskAlertIngestEvent_AlertValidationError is the validation error returned
// by RiskAlertIngestEvent_Alert.Validate if the designated constraints aren't met.
type RiskAlertIngestEvent_AlertValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RiskAlertIngestEvent_AlertValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RiskAlertIngestEvent_AlertValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RiskAlertIngestEvent_AlertValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RiskAlertIngestEvent_AlertValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RiskAlertIngestEvent_AlertValidationError) ErrorName() string {
	return "RiskAlertIngestEvent_AlertValidationError"
}

// Error satisfies the builtin error interface
func (e RiskAlertIngestEvent_AlertValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRiskAlertIngestEvent_Alert.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RiskAlertIngestEvent_AlertValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RiskAlertIngestEvent_AlertValidationError{}
