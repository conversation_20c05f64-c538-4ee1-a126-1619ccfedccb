//go:generate gen_queue_pb

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/risk/case_management/consumer.proto

package case_management

import (
	context "context"
	event "github.com/epifi/gamma/api/cx/call_routing/event"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	RiskCaseManagementConsumer_AddCases_FullMethodName                    = "/risk.case_management.RiskCaseManagementConsumer/AddCases"
	RiskCaseManagementConsumer_AddAlerts_FullMethodName                   = "/risk.case_management.RiskCaseManagementConsumer/AddAlerts"
	RiskCaseManagementConsumer_ProcessFormSubmission_FullMethodName       = "/risk.case_management.RiskCaseManagementConsumer/ProcessFormSubmission"
	RiskCaseManagementConsumer_ProcessCallRoutingEvent_FullMethodName     = "/risk.case_management.RiskCaseManagementConsumer/ProcessCallRoutingEvent"
	RiskCaseManagementConsumer_ProcessBatchRuleEngineEvent_FullMethodName = "/risk.case_management.RiskCaseManagementConsumer/ProcessBatchRuleEngineEvent"
	RiskCaseManagementConsumer_ProcessCXTicketUpdateEvent_FullMethodName  = "/risk.case_management.RiskCaseManagementConsumer/ProcessCXTicketUpdateEvent"
	RiskCaseManagementConsumer_ProcessRiskSignalEvent_FullMethodName      = "/risk.case_management.RiskCaseManagementConsumer/ProcessRiskSignalEvent"
	RiskCaseManagementConsumer_ProcessRiskAlertEvent_FullMethodName       = "/risk.case_management.RiskCaseManagementConsumer/ProcessRiskAlertEvent"
)

// RiskCaseManagementConsumerClient is the client API for RiskCaseManagementConsumer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RiskCaseManagementConsumerClient interface {
	AddCases(ctx context.Context, in *RiskCasesIngestEvent, opts ...grpc.CallOption) (*AddCasesResponse, error)
	// RPC to ingest alerts into case-management from different systems like DS
	// Accepts batched requests and handles errors internally
	AddAlerts(ctx context.Context, in *FrmIngestAlertsEvent, opts ...grpc.CallOption) (*AddAlertsResponse, error)
	// RPC to process risk form response from user.
	// Processing involves storing response in db and triggering form submission event.
	ProcessFormSubmission(ctx context.Context, in *FormSubmissionEvent, opts ...grpc.CallOption) (*ProcessFormSubmissionResponse, error)
	// RPC will consume event published by cx call routing service and process risk specific routing events
	// This will take care of risk specific handling needed for a particular routing event
	// Ex: resending comms if user is reaching out via call and we are routing them to a call recording
	ProcessCallRoutingEvent(ctx context.Context, in *event.CallRoutingEvent, opts ...grpc.CallOption) (*ProcessCallRoutingEventResponse, error)
	// ProcessBatchRuleEngineEvent will consume rule engine event for rule hits.
	// Rule engine periodically stores rule hits in s3 bucket as csv files.
	// New file creation triggers event and event is published to a queue.
	// This will create alerts in case management for all such rule hits.
	ProcessBatchRuleEngineEvent(ctx context.Context, in *BatchRuleEngineEvent, opts ...grpc.CallOption) (*ProcessBatchRuleEngineEventResponse, error)
	// ProcessCXTicketUpdateEvent will consume cx ticket update event
	ProcessCXTicketUpdateEvent(ctx context.Context, in *CXTicketUpdateEvent, opts ...grpc.CallOption) (*ProcessCXTicketUpdateEventResponse, error)
	// ProcessTransactionBlocks will consume transaction blocks from the queue and store them in DB
	ProcessRiskSignalEvent(ctx context.Context, in *RiskSignalIngestEvent, opts ...grpc.CallOption) (*RiskSignalIngestResponse, error)
	ProcessRiskAlertEvent(ctx context.Context, in *RiskSignalIngestEvent, opts ...grpc.CallOption) (*RiskSignalIngestResponse, error)
}

type riskCaseManagementConsumerClient struct {
	cc grpc.ClientConnInterface
}

func NewRiskCaseManagementConsumerClient(cc grpc.ClientConnInterface) RiskCaseManagementConsumerClient {
	return &riskCaseManagementConsumerClient{cc}
}

func (c *riskCaseManagementConsumerClient) AddCases(ctx context.Context, in *RiskCasesIngestEvent, opts ...grpc.CallOption) (*AddCasesResponse, error) {
	out := new(AddCasesResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_AddCases_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) AddAlerts(ctx context.Context, in *FrmIngestAlertsEvent, opts ...grpc.CallOption) (*AddAlertsResponse, error) {
	out := new(AddAlertsResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_AddAlerts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessFormSubmission(ctx context.Context, in *FormSubmissionEvent, opts ...grpc.CallOption) (*ProcessFormSubmissionResponse, error) {
	out := new(ProcessFormSubmissionResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessFormSubmission_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessCallRoutingEvent(ctx context.Context, in *event.CallRoutingEvent, opts ...grpc.CallOption) (*ProcessCallRoutingEventResponse, error) {
	out := new(ProcessCallRoutingEventResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessCallRoutingEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessBatchRuleEngineEvent(ctx context.Context, in *BatchRuleEngineEvent, opts ...grpc.CallOption) (*ProcessBatchRuleEngineEventResponse, error) {
	out := new(ProcessBatchRuleEngineEventResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessBatchRuleEngineEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessCXTicketUpdateEvent(ctx context.Context, in *CXTicketUpdateEvent, opts ...grpc.CallOption) (*ProcessCXTicketUpdateEventResponse, error) {
	out := new(ProcessCXTicketUpdateEventResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessCXTicketUpdateEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessRiskSignalEvent(ctx context.Context, in *RiskSignalIngestEvent, opts ...grpc.CallOption) (*RiskSignalIngestResponse, error) {
	out := new(RiskSignalIngestResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessRiskSignalEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *riskCaseManagementConsumerClient) ProcessRiskAlertEvent(ctx context.Context, in *RiskSignalIngestEvent, opts ...grpc.CallOption) (*RiskSignalIngestResponse, error) {
	out := new(RiskSignalIngestResponse)
	err := c.cc.Invoke(ctx, RiskCaseManagementConsumer_ProcessRiskAlertEvent_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RiskCaseManagementConsumerServer is the server API for RiskCaseManagementConsumer service.
// All implementations should embed UnimplementedRiskCaseManagementConsumerServer
// for forward compatibility
type RiskCaseManagementConsumerServer interface {
	AddCases(context.Context, *RiskCasesIngestEvent) (*AddCasesResponse, error)
	// RPC to ingest alerts into case-management from different systems like DS
	// Accepts batched requests and handles errors internally
	AddAlerts(context.Context, *FrmIngestAlertsEvent) (*AddAlertsResponse, error)
	// RPC to process risk form response from user.
	// Processing involves storing response in db and triggering form submission event.
	ProcessFormSubmission(context.Context, *FormSubmissionEvent) (*ProcessFormSubmissionResponse, error)
	// RPC will consume event published by cx call routing service and process risk specific routing events
	// This will take care of risk specific handling needed for a particular routing event
	// Ex: resending comms if user is reaching out via call and we are routing them to a call recording
	ProcessCallRoutingEvent(context.Context, *event.CallRoutingEvent) (*ProcessCallRoutingEventResponse, error)
	// ProcessBatchRuleEngineEvent will consume rule engine event for rule hits.
	// Rule engine periodically stores rule hits in s3 bucket as csv files.
	// New file creation triggers event and event is published to a queue.
	// This will create alerts in case management for all such rule hits.
	ProcessBatchRuleEngineEvent(context.Context, *BatchRuleEngineEvent) (*ProcessBatchRuleEngineEventResponse, error)
	// ProcessCXTicketUpdateEvent will consume cx ticket update event
	ProcessCXTicketUpdateEvent(context.Context, *CXTicketUpdateEvent) (*ProcessCXTicketUpdateEventResponse, error)
	// ProcessTransactionBlocks will consume transaction blocks from the queue and store them in DB
	ProcessRiskSignalEvent(context.Context, *RiskSignalIngestEvent) (*RiskSignalIngestResponse, error)
	ProcessRiskAlertEvent(context.Context, *RiskSignalIngestEvent) (*RiskSignalIngestResponse, error)
}

// UnimplementedRiskCaseManagementConsumerServer should be embedded to have forward compatible implementations.
type UnimplementedRiskCaseManagementConsumerServer struct {
}

func (UnimplementedRiskCaseManagementConsumerServer) AddCases(context.Context, *RiskCasesIngestEvent) (*AddCasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddCases not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) AddAlerts(context.Context, *FrmIngestAlertsEvent) (*AddAlertsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAlerts not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessFormSubmission(context.Context, *FormSubmissionEvent) (*ProcessFormSubmissionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessFormSubmission not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessCallRoutingEvent(context.Context, *event.CallRoutingEvent) (*ProcessCallRoutingEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCallRoutingEvent not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessBatchRuleEngineEvent(context.Context, *BatchRuleEngineEvent) (*ProcessBatchRuleEngineEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessBatchRuleEngineEvent not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessCXTicketUpdateEvent(context.Context, *CXTicketUpdateEvent) (*ProcessCXTicketUpdateEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessCXTicketUpdateEvent not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessRiskSignalEvent(context.Context, *RiskSignalIngestEvent) (*RiskSignalIngestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRiskSignalEvent not implemented")
}
func (UnimplementedRiskCaseManagementConsumerServer) ProcessRiskAlertEvent(context.Context, *RiskSignalIngestEvent) (*RiskSignalIngestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessRiskAlertEvent not implemented")
}

// UnsafeRiskCaseManagementConsumerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RiskCaseManagementConsumerServer will
// result in compilation errors.
type UnsafeRiskCaseManagementConsumerServer interface {
	mustEmbedUnimplementedRiskCaseManagementConsumerServer()
}

func RegisterRiskCaseManagementConsumerServer(s grpc.ServiceRegistrar, srv RiskCaseManagementConsumerServer) {
	s.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv)
}

func _RiskCaseManagementConsumer_AddCases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskCasesIngestEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).AddCases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_AddCases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).AddCases(ctx, req.(*RiskCasesIngestEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_AddAlerts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FrmIngestAlertsEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).AddAlerts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_AddAlerts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).AddAlerts(ctx, req.(*FrmIngestAlertsEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessFormSubmission_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FormSubmissionEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessFormSubmission(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessFormSubmission_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessFormSubmission(ctx, req.(*FormSubmissionEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessCallRoutingEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(event.CallRoutingEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessCallRoutingEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessCallRoutingEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessCallRoutingEvent(ctx, req.(*event.CallRoutingEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessBatchRuleEngineEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchRuleEngineEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessBatchRuleEngineEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessBatchRuleEngineEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessBatchRuleEngineEvent(ctx, req.(*BatchRuleEngineEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessCXTicketUpdateEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CXTicketUpdateEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessCXTicketUpdateEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessCXTicketUpdateEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessCXTicketUpdateEvent(ctx, req.(*CXTicketUpdateEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessRiskSignalEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskSignalIngestEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessRiskSignalEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessRiskSignalEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessRiskSignalEvent(ctx, req.(*RiskSignalIngestEvent))
	}
	return interceptor(ctx, in, info, handler)
}

func _RiskCaseManagementConsumer_ProcessRiskAlertEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RiskSignalIngestEvent)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RiskCaseManagementConsumerServer).ProcessRiskAlertEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RiskCaseManagementConsumer_ProcessRiskAlertEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RiskCaseManagementConsumerServer).ProcessRiskAlertEvent(ctx, req.(*RiskSignalIngestEvent))
	}
	return interceptor(ctx, in, info, handler)
}

// RiskCaseManagementConsumer_ServiceDesc is the grpc.ServiceDesc for RiskCaseManagementConsumer service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RiskCaseManagementConsumer_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "risk.case_management.RiskCaseManagementConsumer",
	HandlerType: (*RiskCaseManagementConsumerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddCases",
			Handler:    _RiskCaseManagementConsumer_AddCases_Handler,
		},
		{
			MethodName: "AddAlerts",
			Handler:    _RiskCaseManagementConsumer_AddAlerts_Handler,
		},
		{
			MethodName: "ProcessFormSubmission",
			Handler:    _RiskCaseManagementConsumer_ProcessFormSubmission_Handler,
		},
		{
			MethodName: "ProcessCallRoutingEvent",
			Handler:    _RiskCaseManagementConsumer_ProcessCallRoutingEvent_Handler,
		},
		{
			MethodName: "ProcessBatchRuleEngineEvent",
			Handler:    _RiskCaseManagementConsumer_ProcessBatchRuleEngineEvent_Handler,
		},
		{
			MethodName: "ProcessCXTicketUpdateEvent",
			Handler:    _RiskCaseManagementConsumer_ProcessCXTicketUpdateEvent_Handler,
		},
		{
			MethodName: "ProcessRiskSignalEvent",
			Handler:    _RiskCaseManagementConsumer_ProcessRiskSignalEvent_Handler,
		},
		{
			MethodName: "ProcessRiskAlertEvent",
			Handler:    _RiskCaseManagementConsumer_ProcessRiskAlertEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/risk/case_management/consumer.proto",
}
