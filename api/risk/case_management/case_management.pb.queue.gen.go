// Code generated by gen_queue_pb. Do not edit
// Source directory : github.com/epifi/gamma/api/risk/case_management
package case_management

import (
	queuePb "github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/queue"
)

const (
	AddCasesMethod                    = "AddCases"
	AddAlertsMethod                   = "AddAlerts"
	ProcessFormSubmissionMethod       = "ProcessFormSubmission"
	ProcessCallRoutingEventMethod     = "ProcessCallRoutingEvent"
	ProcessBatchRuleEngineEventMethod = "ProcessBatchRuleEngineEvent"
	ProcessCXTicketUpdateEventMethod  = "ProcessCXTicketUpdateEvent"
	ProcessRiskSignalEventMethod      = "ProcessRiskSignalEvent"
	ProcessRiskAlertEventMethod       = "ProcessRiskAlertEvent"
)

// Ensure all requests follows the ConsumerRequest interface in future as well
var _ queue.ConsumerRequest = &CXTicketUpdateEvent{}
var _ queue.ConsumerRequest = &RiskCasesIngestEvent{}
var _ queue.ConsumerRequest = &FrmIngestAlertsEvent{}
var _ queue.ConsumerRequest = &FormSubmissionEvent{}
var _ queue.ConsumerRequest = &BatchRuleEngineEvent{}
var _ queue.ConsumerRequest = &RiskSignalIngestEvent{}
var _ queue.ConsumerRequest = &RiskAlertIngestEvent{}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *CXTicketUpdateEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *RiskCasesIngestEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *FrmIngestAlertsEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *FormSubmissionEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *BatchRuleEngineEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *RiskSignalIngestEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// SetRequestHeader is required to set consumer request header information by queue subscribers before calling consumer method.
func (p *RiskAlertIngestEvent) SetRequestHeader(header *queuePb.ConsumerRequestHeader) {
	p.RequestHeader = header
}

// RegisterAddCasesMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterAddCasesMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, AddCasesMethod)
}

// RegisterAddAlertsMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterAddAlertsMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, AddAlertsMethod)
}

// RegisterProcessFormSubmissionMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessFormSubmissionMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessFormSubmissionMethod)
}

// RegisterProcessCallRoutingEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCallRoutingEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessCallRoutingEventMethod)
}

// RegisterProcessBatchRuleEngineEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessBatchRuleEngineEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessBatchRuleEngineEventMethod)
}

// RegisterProcessCXTicketUpdateEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessCXTicketUpdateEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessCXTicketUpdateEventMethod)
}

// RegisterProcessRiskSignalEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRiskSignalEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessRiskSignalEventMethod)
}

// RegisterProcessRiskAlertEventMethodToSubscriber registers a method to queue subscriber.
// The method is invoked upon receiving message from the queue
func RegisterProcessRiskAlertEventMethodToSubscriber(subscriber queue.Subscriber, srv RiskCaseManagementConsumerServer) {
	subscriber.RegisterService(&RiskCaseManagementConsumer_ServiceDesc, srv, ProcessRiskAlertEventMethod)
}
