//go:generate gen_sql -types=PolicyIssuanceRequestStatus,HealthInsurancePolicyType

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/salaryprogram/healthinsurance/policy_issuance_request.proto

package healthinsurance

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PolicyIssuanceRequestStatus int32

const (
	PolicyIssuanceRequestStatus_POLICY_ISSUANCE_REQUEST_STATUS_UNSPECIFIED PolicyIssuanceRequestStatus = 0
	PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED                     PolicyIssuanceRequestStatus = 1
	PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS PolicyIssuanceRequestStatus = 2
	PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL  PolicyIssuanceRequestStatus = 3
	PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_FAILED      PolicyIssuanceRequestStatus = 4
)

// Enum value maps for PolicyIssuanceRequestStatus.
var (
	PolicyIssuanceRequestStatus_name = map[int32]string{
		0: "POLICY_ISSUANCE_REQUEST_STATUS_UNSPECIFIED",
		1: "REQUEST_STATUS_CREATED",
		2: "REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS",
		3: "REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL",
		4: "REQUEST_STATUS_VENDOR_PURCHASE_FAILED",
	}
	PolicyIssuanceRequestStatus_value = map[string]int32{
		"POLICY_ISSUANCE_REQUEST_STATUS_UNSPECIFIED": 0,
		"REQUEST_STATUS_CREATED":                     1,
		"REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS": 2,
		"REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL":  3,
		"REQUEST_STATUS_VENDOR_PURCHASE_FAILED":      4,
	}
)

func (x PolicyIssuanceRequestStatus) Enum() *PolicyIssuanceRequestStatus {
	p := new(PolicyIssuanceRequestStatus)
	*p = x
	return p
}

func (x PolicyIssuanceRequestStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PolicyIssuanceRequestStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[0].Descriptor()
}

func (PolicyIssuanceRequestStatus) Type() protoreflect.EnumType {
	return &file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[0]
}

func (x PolicyIssuanceRequestStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PolicyIssuanceRequestStatus.Descriptor instead.
func (PolicyIssuanceRequestStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescGZIP(), []int{0}
}

type HealthInsurancePolicyIssuanceRequestFieldMask int32

const (
	HealthInsurancePolicyIssuanceRequestFieldMask_POLICY_ISSUANCE_REQUEST_FIELD_MASK_UNSPECIFIED HealthInsurancePolicyIssuanceRequestFieldMask = 0
	HealthInsurancePolicyIssuanceRequestFieldMask_REQUEST_STATUS                                 HealthInsurancePolicyIssuanceRequestFieldMask = 1
)

// Enum value maps for HealthInsurancePolicyIssuanceRequestFieldMask.
var (
	HealthInsurancePolicyIssuanceRequestFieldMask_name = map[int32]string{
		0: "POLICY_ISSUANCE_REQUEST_FIELD_MASK_UNSPECIFIED",
		1: "REQUEST_STATUS",
	}
	HealthInsurancePolicyIssuanceRequestFieldMask_value = map[string]int32{
		"POLICY_ISSUANCE_REQUEST_FIELD_MASK_UNSPECIFIED": 0,
		"REQUEST_STATUS": 1,
	}
)

func (x HealthInsurancePolicyIssuanceRequestFieldMask) Enum() *HealthInsurancePolicyIssuanceRequestFieldMask {
	p := new(HealthInsurancePolicyIssuanceRequestFieldMask)
	*p = x
	return p
}

func (x HealthInsurancePolicyIssuanceRequestFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthInsurancePolicyIssuanceRequestFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[1].Descriptor()
}

func (HealthInsurancePolicyIssuanceRequestFieldMask) Type() protoreflect.EnumType {
	return &file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[1]
}

func (x HealthInsurancePolicyIssuanceRequestFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthInsurancePolicyIssuanceRequestFieldMask.Descriptor instead.
func (HealthInsurancePolicyIssuanceRequestFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescGZIP(), []int{1}
}

// https://docs.google.com/spreadsheets/d/1ArS49J91TfqFLbw2-U32FfF1rj6wE57XHfyKNLV1Jfw/edit?gid=0#gid=0 for updated policies
// Steps to follow to add a new policy:
// - update enum in api/typesv2/salaryprogram/health_insurance.proto
// - create reward offer for the newly added policy. check 95711cae-a985-47df-9f80-c27de9a8059f reward offer for referance
// - handle new enums in FE and BE RPCs
// - update salaryprogram/healthinsurance/service.go -> getVendorBasedOnUserEmployerChannelAndPolicyType method for every newly added health insruance policy
type HealthInsurancePolicyType int32

const (
	HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED HealthInsurancePolicyType = 0
	HealthInsurancePolicyType_BASE_HEALTH_INSURANCE                    HealthInsurancePolicyType = 1
	HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE                   HealthInsurancePolicyType = 2
	HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C               HealthInsurancePolicyType = 3
	HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A                 HealthInsurancePolicyType = 4
	HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A                 HealthInsurancePolicyType = 5
	HealthInsurancePolicyType_ONSURITY_DIAMOND_PLUS_1A                 HealthInsurancePolicyType = 6
	// GPA - Group Personal Accident insurance
	// GHI - Group Health Insurance
	HealthInsurancePolicyType_ONSURITY_GHI_GPA_RUBY_1A HealthInsurancePolicyType = 7
	HealthInsurancePolicyType_ONSURITY_GHI_GPA_OPAL_1A HealthInsurancePolicyType = 8
)

// Enum value maps for HealthInsurancePolicyType.
var (
	HealthInsurancePolicyType_name = map[int32]string{
		0: "HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED",
		1: "BASE_HEALTH_INSURANCE",
		2: "SUPER_TOP_UP_INSURANCE",
		3: "ONSURITY_OPD_WELLNESS_2A2C",
		4: "ONSURITY_OPD_WELLNESS_2A",
		5: "ONSURITY_OPD_WELLNESS_1A",
		6: "ONSURITY_DIAMOND_PLUS_1A",
		7: "ONSURITY_GHI_GPA_RUBY_1A",
		8: "ONSURITY_GHI_GPA_OPAL_1A",
	}
	HealthInsurancePolicyType_value = map[string]int32{
		"HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED": 0,
		"BASE_HEALTH_INSURANCE":                    1,
		"SUPER_TOP_UP_INSURANCE":                   2,
		"ONSURITY_OPD_WELLNESS_2A2C":               3,
		"ONSURITY_OPD_WELLNESS_2A":                 4,
		"ONSURITY_OPD_WELLNESS_1A":                 5,
		"ONSURITY_DIAMOND_PLUS_1A":                 6,
		"ONSURITY_GHI_GPA_RUBY_1A":                 7,
		"ONSURITY_GHI_GPA_OPAL_1A":                 8,
	}
)

func (x HealthInsurancePolicyType) Enum() *HealthInsurancePolicyType {
	p := new(HealthInsurancePolicyType)
	*p = x
	return p
}

func (x HealthInsurancePolicyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthInsurancePolicyType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[2].Descriptor()
}

func (HealthInsurancePolicyType) Type() protoreflect.EnumType {
	return &file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes[2]
}

func (x HealthInsurancePolicyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthInsurancePolicyType.Descriptor instead.
func (HealthInsurancePolicyType) EnumDescriptor() ([]byte, []int) {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescGZIP(), []int{2}
}

// HealthInsurancePolicyIssuanceRequest stores the details of request needed for initiating a health insurance policy purchase.
type HealthInsurancePolicyIssuanceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// unique id of issuance request.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// denotes the actor for whom request was created.
	ActorId string `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	// denotes the vendor who would be fulfilling the policy request.
	PolicyVendor vendorgateway.Vendor `protobuf:"varint,3,opt,name=policy_vendor,json=policyVendor,proto3,enum=vendorgateway.Vendor" json:"policy_vendor,omitempty"`
	// denotes the unique request id passed to vendor while purchasing the policy.
	VendorRequestId string `protobuf:"bytes,4,opt,name=vendor_request_id,json=vendorRequestId,proto3" json:"vendor_request_id,omitempty"`
	// denotes the status of issuance request.
	RequestStatus PolicyIssuanceRequestStatus `protobuf:"varint,5,opt,name=request_status,json=requestStatus,proto3,enum=salaryprogram.healthinsurance.PolicyIssuanceRequestStatus" json:"request_status,omitempty"`
	// denotes the time until which the policy issuance request is valid,
	// if the callback for policy purchase confirmation comes after this time then it should be rejected.
	RequestExpiresAt *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=request_expires_at,json=requestExpiresAt,proto3" json:"request_expires_at,omitempty"`
	// Enum denotes the policy type of health insurance for which request is created.
	PolicyType HealthInsurancePolicyType `protobuf:"varint,7,opt,name=policy_type,json=policyType,proto3,enum=salaryprogram.healthinsurance.HealthInsurancePolicyType" json:"policy_type,omitempty"`
	CreatedAt  *timestamppb.Timestamp    `protobuf:"bytes,14,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt  *timestamppb.Timestamp    `protobuf:"bytes,15,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
}

func (x *HealthInsurancePolicyIssuanceRequest) Reset() {
	*x = HealthInsurancePolicyIssuanceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthInsurancePolicyIssuanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthInsurancePolicyIssuanceRequest) ProtoMessage() {}

func (x *HealthInsurancePolicyIssuanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthInsurancePolicyIssuanceRequest.ProtoReflect.Descriptor instead.
func (*HealthInsurancePolicyIssuanceRequest) Descriptor() ([]byte, []int) {
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescGZIP(), []int{0}
}

func (x *HealthInsurancePolicyIssuanceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *HealthInsurancePolicyIssuanceRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *HealthInsurancePolicyIssuanceRequest) GetPolicyVendor() vendorgateway.Vendor {
	if x != nil {
		return x.PolicyVendor
	}
	return vendorgateway.Vendor(0)
}

func (x *HealthInsurancePolicyIssuanceRequest) GetVendorRequestId() string {
	if x != nil {
		return x.VendorRequestId
	}
	return ""
}

func (x *HealthInsurancePolicyIssuanceRequest) GetRequestStatus() PolicyIssuanceRequestStatus {
	if x != nil {
		return x.RequestStatus
	}
	return PolicyIssuanceRequestStatus_POLICY_ISSUANCE_REQUEST_STATUS_UNSPECIFIED
}

func (x *HealthInsurancePolicyIssuanceRequest) GetRequestExpiresAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RequestExpiresAt
	}
	return nil
}

func (x *HealthInsurancePolicyIssuanceRequest) GetPolicyType() HealthInsurancePolicyType {
	if x != nil {
		return x.PolicyType
	}
	return HealthInsurancePolicyType_HEALTH_INSURANCE_POLICY_TYPE_UNSPECIFIED
}

func (x *HealthInsurancePolicyIssuanceRequest) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *HealthInsurancePolicyIssuanceRequest) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_api_salaryprogram_healthinsurance_policy_issuance_request_proto protoreflect.FileDescriptor

var file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDesc = []byte{
	0x0a, 0x3f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67,
	0x72, 0x61, 0x6d, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61,
	0x6e, 0x63, 0x65, 0x2f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x69, 0x73, 0x73, 0x75, 0x61,
	0x6e, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x1d, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65,
	0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0xb7, 0x04, 0x0a, 0x24, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x73, 0x73, 0x75, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x3a, 0x0a, 0x0d, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x52, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x56, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x12, 0x2a, 0x0a, 0x11, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x61, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72,
	0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x69, 0x6e, 0x73, 0x75,
	0x72, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x73, 0x73, 0x75,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x48, 0x0a, 0x12, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x73, 0x5f, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x73, 0x41, 0x74, 0x12, 0x59, 0x0a, 0x0b, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x38, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x2e,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x61, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x2a, 0xf3, 0x01, 0x0a, 0x1b,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x73, 0x73, 0x75, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x2a, 0x50,
	0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x49, 0x53, 0x53, 0x55, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2e, 0x0a, 0x2a, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f,
	0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x2d, 0x0a, 0x29, 0x52, 0x45, 0x51, 0x55, 0x45,
	0x53, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52,
	0x5f, 0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53,
	0x53, 0x46, 0x55, 0x4c, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f,
	0x50, 0x55, 0x52, 0x43, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x04, 0x2a, 0x77, 0x0a, 0x2d, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x75, 0x72,
	0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x73, 0x73, 0x75, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x12, 0x32, 0x0a, 0x2e, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x49, 0x53, 0x53,
	0x55, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x5f, 0x46, 0x49,
	0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x51, 0x55, 0x45, 0x53,
	0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x01, 0x2a, 0xb6, 0x02, 0x0a, 0x19, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x49, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x28, 0x48, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x4c,
	0x49, 0x43, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x42, 0x41, 0x53, 0x45, 0x5f, 0x48,
	0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x10,
	0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x55, 0x50, 0x45, 0x52, 0x5f, 0x54, 0x4f, 0x50, 0x5f, 0x55,
	0x50, 0x5f, 0x49, 0x4e, 0x53, 0x55, 0x52, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x02, 0x12, 0x1e, 0x0a,
	0x1a, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x50, 0x44, 0x5f, 0x57, 0x45,
	0x4c, 0x4c, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x32, 0x41, 0x32, 0x43, 0x10, 0x03, 0x12, 0x1c, 0x0a,
	0x18, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x50, 0x44, 0x5f, 0x57, 0x45,
	0x4c, 0x4c, 0x4e, 0x45, 0x53, 0x53, 0x5f, 0x32, 0x41, 0x10, 0x04, 0x12, 0x1c, 0x0a, 0x18, 0x4f,
	0x4e, 0x53, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x4f, 0x50, 0x44, 0x5f, 0x57, 0x45, 0x4c, 0x4c,
	0x4e, 0x45, 0x53, 0x53, 0x5f, 0x31, 0x41, 0x10, 0x05, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x4e, 0x53,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x44, 0x49, 0x41, 0x4d, 0x4f, 0x4e, 0x44, 0x5f, 0x50, 0x4c,
	0x55, 0x53, 0x5f, 0x31, 0x41, 0x10, 0x06, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x4e, 0x53, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x47, 0x48, 0x49, 0x5f, 0x47, 0x50, 0x41, 0x5f, 0x52, 0x55, 0x42, 0x59,
	0x5f, 0x31, 0x41, 0x10, 0x07, 0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x4e, 0x53, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x47, 0x48, 0x49, 0x5f, 0x47, 0x50, 0x41, 0x5f, 0x4f, 0x50, 0x41, 0x4c, 0x5f, 0x31,
	0x41, 0x10, 0x08, 0x42, 0x74, 0x0a, 0x38, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x61, 0x6c, 0x61, 0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2e,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x5a,
	0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66,
	0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x61, 0x6c, 0x61,
	0x72, 0x79, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x2f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x69, 0x6e, 0x73, 0x75, 0x72, 0x61, 0x6e, 0x63, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescOnce sync.Once
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescData = file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDesc
)

func file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescGZIP() []byte {
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescOnce.Do(func() {
		file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescData)
	})
	return file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDescData
}

var file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_goTypes = []interface{}{
	(PolicyIssuanceRequestStatus)(0),                   // 0: salaryprogram.healthinsurance.PolicyIssuanceRequestStatus
	(HealthInsurancePolicyIssuanceRequestFieldMask)(0), // 1: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequestFieldMask
	(HealthInsurancePolicyType)(0),                     // 2: salaryprogram.healthinsurance.HealthInsurancePolicyType
	(*HealthInsurancePolicyIssuanceRequest)(nil),       // 3: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest
	(vendorgateway.Vendor)(0),                          // 4: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil),                      // 5: google.protobuf.Timestamp
}
var file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_depIdxs = []int32{
	4, // 0: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.policy_vendor:type_name -> vendorgateway.Vendor
	0, // 1: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.request_status:type_name -> salaryprogram.healthinsurance.PolicyIssuanceRequestStatus
	5, // 2: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.request_expires_at:type_name -> google.protobuf.Timestamp
	2, // 3: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.policy_type:type_name -> salaryprogram.healthinsurance.HealthInsurancePolicyType
	5, // 4: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.created_at:type_name -> google.protobuf.Timestamp
	5, // 5: salaryprogram.healthinsurance.HealthInsurancePolicyIssuanceRequest.updated_at:type_name -> google.protobuf.Timestamp
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_init() }
func file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_init() {
	if File_api_salaryprogram_healthinsurance_policy_issuance_request_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthInsurancePolicyIssuanceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_goTypes,
		DependencyIndexes: file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_depIdxs,
		EnumInfos:         file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_enumTypes,
		MessageInfos:      file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_msgTypes,
	}.Build()
	File_api_salaryprogram_healthinsurance_policy_issuance_request_proto = out.File
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_rawDesc = nil
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_goTypes = nil
	file_api_salaryprogram_healthinsurance_policy_issuance_request_proto_depIdxs = nil
}
