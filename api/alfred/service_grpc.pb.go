// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/alfred/service.proto

package alfred

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Alfred_ProvisionNewRequest_FullMethodName        = "/alfred.Alfred/ProvisionNewRequest"
	Alfred_PollRequestStatus_FullMethodName          = "/alfred.Alfred/PollRequestStatus"
	Alfred_GetRequestStatusDetails_FullMethodName    = "/alfred.Alfred/GetRequestStatusDetails"
	Alfred_GetAllRequestStatusDetails_FullMethodName = "/alfred.Alfred/GetAllRequestStatusDetails"
	Alfred_UpdateUserInput_FullMethodName            = "/alfred.Alfred/UpdateUserInput"
	Alfred_SoftDeleteServiceRequest_FullMethodName   = "/alfred.Alfred/SoftDeleteServiceRequest"
	Alfred_IsEligibleForRequest_FullMethodName       = "/alfred.Alfred/IsEligibleForRequest"
)

// AlfredClient is the client API for Alfred service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AlfredClient interface {
	// ProvisionNewRequest rpc takes in actor identifier and request type to provision new request.
	// It assigns a request processor mapped to the request type, which takes actions to provision new request
	ProvisionNewRequest(ctx context.Context, in *ProvisionNewRequestRequest, opts ...grpc.CallOption) (*ProvisionNewRequestResponse, error)
	// PollRequestStatus rpc takes in request identifier which it received in the
	// screen options from generic polling screen
	PollRequestStatus(ctx context.Context, in *PollRequestStatusRequest, opts ...grpc.CallOption) (*PollRequestStatusResponse, error)
	// GetRequestStatusDetails will get a latest non-deleted unique request, sync for updates and returns details
	GetRequestStatusDetails(ctx context.Context, in *GetRequestStatusDetailsRequest, opts ...grpc.CallOption) (*GetRequestStatusDetailsResponse, error)
	// GetAllRequestStatusDetails will fetch records from Alfred Db and run getRequestStatusDetails if any request status is in non-terminal state.
	GetAllRequestStatusDetails(ctx context.Context, in *GetAllRequestStatusDetailsRequest, opts ...grpc.CallOption) (*GetAllRequestStatusDetailsResponse, error)
	// UpdateUserInput stores input received from user in the process of service request
	// Usually, these are temporary data taken from user more tied to the service request rather than the corresponding domain service
	UpdateUserInput(ctx context.Context, in *UpdateUserInputRequest, opts ...grpc.CallOption) (*UpdateUserInputResponse, error)
	// rpc to soft delete service request from db.
	// response is the ack for the soft delete operation
	// rpc is written to be used in non-prod env
	SoftDeleteServiceRequest(ctx context.Context, in *SoftDeleteServiceReqRequest, opts ...grpc.CallOption) (*SoftDeleteServiceReqReqResponse, error)
	// IsEligibleForRequest checks if an actor is eligible for a specific request type
	// Returns eligibility status and optional next action deeplink if not eligible
	IsEligibleForRequest(ctx context.Context, in *IsEligibleForRequestRequest, opts ...grpc.CallOption) (*IsEligibleForRequestResponse, error)
}

type alfredClient struct {
	cc grpc.ClientConnInterface
}

func NewAlfredClient(cc grpc.ClientConnInterface) AlfredClient {
	return &alfredClient{cc}
}

func (c *alfredClient) ProvisionNewRequest(ctx context.Context, in *ProvisionNewRequestRequest, opts ...grpc.CallOption) (*ProvisionNewRequestResponse, error) {
	out := new(ProvisionNewRequestResponse)
	err := c.cc.Invoke(ctx, Alfred_ProvisionNewRequest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) PollRequestStatus(ctx context.Context, in *PollRequestStatusRequest, opts ...grpc.CallOption) (*PollRequestStatusResponse, error) {
	out := new(PollRequestStatusResponse)
	err := c.cc.Invoke(ctx, Alfred_PollRequestStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) GetRequestStatusDetails(ctx context.Context, in *GetRequestStatusDetailsRequest, opts ...grpc.CallOption) (*GetRequestStatusDetailsResponse, error) {
	out := new(GetRequestStatusDetailsResponse)
	err := c.cc.Invoke(ctx, Alfred_GetRequestStatusDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) GetAllRequestStatusDetails(ctx context.Context, in *GetAllRequestStatusDetailsRequest, opts ...grpc.CallOption) (*GetAllRequestStatusDetailsResponse, error) {
	out := new(GetAllRequestStatusDetailsResponse)
	err := c.cc.Invoke(ctx, Alfred_GetAllRequestStatusDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) UpdateUserInput(ctx context.Context, in *UpdateUserInputRequest, opts ...grpc.CallOption) (*UpdateUserInputResponse, error) {
	out := new(UpdateUserInputResponse)
	err := c.cc.Invoke(ctx, Alfred_UpdateUserInput_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) SoftDeleteServiceRequest(ctx context.Context, in *SoftDeleteServiceReqRequest, opts ...grpc.CallOption) (*SoftDeleteServiceReqReqResponse, error) {
	out := new(SoftDeleteServiceReqReqResponse)
	err := c.cc.Invoke(ctx, Alfred_SoftDeleteServiceRequest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *alfredClient) IsEligibleForRequest(ctx context.Context, in *IsEligibleForRequestRequest, opts ...grpc.CallOption) (*IsEligibleForRequestResponse, error) {
	out := new(IsEligibleForRequestResponse)
	err := c.cc.Invoke(ctx, Alfred_IsEligibleForRequest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AlfredServer is the server API for Alfred service.
// All implementations should embed UnimplementedAlfredServer
// for forward compatibility
type AlfredServer interface {
	// ProvisionNewRequest rpc takes in actor identifier and request type to provision new request.
	// It assigns a request processor mapped to the request type, which takes actions to provision new request
	ProvisionNewRequest(context.Context, *ProvisionNewRequestRequest) (*ProvisionNewRequestResponse, error)
	// PollRequestStatus rpc takes in request identifier which it received in the
	// screen options from generic polling screen
	PollRequestStatus(context.Context, *PollRequestStatusRequest) (*PollRequestStatusResponse, error)
	// GetRequestStatusDetails will get a latest non-deleted unique request, sync for updates and returns details
	GetRequestStatusDetails(context.Context, *GetRequestStatusDetailsRequest) (*GetRequestStatusDetailsResponse, error)
	// GetAllRequestStatusDetails will fetch records from Alfred Db and run getRequestStatusDetails if any request status is in non-terminal state.
	GetAllRequestStatusDetails(context.Context, *GetAllRequestStatusDetailsRequest) (*GetAllRequestStatusDetailsResponse, error)
	// UpdateUserInput stores input received from user in the process of service request
	// Usually, these are temporary data taken from user more tied to the service request rather than the corresponding domain service
	UpdateUserInput(context.Context, *UpdateUserInputRequest) (*UpdateUserInputResponse, error)
	// rpc to soft delete service request from db.
	// response is the ack for the soft delete operation
	// rpc is written to be used in non-prod env
	SoftDeleteServiceRequest(context.Context, *SoftDeleteServiceReqRequest) (*SoftDeleteServiceReqReqResponse, error)
	// IsEligibleForRequest checks if an actor is eligible for a specific request type
	// Returns eligibility status and optional next action deeplink if not eligible
	IsEligibleForRequest(context.Context, *IsEligibleForRequestRequest) (*IsEligibleForRequestResponse, error)
}

// UnimplementedAlfredServer should be embedded to have forward compatible implementations.
type UnimplementedAlfredServer struct {
}

func (UnimplementedAlfredServer) ProvisionNewRequest(context.Context, *ProvisionNewRequestRequest) (*ProvisionNewRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProvisionNewRequest not implemented")
}
func (UnimplementedAlfredServer) PollRequestStatus(context.Context, *PollRequestStatusRequest) (*PollRequestStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PollRequestStatus not implemented")
}
func (UnimplementedAlfredServer) GetRequestStatusDetails(context.Context, *GetRequestStatusDetailsRequest) (*GetRequestStatusDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRequestStatusDetails not implemented")
}
func (UnimplementedAlfredServer) GetAllRequestStatusDetails(context.Context, *GetAllRequestStatusDetailsRequest) (*GetAllRequestStatusDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllRequestStatusDetails not implemented")
}
func (UnimplementedAlfredServer) UpdateUserInput(context.Context, *UpdateUserInputRequest) (*UpdateUserInputResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateUserInput not implemented")
}
func (UnimplementedAlfredServer) SoftDeleteServiceRequest(context.Context, *SoftDeleteServiceReqRequest) (*SoftDeleteServiceReqReqResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SoftDeleteServiceRequest not implemented")
}
func (UnimplementedAlfredServer) IsEligibleForRequest(context.Context, *IsEligibleForRequestRequest) (*IsEligibleForRequestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsEligibleForRequest not implemented")
}

// UnsafeAlfredServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AlfredServer will
// result in compilation errors.
type UnsafeAlfredServer interface {
	mustEmbedUnimplementedAlfredServer()
}

func RegisterAlfredServer(s grpc.ServiceRegistrar, srv AlfredServer) {
	s.RegisterService(&Alfred_ServiceDesc, srv)
}

func _Alfred_ProvisionNewRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProvisionNewRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).ProvisionNewRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_ProvisionNewRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).ProvisionNewRequest(ctx, req.(*ProvisionNewRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_PollRequestStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PollRequestStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).PollRequestStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_PollRequestStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).PollRequestStatus(ctx, req.(*PollRequestStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_GetRequestStatusDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRequestStatusDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).GetRequestStatusDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_GetRequestStatusDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).GetRequestStatusDetails(ctx, req.(*GetRequestStatusDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_GetAllRequestStatusDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllRequestStatusDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).GetAllRequestStatusDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_GetAllRequestStatusDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).GetAllRequestStatusDetails(ctx, req.(*GetAllRequestStatusDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_UpdateUserInput_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateUserInputRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).UpdateUserInput(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_UpdateUserInput_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).UpdateUserInput(ctx, req.(*UpdateUserInputRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_SoftDeleteServiceRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SoftDeleteServiceReqRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).SoftDeleteServiceRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_SoftDeleteServiceRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).SoftDeleteServiceRequest(ctx, req.(*SoftDeleteServiceReqRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Alfred_IsEligibleForRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IsEligibleForRequestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AlfredServer).IsEligibleForRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Alfred_IsEligibleForRequest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AlfredServer).IsEligibleForRequest(ctx, req.(*IsEligibleForRequestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Alfred_ServiceDesc is the grpc.ServiceDesc for Alfred service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Alfred_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "alfred.Alfred",
	HandlerType: (*AlfredServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProvisionNewRequest",
			Handler:    _Alfred_ProvisionNewRequest_Handler,
		},
		{
			MethodName: "PollRequestStatus",
			Handler:    _Alfred_PollRequestStatus_Handler,
		},
		{
			MethodName: "GetRequestStatusDetails",
			Handler:    _Alfred_GetRequestStatusDetails_Handler,
		},
		{
			MethodName: "GetAllRequestStatusDetails",
			Handler:    _Alfred_GetAllRequestStatusDetails_Handler,
		},
		{
			MethodName: "UpdateUserInput",
			Handler:    _Alfred_UpdateUserInput_Handler,
		},
		{
			MethodName: "SoftDeleteServiceRequest",
			Handler:    _Alfred_SoftDeleteServiceRequest_Handler,
		},
		{
			MethodName: "IsEligibleForRequest",
			Handler:    _Alfred_IsEligibleForRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/alfred/service.proto",
}
