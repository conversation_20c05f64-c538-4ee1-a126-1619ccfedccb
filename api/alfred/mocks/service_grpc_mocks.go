// Code generated by MockGen. DO NOT EDIT.
// Source: api/alfred/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	alfred "github.com/epifi/gamma/api/alfred"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAlfredClient is a mock of AlfredClient interface.
type MockAlfredClient struct {
	ctrl     *gomock.Controller
	recorder *MockAlfredClientMockRecorder
}

// MockAlfredClientMockRecorder is the mock recorder for MockAlfredClient.
type MockAlfredClientMockRecorder struct {
	mock *MockAlfredClient
}

// NewMockAlfredClient creates a new mock instance.
func NewMockAlfredClient(ctrl *gomock.Controller) *MockAlfredClient {
	mock := &MockAlfredClient{ctrl: ctrl}
	mock.recorder = &MockAlfredClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlfredClient) EXPECT() *MockAlfredClientMockRecorder {
	return m.recorder
}

// GetAllRequestStatusDetails mocks base method.
func (m *MockAlfredClient) GetAllRequestStatusDetails(ctx context.Context, in *alfred.GetAllRequestStatusDetailsRequest, opts ...grpc.CallOption) (*alfred.GetAllRequestStatusDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAllRequestStatusDetails", varargs...)
	ret0, _ := ret[0].(*alfred.GetAllRequestStatusDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRequestStatusDetails indicates an expected call of GetAllRequestStatusDetails.
func (mr *MockAlfredClientMockRecorder) GetAllRequestStatusDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRequestStatusDetails", reflect.TypeOf((*MockAlfredClient)(nil).GetAllRequestStatusDetails), varargs...)
}

// GetRequestStatusDetails mocks base method.
func (m *MockAlfredClient) GetRequestStatusDetails(ctx context.Context, in *alfred.GetRequestStatusDetailsRequest, opts ...grpc.CallOption) (*alfred.GetRequestStatusDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRequestStatusDetails", varargs...)
	ret0, _ := ret[0].(*alfred.GetRequestStatusDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatusDetails indicates an expected call of GetRequestStatusDetails.
func (mr *MockAlfredClientMockRecorder) GetRequestStatusDetails(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatusDetails", reflect.TypeOf((*MockAlfredClient)(nil).GetRequestStatusDetails), varargs...)
}

// IsEligibleForRequest mocks base method.
func (m *MockAlfredClient) IsEligibleForRequest(ctx context.Context, in *alfred.IsEligibleForRequestRequest, opts ...grpc.CallOption) (*alfred.IsEligibleForRequestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsEligibleForRequest", varargs...)
	ret0, _ := ret[0].(*alfred.IsEligibleForRequestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsEligibleForRequest indicates an expected call of IsEligibleForRequest.
func (mr *MockAlfredClientMockRecorder) IsEligibleForRequest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEligibleForRequest", reflect.TypeOf((*MockAlfredClient)(nil).IsEligibleForRequest), varargs...)
}

// PollRequestStatus mocks base method.
func (m *MockAlfredClient) PollRequestStatus(ctx context.Context, in *alfred.PollRequestStatusRequest, opts ...grpc.CallOption) (*alfred.PollRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PollRequestStatus", varargs...)
	ret0, _ := ret[0].(*alfred.PollRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollRequestStatus indicates an expected call of PollRequestStatus.
func (mr *MockAlfredClientMockRecorder) PollRequestStatus(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollRequestStatus", reflect.TypeOf((*MockAlfredClient)(nil).PollRequestStatus), varargs...)
}

// ProvisionNewRequest mocks base method.
func (m *MockAlfredClient) ProvisionNewRequest(ctx context.Context, in *alfred.ProvisionNewRequestRequest, opts ...grpc.CallOption) (*alfred.ProvisionNewRequestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ProvisionNewRequest", varargs...)
	ret0, _ := ret[0].(*alfred.ProvisionNewRequestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvisionNewRequest indicates an expected call of ProvisionNewRequest.
func (mr *MockAlfredClientMockRecorder) ProvisionNewRequest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionNewRequest", reflect.TypeOf((*MockAlfredClient)(nil).ProvisionNewRequest), varargs...)
}

// SoftDeleteServiceRequest mocks base method.
func (m *MockAlfredClient) SoftDeleteServiceRequest(ctx context.Context, in *alfred.SoftDeleteServiceReqRequest, opts ...grpc.CallOption) (*alfred.SoftDeleteServiceReqReqResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SoftDeleteServiceRequest", varargs...)
	ret0, _ := ret[0].(*alfred.SoftDeleteServiceReqReqResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SoftDeleteServiceRequest indicates an expected call of SoftDeleteServiceRequest.
func (mr *MockAlfredClientMockRecorder) SoftDeleteServiceRequest(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SoftDeleteServiceRequest", reflect.TypeOf((*MockAlfredClient)(nil).SoftDeleteServiceRequest), varargs...)
}

// UpdateUserInput mocks base method.
func (m *MockAlfredClient) UpdateUserInput(ctx context.Context, in *alfred.UpdateUserInputRequest, opts ...grpc.CallOption) (*alfred.UpdateUserInputResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateUserInput", varargs...)
	ret0, _ := ret[0].(*alfred.UpdateUserInputResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserInput indicates an expected call of UpdateUserInput.
func (mr *MockAlfredClientMockRecorder) UpdateUserInput(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserInput", reflect.TypeOf((*MockAlfredClient)(nil).UpdateUserInput), varargs...)
}

// MockAlfredServer is a mock of AlfredServer interface.
type MockAlfredServer struct {
	ctrl     *gomock.Controller
	recorder *MockAlfredServerMockRecorder
}

// MockAlfredServerMockRecorder is the mock recorder for MockAlfredServer.
type MockAlfredServerMockRecorder struct {
	mock *MockAlfredServer
}

// NewMockAlfredServer creates a new mock instance.
func NewMockAlfredServer(ctrl *gomock.Controller) *MockAlfredServer {
	mock := &MockAlfredServer{ctrl: ctrl}
	mock.recorder = &MockAlfredServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAlfredServer) EXPECT() *MockAlfredServerMockRecorder {
	return m.recorder
}

// GetAllRequestStatusDetails mocks base method.
func (m *MockAlfredServer) GetAllRequestStatusDetails(arg0 context.Context, arg1 *alfred.GetAllRequestStatusDetailsRequest) (*alfred.GetAllRequestStatusDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAllRequestStatusDetails", arg0, arg1)
	ret0, _ := ret[0].(*alfred.GetAllRequestStatusDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAllRequestStatusDetails indicates an expected call of GetAllRequestStatusDetails.
func (mr *MockAlfredServerMockRecorder) GetAllRequestStatusDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAllRequestStatusDetails", reflect.TypeOf((*MockAlfredServer)(nil).GetAllRequestStatusDetails), arg0, arg1)
}

// GetRequestStatusDetails mocks base method.
func (m *MockAlfredServer) GetRequestStatusDetails(arg0 context.Context, arg1 *alfred.GetRequestStatusDetailsRequest) (*alfred.GetRequestStatusDetailsResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRequestStatusDetails", arg0, arg1)
	ret0, _ := ret[0].(*alfred.GetRequestStatusDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRequestStatusDetails indicates an expected call of GetRequestStatusDetails.
func (mr *MockAlfredServerMockRecorder) GetRequestStatusDetails(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRequestStatusDetails", reflect.TypeOf((*MockAlfredServer)(nil).GetRequestStatusDetails), arg0, arg1)
}

// IsEligibleForRequest mocks base method.
func (m *MockAlfredServer) IsEligibleForRequest(arg0 context.Context, arg1 *alfred.IsEligibleForRequestRequest) (*alfred.IsEligibleForRequestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsEligibleForRequest", arg0, arg1)
	ret0, _ := ret[0].(*alfred.IsEligibleForRequestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsEligibleForRequest indicates an expected call of IsEligibleForRequest.
func (mr *MockAlfredServerMockRecorder) IsEligibleForRequest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsEligibleForRequest", reflect.TypeOf((*MockAlfredServer)(nil).IsEligibleForRequest), arg0, arg1)
}

// PollRequestStatus mocks base method.
func (m *MockAlfredServer) PollRequestStatus(arg0 context.Context, arg1 *alfred.PollRequestStatusRequest) (*alfred.PollRequestStatusResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PollRequestStatus", arg0, arg1)
	ret0, _ := ret[0].(*alfred.PollRequestStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PollRequestStatus indicates an expected call of PollRequestStatus.
func (mr *MockAlfredServerMockRecorder) PollRequestStatus(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PollRequestStatus", reflect.TypeOf((*MockAlfredServer)(nil).PollRequestStatus), arg0, arg1)
}

// ProvisionNewRequest mocks base method.
func (m *MockAlfredServer) ProvisionNewRequest(arg0 context.Context, arg1 *alfred.ProvisionNewRequestRequest) (*alfred.ProvisionNewRequestResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProvisionNewRequest", arg0, arg1)
	ret0, _ := ret[0].(*alfred.ProvisionNewRequestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProvisionNewRequest indicates an expected call of ProvisionNewRequest.
func (mr *MockAlfredServerMockRecorder) ProvisionNewRequest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProvisionNewRequest", reflect.TypeOf((*MockAlfredServer)(nil).ProvisionNewRequest), arg0, arg1)
}

// SoftDeleteServiceRequest mocks base method.
func (m *MockAlfredServer) SoftDeleteServiceRequest(arg0 context.Context, arg1 *alfred.SoftDeleteServiceReqRequest) (*alfred.SoftDeleteServiceReqReqResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SoftDeleteServiceRequest", arg0, arg1)
	ret0, _ := ret[0].(*alfred.SoftDeleteServiceReqReqResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SoftDeleteServiceRequest indicates an expected call of SoftDeleteServiceRequest.
func (mr *MockAlfredServerMockRecorder) SoftDeleteServiceRequest(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SoftDeleteServiceRequest", reflect.TypeOf((*MockAlfredServer)(nil).SoftDeleteServiceRequest), arg0, arg1)
}

// UpdateUserInput mocks base method.
func (m *MockAlfredServer) UpdateUserInput(arg0 context.Context, arg1 *alfred.UpdateUserInputRequest) (*alfred.UpdateUserInputResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserInput", arg0, arg1)
	ret0, _ := ret[0].(*alfred.UpdateUserInputResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserInput indicates an expected call of UpdateUserInput.
func (mr *MockAlfredServerMockRecorder) UpdateUserInput(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserInput", reflect.TypeOf((*MockAlfredServer)(nil).UpdateUserInput), arg0, arg1)
}

// MockUnsafeAlfredServer is a mock of UnsafeAlfredServer interface.
type MockUnsafeAlfredServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeAlfredServerMockRecorder
}

// MockUnsafeAlfredServerMockRecorder is the mock recorder for MockUnsafeAlfredServer.
type MockUnsafeAlfredServerMockRecorder struct {
	mock *MockUnsafeAlfredServer
}

// NewMockUnsafeAlfredServer creates a new mock instance.
func NewMockUnsafeAlfredServer(ctrl *gomock.Controller) *MockUnsafeAlfredServer {
	mock := &MockUnsafeAlfredServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeAlfredServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeAlfredServer) EXPECT() *MockUnsafeAlfredServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedAlfredServer mocks base method.
func (m *MockUnsafeAlfredServer) mustEmbedUnimplementedAlfredServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedAlfredServer")
}

// mustEmbedUnimplementedAlfredServer indicates an expected call of mustEmbedUnimplementedAlfredServer.
func (mr *MockUnsafeAlfredServerMockRecorder) mustEmbedUnimplementedAlfredServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedAlfredServer", reflect.TypeOf((*MockUnsafeAlfredServer)(nil).mustEmbedUnimplementedAlfredServer))
}
