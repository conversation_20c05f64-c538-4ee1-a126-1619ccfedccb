// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/alfred/service.proto

package alfred

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ProvisionNewRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProvisionNewRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProvisionNewRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProvisionNewRequestRequestMultiError, or nil if none found.
func (m *ProvisionNewRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ProvisionNewRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	// no validation rules for RequestType

	// no validation rules for Blob

	if len(errors) > 0 {
		return ProvisionNewRequestRequestMultiError(errors)
	}

	return nil
}

// ProvisionNewRequestRequestMultiError is an error wrapping multiple
// validation errors returned by ProvisionNewRequestRequest.ValidateAll() if
// the designated constraints aren't met.
type ProvisionNewRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProvisionNewRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProvisionNewRequestRequestMultiError) AllErrors() []error { return m }

// ProvisionNewRequestRequestValidationError is the validation error returned
// by ProvisionNewRequestRequest.Validate if the designated constraints aren't met.
type ProvisionNewRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProvisionNewRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProvisionNewRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProvisionNewRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProvisionNewRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProvisionNewRequestRequestValidationError) ErrorName() string {
	return "ProvisionNewRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ProvisionNewRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProvisionNewRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProvisionNewRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProvisionNewRequestRequestValidationError{}

// Validate checks the field values on ProvisionNewRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProvisionNewRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProvisionNewRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProvisionNewRequestResponseMultiError, or nil if none found.
func (m *ProvisionNewRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ProvisionNewRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProvisionNewRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProvisionNewRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProvisionNewRequestResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProvisionNewRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProvisionNewRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProvisionNewRequestResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProvisionNewRequestResponseMultiError(errors)
	}

	return nil
}

// ProvisionNewRequestResponseMultiError is an error wrapping multiple
// validation errors returned by ProvisionNewRequestResponse.ValidateAll() if
// the designated constraints aren't met.
type ProvisionNewRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProvisionNewRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProvisionNewRequestResponseMultiError) AllErrors() []error { return m }

// ProvisionNewRequestResponseValidationError is the validation error returned
// by ProvisionNewRequestResponse.Validate if the designated constraints
// aren't met.
type ProvisionNewRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProvisionNewRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProvisionNewRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProvisionNewRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProvisionNewRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProvisionNewRequestResponseValidationError) ErrorName() string {
	return "ProvisionNewRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ProvisionNewRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProvisionNewRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProvisionNewRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProvisionNewRequestResponseValidationError{}

// Validate checks the field values on PollRequestStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PollRequestStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollRequestStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PollRequestStatusRequestMultiError, or nil if none found.
func (m *PollRequestStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *PollRequestStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return PollRequestStatusRequestMultiError(errors)
	}

	return nil
}

// PollRequestStatusRequestMultiError is an error wrapping multiple validation
// errors returned by PollRequestStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type PollRequestStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollRequestStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollRequestStatusRequestMultiError) AllErrors() []error { return m }

// PollRequestStatusRequestValidationError is the validation error returned by
// PollRequestStatusRequest.Validate if the designated constraints aren't met.
type PollRequestStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollRequestStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollRequestStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollRequestStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollRequestStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollRequestStatusRequestValidationError) ErrorName() string {
	return "PollRequestStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e PollRequestStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollRequestStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollRequestStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollRequestStatusRequestValidationError{}

// Validate checks the field values on PollRequestStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *PollRequestStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PollRequestStatusResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PollRequestStatusResponseMultiError, or nil if none found.
func (m *PollRequestStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *PollRequestStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollRequestStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollRequestStatusResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollRequestStatusResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, PollRequestStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, PollRequestStatusResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return PollRequestStatusResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return PollRequestStatusResponseMultiError(errors)
	}

	return nil
}

// PollRequestStatusResponseMultiError is an error wrapping multiple validation
// errors returned by PollRequestStatusResponse.ValidateAll() if the
// designated constraints aren't met.
type PollRequestStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PollRequestStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PollRequestStatusResponseMultiError) AllErrors() []error { return m }

// PollRequestStatusResponseValidationError is the validation error returned by
// PollRequestStatusResponse.Validate if the designated constraints aren't met.
type PollRequestStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PollRequestStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PollRequestStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PollRequestStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PollRequestStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PollRequestStatusResponseValidationError) ErrorName() string {
	return "PollRequestStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e PollRequestStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPollRequestStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PollRequestStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PollRequestStatusResponseValidationError{}

// Validate checks the field values on GetRequestStatusDetailsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRequestStatusDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRequestStatusDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRequestStatusDetailsRequestMultiError, or nil if none found.
func (m *GetRequestStatusDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRequestStatusDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	if len(errors) > 0 {
		return GetRequestStatusDetailsRequestMultiError(errors)
	}

	return nil
}

// GetRequestStatusDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by GetRequestStatusDetailsRequest.ValidateAll()
// if the designated constraints aren't met.
type GetRequestStatusDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRequestStatusDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRequestStatusDetailsRequestMultiError) AllErrors() []error { return m }

// GetRequestStatusDetailsRequestValidationError is the validation error
// returned by GetRequestStatusDetailsRequest.Validate if the designated
// constraints aren't met.
type GetRequestStatusDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRequestStatusDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRequestStatusDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRequestStatusDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRequestStatusDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRequestStatusDetailsRequestValidationError) ErrorName() string {
	return "GetRequestStatusDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetRequestStatusDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRequestStatusDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRequestStatusDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRequestStatusDetailsRequestValidationError{}

// Validate checks the field values on GetRequestStatusDetailsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetRequestStatusDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetRequestStatusDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetRequestStatusDetailsResponseMultiError, or nil if none found.
func (m *GetRequestStatusDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetRequestStatusDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRequestStatusDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRequestStatusDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRequestStatusDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetServiceRequest()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetRequestStatusDetailsResponseValidationError{
					field:  "ServiceRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetRequestStatusDetailsResponseValidationError{
					field:  "ServiceRequest",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetServiceRequest()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetRequestStatusDetailsResponseValidationError{
				field:  "ServiceRequest",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetRequestStatusDetailsResponseMultiError(errors)
	}

	return nil
}

// GetRequestStatusDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by GetRequestStatusDetailsResponse.ValidateAll()
// if the designated constraints aren't met.
type GetRequestStatusDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetRequestStatusDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetRequestStatusDetailsResponseMultiError) AllErrors() []error { return m }

// GetRequestStatusDetailsResponseValidationError is the validation error
// returned by GetRequestStatusDetailsResponse.Validate if the designated
// constraints aren't met.
type GetRequestStatusDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetRequestStatusDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetRequestStatusDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetRequestStatusDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetRequestStatusDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetRequestStatusDetailsResponseValidationError) ErrorName() string {
	return "GetRequestStatusDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetRequestStatusDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetRequestStatusDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetRequestStatusDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetRequestStatusDetailsResponseValidationError{}

// Validate checks the field values on GetAllRequestStatusDetailsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAllRequestStatusDetailsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllRequestStatusDetailsRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAllRequestStatusDetailsRequestMultiError, or nil if none found.
func (m *GetAllRequestStatusDetailsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllRequestStatusDetailsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFilters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsRequestValidationError{
					field:  "Filters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFilters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllRequestStatusDetailsRequestValidationError{
				field:  "Filters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsRequestValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllRequestStatusDetailsRequestValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SortOrder

	if len(errors) > 0 {
		return GetAllRequestStatusDetailsRequestMultiError(errors)
	}

	return nil
}

// GetAllRequestStatusDetailsRequestMultiError is an error wrapping multiple
// validation errors returned by
// GetAllRequestStatusDetailsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetAllRequestStatusDetailsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllRequestStatusDetailsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllRequestStatusDetailsRequestMultiError) AllErrors() []error { return m }

// GetAllRequestStatusDetailsRequestValidationError is the validation error
// returned by GetAllRequestStatusDetailsRequest.Validate if the designated
// constraints aren't met.
type GetAllRequestStatusDetailsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllRequestStatusDetailsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllRequestStatusDetailsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllRequestStatusDetailsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllRequestStatusDetailsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllRequestStatusDetailsRequestValidationError) ErrorName() string {
	return "GetAllRequestStatusDetailsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllRequestStatusDetailsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllRequestStatusDetailsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllRequestStatusDetailsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllRequestStatusDetailsRequestValidationError{}

// Validate checks the field values on GetAllRequestStatusDetailsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetAllRequestStatusDetailsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetAllRequestStatusDetailsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetAllRequestStatusDetailsResponseMultiError, or nil if none found.
func (m *GetAllRequestStatusDetailsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetAllRequestStatusDetailsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllRequestStatusDetailsResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetServiceRequestList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
						field:  fmt.Sprintf("ServiceRequestList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
						field:  fmt.Sprintf("ServiceRequestList[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetAllRequestStatusDetailsResponseValidationError{
					field:  fmt.Sprintf("ServiceRequestList[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetPageContext()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetAllRequestStatusDetailsResponseValidationError{
					field:  "PageContext",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageContext()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetAllRequestStatusDetailsResponseValidationError{
				field:  "PageContext",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetAllRequestStatusDetailsResponseMultiError(errors)
	}

	return nil
}

// GetAllRequestStatusDetailsResponseMultiError is an error wrapping multiple
// validation errors returned by
// GetAllRequestStatusDetailsResponse.ValidateAll() if the designated
// constraints aren't met.
type GetAllRequestStatusDetailsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetAllRequestStatusDetailsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetAllRequestStatusDetailsResponseMultiError) AllErrors() []error { return m }

// GetAllRequestStatusDetailsResponseValidationError is the validation error
// returned by GetAllRequestStatusDetailsResponse.Validate if the designated
// constraints aren't met.
type GetAllRequestStatusDetailsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetAllRequestStatusDetailsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetAllRequestStatusDetailsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetAllRequestStatusDetailsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetAllRequestStatusDetailsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetAllRequestStatusDetailsResponseValidationError) ErrorName() string {
	return "GetAllRequestStatusDetailsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetAllRequestStatusDetailsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetAllRequestStatusDetailsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetAllRequestStatusDetailsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetAllRequestStatusDetailsResponseValidationError{}

// Validate checks the field values on UpdateUserInputRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserInputRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserInputRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserInputRequestMultiError, or nil if none found.
func (m *UpdateUserInputRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserInputRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	switch v := m.UserInput.(type) {
	case *UpdateUserInputRequest_ProfileUpdateUserInput:
		if v == nil {
			err := UpdateUserInputRequestValidationError{
				field:  "UserInput",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetProfileUpdateUserInput()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, UpdateUserInputRequestValidationError{
						field:  "ProfileUpdateUserInput",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, UpdateUserInputRequestValidationError{
						field:  "ProfileUpdateUserInput",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetProfileUpdateUserInput()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return UpdateUserInputRequestValidationError{
					field:  "ProfileUpdateUserInput",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return UpdateUserInputRequestMultiError(errors)
	}

	return nil
}

// UpdateUserInputRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateUserInputRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserInputRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserInputRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserInputRequestMultiError) AllErrors() []error { return m }

// UpdateUserInputRequestValidationError is the validation error returned by
// UpdateUserInputRequest.Validate if the designated constraints aren't met.
type UpdateUserInputRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserInputRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserInputRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserInputRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserInputRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserInputRequestValidationError) ErrorName() string {
	return "UpdateUserInputRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserInputRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserInputRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserInputRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserInputRequestValidationError{}

// Validate checks the field values on UpdateUserInputResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateUserInputResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateUserInputResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateUserInputResponseMultiError, or nil if none found.
func (m *UpdateUserInputResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateUserInputResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateUserInputResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateUserInputResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateUserInputResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateUserInputResponseMultiError(errors)
	}

	return nil
}

// UpdateUserInputResponseMultiError is an error wrapping multiple validation
// errors returned by UpdateUserInputResponse.ValidateAll() if the designated
// constraints aren't met.
type UpdateUserInputResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateUserInputResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateUserInputResponseMultiError) AllErrors() []error { return m }

// UpdateUserInputResponseValidationError is the validation error returned by
// UpdateUserInputResponse.Validate if the designated constraints aren't met.
type UpdateUserInputResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateUserInputResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateUserInputResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateUserInputResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateUserInputResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateUserInputResponseValidationError) ErrorName() string {
	return "UpdateUserInputResponseValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateUserInputResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateUserInputResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateUserInputResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateUserInputResponseValidationError{}

// Validate checks the field values on SoftDeleteServiceReqRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SoftDeleteServiceReqRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SoftDeleteServiceReqRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SoftDeleteServiceReqRequestMultiError, or nil if none found.
func (m *SoftDeleteServiceReqRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SoftDeleteServiceReqRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestType

	switch v := m.Identifier.(type) {
	case *SoftDeleteServiceReqRequest_ServiceReqId:
		if v == nil {
			err := SoftDeleteServiceReqRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ServiceReqId
	case *SoftDeleteServiceReqRequest_ActorId:
		if v == nil {
			err := SoftDeleteServiceReqRequestValidationError{
				field:  "Identifier",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for ActorId
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SoftDeleteServiceReqRequestMultiError(errors)
	}

	return nil
}

// SoftDeleteServiceReqRequestMultiError is an error wrapping multiple
// validation errors returned by SoftDeleteServiceReqRequest.ValidateAll() if
// the designated constraints aren't met.
type SoftDeleteServiceReqRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SoftDeleteServiceReqRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SoftDeleteServiceReqRequestMultiError) AllErrors() []error { return m }

// SoftDeleteServiceReqRequestValidationError is the validation error returned
// by SoftDeleteServiceReqRequest.Validate if the designated constraints
// aren't met.
type SoftDeleteServiceReqRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SoftDeleteServiceReqRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SoftDeleteServiceReqRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SoftDeleteServiceReqRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SoftDeleteServiceReqRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SoftDeleteServiceReqRequestValidationError) ErrorName() string {
	return "SoftDeleteServiceReqRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SoftDeleteServiceReqRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSoftDeleteServiceReqRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SoftDeleteServiceReqRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SoftDeleteServiceReqRequestValidationError{}

// Validate checks the field values on SoftDeleteServiceReqReqResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SoftDeleteServiceReqReqResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SoftDeleteServiceReqReqResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// SoftDeleteServiceReqReqResponseMultiError, or nil if none found.
func (m *SoftDeleteServiceReqReqResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SoftDeleteServiceReqReqResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SoftDeleteServiceReqReqResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SoftDeleteServiceReqReqResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SoftDeleteServiceReqReqResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SoftDeleteServiceReqReqResponseMultiError(errors)
	}

	return nil
}

// SoftDeleteServiceReqReqResponseMultiError is an error wrapping multiple
// validation errors returned by SoftDeleteServiceReqReqResponse.ValidateAll()
// if the designated constraints aren't met.
type SoftDeleteServiceReqReqResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SoftDeleteServiceReqReqResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SoftDeleteServiceReqReqResponseMultiError) AllErrors() []error { return m }

// SoftDeleteServiceReqReqResponseValidationError is the validation error
// returned by SoftDeleteServiceReqReqResponse.Validate if the designated
// constraints aren't met.
type SoftDeleteServiceReqReqResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SoftDeleteServiceReqReqResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SoftDeleteServiceReqReqResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SoftDeleteServiceReqReqResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SoftDeleteServiceReqReqResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SoftDeleteServiceReqReqResponseValidationError) ErrorName() string {
	return "SoftDeleteServiceReqReqResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SoftDeleteServiceReqReqResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSoftDeleteServiceReqReqResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SoftDeleteServiceReqReqResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SoftDeleteServiceReqReqResponseValidationError{}

// Validate checks the field values on IsEligibleForRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsEligibleForRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsEligibleForRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsEligibleForRequestRequestMultiError, or nil if none found.
func (m *IsEligibleForRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IsEligibleForRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ActorId

	if _, ok := _IsEligibleForRequestRequest_RequestType_NotInLookup[m.GetRequestType()]; ok {
		err := IsEligibleForRequestRequestValidationError{
			field:  "RequestType",
			reason: "value must not be in list [REQUEST_TYPE_UNSPECIFIED]",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return IsEligibleForRequestRequestMultiError(errors)
	}

	return nil
}

// IsEligibleForRequestRequestMultiError is an error wrapping multiple
// validation errors returned by IsEligibleForRequestRequest.ValidateAll() if
// the designated constraints aren't met.
type IsEligibleForRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsEligibleForRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsEligibleForRequestRequestMultiError) AllErrors() []error { return m }

// IsEligibleForRequestRequestValidationError is the validation error returned
// by IsEligibleForRequestRequest.Validate if the designated constraints
// aren't met.
type IsEligibleForRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsEligibleForRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsEligibleForRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsEligibleForRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsEligibleForRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsEligibleForRequestRequestValidationError) ErrorName() string {
	return "IsEligibleForRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e IsEligibleForRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsEligibleForRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsEligibleForRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsEligibleForRequestRequestValidationError{}

var _IsEligibleForRequestRequest_RequestType_NotInLookup = map[RequestType]struct{}{
	0: {},
}

// Validate checks the field values on IsEligibleForRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *IsEligibleForRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IsEligibleForRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IsEligibleForRequestResponseMultiError, or nil if none found.
func (m *IsEligibleForRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *IsEligibleForRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IsEligibleForRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IsEligibleForRequestResponseValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IsEligibleForRequestResponseValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsEligible

	// no validation rules for FailureReason

	if len(errors) > 0 {
		return IsEligibleForRequestResponseMultiError(errors)
	}

	return nil
}

// IsEligibleForRequestResponseMultiError is an error wrapping multiple
// validation errors returned by IsEligibleForRequestResponse.ValidateAll() if
// the designated constraints aren't met.
type IsEligibleForRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IsEligibleForRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IsEligibleForRequestResponseMultiError) AllErrors() []error { return m }

// IsEligibleForRequestResponseValidationError is the validation error returned
// by IsEligibleForRequestResponse.Validate if the designated constraints
// aren't met.
type IsEligibleForRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IsEligibleForRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IsEligibleForRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IsEligibleForRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IsEligibleForRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IsEligibleForRequestResponseValidationError) ErrorName() string {
	return "IsEligibleForRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e IsEligibleForRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIsEligibleForRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IsEligibleForRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IsEligibleForRequestResponseValidationError{}
