// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/alfred/service.proto

package alfred

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EligibilityFailureReason int32

const (
	EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED          EligibilityFailureReason = 0
	EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING     EligibilityFailureReason = 1
	EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE EligibilityFailureReason = 2
	EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE    EligibilityFailureReason = 3
)

// Enum value maps for EligibilityFailureReason.
var (
	EligibilityFailureReason_name = map[int32]string{
		0: "ELIGIBILITY_FAILURE_REASON_UNSPECIFIED",
		1: "ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING",
		2: "ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE",
		3: "ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE",
	}
	EligibilityFailureReason_value = map[string]int32{
		"ELIGIBILITY_FAILURE_REASON_UNSPECIFIED":          0,
		"ELIGIBILITY_FAILURE_REASON_FULL_KYC_MISSING":     1,
		"ELIGIBILITY_FAILURE_REASON_INSUFFICIENT_BALANCE": 2,
		"ELIGIBILITY_FAILURE_REASON_MISSING_SIGNATURE":    3,
	}
)

func (x EligibilityFailureReason) Enum() *EligibilityFailureReason {
	p := new(EligibilityFailureReason)
	*p = x
	return p
}

func (x EligibilityFailureReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EligibilityFailureReason) Descriptor() protoreflect.EnumDescriptor {
	return file_api_alfred_service_proto_enumTypes[0].Descriptor()
}

func (EligibilityFailureReason) Type() protoreflect.EnumType {
	return &file_api_alfred_service_proto_enumTypes[0]
}

func (x EligibilityFailureReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EligibilityFailureReason.Descriptor instead.
func (EligibilityFailureReason) EnumDescriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{0}
}

type ProvisionNewRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string      `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestType RequestType `protobuf:"varint,2,opt,name=request_type,json=requestType,proto3,enum=alfred.RequestType" json:"request_type,omitempty"`
	// OPTIONAL blob data which can be used by different request handlers to determine some parameters e.g. year in case of tax document generation
	Blob []byte `protobuf:"bytes,3,opt,name=blob,proto3" json:"blob,omitempty"`
}

func (x *ProvisionNewRequestRequest) Reset() {
	*x = ProvisionNewRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProvisionNewRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProvisionNewRequestRequest) ProtoMessage() {}

func (x *ProvisionNewRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProvisionNewRequestRequest.ProtoReflect.Descriptor instead.
func (*ProvisionNewRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{0}
}

func (x *ProvisionNewRequestRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *ProvisionNewRequestRequest) GetRequestType() RequestType {
	if x != nil {
		return x.RequestType
	}
	return RequestType_REQUEST_TYPE_UNSPECIFIED
}

func (x *ProvisionNewRequestRequest) GetBlob() []byte {
	if x != nil {
		return x.Blob
	}
	return nil
}

type ProvisionNewRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *ProvisionNewRequestResponse) Reset() {
	*x = ProvisionNewRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProvisionNewRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProvisionNewRequestResponse) ProtoMessage() {}

func (x *ProvisionNewRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProvisionNewRequestResponse.ProtoReflect.Descriptor instead.
func (*ProvisionNewRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{1}
}

func (x *ProvisionNewRequestResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ProvisionNewRequestResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type PollRequestStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request_id is received by client in the deeplink for generic poll screen deeplink.
	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *PollRequestStatusRequest) Reset() {
	*x = PollRequestStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollRequestStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollRequestStatusRequest) ProtoMessage() {}

func (x *PollRequestStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollRequestStatusRequest.ProtoReflect.Descriptor instead.
func (*PollRequestStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{2}
}

func (x *PollRequestStatusRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type PollRequestStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *PollRequestStatusResponse) Reset() {
	*x = PollRequestStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PollRequestStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PollRequestStatusResponse) ProtoMessage() {}

func (x *PollRequestStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PollRequestStatusResponse.ProtoReflect.Descriptor instead.
func (*PollRequestStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{3}
}

func (x *PollRequestStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *PollRequestStatusResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetRequestStatusDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *GetRequestStatusDetailsRequest) Reset() {
	*x = GetRequestStatusDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequestStatusDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequestStatusDetailsRequest) ProtoMessage() {}

func (x *GetRequestStatusDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequestStatusDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetRequestStatusDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetRequestStatusDetailsRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

type GetRequestStatusDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status     `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ServiceRequest *ServiceRequest `protobuf:"bytes,2,opt,name=service_request,json=serviceRequest,proto3" json:"service_request,omitempty"`
}

func (x *GetRequestStatusDetailsResponse) Reset() {
	*x = GetRequestStatusDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetRequestStatusDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRequestStatusDetailsResponse) ProtoMessage() {}

func (x *GetRequestStatusDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRequestStatusDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetRequestStatusDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetRequestStatusDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRequestStatusDetailsResponse) GetServiceRequest() *ServiceRequest {
	if x != nil {
		return x.ServiceRequest
	}
	return nil
}

type GetAllRequestStatusDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Filters *Filters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	// page context to help server fetch the page
	PageContext *rpc.PageContextRequest `protobuf:"bytes,2,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
	// default sort order is DESC
	SortOrder SortOrder `protobuf:"varint,3,opt,name=sort_order,json=sortOrder,proto3,enum=alfred.SortOrder" json:"sort_order,omitempty"`
}

func (x *GetAllRequestStatusDetailsRequest) Reset() {
	*x = GetAllRequestStatusDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRequestStatusDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRequestStatusDetailsRequest) ProtoMessage() {}

func (x *GetAllRequestStatusDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRequestStatusDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetAllRequestStatusDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetAllRequestStatusDetailsRequest) GetFilters() *Filters {
	if x != nil {
		return x.Filters
	}
	return nil
}

func (x *GetAllRequestStatusDetailsRequest) GetPageContext() *rpc.PageContextRequest {
	if x != nil {
		return x.PageContext
	}
	return nil
}

func (x *GetAllRequestStatusDetailsRequest) GetSortOrder() SortOrder {
	if x != nil {
		return x.SortOrder
	}
	return SortOrder_SORT_ORDER_UNSPECIFIED
}

type GetAllRequestStatusDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// rpc response status
	Status             *rpc.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ServiceRequestList []*ServiceRequest `protobuf:"bytes,2,rep,name=service_request_list,json=serviceRequestList,proto3" json:"service_request_list,omitempty"`
	// page context to help client fetch the next page
	PageContext *rpc.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetAllRequestStatusDetailsResponse) Reset() {
	*x = GetAllRequestStatusDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAllRequestStatusDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllRequestStatusDetailsResponse) ProtoMessage() {}

func (x *GetAllRequestStatusDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllRequestStatusDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetAllRequestStatusDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetAllRequestStatusDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAllRequestStatusDetailsResponse) GetServiceRequestList() []*ServiceRequest {
	if x != nil {
		return x.ServiceRequestList
	}
	return nil
}

func (x *GetAllRequestStatusDetailsResponse) GetPageContext() *rpc.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

type UpdateUserInputRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Types that are assignable to UserInput:
	//
	//	*UpdateUserInputRequest_ProfileUpdateUserInput
	UserInput isUpdateUserInputRequest_UserInput `protobuf_oneof:"user_input"`
}

func (x *UpdateUserInputRequest) Reset() {
	*x = UpdateUserInputRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserInputRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInputRequest) ProtoMessage() {}

func (x *UpdateUserInputRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInputRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserInputRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateUserInputRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (m *UpdateUserInputRequest) GetUserInput() isUpdateUserInputRequest_UserInput {
	if m != nil {
		return m.UserInput
	}
	return nil
}

func (x *UpdateUserInputRequest) GetProfileUpdateUserInput() *ProfileUpdateUserInput {
	if x, ok := x.GetUserInput().(*UpdateUserInputRequest_ProfileUpdateUserInput); ok {
		return x.ProfileUpdateUserInput
	}
	return nil
}

type isUpdateUserInputRequest_UserInput interface {
	isUpdateUserInputRequest_UserInput()
}

type UpdateUserInputRequest_ProfileUpdateUserInput struct {
	ProfileUpdateUserInput *ProfileUpdateUserInput `protobuf:"bytes,2,opt,name=profile_update_user_input,json=profileUpdateUserInput,proto3,oneof"`
}

func (*UpdateUserInputRequest_ProfileUpdateUserInput) isUpdateUserInputRequest_UserInput() {}

type UpdateUserInputResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *UpdateUserInputResponse) Reset() {
	*x = UpdateUserInputResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserInputResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInputResponse) ProtoMessage() {}

func (x *UpdateUserInputResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInputResponse.ProtoReflect.Descriptor instead.
func (*UpdateUserInputResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateUserInputResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type SoftDeleteServiceReqRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestType RequestType `protobuf:"varint,1,opt,name=request_type,json=requestType,proto3,enum=alfred.RequestType" json:"request_type,omitempty"`
	// Types that are assignable to Identifier:
	//
	//	*SoftDeleteServiceReqRequest_ServiceReqId
	//	*SoftDeleteServiceReqRequest_ActorId
	Identifier isSoftDeleteServiceReqRequest_Identifier `protobuf_oneof:"identifier"`
}

func (x *SoftDeleteServiceReqRequest) Reset() {
	*x = SoftDeleteServiceReqRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftDeleteServiceReqRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftDeleteServiceReqRequest) ProtoMessage() {}

func (x *SoftDeleteServiceReqRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftDeleteServiceReqRequest.ProtoReflect.Descriptor instead.
func (*SoftDeleteServiceReqRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{10}
}

func (x *SoftDeleteServiceReqRequest) GetRequestType() RequestType {
	if x != nil {
		return x.RequestType
	}
	return RequestType_REQUEST_TYPE_UNSPECIFIED
}

func (m *SoftDeleteServiceReqRequest) GetIdentifier() isSoftDeleteServiceReqRequest_Identifier {
	if m != nil {
		return m.Identifier
	}
	return nil
}

func (x *SoftDeleteServiceReqRequest) GetServiceReqId() string {
	if x, ok := x.GetIdentifier().(*SoftDeleteServiceReqRequest_ServiceReqId); ok {
		return x.ServiceReqId
	}
	return ""
}

func (x *SoftDeleteServiceReqRequest) GetActorId() string {
	if x, ok := x.GetIdentifier().(*SoftDeleteServiceReqRequest_ActorId); ok {
		return x.ActorId
	}
	return ""
}

type isSoftDeleteServiceReqRequest_Identifier interface {
	isSoftDeleteServiceReqRequest_Identifier()
}

type SoftDeleteServiceReqRequest_ServiceReqId struct {
	ServiceReqId string `protobuf:"bytes,2,opt,name=service_req_id,json=serviceReqId,proto3,oneof"`
}

type SoftDeleteServiceReqRequest_ActorId struct {
	ActorId string `protobuf:"bytes,3,opt,name=actor_id,json=actorId,proto3,oneof"`
}

func (*SoftDeleteServiceReqRequest_ServiceReqId) isSoftDeleteServiceReqRequest_Identifier() {}

func (*SoftDeleteServiceReqRequest_ActorId) isSoftDeleteServiceReqRequest_Identifier() {}

type SoftDeleteServiceReqReqResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// OK for success
	// INTERNAL for failure of the operation
	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *SoftDeleteServiceReqReqResponse) Reset() {
	*x = SoftDeleteServiceReqReqResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SoftDeleteServiceReqReqResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SoftDeleteServiceReqReqResponse) ProtoMessage() {}

func (x *SoftDeleteServiceReqReqResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SoftDeleteServiceReqReqResponse.ProtoReflect.Descriptor instead.
func (*SoftDeleteServiceReqReqResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{11}
}

func (x *SoftDeleteServiceReqReqResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type IsEligibleForRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ActorId     string      `protobuf:"bytes,1,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
	RequestType RequestType `protobuf:"varint,2,opt,name=request_type,json=requestType,proto3,enum=alfred.RequestType" json:"request_type,omitempty"`
}

func (x *IsEligibleForRequestRequest) Reset() {
	*x = IsEligibleForRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsEligibleForRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsEligibleForRequestRequest) ProtoMessage() {}

func (x *IsEligibleForRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsEligibleForRequestRequest.ProtoReflect.Descriptor instead.
func (*IsEligibleForRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{12}
}

func (x *IsEligibleForRequestRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

func (x *IsEligibleForRequestRequest) GetRequestType() RequestType {
	if x != nil {
		return x.RequestType
	}
	return RequestType_REQUEST_TYPE_UNSPECIFIED
}

type IsEligibleForRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status     *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IsEligible bool        `protobuf:"varint,2,opt,name=is_eligible,json=isEligible,proto3" json:"is_eligible,omitempty"`
	// Indicates why the user is not eligible.
	// Will be UNSPECIFIED if the user is eligible or some unknown failure reason occurs.
	FailureReason EligibilityFailureReason `protobuf:"varint,3,opt,name=failure_reason,json=failureReason,proto3,enum=alfred.EligibilityFailureReason" json:"failure_reason,omitempty"`
}

func (x *IsEligibleForRequestResponse) Reset() {
	*x = IsEligibleForRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_alfred_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IsEligibleForRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsEligibleForRequestResponse) ProtoMessage() {}

func (x *IsEligibleForRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_alfred_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsEligibleForRequestResponse.ProtoReflect.Descriptor instead.
func (*IsEligibleForRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_alfred_service_proto_rawDescGZIP(), []int{13}
}

func (x *IsEligibleForRequestResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *IsEligibleForRequestResponse) GetIsEligible() bool {
	if x != nil {
		return x.IsEligible
	}
	return false
}

func (x *IsEligibleForRequestResponse) GetFailureReason() EligibilityFailureReason {
	if x != nil {
		return x.FailureReason
	}
	return EligibilityFailureReason_ELIGIBILITY_FAILURE_REASON_UNSPECIFIED
}

var File_api_alfred_service_proto protoreflect.FileDescriptor

var file_api_alfred_service_proto_rawDesc = []byte{
	0x0a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x61, 0x6c, 0x66, 0x72,
	0x65, 0x64, 0x1a, 0x21, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x61, 0x70, 0x69,
	0x2f, 0x72, 0x70, 0x63, 0x2f, 0x70, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83,
	0x01, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13,
	0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x62, 0x6c, 0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04,
	0x62, 0x6c, 0x6f, 0x62, 0x22, 0x80, 0x01, 0x0a, 0x1b, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69,
	0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69,
	0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78,
	0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x39, 0x0a, 0x18, 0x50, 0x6f, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x22, 0x7e, 0x0a, 0x19, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x3f, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x22, 0x87, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3f, 0x0a, 0x0f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xbc, 0x01,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x29, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x46, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x73, 0x12, 0x3a,
	0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x0b, 0x70,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x12, 0x30, 0x0a, 0x0a, 0x73, 0x6f,
	0x72, 0x74, 0x5f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x11,
	0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x52, 0x09, 0x73, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x22, 0xd0, 0x01, 0x0a,
	0x22, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x48, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x6c, 0x69, 0x73, 0x74,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x12,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x3b, 0x0a, 0x0c, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65,
	0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x50,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x22,
	0xa2, 0x01, 0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x19, 0x70, 0x72, 0x6f,
	0x66, 0x69, 0x6c, 0x65, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x70, 0x75, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61,
	0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x48, 0x00, 0x52, 0x16,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x42, 0x0c, 0x0a, 0x0a, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x6e, 0x70, 0x75, 0x74, 0x22, 0x3e, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x1b, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x36, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x6c, 0x66,
	0x72, 0x65, 0x64, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x26, 0x0a, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x42, 0x0c, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0x46, 0x0a, 0x1f, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x52, 0x65, 0x71, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x1b, 0x49, 0x73, 0x45, 0x6c, 0x69,
	0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x40, 0x0a, 0x0c, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xad, 0x01, 0x0a, 0x1c, 0x49, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62,
	0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x73, 0x5f,
	0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0a,
	0x69, 0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x12, 0x47, 0x0a, 0x0e, 0x66, 0x61,
	0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x20, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x2a, 0xde, 0x01, 0x0a, 0x18, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x46, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x2a, 0x0a, 0x26, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f,
	0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x2f, 0x0a, 0x2b,
	0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c,
	0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x46, 0x55, 0x4c, 0x4c, 0x5f,
	0x4b, 0x59, 0x43, 0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x33, 0x0a,
	0x2f, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x49, 0x4e, 0x53, 0x55,
	0x46, 0x46, 0x49, 0x43, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45,
	0x10, 0x02, 0x12, 0x30, 0x0a, 0x2c, 0x45, 0x4c, 0x49, 0x47, 0x49, 0x42, 0x49, 0x4c, 0x49, 0x54,
	0x59, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x53, 0x4f, 0x4e,
	0x5f, 0x4d, 0x49, 0x53, 0x53, 0x49, 0x4e, 0x47, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x41, 0x54, 0x55,
	0x52, 0x45, 0x10, 0x03, 0x32, 0xc4, 0x05, 0x0a, 0x06, 0x41, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x12,
	0x5e, 0x0a, 0x13, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e,
	0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x61, 0x6c, 0x66,
	0x72, 0x65, 0x64, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x58, 0x0a, 0x11, 0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x50, 0x6f,
	0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e,
	0x50, 0x6f, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x6a, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x12, 0x26, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x47, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x61,
	0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x73, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x12, 0x29, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a,
	0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x0f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x12, 0x1e, 0x2e,
	0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e,
	0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x70, 0x75, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x68,
	0x0a, 0x18, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x23, 0x2e, 0x61, 0x6c, 0x66,
	0x72, 0x65, 0x64, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x53, 0x6f, 0x66, 0x74, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x52, 0x65, 0x71,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x61, 0x0a, 0x14, 0x49, 0x73, 0x45, 0x6c,
	0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x23, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x49, 0x73, 0x45, 0x6c, 0x69, 0x67,
	0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64, 0x2e, 0x49,
	0x73, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x6c, 0x65, 0x46, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42, 0x46, 0x0a, 0x21, 0x63,
	0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e,
	0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x61, 0x6c, 0x66, 0x72, 0x65, 0x64,
	0x5a, 0x21, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6c, 0x66,
	0x72, 0x65, 0x64, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_alfred_service_proto_rawDescOnce sync.Once
	file_api_alfred_service_proto_rawDescData = file_api_alfred_service_proto_rawDesc
)

func file_api_alfred_service_proto_rawDescGZIP() []byte {
	file_api_alfred_service_proto_rawDescOnce.Do(func() {
		file_api_alfred_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_alfred_service_proto_rawDescData)
	})
	return file_api_alfred_service_proto_rawDescData
}

var file_api_alfred_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_alfred_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_api_alfred_service_proto_goTypes = []interface{}{
	(EligibilityFailureReason)(0),              // 0: alfred.EligibilityFailureReason
	(*ProvisionNewRequestRequest)(nil),         // 1: alfred.ProvisionNewRequestRequest
	(*ProvisionNewRequestResponse)(nil),        // 2: alfred.ProvisionNewRequestResponse
	(*PollRequestStatusRequest)(nil),           // 3: alfred.PollRequestStatusRequest
	(*PollRequestStatusResponse)(nil),          // 4: alfred.PollRequestStatusResponse
	(*GetRequestStatusDetailsRequest)(nil),     // 5: alfred.GetRequestStatusDetailsRequest
	(*GetRequestStatusDetailsResponse)(nil),    // 6: alfred.GetRequestStatusDetailsResponse
	(*GetAllRequestStatusDetailsRequest)(nil),  // 7: alfred.GetAllRequestStatusDetailsRequest
	(*GetAllRequestStatusDetailsResponse)(nil), // 8: alfred.GetAllRequestStatusDetailsResponse
	(*UpdateUserInputRequest)(nil),             // 9: alfred.UpdateUserInputRequest
	(*UpdateUserInputResponse)(nil),            // 10: alfred.UpdateUserInputResponse
	(*SoftDeleteServiceReqRequest)(nil),        // 11: alfred.SoftDeleteServiceReqRequest
	(*SoftDeleteServiceReqReqResponse)(nil),    // 12: alfred.SoftDeleteServiceReqReqResponse
	(*IsEligibleForRequestRequest)(nil),        // 13: alfred.IsEligibleForRequestRequest
	(*IsEligibleForRequestResponse)(nil),       // 14: alfred.IsEligibleForRequestResponse
	(RequestType)(0),                           // 15: alfred.RequestType
	(*rpc.Status)(nil),                         // 16: rpc.Status
	(*deeplink.Deeplink)(nil),                  // 17: frontend.deeplink.Deeplink
	(*ServiceRequest)(nil),                     // 18: alfred.ServiceRequest
	(*Filters)(nil),                            // 19: alfred.Filters
	(*rpc.PageContextRequest)(nil),             // 20: rpc.PageContextRequest
	(SortOrder)(0),                             // 21: alfred.SortOrder
	(*rpc.PageContextResponse)(nil),            // 22: rpc.PageContextResponse
	(*ProfileUpdateUserInput)(nil),             // 23: alfred.ProfileUpdateUserInput
}
var file_api_alfred_service_proto_depIdxs = []int32{
	15, // 0: alfred.ProvisionNewRequestRequest.request_type:type_name -> alfred.RequestType
	16, // 1: alfred.ProvisionNewRequestResponse.status:type_name -> rpc.Status
	17, // 2: alfred.ProvisionNewRequestResponse.next_action:type_name -> frontend.deeplink.Deeplink
	16, // 3: alfred.PollRequestStatusResponse.status:type_name -> rpc.Status
	17, // 4: alfred.PollRequestStatusResponse.next_action:type_name -> frontend.deeplink.Deeplink
	16, // 5: alfred.GetRequestStatusDetailsResponse.status:type_name -> rpc.Status
	18, // 6: alfred.GetRequestStatusDetailsResponse.service_request:type_name -> alfred.ServiceRequest
	19, // 7: alfred.GetAllRequestStatusDetailsRequest.filters:type_name -> alfred.Filters
	20, // 8: alfred.GetAllRequestStatusDetailsRequest.page_context:type_name -> rpc.PageContextRequest
	21, // 9: alfred.GetAllRequestStatusDetailsRequest.sort_order:type_name -> alfred.SortOrder
	16, // 10: alfred.GetAllRequestStatusDetailsResponse.status:type_name -> rpc.Status
	18, // 11: alfred.GetAllRequestStatusDetailsResponse.service_request_list:type_name -> alfred.ServiceRequest
	22, // 12: alfred.GetAllRequestStatusDetailsResponse.page_context:type_name -> rpc.PageContextResponse
	23, // 13: alfred.UpdateUserInputRequest.profile_update_user_input:type_name -> alfred.ProfileUpdateUserInput
	16, // 14: alfred.UpdateUserInputResponse.status:type_name -> rpc.Status
	15, // 15: alfred.SoftDeleteServiceReqRequest.request_type:type_name -> alfred.RequestType
	16, // 16: alfred.SoftDeleteServiceReqReqResponse.status:type_name -> rpc.Status
	15, // 17: alfred.IsEligibleForRequestRequest.request_type:type_name -> alfred.RequestType
	16, // 18: alfred.IsEligibleForRequestResponse.status:type_name -> rpc.Status
	0,  // 19: alfred.IsEligibleForRequestResponse.failure_reason:type_name -> alfred.EligibilityFailureReason
	1,  // 20: alfred.Alfred.ProvisionNewRequest:input_type -> alfred.ProvisionNewRequestRequest
	3,  // 21: alfred.Alfred.PollRequestStatus:input_type -> alfred.PollRequestStatusRequest
	5,  // 22: alfred.Alfred.GetRequestStatusDetails:input_type -> alfred.GetRequestStatusDetailsRequest
	7,  // 23: alfred.Alfred.GetAllRequestStatusDetails:input_type -> alfred.GetAllRequestStatusDetailsRequest
	9,  // 24: alfred.Alfred.UpdateUserInput:input_type -> alfred.UpdateUserInputRequest
	11, // 25: alfred.Alfred.SoftDeleteServiceRequest:input_type -> alfred.SoftDeleteServiceReqRequest
	13, // 26: alfred.Alfred.IsEligibleForRequest:input_type -> alfred.IsEligibleForRequestRequest
	2,  // 27: alfred.Alfred.ProvisionNewRequest:output_type -> alfred.ProvisionNewRequestResponse
	4,  // 28: alfred.Alfred.PollRequestStatus:output_type -> alfred.PollRequestStatusResponse
	6,  // 29: alfred.Alfred.GetRequestStatusDetails:output_type -> alfred.GetRequestStatusDetailsResponse
	8,  // 30: alfred.Alfred.GetAllRequestStatusDetails:output_type -> alfred.GetAllRequestStatusDetailsResponse
	10, // 31: alfred.Alfred.UpdateUserInput:output_type -> alfred.UpdateUserInputResponse
	12, // 32: alfred.Alfred.SoftDeleteServiceRequest:output_type -> alfred.SoftDeleteServiceReqReqResponse
	14, // 33: alfred.Alfred.IsEligibleForRequest:output_type -> alfred.IsEligibleForRequestResponse
	27, // [27:34] is the sub-list for method output_type
	20, // [20:27] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_api_alfred_service_proto_init() }
func file_api_alfred_service_proto_init() {
	if File_api_alfred_service_proto != nil {
		return
	}
	file_api_alfred_service_requests_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_alfred_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProvisionNewRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProvisionNewRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollRequestStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PollRequestStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequestStatusDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetRequestStatusDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRequestStatusDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAllRequestStatusDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserInputRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserInputResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftDeleteServiceReqRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SoftDeleteServiceReqReqResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsEligibleForRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_alfred_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IsEligibleForRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_alfred_service_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*UpdateUserInputRequest_ProfileUpdateUserInput)(nil),
	}
	file_api_alfred_service_proto_msgTypes[10].OneofWrappers = []interface{}{
		(*SoftDeleteServiceReqRequest_ServiceReqId)(nil),
		(*SoftDeleteServiceReqRequest_ActorId)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_alfred_service_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_alfred_service_proto_goTypes,
		DependencyIndexes: file_api_alfred_service_proto_depIdxs,
		EnumInfos:         file_api_alfred_service_proto_enumTypes,
		MessageInfos:      file_api_alfred_service_proto_msgTypes,
	}.Build()
	File_api_alfred_service_proto = out.File
	file_api_alfred_service_proto_rawDesc = nil
	file_api_alfred_service_proto_goTypes = nil
	file_api_alfred_service_proto_depIdxs = nil
}
