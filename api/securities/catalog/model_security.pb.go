//go:generate gen_sql -types=SecurityDetails,StockDetails,FundDetails,FinancialInfo,FundamentalParameters

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/securities/catalog/model_security.proto

package catalog

import (
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	typesv2 "github.com/epifi/gamma/api/typesv2"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SecurityFieldMask int32

const (
	SecurityFieldMask_SECURITY_FIELD_MASK_UNSPECIFIED        SecurityFieldMask = 0
	SecurityFieldMask_SECURITY_FIELD_MASK_ID                 SecurityFieldMask = 1
	SecurityFieldMask_SECURITY_FIELD_MASK_SECURITY_TYPE      SecurityFieldMask = 2
	SecurityFieldMask_SECURITY_FIELD_MASK_NAME               SecurityFieldMask = 3
	SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR             SecurityFieldMask = 4
	SecurityFieldMask_SECURITY_FIELD_MASK_VENDOR_SECURITY_ID SecurityFieldMask = 5
	SecurityFieldMask_SECURITY_FIELD_MASK_LOGO_URL           SecurityFieldMask = 6
	SecurityFieldMask_SECURITY_FIELD_MASK_DETAILS            SecurityFieldMask = 7
	SecurityFieldMask_SECURITY_FIELD_MASK_CREATED_AT         SecurityFieldMask = 8
	SecurityFieldMask_SECURITY_FIELD_MASK_UPDATED_AT         SecurityFieldMask = 9
	SecurityFieldMask_SECURITY_FIELD_MASK_DELETED_AT         SecurityFieldMask = 10
	SecurityFieldMask_SECURITY_FIELD_MASK_FINANCIAL_INFO     SecurityFieldMask = 11
)

// Enum value maps for SecurityFieldMask.
var (
	SecurityFieldMask_name = map[int32]string{
		0:  "SECURITY_FIELD_MASK_UNSPECIFIED",
		1:  "SECURITY_FIELD_MASK_ID",
		2:  "SECURITY_FIELD_MASK_SECURITY_TYPE",
		3:  "SECURITY_FIELD_MASK_NAME",
		4:  "SECURITY_FIELD_MASK_VENDOR",
		5:  "SECURITY_FIELD_MASK_VENDOR_SECURITY_ID",
		6:  "SECURITY_FIELD_MASK_LOGO_URL",
		7:  "SECURITY_FIELD_MASK_DETAILS",
		8:  "SECURITY_FIELD_MASK_CREATED_AT",
		9:  "SECURITY_FIELD_MASK_UPDATED_AT",
		10: "SECURITY_FIELD_MASK_DELETED_AT",
		11: "SECURITY_FIELD_MASK_FINANCIAL_INFO",
	}
	SecurityFieldMask_value = map[string]int32{
		"SECURITY_FIELD_MASK_UNSPECIFIED":        0,
		"SECURITY_FIELD_MASK_ID":                 1,
		"SECURITY_FIELD_MASK_SECURITY_TYPE":      2,
		"SECURITY_FIELD_MASK_NAME":               3,
		"SECURITY_FIELD_MASK_VENDOR":             4,
		"SECURITY_FIELD_MASK_VENDOR_SECURITY_ID": 5,
		"SECURITY_FIELD_MASK_LOGO_URL":           6,
		"SECURITY_FIELD_MASK_DETAILS":            7,
		"SECURITY_FIELD_MASK_CREATED_AT":         8,
		"SECURITY_FIELD_MASK_UPDATED_AT":         9,
		"SECURITY_FIELD_MASK_DELETED_AT":         10,
		"SECURITY_FIELD_MASK_FINANCIAL_INFO":     11,
	}
)

func (x SecurityFieldMask) Enum() *SecurityFieldMask {
	p := new(SecurityFieldMask)
	*p = x
	return p
}

func (x SecurityFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_securities_catalog_model_security_proto_enumTypes[0].Descriptor()
}

func (SecurityFieldMask) Type() protoreflect.EnumType {
	return &file_api_securities_catalog_model_security_proto_enumTypes[0]
}

func (x SecurityFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityFieldMask.Descriptor instead.
func (SecurityFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{0}
}

type Security struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SecurityType     SecurityType           `protobuf:"varint,2,opt,name=security_type,json=securityType,proto3,enum=api.securities.catalog.SecurityType" json:"security_type,omitempty"`
	SecurityName     string                 `protobuf:"bytes,3,opt,name=security_name,json=securityName,proto3" json:"security_name,omitempty"`
	Vendor           vendorgateway.Vendor   `protobuf:"varint,4,opt,name=vendor,proto3,enum=vendorgateway.Vendor" json:"vendor,omitempty"`
	VendorSecurityId string                 `protobuf:"bytes,5,opt,name=vendor_security_id,json=vendorSecurityId,proto3" json:"vendor_security_id,omitempty"`
	LogoUrl          string                 `protobuf:"bytes,6,opt,name=logo_url,json=logoUrl,proto3" json:"logo_url,omitempty"`
	SecurityDetails  *SecurityDetails       `protobuf:"bytes,7,opt,name=security_details,json=securityDetails,proto3" json:"security_details,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	DeletedAt        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Financial information for this listing like PE, PB, etc.
	FinancialInfo *FinancialInfo `protobuf:"bytes,11,opt,name=financial_info,json=financialInfo,proto3" json:"financial_info,omitempty"`
}

func (x *Security) Reset() {
	*x = Security{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Security) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Security) ProtoMessage() {}

func (x *Security) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Security.ProtoReflect.Descriptor instead.
func (*Security) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{0}
}

func (x *Security) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Security) GetSecurityType() SecurityType {
	if x != nil {
		return x.SecurityType
	}
	return SecurityType_SECURITY_TYPE_UNSPECIFIED
}

func (x *Security) GetSecurityName() string {
	if x != nil {
		return x.SecurityName
	}
	return ""
}

func (x *Security) GetVendor() vendorgateway.Vendor {
	if x != nil {
		return x.Vendor
	}
	return vendorgateway.Vendor(0)
}

func (x *Security) GetVendorSecurityId() string {
	if x != nil {
		return x.VendorSecurityId
	}
	return ""
}

func (x *Security) GetLogoUrl() string {
	if x != nil {
		return x.LogoUrl
	}
	return ""
}

func (x *Security) GetSecurityDetails() *SecurityDetails {
	if x != nil {
		return x.SecurityDetails
	}
	return nil
}

func (x *Security) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Security) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Security) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Security) GetFinancialInfo() *FinancialInfo {
	if x != nil {
		return x.FinancialInfo
	}
	return nil
}

type SecurityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to SecurityType:
	//
	//	*SecurityDetails_StockDetails
	//	*SecurityDetails_FundDetails
	SecurityType isSecurityDetails_SecurityType `protobuf_oneof:"security_type"`
}

func (x *SecurityDetails) Reset() {
	*x = SecurityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SecurityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecurityDetails) ProtoMessage() {}

func (x *SecurityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecurityDetails.ProtoReflect.Descriptor instead.
func (*SecurityDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{1}
}

func (m *SecurityDetails) GetSecurityType() isSecurityDetails_SecurityType {
	if m != nil {
		return m.SecurityType
	}
	return nil
}

func (x *SecurityDetails) GetStockDetails() *StockDetails {
	if x, ok := x.GetSecurityType().(*SecurityDetails_StockDetails); ok {
		return x.StockDetails
	}
	return nil
}

func (x *SecurityDetails) GetFundDetails() *FundDetails {
	if x, ok := x.GetSecurityType().(*SecurityDetails_FundDetails); ok {
		return x.FundDetails
	}
	return nil
}

type isSecurityDetails_SecurityType interface {
	isSecurityDetails_SecurityType()
}

type SecurityDetails_StockDetails struct {
	// StockDetails contains the stock related information and metadata
	StockDetails *StockDetails `protobuf:"bytes,1,opt,name=stock_details,json=stockDetails,proto3,oneof"`
}

type SecurityDetails_FundDetails struct {
	// FundDetails contains fund related information and metadata
	FundDetails *FundDetails `protobuf:"bytes,2,opt,name=fund_details,json=fundDetails,proto3,oneof"`
}

func (*SecurityDetails_StockDetails) isSecurityDetails_SecurityType() {}

func (*SecurityDetails_FundDetails) isSecurityDetails_SecurityType() {}

// StockDetails contains the stock related information and metadata
type StockDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	StockName                string                `protobuf:"bytes,1,opt,name=stock_name,json=stockName,proto3" json:"stock_name,omitempty"`
	StockShortName           string                `protobuf:"bytes,2,opt,name=stock_short_name,json=stockShortName,proto3" json:"stock_short_name,omitempty"`
	WebsiteUrl               string                `protobuf:"bytes,3,opt,name=website_url,json=websiteUrl,proto3" json:"website_url,omitempty"`
	RegionName               string                `protobuf:"bytes,4,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	IncorporationCountryName string                `protobuf:"bytes,5,opt,name=incorporation_country_name,json=incorporationCountryName,proto3" json:"incorporation_country_name,omitempty"`
	GicsSectorType           GICSSectorType        `protobuf:"varint,6,opt,name=gics_sector_type,json=gicsSectorType,proto3,enum=api.securities.catalog.GICSSectorType" json:"gics_sector_type,omitempty"`
	GicsIndustryGroupType    GICSIndustryGroupType `protobuf:"varint,7,opt,name=gics_industry_group_type,json=gicsIndustryGroupType,proto3,enum=api.securities.catalog.GICSIndustryGroupType" json:"gics_industry_group_type,omitempty"`
	GicsIndustryType         GICSIndustryType      `protobuf:"varint,8,opt,name=gics_industry_type,json=gicsIndustryType,proto3,enum=api.securities.catalog.GICSIndustryType" json:"gics_industry_type,omitempty"`
	StockDescription         string                `protobuf:"bytes,9,opt,name=stock_description,json=stockDescription,proto3" json:"stock_description,omitempty"`
}

func (x *StockDetails) Reset() {
	*x = StockDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockDetails) ProtoMessage() {}

func (x *StockDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockDetails.ProtoReflect.Descriptor instead.
func (*StockDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{2}
}

func (x *StockDetails) GetStockName() string {
	if x != nil {
		return x.StockName
	}
	return ""
}

func (x *StockDetails) GetStockShortName() string {
	if x != nil {
		return x.StockShortName
	}
	return ""
}

func (x *StockDetails) GetWebsiteUrl() string {
	if x != nil {
		return x.WebsiteUrl
	}
	return ""
}

func (x *StockDetails) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *StockDetails) GetIncorporationCountryName() string {
	if x != nil {
		return x.IncorporationCountryName
	}
	return ""
}

func (x *StockDetails) GetGicsSectorType() GICSSectorType {
	if x != nil {
		return x.GicsSectorType
	}
	return GICSSectorType_GICS_SECTOR_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetGicsIndustryGroupType() GICSIndustryGroupType {
	if x != nil {
		return x.GicsIndustryGroupType
	}
	return GICSIndustryGroupType_GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetGicsIndustryType() GICSIndustryType {
	if x != nil {
		return x.GicsIndustryType
	}
	return GICSIndustryType_GICS_INDUSTRY_TYPE_UNSPECIFIED
}

func (x *StockDetails) GetStockDescription() string {
	if x != nil {
		return x.StockDescription
	}
	return ""
}

// FundDetails contains fund related information and metadata
type FundDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FundName             string                `protobuf:"bytes,1,opt,name=fund_name,json=fundName,proto3" json:"fund_name,omitempty"`
	FundNameShort        string                `protobuf:"bytes,2,opt,name=fund_name_short,json=fundNameShort,proto3" json:"fund_name_short,omitempty"`
	RegionName           string                `protobuf:"bytes,3,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	CountryName          string                `protobuf:"bytes,4,opt,name=country_name,json=countryName,proto3" json:"country_name,omitempty"`
	BenchmarkName        string                `protobuf:"bytes,5,opt,name=benchmark_name,json=benchmarkName,proto3" json:"benchmark_name,omitempty"`
	BenchmarkNameShort   string                `protobuf:"bytes,6,opt,name=benchmark_name_short,json=benchmarkNameShort,proto3" json:"benchmark_name_short,omitempty"`
	EtfHoldings          *Holdings             `protobuf:"bytes,7,opt,name=etf_holdings,json=etfHoldings,proto3" json:"etf_holdings,omitempty"`
	EquitySectorHoldings *EquitySectorHoldings `protobuf:"bytes,8,opt,name=equity_sector_holdings,json=equitySectorHoldings,proto3" json:"equity_sector_holdings,omitempty"`
	FundDescription      string                `protobuf:"bytes,9,opt,name=fund_description,json=fundDescription,proto3" json:"fund_description,omitempty"`
}

func (x *FundDetails) Reset() {
	*x = FundDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundDetails) ProtoMessage() {}

func (x *FundDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundDetails.ProtoReflect.Descriptor instead.
func (*FundDetails) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{3}
}

func (x *FundDetails) GetFundName() string {
	if x != nil {
		return x.FundName
	}
	return ""
}

func (x *FundDetails) GetFundNameShort() string {
	if x != nil {
		return x.FundNameShort
	}
	return ""
}

func (x *FundDetails) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *FundDetails) GetCountryName() string {
	if x != nil {
		return x.CountryName
	}
	return ""
}

func (x *FundDetails) GetBenchmarkName() string {
	if x != nil {
		return x.BenchmarkName
	}
	return ""
}

func (x *FundDetails) GetBenchmarkNameShort() string {
	if x != nil {
		return x.BenchmarkNameShort
	}
	return ""
}

func (x *FundDetails) GetEtfHoldings() *Holdings {
	if x != nil {
		return x.EtfHoldings
	}
	return nil
}

func (x *FundDetails) GetEquitySectorHoldings() *EquitySectorHoldings {
	if x != nil {
		return x.EquitySectorHoldings
	}
	return nil
}

func (x *FundDetails) GetFundDescription() string {
	if x != nil {
		return x.FundDescription
	}
	return ""
}

// Holdings are the securities which constitute the ETF
type Holdings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Key is the security name and value would be it's weight in the fund
	Holdings map[string]float64 `protobuf:"bytes,1,rep,name=holdings,proto3" json:"holdings,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
}

func (x *Holdings) Reset() {
	*x = Holdings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Holdings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Holdings) ProtoMessage() {}

func (x *Holdings) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Holdings.ProtoReflect.Descriptor instead.
func (*Holdings) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{4}
}

func (x *Holdings) GetHoldings() map[string]float64 {
	if x != nil {
		return x.Holdings
	}
	return nil
}

type EquitySectorHoldings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// key would be string value of GICSSectorType enum and value would be percentage of this sector
	EquitySectors map[string]float64 `protobuf:"bytes,1,rep,name=equity_sectors,json=equitySectors,proto3" json:"equity_sectors,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"fixed64,2,opt,name=value,proto3"`
}

func (x *EquitySectorHoldings) Reset() {
	*x = EquitySectorHoldings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EquitySectorHoldings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EquitySectorHoldings) ProtoMessage() {}

func (x *EquitySectorHoldings) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EquitySectorHoldings.ProtoReflect.Descriptor instead.
func (*EquitySectorHoldings) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{5}
}

func (x *EquitySectorHoldings) GetEquitySectors() map[string]float64 {
	if x != nil {
		return x.EquitySectors
	}
	return nil
}

// FinancialInfo contains the financial information for the security
type FinancialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Market Capitalization (Market Cap) is the total market value of a security's outstanding shares.
	// It is calculated as share price times the number of shares outstanding and is used to measure a company's size and investment risk.
	MarketCap *money.Money `protobuf:"bytes,1,opt,name=market_cap,json=marketCap,proto3" json:"market_cap,omitempty"`
	// Fundamental parameters for the security based on the latest trailing twelve months (TTM) data
	//
	// Deprecated: Marked as deprecated in api/securities/catalog/model_security.proto.
	TtmFundamentalParameters *FundamentalParameters `protobuf:"bytes,2,opt,name=ttm_fundamental_parameters,json=ttmFundamentalParameters,proto3" json:"ttm_fundamental_parameters,omitempty"`
	// FinancialParameters contains the financial data for a security.
	FinancialParameters *FinancialParameters `protobuf:"bytes,3,opt,name=financial_parameters,json=financialParameters,proto3" json:"financial_parameters,omitempty"`
}

func (x *FinancialInfo) Reset() {
	*x = FinancialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialInfo) ProtoMessage() {}

func (x *FinancialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialInfo.ProtoReflect.Descriptor instead.
func (*FinancialInfo) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{6}
}

func (x *FinancialInfo) GetMarketCap() *money.Money {
	if x != nil {
		return x.MarketCap
	}
	return nil
}

// Deprecated: Marked as deprecated in api/securities/catalog/model_security.proto.
func (x *FinancialInfo) GetTtmFundamentalParameters() *FundamentalParameters {
	if x != nil {
		return x.TtmFundamentalParameters
	}
	return nil
}

func (x *FinancialInfo) GetFinancialParameters() *FinancialParameters {
	if x != nil {
		return x.FinancialParameters
	}
	return nil
}

// FinancialParameters represents the financial data for a security.
type FinancialParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Types that are assignable to FinancialInfo:
	//
	//	*FinancialParameters_StockFinancialInfo
	//	*FinancialParameters_FundFinancialInfo
	FinancialInfo isFinancialParameters_FinancialInfo `protobuf_oneof:"financial_info"`
}

func (x *FinancialParameters) Reset() {
	*x = FinancialParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialParameters) ProtoMessage() {}

func (x *FinancialParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialParameters.ProtoReflect.Descriptor instead.
func (*FinancialParameters) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{7}
}

func (m *FinancialParameters) GetFinancialInfo() isFinancialParameters_FinancialInfo {
	if m != nil {
		return m.FinancialInfo
	}
	return nil
}

func (x *FinancialParameters) GetStockFinancialInfo() *StockFinancialInfo {
	if x, ok := x.GetFinancialInfo().(*FinancialParameters_StockFinancialInfo); ok {
		return x.StockFinancialInfo
	}
	return nil
}

func (x *FinancialParameters) GetFundFinancialInfo() *FundFinancialInfo {
	if x, ok := x.GetFinancialInfo().(*FinancialParameters_FundFinancialInfo); ok {
		return x.FundFinancialInfo
	}
	return nil
}

type isFinancialParameters_FinancialInfo interface {
	isFinancialParameters_FinancialInfo()
}

type FinancialParameters_StockFinancialInfo struct {
	// Financial data specific to individual stocks (equities)
	StockFinancialInfo *StockFinancialInfo `protobuf:"bytes,1,opt,name=stock_financial_info,json=stockFinancialInfo,proto3,oneof"`
}

type FinancialParameters_FundFinancialInfo struct {
	// Financial data specific to ETFs
	FundFinancialInfo *FundFinancialInfo `protobuf:"bytes,2,opt,name=fund_financial_info,json=fundFinancialInfo,proto3,oneof"`
}

func (*FinancialParameters_StockFinancialInfo) isFinancialParameters_FinancialInfo() {}

func (*FinancialParameters_FundFinancialInfo) isFinancialParameters_FinancialInfo() {}

// StockFinancialInfo represents a comprehensive snapshot of a stock's financial information, including statements and financial ratios at both quarterly and yearly frequencies.
type StockFinancialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Quarterly and yearly balance sheets
	QuarterlyBalanceSheets []*BalanceSheet `protobuf:"bytes,1,rep,name=quarterly_balance_sheets,json=quarterlyBalanceSheets,proto3" json:"quarterly_balance_sheets,omitempty"`
	YearlyBalanceSheets    []*BalanceSheet `protobuf:"bytes,2,rep,name=yearly_balance_sheets,json=yearlyBalanceSheets,proto3" json:"yearly_balance_sheets,omitempty"`
	// Quarterly and yearly cash flow statements
	QuarterlyCashFlowStatements []*CashFlowStatement `protobuf:"bytes,3,rep,name=quarterly_cash_flow_statements,json=quarterlyCashFlowStatements,proto3" json:"quarterly_cash_flow_statements,omitempty"`
	YearlyCashFlowStatements    []*CashFlowStatement `protobuf:"bytes,4,rep,name=yearly_cash_flow_statements,json=yearlyCashFlowStatements,proto3" json:"yearly_cash_flow_statements,omitempty"`
	// Quarterly and yearly income statements
	QuarterlyIncomeStatements []*IncomeStatement `protobuf:"bytes,5,rep,name=quarterly_income_statements,json=quarterlyIncomeStatements,proto3" json:"quarterly_income_statements,omitempty"`
	YearlyIncomeStatements    []*IncomeStatement `protobuf:"bytes,6,rep,name=yearly_income_statements,json=yearlyIncomeStatements,proto3" json:"yearly_income_statements,omitempty"`
	// Profitability ratios (e.g., net margin, gross margin)
	QuarterlyProfitabilityRatios []*ProfitabilityRatio `protobuf:"bytes,7,rep,name=quarterly_profitability_ratios,json=quarterlyProfitabilityRatios,proto3" json:"quarterly_profitability_ratios,omitempty"`
	YearlyProfitabilityRatios    []*ProfitabilityRatio `protobuf:"bytes,8,rep,name=yearly_profitability_ratios,json=yearlyProfitabilityRatios,proto3" json:"yearly_profitability_ratios,omitempty"`
	// Efficiency ratios (e.g., ROE, ROTC)
	QuarterlyEfficiencyRatios []*EfficiencyRatio `protobuf:"bytes,9,rep,name=quarterly_efficiency_ratios,json=quarterlyEfficiencyRatios,proto3" json:"quarterly_efficiency_ratios,omitempty"`
	YearlyEfficiencyRatios    []*EfficiencyRatio `protobuf:"bytes,10,rep,name=yearly_efficiency_ratios,json=yearlyEfficiencyRatios,proto3" json:"yearly_efficiency_ratios,omitempty"`
	// Financial health ratios (e.g., debt-to-equity)
	QuarterlyFinancialHealthRatios []*FinancialHealthRatio `protobuf:"bytes,11,rep,name=quarterly_financial_health_ratios,json=quarterlyFinancialHealthRatios,proto3" json:"quarterly_financial_health_ratios,omitempty"`
	YearlyFinancialHealthRatios    []*FinancialHealthRatio `protobuf:"bytes,12,rep,name=yearly_financial_health_ratios,json=yearlyFinancialHealthRatios,proto3" json:"yearly_financial_health_ratios,omitempty"`
	// Growth ratios (e.g., revenue growth, EPS growth)
	QuarterlyGrowthRatios []*GrowthRatio `protobuf:"bytes,13,rep,name=quarterly_growth_ratios,json=quarterlyGrowthRatios,proto3" json:"quarterly_growth_ratios,omitempty"`
	YearlyGrowthRatios    []*GrowthRatio `protobuf:"bytes,14,rep,name=yearly_growth_ratios,json=yearlyGrowthRatios,proto3" json:"yearly_growth_ratios,omitempty"`
	// Valuation ratios (e.g., P/E, P/B, EV/EBITDA)
	QuarterlyValuationRatios []*ValuationRatio      `protobuf:"bytes,15,rep,name=quarterly_valuation_ratios,json=quarterlyValuationRatios,proto3" json:"quarterly_valuation_ratios,omitempty"`
	YearlyValuationRatios    []*ValuationRatio      `protobuf:"bytes,16,rep,name=yearly_valuation_ratios,json=yearlyValuationRatios,proto3" json:"yearly_valuation_ratios,omitempty"`
	QuarterlyLastUpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=quarterly_last_updated_at,json=quarterlyLastUpdatedAt,proto3" json:"quarterly_last_updated_at,omitempty"`
	YearlyLastUpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=yearly_last_updated_at,json=yearlyLastUpdatedAt,proto3" json:"yearly_last_updated_at,omitempty"`
}

func (x *StockFinancialInfo) Reset() {
	*x = StockFinancialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StockFinancialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StockFinancialInfo) ProtoMessage() {}

func (x *StockFinancialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StockFinancialInfo.ProtoReflect.Descriptor instead.
func (*StockFinancialInfo) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{8}
}

func (x *StockFinancialInfo) GetQuarterlyBalanceSheets() []*BalanceSheet {
	if x != nil {
		return x.QuarterlyBalanceSheets
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyBalanceSheets() []*BalanceSheet {
	if x != nil {
		return x.YearlyBalanceSheets
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyCashFlowStatements() []*CashFlowStatement {
	if x != nil {
		return x.QuarterlyCashFlowStatements
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyCashFlowStatements() []*CashFlowStatement {
	if x != nil {
		return x.YearlyCashFlowStatements
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyIncomeStatements() []*IncomeStatement {
	if x != nil {
		return x.QuarterlyIncomeStatements
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyIncomeStatements() []*IncomeStatement {
	if x != nil {
		return x.YearlyIncomeStatements
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyProfitabilityRatios() []*ProfitabilityRatio {
	if x != nil {
		return x.QuarterlyProfitabilityRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyProfitabilityRatios() []*ProfitabilityRatio {
	if x != nil {
		return x.YearlyProfitabilityRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyEfficiencyRatios() []*EfficiencyRatio {
	if x != nil {
		return x.QuarterlyEfficiencyRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyEfficiencyRatios() []*EfficiencyRatio {
	if x != nil {
		return x.YearlyEfficiencyRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyFinancialHealthRatios() []*FinancialHealthRatio {
	if x != nil {
		return x.QuarterlyFinancialHealthRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyFinancialHealthRatios() []*FinancialHealthRatio {
	if x != nil {
		return x.YearlyFinancialHealthRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyGrowthRatios() []*GrowthRatio {
	if x != nil {
		return x.QuarterlyGrowthRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyGrowthRatios() []*GrowthRatio {
	if x != nil {
		return x.YearlyGrowthRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyValuationRatios() []*ValuationRatio {
	if x != nil {
		return x.QuarterlyValuationRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyValuationRatios() []*ValuationRatio {
	if x != nil {
		return x.YearlyValuationRatios
	}
	return nil
}

func (x *StockFinancialInfo) GetQuarterlyLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.QuarterlyLastUpdatedAt
	}
	return nil
}

func (x *StockFinancialInfo) GetYearlyLastUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.YearlyLastUpdatedAt
	}
	return nil
}

// FundFinancialInfo contains financial details of an ETF. These details may represent either an aggregate of the underlying assets
// (such as stocks or commodities), or the difference between the fund's metrics and a benchmark or index—such as tracking error or NAV deviation.
type FundFinancialInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Tracking Error measures the degree to which the fund's performance deviates from the performance of its benchmark index
	TrackingErrorPercentage float64 `protobuf:"fixed64,1,opt,name=tracking_error_percentage,json=trackingErrorPercentage,proto3" json:"tracking_error_percentage,omitempty"`
	// Expense Ratio is the percentage value of the annual fee that the fund charges its investors
	ExpenseRatioPercentage float64 `protobuf:"fixed64,2,opt,name=expense_ratio_percentage,json=expenseRatioPercentage,proto3" json:"expense_ratio_percentage,omitempty"`
	// The daily average trading volume over the past three months, in trading currency value terms. This metric provides insights into the liquidity of the fund.
	TradingVolumeAverage float64 `protobuf:"fixed64,3,opt,name=trading_volume_average,json=tradingVolumeAverage,proto3" json:"trading_volume_average,omitempty"`
	// Sharpe Ratio measures the risk-adjusted return of the fund.
	// It helps investors understand how much excess return they are receiving for the extra volatility they endure for holding a riskier asset compared to a risk-free asset
	SharpeRatio *SharpeRatio `protobuf:"bytes,4,opt,name=sharpe_ratio,json=sharpeRatio,proto3" json:"sharpe_ratio,omitempty"`
	// Measures the excess return of a fund relative to its expected return
	Alpha *Alpha `protobuf:"bytes,5,opt,name=alpha,proto3" json:"alpha,omitempty"`
	// Measures the fund's volatility relative to the benchmark. It indicates how much the fund's returns are expected to move in relation to the benchmark's returns
	Beta *Beta `protobuf:"bytes,6,opt,name=beta,proto3" json:"beta,omitempty"`
}

func (x *FundFinancialInfo) Reset() {
	*x = FundFinancialInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundFinancialInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundFinancialInfo) ProtoMessage() {}

func (x *FundFinancialInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundFinancialInfo.ProtoReflect.Descriptor instead.
func (*FundFinancialInfo) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{9}
}

func (x *FundFinancialInfo) GetTrackingErrorPercentage() float64 {
	if x != nil {
		return x.TrackingErrorPercentage
	}
	return 0
}

func (x *FundFinancialInfo) GetExpenseRatioPercentage() float64 {
	if x != nil {
		return x.ExpenseRatioPercentage
	}
	return 0
}

func (x *FundFinancialInfo) GetTradingVolumeAverage() float64 {
	if x != nil {
		return x.TradingVolumeAverage
	}
	return 0
}

func (x *FundFinancialInfo) GetSharpeRatio() *SharpeRatio {
	if x != nil {
		return x.SharpeRatio
	}
	return nil
}

func (x *FundFinancialInfo) GetAlpha() *Alpha {
	if x != nil {
		return x.Alpha
	}
	return nil
}

func (x *FundFinancialInfo) GetBeta() *Beta {
	if x != nil {
		return x.Beta
	}
	return nil
}

type SharpeRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OneYear   float32 `protobuf:"fixed32,1,opt,name=one_year,json=oneYear,proto3" json:"one_year,omitempty"`
	ThreeYear float32 `protobuf:"fixed32,2,opt,name=three_year,json=threeYear,proto3" json:"three_year,omitempty"`
}

func (x *SharpeRatio) Reset() {
	*x = SharpeRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SharpeRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SharpeRatio) ProtoMessage() {}

func (x *SharpeRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SharpeRatio.ProtoReflect.Descriptor instead.
func (*SharpeRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{10}
}

func (x *SharpeRatio) GetOneYear() float32 {
	if x != nil {
		return x.OneYear
	}
	return 0
}

func (x *SharpeRatio) GetThreeYear() float32 {
	if x != nil {
		return x.ThreeYear
	}
	return 0
}

type Alpha struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OneYear   float32 `protobuf:"fixed32,1,opt,name=one_year,json=oneYear,proto3" json:"one_year,omitempty"`
	ThreeYear float32 `protobuf:"fixed32,2,opt,name=three_year,json=threeYear,proto3" json:"three_year,omitempty"`
}

func (x *Alpha) Reset() {
	*x = Alpha{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Alpha) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Alpha) ProtoMessage() {}

func (x *Alpha) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Alpha.ProtoReflect.Descriptor instead.
func (*Alpha) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{11}
}

func (x *Alpha) GetOneYear() float32 {
	if x != nil {
		return x.OneYear
	}
	return 0
}

func (x *Alpha) GetThreeYear() float32 {
	if x != nil {
		return x.ThreeYear
	}
	return 0
}

type Beta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OneYear   float32 `protobuf:"fixed32,1,opt,name=one_year,json=oneYear,proto3" json:"one_year,omitempty"`
	ThreeYear float32 `protobuf:"fixed32,2,opt,name=three_year,json=threeYear,proto3" json:"three_year,omitempty"`
}

func (x *Beta) Reset() {
	*x = Beta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Beta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Beta) ProtoMessage() {}

func (x *Beta) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Beta.ProtoReflect.Descriptor instead.
func (*Beta) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{12}
}

func (x *Beta) GetOneYear() float32 {
	if x != nil {
		return x.OneYear
	}
	return 0
}

func (x *Beta) GetThreeYear() float32 {
	if x != nil {
		return x.ThreeYear
	}
	return 0
}

type FundamentalParameters struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Book Value Per Share (BVPS) represents the equity available to common shareholders divided by the number of outstanding shares.
	// It indicates the per-share value of a company's net assets and is used to assess whether a stock is undervalued or overvalued compared to its market price.
	BookValuePerShare *money.Money `protobuf:"bytes,1,opt,name=book_value_per_share,json=bookValuePerShare,proto3" json:"book_value_per_share,omitempty"`
	// Price-to-Earnings (P/E) Ratio is calculated as the market price per share divided by earnings per share.
	// It measures how much investors are willing to pay for each dollar of earnings and is widely used to value companies and compare them across industries.
	PeRatio float64 `protobuf:"fixed64,2,opt,name=pe_ratio,json=peRatio,proto3" json:"pe_ratio,omitempty"`
	// Price-to-Book (P/B) Ratio compares a company's market value to its book value. It is calculated as the market price per share divided by book value per share.
	// A lower P/B ratio may indicate an undervalued stock, while a higher ratio may suggest overvaluation.
	PbRatio float64 `protobuf:"fixed64,3,opt,name=pb_ratio,json=pbRatio,proto3" json:"pb_ratio,omitempty"`
	// Dividend Yield is the ratio of a company's annual dividend per share to its share price. It shows the return on investment from dividends alone and is important for income-focused investors.
	DividendYield float64 `protobuf:"fixed64,4,opt,name=dividend_yield,json=dividendYield,proto3" json:"dividend_yield,omitempty"`
	// Return on Equity (ROE) measures a company's profitability by showing how much profit it generates with the money shareholders have invested. It is calculated as net income divided by shareholder equity.
	ReturnOnEquity float64 `protobuf:"fixed64,5,opt,name=return_on_equity,json=returnOnEquity,proto3" json:"return_on_equity,omitempty"`
	// Shares Outstanding is the total number of a company's shares that are currently held by all its shareholders. It is used in the calculation of metrics like earnings per share and book value per share.
	SharesOutstanding float64 `protobuf:"fixed64,6,opt,name=shares_outstanding,json=sharesOutstanding,proto3" json:"shares_outstanding,omitempty"`
	// Sharpe Ratio measures the risk-adjusted return of the fund.
	// It helps investors understand how much excess return they are receiving for the extra volatility they endure for holding a riskier asset compared to a risk-free asset
	SharpeRatio float64 `protobuf:"fixed64,7,opt,name=sharpe_ratio,json=sharpeRatio,proto3" json:"sharpe_ratio,omitempty"`
	// Tracking Error measures the degree to which the fund's performance deviates from the performance of its benchmark index
	TrackingErrorPercentage float64 `protobuf:"fixed64,8,opt,name=tracking_error_percentage,json=trackingErrorPercentage,proto3" json:"tracking_error_percentage,omitempty"`
	// Expense Ratio is the percentage value of the annual fee that the fund charges its investors
	ExpenseRatioPercentage float64 `protobuf:"fixed64,9,opt,name=expense_ratio_percentage,json=expenseRatioPercentage,proto3" json:"expense_ratio_percentage,omitempty"`
}

func (x *FundamentalParameters) Reset() {
	*x = FundamentalParameters{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FundamentalParameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FundamentalParameters) ProtoMessage() {}

func (x *FundamentalParameters) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FundamentalParameters.ProtoReflect.Descriptor instead.
func (*FundamentalParameters) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{13}
}

func (x *FundamentalParameters) GetBookValuePerShare() *money.Money {
	if x != nil {
		return x.BookValuePerShare
	}
	return nil
}

func (x *FundamentalParameters) GetPeRatio() float64 {
	if x != nil {
		return x.PeRatio
	}
	return 0
}

func (x *FundamentalParameters) GetPbRatio() float64 {
	if x != nil {
		return x.PbRatio
	}
	return 0
}

func (x *FundamentalParameters) GetDividendYield() float64 {
	if x != nil {
		return x.DividendYield
	}
	return 0
}

func (x *FundamentalParameters) GetReturnOnEquity() float64 {
	if x != nil {
		return x.ReturnOnEquity
	}
	return 0
}

func (x *FundamentalParameters) GetSharesOutstanding() float64 {
	if x != nil {
		return x.SharesOutstanding
	}
	return 0
}

func (x *FundamentalParameters) GetSharpeRatio() float64 {
	if x != nil {
		return x.SharpeRatio
	}
	return 0
}

func (x *FundamentalParameters) GetTrackingErrorPercentage() float64 {
	if x != nil {
		return x.TrackingErrorPercentage
	}
	return 0
}

func (x *FundamentalParameters) GetExpenseRatioPercentage() float64 {
	if x != nil {
		return x.ExpenseRatioPercentage
	}
	return 0
}

// IncomeStatement reports a company’s financial performance over a specific time period
// ref: https://www.investopedia.com/terms/i/incomestatement.asp
type IncomeStatement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// total revenue, ref: https://www.investopedia.com/terms/r/revenue.asp
	// diff b/w revenue and income: https://www.investopedia.com/ask/answers/122214/what-difference-between-revenue-and-income.asp
	TotalRevenue *typesv2.Money `protobuf:"bytes,1,opt,name=total_revenue,json=totalRevenue,proto3" json:"total_revenue,omitempty"`
	// expenses incurred by company
	TotalExpenses *typesv2.Money `protobuf:"bytes,2,opt,name=total_expenses,json=totalExpenses,proto3" json:"total_expenses,omitempty"`
	// profit a company makes after deducting the costs associated with making and selling its products, or the costs associated with providing its services
	// ref: https://www.investopedia.com/terms/g/grossprofit.asp
	GrossProfit *typesv2.Money `protobuf:"bytes,3,opt,name=gross_profit,json=grossProfit,proto3" json:"gross_profit,omitempty"`
	// net income after taxes, ref: https://www.investopedia.com/terms/n/net-income-after-taxes-niat.asp
	// diff b/w revenue and income: https://www.investopedia.com/ask/answers/122214/what-difference-between-revenue-and-income.asp
	NetIncome *typesv2.Money `protobuf:"bytes,4,opt,name=net_income,json=netIncome,proto3" json:"net_income,omitempty"`
	// earnings before interest, taxes, depreciation, and amortization, ref: https://www.investopedia.com/terms/e/ebitda.asp
	Ebitda          *typesv2.Money `protobuf:"bytes,5,opt,name=ebitda,proto3" json:"ebitda,omitempty"`
	CalendarYear    int32          `protobuf:"varint,6,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32          `protobuf:"varint,7,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date  `protobuf:"bytes,8,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *IncomeStatement) Reset() {
	*x = IncomeStatement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IncomeStatement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IncomeStatement) ProtoMessage() {}

func (x *IncomeStatement) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IncomeStatement.ProtoReflect.Descriptor instead.
func (*IncomeStatement) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{14}
}

func (x *IncomeStatement) GetTotalRevenue() *typesv2.Money {
	if x != nil {
		return x.TotalRevenue
	}
	return nil
}

func (x *IncomeStatement) GetTotalExpenses() *typesv2.Money {
	if x != nil {
		return x.TotalExpenses
	}
	return nil
}

func (x *IncomeStatement) GetGrossProfit() *typesv2.Money {
	if x != nil {
		return x.GrossProfit
	}
	return nil
}

func (x *IncomeStatement) GetNetIncome() *typesv2.Money {
	if x != nil {
		return x.NetIncome
	}
	return nil
}

func (x *IncomeStatement) GetEbitda() *typesv2.Money {
	if x != nil {
		return x.Ebitda
	}
	return nil
}

func (x *IncomeStatement) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *IncomeStatement) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *IncomeStatement) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// BalanceSheet reports a company's assets, liabilities, and working capital at a specific point in time
// ref: https://www.investopedia.com/terms/b/balancesheet.asp
type BalanceSheet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cash, accounts receivable/customers’ unpaid bills, and inventories of raw materials and finished goods
	TotalAssets *typesv2.Money `protobuf:"bytes,1,opt,name=total_assets,json=totalAssets,proto3" json:"total_assets,omitempty"`
	// accounts payable and debts
	TotalLiabilities *typesv2.Money `protobuf:"bytes,2,opt,name=total_liabilities,json=totalLiabilities,proto3" json:"total_liabilities,omitempty"`
	// ratio between total_assets and total_liabilities;
	CurrentRatio    float64       `protobuf:"fixed64,3,opt,name=current_ratio,json=currentRatio,proto3" json:"current_ratio,omitempty"`
	CalendarYear    int32         `protobuf:"varint,4,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32         `protobuf:"varint,5,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date `protobuf:"bytes,6,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *BalanceSheet) Reset() {
	*x = BalanceSheet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BalanceSheet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BalanceSheet) ProtoMessage() {}

func (x *BalanceSheet) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BalanceSheet.ProtoReflect.Descriptor instead.
func (*BalanceSheet) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{15}
}

func (x *BalanceSheet) GetTotalAssets() *typesv2.Money {
	if x != nil {
		return x.TotalAssets
	}
	return nil
}

func (x *BalanceSheet) GetTotalLiabilities() *typesv2.Money {
	if x != nil {
		return x.TotalLiabilities
	}
	return nil
}

func (x *BalanceSheet) GetCurrentRatio() float64 {
	if x != nil {
		return x.CurrentRatio
	}
	return 0
}

func (x *BalanceSheet) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *BalanceSheet) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *BalanceSheet) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// CashFlowStatement summarizes the movement of cash and cash equivalents in and out of a company
// ref: https://www.investopedia.com/investing/what-is-a-cash-flow-statement/
type CashFlowStatement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cash generated from business operations before interest payments and after subtracting capital expenditures
	FreeCashFlow *typesv2.Money `protobuf:"bytes,1,opt,name=free_cash_flow,json=freeCashFlow,proto3" json:"free_cash_flow,omitempty"`
	// cash generated from business operations or activities
	OperatingCashFlow *typesv2.Money `protobuf:"bytes,2,opt,name=operating_cash_flow,json=operatingCashFlow,proto3" json:"operating_cash_flow,omitempty"`
	// cash generated or spent from various investment-related activities
	InvestingCashFlow *typesv2.Money `protobuf:"bytes,3,opt,name=investing_cash_flow,json=investingCashFlow,proto3" json:"investing_cash_flow,omitempty"`
	// net flows of cash that are used to fund the company and its capital
	FinancingCashFlow *typesv2.Money `protobuf:"bytes,4,opt,name=financing_cash_flow,json=financingCashFlow,proto3" json:"financing_cash_flow,omitempty"`
	CalendarYear      int32          `protobuf:"varint,5,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter   int32          `protobuf:"varint,6,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate        *typesv2.Date  `protobuf:"bytes,7,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *CashFlowStatement) Reset() {
	*x = CashFlowStatement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CashFlowStatement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CashFlowStatement) ProtoMessage() {}

func (x *CashFlowStatement) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CashFlowStatement.ProtoReflect.Descriptor instead.
func (*CashFlowStatement) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{16}
}

func (x *CashFlowStatement) GetFreeCashFlow() *typesv2.Money {
	if x != nil {
		return x.FreeCashFlow
	}
	return nil
}

func (x *CashFlowStatement) GetOperatingCashFlow() *typesv2.Money {
	if x != nil {
		return x.OperatingCashFlow
	}
	return nil
}

func (x *CashFlowStatement) GetInvestingCashFlow() *typesv2.Money {
	if x != nil {
		return x.InvestingCashFlow
	}
	return nil
}

func (x *CashFlowStatement) GetFinancingCashFlow() *typesv2.Money {
	if x != nil {
		return x.FinancingCashFlow
	}
	return nil
}

func (x *CashFlowStatement) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *CashFlowStatement) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *CashFlowStatement) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// ProfitabilityRatio indicates profit margins of the company
type ProfitabilityRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Gross Margin: refers to the ratio of gross profit to revenue.
	GrossMargin float64 `protobuf:"fixed64,1,opt,name=gross_margin,json=grossMargin,proto3" json:"gross_margin,omitempty"`
	// EBITDA Margin: refers to the ratio of earnings before interest, taxes and depreciation and amortization to revenue
	EbitdaMargin float64 `protobuf:"fixed64,2,opt,name=ebitda_margin,json=ebitdaMargin,proto3" json:"ebitda_margin,omitempty"`
	// Net Margin: refers to the ratio of net income to revenue
	NetMargin       float64       `protobuf:"fixed64,3,opt,name=net_margin,json=netMargin,proto3" json:"net_margin,omitempty"`
	CalendarYear    int32         `protobuf:"varint,4,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32         `protobuf:"varint,5,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date `protobuf:"bytes,6,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *ProfitabilityRatio) Reset() {
	*x = ProfitabilityRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ProfitabilityRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProfitabilityRatio) ProtoMessage() {}

func (x *ProfitabilityRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProfitabilityRatio.ProtoReflect.Descriptor instead.
func (*ProfitabilityRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{17}
}

func (x *ProfitabilityRatio) GetGrossMargin() float64 {
	if x != nil {
		return x.GrossMargin
	}
	return 0
}

func (x *ProfitabilityRatio) GetEbitdaMargin() float64 {
	if x != nil {
		return x.EbitdaMargin
	}
	return 0
}

func (x *ProfitabilityRatio) GetNetMargin() float64 {
	if x != nil {
		return x.NetMargin
	}
	return 0
}

func (x *ProfitabilityRatio) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *ProfitabilityRatio) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *ProfitabilityRatio) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// EfficiencyRatio indicates efficiency of the company stock in market
type EfficiencyRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Return on Equity, ref: https://www.investopedia.com/terms/r/returnonequity.asp
	Roe float64 `protobuf:"fixed64,1,opt,name=roe,proto3" json:"roe,omitempty"`
	// Return on Total Capital
	Rotc            float64       `protobuf:"fixed64,2,opt,name=rotc,proto3" json:"rotc,omitempty"`
	CalendarYear    int32         `protobuf:"varint,3,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32         `protobuf:"varint,4,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date `protobuf:"bytes,5,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *EfficiencyRatio) Reset() {
	*x = EfficiencyRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EfficiencyRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EfficiencyRatio) ProtoMessage() {}

func (x *EfficiencyRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EfficiencyRatio.ProtoReflect.Descriptor instead.
func (*EfficiencyRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{18}
}

func (x *EfficiencyRatio) GetRoe() float64 {
	if x != nil {
		return x.Roe
	}
	return 0
}

func (x *EfficiencyRatio) GetRotc() float64 {
	if x != nil {
		return x.Rotc
	}
	return 0
}

func (x *EfficiencyRatio) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *EfficiencyRatio) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *EfficiencyRatio) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// FinancialHealthRatio indicates financial health of the company
type FinancialHealthRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// debt to equity ratio of company, ref: https://www.investopedia.com/terms/d/debtequityratio.asp
	TotalDebtToEquity float64       `protobuf:"fixed64,1,opt,name=total_debt_to_equity,json=totalDebtToEquity,proto3" json:"total_debt_to_equity,omitempty"`
	CalendarYear      int32         `protobuf:"varint,2,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter   int32         `protobuf:"varint,3,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate        *typesv2.Date `protobuf:"bytes,4,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *FinancialHealthRatio) Reset() {
	*x = FinancialHealthRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FinancialHealthRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FinancialHealthRatio) ProtoMessage() {}

func (x *FinancialHealthRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FinancialHealthRatio.ProtoReflect.Descriptor instead.
func (*FinancialHealthRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{19}
}

func (x *FinancialHealthRatio) GetTotalDebtToEquity() float64 {
	if x != nil {
		return x.TotalDebtToEquity
	}
	return 0
}

func (x *FinancialHealthRatio) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *FinancialHealthRatio) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *FinancialHealthRatio) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// GrowthRatio indicates historical growth of the company, including growth in revenue, earnings per share, etc. of the company
// For quarterly reporting, last 3 months growth ratios are considered
// For annual reporting, last 1 year growth ratios are considered
type GrowthRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// For quarterly reporting, this indicates 3-month book value per share growth year over year
	// For annual reporting, this indicates annual growth rate of book value per share
	DilutedEpsGrowth float64 `protobuf:"fixed64,1,opt,name=diluted_eps_growth,json=dilutedEpsGrowth,proto3" json:"diluted_eps_growth,omitempty"`
	// For quarterly reporting, this indicates 3-month total revenue growth year over year
	// For annual reporting, this indicates annual growth rate of total revenue over 1 year
	RevenueGrowth   float64       `protobuf:"fixed64,2,opt,name=revenue_growth,json=revenueGrowth,proto3" json:"revenue_growth,omitempty"`
	CalendarYear    int32         `protobuf:"varint,3,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32         `protobuf:"varint,4,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date `protobuf:"bytes,5,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *GrowthRatio) Reset() {
	*x = GrowthRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GrowthRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GrowthRatio) ProtoMessage() {}

func (x *GrowthRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GrowthRatio.ProtoReflect.Descriptor instead.
func (*GrowthRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{20}
}

func (x *GrowthRatio) GetDilutedEpsGrowth() float64 {
	if x != nil {
		return x.DilutedEpsGrowth
	}
	return 0
}

func (x *GrowthRatio) GetRevenueGrowth() float64 {
	if x != nil {
		return x.RevenueGrowth
	}
	return 0
}

func (x *GrowthRatio) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *GrowthRatio) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *GrowthRatio) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

// ValuationRatio provides the valuation of a company, w.r.t. earnings, book value etc. of the company
type ValuationRatio struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Price-to-Earnings (P/E) Ratio is calculated as the market price per share divided by earnings per share.
	// It measures how much investors are willing to pay for each dollar of earnings and is widely used to value companies and compare them across industries.
	PeRatio float64 `protobuf:"fixed64,1,opt,name=pe_ratio,json=peRatio,proto3" json:"pe_ratio,omitempty"`
	// Price-to-Book (P/B) Ratio compares a company's market value to its book value.
	// It is calculated as the market price per share divided by book value per share.
	// A lower P/B ratio may indicate an undervalued stock, while a higher ratio may suggest overvaluation.
	PbRatio float64 `protobuf:"fixed64,2,opt,name=pb_ratio,json=pbRatio,proto3" json:"pb_ratio,omitempty"`
	// Dividend Yield is the ratio of a company's annual dividend per share to its share price.
	// It shows the return on investment from dividends alone and is important for income-focused investors.
	DividendYieldPercentage float64 `protobuf:"fixed64,3,opt,name=dividend_yield_percentage,json=dividendYieldPercentage,proto3" json:"dividend_yield_percentage,omitempty"`
	// Enterprise multiple, ref: https://www.investopedia.com/terms/e/ev-ebitda.asp
	EvToEbitda      float64       `protobuf:"fixed64,4,opt,name=ev_to_ebitda,json=evToEbitda,proto3" json:"ev_to_ebitda,omitempty"`
	CalendarYear    int32         `protobuf:"varint,5,opt,name=calendar_year,json=calendarYear,proto3" json:"calendar_year,omitempty"`
	CalendarQuarter int32         `protobuf:"varint,6,opt,name=calendar_quarter,json=calendarQuarter,proto3" json:"calendar_quarter,omitempty"`
	ReportDate      *typesv2.Date `protobuf:"bytes,7,opt,name=report_date,json=reportDate,proto3" json:"report_date,omitempty"`
}

func (x *ValuationRatio) Reset() {
	*x = ValuationRatio{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_securities_catalog_model_security_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValuationRatio) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValuationRatio) ProtoMessage() {}

func (x *ValuationRatio) ProtoReflect() protoreflect.Message {
	mi := &file_api_securities_catalog_model_security_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValuationRatio.ProtoReflect.Descriptor instead.
func (*ValuationRatio) Descriptor() ([]byte, []int) {
	return file_api_securities_catalog_model_security_proto_rawDescGZIP(), []int{21}
}

func (x *ValuationRatio) GetPeRatio() float64 {
	if x != nil {
		return x.PeRatio
	}
	return 0
}

func (x *ValuationRatio) GetPbRatio() float64 {
	if x != nil {
		return x.PbRatio
	}
	return 0
}

func (x *ValuationRatio) GetDividendYieldPercentage() float64 {
	if x != nil {
		return x.DividendYieldPercentage
	}
	return 0
}

func (x *ValuationRatio) GetEvToEbitda() float64 {
	if x != nil {
		return x.EvToEbitda
	}
	return 0
}

func (x *ValuationRatio) GetCalendarYear() int32 {
	if x != nil {
		return x.CalendarYear
	}
	return 0
}

func (x *ValuationRatio) GetCalendarQuarter() int32 {
	if x != nil {
		return x.CalendarQuarter
	}
	return 0
}

func (x *ValuationRatio) GetReportDate() *typesv2.Date {
	if x != nil {
		return x.ReportDate
	}
	return nil
}

var File_api_securities_catalog_model_security_proto protoreflect.FileDescriptor

var file_api_securities_catalog_model_security_proto_rawDesc = []byte{
	0x0a, 0x2b, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x5f, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x17, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x6d,
	0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd5, 0x04, 0x0a, 0x08, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x49, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0c,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2d, 0x0a, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x52, 0x06, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x12, 0x2c, 0x0a, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x49, 0x64, 0x12, 0x19,
	0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x6f, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x6c, 0x6f, 0x67, 0x6f, 0x55, 0x72, 0x6c, 0x12, 0x52, 0x0a, 0x10, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4c,
	0x0a, 0x0e, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0d, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xb9, 0x01, 0x0a,
	0x0f, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x12, 0x4b, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52,
	0x0c, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x48, 0x0a,
	0x0c, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x75, 0x6e,
	0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x48, 0x00, 0x52, 0x0b, 0x66, 0x75, 0x6e, 0x64,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x22, 0x96, 0x04, 0x0a, 0x0c, 0x53, 0x74, 0x6f,
	0x63, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x74, 0x6f,
	0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73,
	0x74, 0x6f, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x77, 0x65, 0x62, 0x73, 0x69, 0x74, 0x65,
	0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x70, 0x6f, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x69, 0x6e, 0x63, 0x6f, 0x72, 0x70,
	0x6f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x50, 0x0a, 0x10, 0x67, 0x69, 0x63, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x49, 0x43, 0x53, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x67, 0x69, 0x63, 0x73, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x66, 0x0a, 0x18, 0x67, 0x69, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x64,
	0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x47, 0x49, 0x43, 0x53, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x54, 0x79, 0x70, 0x65, 0x52, 0x15, 0x67, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x12,
	0x67, 0x69, 0x63, 0x73, 0x5f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x47, 0x49, 0x43, 0x53, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x10, 0x67, 0x69, 0x63, 0x73, 0x49, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x79,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x22, 0xc3, 0x03, 0x0a, 0x0b, 0x46, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x26,
	0x0a, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x75, 0x6e, 0x64, 0x4e, 0x61, 0x6d,
	0x65, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x67,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x62, 0x65,
	0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x30, 0x0a, 0x14, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x5f, 0x73, 0x68, 0x6f, 0x72, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x12, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x6d, 0x61, 0x72, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x53, 0x68,
	0x6f, 0x72, 0x74, 0x12, 0x43, 0x0a, 0x0c, 0x65, 0x74, 0x66, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69,
	0x6e, 0x67, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0b, 0x65, 0x74, 0x66,
	0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x62, 0x0a, 0x16, 0x65, 0x71, 0x75, 0x69,
	0x74, 0x79, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e,
	0x67, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f,
	0x67, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f,
	0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x14, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x29, 0x0a, 0x10,
	0x66, 0x75, 0x6e, 0x64, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x66, 0x75, 0x6e, 0x64, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x93, 0x01, 0x0a, 0x08, 0x48, 0x6f, 0x6c, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x4a, 0x0a, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x68, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73,
	0x1a, 0x3b, 0x0a, 0x0d, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xc0, 0x01,
	0x0a, 0x14, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f,
	0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x66, 0x0a, 0x0e, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3f,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e,
	0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x48, 0x6f, 0x6c, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x45, 0x71, 0x75,
	0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0d, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x1a, 0x40,
	0x0a, 0x12, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x53, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x93, 0x02, 0x0a, 0x0d, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x72, 0x6b, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x72, 0x6b,
	0x65, 0x74, 0x43, 0x61, 0x70, 0x12, 0x6f, 0x0a, 0x1a, 0x74, 0x74, 0x6d, 0x5f, 0x66, 0x75, 0x6e,
	0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74,
	0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x61,
	0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x42, 0x02, 0x18, 0x01, 0x52, 0x18, 0x74, 0x74,
	0x6d, 0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x5e, 0x0a, 0x14, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63,
	0x69, 0x61, 0x6c, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72,
	0x73, 0x52, 0x13, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x22, 0xe4, 0x01, 0x0a, 0x13, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x5e,
	0x0a, 0x14, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e,
	0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x12, 0x73, 0x74, 0x6f, 0x63,
	0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5b,
	0x0a, 0x13, 0x66, 0x75, 0x6e, 0x64, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x48, 0x00, 0x52, 0x11, 0x66, 0x75, 0x6e, 0x64, 0x46, 0x69,
	0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x42, 0x10, 0x0a, 0x0e, 0x66,
	0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0xac, 0x0e,
	0x0a, 0x12, 0x53, 0x74, 0x6f, 0x63, 0x6b, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5e, 0x0a, 0x18, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c,
	0x79, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x16, 0x71, 0x75,
	0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x68,
	0x65, 0x65, 0x74, 0x73, 0x12, 0x58, 0x0a, 0x15, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x62,
	0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x68, 0x65, 0x65, 0x74, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x52, 0x13, 0x79, 0x65, 0x61, 0x72, 0x6c,
	0x79, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x73, 0x12, 0x6e,
	0x0a, 0x1e, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x63, 0x61, 0x73, 0x68,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e,
	0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x1b, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x43, 0x61, 0x73, 0x68,
	0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x68,
	0x0a, 0x1b, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x66, 0x6c,
	0x6f, 0x77, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x43, 0x61, 0x73,
	0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x18,
	0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x67, 0x0a, 0x1b, 0x71, 0x75, 0x61, 0x72,
	0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x19, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c,
	0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x12, 0x61, 0x0a, 0x18, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x69, 0x6e, 0x63, 0x6f,
	0x6d, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x49, 0x6e, 0x63,
	0x6f, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x16, 0x79, 0x65,
	0x61, 0x72, 0x6c, 0x79, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x70, 0x0a, 0x1e, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c,
	0x79, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61,
	0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c,
	0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x1c, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65,
	0x72, 0x6c, 0x79, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x6a, 0x0a, 0x1b, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79,
	0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x19, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x50,
	0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x61, 0x74, 0x69,
	0x6f, 0x73, 0x12, 0x67, 0x0a, 0x1b, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f,
	0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x52, 0x19, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x45, 0x66, 0x66, 0x69, 0x63,
	0x69, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x61, 0x0a, 0x18, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x65, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63,
	0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63,
	0x79, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x16, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x45, 0x66,
	0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x77,
	0x0a, 0x21, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x66, 0x69, 0x6e, 0x61,
	0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x73, 0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x1e, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72,
	0x6c, 0x79, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x71, 0x0a, 0x1e, 0x79, 0x65, 0x61, 0x72, 0x6c,
	0x79, 0x5f, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x68, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x61, 0x6c, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x1b, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x48, 0x65,
	0x61, 0x6c, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x5b, 0x0a, 0x17, 0x71, 0x75,
	0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x52, 0x15, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x47, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x55, 0x0a, 0x14, 0x79, 0x65, 0x61, 0x72, 0x6c,
	0x79, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x47,
	0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x12, 0x79, 0x65, 0x61, 0x72,
	0x6c, 0x79, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x12, 0x64,
	0x0a, 0x1a, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x5f, 0x76, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18, 0x0f, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x56, 0x61, 0x6c, 0x75,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x18, 0x71, 0x75, 0x61, 0x72,
	0x74, 0x65, 0x72, 0x6c, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x73, 0x12, 0x5e, 0x0a, 0x17, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x73, 0x18,
	0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x56,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x15, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x56, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x73, 0x12, 0x55, 0x0a, 0x19, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c,
	0x79, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61,
	0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x16, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x6c, 0x79, 0x4c, 0x61,
	0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x4f, 0x0a, 0x16, 0x79,
	0x65, 0x61, 0x72, 0x6c, 0x79, 0x5f, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x13, 0x79, 0x65, 0x61, 0x72, 0x6c, 0x79, 0x4c,
	0x61, 0x73, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xee, 0x02, 0x0a,
	0x11, 0x46, 0x75, 0x6e, 0x64, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x3a, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x38,
	0x0a, 0x18, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x16, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x50, 0x65,
	0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x72, 0x61, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x61, 0x76, 0x65, 0x72, 0x61,
	0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x14, 0x74, 0x72, 0x61, 0x64, 0x69, 0x6e,
	0x67, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x41, 0x76, 0x65, 0x72, 0x61, 0x67, 0x65, 0x12, 0x46,
	0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x53, 0x68,
	0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x70,
	0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x33, 0x0a, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x2e, 0x41,
	0x6c, 0x70, 0x68, 0x61, 0x52, 0x05, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x12, 0x30, 0x0a, 0x04, 0x62,
	0x65, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2e, 0x42, 0x65, 0x74, 0x61, 0x52, 0x04, 0x62, 0x65, 0x74, 0x61, 0x22, 0x47, 0x0a,
	0x0b, 0x53, 0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x02, 0x52, 0x07,
	0x6f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x68, 0x72, 0x65, 0x65,
	0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09, 0x74, 0x68, 0x72,
	0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0x41, 0x0a, 0x05, 0x41, 0x6c, 0x70, 0x68, 0x61, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x02, 0x52, 0x07, 0x6f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x68,
	0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x09,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0x40, 0x0a, 0x04, 0x42, 0x65, 0x74,
	0x61, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x6e, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x02, 0x52, 0x07, 0x6f, 0x6e, 0x65, 0x59, 0x65, 0x61, 0x72, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x68, 0x72, 0x65, 0x65, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x09, 0x74, 0x68, 0x72, 0x65, 0x65, 0x59, 0x65, 0x61, 0x72, 0x22, 0xab, 0x03, 0x0a, 0x15,
	0x46, 0x75, 0x6e, 0x64, 0x61, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x73, 0x12, 0x43, 0x0a, 0x14, 0x62, 0x6f, 0x6f, 0x6b, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x5f, 0x70, 0x65, 0x72, 0x5f, 0x73, 0x68, 0x61, 0x72, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x62, 0x6f, 0x6f, 0x6b, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x50, 0x65, 0x72, 0x53, 0x68, 0x61, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65,
	0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x65,
	0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x64, 0x5f, 0x79, 0x69, 0x65,
	0x6c, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0d, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65,
	0x6e, 0x64, 0x59, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x75, 0x72,
	0x6e, 0x5f, 0x6f, 0x6e, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0e, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x4f, 0x6e, 0x45, 0x71, 0x75, 0x69, 0x74,
	0x79, 0x12, 0x2d, 0x0a, 0x12, 0x73, 0x68, 0x61, 0x72, 0x65, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x73, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x21, 0x0a, 0x0c, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x73, 0x68, 0x61, 0x72, 0x70, 0x65, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x3a, 0x0a, 0x19, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x01, 0x52, 0x17, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x50, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12,
	0x38, 0x0a, 0x18, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x5f, 0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x16, 0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x22, 0x9f, 0x03, 0x0a, 0x0f, 0x49, 0x6e,
	0x63, 0x6f, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x37, 0x0a,
	0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0c, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52,
	0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x12, 0x39, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x65, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0d, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x45, 0x78, 0x70, 0x65, 0x6e, 0x73, 0x65,
	0x73, 0x12, 0x35, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x67, 0x72, 0x6f,
	0x73, 0x73, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6e, 0x65, 0x74, 0x5f,
	0x69, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x09, 0x6e, 0x65, 0x74, 0x49, 0x6e, 0x63, 0x6f, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x65,
	0x62, 0x69, 0x74, 0x64, 0x61, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x06, 0x65, 0x62, 0x69, 0x74, 0x64, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xaf, 0x02, 0x0a, 0x0c,
	0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x68, 0x65, 0x65, 0x74, 0x12, 0x35, 0x0a, 0x0c,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x61, 0x73, 0x73, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x73, 0x73,
	0x65, 0x74, 0x73, 0x12, 0x3f, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x6c, 0x69, 0x61,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x4c, 0x69, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c, 0x63, 0x75, 0x72,
	0x72, 0x65, 0x6e, 0x74, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74,
	0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0x9d, 0x03,
	0x0a, 0x11, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x53, 0x74, 0x61, 0x74, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x0e, 0x66, 0x72, 0x65, 0x65, 0x5f, 0x63, 0x61, 0x73, 0x68,
	0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x0c, 0x66, 0x72, 0x65, 0x65, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x42, 0x0a,
	0x13, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f,
	0x66, 0x6c, 0x6f, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x42, 0x0a, 0x13, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x61, 0x73, 0x68, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x11, 0x69, 0x6e, 0x76, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x73,
	0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x42, 0x0a, 0x13, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69,
	0x6e, 0x67, 0x5f, 0x63, 0x61, 0x73, 0x68, 0x5f, 0x66, 0x6c, 0x6f, 0x77, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x66, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x6e,
	0x67, 0x43, 0x61, 0x73, 0x68, 0x46, 0x6c, 0x6f, 0x77, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74,
	0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xff, 0x01,
	0x0a, 0x12, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x74, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x61, 0x74, 0x69, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x67, 0x72, 0x6f, 0x73, 0x73, 0x5f, 0x6d, 0x61,
	0x72, 0x67, 0x69, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x73,
	0x73, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x62, 0x69, 0x74, 0x64,
	0x61, 0x5f, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x0c,
	0x65, 0x62, 0x69, 0x74, 0x64, 0x61, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x12, 0x1d, 0x0a, 0x0a,
	0x6e, 0x65, 0x74, 0x5f, 0x6d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x09, 0x6e, 0x65, 0x74, 0x4d, 0x61, 0x72, 0x67, 0x69, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61,
	0x72, 0x74, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22,
	0xbb, 0x01, 0x0a, 0x0f, 0x45, 0x66, 0x66, 0x69, 0x63, 0x69, 0x65, 0x6e, 0x63, 0x79, 0x52, 0x61,
	0x74, 0x69, 0x6f, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x03, 0x72, 0x6f, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x74, 0x63, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x01, 0x52, 0x04, 0x72, 0x6f, 0x74, 0x63, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c,
	0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29,
	0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74,
	0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64,
	0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70,
	0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xcb, 0x01,
	0x0a, 0x14, 0x46, 0x69, 0x6e, 0x61, 0x6e, 0x63, 0x69, 0x61, 0x6c, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x64, 0x65, 0x62, 0x74, 0x5f, 0x74, 0x6f, 0x5f, 0x65, 0x71, 0x75, 0x69, 0x74, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x44, 0x65, 0x62, 0x74, 0x54,
	0x6f, 0x45, 0x71, 0x75, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10,
	0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72,
	0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52,
	0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x22, 0xe6, 0x01, 0x0a, 0x0b,
	0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x2c, 0x0a, 0x12, 0x64,
	0x69, 0x6c, 0x75, 0x74, 0x65, 0x64, 0x5f, 0x65, 0x70, 0x73, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x64, 0x69, 0x6c, 0x75, 0x74, 0x65, 0x64,
	0x45, 0x70, 0x73, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x76,
	0x65, 0x6e, 0x75, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x77, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x0d, 0x72, 0x65, 0x76, 0x65, 0x6e, 0x75, 0x65, 0x47, 0x72, 0x6f, 0x77, 0x74, 0x68,
	0x12, 0x23, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x59, 0x65, 0x61, 0x72, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61,
	0x72, 0x5f, 0x71, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0f, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72,
	0x12, 0x32, 0x0a, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x44, 0x61, 0x74, 0x65, 0x22, 0xa8, 0x02, 0x0a, 0x0e, 0x56, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x5f, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x65, 0x52, 0x61, 0x74,
	0x69, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x62, 0x5f, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x70, 0x62, 0x52, 0x61, 0x74, 0x69, 0x6f, 0x12, 0x3a, 0x0a,
	0x19, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x64, 0x5f, 0x79, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x70, 0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x01,
	0x52, 0x17, 0x64, 0x69, 0x76, 0x69, 0x64, 0x65, 0x6e, 0x64, 0x59, 0x69, 0x65, 0x6c, 0x64, 0x50,
	0x65, 0x72, 0x63, 0x65, 0x6e, 0x74, 0x61, 0x67, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x65, 0x76, 0x5f,
	0x74, 0x6f, 0x5f, 0x65, 0x62, 0x69, 0x74, 0x64, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x0a, 0x65, 0x76, 0x54, 0x6f, 0x45, 0x62, 0x69, 0x74, 0x64, 0x61, 0x12, 0x23, 0x0a, 0x0d, 0x63,
	0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x79, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x59, 0x65, 0x61, 0x72,
	0x12, 0x29, 0x0a, 0x10, 0x63, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x5f, 0x71, 0x75, 0x61,
	0x72, 0x74, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f, 0x63, 0x61, 0x6c, 0x65,
	0x6e, 0x64, 0x61, 0x72, 0x51, 0x75, 0x61, 0x72, 0x74, 0x65, 0x72, 0x12, 0x32, 0x0a, 0x0b, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x2a,
	0xbc, 0x03, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x46, 0x69, 0x65, 0x6c,
	0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a, 0x1f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x49, 0x44, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x10, 0x02, 0x12, 0x1c, 0x0a,
	0x18, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f,
	0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x04, 0x12, 0x2a, 0x0a, 0x26, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41,
	0x53, 0x4b, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x49, 0x44, 0x10, 0x05, 0x12, 0x20, 0x0a, 0x1c, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x4c,
	0x4f, 0x47, 0x4f, 0x5f, 0x55, 0x52, 0x4c, 0x10, 0x06, 0x12, 0x1f, 0x0a, 0x1b, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b,
	0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x07, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53,
	0x4b, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x10, 0x08, 0x12, 0x22,
	0x0a, 0x1e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44,
	0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x54,
	0x10, 0x09, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x46,
	0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49,
	0x4e, 0x41, 0x4e, 0x43, 0x49, 0x41, 0x4c, 0x5f, 0x49, 0x4e, 0x46, 0x4f, 0x10, 0x0b, 0x42, 0x2f,
	0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x69, 0x65, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_securities_catalog_model_security_proto_rawDescOnce sync.Once
	file_api_securities_catalog_model_security_proto_rawDescData = file_api_securities_catalog_model_security_proto_rawDesc
)

func file_api_securities_catalog_model_security_proto_rawDescGZIP() []byte {
	file_api_securities_catalog_model_security_proto_rawDescOnce.Do(func() {
		file_api_securities_catalog_model_security_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_securities_catalog_model_security_proto_rawDescData)
	})
	return file_api_securities_catalog_model_security_proto_rawDescData
}

var file_api_securities_catalog_model_security_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_api_securities_catalog_model_security_proto_msgTypes = make([]protoimpl.MessageInfo, 24)
var file_api_securities_catalog_model_security_proto_goTypes = []interface{}{
	(SecurityFieldMask)(0),        // 0: api.securities.catalog.SecurityFieldMask
	(*Security)(nil),              // 1: api.securities.catalog.Security
	(*SecurityDetails)(nil),       // 2: api.securities.catalog.SecurityDetails
	(*StockDetails)(nil),          // 3: api.securities.catalog.StockDetails
	(*FundDetails)(nil),           // 4: api.securities.catalog.FundDetails
	(*Holdings)(nil),              // 5: api.securities.catalog.Holdings
	(*EquitySectorHoldings)(nil),  // 6: api.securities.catalog.EquitySectorHoldings
	(*FinancialInfo)(nil),         // 7: api.securities.catalog.FinancialInfo
	(*FinancialParameters)(nil),   // 8: api.securities.catalog.FinancialParameters
	(*StockFinancialInfo)(nil),    // 9: api.securities.catalog.StockFinancialInfo
	(*FundFinancialInfo)(nil),     // 10: api.securities.catalog.FundFinancialInfo
	(*SharpeRatio)(nil),           // 11: api.securities.catalog.SharpeRatio
	(*Alpha)(nil),                 // 12: api.securities.catalog.Alpha
	(*Beta)(nil),                  // 13: api.securities.catalog.Beta
	(*FundamentalParameters)(nil), // 14: api.securities.catalog.FundamentalParameters
	(*IncomeStatement)(nil),       // 15: api.securities.catalog.IncomeStatement
	(*BalanceSheet)(nil),          // 16: api.securities.catalog.BalanceSheet
	(*CashFlowStatement)(nil),     // 17: api.securities.catalog.CashFlowStatement
	(*ProfitabilityRatio)(nil),    // 18: api.securities.catalog.ProfitabilityRatio
	(*EfficiencyRatio)(nil),       // 19: api.securities.catalog.EfficiencyRatio
	(*FinancialHealthRatio)(nil),  // 20: api.securities.catalog.FinancialHealthRatio
	(*GrowthRatio)(nil),           // 21: api.securities.catalog.GrowthRatio
	(*ValuationRatio)(nil),        // 22: api.securities.catalog.ValuationRatio
	nil,                           // 23: api.securities.catalog.Holdings.HoldingsEntry
	nil,                           // 24: api.securities.catalog.EquitySectorHoldings.EquitySectorsEntry
	(SecurityType)(0),             // 25: api.securities.catalog.SecurityType
	(vendorgateway.Vendor)(0),     // 26: vendorgateway.Vendor
	(*timestamppb.Timestamp)(nil), // 27: google.protobuf.Timestamp
	(GICSSectorType)(0),           // 28: api.securities.catalog.GICSSectorType
	(GICSIndustryGroupType)(0),    // 29: api.securities.catalog.GICSIndustryGroupType
	(GICSIndustryType)(0),         // 30: api.securities.catalog.GICSIndustryType
	(*money.Money)(nil),           // 31: google.type.Money
	(*typesv2.Money)(nil),         // 32: api.typesv2.Money
	(*typesv2.Date)(nil),          // 33: api.typesv2.Date
}
var file_api_securities_catalog_model_security_proto_depIdxs = []int32{
	25, // 0: api.securities.catalog.Security.security_type:type_name -> api.securities.catalog.SecurityType
	26, // 1: api.securities.catalog.Security.vendor:type_name -> vendorgateway.Vendor
	2,  // 2: api.securities.catalog.Security.security_details:type_name -> api.securities.catalog.SecurityDetails
	27, // 3: api.securities.catalog.Security.created_at:type_name -> google.protobuf.Timestamp
	27, // 4: api.securities.catalog.Security.updated_at:type_name -> google.protobuf.Timestamp
	27, // 5: api.securities.catalog.Security.deleted_at:type_name -> google.protobuf.Timestamp
	7,  // 6: api.securities.catalog.Security.financial_info:type_name -> api.securities.catalog.FinancialInfo
	3,  // 7: api.securities.catalog.SecurityDetails.stock_details:type_name -> api.securities.catalog.StockDetails
	4,  // 8: api.securities.catalog.SecurityDetails.fund_details:type_name -> api.securities.catalog.FundDetails
	28, // 9: api.securities.catalog.StockDetails.gics_sector_type:type_name -> api.securities.catalog.GICSSectorType
	29, // 10: api.securities.catalog.StockDetails.gics_industry_group_type:type_name -> api.securities.catalog.GICSIndustryGroupType
	30, // 11: api.securities.catalog.StockDetails.gics_industry_type:type_name -> api.securities.catalog.GICSIndustryType
	5,  // 12: api.securities.catalog.FundDetails.etf_holdings:type_name -> api.securities.catalog.Holdings
	6,  // 13: api.securities.catalog.FundDetails.equity_sector_holdings:type_name -> api.securities.catalog.EquitySectorHoldings
	23, // 14: api.securities.catalog.Holdings.holdings:type_name -> api.securities.catalog.Holdings.HoldingsEntry
	24, // 15: api.securities.catalog.EquitySectorHoldings.equity_sectors:type_name -> api.securities.catalog.EquitySectorHoldings.EquitySectorsEntry
	31, // 16: api.securities.catalog.FinancialInfo.market_cap:type_name -> google.type.Money
	14, // 17: api.securities.catalog.FinancialInfo.ttm_fundamental_parameters:type_name -> api.securities.catalog.FundamentalParameters
	8,  // 18: api.securities.catalog.FinancialInfo.financial_parameters:type_name -> api.securities.catalog.FinancialParameters
	9,  // 19: api.securities.catalog.FinancialParameters.stock_financial_info:type_name -> api.securities.catalog.StockFinancialInfo
	10, // 20: api.securities.catalog.FinancialParameters.fund_financial_info:type_name -> api.securities.catalog.FundFinancialInfo
	16, // 21: api.securities.catalog.StockFinancialInfo.quarterly_balance_sheets:type_name -> api.securities.catalog.BalanceSheet
	16, // 22: api.securities.catalog.StockFinancialInfo.yearly_balance_sheets:type_name -> api.securities.catalog.BalanceSheet
	17, // 23: api.securities.catalog.StockFinancialInfo.quarterly_cash_flow_statements:type_name -> api.securities.catalog.CashFlowStatement
	17, // 24: api.securities.catalog.StockFinancialInfo.yearly_cash_flow_statements:type_name -> api.securities.catalog.CashFlowStatement
	15, // 25: api.securities.catalog.StockFinancialInfo.quarterly_income_statements:type_name -> api.securities.catalog.IncomeStatement
	15, // 26: api.securities.catalog.StockFinancialInfo.yearly_income_statements:type_name -> api.securities.catalog.IncomeStatement
	18, // 27: api.securities.catalog.StockFinancialInfo.quarterly_profitability_ratios:type_name -> api.securities.catalog.ProfitabilityRatio
	18, // 28: api.securities.catalog.StockFinancialInfo.yearly_profitability_ratios:type_name -> api.securities.catalog.ProfitabilityRatio
	19, // 29: api.securities.catalog.StockFinancialInfo.quarterly_efficiency_ratios:type_name -> api.securities.catalog.EfficiencyRatio
	19, // 30: api.securities.catalog.StockFinancialInfo.yearly_efficiency_ratios:type_name -> api.securities.catalog.EfficiencyRatio
	20, // 31: api.securities.catalog.StockFinancialInfo.quarterly_financial_health_ratios:type_name -> api.securities.catalog.FinancialHealthRatio
	20, // 32: api.securities.catalog.StockFinancialInfo.yearly_financial_health_ratios:type_name -> api.securities.catalog.FinancialHealthRatio
	21, // 33: api.securities.catalog.StockFinancialInfo.quarterly_growth_ratios:type_name -> api.securities.catalog.GrowthRatio
	21, // 34: api.securities.catalog.StockFinancialInfo.yearly_growth_ratios:type_name -> api.securities.catalog.GrowthRatio
	22, // 35: api.securities.catalog.StockFinancialInfo.quarterly_valuation_ratios:type_name -> api.securities.catalog.ValuationRatio
	22, // 36: api.securities.catalog.StockFinancialInfo.yearly_valuation_ratios:type_name -> api.securities.catalog.ValuationRatio
	27, // 37: api.securities.catalog.StockFinancialInfo.quarterly_last_updated_at:type_name -> google.protobuf.Timestamp
	27, // 38: api.securities.catalog.StockFinancialInfo.yearly_last_updated_at:type_name -> google.protobuf.Timestamp
	11, // 39: api.securities.catalog.FundFinancialInfo.sharpe_ratio:type_name -> api.securities.catalog.SharpeRatio
	12, // 40: api.securities.catalog.FundFinancialInfo.alpha:type_name -> api.securities.catalog.Alpha
	13, // 41: api.securities.catalog.FundFinancialInfo.beta:type_name -> api.securities.catalog.Beta
	31, // 42: api.securities.catalog.FundamentalParameters.book_value_per_share:type_name -> google.type.Money
	32, // 43: api.securities.catalog.IncomeStatement.total_revenue:type_name -> api.typesv2.Money
	32, // 44: api.securities.catalog.IncomeStatement.total_expenses:type_name -> api.typesv2.Money
	32, // 45: api.securities.catalog.IncomeStatement.gross_profit:type_name -> api.typesv2.Money
	32, // 46: api.securities.catalog.IncomeStatement.net_income:type_name -> api.typesv2.Money
	32, // 47: api.securities.catalog.IncomeStatement.ebitda:type_name -> api.typesv2.Money
	33, // 48: api.securities.catalog.IncomeStatement.report_date:type_name -> api.typesv2.Date
	32, // 49: api.securities.catalog.BalanceSheet.total_assets:type_name -> api.typesv2.Money
	32, // 50: api.securities.catalog.BalanceSheet.total_liabilities:type_name -> api.typesv2.Money
	33, // 51: api.securities.catalog.BalanceSheet.report_date:type_name -> api.typesv2.Date
	32, // 52: api.securities.catalog.CashFlowStatement.free_cash_flow:type_name -> api.typesv2.Money
	32, // 53: api.securities.catalog.CashFlowStatement.operating_cash_flow:type_name -> api.typesv2.Money
	32, // 54: api.securities.catalog.CashFlowStatement.investing_cash_flow:type_name -> api.typesv2.Money
	32, // 55: api.securities.catalog.CashFlowStatement.financing_cash_flow:type_name -> api.typesv2.Money
	33, // 56: api.securities.catalog.CashFlowStatement.report_date:type_name -> api.typesv2.Date
	33, // 57: api.securities.catalog.ProfitabilityRatio.report_date:type_name -> api.typesv2.Date
	33, // 58: api.securities.catalog.EfficiencyRatio.report_date:type_name -> api.typesv2.Date
	33, // 59: api.securities.catalog.FinancialHealthRatio.report_date:type_name -> api.typesv2.Date
	33, // 60: api.securities.catalog.GrowthRatio.report_date:type_name -> api.typesv2.Date
	33, // 61: api.securities.catalog.ValuationRatio.report_date:type_name -> api.typesv2.Date
	62, // [62:62] is the sub-list for method output_type
	62, // [62:62] is the sub-list for method input_type
	62, // [62:62] is the sub-list for extension type_name
	62, // [62:62] is the sub-list for extension extendee
	0,  // [0:62] is the sub-list for field type_name
}

func init() { file_api_securities_catalog_model_security_proto_init() }
func file_api_securities_catalog_model_security_proto_init() {
	if File_api_securities_catalog_model_security_proto != nil {
		return
	}
	file_api_securities_catalog_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_securities_catalog_model_security_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Security); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SecurityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Holdings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EquitySectorHoldings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StockFinancialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundFinancialInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SharpeRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Alpha); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Beta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FundamentalParameters); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IncomeStatement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BalanceSheet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CashFlowStatement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ProfitabilityRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EfficiencyRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FinancialHealthRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GrowthRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_securities_catalog_model_security_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValuationRatio); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_securities_catalog_model_security_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*SecurityDetails_StockDetails)(nil),
		(*SecurityDetails_FundDetails)(nil),
	}
	file_api_securities_catalog_model_security_proto_msgTypes[7].OneofWrappers = []interface{}{
		(*FinancialParameters_StockFinancialInfo)(nil),
		(*FinancialParameters_FundFinancialInfo)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_securities_catalog_model_security_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   24,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_securities_catalog_model_security_proto_goTypes,
		DependencyIndexes: file_api_securities_catalog_model_security_proto_depIdxs,
		EnumInfos:         file_api_securities_catalog_model_security_proto_enumTypes,
		MessageInfos:      file_api_securities_catalog_model_security_proto_msgTypes,
	}.Build()
	File_api_securities_catalog_model_security_proto = out.File
	file_api_securities_catalog_model_security_proto_rawDesc = nil
	file_api_securities_catalog_model_security_proto_goTypes = nil
	file_api_securities_catalog_model_security_proto_depIdxs = nil
}
