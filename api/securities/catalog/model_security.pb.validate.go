// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/securities/catalog/model_security.proto

package catalog

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = vendorgateway.Vendor(0)
)

// Validate checks the field values on Security with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Security) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Security with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SecurityMultiError, or nil
// if none found.
func (m *Security) ValidateAll() error {
	return m.validate(true)
}

func (m *Security) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for SecurityType

	// no validation rules for SecurityName

	// no validation rules for Vendor

	// no validation rules for VendorSecurityId

	// no validation rules for LogoUrl

	if all {
		switch v := interface{}(m.GetSecurityDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "SecurityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "SecurityDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSecurityDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "SecurityDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCreatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "CreatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "CreatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "UpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "UpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDeletedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "DeletedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDeletedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "DeletedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinancialInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "FinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SecurityValidationError{
					field:  "FinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinancialInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SecurityValidationError{
				field:  "FinancialInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SecurityMultiError(errors)
	}

	return nil
}

// SecurityMultiError is an error wrapping multiple validation errors returned
// by Security.ValidateAll() if the designated constraints aren't met.
type SecurityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityMultiError) AllErrors() []error { return m }

// SecurityValidationError is the validation error returned by
// Security.Validate if the designated constraints aren't met.
type SecurityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityValidationError) ErrorName() string { return "SecurityValidationError" }

// Error satisfies the builtin error interface
func (e SecurityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityValidationError{}

// Validate checks the field values on SecurityDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SecurityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SecurityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SecurityDetailsMultiError, or nil if none found.
func (m *SecurityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *SecurityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.SecurityType.(type) {
	case *SecurityDetails_StockDetails:
		if v == nil {
			err := SecurityDetailsValidationError{
				field:  "SecurityType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStockDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "StockDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "StockDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStockDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecurityDetailsValidationError{
					field:  "StockDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *SecurityDetails_FundDetails:
		if v == nil {
			err := SecurityDetailsValidationError{
				field:  "SecurityType",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFundDetails()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "FundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SecurityDetailsValidationError{
						field:  "FundDetails",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFundDetails()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SecurityDetailsValidationError{
					field:  "FundDetails",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return SecurityDetailsMultiError(errors)
	}

	return nil
}

// SecurityDetailsMultiError is an error wrapping multiple validation errors
// returned by SecurityDetails.ValidateAll() if the designated constraints
// aren't met.
type SecurityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SecurityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SecurityDetailsMultiError) AllErrors() []error { return m }

// SecurityDetailsValidationError is the validation error returned by
// SecurityDetails.Validate if the designated constraints aren't met.
type SecurityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SecurityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SecurityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SecurityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SecurityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SecurityDetailsValidationError) ErrorName() string { return "SecurityDetailsValidationError" }

// Error satisfies the builtin error interface
func (e SecurityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSecurityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SecurityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SecurityDetailsValidationError{}

// Validate checks the field values on StockDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *StockDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StockDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in StockDetailsMultiError, or
// nil if none found.
func (m *StockDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *StockDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for StockName

	// no validation rules for StockShortName

	// no validation rules for WebsiteUrl

	// no validation rules for RegionName

	// no validation rules for IncorporationCountryName

	// no validation rules for GicsSectorType

	// no validation rules for GicsIndustryGroupType

	// no validation rules for GicsIndustryType

	// no validation rules for StockDescription

	if len(errors) > 0 {
		return StockDetailsMultiError(errors)
	}

	return nil
}

// StockDetailsMultiError is an error wrapping multiple validation errors
// returned by StockDetails.ValidateAll() if the designated constraints aren't met.
type StockDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StockDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StockDetailsMultiError) AllErrors() []error { return m }

// StockDetailsValidationError is the validation error returned by
// StockDetails.Validate if the designated constraints aren't met.
type StockDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StockDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StockDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StockDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StockDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StockDetailsValidationError) ErrorName() string { return "StockDetailsValidationError" }

// Error satisfies the builtin error interface
func (e StockDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStockDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StockDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StockDetailsValidationError{}

// Validate checks the field values on FundDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FundDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FundDetailsMultiError, or
// nil if none found.
func (m *FundDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *FundDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FundName

	// no validation rules for FundNameShort

	// no validation rules for RegionName

	// no validation rules for CountryName

	// no validation rules for BenchmarkName

	// no validation rules for BenchmarkNameShort

	if all {
		switch v := interface{}(m.GetEtfHoldings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EtfHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EtfHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEtfHoldings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundDetailsValidationError{
				field:  "EtfHoldings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEquitySectorHoldings()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EquitySectorHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundDetailsValidationError{
					field:  "EquitySectorHoldings",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEquitySectorHoldings()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundDetailsValidationError{
				field:  "EquitySectorHoldings",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FundDescription

	if len(errors) > 0 {
		return FundDetailsMultiError(errors)
	}

	return nil
}

// FundDetailsMultiError is an error wrapping multiple validation errors
// returned by FundDetails.ValidateAll() if the designated constraints aren't met.
type FundDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundDetailsMultiError) AllErrors() []error { return m }

// FundDetailsValidationError is the validation error returned by
// FundDetails.Validate if the designated constraints aren't met.
type FundDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundDetailsValidationError) ErrorName() string { return "FundDetailsValidationError" }

// Error satisfies the builtin error interface
func (e FundDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundDetailsValidationError{}

// Validate checks the field values on Holdings with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Holdings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Holdings with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HoldingsMultiError, or nil
// if none found.
func (m *Holdings) ValidateAll() error {
	return m.validate(true)
}

func (m *Holdings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Holdings

	if len(errors) > 0 {
		return HoldingsMultiError(errors)
	}

	return nil
}

// HoldingsMultiError is an error wrapping multiple validation errors returned
// by Holdings.ValidateAll() if the designated constraints aren't met.
type HoldingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HoldingsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HoldingsMultiError) AllErrors() []error { return m }

// HoldingsValidationError is the validation error returned by
// Holdings.Validate if the designated constraints aren't met.
type HoldingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HoldingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HoldingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HoldingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HoldingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HoldingsValidationError) ErrorName() string { return "HoldingsValidationError" }

// Error satisfies the builtin error interface
func (e HoldingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHoldings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HoldingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HoldingsValidationError{}

// Validate checks the field values on EquitySectorHoldings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *EquitySectorHoldings) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EquitySectorHoldings with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EquitySectorHoldingsMultiError, or nil if none found.
func (m *EquitySectorHoldings) ValidateAll() error {
	return m.validate(true)
}

func (m *EquitySectorHoldings) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EquitySectors

	if len(errors) > 0 {
		return EquitySectorHoldingsMultiError(errors)
	}

	return nil
}

// EquitySectorHoldingsMultiError is an error wrapping multiple validation
// errors returned by EquitySectorHoldings.ValidateAll() if the designated
// constraints aren't met.
type EquitySectorHoldingsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EquitySectorHoldingsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EquitySectorHoldingsMultiError) AllErrors() []error { return m }

// EquitySectorHoldingsValidationError is the validation error returned by
// EquitySectorHoldings.Validate if the designated constraints aren't met.
type EquitySectorHoldingsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EquitySectorHoldingsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EquitySectorHoldingsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EquitySectorHoldingsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EquitySectorHoldingsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EquitySectorHoldingsValidationError) ErrorName() string {
	return "EquitySectorHoldingsValidationError"
}

// Error satisfies the builtin error interface
func (e EquitySectorHoldingsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEquitySectorHoldings.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EquitySectorHoldingsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EquitySectorHoldingsValidationError{}

// Validate checks the field values on FinancialInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *FinancialInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinancialInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in FinancialInfoMultiError, or
// nil if none found.
func (m *FinancialInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FinancialInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMarketCap()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "MarketCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "MarketCap",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMarketCap()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialInfoValidationError{
				field:  "MarketCap",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTtmFundamentalParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "TtmFundamentalParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "TtmFundamentalParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTtmFundamentalParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialInfoValidationError{
				field:  "TtmFundamentalParameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinancialParameters()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "FinancialParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialInfoValidationError{
					field:  "FinancialParameters",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinancialParameters()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialInfoValidationError{
				field:  "FinancialParameters",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FinancialInfoMultiError(errors)
	}

	return nil
}

// FinancialInfoMultiError is an error wrapping multiple validation errors
// returned by FinancialInfo.ValidateAll() if the designated constraints
// aren't met.
type FinancialInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinancialInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinancialInfoMultiError) AllErrors() []error { return m }

// FinancialInfoValidationError is the validation error returned by
// FinancialInfo.Validate if the designated constraints aren't met.
type FinancialInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinancialInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinancialInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinancialInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinancialInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinancialInfoValidationError) ErrorName() string { return "FinancialInfoValidationError" }

// Error satisfies the builtin error interface
func (e FinancialInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinancialInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinancialInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinancialInfoValidationError{}

// Validate checks the field values on FinancialParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FinancialParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinancialParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinancialParametersMultiError, or nil if none found.
func (m *FinancialParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *FinancialParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	switch v := m.FinancialInfo.(type) {
	case *FinancialParameters_StockFinancialInfo:
		if v == nil {
			err := FinancialParametersValidationError{
				field:  "FinancialInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetStockFinancialInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FinancialParametersValidationError{
						field:  "StockFinancialInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FinancialParametersValidationError{
						field:  "StockFinancialInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetStockFinancialInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FinancialParametersValidationError{
					field:  "StockFinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *FinancialParameters_FundFinancialInfo:
		if v == nil {
			err := FinancialParametersValidationError{
				field:  "FinancialInfo",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFundFinancialInfo()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FinancialParametersValidationError{
						field:  "FundFinancialInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FinancialParametersValidationError{
						field:  "FundFinancialInfo",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFundFinancialInfo()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FinancialParametersValidationError{
					field:  "FundFinancialInfo",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return FinancialParametersMultiError(errors)
	}

	return nil
}

// FinancialParametersMultiError is an error wrapping multiple validation
// errors returned by FinancialParameters.ValidateAll() if the designated
// constraints aren't met.
type FinancialParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinancialParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinancialParametersMultiError) AllErrors() []error { return m }

// FinancialParametersValidationError is the validation error returned by
// FinancialParameters.Validate if the designated constraints aren't met.
type FinancialParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinancialParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinancialParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinancialParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinancialParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinancialParametersValidationError) ErrorName() string {
	return "FinancialParametersValidationError"
}

// Error satisfies the builtin error interface
func (e FinancialParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinancialParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinancialParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinancialParametersValidationError{}

// Validate checks the field values on StockFinancialInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StockFinancialInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StockFinancialInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StockFinancialInfoMultiError, or nil if none found.
func (m *StockFinancialInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *StockFinancialInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetQuarterlyBalanceSheets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyBalanceSheets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyBalanceSheets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyBalanceSheets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyBalanceSheets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyBalanceSheets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyBalanceSheets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyBalanceSheets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyCashFlowStatements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyCashFlowStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyCashFlowStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyCashFlowStatements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyCashFlowStatements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyCashFlowStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyCashFlowStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyCashFlowStatements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyIncomeStatements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyIncomeStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyIncomeStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyIncomeStatements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyIncomeStatements() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyIncomeStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyIncomeStatements[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyIncomeStatements[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyProfitabilityRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyProfitabilityRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyProfitabilityRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyProfitabilityRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyProfitabilityRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyProfitabilityRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyProfitabilityRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyProfitabilityRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyEfficiencyRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyEfficiencyRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyEfficiencyRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyEfficiencyRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyEfficiencyRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyEfficiencyRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyEfficiencyRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyEfficiencyRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyFinancialHealthRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyFinancialHealthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyFinancialHealthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyFinancialHealthRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyFinancialHealthRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyFinancialHealthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyFinancialHealthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyFinancialHealthRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyGrowthRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyGrowthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyGrowthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyGrowthRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyGrowthRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyGrowthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyGrowthRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyGrowthRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetQuarterlyValuationRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyValuationRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("QuarterlyValuationRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("QuarterlyValuationRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetYearlyValuationRatios() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyValuationRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, StockFinancialInfoValidationError{
						field:  fmt.Sprintf("YearlyValuationRatios[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return StockFinancialInfoValidationError{
					field:  fmt.Sprintf("YearlyValuationRatios[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetQuarterlyLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StockFinancialInfoValidationError{
					field:  "QuarterlyLastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StockFinancialInfoValidationError{
					field:  "QuarterlyLastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetQuarterlyLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StockFinancialInfoValidationError{
				field:  "QuarterlyLastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetYearlyLastUpdatedAt()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, StockFinancialInfoValidationError{
					field:  "YearlyLastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, StockFinancialInfoValidationError{
					field:  "YearlyLastUpdatedAt",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetYearlyLastUpdatedAt()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return StockFinancialInfoValidationError{
				field:  "YearlyLastUpdatedAt",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return StockFinancialInfoMultiError(errors)
	}

	return nil
}

// StockFinancialInfoMultiError is an error wrapping multiple validation errors
// returned by StockFinancialInfo.ValidateAll() if the designated constraints
// aren't met.
type StockFinancialInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StockFinancialInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StockFinancialInfoMultiError) AllErrors() []error { return m }

// StockFinancialInfoValidationError is the validation error returned by
// StockFinancialInfo.Validate if the designated constraints aren't met.
type StockFinancialInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StockFinancialInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StockFinancialInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StockFinancialInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StockFinancialInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StockFinancialInfoValidationError) ErrorName() string {
	return "StockFinancialInfoValidationError"
}

// Error satisfies the builtin error interface
func (e StockFinancialInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStockFinancialInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StockFinancialInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StockFinancialInfoValidationError{}

// Validate checks the field values on FundFinancialInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *FundFinancialInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundFinancialInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FundFinancialInfoMultiError, or nil if none found.
func (m *FundFinancialInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *FundFinancialInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TrackingErrorPercentage

	// no validation rules for ExpenseRatioPercentage

	// no validation rules for TradingVolumeAverage

	if all {
		switch v := interface{}(m.GetSharpeRatio()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "SharpeRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "SharpeRatio",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSharpeRatio()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundFinancialInfoValidationError{
				field:  "SharpeRatio",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetAlpha()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "Alpha",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "Alpha",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAlpha()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundFinancialInfoValidationError{
				field:  "Alpha",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "Beta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundFinancialInfoValidationError{
					field:  "Beta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundFinancialInfoValidationError{
				field:  "Beta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FundFinancialInfoMultiError(errors)
	}

	return nil
}

// FundFinancialInfoMultiError is an error wrapping multiple validation errors
// returned by FundFinancialInfo.ValidateAll() if the designated constraints
// aren't met.
type FundFinancialInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundFinancialInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundFinancialInfoMultiError) AllErrors() []error { return m }

// FundFinancialInfoValidationError is the validation error returned by
// FundFinancialInfo.Validate if the designated constraints aren't met.
type FundFinancialInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundFinancialInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundFinancialInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundFinancialInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundFinancialInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundFinancialInfoValidationError) ErrorName() string {
	return "FundFinancialInfoValidationError"
}

// Error satisfies the builtin error interface
func (e FundFinancialInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundFinancialInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundFinancialInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundFinancialInfoValidationError{}

// Validate checks the field values on SharpeRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SharpeRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SharpeRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SharpeRatioMultiError, or
// nil if none found.
func (m *SharpeRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *SharpeRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OneYear

	// no validation rules for ThreeYear

	if len(errors) > 0 {
		return SharpeRatioMultiError(errors)
	}

	return nil
}

// SharpeRatioMultiError is an error wrapping multiple validation errors
// returned by SharpeRatio.ValidateAll() if the designated constraints aren't met.
type SharpeRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SharpeRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SharpeRatioMultiError) AllErrors() []error { return m }

// SharpeRatioValidationError is the validation error returned by
// SharpeRatio.Validate if the designated constraints aren't met.
type SharpeRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SharpeRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SharpeRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SharpeRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SharpeRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SharpeRatioValidationError) ErrorName() string { return "SharpeRatioValidationError" }

// Error satisfies the builtin error interface
func (e SharpeRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSharpeRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SharpeRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SharpeRatioValidationError{}

// Validate checks the field values on Alpha with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Alpha) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Alpha with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in AlphaMultiError, or nil if none found.
func (m *Alpha) ValidateAll() error {
	return m.validate(true)
}

func (m *Alpha) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OneYear

	// no validation rules for ThreeYear

	if len(errors) > 0 {
		return AlphaMultiError(errors)
	}

	return nil
}

// AlphaMultiError is an error wrapping multiple validation errors returned by
// Alpha.ValidateAll() if the designated constraints aren't met.
type AlphaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AlphaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AlphaMultiError) AllErrors() []error { return m }

// AlphaValidationError is the validation error returned by Alpha.Validate if
// the designated constraints aren't met.
type AlphaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AlphaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AlphaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AlphaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AlphaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AlphaValidationError) ErrorName() string { return "AlphaValidationError" }

// Error satisfies the builtin error interface
func (e AlphaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAlpha.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AlphaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AlphaValidationError{}

// Validate checks the field values on Beta with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *Beta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Beta with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in BetaMultiError, or nil if none found.
func (m *Beta) ValidateAll() error {
	return m.validate(true)
}

func (m *Beta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OneYear

	// no validation rules for ThreeYear

	if len(errors) > 0 {
		return BetaMultiError(errors)
	}

	return nil
}

// BetaMultiError is an error wrapping multiple validation errors returned by
// Beta.ValidateAll() if the designated constraints aren't met.
type BetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BetaMultiError) AllErrors() []error { return m }

// BetaValidationError is the validation error returned by Beta.Validate if the
// designated constraints aren't met.
type BetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BetaValidationError) ErrorName() string { return "BetaValidationError" }

// Error satisfies the builtin error interface
func (e BetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BetaValidationError{}

// Validate checks the field values on FundamentalParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FundamentalParameters) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FundamentalParameters with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FundamentalParametersMultiError, or nil if none found.
func (m *FundamentalParameters) ValidateAll() error {
	return m.validate(true)
}

func (m *FundamentalParameters) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBookValuePerShare()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FundamentalParametersValidationError{
					field:  "BookValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FundamentalParametersValidationError{
					field:  "BookValuePerShare",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBookValuePerShare()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FundamentalParametersValidationError{
				field:  "BookValuePerShare",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for PeRatio

	// no validation rules for PbRatio

	// no validation rules for DividendYield

	// no validation rules for ReturnOnEquity

	// no validation rules for SharesOutstanding

	// no validation rules for SharpeRatio

	// no validation rules for TrackingErrorPercentage

	// no validation rules for ExpenseRatioPercentage

	if len(errors) > 0 {
		return FundamentalParametersMultiError(errors)
	}

	return nil
}

// FundamentalParametersMultiError is an error wrapping multiple validation
// errors returned by FundamentalParameters.ValidateAll() if the designated
// constraints aren't met.
type FundamentalParametersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FundamentalParametersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FundamentalParametersMultiError) AllErrors() []error { return m }

// FundamentalParametersValidationError is the validation error returned by
// FundamentalParameters.Validate if the designated constraints aren't met.
type FundamentalParametersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FundamentalParametersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FundamentalParametersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FundamentalParametersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FundamentalParametersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FundamentalParametersValidationError) ErrorName() string {
	return "FundamentalParametersValidationError"
}

// Error satisfies the builtin error interface
func (e FundamentalParametersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFundamentalParameters.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FundamentalParametersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FundamentalParametersValidationError{}

// Validate checks the field values on IncomeStatement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IncomeStatement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IncomeStatement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IncomeStatementMultiError, or nil if none found.
func (m *IncomeStatement) ValidateAll() error {
	return m.validate(true)
}

func (m *IncomeStatement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalRevenue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "TotalRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "TotalRevenue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalRevenue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "TotalRevenue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalExpenses()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "TotalExpenses",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "TotalExpenses",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalExpenses()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "TotalExpenses",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGrossProfit()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "GrossProfit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "GrossProfit",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGrossProfit()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "GrossProfit",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNetIncome()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "NetIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "NetIncome",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNetIncome()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "NetIncome",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEbitda()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "Ebitda",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "Ebitda",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEbitda()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "Ebitda",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IncomeStatementValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IncomeStatementValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IncomeStatementMultiError(errors)
	}

	return nil
}

// IncomeStatementMultiError is an error wrapping multiple validation errors
// returned by IncomeStatement.ValidateAll() if the designated constraints
// aren't met.
type IncomeStatementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IncomeStatementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IncomeStatementMultiError) AllErrors() []error { return m }

// IncomeStatementValidationError is the validation error returned by
// IncomeStatement.Validate if the designated constraints aren't met.
type IncomeStatementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IncomeStatementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IncomeStatementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IncomeStatementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IncomeStatementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IncomeStatementValidationError) ErrorName() string { return "IncomeStatementValidationError" }

// Error satisfies the builtin error interface
func (e IncomeStatementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIncomeStatement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IncomeStatementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IncomeStatementValidationError{}

// Validate checks the field values on BalanceSheet with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BalanceSheet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BalanceSheet with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BalanceSheetMultiError, or
// nil if none found.
func (m *BalanceSheet) ValidateAll() error {
	return m.validate(true)
}

func (m *BalanceSheet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTotalAssets()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "TotalAssets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "TotalAssets",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalAssets()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceSheetValidationError{
				field:  "TotalAssets",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetTotalLiabilities()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "TotalLiabilities",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "TotalLiabilities",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTotalLiabilities()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceSheetValidationError{
				field:  "TotalLiabilities",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CurrentRatio

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BalanceSheetValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BalanceSheetValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return BalanceSheetMultiError(errors)
	}

	return nil
}

// BalanceSheetMultiError is an error wrapping multiple validation errors
// returned by BalanceSheet.ValidateAll() if the designated constraints aren't met.
type BalanceSheetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BalanceSheetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BalanceSheetMultiError) AllErrors() []error { return m }

// BalanceSheetValidationError is the validation error returned by
// BalanceSheet.Validate if the designated constraints aren't met.
type BalanceSheetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BalanceSheetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BalanceSheetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BalanceSheetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BalanceSheetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BalanceSheetValidationError) ErrorName() string { return "BalanceSheetValidationError" }

// Error satisfies the builtin error interface
func (e BalanceSheetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBalanceSheet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BalanceSheetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BalanceSheetValidationError{}

// Validate checks the field values on CashFlowStatement with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CashFlowStatement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CashFlowStatement with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CashFlowStatementMultiError, or nil if none found.
func (m *CashFlowStatement) ValidateAll() error {
	return m.validate(true)
}

func (m *CashFlowStatement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetFreeCashFlow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "FreeCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "FreeCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFreeCashFlow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashFlowStatementValidationError{
				field:  "FreeCashFlow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOperatingCashFlow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "OperatingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "OperatingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOperatingCashFlow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashFlowStatementValidationError{
				field:  "OperatingCashFlow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetInvestingCashFlow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "InvestingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "InvestingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInvestingCashFlow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashFlowStatementValidationError{
				field:  "InvestingCashFlow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetFinancingCashFlow()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "FinancingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "FinancingCashFlow",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetFinancingCashFlow()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashFlowStatementValidationError{
				field:  "FinancingCashFlow",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CashFlowStatementValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CashFlowStatementValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CashFlowStatementMultiError(errors)
	}

	return nil
}

// CashFlowStatementMultiError is an error wrapping multiple validation errors
// returned by CashFlowStatement.ValidateAll() if the designated constraints
// aren't met.
type CashFlowStatementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CashFlowStatementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CashFlowStatementMultiError) AllErrors() []error { return m }

// CashFlowStatementValidationError is the validation error returned by
// CashFlowStatement.Validate if the designated constraints aren't met.
type CashFlowStatementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CashFlowStatementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CashFlowStatementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CashFlowStatementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CashFlowStatementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CashFlowStatementValidationError) ErrorName() string {
	return "CashFlowStatementValidationError"
}

// Error satisfies the builtin error interface
func (e CashFlowStatementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCashFlowStatement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CashFlowStatementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CashFlowStatementValidationError{}

// Validate checks the field values on ProfitabilityRatio with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ProfitabilityRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ProfitabilityRatio with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ProfitabilityRatioMultiError, or nil if none found.
func (m *ProfitabilityRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *ProfitabilityRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GrossMargin

	// no validation rules for EbitdaMargin

	// no validation rules for NetMargin

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ProfitabilityRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ProfitabilityRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ProfitabilityRatioValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ProfitabilityRatioMultiError(errors)
	}

	return nil
}

// ProfitabilityRatioMultiError is an error wrapping multiple validation errors
// returned by ProfitabilityRatio.ValidateAll() if the designated constraints
// aren't met.
type ProfitabilityRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ProfitabilityRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ProfitabilityRatioMultiError) AllErrors() []error { return m }

// ProfitabilityRatioValidationError is the validation error returned by
// ProfitabilityRatio.Validate if the designated constraints aren't met.
type ProfitabilityRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ProfitabilityRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ProfitabilityRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ProfitabilityRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ProfitabilityRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ProfitabilityRatioValidationError) ErrorName() string {
	return "ProfitabilityRatioValidationError"
}

// Error satisfies the builtin error interface
func (e ProfitabilityRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sProfitabilityRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ProfitabilityRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ProfitabilityRatioValidationError{}

// Validate checks the field values on EfficiencyRatio with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *EfficiencyRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EfficiencyRatio with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// EfficiencyRatioMultiError, or nil if none found.
func (m *EfficiencyRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *EfficiencyRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Roe

	// no validation rules for Rotc

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EfficiencyRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EfficiencyRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EfficiencyRatioValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EfficiencyRatioMultiError(errors)
	}

	return nil
}

// EfficiencyRatioMultiError is an error wrapping multiple validation errors
// returned by EfficiencyRatio.ValidateAll() if the designated constraints
// aren't met.
type EfficiencyRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EfficiencyRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EfficiencyRatioMultiError) AllErrors() []error { return m }

// EfficiencyRatioValidationError is the validation error returned by
// EfficiencyRatio.Validate if the designated constraints aren't met.
type EfficiencyRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EfficiencyRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EfficiencyRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EfficiencyRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EfficiencyRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EfficiencyRatioValidationError) ErrorName() string { return "EfficiencyRatioValidationError" }

// Error satisfies the builtin error interface
func (e EfficiencyRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEfficiencyRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EfficiencyRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EfficiencyRatioValidationError{}

// Validate checks the field values on FinancialHealthRatio with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FinancialHealthRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FinancialHealthRatio with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FinancialHealthRatioMultiError, or nil if none found.
func (m *FinancialHealthRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *FinancialHealthRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TotalDebtToEquity

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FinancialHealthRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FinancialHealthRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FinancialHealthRatioValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FinancialHealthRatioMultiError(errors)
	}

	return nil
}

// FinancialHealthRatioMultiError is an error wrapping multiple validation
// errors returned by FinancialHealthRatio.ValidateAll() if the designated
// constraints aren't met.
type FinancialHealthRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FinancialHealthRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FinancialHealthRatioMultiError) AllErrors() []error { return m }

// FinancialHealthRatioValidationError is the validation error returned by
// FinancialHealthRatio.Validate if the designated constraints aren't met.
type FinancialHealthRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FinancialHealthRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FinancialHealthRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FinancialHealthRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FinancialHealthRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FinancialHealthRatioValidationError) ErrorName() string {
	return "FinancialHealthRatioValidationError"
}

// Error satisfies the builtin error interface
func (e FinancialHealthRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFinancialHealthRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FinancialHealthRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FinancialHealthRatioValidationError{}

// Validate checks the field values on GrowthRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GrowthRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GrowthRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GrowthRatioMultiError, or
// nil if none found.
func (m *GrowthRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *GrowthRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DilutedEpsGrowth

	// no validation rules for RevenueGrowth

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GrowthRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GrowthRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GrowthRatioValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GrowthRatioMultiError(errors)
	}

	return nil
}

// GrowthRatioMultiError is an error wrapping multiple validation errors
// returned by GrowthRatio.ValidateAll() if the designated constraints aren't met.
type GrowthRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GrowthRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GrowthRatioMultiError) AllErrors() []error { return m }

// GrowthRatioValidationError is the validation error returned by
// GrowthRatio.Validate if the designated constraints aren't met.
type GrowthRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GrowthRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GrowthRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GrowthRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GrowthRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GrowthRatioValidationError) ErrorName() string { return "GrowthRatioValidationError" }

// Error satisfies the builtin error interface
func (e GrowthRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGrowthRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GrowthRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GrowthRatioValidationError{}

// Validate checks the field values on ValuationRatio with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ValuationRatio) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValuationRatio with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ValuationRatioMultiError,
// or nil if none found.
func (m *ValuationRatio) ValidateAll() error {
	return m.validate(true)
}

func (m *ValuationRatio) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for PeRatio

	// no validation rules for PbRatio

	// no validation rules for DividendYieldPercentage

	// no validation rules for EvToEbitda

	// no validation rules for CalendarYear

	// no validation rules for CalendarQuarter

	if all {
		switch v := interface{}(m.GetReportDate()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValuationRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValuationRatioValidationError{
					field:  "ReportDate",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReportDate()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValuationRatioValidationError{
				field:  "ReportDate",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValuationRatioMultiError(errors)
	}

	return nil
}

// ValuationRatioMultiError is an error wrapping multiple validation errors
// returned by ValuationRatio.ValidateAll() if the designated constraints
// aren't met.
type ValuationRatioMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValuationRatioMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValuationRatioMultiError) AllErrors() []error { return m }

// ValuationRatioValidationError is the validation error returned by
// ValuationRatio.Validate if the designated constraints aren't met.
type ValuationRatioValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValuationRatioValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValuationRatioValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValuationRatioValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValuationRatioValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValuationRatioValidationError) ErrorName() string { return "ValuationRatioValidationError" }

// Error satisfies the builtin error interface
func (e ValuationRatioValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValuationRatio.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValuationRatioValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValuationRatioValidationError{}
