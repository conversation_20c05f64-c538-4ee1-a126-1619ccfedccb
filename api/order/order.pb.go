// protolint:disable MAX_LINE_LENGTH

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/order/order.proto

package order

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	queue "github.com/epifi/be-common/api/queue"
	common "github.com/epifi/be-common/api/typesv2/common"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	payment "github.com/epifi/gamma/api/order/payment"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Different status through which order state machine transitions
// through an order lifetime.
// Note- the transition workflow of state machine will change per order type.
// e.g. one change can be some order need payment before fulfillment stage while
// some need it after fulfillment, the state transition will vary for both of these
// use cases.
type OrderStatus int32

const (
	OrderStatus_ORDER_STATUS_UNSPECIFIED OrderStatus = 0
	// Entry created in the system.
	// Ideally, order creation is followed by transaction creation in that case order is moved to IN_PAYMENT.
	// Thus, if order in CREATED state can also be inferred as failure of transaction creation or an abandoned order.
	OrderStatus_CREATED OrderStatus = 1
	// Attempting payment for the order.
	// An order is moved to IN_PAYMENT once transaction initialization is successful.
	OrderStatus_IN_PAYMENT OrderStatus = 2
	// Payment is successful.
	// An order is usually updated as PAID once payment system publish payment successful event to the order orchestrator.
	OrderStatus_PAID OrderStatus = 3
	// Payment attempt failed for the order.
	// An order is usually updated as FAILED once payment system publishes payment failure event to the order orchestrator.
	OrderStatus_PAYMENT_FAILED OrderStatus = 4
	// Fulfillment in progress.
	// This can have multiple meaning based on workflow.
	// Good / Service being activated for the actor.
	// e.g. for RECHARGE workflow this step can signify recharge is in progress.
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_IN_FULFILLMENT OrderStatus = 5
	// Fulfillment successful.
	// This can have multiple meaning based on workflow.
	// Goods / Services received by the actor who created the order
	// e.g. for RECHARGE workflow this step can signify recharge is successful.
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_FULFILLED OrderStatus = 6
	// Fulfillment failed.
	// Goods / Services couldn't not received by the actor who made the payment.
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_FULFILLMENT_FAILED OrderStatus = 7
	// Payment to the service provider is being processed
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_IN_SETTLEMENT OrderStatus = 8
	// Payment successful to the service provider
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_SETTLED OrderStatus = 9
	// Payment to the service provider failed
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderStatus_SETTLEMENT_FAILED OrderStatus = 10
	// Order needs human intervention due to unexpected failures.
	// It can occur due to various scenarios but not limited to:
	// 1) retries exhaustion while one of transaction is still in non-terminal status.
	// 2) one of transaction failed while other succeeded
	OrderStatus_MANUAL_INTERVENTION OrderStatus = 11
	// Collect request registration with vendor is in progress.
	// Typically, an order is in this state when there is an RPC failure due to which order enters an unknown state.
	// Order state machine is finally moved to a terminal state based on status call for vendor
	// An order is moved to COLLECT_REGISTERED once collect registration is successful
	OrderStatus_COLLECT_IN_PROGRESS OrderStatus = 12
	// Collect request has been declined by the payer
	OrderStatus_COLLECT_DISMISSED_BY_PAYER OrderStatus = 13
	// Collect request has been cancelled by the payee or the initiator
	OrderStatus_COLLECT_DISMISSED_BY_PAYEE OrderStatus = 14
	// Collect request was successfully registered with the vendor
	OrderStatus_COLLECT_REGISTERED OrderStatus = 15
	// Collect requested registration has failed. This can be typically due to a permanent
	// failure at vendor's end.
	// this can be due to various reasons some of them are invalid payer address, invalid payee address,
	// missing fields in the request or validation error fo some sorts
	OrderStatus_COLLECT_FAILED OrderStatus = 16
	// Order has expired.
	// Different order workflows have different expiry duration.
	// This happens when payment authorisation doesn't come from
	// the payer with in the stipulated time window
	OrderStatus_EXPIRED OrderStatus = 17
	// Order is rejected by the system when the client initiates an order creation request.
	// An order is rejected usually when one of the velocity or validation checks fail on the server side.
	OrderStatus_REJECTED OrderStatus = 18
	// The payment for order is reversed.
	// For eg. in case of fund transfer the order can be reversed after a successful debit
	// in case the credit fails
	// An order can move into PAYMENT_REVERSED state even from PAYMENT_FAILED/PAID state.
	OrderStatus_PAYMENT_REVERSED OrderStatus = 19
	// Order payment authorisation has finished and corresponding transactions(s) are ready to be initiated
	// with partner bank
	OrderStatus_INITIATED OrderStatus = 20
)

// Enum value maps for OrderStatus.
var (
	OrderStatus_name = map[int32]string{
		0:  "ORDER_STATUS_UNSPECIFIED",
		1:  "CREATED",
		2:  "IN_PAYMENT",
		3:  "PAID",
		4:  "PAYMENT_FAILED",
		5:  "IN_FULFILLMENT",
		6:  "FULFILLED",
		7:  "FULFILLMENT_FAILED",
		8:  "IN_SETTLEMENT",
		9:  "SETTLED",
		10: "SETTLEMENT_FAILED",
		11: "MANUAL_INTERVENTION",
		12: "COLLECT_IN_PROGRESS",
		13: "COLLECT_DISMISSED_BY_PAYER",
		14: "COLLECT_DISMISSED_BY_PAYEE",
		15: "COLLECT_REGISTERED",
		16: "COLLECT_FAILED",
		17: "EXPIRED",
		18: "REJECTED",
		19: "PAYMENT_REVERSED",
		20: "INITIATED",
	}
	OrderStatus_value = map[string]int32{
		"ORDER_STATUS_UNSPECIFIED":   0,
		"CREATED":                    1,
		"IN_PAYMENT":                 2,
		"PAID":                       3,
		"PAYMENT_FAILED":             4,
		"IN_FULFILLMENT":             5,
		"FULFILLED":                  6,
		"FULFILLMENT_FAILED":         7,
		"IN_SETTLEMENT":              8,
		"SETTLED":                    9,
		"SETTLEMENT_FAILED":          10,
		"MANUAL_INTERVENTION":        11,
		"COLLECT_IN_PROGRESS":        12,
		"COLLECT_DISMISSED_BY_PAYER": 13,
		"COLLECT_DISMISSED_BY_PAYEE": 14,
		"COLLECT_REGISTERED":         15,
		"COLLECT_FAILED":             16,
		"EXPIRED":                    17,
		"REJECTED":                   18,
		"PAYMENT_REVERSED":           19,
		"INITIATED":                  20,
	}
)

func (x OrderStatus) Enum() *OrderStatus {
	p := new(OrderStatus)
	*p = x
	return p
}

func (x OrderStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[0].Descriptor()
}

func (OrderStatus) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[0]
}

func (x OrderStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStatus.Descriptor instead.
func (OrderStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{0}
}

// Important: Provenance used may affect transaction handling in scenarios such as
// reversal. E.g. In case the provenance is USER_APP, INTERNAL the orders initiated would be
// eligible for In-App reversal. Please be mindful while passing this value in order creation.
// Provenance: the beginning of something's existence; something's origin.
// An order can be created in the system from different entry points.
// e.g. APP, ATM, POS, etc.
// This enum represents different entry provenance of order in the system
type OrderProvenance int32

const (
	OrderProvenance_ORDER_PROVENANCE_UNSPECIFIED OrderProvenance = 0
	// Signifies the order was created from the user APP.
	OrderProvenance_USER_APP OrderProvenance = 1
	// Signifies order was conceived in an external system
	// Few scenarios when order provenance is marked external-
	// 1) there was an offline transaction (credit/debit) in user's account and epiFi got notified either
	// using notification from vendor or by statement sync mechanism
	// 2) When an external PSP initiates a payment/collect request against epiFi user's VPA in UPI.
	OrderProvenance_EXTERNAL OrderProvenance = 2
	// Signifies order was conceived from an internal process in the system.
	// Few scenarios when order provenance is marked internal -
	// 1) Deposit creation with add funds flow. In this case, an order with URN_TRANSFER workflow is created from the
	// user's app, which internally on successfully processing creates another order with workflow CREATE_DEPOSIT.
	OrderProvenance_INTERNAL OrderProvenance = 3
	// Signifies that the order was created due to an ATM card transaction
	// Few scenarios when order provenance is marked ATM -
	// 1) Epifi received a notification for atm withdrawal
	OrderProvenance_ATM OrderProvenance = 4
	// Signifies that the order was created due to POS transaction
	// Few scenarios when order provenance is marked POS -
	// 1) Epifi received a notification for POS transaction
	OrderProvenance_POS OrderProvenance = 5
	// Signifies that the order was created due to ECOMM transaction
	// Few scenarios when order provenance is marked ECOMM -
	// 1) Epifi received a notification for ECOMM transaction
	OrderProvenance_ECOMM OrderProvenance = 6
	// Signifies that order was created due to third party application eg. Intent, QR
	OrderProvenance_THIRD_PARTY OrderProvenance = 7
	// Signifies order was created as a result of partner mobile banking payment
	// Few scenarios when order provenance is marked PARTNER_MOBILE_BANKING -
	// 1) User goes to partner mobile banking app and does a IMPS/NEFT/RTGS payment.
	OrderProvenance_PARTNER_MOBILE_BANKING OrderProvenance = 8
	// Signifies order was created due to a appeasement payout from customer support team
	OrderProvenance_SHERLOCK OrderProvenance = 9
	// Signifies order was created from Bank side.
	// Few cases would be like: ATM charges, ECS charges etc.
	OrderProvenance_VENDOR_BANK OrderProvenance = 10
	// Signifies order was created from to VISA Money Transfer(VMT) system.
	// Usually E-Commerce web uses VMT for refunding card transactions.
	OrderProvenance_VISA_MONEY_TRANSFER OrderProvenance = 11
	// Signifies order was created as a result of partner net banking payment
	// Few scenarios when order provenance is marked PARTNER_NET_BANKING -
	// 1) User goes to website  and does a IMPS/NEFT/RTGS payment.
	OrderProvenance_PARTNER_NET_BANKING OrderProvenance = 12
)

// Enum value maps for OrderProvenance.
var (
	OrderProvenance_name = map[int32]string{
		0:  "ORDER_PROVENANCE_UNSPECIFIED",
		1:  "USER_APP",
		2:  "EXTERNAL",
		3:  "INTERNAL",
		4:  "ATM",
		5:  "POS",
		6:  "ECOMM",
		7:  "THIRD_PARTY",
		8:  "PARTNER_MOBILE_BANKING",
		9:  "SHERLOCK",
		10: "VENDOR_BANK",
		11: "VISA_MONEY_TRANSFER",
		12: "PARTNER_NET_BANKING",
	}
	OrderProvenance_value = map[string]int32{
		"ORDER_PROVENANCE_UNSPECIFIED": 0,
		"USER_APP":                     1,
		"EXTERNAL":                     2,
		"INTERNAL":                     3,
		"ATM":                          4,
		"POS":                          5,
		"ECOMM":                        6,
		"THIRD_PARTY":                  7,
		"PARTNER_MOBILE_BANKING":       8,
		"SHERLOCK":                     9,
		"VENDOR_BANK":                  10,
		"VISA_MONEY_TRANSFER":          11,
		"PARTNER_NET_BANKING":          12,
	}
)

func (x OrderProvenance) Enum() *OrderProvenance {
	p := new(OrderProvenance)
	*p = x
	return p
}

func (x OrderProvenance) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderProvenance) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[1].Descriptor()
}

func (OrderProvenance) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[1]
}

func (x OrderProvenance) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderProvenance.Descriptor instead.
func (OrderProvenance) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{1}
}

// OrderFieldMask is the enum representation of all the order fields.
// Meant to be used as field mask to help with database updates
type OrderFieldMask int32

const (
	OrderFieldMask_ORDER_FIELD_MASK_UNSPECIFIED OrderFieldMask = 0
	OrderFieldMask_ID                           OrderFieldMask = 1
	OrderFieldMask_FROM_ACTOR_ID                OrderFieldMask = 2
	OrderFieldMask_TO_ACTOR_ID                  OrderFieldMask = 3
	OrderFieldMask_WORKFLOW                     OrderFieldMask = 4
	OrderFieldMask_STATUS                       OrderFieldMask = 5
	OrderFieldMask_ORDER_PAYLOAD                OrderFieldMask = 6
	OrderFieldMask_PROVENANCE                   OrderFieldMask = 7
	OrderFieldMask_AMOUNT                       OrderFieldMask = 8
	OrderFieldMask_CREATED_AT                   OrderFieldMask = 9
	OrderFieldMask_UPDATED_AT                   OrderFieldMask = 10
	OrderFieldMask_EXPIRE_AT                    OrderFieldMask = 11
	OrderFieldMask_ALL                          OrderFieldMask = 12
	OrderFieldMask_TAGS                         OrderFieldMask = 13
	OrderFieldMask_EXTERNAL_ID                  OrderFieldMask = 14
	OrderFieldMask_WORFLOW_REF_ID               OrderFieldMask = 15
	OrderFieldMask_FROM_ACTOR_LOCATION_TOKEN    OrderFieldMask = 16
	OrderFieldMask_TO_ACTOR_LOCATION_TOKEN      OrderFieldMask = 17
	// NOTE: client_req_id is a critical piece for performing any sort of updates and should not be updated blindly without any validations.
	OrderFieldMask_CLIENT_REQ_ID OrderFieldMask = 18
)

// Enum value maps for OrderFieldMask.
var (
	OrderFieldMask_name = map[int32]string{
		0:  "ORDER_FIELD_MASK_UNSPECIFIED",
		1:  "ID",
		2:  "FROM_ACTOR_ID",
		3:  "TO_ACTOR_ID",
		4:  "WORKFLOW",
		5:  "STATUS",
		6:  "ORDER_PAYLOAD",
		7:  "PROVENANCE",
		8:  "AMOUNT",
		9:  "CREATED_AT",
		10: "UPDATED_AT",
		11: "EXPIRE_AT",
		12: "ALL",
		13: "TAGS",
		14: "EXTERNAL_ID",
		15: "WORFLOW_REF_ID",
		16: "FROM_ACTOR_LOCATION_TOKEN",
		17: "TO_ACTOR_LOCATION_TOKEN",
		18: "CLIENT_REQ_ID",
	}
	OrderFieldMask_value = map[string]int32{
		"ORDER_FIELD_MASK_UNSPECIFIED": 0,
		"ID":                           1,
		"FROM_ACTOR_ID":                2,
		"TO_ACTOR_ID":                  3,
		"WORKFLOW":                     4,
		"STATUS":                       5,
		"ORDER_PAYLOAD":                6,
		"PROVENANCE":                   7,
		"AMOUNT":                       8,
		"CREATED_AT":                   9,
		"UPDATED_AT":                   10,
		"EXPIRE_AT":                    11,
		"ALL":                          12,
		"TAGS":                         13,
		"EXTERNAL_ID":                  14,
		"WORFLOW_REF_ID":               15,
		"FROM_ACTOR_LOCATION_TOKEN":    16,
		"TO_ACTOR_LOCATION_TOKEN":      17,
		"CLIENT_REQ_ID":                18,
	}
)

func (x OrderFieldMask) Enum() *OrderFieldMask {
	p := new(OrderFieldMask)
	*p = x
	return p
}

func (x OrderFieldMask) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderFieldMask) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[2].Descriptor()
}

func (OrderFieldMask) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[2]
}

func (x OrderFieldMask) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderFieldMask.Descriptor instead.
func (OrderFieldMask) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{2}
}

// orderAccountRelation tells regarding the relation between the order and its respective account
type OrderAccountRelation int32

const (
	OrderAccountRelation_ORDER_ACCOUNT_RELATION_UNSPECIFIED OrderAccountRelation = 0
	// internal - if order belongs to internal account(fi savings account)
	OrderAccountRelation_INTERNAL_ACCOUNT OrderAccountRelation = 1
	// external - if order doesn't belong to internal accounts(like: Tpap account)
	OrderAccountRelation_EXTERNAL_ACCOUNT OrderAccountRelation = 2
)

// Enum value maps for OrderAccountRelation.
var (
	OrderAccountRelation_name = map[int32]string{
		0: "ORDER_ACCOUNT_RELATION_UNSPECIFIED",
		1: "INTERNAL_ACCOUNT",
		2: "EXTERNAL_ACCOUNT",
	}
	OrderAccountRelation_value = map[string]int32{
		"ORDER_ACCOUNT_RELATION_UNSPECIFIED": 0,
		"INTERNAL_ACCOUNT":                   1,
		"EXTERNAL_ACCOUNT":                   2,
	}
)

func (x OrderAccountRelation) Enum() *OrderAccountRelation {
	p := new(OrderAccountRelation)
	*p = x
	return p
}

func (x OrderAccountRelation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderAccountRelation) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[3].Descriptor()
}

func (OrderAccountRelation) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[3]
}

func (x OrderAccountRelation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderAccountRelation.Descriptor instead.
func (OrderAccountRelation) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{3}
}

// A stage is accumulation of similar order states.
// E.g. PAYMENT is order stage under which order state machine goes through IN_PAYMENT, PAID and PAYMENT_FAILED states.
// Similarly, other order stages are FULFILLMENT, SETTLEMENT, REFUND, etc.
type OrderStage int32

const (
	OrderStage_ORDER_STAGE_UNSPECIFIED OrderStage = 0
	// typically, an order in CREATED state is in CREATION state
	OrderStage_CREATION OrderStage = 1
	// Under payment stage order goes through IN_PAYMENT, PAID, PAYMENT_FAILED, COLLECT_REGISTERED, COLLECT_FAILED,
	// COLLECT_IN_PROGRESS,  COLLECT_DISMISSED_BY_PAYER and COLLECT_DISMISSED_BY_PAYEE states
	// This stage involves money movement from user's account.
	OrderStage_PAYMENT OrderStage = 2
	// Under fulfillment stage order goes through IN_FULFILLMENT, FULFILLED, FULFILLMENT_FAILED states
	// This stage involves provisioning of services to the user
	OrderStage_FULFILLMENT OrderStage = 3
	// Under fulfillment stage order goes through IN_SETTLEMENT, SETTLED, SETTLEMENT_FAILED states
	// This stage involves money movement to the service provider account
	// It can be automated in some cases and in some case triggered by the user depending on the workflow
	OrderStage_SETTLEMENT OrderStage = 4
	// NO_OP(No operation) means non-operation stage
	// It is used in workflow map to signify end of order workflow
	OrderStage_ORDER_STAGE_NO_OP OrderStage = 5
	// Under COLLECT stage order goes through `COLLECT_IN_PROGRESS`, `COLLECT_REGISTERED`, `COLLECT_FAILED` states
	// The stage involves initializing the collect request from the user's account
	OrderStage_COLLECT OrderStage = 6
)

// Enum value maps for OrderStage.
var (
	OrderStage_name = map[int32]string{
		0: "ORDER_STAGE_UNSPECIFIED",
		1: "CREATION",
		2: "PAYMENT",
		3: "FULFILLMENT",
		4: "SETTLEMENT",
		5: "ORDER_STAGE_NO_OP",
		6: "COLLECT",
	}
	OrderStage_value = map[string]int32{
		"ORDER_STAGE_UNSPECIFIED": 0,
		"CREATION":                1,
		"PAYMENT":                 2,
		"FULFILLMENT":             3,
		"SETTLEMENT":              4,
		"ORDER_STAGE_NO_OP":       5,
		"COLLECT":                 6,
	}
)

func (x OrderStage) Enum() *OrderStage {
	p := new(OrderStage)
	*p = x
	return p
}

func (x OrderStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderStage) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[4].Descriptor()
}

func (OrderStage) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[4]
}

func (x OrderStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderStage.Descriptor instead.
func (OrderStage) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{4}
}

// OrderTags includes all the tags an order can have
type OrderTag int32

const (
	OrderTag_ORDER_TAGS_UNSPECIFIED OrderTag = 0
	OrderTag_DEPOSIT                OrderTag = 1
	OrderTag_FD                     OrderTag = 2
	OrderTag_SD                     OrderTag = 3
	OrderTag_RD                     OrderTag = 4
	OrderTag_MERCHANT               OrderTag = 5  // to differentiate between merchant and P2P payments
	OrderTag_REWARD                 OrderTag = 6  // to identify orders associated with rewards like SD Reward, Cash Reward etc.
	OrderTag_FIT                    OrderTag = 7  // to identify orders associated with fit
	OrderTag_INTEREST               OrderTag = 8  // to identify orders associated with interest payout
	OrderTag_CASH                   OrderTag = 9  // to identify orders associated with cash deposits/withdrawals
	OrderTag_INTERNATIONAL          OrderTag = 10 //to identify international orders
	OrderTag_WALLET                 OrderTag = 11 // to identify transactions like wallet top ups
	OrderTag_MUTUAL_FUND            OrderTag = 12 // to identify transactions related to mutual funds
	OrderTag_BHARAT_QR              OrderTag = 13 // to identify bharat qr transactions
	OrderTag_STANDING_INSTRUCTION   OrderTag = 14 // to identify standing instruction orders
	OrderTag_UPI_MANDATES           OrderTag = 15 // to identify upi mandates orders
	OrderTag_LOAN                   OrderTag = 16
	OrderTag_EMI                    OrderTag = 17
	OrderTag_US_STOCKS              OrderTag = 18 // To identify US Stocks orders
	OrderTag_JUMP_P2P_INVESTMENT    OrderTag = 19 // To identify jump/P2P investment orders. Note: we are not populating this for all orders
	// deprecated in favour of US_STOCKS_DIVIDEND_GST_DEBIT and US_STOCKS_SELL_GST_DEBIT
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	OrderTag_US_STOCKS_GST                OrderTag = 20 // To identify GST debit for US Stocks
	OrderTag_FUND_TRANSFER_V1             OrderTag = 21 // To identify fund transfer celestial orders. These workflows support direct notification initiation from the workflow
	OrderTag_US_STOCKS_DIVIDEND_GST_DEBIT OrderTag = 22 // to identify GST debit on receiving dividends for US Stocks
	OrderTag_US_STOCKS_SELL_GST_DEBIT     OrderTag = 23 // to identify GST debit on receiving funds back for sell of US Stocks
	OrderTag_US_STOCKS_DIVIDEND_CREDIT    OrderTag = 24 // to identify dividend credit for us-stocks
	OrderTag_US_STOCKS_SELL_CREDIT        OrderTag = 25 // to identify sell credit for US stocks
	// deprecated as we are further bifurcating ECS_ENACH_CHARGES on more granular level.
	// use NACH_RETURN_CHARGE, ECS_RETURN_CHARGE or ECS_ENACH_MANDATE_CHARGE for correct identifcation of charges
	OrderTag_ECS_ENACH_CHARGES                                OrderTag = 26
	OrderTag_US_STOCKS_INWARD_REMITTANCE_GST_REFUND           OrderTag = 27 // to identify refund credit for GST charges on US-Stocks Inwards remittance
	OrderTag_UPI_LITE_ACTIVATION                              OrderTag = 28 // to identify order created while UPI Lite activation
	OrderTag_UPI_LITE_DEACTIVATION                            OrderTag = 29 // to identify order created while UPI Lite deactivation
	OrderTag_UPI_LITE_TOP_UP                                  OrderTag = 30 // to identify order created while UPI Lite top up
	OrderTag_CHEQUE_BOOK_CHARGES                              OrderTag = 31 // To identify cheque book order debit
	OrderTag_LOAN_EARLY_SALARY                                OrderTag = 32 // To identify early salary si order
	OrderTag_US_STOCKS_AGGREGATED_INWARD_REMITTANCE           OrderTag = 33 // aggregated remittance txn for US stocks sell-orders and dividends
	OrderTag_US_STOCKS_AGGREGATED_INWARD_REMITTANCE_GST_DEBIT OrderTag = 34 // GST debit on aggregated US stocks remittance txn
	OrderTag_DC_FOREX_MARKUP_REFUND                           OrderTag = 35 // To identify debit card forex markup refund
	OrderTag_ENACH                                            OrderTag = 36 // to identify enach orders.
	// to identify charges in case of NACH return. NACH returns happens when we have insufficient balance for any e-NACH created on our account.
	OrderTag_NACH_RETURN_CHARGE             OrderTag = 37
	OrderTag_ECS_RETURN_CHARGE              OrderTag = 38 // to identify charges in case of ECS return. ECS Return is same as NACH return just the protocal is different
	OrderTag_ANYWHERE_BANKING_CHARGE        OrderTag = 39 // to identify charges in case of cash deposit/withdrawal at physical branches
	OrderTag_ATM_DECLINE_CHARGE             OrderTag = 40 // to identify charges for insufficient balance during ATM withdrawal
	OrderTag_DUPLICATE_CARD_FEE             OrderTag = 41 // to identify fee incurred during debit card replacement
	OrderTag_OTHER_BANK_ATM_CHARGE          OrderTag = 42 // to identify charge for cash deposit/withdrawal from any ATM other than account associated Vendor bank ATM
	OrderTag_CASH_HAND_CHARGE               OrderTag = 43 // to identify charges for handling cash at physical branches. It has some overlapping with ANYWHERE_BANKING_CHARGE
	OrderTag_ECS_ENACH_MANDATE_SETUP_CHARGE OrderTag = 44 // to identify charges debited by bank setup of ecs/nach mandate.
	OrderTag_DEBIT_CARD_CHARGES             OrderTag = 45 // to identify orders related to debit card charges eg: Physical debit card dispatch.
	OrderTag_CREDIT_CARD_PAYMENT            OrderTag = 46 // to identify orders related to credit card payment
	OrderTag_ECOM_POS_DECLINE_CHARGE        OrderTag = 48 // to identify charges for insufficient balance during ECOM/POS transactions
	OrderTag_DEBIT_CARD_AMC_CHARGE          OrderTag = 49 // to identify AMC charges for debit card.
	OrderTag_RAZORPAY                       OrderTag = 50 // to identify order related to razorpay
	OrderTag_POST_DEBIT_CHARGE              OrderTag = 51 // Charges debited by a vendor post the original transaction
	OrderTag_DC_FOREX_MARKUP_CHARGE         OrderTag = 52 // to identify debit card forex markup charge
	OrderTag_DC_TCS_FEE_CHARGE              OrderTag = 53 // to identify debit card TCS fee charge
	OrderTag_DC_DCC_FEE_CHARGE              OrderTag = 54 // to identify debit card DCC fee charge
	OrderTag_CHEQUE                         OrderTag = 55 // to identify cheque related orders
	OrderTag_CHEQUE_REVERSAL                OrderTag = 56 // to identify cheque reversal/bounces related orders
	OrderTag_PG_REFUND                      OrderTag = 57 // to identify orders that are created due to refunds to customers initiated from payment gateway
	OrderTag_DC_MANDATE_REGISTER            OrderTag = 58 // to identify debit card mandate registration transaction
	OrderTag_DC_MANDATE_PAYMENT             OrderTag = 59 // to identify debit card mandate payment transaction
	OrderTag_AMB_CHARGE                     OrderTag = 60 // to identify orders corresponding to non-maintenance of AMB (Average Monthly Balance) charges
	OrderTag_PREDICTED_MERCHANT             OrderTag = 61 // to identify whether a NEFT/IMPS/RTGS credit transaction to a user has been made by a merchant based on the remitter name.
	OrderTag_NFC                            OrderTag = 62 // to identify orders made via NFC payment mode
	OrderTag_FIRST_CARD_ORDER_FEE           OrderTag = 63 // to identify orders corresponding to first card order fee
	OrderTag_INTL_ATM_CHARGE                OrderTag = 64 // to identify charges posted in a user's savings account for transacting in an ATM outside india
	OrderTag_TOD_CHARGE                     OrderTag = 65 // to identify temporary over draft charges
	OrderTag_RECHARGE_PAYMENT               OrderTag = 66 // to identify recharge mobile transactions
)

// Enum value maps for OrderTag.
var (
	OrderTag_name = map[int32]string{
		0:  "ORDER_TAGS_UNSPECIFIED",
		1:  "DEPOSIT",
		2:  "FD",
		3:  "SD",
		4:  "RD",
		5:  "MERCHANT",
		6:  "REWARD",
		7:  "FIT",
		8:  "INTEREST",
		9:  "CASH",
		10: "INTERNATIONAL",
		11: "WALLET",
		12: "MUTUAL_FUND",
		13: "BHARAT_QR",
		14: "STANDING_INSTRUCTION",
		15: "UPI_MANDATES",
		16: "LOAN",
		17: "EMI",
		18: "US_STOCKS",
		19: "JUMP_P2P_INVESTMENT",
		20: "US_STOCKS_GST",
		21: "FUND_TRANSFER_V1",
		22: "US_STOCKS_DIVIDEND_GST_DEBIT",
		23: "US_STOCKS_SELL_GST_DEBIT",
		24: "US_STOCKS_DIVIDEND_CREDIT",
		25: "US_STOCKS_SELL_CREDIT",
		26: "ECS_ENACH_CHARGES",
		27: "US_STOCKS_INWARD_REMITTANCE_GST_REFUND",
		28: "UPI_LITE_ACTIVATION",
		29: "UPI_LITE_DEACTIVATION",
		30: "UPI_LITE_TOP_UP",
		31: "CHEQUE_BOOK_CHARGES",
		32: "LOAN_EARLY_SALARY",
		33: "US_STOCKS_AGGREGATED_INWARD_REMITTANCE",
		34: "US_STOCKS_AGGREGATED_INWARD_REMITTANCE_GST_DEBIT",
		35: "DC_FOREX_MARKUP_REFUND",
		36: "ENACH",
		37: "NACH_RETURN_CHARGE",
		38: "ECS_RETURN_CHARGE",
		39: "ANYWHERE_BANKING_CHARGE",
		40: "ATM_DECLINE_CHARGE",
		41: "DUPLICATE_CARD_FEE",
		42: "OTHER_BANK_ATM_CHARGE",
		43: "CASH_HAND_CHARGE",
		44: "ECS_ENACH_MANDATE_SETUP_CHARGE",
		45: "DEBIT_CARD_CHARGES",
		46: "CREDIT_CARD_PAYMENT",
		48: "ECOM_POS_DECLINE_CHARGE",
		49: "DEBIT_CARD_AMC_CHARGE",
		50: "RAZORPAY",
		51: "POST_DEBIT_CHARGE",
		52: "DC_FOREX_MARKUP_CHARGE",
		53: "DC_TCS_FEE_CHARGE",
		54: "DC_DCC_FEE_CHARGE",
		55: "CHEQUE",
		56: "CHEQUE_REVERSAL",
		57: "PG_REFUND",
		58: "DC_MANDATE_REGISTER",
		59: "DC_MANDATE_PAYMENT",
		60: "AMB_CHARGE",
		61: "PREDICTED_MERCHANT",
		62: "NFC",
		63: "FIRST_CARD_ORDER_FEE",
		64: "INTL_ATM_CHARGE",
		65: "TOD_CHARGE",
		66: "RECHARGE_PAYMENT",
	}
	OrderTag_value = map[string]int32{
		"ORDER_TAGS_UNSPECIFIED":                 0,
		"DEPOSIT":                                1,
		"FD":                                     2,
		"SD":                                     3,
		"RD":                                     4,
		"MERCHANT":                               5,
		"REWARD":                                 6,
		"FIT":                                    7,
		"INTEREST":                               8,
		"CASH":                                   9,
		"INTERNATIONAL":                          10,
		"WALLET":                                 11,
		"MUTUAL_FUND":                            12,
		"BHARAT_QR":                              13,
		"STANDING_INSTRUCTION":                   14,
		"UPI_MANDATES":                           15,
		"LOAN":                                   16,
		"EMI":                                    17,
		"US_STOCKS":                              18,
		"JUMP_P2P_INVESTMENT":                    19,
		"US_STOCKS_GST":                          20,
		"FUND_TRANSFER_V1":                       21,
		"US_STOCKS_DIVIDEND_GST_DEBIT":           22,
		"US_STOCKS_SELL_GST_DEBIT":               23,
		"US_STOCKS_DIVIDEND_CREDIT":              24,
		"US_STOCKS_SELL_CREDIT":                  25,
		"ECS_ENACH_CHARGES":                      26,
		"US_STOCKS_INWARD_REMITTANCE_GST_REFUND": 27,
		"UPI_LITE_ACTIVATION":                    28,
		"UPI_LITE_DEACTIVATION":                  29,
		"UPI_LITE_TOP_UP":                        30,
		"CHEQUE_BOOK_CHARGES":                    31,
		"LOAN_EARLY_SALARY":                      32,
		"US_STOCKS_AGGREGATED_INWARD_REMITTANCE": 33,
		"US_STOCKS_AGGREGATED_INWARD_REMITTANCE_GST_DEBIT": 34,
		"DC_FOREX_MARKUP_REFUND":                           35,
		"ENACH":                                            36,
		"NACH_RETURN_CHARGE":                               37,
		"ECS_RETURN_CHARGE":                                38,
		"ANYWHERE_BANKING_CHARGE":                          39,
		"ATM_DECLINE_CHARGE":                               40,
		"DUPLICATE_CARD_FEE":                               41,
		"OTHER_BANK_ATM_CHARGE":                            42,
		"CASH_HAND_CHARGE":                                 43,
		"ECS_ENACH_MANDATE_SETUP_CHARGE":                   44,
		"DEBIT_CARD_CHARGES":                               45,
		"CREDIT_CARD_PAYMENT":                              46,
		"ECOM_POS_DECLINE_CHARGE":                          48,
		"DEBIT_CARD_AMC_CHARGE":                            49,
		"RAZORPAY":                                         50,
		"POST_DEBIT_CHARGE":                                51,
		"DC_FOREX_MARKUP_CHARGE":                           52,
		"DC_TCS_FEE_CHARGE":                                53,
		"DC_DCC_FEE_CHARGE":                                54,
		"CHEQUE":                                           55,
		"CHEQUE_REVERSAL":                                  56,
		"PG_REFUND":                                        57,
		"DC_MANDATE_REGISTER":                              58,
		"DC_MANDATE_PAYMENT":                               59,
		"AMB_CHARGE":                                       60,
		"PREDICTED_MERCHANT":                               61,
		"NFC":                                              62,
		"FIRST_CARD_ORDER_FEE":                             63,
		"INTL_ATM_CHARGE":                                  64,
		"TOD_CHARGE":                                       65,
		"RECHARGE_PAYMENT":                                 66,
	}
)

func (x OrderTag) Enum() *OrderTag {
	p := new(OrderTag)
	*p = x
	return p
}

func (x OrderTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrderTag) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[5].Descriptor()
}

func (OrderTag) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[5]
}

func (x OrderTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrderTag.Descriptor instead.
func (OrderTag) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{5}
}

type UIEntryPoint int32

const (
	// unspecified
	UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED UIEntryPoint = 0
	// Signifies order was created using timeline
	// wil be used as default in case entry point is unspecified
	UIEntryPoint_TIMELINE UIEntryPoint = 1
	// Signifies order was created for secure QR code scan
	UIEntryPoint_SECURE_QR_CODE UIEntryPoint = 2
	// Signifies order was created using insecure QR code scan
	UIEntryPoint_INSECURE_QR_CODE UIEntryPoint = 3
	// Signifies order was created using secure intent payment
	UIEntryPoint_SECURE_INTENT UIEntryPoint = 4
	// Signifies order was created using insecure intent payment
	UIEntryPoint_INSECURE_INTENT UIEntryPoint = 5
	// Signifies order was created using add funds flow during onboarding
	UIEntryPoint_ONBOARD_ADD_FUNDS UIEntryPoint = 6
	// Signifies order was created using transfer in flow
	UIEntryPoint_TRANSFER_IN UIEntryPoint = 7
	// Signifies add funds order was created from home screen
	UIEntryPoint_HOME UIEntryPoint = 8
	// Signifies order was created from account details screen
	UIEntryPoint_ACCOUNT_DETAILS UIEntryPoint = 9
	// Signifies order was created from account summary screen
	UIEntryPoint_ACCOUNT_SUMMARY UIEntryPoint = 10
	// Signifies order was created from referrals screen
	UIEntryPoint_REFERRALS UIEntryPoint = 11
	// Signifies order created from Home screen's persistent Add funds button
	UIEntryPoint_HOME_PERSISTENT_CTA UIEntryPoint = 12
	// Signifies order was created from Deposit creation flow, if funds were insufficient
	// for deposit creation
	UIEntryPoint_DEPOSIT_CREATION UIEntryPoint = 13
	// Signifies order was created from Bonus jar creation flow, if funds were insufficient
	// for bonus jar creation
	UIEntryPoint_BONUS_JAR_CREATION UIEntryPoint = 14
	// Signifies order was created from FITTT screen
	UIEntryPoint_FITTT UIEntryPoint = 15
	// Request has been received from Auto Pay hub screen
	UIEntryPoint_AUTO_PAY_HUB UIEntryPoint = 16
	// Request has come from One time mutual fund investment screen
	UIEntryPoint_MF_BUY_ONE_TIME UIEntryPoint = 17
	// Request has come from physical debit card charges flow
	UIEntryPoint_PHYSICAL_DEBIT_CARD_CHARGES UIEntryPoint = 18
	// Signifies order was created from AskFi result summary
	UIEntryPoint_ASK_FI UIEntryPoint = 19
	// Signifies order is getting created while activation of UPI Lite
	UIEntryPoint_ACTIVATE_UPI_LITE UIEntryPoint = 20
	// Signifies order is getting created for UPI Lite top up
	UIEntryPoint_TOP_UP_UPI_LITE UIEntryPoint = 21
	// Signifies order is getting created while deletion of UPI Lite
	UIEntryPoint_DELETE_UPI_LITE UIEntryPoint = 22
	// Signifies order is created by pre approved loan
	UIEntryPoint_PRE_APPROVED_LOAN UIEntryPoint = 23
	// This entry point is to be used for penny drop flows
	UIEntryPoint_PENNY_DROP UIEntryPoint = 24
	// Signifies order is created from secure cc flow
	UIEntryPoint_SECURED_CC UIEntryPoint = 25
	// Signifies order was created for secure QR code scan
	// i.e. This entryPoint will be chosen when user tries to pay
	// via qr share and pay option to a verified urn.
	UIEntryPoint_SECURE_QR_SHARE_AND_PAY UIEntryPoint = 26
	// Signifies order was created for insecure QR code scan
	// i.e. This entryPoint will be chosen when user tries to pay
	// via qr share and pay option to an unverified urn.
	UIEntryPoint_INSECURE_QR_SHARE_AND_PAY UIEntryPoint = 27
	// Signifies order is getting created in AA salary add funds flow
	// figma: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=19696-28555&mode=design&t=13Y3cc6K5WNDYx1P-0
	UIEntryPoint_AA_SALARY UIEntryPoint = 28
	// if a user has insufficient fund in savings account, we show the payment options to add funds
	// figma: https://www.figma.com/design/kLX73V4pRucy8JM71ajH50/%F0%9F%9B%A0-US-stocks%2Fworkfile?node-id=7099-7475&t=csC27maxcurUmfmU-4
	UIEntryPoint_ADD_FUNDS_USS UIEntryPoint = 29
	// signifies that the order creation has been done from the preapproved
	// loans prepay's end
	UIEntryPoint_UI_ENTRY_POINT_LOAN_PREPAYMENT UIEntryPoint = 30
	// signifies that the order creation has been done to register a mandate
	// using an external route (eg. PG)
	UIEntryPoint_UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION UIEntryPoint = 31
	// Add funds flow initiated from account closure screen for pending charges
	UIEntryPoint_ACCOUNT_CLOSURE_PENDING_CHARGES UIEntryPoint = 32
	// Signifies order creation was initiated by pre-funding add funds flow during onboarding
	UIEntryPoint_ONBOARD_ADD_FUNDS_PRE_FUNDING UIEntryPoint = 33
	// order creation was initiated from tiering all plans screen
	UIEntryPoint_TIERING_ALL_PLANS_JOIN_PLUS     UIEntryPoint = 34
	UIEntryPoint_TIERING_ALL_PLANS_JOIN_INFINITE UIEntryPoint = 35
	UIEntryPoint_TIERING_ALL_PLANS_JOIN_PRIME    UIEntryPoint = 36
	// Add funds flow initiated from AMB details screen
	UIEntryPoint_AMB_DETAILS UIEntryPoint = 37
	// Signifies order was created from recharge payment flow
	UIEntryPoint_UI_ENTRY_POINT_RECHARGE_PAYMENT UIEntryPoint = 38
)

// Enum value maps for UIEntryPoint.
var (
	UIEntryPoint_name = map[int32]string{
		0:  "UI_ENTRY_POINT_UNSPECIFIED",
		1:  "TIMELINE",
		2:  "SECURE_QR_CODE",
		3:  "INSECURE_QR_CODE",
		4:  "SECURE_INTENT",
		5:  "INSECURE_INTENT",
		6:  "ONBOARD_ADD_FUNDS",
		7:  "TRANSFER_IN",
		8:  "HOME",
		9:  "ACCOUNT_DETAILS",
		10: "ACCOUNT_SUMMARY",
		11: "REFERRALS",
		12: "HOME_PERSISTENT_CTA",
		13: "DEPOSIT_CREATION",
		14: "BONUS_JAR_CREATION",
		15: "FITTT",
		16: "AUTO_PAY_HUB",
		17: "MF_BUY_ONE_TIME",
		18: "PHYSICAL_DEBIT_CARD_CHARGES",
		19: "ASK_FI",
		20: "ACTIVATE_UPI_LITE",
		21: "TOP_UP_UPI_LITE",
		22: "DELETE_UPI_LITE",
		23: "PRE_APPROVED_LOAN",
		24: "PENNY_DROP",
		25: "SECURED_CC",
		26: "SECURE_QR_SHARE_AND_PAY",
		27: "INSECURE_QR_SHARE_AND_PAY",
		28: "AA_SALARY",
		29: "ADD_FUNDS_USS",
		30: "UI_ENTRY_POINT_LOAN_PREPAYMENT",
		31: "UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION",
		32: "ACCOUNT_CLOSURE_PENDING_CHARGES",
		33: "ONBOARD_ADD_FUNDS_PRE_FUNDING",
		34: "TIERING_ALL_PLANS_JOIN_PLUS",
		35: "TIERING_ALL_PLANS_JOIN_INFINITE",
		36: "TIERING_ALL_PLANS_JOIN_PRIME",
		37: "AMB_DETAILS",
		38: "UI_ENTRY_POINT_RECHARGE_PAYMENT",
	}
	UIEntryPoint_value = map[string]int32{
		"UI_ENTRY_POINT_UNSPECIFIED":     0,
		"TIMELINE":                       1,
		"SECURE_QR_CODE":                 2,
		"INSECURE_QR_CODE":               3,
		"SECURE_INTENT":                  4,
		"INSECURE_INTENT":                5,
		"ONBOARD_ADD_FUNDS":              6,
		"TRANSFER_IN":                    7,
		"HOME":                           8,
		"ACCOUNT_DETAILS":                9,
		"ACCOUNT_SUMMARY":                10,
		"REFERRALS":                      11,
		"HOME_PERSISTENT_CTA":            12,
		"DEPOSIT_CREATION":               13,
		"BONUS_JAR_CREATION":             14,
		"FITTT":                          15,
		"AUTO_PAY_HUB":                   16,
		"MF_BUY_ONE_TIME":                17,
		"PHYSICAL_DEBIT_CARD_CHARGES":    18,
		"ASK_FI":                         19,
		"ACTIVATE_UPI_LITE":              20,
		"TOP_UP_UPI_LITE":                21,
		"DELETE_UPI_LITE":                22,
		"PRE_APPROVED_LOAN":              23,
		"PENNY_DROP":                     24,
		"SECURED_CC":                     25,
		"SECURE_QR_SHARE_AND_PAY":        26,
		"INSECURE_QR_SHARE_AND_PAY":      27,
		"AA_SALARY":                      28,
		"ADD_FUNDS_USS":                  29,
		"UI_ENTRY_POINT_LOAN_PREPAYMENT": 30,
		"UI_ENTRY_POINT_EXTERNAL_MANDATE_REGISTRATION": 31,
		"ACCOUNT_CLOSURE_PENDING_CHARGES":              32,
		"ONBOARD_ADD_FUNDS_PRE_FUNDING":                33,
		"TIERING_ALL_PLANS_JOIN_PLUS":                  34,
		"TIERING_ALL_PLANS_JOIN_INFINITE":              35,
		"TIERING_ALL_PLANS_JOIN_PRIME":                 36,
		"AMB_DETAILS":                                  37,
		"UI_ENTRY_POINT_RECHARGE_PAYMENT":              38,
	}
)

func (x UIEntryPoint) Enum() *UIEntryPoint {
	p := new(UIEntryPoint)
	*p = x
	return p
}

func (x UIEntryPoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UIEntryPoint) Descriptor() protoreflect.EnumDescriptor {
	return file_api_order_order_proto_enumTypes[6].Descriptor()
}

func (UIEntryPoint) Type() protoreflect.EnumType {
	return &file_api_order_order_proto_enumTypes[6]
}

func (x UIEntryPoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UIEntryPoint.Descriptor instead.
func (UIEntryPoint) EnumDescriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{6}
}

// We define an order as a workflow for exchange of goods and services between two actors in our system with minimum
// one transaction involved.
type Order struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// actor who initiated the order
	FromActorId string `protobuf:"bytes,2,opt,name=from_actor_id,json=fromActorId,proto3" json:"from_actor_id,omitempty"`
	// receiving entity of the order or the service provider
	ToActorId string `protobuf:"bytes,3,opt,name=to_actor_id,json=toActorId,proto3" json:"to_actor_id,omitempty"`
	// workflow followed by the order
	Workflow OrderWorkflow `protobuf:"varint,4,opt,name=workflow,proto3,enum=order.OrderWorkflow" json:"workflow,omitempty"`
	// order's current status
	Status OrderStatus `protobuf:"varint,5,opt,name=status,proto3,enum=order.OrderStatus" json:"status,omitempty"`
	// An opaque blob containing the data needed for fulfillment of an order.
	// This might vary based on the type of order. The data inside the blob will
	// depend on underlying domain service.
	OrderPayload []byte          `protobuf:"bytes,6,opt,name=order_payload,json=orderPayload,proto3" json:"order_payload,omitempty"`
	Provenance   OrderProvenance `protobuf:"varint,7,opt,name=provenance,proto3,enum=order.OrderProvenance" json:"provenance,omitempty"`
	// Final order amount. (Incl. of all taxes and service charges)
	Amount *money.Money `protobuf:"bytes,8,opt,name=amount,proto3" json:"amount,omitempty"`
	// order creation timestamp
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	// order last updated time stamp
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// order deleted time stamp
	DeletedAt *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=deleted_at,json=deletedAt,proto3" json:"deleted_at,omitempty"`
	// Defines the time till which an order is valid. A payment/transactions must not be initialized beyond
	// if the order has expired because of following reason.
	//  1. Some orders can possibly have expiration associated with them .. taking a typical case of gold
	//     transfer where price of an item say gold fluctuates and is locked for the next 5 minutes.
	//  2. UPI collect requests which comes with a validity time.
	//  3. Orders associated with P2P Fund transfer can have risk factor associated with them (in terms of unlocked phone exposure)
	//     in case if we allow transaction for order created lets say 1 hour ago.
	ExpireAt *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=expire_at,json=expireAt,proto3" json:"expire_at,omitempty"`
	// external is internal id equivalent for an order,
	// that can be shared with actor or any other external system inorder to identify an order uniquely in the system.
	// It can be typically used in places where for
	// security reasons we don't want to expose internal id to the outside world
	ExternalId string `protobuf:"bytes,13,opt,name=external_id,json=externalId,proto3" json:"external_id,omitempty"`
	// Defines the tag associated with the order, e.g. Deposit, FD, etc.
	// An order can be associated with multiple tags.
	Tags []OrderTag `protobuf:"varint,14,rep,packed,name=tags,proto3,enum=order.OrderTag" json:"tags,omitempty"`
	// Optional: Signifies the UI entrypoint from which order was created.
	// Only to be populated for app initiated transactions
	UiEntryPoint UIEntryPoint `protobuf:"varint,15,opt,name=ui_entry_point,json=uiEntryPoint,proto3,enum=order.UIEntryPoint" json:"ui_entry_point,omitempty"`
	// request id sent by client in case idempotency around
	// order creation is important for the caller use case. This can be typically
	// used for automated workflows like B2C_FUND_TRANSFER where single order per request
	// is important.
	// client_req_id must be a valid UUID (via RFC 4122)
	ClientReqId string `protobuf:"bytes,16,opt,name=client_req_id,json=clientReqId,proto3" json:"client_req_id,omitempty"`
	// unique identifier to map an order to a celestial workflow request
	// a single workflow request can have more than one order in general
	WorkflowRefId string `protobuf:"bytes,17,opt,name=workflow_ref_id,json=workflowRefId,proto3" json:"workflow_ref_id,omitempty"`
	// Obfuscated GPS coordinates location identifier for the payer.
	// Since, location identifier is a sensitive user information, it's not
	// recommended to store this data directly in the order domain object.
	// A location token is a place holder for the exact user co-ordinates.
	// Co-ordinates for the specified token can be fetched using location service's
	// GetCoordinates RPC
	// Note - Optional field, will be populated only if the payer's location is known
	FromActorLocationToken string `protobuf:"bytes,18,opt,name=from_actor_location_token,json=fromActorLocationToken,proto3" json:"from_actor_location_token,omitempty"`
	// Obfuscated GPS coordinates location identifier for the payee.
	// Since, location identifier is a sensitive user information, it's not
	// recommended to store this data directly in the order domain object.
	// A location token is a place holder for the exact user co-ordinates.
	// Co-ordinates for the specified token can be fetched using location service's
	// GetCoordinates RPC
	// Note - Optional field, will be populated only if the payee's location is known
	ToActorLocationToken string `protobuf:"bytes,19,opt,name=to_actor_location_token,json=toActorLocationToken,proto3" json:"to_actor_location_token,omitempty"`
	// This deeplink is where user is redirected post authorisation of payment
	// If not specified client is redirected to fund transfer status screen by default.
	// We are not directly using deeplink here to avoid frontend dependency in the root domain proto
	// [deprecated in favour of post_auth_deeplink_v1 in order to have some validation on type of
	// object passed by client and also to avoid multiple conversions of deeplink to Any object and
	// vice-versa]
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	PostAuthDeeplink *anypb.Any `protobuf:"bytes,20,opt,name=post_auth_deeplink,json=postAuthDeeplink,proto3" json:"post_auth_deeplink,omitempty"`
	// This deeplink is where user is redirected post payment. Irrespective of it's state
	// i.e. success or failure.
	// If not passed by default, it takes user to home (in case of add-funds),
	// and to receipt or post-payment-success screen etc.
	// [deprecated in favour of post_payment_deeplink_v1 in order to have some validation on type of
	// object passed by client and also to avoid multiple conversions of deeplink to Any object and
	// vice-versa]
	//
	// Deprecated: Marked as deprecated in api/order/order.proto.
	PostPaymentDeeplink *anypb.Any `protobuf:"bytes,21,opt,name=post_payment_deeplink,json=postPaymentDeeplink,proto3" json:"post_payment_deeplink,omitempty"`
	// This deeplink is where user is redirected post authorisation of payment
	// If not specified client is redirected to fund transfer status screen by default.
	// NOTE - post_auth_deeplink_v1 will be given priority over post_auth_deeplink
	PostAuthDeeplinkV1 *deeplink.Deeplink `protobuf:"bytes,22,opt,name=post_auth_deeplink_v1,json=postAuthDeeplinkV1,proto3" json:"post_auth_deeplink_v1,omitempty"`
	// This deeplink is where user is redirected post payment. Irrespective of it's state
	// i.e. success or failure.
	// If not passed by default, it takes user to home (in case of add-funds),
	// and to receipt or post-payment-success screen etc.
	// NOTE - post_payment_deeplink_v1 will be given priority over post_payment_deeplink
	PostPaymentDeeplinkV1 *deeplink.Deeplink `protobuf:"bytes,23,opt,name=post_payment_deeplink_v1,json=postPaymentDeeplinkV1,proto3" json:"post_payment_deeplink_v1,omitempty"`
}

func (x *Order) Reset() {
	*x = Order{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Order) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Order) ProtoMessage() {}

func (x *Order) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Order.ProtoReflect.Descriptor instead.
func (*Order) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{0}
}

func (x *Order) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Order) GetFromActorId() string {
	if x != nil {
		return x.FromActorId
	}
	return ""
}

func (x *Order) GetToActorId() string {
	if x != nil {
		return x.ToActorId
	}
	return ""
}

func (x *Order) GetWorkflow() OrderWorkflow {
	if x != nil {
		return x.Workflow
	}
	return OrderWorkflow_ORDER_WORKFLOW_UNSPECIFIED
}

func (x *Order) GetStatus() OrderStatus {
	if x != nil {
		return x.Status
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *Order) GetOrderPayload() []byte {
	if x != nil {
		return x.OrderPayload
	}
	return nil
}

func (x *Order) GetProvenance() OrderProvenance {
	if x != nil {
		return x.Provenance
	}
	return OrderProvenance_ORDER_PROVENANCE_UNSPECIFIED
}

func (x *Order) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *Order) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Order) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *Order) GetDeletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.DeletedAt
	}
	return nil
}

func (x *Order) GetExpireAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpireAt
	}
	return nil
}

func (x *Order) GetExternalId() string {
	if x != nil {
		return x.ExternalId
	}
	return ""
}

func (x *Order) GetTags() []OrderTag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Order) GetUiEntryPoint() UIEntryPoint {
	if x != nil {
		return x.UiEntryPoint
	}
	return UIEntryPoint_UI_ENTRY_POINT_UNSPECIFIED
}

func (x *Order) GetClientReqId() string {
	if x != nil {
		return x.ClientReqId
	}
	return ""
}

func (x *Order) GetWorkflowRefId() string {
	if x != nil {
		return x.WorkflowRefId
	}
	return ""
}

func (x *Order) GetFromActorLocationToken() string {
	if x != nil {
		return x.FromActorLocationToken
	}
	return ""
}

func (x *Order) GetToActorLocationToken() string {
	if x != nil {
		return x.ToActorLocationToken
	}
	return ""
}

// Deprecated: Marked as deprecated in api/order/order.proto.
func (x *Order) GetPostAuthDeeplink() *anypb.Any {
	if x != nil {
		return x.PostAuthDeeplink
	}
	return nil
}

// Deprecated: Marked as deprecated in api/order/order.proto.
func (x *Order) GetPostPaymentDeeplink() *anypb.Any {
	if x != nil {
		return x.PostPaymentDeeplink
	}
	return nil
}

func (x *Order) GetPostAuthDeeplinkV1() *deeplink.Deeplink {
	if x != nil {
		return x.PostAuthDeeplinkV1
	}
	return nil
}

func (x *Order) GetPostPaymentDeeplinkV1() *deeplink.Deeplink {
	if x != nil {
		return x.PostPaymentDeeplinkV1
	}
	return nil
}

// OrderWithTransactions is a wrapper message containing order along with a list of transactions associated with it.
type OrderWithTransactions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Order object containing order data.
	Order *Order `protobuf:"bytes,1,opt,name=order,proto3" json:"order,omitempty"`
	// list of transactions associated with an order.
	// an order can have more than one transactions under-neath
	Transactions []*payment.Transaction `protobuf:"bytes,2,rep,name=transactions,proto3" json:"transactions,omitempty"`
}

func (x *OrderWithTransactions) Reset() {
	*x = OrderWithTransactions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderWithTransactions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderWithTransactions) ProtoMessage() {}

func (x *OrderWithTransactions) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderWithTransactions.ProtoReflect.Descriptor instead.
func (*OrderWithTransactions) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{1}
}

func (x *OrderWithTransactions) GetOrder() *Order {
	if x != nil {
		return x.Order
	}
	return nil
}

func (x *OrderWithTransactions) GetTransactions() []*payment.Transaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

// OrderUpdate is the message to be published to a topic when there is a update in order state
type OrderUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader         *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	OrderWithTransactions *OrderWithTransactions       `protobuf:"bytes,2,opt,name=orderWithTransactions,proto3" json:"orderWithTransactions,omitempty"`
	// orderAccountRelation will tell if the order is for internal account or not
	OrderAccountRelation OrderAccountRelation `protobuf:"varint,3,opt,name=orderAccountRelation,proto3,enum=order.OrderAccountRelation" json:"orderAccountRelation,omitempty"`
	// ownership denotes the RE level entity-segregation ownership for which this update is being published.
	// This ownership does not convey the row level entity segregation of the txns (i.e. it returns ownership
	// as EPIFI_TECH, for both FEDERAL_BANK & EPIFI_TECH owned transactions). In other words, it denotes the ownership
	// which should be used to find the DB to connect to. In order to determine the actual ownership of the transaction,
	// check the ownership field contained in the transaction payload.
	Ownership common.Ownership `protobuf:"varint,4,opt,name=ownership,proto3,enum=api.typesv2.common.Ownership" json:"ownership,omitempty"`
}

func (x *OrderUpdate) Reset() {
	*x = OrderUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderUpdate) ProtoMessage() {}

func (x *OrderUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderUpdate.ProtoReflect.Descriptor instead.
func (*OrderUpdate) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{2}
}

func (x *OrderUpdate) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *OrderUpdate) GetOrderWithTransactions() *OrderWithTransactions {
	if x != nil {
		return x.OrderWithTransactions
	}
	return nil
}

func (x *OrderUpdate) GetOrderAccountRelation() OrderAccountRelation {
	if x != nil {
		return x.OrderAccountRelation
	}
	return OrderAccountRelation_ORDER_ACCOUNT_RELATION_UNSPECIFIED
}

func (x *OrderUpdate) GetOwnership() common.Ownership {
	if x != nil {
		return x.Ownership
	}
	return common.Ownership(0)
}

// BatchOrderUpdate is the message to be published to a topic after batch of order is updated
type BatchOrderUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	Orders        []*Order                     `protobuf:"bytes,2,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *BatchOrderUpdate) Reset() {
	*x = BatchOrderUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BatchOrderUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchOrderUpdate) ProtoMessage() {}

func (x *BatchOrderUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchOrderUpdate.ProtoReflect.Descriptor instead.
func (*BatchOrderUpdate) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{3}
}

func (x *BatchOrderUpdate) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *BatchOrderUpdate) GetOrders() []*Order {
	if x != nil {
		return x.Orders
	}
	return nil
}

type UpdateInPaymentOrderTimelineRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer request
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// order with txn on which the event is published
	OrderWithTransactions *OrderWithTransactions `protobuf:"bytes,2,opt,name=orderWithTransactions,proto3" json:"orderWithTransactions,omitempty"`
}

func (x *UpdateInPaymentOrderTimelineRequest) Reset() {
	*x = UpdateInPaymentOrderTimelineRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateInPaymentOrderTimelineRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateInPaymentOrderTimelineRequest) ProtoMessage() {}

func (x *UpdateInPaymentOrderTimelineRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateInPaymentOrderTimelineRequest.ProtoReflect.Descriptor instead.
func (*UpdateInPaymentOrderTimelineRequest) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateInPaymentOrderTimelineRequest) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *UpdateInPaymentOrderTimelineRequest) GetOrderWithTransactions() *OrderWithTransactions {
	if x != nil {
		return x.OrderWithTransactions
	}
	return nil
}

// orders and transactions are filtered based upon the combination of status and workflow.
// A workflow can have multiple status which may change with time and for every status we do not want to show to the user
// for eg. OrderWorkflow_REWARDS_ADD_FUNDS_SD is only shown when the status is FULFILLMENT
type OrderStatusAndWorkflowTypeFilter struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OrderStatus OrderStatus     `protobuf:"varint,1,opt,name=order_status,json=orderStatus,proto3,enum=order.OrderStatus" json:"order_status,omitempty"`
	Workflows   []OrderWorkflow `protobuf:"varint,2,rep,packed,name=workflows,proto3,enum=order.OrderWorkflow" json:"workflows,omitempty"`
}

func (x *OrderStatusAndWorkflowTypeFilter) Reset() {
	*x = OrderStatusAndWorkflowTypeFilter{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OrderStatusAndWorkflowTypeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderStatusAndWorkflowTypeFilter) ProtoMessage() {}

func (x *OrderStatusAndWorkflowTypeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderStatusAndWorkflowTypeFilter.ProtoReflect.Descriptor instead.
func (*OrderStatusAndWorkflowTypeFilter) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{5}
}

func (x *OrderStatusAndWorkflowTypeFilter) GetOrderStatus() OrderStatus {
	if x != nil {
		return x.OrderStatus
	}
	return OrderStatus_ORDER_STATUS_UNSPECIFIED
}

func (x *OrderStatusAndWorkflowTypeFilter) GetWorkflows() []OrderWorkflow {
	if x != nil {
		return x.Workflows
	}
	return nil
}

// TransactionDetailedStatusUpdate is the message to be published to a topic when there is a update in txn detailed status
type TransactionDetailedStatusUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A set of all the common attributes to be contained in a queue consumer response
	RequestHeader *queue.ConsumerRequestHeader `protobuf:"bytes,1,opt,name=request_header,json=requestHeader,proto3" json:"request_header,omitempty"`
	// transaction that was updated
	Transaction *payment.Transaction `protobuf:"bytes,2,opt,name=transaction,proto3" json:"transaction,omitempty"`
}

func (x *TransactionDetailedStatusUpdate) Reset() {
	*x = TransactionDetailedStatusUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_order_order_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TransactionDetailedStatusUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionDetailedStatusUpdate) ProtoMessage() {}

func (x *TransactionDetailedStatusUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_api_order_order_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionDetailedStatusUpdate.ProtoReflect.Descriptor instead.
func (*TransactionDetailedStatusUpdate) Descriptor() ([]byte, []int) {
	return file_api_order_order_proto_rawDescGZIP(), []int{6}
}

func (x *TransactionDetailedStatusUpdate) GetRequestHeader() *queue.ConsumerRequestHeader {
	if x != nil {
		return x.RequestHeader
	}
	return nil
}

func (x *TransactionDetailedStatusUpdate) GetTransaction() *payment.Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

var File_api_order_order_proto protoreflect.FileDescriptor

var file_api_order_order_proto_rawDesc = []byte{
	0x0a, 0x15, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x1a, 0x24,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2f,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2f, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x61, 0x70, 0x69, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2f, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x71, 0x75, 0x65,
	0x75, 0x65, 0x2f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x61, 0x6e, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa7, 0x09, 0x0a, 0x05, 0x4f, 0x72, 0x64, 0x65,
	0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x22, 0x0a, 0x0d, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74, 0x6f, 0x41, 0x63,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x30, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f,
	0x77, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x08, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x12, 0x2a, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x12, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0c, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x76, 0x65, 0x6e,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x6e, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x39, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x41, 0x74, 0x12, 0x39, 0x0a, 0x0a, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x37, 0x0a,
	0x09, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x5f, 0x61, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x08, 0x65, 0x78,
	0x70, 0x69, 0x72, 0x65, 0x41, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0e, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x54, 0x61, 0x67, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x39, 0x0a, 0x0e,
	0x75, 0x69, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x0f,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x55, 0x49, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x0c, 0x75, 0x69, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e,
	0x74, 0x5f, 0x72, 0x65, 0x71, 0x5f, 0x69, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x5f, 0x72, 0x65, 0x66, 0x5f, 0x69, 0x64, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x52, 0x65,
	0x66, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x19, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x66, 0x72, 0x6f, 0x6d, 0x41, 0x63, 0x74, 0x6f,
	0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x35,
	0x0a, 0x17, 0x74, 0x6f, 0x5f, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x74, 0x6f, 0x41, 0x63, 0x74, 0x6f, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x46, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x61, 0x75,
	0x74, 0x68, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x14, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x41, 0x6e, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x10, 0x70, 0x6f, 0x73,
	0x74, 0x41, 0x75, 0x74, 0x68, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x4c, 0x0a,
	0x15, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x15, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x41,
	0x6e, 0x79, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x70, 0x6f, 0x73, 0x74, 0x50, 0x61, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x12, 0x4e, 0x0a, 0x15, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x5f, 0x76, 0x31, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x12, 0x70, 0x6f, 0x73, 0x74, 0x41, 0x75, 0x74,
	0x68, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x56, 0x31, 0x12, 0x54, 0x0a, 0x18, 0x70,
	0x6f, 0x73, 0x74, 0x5f, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x76, 0x31, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e,
	0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x15, 0x70, 0x6f, 0x73, 0x74,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x56,
	0x31, 0x22, 0x7b, 0x0a, 0x15, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x05, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x05, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x3e,
	0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xb4,
	0x02, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x43,
	0x0a, 0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x12, 0x52, 0x0a, 0x15, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72,
	0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x15, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x4f, 0x0a, 0x14, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x14, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x09, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x4f, 0x77, 0x6e, 0x65, 0x72, 0x73, 0x68, 0x69, 0x70, 0x52, 0x09, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x73, 0x68, 0x69, 0x70, 0x22, 0x7d, 0x0a, 0x10, 0x42, 0x61, 0x74, 0x63, 0x68, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a, 0x0e, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x24,
	0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x0c,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x52, 0x06, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49,
	0x6e, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x69, 0x6d,
	0x65, 0x6c, 0x69, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x0e,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x52, 0x0a, 0x15, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69,
	0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x15,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x20, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x41, 0x6e, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77,
	0x54, 0x79, 0x70, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x12, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3c, 0x0a, 0x09, 0x77,
	0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x14,
	0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b,
	0x66, 0x6c, 0x6f, 0x77, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x09,
	0x77, 0x6f, 0x72, 0x6b, 0x66, 0x6c, 0x6f, 0x77, 0x73, 0x22, 0xa4, 0x01, 0x0a, 0x1f, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x65,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x43, 0x0a,
	0x0e, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x71, 0x75, 0x65, 0x75, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x75, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x0d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x2e,
	0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x2a, 0xce, 0x03, 0x0a, 0x0b, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1c, 0x0a, 0x18, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x49,
	0x4e, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x50,
	0x41, 0x49, 0x44, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x0e, 0x49, 0x4e, 0x5f,
	0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x05, 0x1a, 0x02, 0x08,
	0x01, 0x12, 0x11, 0x0a, 0x09, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x06,
	0x1a, 0x02, 0x08, 0x01, 0x12, 0x1a, 0x0a, 0x12, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d,
	0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x07, 0x1a, 0x02, 0x08, 0x01,
	0x12, 0x15, 0x0a, 0x0d, 0x49, 0x4e, 0x5f, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x08, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x0f, 0x0a, 0x07, 0x53, 0x45, 0x54, 0x54, 0x4c,
	0x45, 0x44, 0x10, 0x09, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x19, 0x0a, 0x11, 0x53, 0x45, 0x54, 0x54,
	0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x0a, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x4e, 0x55, 0x41, 0x4c, 0x5f, 0x49, 0x4e,
	0x54, 0x45, 0x52, 0x56, 0x45, 0x4e, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13,
	0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52,
	0x45, 0x53, 0x53, 0x10, 0x0c, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41,
	0x59, 0x45, 0x52, 0x10, 0x0d, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x5f, 0x44, 0x49, 0x53, 0x4d, 0x49, 0x53, 0x53, 0x45, 0x44, 0x5f, 0x42, 0x59, 0x5f, 0x50, 0x41,
	0x59, 0x45, 0x45, 0x10, 0x0e, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54,
	0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x45, 0x44, 0x10, 0x0f, 0x12, 0x12, 0x0a,
	0x0e, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x10, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x11, 0x12, 0x0c,
	0x0a, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x12, 0x12, 0x14, 0x0a, 0x10,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x52, 0x53, 0x45, 0x44,
	0x10, 0x13, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x54, 0x45, 0x44, 0x10,
	0x14, 0x2a, 0xf8, 0x01, 0x0a, 0x0f, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x76, 0x65,
	0x6e, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x50,
	0x52, 0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x53, 0x45, 0x52, 0x5f,
	0x41, 0x50, 0x50, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41,
	0x4c, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10,
	0x03, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x54, 0x4d, 0x10, 0x04, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f,
	0x53, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x43, 0x4f, 0x4d, 0x4d, 0x10, 0x06, 0x12, 0x0f,
	0x0a, 0x0b, 0x54, 0x48, 0x49, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x59, 0x10, 0x07, 0x12,
	0x1a, 0x0a, 0x16, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4d, 0x4f, 0x42, 0x49, 0x4c,
	0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x0c, 0x0a, 0x08, 0x53,
	0x48, 0x45, 0x52, 0x4c, 0x4f, 0x43, 0x4b, 0x10, 0x09, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x45, 0x4e,
	0x44, 0x4f, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x10, 0x0a, 0x12, 0x17, 0x0a, 0x13, 0x56, 0x49,
	0x53, 0x41, 0x5f, 0x4d, 0x4f, 0x4e, 0x45, 0x59, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45,
	0x52, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x41, 0x52, 0x54, 0x4e, 0x45, 0x52, 0x5f, 0x4e,
	0x45, 0x54, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x2a, 0xdd, 0x02, 0x0a,
	0x0e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x12,
	0x20, 0x0a, 0x1c, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46, 0x49, 0x45, 0x4c, 0x44, 0x5f, 0x4d,
	0x41, 0x53, 0x4b, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x44, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x46, 0x52, 0x4f,
	0x4d, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b,
	0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a,
	0x08, 0x57, 0x4f, 0x52, 0x4b, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x05, 0x12, 0x11, 0x0a, 0x0d, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x50, 0x41, 0x59, 0x4c, 0x4f, 0x41, 0x44, 0x10, 0x06, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x52,
	0x4f, 0x56, 0x45, 0x4e, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4d,
	0x4f, 0x55, 0x4e, 0x54, 0x10, 0x08, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x09, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45,
	0x44, 0x5f, 0x41, 0x54, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45,
	0x5f, 0x41, 0x54, 0x10, 0x0b, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4c, 0x4c, 0x10, 0x0c, 0x12, 0x08,
	0x0a, 0x04, 0x54, 0x41, 0x47, 0x53, 0x10, 0x0d, 0x12, 0x0f, 0x0a, 0x0b, 0x45, 0x58, 0x54, 0x45,
	0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x49, 0x44, 0x10, 0x0e, 0x12, 0x12, 0x0a, 0x0e, 0x57, 0x4f, 0x52,
	0x46, 0x4c, 0x4f, 0x57, 0x5f, 0x52, 0x45, 0x46, 0x5f, 0x49, 0x44, 0x10, 0x0f, 0x12, 0x1d, 0x0a,
	0x19, 0x46, 0x52, 0x4f, 0x4d, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x10, 0x12, 0x1b, 0x0a, 0x17,
	0x54, 0x4f, 0x5f, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x4f, 0x4b, 0x45, 0x4e, 0x10, 0x11, 0x12, 0x11, 0x0a, 0x0d, 0x43, 0x4c, 0x49,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x51, 0x5f, 0x49, 0x44, 0x10, 0x12, 0x2a, 0x6a, 0x0a, 0x14,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x6c, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x4c, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10,
	0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x41,
	0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x02, 0x2a, 0x89, 0x01, 0x0a, 0x0a, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x52, 0x44, 0x45, 0x52,
	0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x12,
	0x0f, 0x0a, 0x0b, 0x46, 0x55, 0x4c, 0x46, 0x49, 0x4c, 0x4c, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x03,
	0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x54, 0x54, 0x4c, 0x45, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x04,
	0x12, 0x15, 0x0a, 0x11, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x53, 0x54, 0x41, 0x47, 0x45, 0x5f,
	0x4e, 0x4f, 0x5f, 0x4f, 0x50, 0x10, 0x05, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4c, 0x4c, 0x45,
	0x43, 0x54, 0x10, 0x06, 0x2a, 0xa8, 0x0b, 0x0a, 0x08, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x54, 0x61,
	0x67, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x54, 0x41, 0x47, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a,
	0x07, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x46, 0x44,
	0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x53, 0x44, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x52, 0x44,
	0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x10, 0x05,
	0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x57, 0x41, 0x52, 0x44, 0x10, 0x06, 0x12, 0x07, 0x0a, 0x03,
	0x46, 0x49, 0x54, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53,
	0x54, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x53, 0x48, 0x10, 0x09, 0x12, 0x11, 0x0a,
	0x0d, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x0a,
	0x12, 0x0a, 0x0a, 0x06, 0x57, 0x41, 0x4c, 0x4c, 0x45, 0x54, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b,
	0x4d, 0x55, 0x54, 0x55, 0x41, 0x4c, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x0c, 0x12, 0x0d, 0x0a,
	0x09, 0x42, 0x48, 0x41, 0x52, 0x41, 0x54, 0x5f, 0x51, 0x52, 0x10, 0x0d, 0x12, 0x18, 0x0a, 0x14,
	0x53, 0x54, 0x41, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x43,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0e, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x50, 0x49, 0x5f, 0x4d, 0x41,
	0x4e, 0x44, 0x41, 0x54, 0x45, 0x53, 0x10, 0x0f, 0x12, 0x08, 0x0a, 0x04, 0x4c, 0x4f, 0x41, 0x4e,
	0x10, 0x10, 0x12, 0x07, 0x0a, 0x03, 0x45, 0x4d, 0x49, 0x10, 0x11, 0x12, 0x0d, 0x0a, 0x09, 0x55,
	0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x10, 0x12, 0x12, 0x17, 0x0a, 0x13, 0x4a, 0x55,
	0x4d, 0x50, 0x5f, 0x50, 0x32, 0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x13, 0x12, 0x15, 0x0a, 0x0d, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x5f, 0x47, 0x53, 0x54, 0x10, 0x14, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x46, 0x55,
	0x4e, 0x44, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x46, 0x45, 0x52, 0x5f, 0x56, 0x31, 0x10, 0x15,
	0x12, 0x20, 0x0a, 0x1c, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x44, 0x49,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54,
	0x10, 0x16, 0x12, 0x1c, 0x0a, 0x18, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f,
	0x53, 0x45, 0x4c, 0x4c, 0x5f, 0x47, 0x53, 0x54, 0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x17,
	0x12, 0x1d, 0x0a, 0x19, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x44, 0x49,
	0x56, 0x49, 0x44, 0x45, 0x4e, 0x44, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x18, 0x12,
	0x19, 0x0a, 0x15, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x53, 0x45, 0x4c,
	0x4c, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x10, 0x19, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x43,
	0x53, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10,
	0x1a, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f, 0x49,
	0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x5f, 0x47, 0x53, 0x54, 0x5f, 0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x1b, 0x12, 0x17, 0x0a,
	0x13, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1c, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49,
	0x54, 0x45, 0x5f, 0x44, 0x45, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10,
	0x1d, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x5f, 0x54, 0x4f,
	0x50, 0x5f, 0x55, 0x50, 0x10, 0x1e, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45,
	0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x1f, 0x12,
	0x15, 0x0a, 0x11, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x45, 0x41, 0x52, 0x4c, 0x59, 0x5f, 0x53, 0x41,
	0x4c, 0x41, 0x52, 0x59, 0x10, 0x20, 0x12, 0x2a, 0x0a, 0x26, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x53, 0x5f, 0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x49,
	0x4e, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x10, 0x21, 0x12, 0x34, 0x0a, 0x30, 0x55, 0x53, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53, 0x5f,
	0x41, 0x47, 0x47, 0x52, 0x45, 0x47, 0x41, 0x54, 0x45, 0x44, 0x5f, 0x49, 0x4e, 0x57, 0x41, 0x52,
	0x44, 0x5f, 0x52, 0x45, 0x4d, 0x49, 0x54, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x5f, 0x47, 0x53, 0x54,
	0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x10, 0x22, 0x12, 0x1a, 0x0a, 0x16, 0x44, 0x43, 0x5f, 0x46,
	0x4f, 0x52, 0x45, 0x58, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x55, 0x50, 0x5f, 0x52, 0x45, 0x46, 0x55,
	0x4e, 0x44, 0x10, 0x23, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x10, 0x24, 0x12,
	0x16, 0x0a, 0x12, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x25, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x43, 0x53, 0x5f, 0x52,
	0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x26, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x4e, 0x59, 0x57, 0x48, 0x45, 0x52, 0x45, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x49,
	0x4e, 0x47, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x27, 0x12, 0x16, 0x0a, 0x12, 0x41,
	0x54, 0x4d, 0x5f, 0x44, 0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47,
	0x45, 0x10, 0x28, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x55, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x45,
	0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x46, 0x45, 0x45, 0x10, 0x29, 0x12, 0x19, 0x0a, 0x15, 0x4f,
	0x54, 0x48, 0x45, 0x52, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x54, 0x4d, 0x5f, 0x43, 0x48,
	0x41, 0x52, 0x47, 0x45, 0x10, 0x2a, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x41, 0x53, 0x48, 0x5f, 0x48,
	0x41, 0x4e, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x2b, 0x12, 0x22, 0x0a, 0x1e,
	0x45, 0x43, 0x53, 0x5f, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x45, 0x54, 0x55, 0x50, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x2c,
	0x12, 0x16, 0x0a, 0x12, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x10, 0x2d, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x52, 0x45, 0x44,
	0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10,
	0x2e, 0x12, 0x1b, 0x0a, 0x17, 0x45, 0x43, 0x4f, 0x4d, 0x5f, 0x50, 0x4f, 0x53, 0x5f, 0x44, 0x45,
	0x43, 0x4c, 0x49, 0x4e, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x30, 0x12, 0x19,
	0x0a, 0x15, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x4d, 0x43,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x31, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x41, 0x5a,
	0x4f, 0x52, 0x50, 0x41, 0x59, 0x10, 0x32, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x53, 0x54, 0x5f,
	0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x33, 0x12, 0x1a,
	0x0a, 0x16, 0x44, 0x43, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x58, 0x5f, 0x4d, 0x41, 0x52, 0x4b, 0x55,
	0x50, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x34, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x43,
	0x5f, 0x54, 0x43, 0x53, 0x5f, 0x46, 0x45, 0x45, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10,
	0x35, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x43, 0x5f, 0x44, 0x43, 0x43, 0x5f, 0x46, 0x45, 0x45, 0x5f,
	0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x36, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x48, 0x45, 0x51,
	0x55, 0x45, 0x10, 0x37, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x48, 0x45, 0x51, 0x55, 0x45, 0x5f, 0x52,
	0x45, 0x56, 0x45, 0x52, 0x53, 0x41, 0x4c, 0x10, 0x38, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x47, 0x5f,
	0x52, 0x45, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x39, 0x12, 0x17, 0x0a, 0x13, 0x44, 0x43, 0x5f, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x45, 0x52, 0x10,
	0x3a, 0x12, 0x16, 0x0a, 0x12, 0x44, 0x43, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x3b, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x4d, 0x42,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x3c, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x52, 0x45,
	0x44, 0x49, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x10,
	0x3d, 0x12, 0x07, 0x0a, 0x03, 0x4e, 0x46, 0x43, 0x10, 0x3e, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x49,
	0x52, 0x53, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x4f, 0x52, 0x44, 0x45, 0x52, 0x5f, 0x46,
	0x45, 0x45, 0x10, 0x3f, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x54, 0x4c, 0x5f, 0x41, 0x54, 0x4d,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x40, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x4f, 0x44,
	0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x10, 0x41, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43,
	0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x42, 0x2a,
	0xb1, 0x07, 0x0a, 0x0c, 0x55, 0x49, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x1e, 0x0a, 0x1a, 0x55, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49,
	0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x0c, 0x0a, 0x08, 0x54, 0x49, 0x4d, 0x45, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x01, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x51, 0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x49, 0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x51,
	0x52, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x49,
	0x4e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x10, 0x05,
	0x12, 0x15, 0x0a, 0x11, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x44, 0x44, 0x5f,
	0x46, 0x55, 0x4e, 0x44, 0x53, 0x10, 0x06, 0x12, 0x0f, 0x0a, 0x0b, 0x54, 0x52, 0x41, 0x4e, 0x53,
	0x46, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x10, 0x07, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x4f, 0x4d, 0x45,
	0x10, 0x08, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x09, 0x12, 0x13, 0x0a, 0x0f, 0x41, 0x43, 0x43, 0x4f, 0x55,
	0x4e, 0x54, 0x5f, 0x53, 0x55, 0x4d, 0x4d, 0x41, 0x52, 0x59, 0x10, 0x0a, 0x12, 0x0d, 0x0a, 0x09,
	0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x53, 0x10, 0x0b, 0x12, 0x17, 0x0a, 0x13, 0x48,
	0x4f, 0x4d, 0x45, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x49, 0x53, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x43,
	0x54, 0x41, 0x10, 0x0c, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x5f,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x0d, 0x12, 0x16, 0x0a, 0x12, 0x42, 0x4f,
	0x4e, 0x55, 0x53, 0x5f, 0x4a, 0x41, 0x52, 0x5f, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x10, 0x0e, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x49, 0x54, 0x54, 0x54, 0x10, 0x0f, 0x12, 0x10, 0x0a,
	0x0c, 0x41, 0x55, 0x54, 0x4f, 0x5f, 0x50, 0x41, 0x59, 0x5f, 0x48, 0x55, 0x42, 0x10, 0x10, 0x12,
	0x13, 0x0a, 0x0f, 0x4d, 0x46, 0x5f, 0x42, 0x55, 0x59, 0x5f, 0x4f, 0x4e, 0x45, 0x5f, 0x54, 0x49,
	0x4d, 0x45, 0x10, 0x11, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x48, 0x59, 0x53, 0x49, 0x43, 0x41, 0x4c,
	0x5f, 0x44, 0x45, 0x42, 0x49, 0x54, 0x5f, 0x43, 0x41, 0x52, 0x44, 0x5f, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x53, 0x10, 0x12, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x53, 0x4b, 0x5f, 0x46, 0x49, 0x10,
	0x13, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x43, 0x54, 0x49, 0x56, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x50,
	0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x14, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x4f, 0x50, 0x5f,
	0x55, 0x50, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45, 0x10, 0x15, 0x12, 0x13, 0x0a,
	0x0f, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x55, 0x50, 0x49, 0x5f, 0x4c, 0x49, 0x54, 0x45,
	0x10, 0x16, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56,
	0x45, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x10, 0x17, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x45, 0x4e,
	0x4e, 0x59, 0x5f, 0x44, 0x52, 0x4f, 0x50, 0x10, 0x18, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x45, 0x44, 0x5f, 0x43, 0x43, 0x10, 0x19, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x45, 0x5f, 0x51, 0x52, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x44,
	0x5f, 0x50, 0x41, 0x59, 0x10, 0x1a, 0x12, 0x1d, 0x0a, 0x19, 0x49, 0x4e, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x45, 0x5f, 0x51, 0x52, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x5f, 0x41, 0x4e, 0x44, 0x5f,
	0x50, 0x41, 0x59, 0x10, 0x1b, 0x12, 0x0d, 0x0a, 0x09, 0x41, 0x41, 0x5f, 0x53, 0x41, 0x4c, 0x41,
	0x52, 0x59, 0x10, 0x1c, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x44, 0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x53, 0x5f, 0x55, 0x53, 0x53, 0x10, 0x1d, 0x12, 0x22, 0x0a, 0x1e, 0x55, 0x49, 0x5f, 0x45, 0x4e,
	0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x50,
	0x52, 0x45, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1e, 0x12, 0x30, 0x0a, 0x2c, 0x55,
	0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x58,
	0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x1f, 0x12, 0x23, 0x0a,
	0x1f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53,
	0x10, 0x20, 0x12, 0x21, 0x0a, 0x1d, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x5f, 0x41, 0x44,
	0x44, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x53, 0x5f, 0x50, 0x52, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x21, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47,
	0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x4a, 0x4f, 0x49, 0x4e, 0x5f,
	0x50, 0x4c, 0x55, 0x53, 0x10, 0x22, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e,
	0x47, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53, 0x5f, 0x4a, 0x4f, 0x49, 0x4e,
	0x5f, 0x49, 0x4e, 0x46, 0x49, 0x4e, 0x49, 0x54, 0x45, 0x10, 0x23, 0x12, 0x20, 0x0a, 0x1c, 0x54,
	0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x5f, 0x41, 0x4c, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x4e, 0x53,
	0x5f, 0x4a, 0x4f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x49, 0x4d, 0x45, 0x10, 0x24, 0x12, 0x0f, 0x0a,
	0x0b, 0x41, 0x4d, 0x42, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10, 0x25, 0x12, 0x23,
	0x0a, 0x1f, 0x55, 0x49, 0x5f, 0x45, 0x4e, 0x54, 0x52, 0x59, 0x5f, 0x50, 0x4f, 0x49, 0x4e, 0x54,
	0x5f, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x26, 0x42, 0x44, 0x0a, 0x20, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5a, 0x20, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_order_order_proto_rawDescOnce sync.Once
	file_api_order_order_proto_rawDescData = file_api_order_order_proto_rawDesc
)

func file_api_order_order_proto_rawDescGZIP() []byte {
	file_api_order_order_proto_rawDescOnce.Do(func() {
		file_api_order_order_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_order_order_proto_rawDescData)
	})
	return file_api_order_order_proto_rawDescData
}

var file_api_order_order_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_order_order_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_order_order_proto_goTypes = []interface{}{
	(OrderStatus)(0),                            // 0: order.OrderStatus
	(OrderProvenance)(0),                        // 1: order.OrderProvenance
	(OrderFieldMask)(0),                         // 2: order.OrderFieldMask
	(OrderAccountRelation)(0),                   // 3: order.OrderAccountRelation
	(OrderStage)(0),                             // 4: order.OrderStage
	(OrderTag)(0),                               // 5: order.OrderTag
	(UIEntryPoint)(0),                           // 6: order.UIEntryPoint
	(*Order)(nil),                               // 7: order.Order
	(*OrderWithTransactions)(nil),               // 8: order.OrderWithTransactions
	(*OrderUpdate)(nil),                         // 9: order.OrderUpdate
	(*BatchOrderUpdate)(nil),                    // 10: order.BatchOrderUpdate
	(*UpdateInPaymentOrderTimelineRequest)(nil), // 11: order.UpdateInPaymentOrderTimelineRequest
	(*OrderStatusAndWorkflowTypeFilter)(nil),    // 12: order.OrderStatusAndWorkflowTypeFilter
	(*TransactionDetailedStatusUpdate)(nil),     // 13: order.TransactionDetailedStatusUpdate
	(OrderWorkflow)(0),                          // 14: order.OrderWorkflow
	(*money.Money)(nil),                         // 15: google.type.Money
	(*timestamppb.Timestamp)(nil),               // 16: google.protobuf.Timestamp
	(*anypb.Any)(nil),                           // 17: google.protobuf.Any
	(*deeplink.Deeplink)(nil),                   // 18: frontend.deeplink.Deeplink
	(*payment.Transaction)(nil),                 // 19: order.payment.Transaction
	(*queue.ConsumerRequestHeader)(nil),         // 20: queue.ConsumerRequestHeader
	(common.Ownership)(0),                       // 21: api.typesv2.common.Ownership
}
var file_api_order_order_proto_depIdxs = []int32{
	14, // 0: order.Order.workflow:type_name -> order.OrderWorkflow
	0,  // 1: order.Order.status:type_name -> order.OrderStatus
	1,  // 2: order.Order.provenance:type_name -> order.OrderProvenance
	15, // 3: order.Order.amount:type_name -> google.type.Money
	16, // 4: order.Order.created_at:type_name -> google.protobuf.Timestamp
	16, // 5: order.Order.updated_at:type_name -> google.protobuf.Timestamp
	16, // 6: order.Order.deleted_at:type_name -> google.protobuf.Timestamp
	16, // 7: order.Order.expire_at:type_name -> google.protobuf.Timestamp
	5,  // 8: order.Order.tags:type_name -> order.OrderTag
	6,  // 9: order.Order.ui_entry_point:type_name -> order.UIEntryPoint
	17, // 10: order.Order.post_auth_deeplink:type_name -> google.protobuf.Any
	17, // 11: order.Order.post_payment_deeplink:type_name -> google.protobuf.Any
	18, // 12: order.Order.post_auth_deeplink_v1:type_name -> frontend.deeplink.Deeplink
	18, // 13: order.Order.post_payment_deeplink_v1:type_name -> frontend.deeplink.Deeplink
	7,  // 14: order.OrderWithTransactions.order:type_name -> order.Order
	19, // 15: order.OrderWithTransactions.transactions:type_name -> order.payment.Transaction
	20, // 16: order.OrderUpdate.request_header:type_name -> queue.ConsumerRequestHeader
	8,  // 17: order.OrderUpdate.orderWithTransactions:type_name -> order.OrderWithTransactions
	3,  // 18: order.OrderUpdate.orderAccountRelation:type_name -> order.OrderAccountRelation
	21, // 19: order.OrderUpdate.ownership:type_name -> api.typesv2.common.Ownership
	20, // 20: order.BatchOrderUpdate.request_header:type_name -> queue.ConsumerRequestHeader
	7,  // 21: order.BatchOrderUpdate.orders:type_name -> order.Order
	20, // 22: order.UpdateInPaymentOrderTimelineRequest.request_header:type_name -> queue.ConsumerRequestHeader
	8,  // 23: order.UpdateInPaymentOrderTimelineRequest.orderWithTransactions:type_name -> order.OrderWithTransactions
	0,  // 24: order.OrderStatusAndWorkflowTypeFilter.order_status:type_name -> order.OrderStatus
	14, // 25: order.OrderStatusAndWorkflowTypeFilter.workflows:type_name -> order.OrderWorkflow
	20, // 26: order.TransactionDetailedStatusUpdate.request_header:type_name -> queue.ConsumerRequestHeader
	19, // 27: order.TransactionDetailedStatusUpdate.transaction:type_name -> order.payment.Transaction
	28, // [28:28] is the sub-list for method output_type
	28, // [28:28] is the sub-list for method input_type
	28, // [28:28] is the sub-list for extension type_name
	28, // [28:28] is the sub-list for extension extendee
	0,  // [0:28] is the sub-list for field type_name
}

func init() { file_api_order_order_proto_init() }
func file_api_order_order_proto_init() {
	if File_api_order_order_proto != nil {
		return
	}
	file_api_order_workflow_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_order_order_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Order); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderWithTransactions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BatchOrderUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateInPaymentOrderTimelineRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OrderStatusAndWorkflowTypeFilter); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_order_order_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TransactionDetailedStatusUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_order_order_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_order_order_proto_goTypes,
		DependencyIndexes: file_api_order_order_proto_depIdxs,
		EnumInfos:         file_api_order_order_proto_enumTypes,
		MessageInfos:      file_api_order_order_proto_msgTypes,
	}.Build()
	File_api_order_order_proto = out.File
	file_api_order_order_proto_rawDesc = nil
	file_api_order_order_proto_goTypes = nil
	file_api_order_order_proto_depIdxs = nil
}
