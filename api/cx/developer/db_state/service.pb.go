// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/developer/db_state/service.proto

package db_state

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	cx "github.com/epifi/gamma/api/cx"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetServiceListRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
}

func (x *GetServiceListRequest) Reset() {
	*x = GetServiceListRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceListRequest) ProtoMessage() {}

func (x *GetServiceListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceListRequest.ProtoReflect.Descriptor instead.
func (*GetServiceListRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetServiceListRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

type GetServiceListResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of services available
	ServiceList []Service `protobuf:"varint,2,rep,packed,name=service_list,json=serviceList,proto3,enum=cx.developer.db_state.Service" json:"service_list,omitempty"`
}

func (x *GetServiceListResponse) Reset() {
	*x = GetServiceListResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetServiceListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceListResponse) ProtoMessage() {}

func (x *GetServiceListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceListResponse.ProtoReflect.Descriptor instead.
func (*GetServiceListResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetServiceListResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetServiceListResponse) GetServiceList() []Service {
	if x != nil {
		return x.ServiceList
	}
	return nil
}

type GetEntityListForServiceRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// service name for which entity list is being fetched
	// mandatory
	Service Service `protobuf:"varint,2,opt,name=service,proto3,enum=cx.developer.db_state.Service" json:"service,omitempty"`
}

func (x *GetEntityListForServiceRequest) Reset() {
	*x = GetEntityListForServiceRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityListForServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityListForServiceRequest) ProtoMessage() {}

func (x *GetEntityListForServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityListForServiceRequest.ProtoReflect.Descriptor instead.
func (*GetEntityListForServiceRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetEntityListForServiceRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetEntityListForServiceRequest) GetService() Service {
	if x != nil {
		return x.Service
	}
	return Service_SERVICE_UNSPECIFIED
}

type GetEntityListForServiceResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of entities available for the given service
	EntityList []string `protobuf:"bytes,2,rep,name=entity_list,json=entityList,proto3" json:"entity_list,omitempty"`
}

func (x *GetEntityListForServiceResponse) Reset() {
	*x = GetEntityListForServiceResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEntityListForServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEntityListForServiceResponse) ProtoMessage() {}

func (x *GetEntityListForServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEntityListForServiceResponse.ProtoReflect.Descriptor instead.
func (*GetEntityListForServiceResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetEntityListForServiceResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetEntityListForServiceResponse) GetEntityList() []string {
	if x != nil {
		return x.EntityList
	}
	return nil
}

type GetParameterListForEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// service name for which entity list is being fetched
	// mandatory
	Service Service `protobuf:"varint,2,opt,name=service,proto3,enum=cx.developer.db_state.Service" json:"service,omitempty"`
	// entity name for which parameter list is being fetched
	// mandatory
	Entity string `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
}

func (x *GetParameterListForEntityRequest) Reset() {
	*x = GetParameterListForEntityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParameterListForEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParameterListForEntityRequest) ProtoMessage() {}

func (x *GetParameterListForEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParameterListForEntityRequest.ProtoReflect.Descriptor instead.
func (*GetParameterListForEntityRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetParameterListForEntityRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetParameterListForEntityRequest) GetService() Service {
	if x != nil {
		return x.Service
	}
	return Service_SERVICE_UNSPECIFIED
}

func (x *GetParameterListForEntityRequest) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

type GetParameterListForEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// list of parameters available for the given entity
	ParameterList []*ParameterMeta `protobuf:"bytes,2,rep,name=parameter_list,json=parameterList,proto3" json:"parameter_list,omitempty"`
}

func (x *GetParameterListForEntityResponse) Reset() {
	*x = GetParameterListForEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetParameterListForEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetParameterListForEntityResponse) ProtoMessage() {}

func (x *GetParameterListForEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetParameterListForEntityResponse.ProtoReflect.Descriptor instead.
func (*GetParameterListForEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{5}
}

func (x *GetParameterListForEntityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetParameterListForEntityResponse) GetParameterList() []*ParameterMeta {
	if x != nil {
		return x.ParameterList
	}
	return nil
}

type GetDataForEntityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *cx.Header `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// service name for which entity list is being fetched
	// mandatory
	Service Service `protobuf:"varint,2,opt,name=service,proto3,enum=cx.developer.db_state.Service" json:"service,omitempty"`
	// entity name for which data is being fetched
	// mandatory
	Entity string `protobuf:"bytes,3,opt,name=entity,proto3" json:"entity,omitempty"`
	// filter list for the entity data
	// you can pass filter values for any of the parameter available for entity
	// value for the filter should be of correct type based on the ParameterDataType
	Filters []*Filter `protobuf:"bytes,4,rep,name=filters,proto3" json:"filters,omitempty"`
}

func (x *GetDataForEntityRequest) Reset() {
	*x = GetDataForEntityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataForEntityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataForEntityRequest) ProtoMessage() {}

func (x *GetDataForEntityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataForEntityRequest.ProtoReflect.Descriptor instead.
func (*GetDataForEntityRequest) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetDataForEntityRequest) GetHeader() *cx.Header {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetDataForEntityRequest) GetService() Service {
	if x != nil {
		return x.Service
	}
	return Service_SERVICE_UNSPECIFIED
}

func (x *GetDataForEntityRequest) GetEntity() string {
	if x != nil {
		return x.Entity
	}
	return ""
}

func (x *GetDataForEntityRequest) GetFilters() []*Filter {
	if x != nil {
		return x.Filters
	}
	return nil
}

type GetDataForEntityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// entity data as a json string
	// UI will show this json as key value data
	JsonResponse string `protobuf:"bytes,2,opt,name=json_response,json=jsonResponse,proto3" json:"json_response,omitempty"`
	// page conext response
	// this should be returned by service if they intend to use pagination for the entity
	// you also need to add a parameter with type PAGE_CONTEXT to make UI support pagination
	// and use the tokens returned here to navigate between pages
	// UI will only read this if parameter with type PAGE_CONTEXT is added
	PageContext *cx.PageContextResponse `protobuf:"bytes,3,opt,name=page_context,json=pageContext,proto3" json:"page_context,omitempty"`
}

func (x *GetDataForEntityResponse) Reset() {
	*x = GetDataForEntityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_cx_developer_db_state_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataForEntityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataForEntityResponse) ProtoMessage() {}

func (x *GetDataForEntityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_cx_developer_db_state_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataForEntityResponse.ProtoReflect.Descriptor instead.
func (*GetDataForEntityResponse) Descriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_service_proto_rawDescGZIP(), []int{7}
}

func (x *GetDataForEntityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDataForEntityResponse) GetJsonResponse() string {
	if x != nil {
		return x.JsonResponse
	}
	return ""
}

func (x *GetDataForEntityResponse) GetPageContext() *cx.PageContextResponse {
	if x != nil {
		return x.PageContext
	}
	return nil
}

var File_api_cx_developer_db_state_service_proto protoreflect.FileDescriptor

var file_api_cx_developer_db_state_service_proto_rawDesc = []byte{
	0x0a, 0x27, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x78, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x64, 0x62, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x13, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x6d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x45, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2c, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78,
	0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x22, 0x80, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x0c, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x7e, 0x0a, 0x1e,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22,
	0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a,
	0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x22, 0x67, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x5f, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x98, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x22, 0x95, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65,
	0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4b, 0x0a, 0x0e, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x65, 0x74, 0x65, 0x72, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0d, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xc8, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x22, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0a, 0x2e, 0x63, 0x78, 0x2e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x38, 0x0a, 0x07, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x07, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x78,
	0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x2e, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x52, 0x07, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x73, 0x22, 0xa0, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46,
	0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x6a, 0x73, 0x6f, 0x6e, 0x5f, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6a, 0x73,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x63, 0x78, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x78,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x0b, 0x70, 0x61, 0x67, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x65, 0x78, 0x74, 0x32, 0xa3, 0x05, 0x0a, 0x07, 0x44, 0x42, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0xae, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x35,
	0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8,
	0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49,
	0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92,
	0xe8, 0x6a, 0x00, 0x12, 0xb4, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x37, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72,
	0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74,
	0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x63, 0x78, 0x2e,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4c,
	0x69, 0x73, 0x74, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b,
	0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01,
	0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x99, 0x01, 0x0a, 0x10, 0x47,
	0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12,
	0x2e, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46,
	0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64,
	0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x46,
	0x6f, 0x72, 0x45, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7, 0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45,
	0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8, 0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a,
	0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x2c, 0x2e, 0x63, 0x78, 0x2e, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0xd8, 0xba, 0xd7, 0x0a, 0x00, 0xda, 0xc2, 0xd7,
	0x0a, 0x0b, 0x49, 0x4e, 0x53, 0x45, 0x4e, 0x53, 0x49, 0x54, 0x49, 0x56, 0x45, 0xa0, 0x8d, 0xe8,
	0x6a, 0x01, 0xa8, 0x8d, 0xe8, 0x6a, 0x01, 0xf0, 0x92, 0xe8, 0x6a, 0x00, 0x42, 0x64, 0x0a, 0x30,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78, 0x2e, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_developer_db_state_service_proto_rawDescOnce sync.Once
	file_api_cx_developer_db_state_service_proto_rawDescData = file_api_cx_developer_db_state_service_proto_rawDesc
)

func file_api_cx_developer_db_state_service_proto_rawDescGZIP() []byte {
	file_api_cx_developer_db_state_service_proto_rawDescOnce.Do(func() {
		file_api_cx_developer_db_state_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_developer_db_state_service_proto_rawDescData)
	})
	return file_api_cx_developer_db_state_service_proto_rawDescData
}

var file_api_cx_developer_db_state_service_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_cx_developer_db_state_service_proto_goTypes = []interface{}{
	(*GetServiceListRequest)(nil),             // 0: cx.developer.db_state.GetServiceListRequest
	(*GetServiceListResponse)(nil),            // 1: cx.developer.db_state.GetServiceListResponse
	(*GetEntityListForServiceRequest)(nil),    // 2: cx.developer.db_state.GetEntityListForServiceRequest
	(*GetEntityListForServiceResponse)(nil),   // 3: cx.developer.db_state.GetEntityListForServiceResponse
	(*GetParameterListForEntityRequest)(nil),  // 4: cx.developer.db_state.GetParameterListForEntityRequest
	(*GetParameterListForEntityResponse)(nil), // 5: cx.developer.db_state.GetParameterListForEntityResponse
	(*GetDataForEntityRequest)(nil),           // 6: cx.developer.db_state.GetDataForEntityRequest
	(*GetDataForEntityResponse)(nil),          // 7: cx.developer.db_state.GetDataForEntityResponse
	(*cx.Header)(nil),                         // 8: cx.Header
	(*rpc.Status)(nil),                        // 9: rpc.Status
	(Service)(0),                              // 10: cx.developer.db_state.Service
	(*ParameterMeta)(nil),                     // 11: cx.developer.db_state.ParameterMeta
	(*Filter)(nil),                            // 12: cx.developer.db_state.Filter
	(*cx.PageContextResponse)(nil),            // 13: cx.PageContextResponse
}
var file_api_cx_developer_db_state_service_proto_depIdxs = []int32{
	8,  // 0: cx.developer.db_state.GetServiceListRequest.header:type_name -> cx.Header
	9,  // 1: cx.developer.db_state.GetServiceListResponse.status:type_name -> rpc.Status
	10, // 2: cx.developer.db_state.GetServiceListResponse.service_list:type_name -> cx.developer.db_state.Service
	8,  // 3: cx.developer.db_state.GetEntityListForServiceRequest.header:type_name -> cx.Header
	10, // 4: cx.developer.db_state.GetEntityListForServiceRequest.service:type_name -> cx.developer.db_state.Service
	9,  // 5: cx.developer.db_state.GetEntityListForServiceResponse.status:type_name -> rpc.Status
	8,  // 6: cx.developer.db_state.GetParameterListForEntityRequest.header:type_name -> cx.Header
	10, // 7: cx.developer.db_state.GetParameterListForEntityRequest.service:type_name -> cx.developer.db_state.Service
	9,  // 8: cx.developer.db_state.GetParameterListForEntityResponse.status:type_name -> rpc.Status
	11, // 9: cx.developer.db_state.GetParameterListForEntityResponse.parameter_list:type_name -> cx.developer.db_state.ParameterMeta
	8,  // 10: cx.developer.db_state.GetDataForEntityRequest.header:type_name -> cx.Header
	10, // 11: cx.developer.db_state.GetDataForEntityRequest.service:type_name -> cx.developer.db_state.Service
	12, // 12: cx.developer.db_state.GetDataForEntityRequest.filters:type_name -> cx.developer.db_state.Filter
	9,  // 13: cx.developer.db_state.GetDataForEntityResponse.status:type_name -> rpc.Status
	13, // 14: cx.developer.db_state.GetDataForEntityResponse.page_context:type_name -> cx.PageContextResponse
	2,  // 15: cx.developer.db_state.DBState.GetEntityListForService:input_type -> cx.developer.db_state.GetEntityListForServiceRequest
	4,  // 16: cx.developer.db_state.DBState.GetParameterListForEntity:input_type -> cx.developer.db_state.GetParameterListForEntityRequest
	6,  // 17: cx.developer.db_state.DBState.GetDataForEntity:input_type -> cx.developer.db_state.GetDataForEntityRequest
	0,  // 18: cx.developer.db_state.DBState.GetServiceList:input_type -> cx.developer.db_state.GetServiceListRequest
	3,  // 19: cx.developer.db_state.DBState.GetEntityListForService:output_type -> cx.developer.db_state.GetEntityListForServiceResponse
	5,  // 20: cx.developer.db_state.DBState.GetParameterListForEntity:output_type -> cx.developer.db_state.GetParameterListForEntityResponse
	7,  // 21: cx.developer.db_state.DBState.GetDataForEntity:output_type -> cx.developer.db_state.GetDataForEntityResponse
	1,  // 22: cx.developer.db_state.DBState.GetServiceList:output_type -> cx.developer.db_state.GetServiceListResponse
	19, // [19:23] is the sub-list for method output_type
	15, // [15:19] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_api_cx_developer_db_state_service_proto_init() }
func file_api_cx_developer_db_state_service_proto_init() {
	if File_api_cx_developer_db_state_service_proto != nil {
		return
	}
	file_api_cx_developer_db_state_db_state_proto_init()
	file_api_cx_developer_db_state_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_cx_developer_db_state_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceListRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetServiceListResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityListForServiceRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEntityListForServiceResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetParameterListForEntityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetParameterListForEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataForEntityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_cx_developer_db_state_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataForEntityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_developer_db_state_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_cx_developer_db_state_service_proto_goTypes,
		DependencyIndexes: file_api_cx_developer_db_state_service_proto_depIdxs,
		MessageInfos:      file_api_cx_developer_db_state_service_proto_msgTypes,
	}.Build()
	File_api_cx_developer_db_state_service_proto = out.File
	file_api_cx_developer_db_state_service_proto_rawDesc = nil
	file_api_cx_developer_db_state_service_proto_goTypes = nil
	file_api_cx_developer_db_state_service_proto_depIdxs = nil
}
