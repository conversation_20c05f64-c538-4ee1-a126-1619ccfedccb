// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/cx/developer/db_state/enums.proto

package db_state

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// list of services which are exposing db states
// each service will have it's own implementation for exposing data
// whenever a new service is added you need to write integration for the service on CX backend
// service owner will need to define and implement a service(which implements collector service)
// cx will use the client of that service to integrate
// error will be thrown by db states rpc for any service which is not integrated in CX
// this service is not equivalent to the servers in gamma
// you can have multiple services from the same server as well
// deprecated in the favor of sherlock.dev.dbstate.Service as this is needed in gringott repo as well
// until we have not fully moved to sherlock.dev.dbstate.Service we need to add new services here as well
type Service int32

const (
	Service_SERVICE_UNSPECIFIED           Service = 0
	Service_CX                            Service = 1
	Service_COMMS                         Service = 2
	Service_ACTOR                         Service = 3
	Service_DEPOSIT                       Service = 4
	Service_USER                          Service = 5
	Service_ORDER                         Service = 6
	Service_KYC                           Service = 7
	Service_PAYMENT_INSTRUMENT            Service = 8
	Service_CARD                          Service = 9
	Service_LIVENESS                      Service = 10
	Service_SAVINGS                       Service = 11
	Service_UPI                           Service = 12
	Service_INAPPHELP                     Service = 13
	Service_CASBIN                        Service = 14
	Service_INSIGHTS                      Service = 15
	Service_WAITLIST                      Service = 16
	Service_REWARDS                       Service = 17
	Service_AUTH                          Service = 18
	Service_VENDOR_MAPPING                Service = 19
	Service_TIMELINE                      Service = 20
	Service_CASPER                        Service = 21
	Service_MERCHANT                      Service = 22
	Service_RMS                           Service = 23
	Service_FITTT                         Service = 24
	Service_CONNECTED_ACCOUNT             Service = 25
	Service_IN_APP_REFERRAL               Service = 26
	Service_WEALTH_ONBOARDING             Service = 27
	Service_INVESTMENT                    Service = 28
	Service_RECURRING_PAYMENT             Service = 29
	Service_P2P_INVESTMENT                Service = 30
	Service_SEGMENT                       Service = 31
	Service_SALARY_PROGRAM                Service = 32
	Service_ANALYSER                      Service = 33
	Service_CATEGORIZER                   Service = 34
	Service_PRE_APPROVED_LOAN             Service = 35
	Service_CELESTIAL                     Service = 36
	Service_RISK                          Service = 37
	Service_NUDGE                         Service = 38
	Service_SCREENER                      Service = 39
	Service_FIREFLY                       Service = 40
	Service_AUTH_ORCHESTRATOR             Service = 41
	Service_PAY                           Service = 42
	Service_USSTOCKS                      Service = 43
	Service_TIERING                       Service = 44
	Service_AML                           Service = 45
	Service_ALFRED                        Service = 46
	Service_PAN                           Service = 47
	Service_QUEST                         Service = 48
	Service_UPCOMING_TRANSACTIONS         Service = 49
	Service_HEALTH_ENGINE                 Service = 50
	Service_CMS                           Service = 51
	Service_ENACH                         Service = 52
	Service_BANK_CUSTOMER                 Service = 53
	Service_EMPLOYMENT                    Service = 54
	Service_COLLECTION                    Service = 55
	Service_OMEGLE                        Service = 56
	Service_VKYC_CALL                     Service = 57
	Service_STOCKGUARDIAN_VKYC_CALL       Service = 58
	Service_STOCKGUARDIAN_OMEGLE          Service = 59
	Service_STOCKGUARDIAN_CREDIT_REPORT   Service = 60
	Service_STOCKGUARDIAN_CREDIT_RISK     Service = 61
	Service_STOCKGUARDIAN_E_SIGN          Service = 62
	Service_STOCKGUARDIAN_IN_HOUSE_BRE    Service = 63
	Service_STOCKGUARDIAN_LMS             Service = 64
	Service_STOCKGUARDIAN_APPLICATION     Service = 65
	Service_STOCKGUARDIAN_BRE             Service = 66
	Service_STOCKGUARDIAN_DOCS            Service = 67
	Service_STOCKGUARDIAN_MATRIX          Service = 68
	Service_STOCKGUARDIAN_CUSTOMER        Service = 69
	Service_STOCKGUARDIAN_APPLICANT       Service = 70
	Service_STOCKGUARDIAN_KYC_VENDOR_DATA Service = 71
	Service_TSP_USER                      Service = 72
	Service_LEADS                         Service = 73
	Service_CREDIT_REPORT                 Service = 74
	Service_ACCOUNTS                      Service = 75
	Service_NPS                           Service = 76
	Service_SECURITIES                    Service = 77
	Service_SALARY_ESTIMATION             Service = 78
	Service_BILLPAY                       Service = 79
)

// Enum value maps for Service.
var (
	Service_name = map[int32]string{
		0:  "SERVICE_UNSPECIFIED",
		1:  "CX",
		2:  "COMMS",
		3:  "ACTOR",
		4:  "DEPOSIT",
		5:  "USER",
		6:  "ORDER",
		7:  "KYC",
		8:  "PAYMENT_INSTRUMENT",
		9:  "CARD",
		10: "LIVENESS",
		11: "SAVINGS",
		12: "UPI",
		13: "INAPPHELP",
		14: "CASBIN",
		15: "INSIGHTS",
		16: "WAITLIST",
		17: "REWARDS",
		18: "AUTH",
		19: "VENDOR_MAPPING",
		20: "TIMELINE",
		21: "CASPER",
		22: "MERCHANT",
		23: "RMS",
		24: "FITTT",
		25: "CONNECTED_ACCOUNT",
		26: "IN_APP_REFERRAL",
		27: "WEALTH_ONBOARDING",
		28: "INVESTMENT",
		29: "RECURRING_PAYMENT",
		30: "P2P_INVESTMENT",
		31: "SEGMENT",
		32: "SALARY_PROGRAM",
		33: "ANALYSER",
		34: "CATEGORIZER",
		35: "PRE_APPROVED_LOAN",
		36: "CELESTIAL",
		37: "RISK",
		38: "NUDGE",
		39: "SCREENER",
		40: "FIREFLY",
		41: "AUTH_ORCHESTRATOR",
		42: "PAY",
		43: "USSTOCKS",
		44: "TIERING",
		45: "AML",
		46: "ALFRED",
		47: "PAN",
		48: "QUEST",
		49: "UPCOMING_TRANSACTIONS",
		50: "HEALTH_ENGINE",
		51: "CMS",
		52: "ENACH",
		53: "BANK_CUSTOMER",
		54: "EMPLOYMENT",
		55: "COLLECTION",
		56: "OMEGLE",
		57: "VKYC_CALL",
		58: "STOCKGUARDIAN_VKYC_CALL",
		59: "STOCKGUARDIAN_OMEGLE",
		60: "STOCKGUARDIAN_CREDIT_REPORT",
		61: "STOCKGUARDIAN_CREDIT_RISK",
		62: "STOCKGUARDIAN_E_SIGN",
		63: "STOCKGUARDIAN_IN_HOUSE_BRE",
		64: "STOCKGUARDIAN_LMS",
		65: "STOCKGUARDIAN_APPLICATION",
		66: "STOCKGUARDIAN_BRE",
		67: "STOCKGUARDIAN_DOCS",
		68: "STOCKGUARDIAN_MATRIX",
		69: "STOCKGUARDIAN_CUSTOMER",
		70: "STOCKGUARDIAN_APPLICANT",
		71: "STOCKGUARDIAN_KYC_VENDOR_DATA",
		72: "TSP_USER",
		73: "LEADS",
		74: "CREDIT_REPORT",
		75: "ACCOUNTS",
		76: "NPS",
		77: "SECURITIES",
		78: "SALARY_ESTIMATION",
		79: "BILLPAY",
	}
	Service_value = map[string]int32{
		"SERVICE_UNSPECIFIED":           0,
		"CX":                            1,
		"COMMS":                         2,
		"ACTOR":                         3,
		"DEPOSIT":                       4,
		"USER":                          5,
		"ORDER":                         6,
		"KYC":                           7,
		"PAYMENT_INSTRUMENT":            8,
		"CARD":                          9,
		"LIVENESS":                      10,
		"SAVINGS":                       11,
		"UPI":                           12,
		"INAPPHELP":                     13,
		"CASBIN":                        14,
		"INSIGHTS":                      15,
		"WAITLIST":                      16,
		"REWARDS":                       17,
		"AUTH":                          18,
		"VENDOR_MAPPING":                19,
		"TIMELINE":                      20,
		"CASPER":                        21,
		"MERCHANT":                      22,
		"RMS":                           23,
		"FITTT":                         24,
		"CONNECTED_ACCOUNT":             25,
		"IN_APP_REFERRAL":               26,
		"WEALTH_ONBOARDING":             27,
		"INVESTMENT":                    28,
		"RECURRING_PAYMENT":             29,
		"P2P_INVESTMENT":                30,
		"SEGMENT":                       31,
		"SALARY_PROGRAM":                32,
		"ANALYSER":                      33,
		"CATEGORIZER":                   34,
		"PRE_APPROVED_LOAN":             35,
		"CELESTIAL":                     36,
		"RISK":                          37,
		"NUDGE":                         38,
		"SCREENER":                      39,
		"FIREFLY":                       40,
		"AUTH_ORCHESTRATOR":             41,
		"PAY":                           42,
		"USSTOCKS":                      43,
		"TIERING":                       44,
		"AML":                           45,
		"ALFRED":                        46,
		"PAN":                           47,
		"QUEST":                         48,
		"UPCOMING_TRANSACTIONS":         49,
		"HEALTH_ENGINE":                 50,
		"CMS":                           51,
		"ENACH":                         52,
		"BANK_CUSTOMER":                 53,
		"EMPLOYMENT":                    54,
		"COLLECTION":                    55,
		"OMEGLE":                        56,
		"VKYC_CALL":                     57,
		"STOCKGUARDIAN_VKYC_CALL":       58,
		"STOCKGUARDIAN_OMEGLE":          59,
		"STOCKGUARDIAN_CREDIT_REPORT":   60,
		"STOCKGUARDIAN_CREDIT_RISK":     61,
		"STOCKGUARDIAN_E_SIGN":          62,
		"STOCKGUARDIAN_IN_HOUSE_BRE":    63,
		"STOCKGUARDIAN_LMS":             64,
		"STOCKGUARDIAN_APPLICATION":     65,
		"STOCKGUARDIAN_BRE":             66,
		"STOCKGUARDIAN_DOCS":            67,
		"STOCKGUARDIAN_MATRIX":          68,
		"STOCKGUARDIAN_CUSTOMER":        69,
		"STOCKGUARDIAN_APPLICANT":       70,
		"STOCKGUARDIAN_KYC_VENDOR_DATA": 71,
		"TSP_USER":                      72,
		"LEADS":                         73,
		"CREDIT_REPORT":                 74,
		"ACCOUNTS":                      75,
		"NPS":                           76,
		"SECURITIES":                    77,
		"SALARY_ESTIMATION":             78,
		"BILLPAY":                       79,
	}
)

func (x Service) Enum() *Service {
	p := new(Service)
	*p = x
	return p
}

func (x Service) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Service) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_db_state_enums_proto_enumTypes[0].Descriptor()
}

func (Service) Type() protoreflect.EnumType {
	return &file_api_cx_developer_db_state_enums_proto_enumTypes[0]
}

func (x Service) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Service.Descriptor instead.
func (Service) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_enums_proto_rawDescGZIP(), []int{0}
}

// specifies the data type to be used for the given parameter
type ParameterDataType int32

const (
	ParameterDataType_PARAMETER_DATA_TYPE_UNSPECIFIED ParameterDataType = 0
	// value for the parameter will be a string
	// text input box will be shown on UI
	// proto data type string will be used for value
	ParameterDataType_STRING ParameterDataType = 1
	// value of parameter will be a integer
	// number input will be shown on UI
	// proto data type int64 will be used for value
	ParameterDataType_INTEGER ParameterDataType = 2
	// value of parameter will be a double
	// number input will be shown on UI
	// proto data type double will be used for value
	ParameterDataType_DOUBLE ParameterDataType = 3
	// value of parameter will be a timestamp
	// date picker will be shown on UI
	// proto data type google.protobuf.Timestamp will be used for value
	ParameterDataType_TIMESTAMP ParameterDataType = 4
	// value of parameter will be a string(one of the option given in ParameterMeta)
	// a dropdown will be shown on UI with options given in the ParameterMeta
	// user will be able to select only one of the dropdown values
	// proto data type string will be used for value
	ParameterDataType_DROPDOWN ParameterDataType = 5
	// value of parameter will be of type Money(amount and currency)
	// a custom money input box will be shown on UI
	// proto data type google.type.Money  will be used for value
	ParameterDataType_MONEY ParameterDataType = 6
	// value of parameter will be of type Name(honorific, first name,middle name ,last name)
	// a custom money input box will be shown on UI
	// proto data type api.typesv2.common.Name will be used for value
	ParameterDataType_NAME ParameterDataType = 7
	// value of parameter will be of type phone number(country code and national number)
	// a custom money input box will be shown on UI
	// proto data type api.typesv2.common.PhoneNumber will be used for value
	ParameterDataType_PHONE_NUMBER ParameterDataType = 8
	// value of parameter will be a list of strings(one or more options given in ParameterMeta)
	// a dropdown will be shown on UI with options given in the ParameterMeta
	// user will be able to select multiple dropdown values
	// proto data type repeated string will be used for value
	ParameterDataType_MULTI_SELECT_DROPDOWN ParameterDataType = 9
	// value of parameter will be a pagination context object
	// pagination options will be added on UI if this parameter type is used
	// user will be able to select limit and navigate to prev and next pages
	// proto data type PageContextRequest in cx header.proto will be used for value
	ParameterDataType_PAGE_CONTEXT ParameterDataType = 10
	// value of parameter will be a file
	// a file upload option will be shown on UI
	// uploaded file will be passed to backend in bytes format
	ParameterDataType_FILE ParameterDataType = 11
	// value of parameter will be a list of files
	// a file upload option will be shown on UI
	// uploaded files will be passed to backend in bytes format
	ParameterDataType_FILE_LIST ParameterDataType = 12
	// value of parameter will be multiline text
	// a text area will shown on UI for parameters with this type
	// values will be passed as string value to backend
	ParameterDataType_MULTILINE_TEXT ParameterDataType = 13
)

// Enum value maps for ParameterDataType.
var (
	ParameterDataType_name = map[int32]string{
		0:  "PARAMETER_DATA_TYPE_UNSPECIFIED",
		1:  "STRING",
		2:  "INTEGER",
		3:  "DOUBLE",
		4:  "TIMESTAMP",
		5:  "DROPDOWN",
		6:  "MONEY",
		7:  "NAME",
		8:  "PHONE_NUMBER",
		9:  "MULTI_SELECT_DROPDOWN",
		10: "PAGE_CONTEXT",
		11: "FILE",
		12: "FILE_LIST",
		13: "MULTILINE_TEXT",
	}
	ParameterDataType_value = map[string]int32{
		"PARAMETER_DATA_TYPE_UNSPECIFIED": 0,
		"STRING":                          1,
		"INTEGER":                         2,
		"DOUBLE":                          3,
		"TIMESTAMP":                       4,
		"DROPDOWN":                        5,
		"MONEY":                           6,
		"NAME":                            7,
		"PHONE_NUMBER":                    8,
		"MULTI_SELECT_DROPDOWN":           9,
		"PAGE_CONTEXT":                    10,
		"FILE":                            11,
		"FILE_LIST":                       12,
		"MULTILINE_TEXT":                  13,
	}
)

func (x ParameterDataType) Enum() *ParameterDataType {
	p := new(ParameterDataType)
	*p = x
	return p
}

func (x ParameterDataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParameterDataType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_db_state_enums_proto_enumTypes[1].Descriptor()
}

func (ParameterDataType) Type() protoreflect.EnumType {
	return &file_api_cx_developer_db_state_enums_proto_enumTypes[1]
}

func (x ParameterDataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParameterDataType.Descriptor instead.
func (ParameterDataType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_enums_proto_rawDescGZIP(), []int{1}
}

// indicates whether the given parameter is optional or mandatory
type ParameterOption int32

const (
	ParameterOption_PARAMETER_OPTION_UNSEPCIFIED ParameterOption = 0
	// given parameter is optional and not needed to be passed in every data request
	ParameterOption_OPTIONAL ParameterOption = 1
	// given parameter is mandatory and will be passed in every data request
	// validation will be added on UI
	// submit will be disabled till all the mandatory fields are filled
	ParameterOption_MANDATORY ParameterOption = 2
)

// Enum value maps for ParameterOption.
var (
	ParameterOption_name = map[int32]string{
		0: "PARAMETER_OPTION_UNSEPCIFIED",
		1: "OPTIONAL",
		2: "MANDATORY",
	}
	ParameterOption_value = map[string]int32{
		"PARAMETER_OPTION_UNSEPCIFIED": 0,
		"OPTIONAL":                     1,
		"MANDATORY":                    2,
	}
)

func (x ParameterOption) Enum() *ParameterOption {
	p := new(ParameterOption)
	*p = x
	return p
}

func (x ParameterOption) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ParameterOption) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_db_state_enums_proto_enumTypes[2].Descriptor()
}

func (ParameterOption) Type() protoreflect.EnumType {
	return &file_api_cx_developer_db_state_enums_proto_enumTypes[2]
}

func (x ParameterOption) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ParameterOption.Descriptor instead.
func (ParameterOption) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_enums_proto_rawDescGZIP(), []int{2}
}

type FileContentType int32

const (
	FileContentType_FILE_CONTENT_TYPE_UNSPECIFIED FileContentType = 0
	FileContentType_FILE_CONTENT_TYPE_CSV         FileContentType = 1
	FileContentType_FILE_CONTENT_TYPE_TXT         FileContentType = 2
	FileContentType_FILE_CONTENT_TYPE_JPEG        FileContentType = 3
	FileContentType_FILE_CONTENT_TYPE_XLS         FileContentType = 4
	FileContentType_FILE_CONTENT_TYPE_XLSX        FileContentType = 5
	FileContentType_FILE_CONTENT_TYPE_ZIP         FileContentType = 7
	FileContentType_FILE_CONTENT_TYPE_PDF         FileContentType = 8
	FileContentType_FILE_CONTENT_TYPE_DOCX        FileContentType = 10
	FileContentType_FILE_CONTENT_TYPE_JPG         FileContentType = 11
	FileContentType_FILE_CONTENT_TYPE_PNG         FileContentType = 12
	FileContentType_FILE_CONTENT_TYPE_WEBP        FileContentType = 13
)

// Enum value maps for FileContentType.
var (
	FileContentType_name = map[int32]string{
		0:  "FILE_CONTENT_TYPE_UNSPECIFIED",
		1:  "FILE_CONTENT_TYPE_CSV",
		2:  "FILE_CONTENT_TYPE_TXT",
		3:  "FILE_CONTENT_TYPE_JPEG",
		4:  "FILE_CONTENT_TYPE_XLS",
		5:  "FILE_CONTENT_TYPE_XLSX",
		7:  "FILE_CONTENT_TYPE_ZIP",
		8:  "FILE_CONTENT_TYPE_PDF",
		10: "FILE_CONTENT_TYPE_DOCX",
		11: "FILE_CONTENT_TYPE_JPG",
		12: "FILE_CONTENT_TYPE_PNG",
		13: "FILE_CONTENT_TYPE_WEBP",
	}
	FileContentType_value = map[string]int32{
		"FILE_CONTENT_TYPE_UNSPECIFIED": 0,
		"FILE_CONTENT_TYPE_CSV":         1,
		"FILE_CONTENT_TYPE_TXT":         2,
		"FILE_CONTENT_TYPE_JPEG":        3,
		"FILE_CONTENT_TYPE_XLS":         4,
		"FILE_CONTENT_TYPE_XLSX":        5,
		"FILE_CONTENT_TYPE_ZIP":         7,
		"FILE_CONTENT_TYPE_PDF":         8,
		"FILE_CONTENT_TYPE_DOCX":        10,
		"FILE_CONTENT_TYPE_JPG":         11,
		"FILE_CONTENT_TYPE_PNG":         12,
		"FILE_CONTENT_TYPE_WEBP":        13,
	}
)

func (x FileContentType) Enum() *FileContentType {
	p := new(FileContentType)
	*p = x
	return p
}

func (x FileContentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FileContentType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_cx_developer_db_state_enums_proto_enumTypes[3].Descriptor()
}

func (FileContentType) Type() protoreflect.EnumType {
	return &file_api_cx_developer_db_state_enums_proto_enumTypes[3]
}

func (x FileContentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FileContentType.Descriptor instead.
func (FileContentType) EnumDescriptor() ([]byte, []int) {
	return file_api_cx_developer_db_state_enums_proto_rawDescGZIP(), []int{3}
}

var File_api_cx_developer_db_state_enums_proto protoreflect.FileDescriptor

var file_api_cx_developer_db_state_enums_proto_rawDesc = []byte{
	0x0a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2f, 0x65, 0x6e, 0x75, 0x6d,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x63, 0x78, 0x2e, 0x64, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x2a, 0xda,
	0x0a, 0x0a, 0x07, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x53, 0x45,
	0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x43, 0x58, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x43,
	0x4f, 0x4d, 0x4d, 0x53, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x50, 0x4f, 0x53, 0x49, 0x54, 0x10, 0x04, 0x12, 0x08,
	0x0a, 0x04, 0x55, 0x53, 0x45, 0x52, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x4f, 0x52, 0x44, 0x45,
	0x52, 0x10, 0x06, 0x12, 0x07, 0x0a, 0x03, 0x4b, 0x59, 0x43, 0x10, 0x07, 0x12, 0x16, 0x0a, 0x12,
	0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x52, 0x55, 0x4d, 0x45,
	0x4e, 0x54, 0x10, 0x08, 0x12, 0x08, 0x0a, 0x04, 0x43, 0x41, 0x52, 0x44, 0x10, 0x09, 0x12, 0x0c,
	0x0a, 0x08, 0x4c, 0x49, 0x56, 0x45, 0x4e, 0x45, 0x53, 0x53, 0x10, 0x0a, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x41, 0x56, 0x49, 0x4e, 0x47, 0x53, 0x10, 0x0b, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x50, 0x49,
	0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x49, 0x4e, 0x41, 0x50, 0x50, 0x48, 0x45, 0x4c, 0x50, 0x10,
	0x0d, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x53, 0x42, 0x49, 0x4e, 0x10, 0x0e, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x53, 0x49, 0x47, 0x48, 0x54, 0x53, 0x10, 0x0f, 0x12, 0x0c, 0x0a, 0x08, 0x57,
	0x41, 0x49, 0x54, 0x4c, 0x49, 0x53, 0x54, 0x10, 0x10, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x57,
	0x41, 0x52, 0x44, 0x53, 0x10, 0x11, 0x12, 0x08, 0x0a, 0x04, 0x41, 0x55, 0x54, 0x48, 0x10, 0x12,
	0x12, 0x12, 0x0a, 0x0e, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x4d, 0x41, 0x50, 0x50, 0x49,
	0x4e, 0x47, 0x10, 0x13, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x49, 0x4d, 0x45, 0x4c, 0x49, 0x4e, 0x45,
	0x10, 0x14, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x41, 0x53, 0x50, 0x45, 0x52, 0x10, 0x15, 0x12, 0x0c,
	0x0a, 0x08, 0x4d, 0x45, 0x52, 0x43, 0x48, 0x41, 0x4e, 0x54, 0x10, 0x16, 0x12, 0x07, 0x0a, 0x03,
	0x52, 0x4d, 0x53, 0x10, 0x17, 0x12, 0x09, 0x0a, 0x05, 0x46, 0x49, 0x54, 0x54, 0x54, 0x10, 0x18,
	0x12, 0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x45, 0x44, 0x5f, 0x41, 0x43,
	0x43, 0x4f, 0x55, 0x4e, 0x54, 0x10, 0x19, 0x12, 0x13, 0x0a, 0x0f, 0x49, 0x4e, 0x5f, 0x41, 0x50,
	0x50, 0x5f, 0x52, 0x45, 0x46, 0x45, 0x52, 0x52, 0x41, 0x4c, 0x10, 0x1a, 0x12, 0x15, 0x0a, 0x11,
	0x57, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x1b, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e,
	0x54, 0x10, 0x1c, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x45, 0x43, 0x55, 0x52, 0x52, 0x49, 0x4e, 0x47,
	0x5f, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1d, 0x12, 0x12, 0x0a, 0x0e, 0x50, 0x32,
	0x50, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1e, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x45, 0x47, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x1f, 0x12, 0x12, 0x0a, 0x0e, 0x53,
	0x41, 0x4c, 0x41, 0x52, 0x59, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x41, 0x4d, 0x10, 0x20, 0x12,
	0x0c, 0x0a, 0x08, 0x41, 0x4e, 0x41, 0x4c, 0x59, 0x53, 0x45, 0x52, 0x10, 0x21, 0x12, 0x0f, 0x0a,
	0x0b, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x49, 0x5a, 0x45, 0x52, 0x10, 0x22, 0x12, 0x15,
	0x0a, 0x11, 0x50, 0x52, 0x45, 0x5f, 0x41, 0x50, 0x50, 0x52, 0x4f, 0x56, 0x45, 0x44, 0x5f, 0x4c,
	0x4f, 0x41, 0x4e, 0x10, 0x23, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x45, 0x4c, 0x45, 0x53, 0x54, 0x49,
	0x41, 0x4c, 0x10, 0x24, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x25, 0x12, 0x09,
	0x0a, 0x05, 0x4e, 0x55, 0x44, 0x47, 0x45, 0x10, 0x26, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x43, 0x52,
	0x45, 0x45, 0x4e, 0x45, 0x52, 0x10, 0x27, 0x12, 0x0b, 0x0a, 0x07, 0x46, 0x49, 0x52, 0x45, 0x46,
	0x4c, 0x59, 0x10, 0x28, 0x12, 0x15, 0x0a, 0x11, 0x41, 0x55, 0x54, 0x48, 0x5f, 0x4f, 0x52, 0x43,
	0x48, 0x45, 0x53, 0x54, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x10, 0x29, 0x12, 0x07, 0x0a, 0x03, 0x50,
	0x41, 0x59, 0x10, 0x2a, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x53, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x53,
	0x10, 0x2b, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x49, 0x45, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x2c, 0x12,
	0x07, 0x0a, 0x03, 0x41, 0x4d, 0x4c, 0x10, 0x2d, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x4c, 0x46, 0x52,
	0x45, 0x44, 0x10, 0x2e, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x41, 0x4e, 0x10, 0x2f, 0x12, 0x09, 0x0a,
	0x05, 0x51, 0x55, 0x45, 0x53, 0x54, 0x10, 0x30, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50, 0x43, 0x4f,
	0x4d, 0x49, 0x4e, 0x47, 0x5f, 0x54, 0x52, 0x41, 0x4e, 0x53, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x53, 0x10, 0x31, 0x12, 0x11, 0x0a, 0x0d, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x5f, 0x45, 0x4e,
	0x47, 0x49, 0x4e, 0x45, 0x10, 0x32, 0x12, 0x07, 0x0a, 0x03, 0x43, 0x4d, 0x53, 0x10, 0x33, 0x12,
	0x09, 0x0a, 0x05, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x10, 0x34, 0x12, 0x11, 0x0a, 0x0d, 0x42, 0x41,
	0x4e, 0x4b, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d, 0x45, 0x52, 0x10, 0x35, 0x12, 0x0e, 0x0a,
	0x0a, 0x45, 0x4d, 0x50, 0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x10, 0x36, 0x12, 0x0e, 0x0a,
	0x0a, 0x43, 0x4f, 0x4c, 0x4c, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x37, 0x12, 0x0a, 0x0a,
	0x06, 0x4f, 0x4d, 0x45, 0x47, 0x4c, 0x45, 0x10, 0x38, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x4b, 0x59,
	0x43, 0x5f, 0x43, 0x41, 0x4c, 0x4c, 0x10, 0x39, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x56, 0x4b, 0x59, 0x43, 0x5f, 0x43,
	0x41, 0x4c, 0x4c, 0x10, 0x3a, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55,
	0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x4f, 0x4d, 0x45, 0x47, 0x4c, 0x45, 0x10, 0x3b, 0x12,
	0x1f, 0x0a, 0x1b, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e,
	0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x3c,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x49, 0x53, 0x4b, 0x10, 0x3d, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e,
	0x5f, 0x45, 0x5f, 0x53, 0x49, 0x47, 0x4e, 0x10, 0x3e, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x48, 0x4f,
	0x55, 0x53, 0x45, 0x5f, 0x42, 0x52, 0x45, 0x10, 0x3f, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x54, 0x4f,
	0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x4c, 0x4d, 0x53, 0x10, 0x40,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41,
	0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x41, 0x12,
	0x15, 0x0a, 0x11, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e,
	0x5f, 0x42, 0x52, 0x45, 0x10, 0x42, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47,
	0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x44, 0x4f, 0x43, 0x53, 0x10, 0x43, 0x12, 0x18,
	0x0a, 0x14, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f,
	0x4d, 0x41, 0x54, 0x52, 0x49, 0x58, 0x10, 0x44, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x54, 0x4f, 0x43,
	0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x43, 0x55, 0x53, 0x54, 0x4f, 0x4d,
	0x45, 0x52, 0x10, 0x45, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41,
	0x52, 0x44, 0x49, 0x41, 0x4e, 0x5f, 0x41, 0x50, 0x50, 0x4c, 0x49, 0x43, 0x41, 0x4e, 0x54, 0x10,
	0x46, 0x12, 0x21, 0x0a, 0x1d, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x47, 0x55, 0x41, 0x52, 0x44, 0x49,
	0x41, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x5f, 0x56, 0x45, 0x4e, 0x44, 0x4f, 0x52, 0x5f, 0x44, 0x41,
	0x54, 0x41, 0x10, 0x47, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x53, 0x50, 0x5f, 0x55, 0x53, 0x45, 0x52,
	0x10, 0x48, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x45, 0x41, 0x44, 0x53, 0x10, 0x49, 0x12, 0x11, 0x0a,
	0x0d, 0x43, 0x52, 0x45, 0x44, 0x49, 0x54, 0x5f, 0x52, 0x45, 0x50, 0x4f, 0x52, 0x54, 0x10, 0x4a,
	0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x53, 0x10, 0x4b, 0x12, 0x07,
	0x0a, 0x03, 0x4e, 0x50, 0x53, 0x10, 0x4c, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x4d, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x41, 0x4c, 0x41, 0x52,
	0x59, 0x5f, 0x45, 0x53, 0x54, 0x49, 0x4d, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x4e, 0x12, 0x0b,
	0x0a, 0x07, 0x42, 0x49, 0x4c, 0x4c, 0x50, 0x41, 0x59, 0x10, 0x4f, 0x2a, 0xfb, 0x01, 0x0a, 0x11,
	0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x23, 0x0a, 0x1f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x44,
	0x41, 0x54, 0x41, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x54, 0x45, 0x47, 0x45, 0x52, 0x10, 0x02, 0x12,
	0x0a, 0x0a, 0x06, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x54,
	0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x52,
	0x4f, 0x50, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05, 0x4d, 0x4f, 0x4e, 0x45,
	0x59, 0x10, 0x06, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x41, 0x4d, 0x45, 0x10, 0x07, 0x12, 0x10, 0x0a,
	0x0c, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x08, 0x12,
	0x19, 0x0a, 0x15, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x5f,
	0x44, 0x52, 0x4f, 0x50, 0x44, 0x4f, 0x57, 0x4e, 0x10, 0x09, 0x12, 0x10, 0x0a, 0x0c, 0x50, 0x41,
	0x47, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0a, 0x12, 0x08, 0x0a, 0x04,
	0x46, 0x49, 0x4c, 0x45, 0x10, 0x0b, 0x12, 0x0d, 0x0a, 0x09, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x4c,
	0x49, 0x53, 0x54, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x4d, 0x55, 0x4c, 0x54, 0x49, 0x4c, 0x49,
	0x4e, 0x45, 0x5f, 0x54, 0x45, 0x58, 0x54, 0x10, 0x0d, 0x2a, 0x50, 0x0a, 0x0f, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x1c,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x45, 0x50, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x4f, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09,
	0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x4f, 0x52, 0x59, 0x10, 0x02, 0x2a, 0xe1, 0x02, 0x0a, 0x0f,
	0x46, 0x69, 0x6c, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45,
	0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x53, 0x56, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x58, 0x54, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4a, 0x50,
	0x45, 0x47, 0x10, 0x03, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e,
	0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x4c, 0x53, 0x10, 0x04, 0x12,
	0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x58, 0x4c, 0x53, 0x58, 0x10, 0x05, 0x12, 0x19, 0x0a, 0x15, 0x46,
	0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x5a, 0x49, 0x50, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x44, 0x46, 0x10,
	0x08, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x4f, 0x43, 0x58, 0x10, 0x0a, 0x12, 0x19, 0x0a,
	0x15, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4a, 0x50, 0x47, 0x10, 0x0b, 0x12, 0x19, 0x0a, 0x15, 0x46, 0x49, 0x4c, 0x45,
	0x5f, 0x43, 0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x4e,
	0x47, 0x10, 0x0c, 0x12, 0x1a, 0x0a, 0x16, 0x46, 0x49, 0x4c, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x54,
	0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x45, 0x42, 0x50, 0x10, 0x0d, 0x42,
	0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x63, 0x78,
	0x2e, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2e, 0x64, 0x62, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x63, 0x78, 0x2f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x72, 0x2f, 0x64, 0x62, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_cx_developer_db_state_enums_proto_rawDescOnce sync.Once
	file_api_cx_developer_db_state_enums_proto_rawDescData = file_api_cx_developer_db_state_enums_proto_rawDesc
)

func file_api_cx_developer_db_state_enums_proto_rawDescGZIP() []byte {
	file_api_cx_developer_db_state_enums_proto_rawDescOnce.Do(func() {
		file_api_cx_developer_db_state_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_cx_developer_db_state_enums_proto_rawDescData)
	})
	return file_api_cx_developer_db_state_enums_proto_rawDescData
}

var file_api_cx_developer_db_state_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_cx_developer_db_state_enums_proto_goTypes = []interface{}{
	(Service)(0),           // 0: cx.developer.db_state.Service
	(ParameterDataType)(0), // 1: cx.developer.db_state.ParameterDataType
	(ParameterOption)(0),   // 2: cx.developer.db_state.ParameterOption
	(FileContentType)(0),   // 3: cx.developer.db_state.FileContentType
}
var file_api_cx_developer_db_state_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_cx_developer_db_state_enums_proto_init() }
func file_api_cx_developer_db_state_enums_proto_init() {
	if File_api_cx_developer_db_state_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_cx_developer_db_state_enums_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_cx_developer_db_state_enums_proto_goTypes,
		DependencyIndexes: file_api_cx_developer_db_state_enums_proto_depIdxs,
		EnumInfos:         file_api_cx_developer_db_state_enums_proto_enumTypes,
	}.Build()
	File_api_cx_developer_db_state_enums_proto = out.File
	file_api_cx_developer_db_state_enums_proto_rawDesc = nil
	file_api_cx_developer_db_state_enums_proto_goTypes = nil
	file_api_cx_developer_db_state_enums_proto_depIdxs = nil
}
