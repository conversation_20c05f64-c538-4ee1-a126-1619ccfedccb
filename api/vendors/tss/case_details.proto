syntax = "proto3";

package vendors.tss;

option go_package = "github.com/epifi/gamma/api/vendors/tss";
option java_package = "com.github.epifi.gamma.api.vendors.tss";

message CaseDetails {
  string case_id = 1 [json_name = "caseId"];
  string case_creation_date_time_in_utc = 2 [json_name = "caseCreationDateTimeInUTC"];
  string source_system_name = 3 [json_name = "sourceSystemName"];
  string source_system_customer_code = 4 [json_name = "sourceSystemCustomerCode"];
  string application_ref_number = 5 [json_name = "applicationRefNumber"];
  string case_of = 6 [json_name = "caseOf"];
  string linked_to_source_system_customer_code = 7 [json_name = "linkedToSourceSystemCustomerCode"];
  string relation = 8 [json_name = "relation"];
  string screening_profile = 9 [json_name = "screeningProfile"];
  string screening_profile_name = 10 [json_name = "screeningProfileName"];
  string customer_name = 11 [json_name = "customerName"];
  string case_type = 12 [json_name = "caseType"];
  string initial_screening_mode = 13 [json_name = "initialScreeningMode"];
  string onboarding_decision = 14 [json_name = "onboardingDecision"];
  int32 total_alert_count = 15 [json_name = "totalAlertCount"];
  int32 confirmed_alert_count = 16 [json_name = "confirmedAlertCount"];
  int32 probable_alert_count = 17 [json_name = "probableAlertCount"];
  int32 pending_for_decision = 18 [json_name = "pendingForDecision"];
  int32 no_match_count = 19 [json_name = "noMatchCount"];
  int32 true_match_count = 20 [json_name = "trueMatchCount"];
  string case_stage = 21 [json_name = "caseStage"];
  string case_category = 22 [json_name = "caseCategory"];
  string current_assignee = 23 [json_name = "currentAssignee"];
  string case_closure_date_time_in_utc = 24 [json_name = "caseClosureDateTimeInUTC"];
  string final_remarks = 25 [json_name = "finalRemarks"];
  repeated CaseAction case_actions = 26 [json_name = "caseActions"];
  repeated AlertDetails alert_details = 27 [json_name = "alertDetails"];
}

message CaseAction {
  string user_name = 1 [json_name = "userName"];
  string date_time_in_utc = 2 [json_name = "dateTimeInUTC"];
  string action = 3 [json_name = "action"];
}

message AlertDetails {
  string alert_id = 1 [json_name = "alertId"];
  string source = 2 [json_name = "source"];
  string watchlist_source_id = 3 [json_name = "watchlistSourceId"];
  string match_type = 4 [json_name = "matchType"];
  repeated string matching_attributes = 5 [json_name = "matchingAttributes"];
  repeated SourceIdentification source_identification = 6 [json_name = "sourceIdentification"];
  string watchlist_name = 7 [json_name = "watchlistName"];
  string alert_decision = 8 [json_name = "alertDecision"];
  repeated Comment comments = 9 [json_name = "comments"];
}

message SourceIdentification {
  string source_identification_id = 1 [json_name = "sourceIdentificationID"];
  string source_identification_key = 2 [json_name = "sourceIdentificationKey"];
  string source_identification_value = 3 [json_name = "sourceIdentificationValue"];
}

message Comment {
  string user_name = 1 [json_name = "userName"];
  string date_time_in_utc = 2 [json_name = "dateTimeInUTC"];
  string comment = 3 [json_name = "comment"];
}
