// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendors/tss/as502.proto

package tss

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Case category enum
type CaseCategory int32

const (
	CaseCategory_CASE_CATEGORY_UNSPECIFIED CaseCategory = 0
	CaseCategory_CASE_CATEGORY_OPEN        CaseCategory = 1
	CaseCategory_CASE_CATEGORY_PENDING     CaseCategory = 2
	CaseCategory_CASE_CATEGORY_COMPLETED   CaseCategory = 3
)

// Enum value maps for CaseCategory.
var (
	CaseCategory_name = map[int32]string{
		0: "CASE_CATEGORY_UNSPECIFIED",
		1: "CASE_CATEGORY_OPEN",
		2: "CASE_CATEGORY_PENDING",
		3: "CASE_CATEGORY_COMPLETED",
	}
	CaseCategory_value = map[string]int32{
		"CASE_CATEGORY_UNSPECIFIED": 0,
		"CASE_CATEGORY_OPEN":        1,
		"CASE_CATEGORY_PENDING":     2,
		"CASE_CATEGORY_COMPLETED":   3,
	}
)

func (x CaseCategory) Enum() *CaseCategory {
	p := new(CaseCategory)
	*p = x
	return p
}

func (x CaseCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[0].Descriptor()
}

func (CaseCategory) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[0]
}

func (x CaseCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseCategory.Descriptor instead.
func (CaseCategory) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{0}
}

// Case type enum
type CaseType int32

const (
	CaseType_CASE_TYPE_UNSPECIFIED     CaseType = 0
	CaseType_CASE_TYPE_INITIAL         CaseType = 1
	CaseType_CASE_TYPE_WATCHLIST_ADDED CaseType = 2
)

// Enum value maps for CaseType.
var (
	CaseType_name = map[int32]string{
		0: "CASE_TYPE_UNSPECIFIED",
		1: "CASE_TYPE_INITIAL",
		2: "CASE_TYPE_WATCHLIST_ADDED",
	}
	CaseType_value = map[string]int32{
		"CASE_TYPE_UNSPECIFIED":     0,
		"CASE_TYPE_INITIAL":         1,
		"CASE_TYPE_WATCHLIST_ADDED": 2,
	}
)

func (x CaseType) Enum() *CaseType {
	p := new(CaseType)
	*p = x
	return p
}

func (x CaseType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[1].Descriptor()
}

func (CaseType) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[1]
}

func (x CaseType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseType.Descriptor instead.
func (CaseType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{1}
}

// Initial screening mode enum
type InitialScreeningMode int32

const (
	InitialScreeningMode_INITIAL_SCREENING_MODE_UNSPECIFIED    InitialScreeningMode = 0
	InitialScreeningMode_INITIAL_SCREENING_MODE_API            InitialScreeningMode = 1
	InitialScreeningMode_INITIAL_SCREENING_MODE_LOOKUP_IN_BULK InitialScreeningMode = 2
)

// Enum value maps for InitialScreeningMode.
var (
	InitialScreeningMode_name = map[int32]string{
		0: "INITIAL_SCREENING_MODE_UNSPECIFIED",
		1: "INITIAL_SCREENING_MODE_API",
		2: "INITIAL_SCREENING_MODE_LOOKUP_IN_BULK",
	}
	InitialScreeningMode_value = map[string]int32{
		"INITIAL_SCREENING_MODE_UNSPECIFIED":    0,
		"INITIAL_SCREENING_MODE_API":            1,
		"INITIAL_SCREENING_MODE_LOOKUP_IN_BULK": 2,
	}
)

func (x InitialScreeningMode) Enum() *InitialScreeningMode {
	p := new(InitialScreeningMode)
	*p = x
	return p
}

func (x InitialScreeningMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitialScreeningMode) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[2].Descriptor()
}

func (InitialScreeningMode) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[2]
}

func (x InitialScreeningMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitialScreeningMode.Descriptor instead.
func (InitialScreeningMode) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{2}
}

// Onboarding decision enum
type OnboardingDecision int32

const (
	OnboardingDecision_ONBOARDING_DECISION_UNSPECIFIED OnboardingDecision = 0
	OnboardingDecision_ONBOARDING_DECISION_PROCEED     OnboardingDecision = 1
	OnboardingDecision_ONBOARDING_DECISION_DECLINE     OnboardingDecision = 2
)

// Enum value maps for OnboardingDecision.
var (
	OnboardingDecision_name = map[int32]string{
		0: "ONBOARDING_DECISION_UNSPECIFIED",
		1: "ONBOARDING_DECISION_PROCEED",
		2: "ONBOARDING_DECISION_DECLINE",
	}
	OnboardingDecision_value = map[string]int32{
		"ONBOARDING_DECISION_UNSPECIFIED": 0,
		"ONBOARDING_DECISION_PROCEED":     1,
		"ONBOARDING_DECISION_DECLINE":     2,
	}
)

func (x OnboardingDecision) Enum() *OnboardingDecision {
	p := new(OnboardingDecision)
	*p = x
	return p
}

func (x OnboardingDecision) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OnboardingDecision) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[3].Descriptor()
}

func (OnboardingDecision) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[3]
}

func (x OnboardingDecision) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OnboardingDecision.Descriptor instead.
func (OnboardingDecision) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{3}
}

// Case of enum
type CaseOf int32

const (
	CaseOf_CASE_OF_UNSPECIFIED        CaseOf = 0
	CaseOf_CASE_OF_MAIN_KYC           CaseOf = 1
	CaseOf_CASE_OF_RELATED_PERSON_KYC CaseOf = 2
)

// Enum value maps for CaseOf.
var (
	CaseOf_name = map[int32]string{
		0: "CASE_OF_UNSPECIFIED",
		1: "CASE_OF_MAIN_KYC",
		2: "CASE_OF_RELATED_PERSON_KYC",
	}
	CaseOf_value = map[string]int32{
		"CASE_OF_UNSPECIFIED":        0,
		"CASE_OF_MAIN_KYC":           1,
		"CASE_OF_RELATED_PERSON_KYC": 2,
	}
)

func (x CaseOf) Enum() *CaseOf {
	p := new(CaseOf)
	*p = x
	return p
}

func (x CaseOf) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CaseOf) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[4].Descriptor()
}

func (CaseOf) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[4]
}

func (x CaseOf) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CaseOf.Descriptor instead.
func (CaseOf) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{4}
}

// Match type enum
type MatchType int32

const (
	MatchType_MATCH_TYPE_UNSPECIFIED MatchType = 0
	MatchType_MATCH_TYPE_CONFIRMED   MatchType = 1
	MatchType_MATCH_TYPE_PROBABLE    MatchType = 2
)

// Enum value maps for MatchType.
var (
	MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "MATCH_TYPE_CONFIRMED",
		2: "MATCH_TYPE_PROBABLE",
	}
	MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"MATCH_TYPE_CONFIRMED":   1,
		"MATCH_TYPE_PROBABLE":    2,
	}
)

func (x MatchType) Enum() *MatchType {
	p := new(MatchType)
	*p = x
	return p
}

func (x MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[5].Descriptor()
}

func (MatchType) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[5]
}

func (x MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchType.Descriptor instead.
func (MatchType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{5}
}

// Alert decision enum
type AlertDecision int32

const (
	AlertDecision_ALERT_DECISION_UNSPECIFIED AlertDecision = 0
	AlertDecision_ALERT_DECISION_TRUE_MATCH  AlertDecision = 1
	AlertDecision_ALERT_DECISION_NO_MATCH    AlertDecision = 2
	AlertDecision_ALERT_DECISION_PENDING     AlertDecision = 3
)

// Enum value maps for AlertDecision.
var (
	AlertDecision_name = map[int32]string{
		0: "ALERT_DECISION_UNSPECIFIED",
		1: "ALERT_DECISION_TRUE_MATCH",
		2: "ALERT_DECISION_NO_MATCH",
		3: "ALERT_DECISION_PENDING",
	}
	AlertDecision_value = map[string]int32{
		"ALERT_DECISION_UNSPECIFIED": 0,
		"ALERT_DECISION_TRUE_MATCH":  1,
		"ALERT_DECISION_NO_MATCH":    2,
		"ALERT_DECISION_PENDING":     3,
	}
)

func (x AlertDecision) Enum() *AlertDecision {
	p := new(AlertDecision)
	*p = x
	return p
}

func (x AlertDecision) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AlertDecision) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendors_tss_as502_proto_enumTypes[6].Descriptor()
}

func (AlertDecision) Type() protoreflect.EnumType {
	return &file_api_vendors_tss_as502_proto_enumTypes[6]
}

func (x AlertDecision) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AlertDecision.Descriptor instead.
func (AlertDecision) EnumDescriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{6}
}

// Request message for AS502 API
type AS502Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId     string              `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ApiRequestDto *AS502ApiRequestDto `protobuf:"bytes,2,opt,name=api_request_dto,json=apiRequestDto,proto3" json:"api_request_dto,omitempty"`
}

func (x *AS502Request) Reset() {
	*x = AS502Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS502Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS502Request) ProtoMessage() {}

func (x *AS502Request) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS502Request.ProtoReflect.Descriptor instead.
func (*AS502Request) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{0}
}

func (x *AS502Request) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *AS502Request) GetApiRequestDto() *AS502ApiRequestDto {
	if x != nil {
		return x.ApiRequestDto
	}
	return nil
}

// API request data transfer object
type AS502ApiRequestDto struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseCategory     []CaseCategory         `protobuf:"varint,1,rep,packed,name=case_category,json=caseCategory,proto3,enum=vendors.tss.CaseCategory" json:"case_category,omitempty"`
	SourceSystemName string                 `protobuf:"bytes,2,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	FromDateTime     *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=from_date_time,json=fromDateTime,proto3" json:"from_date_time,omitempty"`
	ToDateTime       *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=to_date_time,json=toDateTime,proto3" json:"to_date_time,omitempty"`
	CaseType         []CaseType             `protobuf:"varint,5,rep,packed,name=case_type,json=caseType,proto3,enum=vendors.tss.CaseType" json:"case_type,omitempty"`
}

func (x *AS502ApiRequestDto) Reset() {
	*x = AS502ApiRequestDto{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS502ApiRequestDto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS502ApiRequestDto) ProtoMessage() {}

func (x *AS502ApiRequestDto) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS502ApiRequestDto.ProtoReflect.Descriptor instead.
func (*AS502ApiRequestDto) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{1}
}

func (x *AS502ApiRequestDto) GetCaseCategory() []CaseCategory {
	if x != nil {
		return x.CaseCategory
	}
	return nil
}

func (x *AS502ApiRequestDto) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *AS502ApiRequestDto) GetFromDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.FromDateTime
	}
	return nil
}

func (x *AS502ApiRequestDto) GetToDateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ToDateTime
	}
	return nil
}

func (x *AS502ApiRequestDto) GetCaseType() []CaseType {
	if x != nil {
		return x.CaseType
	}
	return nil
}

// Response message for AS502 API
type AS502Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RequestId string `protobuf:"bytes,1,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// Types that are assignable to Response:
	//
	//	*AS502Response_ResponseData
	//	*AS502Response_ErrorResponse
	Response isAS502Response_Response `protobuf_oneof:"response"`
}

func (x *AS502Response) Reset() {
	*x = AS502Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS502Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS502Response) ProtoMessage() {}

func (x *AS502Response) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS502Response.ProtoReflect.Descriptor instead.
func (*AS502Response) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{2}
}

func (x *AS502Response) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (m *AS502Response) GetResponse() isAS502Response_Response {
	if m != nil {
		return m.Response
	}
	return nil
}

func (x *AS502Response) GetResponseData() *AS502ResponseData {
	if x, ok := x.GetResponse().(*AS502Response_ResponseData); ok {
		return x.ResponseData
	}
	return nil
}

func (x *AS502Response) GetErrorResponse() *AS502ErrorResponse {
	if x, ok := x.GetResponse().(*AS502Response_ErrorResponse); ok {
		return x.ErrorResponse
	}
	return nil
}

type isAS502Response_Response interface {
	isAS502Response_Response()
}

type AS502Response_ResponseData struct {
	ResponseData *AS502ResponseData `protobuf:"bytes,2,opt,name=response_data,json=responseData,proto3,oneof"`
}

type AS502Response_ErrorResponse struct {
	ErrorResponse *AS502ErrorResponse `protobuf:"bytes,3,opt,name=error_response,json=errorResponse,proto3,oneof"`
}

func (*AS502Response_ResponseData) isAS502Response_Response() {}

func (*AS502Response_ErrorResponse) isAS502Response_Response() {}

// Success response data
type AS502ResponseData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseCount   int32          `protobuf:"varint,1,opt,name=case_count,json=caseCount,proto3" json:"case_count,omitempty"`
	CaseDetails []*CaseDetails `protobuf:"bytes,2,rep,name=case_details,json=caseDetails,proto3" json:"case_details,omitempty"`
}

func (x *AS502ResponseData) Reset() {
	*x = AS502ResponseData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS502ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS502ResponseData) ProtoMessage() {}

func (x *AS502ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS502ResponseData.ProtoReflect.Descriptor instead.
func (*AS502ResponseData) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{3}
}

func (x *AS502ResponseData) GetCaseCount() int32 {
	if x != nil {
		return x.CaseCount
	}
	return 0
}

func (x *AS502ResponseData) GetCaseDetails() []*CaseDetails {
	if x != nil {
		return x.CaseDetails
	}
	return nil
}

// Error response for validation failures
type AS502ErrorResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ValidationCodes        []string `protobuf:"bytes,1,rep,name=validation_codes,json=validationCodes,proto3" json:"validation_codes,omitempty"`
	ValidationDescriptions []string `protobuf:"bytes,2,rep,name=validation_descriptions,json=validationDescriptions,proto3" json:"validation_descriptions,omitempty"`
}

func (x *AS502ErrorResponse) Reset() {
	*x = AS502ErrorResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AS502ErrorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AS502ErrorResponse) ProtoMessage() {}

func (x *AS502ErrorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AS502ErrorResponse.ProtoReflect.Descriptor instead.
func (*AS502ErrorResponse) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{4}
}

func (x *AS502ErrorResponse) GetValidationCodes() []string {
	if x != nil {
		return x.ValidationCodes
	}
	return nil
}

func (x *AS502ErrorResponse) GetValidationDescriptions() []string {
	if x != nil {
		return x.ValidationDescriptions
	}
	return nil
}

// Case details structure
type CaseDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CaseId                           string                 `protobuf:"bytes,1,opt,name=case_id,json=caseId,proto3" json:"case_id,omitempty"`
	CaseCreationDateTimeInUtc        *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=case_creation_date_time_in_utc,json=caseCreationDateTimeInUTC,proto3" json:"case_creation_date_time_in_utc,omitempty"`
	SourceSystemName                 string                 `protobuf:"bytes,3,opt,name=source_system_name,json=sourceSystemName,proto3" json:"source_system_name,omitempty"`
	SourceSystemCustomerCode         string                 `protobuf:"bytes,4,opt,name=source_system_customer_code,json=sourceSystemCustomerCode,proto3" json:"source_system_customer_code,omitempty"`
	ApplicationRefNumber             string                 `protobuf:"bytes,5,opt,name=application_ref_number,json=applicationRefNumber,proto3" json:"application_ref_number,omitempty"`
	CaseOf                           CaseOf                 `protobuf:"varint,6,opt,name=case_of,json=caseOf,proto3,enum=vendors.tss.CaseOf" json:"case_of,omitempty"`
	LinkedToSourceSystemCustomerCode string                 `protobuf:"bytes,7,opt,name=linked_to_source_system_customer_code,json=linkedToSourceSystemCustomerCode,proto3" json:"linked_to_source_system_customer_code,omitempty"`
	Relation                         string                 `protobuf:"bytes,8,opt,name=relation,proto3" json:"relation,omitempty"`
	ScreeningProfile                 string                 `protobuf:"bytes,9,opt,name=screening_profile,json=screeningProfile,proto3" json:"screening_profile,omitempty"`
	ScreeningProfileName             string                 `protobuf:"bytes,10,opt,name=screening_profile_name,json=screeningProfileName,proto3" json:"screening_profile_name,omitempty"`
	CustomerName                     string                 `protobuf:"bytes,11,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	CaseType                         CaseType               `protobuf:"varint,12,opt,name=case_type,json=caseType,proto3,enum=vendors.tss.CaseType" json:"case_type,omitempty"`
	InitialScreeningMode             InitialScreeningMode   `protobuf:"varint,13,opt,name=initial_screening_mode,json=initialScreeningMode,proto3,enum=vendors.tss.InitialScreeningMode" json:"initial_screening_mode,omitempty"`
	OnboardingDecision               OnboardingDecision     `protobuf:"varint,14,opt,name=onboarding_decision,json=onboardingDecision,proto3,enum=vendors.tss.OnboardingDecision" json:"onboarding_decision,omitempty"`
	TotalAlertCount                  int32                  `protobuf:"varint,15,opt,name=total_alert_count,json=totalAlertCount,proto3" json:"total_alert_count,omitempty"`
	ConfirmedAlertCount              int32                  `protobuf:"varint,16,opt,name=confirmed_alert_count,json=confirmedAlertCount,proto3" json:"confirmed_alert_count,omitempty"`
	ProbableAlertCount               int32                  `protobuf:"varint,17,opt,name=probable_alert_count,json=probableAlertCount,proto3" json:"probable_alert_count,omitempty"`
	PendingForDecision               int32                  `protobuf:"varint,18,opt,name=pending_for_decision,json=pendingForDecision,proto3" json:"pending_for_decision,omitempty"`
	NoMatchCount                     int32                  `protobuf:"varint,19,opt,name=no_match_count,json=noMatchCount,proto3" json:"no_match_count,omitempty"`
	TrueMatchCount                   int32                  `protobuf:"varint,20,opt,name=true_match_count,json=trueMatchCount,proto3" json:"true_match_count,omitempty"`
	CaseStage                        string                 `protobuf:"bytes,21,opt,name=case_stage,json=caseStage,proto3" json:"case_stage,omitempty"`
	CaseCategory                     CaseCategory           `protobuf:"varint,22,opt,name=case_category,json=caseCategory,proto3,enum=vendors.tss.CaseCategory" json:"case_category,omitempty"`
	CurrentAssignee                  string                 `protobuf:"bytes,23,opt,name=current_assignee,json=currentAssignee,proto3" json:"current_assignee,omitempty"`
	CaseClosureDateTimeInUtc         *timestamppb.Timestamp `protobuf:"bytes,24,opt,name=case_closure_date_time_in_utc,json=caseClosureDateTimeInUTC,proto3" json:"case_closure_date_time_in_utc,omitempty"`
	FinalRemarks                     string                 `protobuf:"bytes,25,opt,name=final_remarks,json=finalRemarks,proto3" json:"final_remarks,omitempty"`
	CaseActions                      []*CaseAction          `protobuf:"bytes,26,rep,name=case_actions,json=caseActions,proto3" json:"case_actions,omitempty"`
	AlertDetails                     []*AlertDetails        `protobuf:"bytes,27,rep,name=alert_details,json=alertDetails,proto3" json:"alert_details,omitempty"`
}

func (x *CaseDetails) Reset() {
	*x = CaseDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseDetails) ProtoMessage() {}

func (x *CaseDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseDetails.ProtoReflect.Descriptor instead.
func (*CaseDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{5}
}

func (x *CaseDetails) GetCaseId() string {
	if x != nil {
		return x.CaseId
	}
	return ""
}

func (x *CaseDetails) GetCaseCreationDateTimeInUtc() *timestamppb.Timestamp {
	if x != nil {
		return x.CaseCreationDateTimeInUtc
	}
	return nil
}

func (x *CaseDetails) GetSourceSystemName() string {
	if x != nil {
		return x.SourceSystemName
	}
	return ""
}

func (x *CaseDetails) GetSourceSystemCustomerCode() string {
	if x != nil {
		return x.SourceSystemCustomerCode
	}
	return ""
}

func (x *CaseDetails) GetApplicationRefNumber() string {
	if x != nil {
		return x.ApplicationRefNumber
	}
	return ""
}

func (x *CaseDetails) GetCaseOf() CaseOf {
	if x != nil {
		return x.CaseOf
	}
	return CaseOf_CASE_OF_UNSPECIFIED
}

func (x *CaseDetails) GetLinkedToSourceSystemCustomerCode() string {
	if x != nil {
		return x.LinkedToSourceSystemCustomerCode
	}
	return ""
}

func (x *CaseDetails) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

func (x *CaseDetails) GetScreeningProfile() string {
	if x != nil {
		return x.ScreeningProfile
	}
	return ""
}

func (x *CaseDetails) GetScreeningProfileName() string {
	if x != nil {
		return x.ScreeningProfileName
	}
	return ""
}

func (x *CaseDetails) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *CaseDetails) GetCaseType() CaseType {
	if x != nil {
		return x.CaseType
	}
	return CaseType_CASE_TYPE_UNSPECIFIED
}

func (x *CaseDetails) GetInitialScreeningMode() InitialScreeningMode {
	if x != nil {
		return x.InitialScreeningMode
	}
	return InitialScreeningMode_INITIAL_SCREENING_MODE_UNSPECIFIED
}

func (x *CaseDetails) GetOnboardingDecision() OnboardingDecision {
	if x != nil {
		return x.OnboardingDecision
	}
	return OnboardingDecision_ONBOARDING_DECISION_UNSPECIFIED
}

func (x *CaseDetails) GetTotalAlertCount() int32 {
	if x != nil {
		return x.TotalAlertCount
	}
	return 0
}

func (x *CaseDetails) GetConfirmedAlertCount() int32 {
	if x != nil {
		return x.ConfirmedAlertCount
	}
	return 0
}

func (x *CaseDetails) GetProbableAlertCount() int32 {
	if x != nil {
		return x.ProbableAlertCount
	}
	return 0
}

func (x *CaseDetails) GetPendingForDecision() int32 {
	if x != nil {
		return x.PendingForDecision
	}
	return 0
}

func (x *CaseDetails) GetNoMatchCount() int32 {
	if x != nil {
		return x.NoMatchCount
	}
	return 0
}

func (x *CaseDetails) GetTrueMatchCount() int32 {
	if x != nil {
		return x.TrueMatchCount
	}
	return 0
}

func (x *CaseDetails) GetCaseStage() string {
	if x != nil {
		return x.CaseStage
	}
	return ""
}

func (x *CaseDetails) GetCaseCategory() CaseCategory {
	if x != nil {
		return x.CaseCategory
	}
	return CaseCategory_CASE_CATEGORY_UNSPECIFIED
}

func (x *CaseDetails) GetCurrentAssignee() string {
	if x != nil {
		return x.CurrentAssignee
	}
	return ""
}

func (x *CaseDetails) GetCaseClosureDateTimeInUtc() *timestamppb.Timestamp {
	if x != nil {
		return x.CaseClosureDateTimeInUtc
	}
	return nil
}

func (x *CaseDetails) GetFinalRemarks() string {
	if x != nil {
		return x.FinalRemarks
	}
	return ""
}

func (x *CaseDetails) GetCaseActions() []*CaseAction {
	if x != nil {
		return x.CaseActions
	}
	return nil
}

func (x *CaseDetails) GetAlertDetails() []*AlertDetails {
	if x != nil {
		return x.AlertDetails
	}
	return nil
}

// Case action structure
type CaseAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName      string                 `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTimeInUtc *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date_time_in_utc,json=dateTimeInUTC,proto3" json:"date_time_in_utc,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
}

func (x *CaseAction) Reset() {
	*x = CaseAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CaseAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CaseAction) ProtoMessage() {}

func (x *CaseAction) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CaseAction.ProtoReflect.Descriptor instead.
func (*CaseAction) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{6}
}

func (x *CaseAction) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CaseAction) GetDateTimeInUtc() *timestamppb.Timestamp {
	if x != nil {
		return x.DateTimeInUtc
	}
	return nil
}

func (x *CaseAction) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

// Alert details structure
type AlertDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AlertId              string                  `protobuf:"bytes,1,opt,name=alert_id,json=alertId,proto3" json:"alert_id,omitempty"`
	Source               string                  `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	WatchlistSourceId    string                  `protobuf:"bytes,3,opt,name=watchlist_source_id,json=watchlistSourceId,proto3" json:"watchlist_source_id,omitempty"`
	MatchType            MatchType               `protobuf:"varint,4,opt,name=match_type,json=matchType,proto3,enum=vendors.tss.MatchType" json:"match_type,omitempty"`
	MatchingAttributes   []string                `protobuf:"bytes,5,rep,name=matching_attributes,json=matchingAttributes,proto3" json:"matching_attributes,omitempty"`
	SourceIdentification []*SourceIdentification `protobuf:"bytes,6,rep,name=source_identification,json=sourceIdentification,proto3" json:"source_identification,omitempty"`
	WatchlistName        string                  `protobuf:"bytes,7,opt,name=watchlist_name,json=watchlistName,proto3" json:"watchlist_name,omitempty"`
	AlertDecision        AlertDecision           `protobuf:"varint,8,opt,name=alert_decision,json=alertDecision,proto3,enum=vendors.tss.AlertDecision" json:"alert_decision,omitempty"`
	Comments             []*Comment              `protobuf:"bytes,9,rep,name=comments,proto3" json:"comments,omitempty"`
}

func (x *AlertDetails) Reset() {
	*x = AlertDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AlertDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlertDetails) ProtoMessage() {}

func (x *AlertDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlertDetails.ProtoReflect.Descriptor instead.
func (*AlertDetails) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{7}
}

func (x *AlertDetails) GetAlertId() string {
	if x != nil {
		return x.AlertId
	}
	return ""
}

func (x *AlertDetails) GetSource() string {
	if x != nil {
		return x.Source
	}
	return ""
}

func (x *AlertDetails) GetWatchlistSourceId() string {
	if x != nil {
		return x.WatchlistSourceId
	}
	return ""
}

func (x *AlertDetails) GetMatchType() MatchType {
	if x != nil {
		return x.MatchType
	}
	return MatchType_MATCH_TYPE_UNSPECIFIED
}

func (x *AlertDetails) GetMatchingAttributes() []string {
	if x != nil {
		return x.MatchingAttributes
	}
	return nil
}

func (x *AlertDetails) GetSourceIdentification() []*SourceIdentification {
	if x != nil {
		return x.SourceIdentification
	}
	return nil
}

func (x *AlertDetails) GetWatchlistName() string {
	if x != nil {
		return x.WatchlistName
	}
	return ""
}

func (x *AlertDetails) GetAlertDecision() AlertDecision {
	if x != nil {
		return x.AlertDecision
	}
	return AlertDecision_ALERT_DECISION_UNSPECIFIED
}

func (x *AlertDetails) GetComments() []*Comment {
	if x != nil {
		return x.Comments
	}
	return nil
}

// Source identification structure
type SourceIdentification struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SourceIdentificationId    string `protobuf:"bytes,1,opt,name=source_identification_id,json=sourceIdentificationID,proto3" json:"source_identification_id,omitempty"`
	SourceIdentificationKey   string `protobuf:"bytes,2,opt,name=source_identification_key,json=sourceIdentificationKey,proto3" json:"source_identification_key,omitempty"`
	SourceIdentificationValue string `protobuf:"bytes,3,opt,name=source_identification_value,json=sourceIdentificationValue,proto3" json:"source_identification_value,omitempty"`
}

func (x *SourceIdentification) Reset() {
	*x = SourceIdentification{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceIdentification) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceIdentification) ProtoMessage() {}

func (x *SourceIdentification) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceIdentification.ProtoReflect.Descriptor instead.
func (*SourceIdentification) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{8}
}

func (x *SourceIdentification) GetSourceIdentificationId() string {
	if x != nil {
		return x.SourceIdentificationId
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationKey() string {
	if x != nil {
		return x.SourceIdentificationKey
	}
	return ""
}

func (x *SourceIdentification) GetSourceIdentificationValue() string {
	if x != nil {
		return x.SourceIdentificationValue
	}
	return ""
}

// Comment structure
type Comment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName      string                 `protobuf:"bytes,1,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	DateTimeInUtc *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=date_time_in_utc,json=dateTimeInUTC,proto3" json:"date_time_in_utc,omitempty"`
	Comment       string                 `protobuf:"bytes,3,opt,name=comment,proto3" json:"comment,omitempty"`
}

func (x *Comment) Reset() {
	*x = Comment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendors_tss_as502_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Comment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Comment) ProtoMessage() {}

func (x *Comment) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendors_tss_as502_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Comment.ProtoReflect.Descriptor instead.
func (*Comment) Descriptor() ([]byte, []int) {
	return file_api_vendors_tss_as502_proto_rawDescGZIP(), []int{9}
}

func (x *Comment) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Comment) GetDateTimeInUtc() *timestamppb.Timestamp {
	if x != nil {
		return x.DateTimeInUtc
	}
	return nil
}

func (x *Comment) GetComment() string {
	if x != nil {
		return x.Comment
	}
	return ""
}

var File_api_vendors_tss_as502_proto protoreflect.FileDescriptor

var file_api_vendors_tss_as502_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73,
	0x73, 0x2f, 0x61, 0x73, 0x35, 0x30, 0x32, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x89, 0x01, 0x0a, 0x0c, 0x41, 0x53, 0x35, 0x30, 0x32, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x51, 0x0a,
	0x0f, 0x61, 0x70, 0x69, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x64, 0x74, 0x6f,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x41, 0x53, 0x35, 0x30, 0x32, 0x41, 0x70, 0x69, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x74, 0x6f, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10,
	0x01, 0x52, 0x0d, 0x61, 0x70, 0x69, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x44, 0x74, 0x6f,
	0x22, 0xde, 0x02, 0x0a, 0x12, 0x41, 0x53, 0x35, 0x30, 0x32, 0x41, 0x70, 0x69, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x44, 0x74, 0x6f, 0x12, 0x48, 0x0a, 0x0d, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73,
	0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72,
	0x79, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x4a, 0x0a, 0x0e, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0c, 0x66,
	0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x46, 0x0a, 0x0c, 0x74,
	0x6f, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0a, 0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x08, 0x01, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x22, 0xcb, 0x01, 0x0a, 0x0d, 0x41, 0x53, 0x35, 0x30, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x49, 0x64, 0x12, 0x45, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x41, 0x53, 0x35, 0x30, 0x32, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x48, 0x00, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x44, 0x61, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x0e, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e,
	0x41, 0x53, 0x35, 0x30, 0x32, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x00, 0x52, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x42, 0x0a, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x6f, 0x0a, 0x11, 0x41, 0x53, 0x35, 0x30, 0x32, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x61, 0x73, 0x65, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x22, 0x78, 0x0a, 0x12, 0x41, 0x53, 0x35, 0x30, 0x32, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x37, 0x0a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x16, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xc6, 0x0b, 0x0a, 0x0b, 0x43,
	0x61, 0x73, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x49, 0x64, 0x12, 0x5d, 0x0a, 0x1e, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69,
	0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x19, 0x63, 0x61, 0x73, 0x65, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55,
	0x54, 0x43, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x79, 0x73,
	0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x34, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x72,
	0x65, 0x66, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x07, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x6f, 0x66,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73,
	0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x4f, 0x66, 0x52, 0x06, 0x63, 0x61, 0x73,
	0x65, 0x4f, 0x66, 0x12, 0x4f, 0x0a, 0x25, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x5f, 0x74, 0x6f,
	0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x20, 0x6c, 0x69, 0x6e, 0x6b, 0x65, 0x64, 0x54, 0x6f, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72,
	0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x63, 0x72,
	0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x34, 0x0a,
	0x16, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x66, 0x69,
	0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73,
	0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x09, 0x63, 0x61, 0x73, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x08, 0x63, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x57, 0x0a, 0x16,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x49, 0x6e, 0x69, 0x74, 0x69,
	0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x14, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65, 0x6e, 0x69, 0x6e,
	0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x50, 0x0a, 0x13, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73,
	0x2e, 0x4f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x63, 0x69, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x12, 0x6f, 0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44,
	0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x11, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0f, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x41, 0x6c, 0x65,
	0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x72, 0x6f, 0x62, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x6c, 0x65, 0x41,
	0x6c, 0x65, 0x72, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x70, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x6f, 0x72, 0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x46, 0x6f, 0x72, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6e,
	0x6f, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x13, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x0c, 0x6e, 0x6f, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x28, 0x0a, 0x10, 0x74, 0x72, 0x75, 0x65, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x14, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e, 0x74, 0x72, 0x75,
	0x65, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x61, 0x73, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a, 0x0d, 0x63, 0x61,
	0x73, 0x65, 0x5f, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x19, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e,
	0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x0c, 0x63, 0x61,
	0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x65, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73,
	0x69, 0x67, 0x6e, 0x65, 0x65, 0x12, 0x5b, 0x0a, 0x1d, 0x63, 0x61, 0x73, 0x65, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x18, 0x63, 0x61, 0x73, 0x65, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55,
	0x54, 0x43, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x69, 0x6e, 0x61, 0x6c,
	0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x61, 0x73, 0x65, 0x5f,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x1a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x61, 0x73, 0x65,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x63, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x1b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0c, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x0a, 0x43, 0x61, 0x73, 0x65, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x43, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x69, 0x6e, 0x5f,
	0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x49,
	0x6e, 0x55, 0x54, 0x43, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcd, 0x03, 0x0a,
	0x0c, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x19, 0x0a,
	0x08, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x6c, 0x65, 0x72, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x77,
	0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64,
	0x12, 0x35, 0x0a, 0x0a, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x16, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74,
	0x73, 0x73, 0x2e, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x6d, 0x61, 0x74, 0x63, 0x68,
	0x69, 0x6e, 0x67, 0x5f, 0x61, 0x74, 0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x12, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x69, 0x6e, 0x67, 0x41, 0x74,
	0x74, 0x72, 0x69, 0x62, 0x75, 0x74, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x25, 0x0a, 0x0e, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x61, 0x74, 0x63, 0x68, 0x6c,
	0x69, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x41, 0x0a, 0x0e, 0x61, 0x6c, 0x65, 0x72, 0x74,
	0x5f, 0x64, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x41, 0x6c,
	0x65, 0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x61, 0x6c, 0x65,
	0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x08, 0x63, 0x6f,
	0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x08, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0xcc, 0x01, 0x0a,
	0x14, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x18, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x16, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x12,
	0x3a, 0x0a, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x17, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4b, 0x65, 0x79, 0x12, 0x3e, 0x0a, 0x1b, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x07,
	0x43, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x5f, 0x69, 0x6e, 0x5f, 0x75, 0x74, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x49, 0x6e, 0x55, 0x54, 0x43, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6d,
	0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6d, 0x6d,
	0x65, 0x6e, 0x74, 0x2a, 0x7d, 0x0a, 0x0c, 0x43, 0x61, 0x73, 0x65, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45,
	0x47, 0x4f, 0x52, 0x59, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47,
	0x4f, 0x52, 0x59, 0x5f, 0x4f, 0x50, 0x45, 0x4e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x41,
	0x53, 0x45, 0x5f, 0x43, 0x41, 0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x50, 0x45, 0x4e, 0x44,
	0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x43, 0x41,
	0x54, 0x45, 0x47, 0x4f, 0x52, 0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44,
	0x10, 0x03, 0x2a, 0x5b, 0x0a, 0x08, 0x43, 0x61, 0x73, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x41, 0x53,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x01,
	0x12, 0x1d, 0x0a, 0x19, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x57, 0x41,
	0x54, 0x43, 0x48, 0x4c, 0x49, 0x53, 0x54, 0x5f, 0x41, 0x44, 0x44, 0x45, 0x44, 0x10, 0x02, 0x2a,
	0x89, 0x01, 0x0a, 0x14, 0x49, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x63, 0x72, 0x65, 0x65,
	0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x26, 0x0a, 0x22, 0x49, 0x4e, 0x49, 0x54,
	0x49, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45, 0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f,
	0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1e, 0x0a, 0x1a, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x41, 0x50, 0x49, 0x10, 0x01,
	0x12, 0x29, 0x0a, 0x25, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x5f, 0x53, 0x43, 0x52, 0x45,
	0x45, 0x4e, 0x49, 0x4e, 0x47, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x4c, 0x4f, 0x4f, 0x4b, 0x55,
	0x50, 0x5f, 0x49, 0x4e, 0x5f, 0x42, 0x55, 0x4c, 0x4b, 0x10, 0x02, 0x2a, 0x7b, 0x0a, 0x12, 0x4f,
	0x6e, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x23, 0x0a, 0x1f, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f,
	0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x4e, 0x42, 0x4f, 0x41, 0x52,
	0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x52,
	0x4f, 0x43, 0x45, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1f, 0x0a, 0x1b, 0x4f, 0x4e, 0x42, 0x4f, 0x41,
	0x52, 0x44, 0x49, 0x4e, 0x47, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x44,
	0x45, 0x43, 0x4c, 0x49, 0x4e, 0x45, 0x10, 0x02, 0x2a, 0x57, 0x0a, 0x06, 0x43, 0x61, 0x73, 0x65,
	0x4f, 0x66, 0x12, 0x17, 0x0a, 0x13, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x43,
	0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x4d, 0x41, 0x49, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x10,
	0x01, 0x12, 0x1e, 0x0a, 0x1a, 0x43, 0x41, 0x53, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x52, 0x45, 0x4c,
	0x41, 0x54, 0x45, 0x44, 0x5f, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x5f, 0x4b, 0x59, 0x43, 0x10,
	0x02, 0x2a, 0x5a, 0x0a, 0x09, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a,
	0x0a, 0x16, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x4d, 0x41,
	0x54, 0x43, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4f, 0x4e, 0x46, 0x49, 0x52, 0x4d,
	0x45, 0x44, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x50, 0x52, 0x4f, 0x42, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x2a, 0x87, 0x01,
	0x0a, 0x0d, 0x41, 0x6c, 0x65, 0x72, 0x74, 0x44, 0x65, 0x63, 0x69, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x1e, 0x0a, 0x1a, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x1d, 0x0a, 0x19, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f,
	0x4e, 0x5f, 0x54, 0x52, 0x55, 0x45, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x01, 0x12, 0x1b,
	0x0a, 0x17, 0x41, 0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e,
	0x5f, 0x4e, 0x4f, 0x5f, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x02, 0x12, 0x1a, 0x0a, 0x16, 0x41,
	0x4c, 0x45, 0x52, 0x54, 0x5f, 0x44, 0x45, 0x43, 0x49, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x42, 0x50, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d,
	0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2e, 0x74, 0x73,
	0x73, 0x5a, 0x26, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70,
	0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x74, 0x73, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_vendors_tss_as502_proto_rawDescOnce sync.Once
	file_api_vendors_tss_as502_proto_rawDescData = file_api_vendors_tss_as502_proto_rawDesc
)

func file_api_vendors_tss_as502_proto_rawDescGZIP() []byte {
	file_api_vendors_tss_as502_proto_rawDescOnce.Do(func() {
		file_api_vendors_tss_as502_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendors_tss_as502_proto_rawDescData)
	})
	return file_api_vendors_tss_as502_proto_rawDescData
}

var file_api_vendors_tss_as502_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_api_vendors_tss_as502_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_api_vendors_tss_as502_proto_goTypes = []interface{}{
	(CaseCategory)(0),             // 0: vendors.tss.CaseCategory
	(CaseType)(0),                 // 1: vendors.tss.CaseType
	(InitialScreeningMode)(0),     // 2: vendors.tss.InitialScreeningMode
	(OnboardingDecision)(0),       // 3: vendors.tss.OnboardingDecision
	(CaseOf)(0),                   // 4: vendors.tss.CaseOf
	(MatchType)(0),                // 5: vendors.tss.MatchType
	(AlertDecision)(0),            // 6: vendors.tss.AlertDecision
	(*AS502Request)(nil),          // 7: vendors.tss.AS502Request
	(*AS502ApiRequestDto)(nil),    // 8: vendors.tss.AS502ApiRequestDto
	(*AS502Response)(nil),         // 9: vendors.tss.AS502Response
	(*AS502ResponseData)(nil),     // 10: vendors.tss.AS502ResponseData
	(*AS502ErrorResponse)(nil),    // 11: vendors.tss.AS502ErrorResponse
	(*CaseDetails)(nil),           // 12: vendors.tss.CaseDetails
	(*CaseAction)(nil),            // 13: vendors.tss.CaseAction
	(*AlertDetails)(nil),          // 14: vendors.tss.AlertDetails
	(*SourceIdentification)(nil),  // 15: vendors.tss.SourceIdentification
	(*Comment)(nil),               // 16: vendors.tss.Comment
	(*timestamppb.Timestamp)(nil), // 17: google.protobuf.Timestamp
}
var file_api_vendors_tss_as502_proto_depIdxs = []int32{
	8,  // 0: vendors.tss.AS502Request.api_request_dto:type_name -> vendors.tss.AS502ApiRequestDto
	0,  // 1: vendors.tss.AS502ApiRequestDto.case_category:type_name -> vendors.tss.CaseCategory
	17, // 2: vendors.tss.AS502ApiRequestDto.from_date_time:type_name -> google.protobuf.Timestamp
	17, // 3: vendors.tss.AS502ApiRequestDto.to_date_time:type_name -> google.protobuf.Timestamp
	1,  // 4: vendors.tss.AS502ApiRequestDto.case_type:type_name -> vendors.tss.CaseType
	10, // 5: vendors.tss.AS502Response.response_data:type_name -> vendors.tss.AS502ResponseData
	11, // 6: vendors.tss.AS502Response.error_response:type_name -> vendors.tss.AS502ErrorResponse
	12, // 7: vendors.tss.AS502ResponseData.case_details:type_name -> vendors.tss.CaseDetails
	17, // 8: vendors.tss.CaseDetails.case_creation_date_time_in_utc:type_name -> google.protobuf.Timestamp
	4,  // 9: vendors.tss.CaseDetails.case_of:type_name -> vendors.tss.CaseOf
	1,  // 10: vendors.tss.CaseDetails.case_type:type_name -> vendors.tss.CaseType
	2,  // 11: vendors.tss.CaseDetails.initial_screening_mode:type_name -> vendors.tss.InitialScreeningMode
	3,  // 12: vendors.tss.CaseDetails.onboarding_decision:type_name -> vendors.tss.OnboardingDecision
	0,  // 13: vendors.tss.CaseDetails.case_category:type_name -> vendors.tss.CaseCategory
	17, // 14: vendors.tss.CaseDetails.case_closure_date_time_in_utc:type_name -> google.protobuf.Timestamp
	13, // 15: vendors.tss.CaseDetails.case_actions:type_name -> vendors.tss.CaseAction
	14, // 16: vendors.tss.CaseDetails.alert_details:type_name -> vendors.tss.AlertDetails
	17, // 17: vendors.tss.CaseAction.date_time_in_utc:type_name -> google.protobuf.Timestamp
	5,  // 18: vendors.tss.AlertDetails.match_type:type_name -> vendors.tss.MatchType
	15, // 19: vendors.tss.AlertDetails.source_identification:type_name -> vendors.tss.SourceIdentification
	6,  // 20: vendors.tss.AlertDetails.alert_decision:type_name -> vendors.tss.AlertDecision
	16, // 21: vendors.tss.AlertDetails.comments:type_name -> vendors.tss.Comment
	17, // 22: vendors.tss.Comment.date_time_in_utc:type_name -> google.protobuf.Timestamp
	23, // [23:23] is the sub-list for method output_type
	23, // [23:23] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_api_vendors_tss_as502_proto_init() }
func file_api_vendors_tss_as502_proto_init() {
	if File_api_vendors_tss_as502_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendors_tss_as502_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS502Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS502ApiRequestDto); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS502Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS502ResponseData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AS502ErrorResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CaseAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AlertDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceIdentification); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendors_tss_as502_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Comment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_vendors_tss_as502_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*AS502Response_ResponseData)(nil),
		(*AS502Response_ErrorResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendors_tss_as502_proto_rawDesc,
			NumEnums:      7,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendors_tss_as502_proto_goTypes,
		DependencyIndexes: file_api_vendors_tss_as502_proto_depIdxs,
		EnumInfos:         file_api_vendors_tss_as502_proto_enumTypes,
		MessageInfos:      file_api_vendors_tss_as502_proto_msgTypes,
	}.Build()
	File_api_vendors_tss_as502_proto = out.File
	file_api_vendors_tss_as502_proto_rawDesc = nil
	file_api_vendors_tss_as502_proto_goTypes = nil
	file_api_vendors_tss_as502_proto_depIdxs = nil
}
