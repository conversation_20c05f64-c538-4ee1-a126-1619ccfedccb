// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/vendors/tss/as503.proto

package tss

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on AS503Request with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AS503Request) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503Request with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AS503RequestMultiError, or
// nil if none found.
func (m *AS503Request) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503Request) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetRequestId()) < 1 {
		err := AS503RequestValidationError{
			field:  "RequestId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetApiRequestDto() == nil {
		err := AS503RequestValidationError{
			field:  "ApiRequestDto",
			reason: "value is required",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetApiRequestDto()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503RequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503RequestValidationError{
					field:  "ApiRequestDto",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetApiRequestDto()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503RequestValidationError{
				field:  "ApiRequestDto",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AS503RequestMultiError(errors)
	}

	return nil
}

// AS503RequestMultiError is an error wrapping multiple validation errors
// returned by AS503Request.ValidateAll() if the designated constraints aren't met.
type AS503RequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503RequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503RequestMultiError) AllErrors() []error { return m }

// AS503RequestValidationError is the validation error returned by
// AS503Request.Validate if the designated constraints aren't met.
type AS503RequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503RequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503RequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503RequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503RequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503RequestValidationError) ErrorName() string { return "AS503RequestValidationError" }

// Error satisfies the builtin error interface
func (e AS503RequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503Request.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503RequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503RequestValidationError{}

// Validate checks the field values on AS503ApiRequestDto with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AS503ApiRequestDto) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503ApiRequestDto with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503ApiRequestDtoMultiError, or nil if none found.
func (m *AS503ApiRequestDto) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503ApiRequestDto) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetCaseId()) < 1 {
		err := AS503ApiRequestDtoValidationError{
			field:  "CaseId",
			reason: "value length must be at least 1 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return AS503ApiRequestDtoMultiError(errors)
	}

	return nil
}

// AS503ApiRequestDtoMultiError is an error wrapping multiple validation errors
// returned by AS503ApiRequestDto.ValidateAll() if the designated constraints
// aren't met.
type AS503ApiRequestDtoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503ApiRequestDtoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503ApiRequestDtoMultiError) AllErrors() []error { return m }

// AS503ApiRequestDtoValidationError is the validation error returned by
// AS503ApiRequestDto.Validate if the designated constraints aren't met.
type AS503ApiRequestDtoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503ApiRequestDtoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503ApiRequestDtoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503ApiRequestDtoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503ApiRequestDtoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503ApiRequestDtoValidationError) ErrorName() string {
	return "AS503ApiRequestDtoValidationError"
}

// Error satisfies the builtin error interface
func (e AS503ApiRequestDtoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503ApiRequestDto.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503ApiRequestDtoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503ApiRequestDtoValidationError{}

// Validate checks the field values on AS503Response with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AS503Response) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503Response with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AS503ResponseMultiError, or
// nil if none found.
func (m *AS503Response) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503Response) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RequestId

	switch v := m.Response.(type) {
	case *AS503Response_ResponseData:
		if v == nil {
			err := AS503ResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetResponseData()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503ResponseValidationError{
						field:  "ResponseData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503ResponseValidationError{
						field:  "ResponseData",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetResponseData()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503ResponseValidationError{
					field:  "ResponseData",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *AS503Response_ErrorResponse:
		if v == nil {
			err := AS503ResponseValidationError{
				field:  "Response",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetErrorResponse()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503ResponseValidationError{
						field:  "ErrorResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503ResponseValidationError{
						field:  "ErrorResponse",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetErrorResponse()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503ResponseValidationError{
					field:  "ErrorResponse",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return AS503ResponseMultiError(errors)
	}

	return nil
}

// AS503ResponseMultiError is an error wrapping multiple validation errors
// returned by AS503Response.ValidateAll() if the designated constraints
// aren't met.
type AS503ResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503ResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503ResponseMultiError) AllErrors() []error { return m }

// AS503ResponseValidationError is the validation error returned by
// AS503Response.Validate if the designated constraints aren't met.
type AS503ResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503ResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503ResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503ResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503ResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503ResponseValidationError) ErrorName() string { return "AS503ResponseValidationError" }

// Error satisfies the builtin error interface
func (e AS503ResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503Response.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503ResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503ResponseValidationError{}

// Validate checks the field values on AS503ResponseData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AS503ResponseData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503ResponseData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503ResponseDataMultiError, or nil if none found.
func (m *AS503ResponseData) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503ResponseData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCaseDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503ResponseDataValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503ResponseDataValidationError{
					field:  "CaseDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503ResponseDataValidationError{
				field:  "CaseDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return AS503ResponseDataMultiError(errors)
	}

	return nil
}

// AS503ResponseDataMultiError is an error wrapping multiple validation errors
// returned by AS503ResponseData.ValidateAll() if the designated constraints
// aren't met.
type AS503ResponseDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503ResponseDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503ResponseDataMultiError) AllErrors() []error { return m }

// AS503ResponseDataValidationError is the validation error returned by
// AS503ResponseData.Validate if the designated constraints aren't met.
type AS503ResponseDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503ResponseDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503ResponseDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503ResponseDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503ResponseDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503ResponseDataValidationError) ErrorName() string {
	return "AS503ResponseDataValidationError"
}

// Error satisfies the builtin error interface
func (e AS503ResponseDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503ResponseData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503ResponseDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503ResponseDataValidationError{}

// Validate checks the field values on AS503ErrorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AS503ErrorResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503ErrorResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503ErrorResponseMultiError, or nil if none found.
func (m *AS503ErrorResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503ErrorResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return AS503ErrorResponseMultiError(errors)
	}

	return nil
}

// AS503ErrorResponseMultiError is an error wrapping multiple validation errors
// returned by AS503ErrorResponse.ValidateAll() if the designated constraints
// aren't met.
type AS503ErrorResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503ErrorResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503ErrorResponseMultiError) AllErrors() []error { return m }

// AS503ErrorResponseValidationError is the validation error returned by
// AS503ErrorResponse.Validate if the designated constraints aren't met.
type AS503ErrorResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503ErrorResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503ErrorResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503ErrorResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503ErrorResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503ErrorResponseValidationError) ErrorName() string {
	return "AS503ErrorResponseValidationError"
}

// Error satisfies the builtin error interface
func (e AS503ErrorResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503ErrorResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503ErrorResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503ErrorResponseValidationError{}

// Validate checks the field values on AS503CaseDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AS503CaseDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503CaseDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503CaseDetailsMultiError, or nil if none found.
func (m *AS503CaseDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503CaseDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CaseId

	if all {
		switch v := interface{}(m.GetCaseCreationDateTimeInUtc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503CaseDetailsValidationError{
					field:  "CaseCreationDateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503CaseDetailsValidationError{
					field:  "CaseCreationDateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseCreationDateTimeInUtc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503CaseDetailsValidationError{
				field:  "CaseCreationDateTimeInUtc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SourceSystemName

	// no validation rules for SourceSystemCustomerCode

	// no validation rules for ApplicationRefNumber

	// no validation rules for CaseOf

	// no validation rules for LinkedToSourceSystemCustomerCode

	// no validation rules for Relation

	// no validation rules for ScreeningProfile

	// no validation rules for ScreeningProfileName

	// no validation rules for CustomerName

	// no validation rules for CaseType

	// no validation rules for InitialScreeningMode

	// no validation rules for OnboardingDecision

	// no validation rules for TotalAlertCount

	// no validation rules for ConfirmedAlertCount

	// no validation rules for ProbableAlertCount

	// no validation rules for PendingForDecision

	// no validation rules for NoMatchCount

	// no validation rules for TrueMatchCount

	// no validation rules for CaseStage

	// no validation rules for CaseCategory

	// no validation rules for CurrentAssignee

	if all {
		switch v := interface{}(m.GetCaseClosureDateTimeInUtc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503CaseDetailsValidationError{
					field:  "CaseClosureDateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503CaseDetailsValidationError{
					field:  "CaseClosureDateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCaseClosureDateTimeInUtc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503CaseDetailsValidationError{
				field:  "CaseClosureDateTimeInUtc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FinalRemarks

	for idx, item := range m.GetCaseActions() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503CaseDetailsValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503CaseDetailsValidationError{
						field:  fmt.Sprintf("CaseActions[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503CaseDetailsValidationError{
					field:  fmt.Sprintf("CaseActions[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for ReportDataInBase64String

	for idx, item := range m.GetAlertDetails() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503CaseDetailsValidationError{
						field:  fmt.Sprintf("AlertDetails[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503CaseDetailsValidationError{
					field:  fmt.Sprintf("AlertDetails[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AS503CaseDetailsMultiError(errors)
	}

	return nil
}

// AS503CaseDetailsMultiError is an error wrapping multiple validation errors
// returned by AS503CaseDetails.ValidateAll() if the designated constraints
// aren't met.
type AS503CaseDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503CaseDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503CaseDetailsMultiError) AllErrors() []error { return m }

// AS503CaseDetailsValidationError is the validation error returned by
// AS503CaseDetails.Validate if the designated constraints aren't met.
type AS503CaseDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503CaseDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503CaseDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503CaseDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503CaseDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503CaseDetailsValidationError) ErrorName() string { return "AS503CaseDetailsValidationError" }

// Error satisfies the builtin error interface
func (e AS503CaseDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503CaseDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503CaseDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503CaseDetailsValidationError{}

// Validate checks the field values on AS503CaseAction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AS503CaseAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503CaseAction with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503CaseActionMultiError, or nil if none found.
func (m *AS503CaseAction) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503CaseAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserName

	if all {
		switch v := interface{}(m.GetDateTimeInUtc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503CaseActionValidationError{
					field:  "DateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503CaseActionValidationError{
					field:  "DateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateTimeInUtc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503CaseActionValidationError{
				field:  "DateTimeInUtc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Action

	if len(errors) > 0 {
		return AS503CaseActionMultiError(errors)
	}

	return nil
}

// AS503CaseActionMultiError is an error wrapping multiple validation errors
// returned by AS503CaseAction.ValidateAll() if the designated constraints
// aren't met.
type AS503CaseActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503CaseActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503CaseActionMultiError) AllErrors() []error { return m }

// AS503CaseActionValidationError is the validation error returned by
// AS503CaseAction.Validate if the designated constraints aren't met.
type AS503CaseActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503CaseActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503CaseActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503CaseActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503CaseActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503CaseActionValidationError) ErrorName() string { return "AS503CaseActionValidationError" }

// Error satisfies the builtin error interface
func (e AS503CaseActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503CaseAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503CaseActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503CaseActionValidationError{}

// Validate checks the field values on AS503AlertDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *AS503AlertDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503AlertDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503AlertDetailsMultiError, or nil if none found.
func (m *AS503AlertDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503AlertDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AlertId

	// no validation rules for Source

	// no validation rules for WatchlistSourceId

	// no validation rules for MatchType

	for idx, item := range m.GetSourceIdentification() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503AlertDetailsValidationError{
						field:  fmt.Sprintf("SourceIdentification[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503AlertDetailsValidationError{
						field:  fmt.Sprintf("SourceIdentification[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503AlertDetailsValidationError{
					field:  fmt.Sprintf("SourceIdentification[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for WatchlistName

	// no validation rules for AlertDecision

	for idx, item := range m.GetComments() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, AS503AlertDetailsValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, AS503AlertDetailsValidationError{
						field:  fmt.Sprintf("Comments[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return AS503AlertDetailsValidationError{
					field:  fmt.Sprintf("Comments[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return AS503AlertDetailsMultiError(errors)
	}

	return nil
}

// AS503AlertDetailsMultiError is an error wrapping multiple validation errors
// returned by AS503AlertDetails.ValidateAll() if the designated constraints
// aren't met.
type AS503AlertDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503AlertDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503AlertDetailsMultiError) AllErrors() []error { return m }

// AS503AlertDetailsValidationError is the validation error returned by
// AS503AlertDetails.Validate if the designated constraints aren't met.
type AS503AlertDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503AlertDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503AlertDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503AlertDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503AlertDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503AlertDetailsValidationError) ErrorName() string {
	return "AS503AlertDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e AS503AlertDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503AlertDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503AlertDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503AlertDetailsValidationError{}

// Validate checks the field values on AS503SourceIdentification with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *AS503SourceIdentification) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503SourceIdentification with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// AS503SourceIdentificationMultiError, or nil if none found.
func (m *AS503SourceIdentification) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503SourceIdentification) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SourceIdentificationId

	// no validation rules for SourceIdentificationKey

	// no validation rules for SourceIdentificationValue

	if len(errors) > 0 {
		return AS503SourceIdentificationMultiError(errors)
	}

	return nil
}

// AS503SourceIdentificationMultiError is an error wrapping multiple validation
// errors returned by AS503SourceIdentification.ValidateAll() if the
// designated constraints aren't met.
type AS503SourceIdentificationMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503SourceIdentificationMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503SourceIdentificationMultiError) AllErrors() []error { return m }

// AS503SourceIdentificationValidationError is the validation error returned by
// AS503SourceIdentification.Validate if the designated constraints aren't met.
type AS503SourceIdentificationValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503SourceIdentificationValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503SourceIdentificationValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503SourceIdentificationValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503SourceIdentificationValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503SourceIdentificationValidationError) ErrorName() string {
	return "AS503SourceIdentificationValidationError"
}

// Error satisfies the builtin error interface
func (e AS503SourceIdentificationValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503SourceIdentification.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503SourceIdentificationValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503SourceIdentificationValidationError{}

// Validate checks the field values on AS503Comment with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *AS503Comment) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on AS503Comment with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in AS503CommentMultiError, or
// nil if none found.
func (m *AS503Comment) ValidateAll() error {
	return m.validate(true)
}

func (m *AS503Comment) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserName

	if all {
		switch v := interface{}(m.GetDateTimeInUtc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, AS503CommentValidationError{
					field:  "DateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, AS503CommentValidationError{
					field:  "DateTimeInUtc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDateTimeInUtc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return AS503CommentValidationError{
				field:  "DateTimeInUtc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Comment

	if len(errors) > 0 {
		return AS503CommentMultiError(errors)
	}

	return nil
}

// AS503CommentMultiError is an error wrapping multiple validation errors
// returned by AS503Comment.ValidateAll() if the designated constraints aren't met.
type AS503CommentMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m AS503CommentMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m AS503CommentMultiError) AllErrors() []error { return m }

// AS503CommentValidationError is the validation error returned by
// AS503Comment.Validate if the designated constraints aren't met.
type AS503CommentValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e AS503CommentValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e AS503CommentValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e AS503CommentValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e AS503CommentValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e AS503CommentValidationError) ErrorName() string { return "AS503CommentValidationError" }

// Error satisfies the builtin error interface
func (e AS503CommentValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sAS503Comment.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = AS503CommentValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = AS503CommentValidationError{}
