// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/frontend/account/sa_closure/service.proto

package sa_closure

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"

	enums "github.com/epifi/gamma/api/frontend/account/enums"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort

	_ = enums.SAClosureRequestEntryPoint(0)
)

// Validate checks the field values on GetSaClosureFlowRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSaClosureFlowRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureFlowRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSaClosureFlowRequestMultiError, or nil if none found.
func (m *GetSaClosureFlowRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureFlowRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureFlowRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureFlowRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureFlowRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EntryPoint

	if len(errors) > 0 {
		return GetSaClosureFlowRequestMultiError(errors)
	}

	return nil
}

// GetSaClosureFlowRequestMultiError is an error wrapping multiple validation
// errors returned by GetSaClosureFlowRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSaClosureFlowRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureFlowRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureFlowRequestMultiError) AllErrors() []error { return m }

// GetSaClosureFlowRequestValidationError is the validation error returned by
// GetSaClosureFlowRequest.Validate if the designated constraints aren't met.
type GetSaClosureFlowRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureFlowRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureFlowRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureFlowRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureFlowRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureFlowRequestValidationError) ErrorName() string {
	return "GetSaClosureFlowRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureFlowRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureFlowRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureFlowRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureFlowRequestValidationError{}

// Validate checks the field values on GetSaClosureFlowResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSaClosureFlowResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureFlowResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSaClosureFlowResponseMultiError, or nil if none found.
func (m *GetSaClosureFlowResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureFlowResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureFlowResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureFlowResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureFlowResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureFlowResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureFlowResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSaClosureFlowResponseMultiError(errors)
	}

	return nil
}

// GetSaClosureFlowResponseMultiError is an error wrapping multiple validation
// errors returned by GetSaClosureFlowResponse.ValidateAll() if the designated
// constraints aren't met.
type GetSaClosureFlowResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureFlowResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureFlowResponseMultiError) AllErrors() []error { return m }

// GetSaClosureFlowResponseValidationError is the validation error returned by
// GetSaClosureFlowResponse.Validate if the designated constraints aren't met.
type GetSaClosureFlowResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureFlowResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureFlowResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureFlowResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureFlowResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureFlowResponseValidationError) ErrorName() string {
	return "GetSaClosureFlowResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureFlowResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureFlowResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureFlowResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureFlowResponseValidationError{}

// Validate checks the field values on GetSaClosureBenefitsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSaClosureBenefitsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureBenefitsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSaClosureBenefitsRequestMultiError, or nil if none found.
func (m *GetSaClosureBenefitsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureBenefitsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSaClosureBenefitsRequestMultiError(errors)
	}

	return nil
}

// GetSaClosureBenefitsRequestMultiError is an error wrapping multiple
// validation errors returned by GetSaClosureBenefitsRequest.ValidateAll() if
// the designated constraints aren't met.
type GetSaClosureBenefitsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureBenefitsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureBenefitsRequestMultiError) AllErrors() []error { return m }

// GetSaClosureBenefitsRequestValidationError is the validation error returned
// by GetSaClosureBenefitsRequest.Validate if the designated constraints
// aren't met.
type GetSaClosureBenefitsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureBenefitsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureBenefitsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureBenefitsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureBenefitsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureBenefitsRequestValidationError) ErrorName() string {
	return "GetSaClosureBenefitsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureBenefitsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureBenefitsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureBenefitsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureBenefitsRequestValidationError{}

// Validate checks the field values on GetSaClosureBenefitsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSaClosureBenefitsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSaClosureBenefitsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSaClosureBenefitsResponseMultiError, or nil if none found.
func (m *GetSaClosureBenefitsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSaClosureBenefitsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsResponseValidationError{
				field:  "PageTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetBenefitsSectionHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "BenefitsSectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "BenefitsSectionHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBenefitsSectionHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsResponseValidationError{
				field:  "BenefitsSectionHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetBenefitItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetSaClosureBenefitsResponseValidationError{
						field:  fmt.Sprintf("BenefitItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetSaClosureBenefitsResponseValidationError{
						field:  fmt.Sprintf("BenefitItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetSaClosureBenefitsResponseValidationError{
					field:  fmt.Sprintf("BenefitItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetProceedCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProceedCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsResponseValidationError{
				field:  "ProceedCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCancelCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "CancelCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetSaClosureBenefitsResponseValidationError{
					field:  "CancelCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCancelCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetSaClosureBenefitsResponseValidationError{
				field:  "CancelCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return GetSaClosureBenefitsResponseMultiError(errors)
	}

	return nil
}

// GetSaClosureBenefitsResponseMultiError is an error wrapping multiple
// validation errors returned by GetSaClosureBenefitsResponse.ValidateAll() if
// the designated constraints aren't met.
type GetSaClosureBenefitsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSaClosureBenefitsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSaClosureBenefitsResponseMultiError) AllErrors() []error { return m }

// GetSaClosureBenefitsResponseValidationError is the validation error returned
// by GetSaClosureBenefitsResponse.Validate if the designated constraints
// aren't met.
type GetSaClosureBenefitsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSaClosureBenefitsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSaClosureBenefitsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSaClosureBenefitsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSaClosureBenefitsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSaClosureBenefitsResponseValidationError) ErrorName() string {
	return "GetSaClosureBenefitsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e GetSaClosureBenefitsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSaClosureBenefitsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSaClosureBenefitsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSaClosureBenefitsResponseValidationError{}

// Validate checks the field values on SubmitSaClosureUserFeedbackRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SubmitSaClosureUserFeedbackRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitSaClosureUserFeedbackRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SubmitSaClosureUserFeedbackRequestMultiError, or nil if none found.
func (m *SubmitSaClosureUserFeedbackRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitSaClosureUserFeedbackRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitSaClosureUserFeedbackRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for FeedbackText

	// no validation rules for CreateSupportTicket

	if len(errors) > 0 {
		return SubmitSaClosureUserFeedbackRequestMultiError(errors)
	}

	return nil
}

// SubmitSaClosureUserFeedbackRequestMultiError is an error wrapping multiple
// validation errors returned by
// SubmitSaClosureUserFeedbackRequest.ValidateAll() if the designated
// constraints aren't met.
type SubmitSaClosureUserFeedbackRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitSaClosureUserFeedbackRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitSaClosureUserFeedbackRequestMultiError) AllErrors() []error { return m }

// SubmitSaClosureUserFeedbackRequestValidationError is the validation error
// returned by SubmitSaClosureUserFeedbackRequest.Validate if the designated
// constraints aren't met.
type SubmitSaClosureUserFeedbackRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitSaClosureUserFeedbackRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitSaClosureUserFeedbackRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitSaClosureUserFeedbackRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitSaClosureUserFeedbackRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitSaClosureUserFeedbackRequestValidationError) ErrorName() string {
	return "SubmitSaClosureUserFeedbackRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitSaClosureUserFeedbackRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitSaClosureUserFeedbackRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitSaClosureUserFeedbackRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitSaClosureUserFeedbackRequestValidationError{}

// Validate checks the field values on SubmitSaClosureUserFeedbackResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *SubmitSaClosureUserFeedbackResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitSaClosureUserFeedbackResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// SubmitSaClosureUserFeedbackResponseMultiError, or nil if none found.
func (m *SubmitSaClosureUserFeedbackResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitSaClosureUserFeedbackResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitSaClosureUserFeedbackResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitSaClosureUserFeedbackResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitSaClosureUserFeedbackResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitSaClosureUserFeedbackResponseMultiError(errors)
	}

	return nil
}

// SubmitSaClosureUserFeedbackResponseMultiError is an error wrapping multiple
// validation errors returned by
// SubmitSaClosureUserFeedbackResponse.ValidateAll() if the designated
// constraints aren't met.
type SubmitSaClosureUserFeedbackResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitSaClosureUserFeedbackResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitSaClosureUserFeedbackResponseMultiError) AllErrors() []error { return m }

// SubmitSaClosureUserFeedbackResponseValidationError is the validation error
// returned by SubmitSaClosureUserFeedbackResponse.Validate if the designated
// constraints aren't met.
type SubmitSaClosureUserFeedbackResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitSaClosureUserFeedbackResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitSaClosureUserFeedbackResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitSaClosureUserFeedbackResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitSaClosureUserFeedbackResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitSaClosureUserFeedbackResponseValidationError) ErrorName() string {
	return "SubmitSaClosureUserFeedbackResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitSaClosureUserFeedbackResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitSaClosureUserFeedbackResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitSaClosureUserFeedbackResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitSaClosureUserFeedbackResponseValidationError{}

// Validate checks the field values on FetchSaClosureCriteriasForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchSaClosureCriteriasForUserRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FetchSaClosureCriteriasForUserRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// FetchSaClosureCriteriasForUserRequestMultiError, or nil if none found.
func (m *FetchSaClosureCriteriasForUserRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchSaClosureCriteriasForUserRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchSaClosureCriteriasForUserRequestMultiError(errors)
	}

	return nil
}

// FetchSaClosureCriteriasForUserRequestMultiError is an error wrapping
// multiple validation errors returned by
// FetchSaClosureCriteriasForUserRequest.ValidateAll() if the designated
// constraints aren't met.
type FetchSaClosureCriteriasForUserRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchSaClosureCriteriasForUserRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchSaClosureCriteriasForUserRequestMultiError) AllErrors() []error { return m }

// FetchSaClosureCriteriasForUserRequestValidationError is the validation error
// returned by FetchSaClosureCriteriasForUserRequest.Validate if the
// designated constraints aren't met.
type FetchSaClosureCriteriasForUserRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchSaClosureCriteriasForUserRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchSaClosureCriteriasForUserRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchSaClosureCriteriasForUserRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchSaClosureCriteriasForUserRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchSaClosureCriteriasForUserRequestValidationError) ErrorName() string {
	return "FetchSaClosureCriteriasForUserRequestValidationError"
}

// Error satisfies the builtin error interface
func (e FetchSaClosureCriteriasForUserRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchSaClosureCriteriasForUserRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchSaClosureCriteriasForUserRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchSaClosureCriteriasForUserRequestValidationError{}

// Validate checks the field values on FetchSaClosureCriteriasForUserResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *FetchSaClosureCriteriasForUserResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// FetchSaClosureCriteriasForUserResponse with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// FetchSaClosureCriteriasForUserResponseMultiError, or nil if none found.
func (m *FetchSaClosureCriteriasForUserResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *FetchSaClosureCriteriasForUserResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageTitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "PageTitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageTitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserResponseValidationError{
				field:  "PageTitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetPageSubtitle()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "PageSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "PageSubtitle",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPageSubtitle()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserResponseValidationError{
				field:  "PageSubtitle",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCriteriaGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
						field:  fmt.Sprintf("CriteriaGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
						field:  fmt.Sprintf("CriteriaGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return FetchSaClosureCriteriasForUserResponseValidationError{
					field:  fmt.Sprintf("CriteriaGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetProceedCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "ProceedCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProceedCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserResponseValidationError{
				field:  "ProceedCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisallowProceedText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "DisallowProceedText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FetchSaClosureCriteriasForUserResponseValidationError{
					field:  "DisallowProceedText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisallowProceedText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FetchSaClosureCriteriasForUserResponseValidationError{
				field:  "DisallowProceedText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return FetchSaClosureCriteriasForUserResponseMultiError(errors)
	}

	return nil
}

// FetchSaClosureCriteriasForUserResponseMultiError is an error wrapping
// multiple validation errors returned by
// FetchSaClosureCriteriasForUserResponse.ValidateAll() if the designated
// constraints aren't met.
type FetchSaClosureCriteriasForUserResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FetchSaClosureCriteriasForUserResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FetchSaClosureCriteriasForUserResponseMultiError) AllErrors() []error { return m }

// FetchSaClosureCriteriasForUserResponseValidationError is the validation
// error returned by FetchSaClosureCriteriasForUserResponse.Validate if the
// designated constraints aren't met.
type FetchSaClosureCriteriasForUserResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FetchSaClosureCriteriasForUserResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FetchSaClosureCriteriasForUserResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FetchSaClosureCriteriasForUserResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FetchSaClosureCriteriasForUserResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FetchSaClosureCriteriasForUserResponseValidationError) ErrorName() string {
	return "FetchSaClosureCriteriasForUserResponseValidationError"
}

// Error satisfies the builtin error interface
func (e FetchSaClosureCriteriasForUserResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFetchSaClosureCriteriasForUserResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FetchSaClosureCriteriasForUserResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FetchSaClosureCriteriasForUserResponseValidationError{}

// Validate checks the field values on CriteriaGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CriteriaGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaGroup with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CriteriaGroupMultiError, or
// nil if none found.
func (m *CriteriaGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetGroupNumber()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "GroupNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "GroupNumber",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroupNumber()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupValidationError{
				field:  "GroupNumber",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetGroupHeading()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "GroupHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "GroupHeading",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetGroupHeading()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupValidationError{
				field:  "GroupHeading",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetZeroState()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "ZeroState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "ZeroState",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetZeroState()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupValidationError{
				field:  "ZeroState",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetCriteriaItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  fmt.Sprintf("CriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  fmt.Sprintf("CriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CriteriaGroupValidationError{
					field:  fmt.Sprintf("CriteriaItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetInfoText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "InfoText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetInfoText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupValidationError{
				field:  "InfoText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetCollapseIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "CollapseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupValidationError{
					field:  "CollapseIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCollapseIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupValidationError{
				field:  "CollapseIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for IsCollapsed

	// no validation rules for GroupIdentifier

	switch v := m.CriteriaItemsV2.(type) {
	case *CriteriaGroup_CriteriaItemList:
		if v == nil {
			err := CriteriaGroupValidationError{
				field:  "CriteriaItemsV2",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetCriteriaItemList()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  "CriteriaItemList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  "CriteriaItemList",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetCriteriaItemList()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CriteriaGroupValidationError{
					field:  "CriteriaItemList",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CriteriaGroup_FullGroupCriteriaItem:
		if v == nil {
			err := CriteriaGroupValidationError{
				field:  "CriteriaItemsV2",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetFullGroupCriteriaItem()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  "FullGroupCriteriaItem",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CriteriaGroupValidationError{
						field:  "FullGroupCriteriaItem",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetFullGroupCriteriaItem()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CriteriaGroupValidationError{
					field:  "FullGroupCriteriaItem",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CriteriaGroupMultiError(errors)
	}

	return nil
}

// CriteriaGroupMultiError is an error wrapping multiple validation errors
// returned by CriteriaGroup.ValidateAll() if the designated constraints
// aren't met.
type CriteriaGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaGroupMultiError) AllErrors() []error { return m }

// CriteriaGroupValidationError is the validation error returned by
// CriteriaGroup.Validate if the designated constraints aren't met.
type CriteriaGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaGroupValidationError) ErrorName() string { return "CriteriaGroupValidationError" }

// Error satisfies the builtin error interface
func (e CriteriaGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaGroupValidationError{}

// Validate checks the field values on CriteriaItemList with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CriteriaItemList) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaItemList with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CriteriaItemListMultiError, or nil if none found.
func (m *CriteriaItemList) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaItemList) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCriteriaItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CriteriaItemListValidationError{
						field:  fmt.Sprintf("CriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CriteriaItemListValidationError{
						field:  fmt.Sprintf("CriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CriteriaItemListValidationError{
					field:  fmt.Sprintf("CriteriaItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return CriteriaItemListMultiError(errors)
	}

	return nil
}

// CriteriaItemListMultiError is an error wrapping multiple validation errors
// returned by CriteriaItemList.ValidateAll() if the designated constraints
// aren't met.
type CriteriaItemListMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaItemListMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaItemListMultiError) AllErrors() []error { return m }

// CriteriaItemListValidationError is the validation error returned by
// CriteriaItemList.Validate if the designated constraints aren't met.
type CriteriaItemListValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaItemListValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaItemListValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaItemListValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaItemListValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaItemListValidationError) ErrorName() string { return "CriteriaItemListValidationError" }

// Error satisfies the builtin error interface
func (e CriteriaItemListValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaItemList.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaItemListValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaItemListValidationError{}

// Validate checks the field values on CriteriaGroupZeroState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CriteriaGroupZeroState) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaGroupZeroState with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CriteriaGroupZeroStateMultiError, or nil if none found.
func (m *CriteriaGroupZeroState) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaGroupZeroState) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetZeroStateIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupZeroStateValidationError{
					field:  "ZeroStateIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupZeroStateValidationError{
					field:  "ZeroStateIcon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetZeroStateIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupZeroStateValidationError{
				field:  "ZeroStateIcon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetZeroStateText()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaGroupZeroStateValidationError{
					field:  "ZeroStateText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaGroupZeroStateValidationError{
					field:  "ZeroStateText",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetZeroStateText()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaGroupZeroStateValidationError{
				field:  "ZeroStateText",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CriteriaGroupZeroStateMultiError(errors)
	}

	return nil
}

// CriteriaGroupZeroStateMultiError is an error wrapping multiple validation
// errors returned by CriteriaGroupZeroState.ValidateAll() if the designated
// constraints aren't met.
type CriteriaGroupZeroStateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaGroupZeroStateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaGroupZeroStateMultiError) AllErrors() []error { return m }

// CriteriaGroupZeroStateValidationError is the validation error returned by
// CriteriaGroupZeroState.Validate if the designated constraints aren't met.
type CriteriaGroupZeroStateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaGroupZeroStateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaGroupZeroStateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaGroupZeroStateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaGroupZeroStateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaGroupZeroStateValidationError) ErrorName() string {
	return "CriteriaGroupZeroStateValidationError"
}

// Error satisfies the builtin error interface
func (e CriteriaGroupZeroStateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaGroupZeroState.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaGroupZeroStateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaGroupZeroStateValidationError{}

// Validate checks the field values on CriteriaItem with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CriteriaItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaItem with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CriteriaItemMultiError, or
// nil if none found.
func (m *CriteriaItem) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetIcon()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "Icon",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIcon()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaItemValidationError{
				field:  "Icon",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCriteriaName()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CriteriaName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CriteriaName",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCriteriaName()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaItemValidationError{
				field:  "CriteriaName",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCriteriaValue()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CriteriaValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CriteriaValue",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCriteriaValue()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaItemValidationError{
				field:  "CriteriaValue",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCompleteCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CompleteCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "CompleteCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCompleteCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaItemValidationError{
				field:  "CompleteCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for BgColor

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CriteriaItemValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CriteriaItemValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ItemIdentifier

	if len(errors) > 0 {
		return CriteriaItemMultiError(errors)
	}

	return nil
}

// CriteriaItemMultiError is an error wrapping multiple validation errors
// returned by CriteriaItem.ValidateAll() if the designated constraints aren't met.
type CriteriaItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaItemMultiError) AllErrors() []error { return m }

// CriteriaItemValidationError is the validation error returned by
// CriteriaItem.Validate if the designated constraints aren't met.
type CriteriaItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaItemValidationError) ErrorName() string { return "CriteriaItemValidationError" }

// Error satisfies the builtin error interface
func (e CriteriaItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaItemValidationError{}

// Validate checks the field values on FullGroupCriteriaItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *FullGroupCriteriaItem) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on FullGroupCriteriaItem with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// FullGroupCriteriaItemMultiError, or nil if none found.
func (m *FullGroupCriteriaItem) ValidateAll() error {
	return m.validate(true)
}

func (m *FullGroupCriteriaItem) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetMeta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "Meta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetMeta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullGroupCriteriaItemValidationError{
				field:  "Meta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDisplayInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "DisplayInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDisplayInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullGroupCriteriaItemValidationError{
				field:  "DisplayInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRetryCta()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "RetryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, FullGroupCriteriaItemValidationError{
					field:  "RetryCta",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRetryCta()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return FullGroupCriteriaItemValidationError{
				field:  "RetryCta",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ItemIdentifier

	if len(errors) > 0 {
		return FullGroupCriteriaItemMultiError(errors)
	}

	return nil
}

// FullGroupCriteriaItemMultiError is an error wrapping multiple validation
// errors returned by FullGroupCriteriaItem.ValidateAll() if the designated
// constraints aren't met.
type FullGroupCriteriaItemMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m FullGroupCriteriaItemMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m FullGroupCriteriaItemMultiError) AllErrors() []error { return m }

// FullGroupCriteriaItemValidationError is the validation error returned by
// FullGroupCriteriaItem.Validate if the designated constraints aren't met.
type FullGroupCriteriaItemValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e FullGroupCriteriaItemValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e FullGroupCriteriaItemValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e FullGroupCriteriaItemValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e FullGroupCriteriaItemValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e FullGroupCriteriaItemValidationError) ErrorName() string {
	return "FullGroupCriteriaItemValidationError"
}

// Error satisfies the builtin error interface
func (e FullGroupCriteriaItemValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sFullGroupCriteriaItem.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = FullGroupCriteriaItemValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = FullGroupCriteriaItemValidationError{}

// Validate checks the field values on CriteriaItemMeta with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CriteriaItemMeta) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CriteriaItemMeta with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CriteriaItemMetaMultiError, or nil if none found.
func (m *CriteriaItemMeta) ValidateAll() error {
	return m.validate(true)
}

func (m *CriteriaItemMeta) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FetchFailed

	// no validation rules for SoftCriteria

	switch v := m.Value.(type) {
	case *CriteriaItemMeta_Money:
		if v == nil {
			err := CriteriaItemMetaValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if all {
			switch v := interface{}(m.GetMoney()).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, CriteriaItemMetaValidationError{
						field:  "Money",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, CriteriaItemMetaValidationError{
						field:  "Money",
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(m.GetMoney()).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return CriteriaItemMetaValidationError{
					field:  "Money",
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	case *CriteriaItemMeta_FiCoins:
		if v == nil {
			err := CriteriaItemMetaValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for FiCoins
	case *CriteriaItemMeta_IsBlocking:
		if v == nil {
			err := CriteriaItemMetaValidationError{
				field:  "Value",
				reason: "oneof value cannot be a typed-nil",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}
		// no validation rules for IsBlocking
	default:
		_ = v // ensures v is used
	}

	if len(errors) > 0 {
		return CriteriaItemMetaMultiError(errors)
	}

	return nil
}

// CriteriaItemMetaMultiError is an error wrapping multiple validation errors
// returned by CriteriaItemMeta.ValidateAll() if the designated constraints
// aren't met.
type CriteriaItemMetaMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CriteriaItemMetaMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CriteriaItemMetaMultiError) AllErrors() []error { return m }

// CriteriaItemMetaValidationError is the validation error returned by
// CriteriaItemMeta.Validate if the designated constraints aren't met.
type CriteriaItemMetaValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CriteriaItemMetaValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CriteriaItemMetaValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CriteriaItemMetaValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CriteriaItemMetaValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CriteriaItemMetaValidationError) ErrorName() string { return "CriteriaItemMetaValidationError" }

// Error satisfies the builtin error interface
func (e CriteriaItemMetaValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCriteriaItemMeta.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CriteriaItemMetaValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CriteriaItemMetaValidationError{}

// Validate checks the field values on ValidatePanDobForSaClosureRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidatePanDobForSaClosureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidatePanDobForSaClosureRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidatePanDobForSaClosureRequestMultiError, or nil if none found.
func (m *ValidatePanDobForSaClosureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidatePanDobForSaClosureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePanDobForSaClosureRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Pan

	if all {
		switch v := interface{}(m.GetDob()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureRequestValidationError{
					field:  "Dob",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDob()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePanDobForSaClosureRequestValidationError{
				field:  "Dob",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidatePanDobForSaClosureRequestMultiError(errors)
	}

	return nil
}

// ValidatePanDobForSaClosureRequestMultiError is an error wrapping multiple
// validation errors returned by
// ValidatePanDobForSaClosureRequest.ValidateAll() if the designated
// constraints aren't met.
type ValidatePanDobForSaClosureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidatePanDobForSaClosureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidatePanDobForSaClosureRequestMultiError) AllErrors() []error { return m }

// ValidatePanDobForSaClosureRequestValidationError is the validation error
// returned by ValidatePanDobForSaClosureRequest.Validate if the designated
// constraints aren't met.
type ValidatePanDobForSaClosureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidatePanDobForSaClosureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidatePanDobForSaClosureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidatePanDobForSaClosureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidatePanDobForSaClosureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidatePanDobForSaClosureRequestValidationError) ErrorName() string {
	return "ValidatePanDobForSaClosureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ValidatePanDobForSaClosureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidatePanDobForSaClosureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidatePanDobForSaClosureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidatePanDobForSaClosureRequestValidationError{}

// Validate checks the field values on ValidatePanDobForSaClosureResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ValidatePanDobForSaClosureResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ValidatePanDobForSaClosureResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ValidatePanDobForSaClosureResponseMultiError, or nil if none found.
func (m *ValidatePanDobForSaClosureResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ValidatePanDobForSaClosureResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePanDobForSaClosureResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ValidatePanDobForSaClosureResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ValidatePanDobForSaClosureResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ValidatePanDobForSaClosureResponseMultiError(errors)
	}

	return nil
}

// ValidatePanDobForSaClosureResponseMultiError is an error wrapping multiple
// validation errors returned by
// ValidatePanDobForSaClosureResponse.ValidateAll() if the designated
// constraints aren't met.
type ValidatePanDobForSaClosureResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ValidatePanDobForSaClosureResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ValidatePanDobForSaClosureResponseMultiError) AllErrors() []error { return m }

// ValidatePanDobForSaClosureResponseValidationError is the validation error
// returned by ValidatePanDobForSaClosureResponse.Validate if the designated
// constraints aren't met.
type ValidatePanDobForSaClosureResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ValidatePanDobForSaClosureResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ValidatePanDobForSaClosureResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ValidatePanDobForSaClosureResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ValidatePanDobForSaClosureResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ValidatePanDobForSaClosureResponseValidationError) ErrorName() string {
	return "ValidatePanDobForSaClosureResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ValidatePanDobForSaClosureResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sValidatePanDobForSaClosureResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ValidatePanDobForSaClosureResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ValidatePanDobForSaClosureResponseValidationError{}

// Validate checks the field values on SubmitClosureRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClosureRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClosureRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitClosureRequestRequestMultiError, or nil if none found.
func (m *SubmitClosureRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClosureRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitClosureRequestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitClosureRequestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitClosureRequestRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitClosureRequestRequestMultiError(errors)
	}

	return nil
}

// SubmitClosureRequestRequestMultiError is an error wrapping multiple
// validation errors returned by SubmitClosureRequestRequest.ValidateAll() if
// the designated constraints aren't met.
type SubmitClosureRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClosureRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClosureRequestRequestMultiError) AllErrors() []error { return m }

// SubmitClosureRequestRequestValidationError is the validation error returned
// by SubmitClosureRequestRequest.Validate if the designated constraints
// aren't met.
type SubmitClosureRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClosureRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClosureRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClosureRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClosureRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClosureRequestRequestValidationError) ErrorName() string {
	return "SubmitClosureRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClosureRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClosureRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClosureRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClosureRequestRequestValidationError{}

// Validate checks the field values on SubmitClosureRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SubmitClosureRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubmitClosureRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubmitClosureRequestResponseMultiError, or nil if none found.
func (m *SubmitClosureRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SubmitClosureRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitClosureRequestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitClosureRequestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitClosureRequestResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubmitClosureRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubmitClosureRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubmitClosureRequestResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubmitClosureRequestResponseMultiError(errors)
	}

	return nil
}

// SubmitClosureRequestResponseMultiError is an error wrapping multiple
// validation errors returned by SubmitClosureRequestResponse.ValidateAll() if
// the designated constraints aren't met.
type SubmitClosureRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubmitClosureRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubmitClosureRequestResponseMultiError) AllErrors() []error { return m }

// SubmitClosureRequestResponseValidationError is the validation error returned
// by SubmitClosureRequestResponse.Validate if the designated constraints
// aren't met.
type SubmitClosureRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubmitClosureRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubmitClosureRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubmitClosureRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubmitClosureRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubmitClosureRequestResponseValidationError) ErrorName() string {
	return "SubmitClosureRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e SubmitClosureRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubmitClosureRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubmitClosureRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubmitClosureRequestResponseValidationError{}

// Validate checks the field values on CancelClosureRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelClosureRequestRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelClosureRequestRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelClosureRequestRequestMultiError, or nil if none found.
func (m *CancelClosureRequestRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelClosureRequestRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelClosureRequestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelClosureRequestRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelClosureRequestRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CancelClosureRequestRequestMultiError(errors)
	}

	return nil
}

// CancelClosureRequestRequestMultiError is an error wrapping multiple
// validation errors returned by CancelClosureRequestRequest.ValidateAll() if
// the designated constraints aren't met.
type CancelClosureRequestRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelClosureRequestRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelClosureRequestRequestMultiError) AllErrors() []error { return m }

// CancelClosureRequestRequestValidationError is the validation error returned
// by CancelClosureRequestRequest.Validate if the designated constraints
// aren't met.
type CancelClosureRequestRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelClosureRequestRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelClosureRequestRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelClosureRequestRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelClosureRequestRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelClosureRequestRequestValidationError) ErrorName() string {
	return "CancelClosureRequestRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CancelClosureRequestRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelClosureRequestRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelClosureRequestRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelClosureRequestRequestValidationError{}

// Validate checks the field values on CancelClosureRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CancelClosureRequestResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CancelClosureRequestResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CancelClosureRequestResponseMultiError, or nil if none found.
func (m *CancelClosureRequestResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *CancelClosureRequestResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelClosureRequestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelClosureRequestResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelClosureRequestResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetNextAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CancelClosureRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CancelClosureRequestResponseValidationError{
					field:  "NextAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNextAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CancelClosureRequestResponseValidationError{
				field:  "NextAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CancelClosureRequestResponseMultiError(errors)
	}

	return nil
}

// CancelClosureRequestResponseMultiError is an error wrapping multiple
// validation errors returned by CancelClosureRequestResponse.ValidateAll() if
// the designated constraints aren't met.
type CancelClosureRequestResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CancelClosureRequestResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CancelClosureRequestResponseMultiError) AllErrors() []error { return m }

// CancelClosureRequestResponseValidationError is the validation error returned
// by CancelClosureRequestResponse.Validate if the designated constraints
// aren't met.
type CancelClosureRequestResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CancelClosureRequestResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CancelClosureRequestResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CancelClosureRequestResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CancelClosureRequestResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CancelClosureRequestResponseValidationError) ErrorName() string {
	return "CancelClosureRequestResponseValidationError"
}

// Error satisfies the builtin error interface
func (e CancelClosureRequestResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCancelClosureRequestResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CancelClosureRequestResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CancelClosureRequestResponseValidationError{}

// Validate checks the field values on EvaluateUserForClosureEligibilityRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *EvaluateUserForClosureEligibilityRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EvaluateUserForClosureEligibilityRequest with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// EvaluateUserForClosureEligibilityRequestMultiError, or nil if none found.
func (m *EvaluateUserForClosureEligibilityRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *EvaluateUserForClosureEligibilityRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetReq()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityRequestValidationError{
					field:  "Req",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetReq()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvaluateUserForClosureEligibilityRequestValidationError{
				field:  "Req",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ActorId

	if len(errors) > 0 {
		return EvaluateUserForClosureEligibilityRequestMultiError(errors)
	}

	return nil
}

// EvaluateUserForClosureEligibilityRequestMultiError is an error wrapping
// multiple validation errors returned by
// EvaluateUserForClosureEligibilityRequest.ValidateAll() if the designated
// constraints aren't met.
type EvaluateUserForClosureEligibilityRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvaluateUserForClosureEligibilityRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvaluateUserForClosureEligibilityRequestMultiError) AllErrors() []error { return m }

// EvaluateUserForClosureEligibilityRequestValidationError is the validation
// error returned by EvaluateUserForClosureEligibilityRequest.Validate if the
// designated constraints aren't met.
type EvaluateUserForClosureEligibilityRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvaluateUserForClosureEligibilityRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvaluateUserForClosureEligibilityRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvaluateUserForClosureEligibilityRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvaluateUserForClosureEligibilityRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvaluateUserForClosureEligibilityRequestValidationError) ErrorName() string {
	return "EvaluateUserForClosureEligibilityRequestValidationError"
}

// Error satisfies the builtin error interface
func (e EvaluateUserForClosureEligibilityRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvaluateUserForClosureEligibilityRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvaluateUserForClosureEligibilityRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvaluateUserForClosureEligibilityRequestValidationError{}

// Validate checks the field values on SaClosureCriteriaItemStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SaClosureCriteriaItemStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SaClosureCriteriaItemStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SaClosureCriteriaItemStatusMultiError, or nil if none found.
func (m *SaClosureCriteriaItemStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *SaClosureCriteriaItemStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CriteriaItem

	// no validation rules for IsFailed

	if len(errors) > 0 {
		return SaClosureCriteriaItemStatusMultiError(errors)
	}

	return nil
}

// SaClosureCriteriaItemStatusMultiError is an error wrapping multiple
// validation errors returned by SaClosureCriteriaItemStatus.ValidateAll() if
// the designated constraints aren't met.
type SaClosureCriteriaItemStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SaClosureCriteriaItemStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SaClosureCriteriaItemStatusMultiError) AllErrors() []error { return m }

// SaClosureCriteriaItemStatusValidationError is the validation error returned
// by SaClosureCriteriaItemStatus.Validate if the designated constraints
// aren't met.
type SaClosureCriteriaItemStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SaClosureCriteriaItemStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SaClosureCriteriaItemStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SaClosureCriteriaItemStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SaClosureCriteriaItemStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SaClosureCriteriaItemStatusValidationError) ErrorName() string {
	return "SaClosureCriteriaItemStatusValidationError"
}

// Error satisfies the builtin error interface
func (e SaClosureCriteriaItemStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSaClosureCriteriaItemStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SaClosureCriteriaItemStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SaClosureCriteriaItemStatusValidationError{}

// Validate checks the field values on
// EvaluateUserForClosureEligibilityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EvaluateUserForClosureEligibilityResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// EvaluateUserForClosureEligibilityResponse with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in
// EvaluateUserForClosureEligibilityResponseMultiError, or nil if none found.
func (m *EvaluateUserForClosureEligibilityResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *EvaluateUserForClosureEligibilityResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetRespHeader()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
					field:  "RespHeader",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRespHeader()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvaluateUserForClosureEligibilityResponseValidationError{
				field:  "RespHeader",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for HasFreeze

	// no validation rules for FailedCriteriaCheck

	// no validation rules for HasLien

	// no validation rules for HasPendingCharges

	for idx, item := range m.GetEvaluatedCriteriaItems() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
						field:  fmt.Sprintf("EvaluatedCriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
						field:  fmt.Sprintf("EvaluatedCriteriaItems[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EvaluateUserForClosureEligibilityResponseValidationError{
					field:  fmt.Sprintf("EvaluatedCriteriaItems[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for IsAccountClosed

	if all {
		switch v := interface{}(m.GetPendingChargesAmount()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
					field:  "PendingChargesAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EvaluateUserForClosureEligibilityResponseValidationError{
					field:  "PendingChargesAmount",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPendingChargesAmount()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EvaluateUserForClosureEligibilityResponseValidationError{
				field:  "PendingChargesAmount",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EvaluateUserForClosureEligibilityResponseMultiError(errors)
	}

	return nil
}

// EvaluateUserForClosureEligibilityResponseMultiError is an error wrapping
// multiple validation errors returned by
// EvaluateUserForClosureEligibilityResponse.ValidateAll() if the designated
// constraints aren't met.
type EvaluateUserForClosureEligibilityResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EvaluateUserForClosureEligibilityResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EvaluateUserForClosureEligibilityResponseMultiError) AllErrors() []error { return m }

// EvaluateUserForClosureEligibilityResponseValidationError is the validation
// error returned by EvaluateUserForClosureEligibilityResponse.Validate if the
// designated constraints aren't met.
type EvaluateUserForClosureEligibilityResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EvaluateUserForClosureEligibilityResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EvaluateUserForClosureEligibilityResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EvaluateUserForClosureEligibilityResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EvaluateUserForClosureEligibilityResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EvaluateUserForClosureEligibilityResponseValidationError) ErrorName() string {
	return "EvaluateUserForClosureEligibilityResponseValidationError"
}

// Error satisfies the builtin error interface
func (e EvaluateUserForClosureEligibilityResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEvaluateUserForClosureEligibilityResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EvaluateUserForClosureEligibilityResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EvaluateUserForClosureEligibilityResponseValidationError{}
