// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/frontend/account/sa_closure/service.proto

package sa_closure

import (
	_ "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	widget "github.com/epifi/be-common/api/typesv2/common/ui/widget"
	enums "github.com/epifi/gamma/api/frontend/account/enums"
	deeplink "github.com/epifi/gamma/api/frontend/deeplink"
	header "github.com/epifi/gamma/api/frontend/header"
	ui "github.com/epifi/gamma/api/typesv2/ui"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GetSaClosureFlowResponse_Status int32

const (
	// rpc successful
	GetSaClosureFlowResponse_OK GetSaClosureFlowResponse_Status = 0
	// Internal error while processing the request
	GetSaClosureFlowResponse_INTERNAL GetSaClosureFlowResponse_Status = 13
	// status code when operational status api fails
	GetSaClosureFlowResponse_FETCH_OPERATIONAL_STATUS_FAILURE GetSaClosureFlowResponse_Status = 101
)

// Enum value maps for GetSaClosureFlowResponse_Status.
var (
	GetSaClosureFlowResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "FETCH_OPERATIONAL_STATUS_FAILURE",
	}
	GetSaClosureFlowResponse_Status_value = map[string]int32{
		"OK":                               0,
		"INTERNAL":                         13,
		"FETCH_OPERATIONAL_STATUS_FAILURE": 101,
	}
)

func (x GetSaClosureFlowResponse_Status) Enum() *GetSaClosureFlowResponse_Status {
	p := new(GetSaClosureFlowResponse_Status)
	*p = x
	return p
}

func (x GetSaClosureFlowResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetSaClosureFlowResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_sa_closure_service_proto_enumTypes[0].Descriptor()
}

func (GetSaClosureFlowResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_sa_closure_service_proto_enumTypes[0]
}

func (x GetSaClosureFlowResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetSaClosureFlowResponse_Status.Descriptor instead.
func (GetSaClosureFlowResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{1, 0}
}

type FetchSaClosureCriteriasForUserResponse_Status int32

const (
	// rpc successful
	FetchSaClosureCriteriasForUserResponse_OK FetchSaClosureCriteriasForUserResponse_Status = 0
	// Internal error while processing the request
	FetchSaClosureCriteriasForUserResponse_INTERNAL FetchSaClosureCriteriasForUserResponse_Status = 13
	// status code when fetching of any criteria item fails
	FetchSaClosureCriteriasForUserResponse_FETCH_FAILED FetchSaClosureCriteriasForUserResponse_Status = 101
)

// Enum value maps for FetchSaClosureCriteriasForUserResponse_Status.
var (
	FetchSaClosureCriteriasForUserResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "FETCH_FAILED",
	}
	FetchSaClosureCriteriasForUserResponse_Status_value = map[string]int32{
		"OK":           0,
		"INTERNAL":     13,
		"FETCH_FAILED": 101,
	}
)

func (x FetchSaClosureCriteriasForUserResponse_Status) Enum() *FetchSaClosureCriteriasForUserResponse_Status {
	p := new(FetchSaClosureCriteriasForUserResponse_Status)
	*p = x
	return p
}

func (x FetchSaClosureCriteriasForUserResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FetchSaClosureCriteriasForUserResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_sa_closure_service_proto_enumTypes[1].Descriptor()
}

func (FetchSaClosureCriteriasForUserResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_sa_closure_service_proto_enumTypes[1]
}

func (x FetchSaClosureCriteriasForUserResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FetchSaClosureCriteriasForUserResponse_Status.Descriptor instead.
func (FetchSaClosureCriteriasForUserResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{7, 0}
}

type SubmitClosureRequestResponse_Status int32

const (
	// rpc successful
	SubmitClosureRequestResponse_OK SubmitClosureRequestResponse_Status = 0
	// Internal error while processing the request
	SubmitClosureRequestResponse_INTERNAL SubmitClosureRequestResponse_Status = 13
	// status code when operational status api fails
	SubmitClosureRequestResponse_FETCH_OPERATIONAL_STATUS_FAILURE SubmitClosureRequestResponse_Status = 101
)

// Enum value maps for SubmitClosureRequestResponse_Status.
var (
	SubmitClosureRequestResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "FETCH_OPERATIONAL_STATUS_FAILURE",
	}
	SubmitClosureRequestResponse_Status_value = map[string]int32{
		"OK":                               0,
		"INTERNAL":                         13,
		"FETCH_OPERATIONAL_STATUS_FAILURE": 101,
	}
)

func (x SubmitClosureRequestResponse_Status) Enum() *SubmitClosureRequestResponse_Status {
	p := new(SubmitClosureRequestResponse_Status)
	*p = x
	return p
}

func (x SubmitClosureRequestResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubmitClosureRequestResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_sa_closure_service_proto_enumTypes[2].Descriptor()
}

func (SubmitClosureRequestResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_sa_closure_service_proto_enumTypes[2]
}

func (x SubmitClosureRequestResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubmitClosureRequestResponse_Status.Descriptor instead.
func (SubmitClosureRequestResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{17, 0}
}

type EvaluateUserForClosureEligibilityResponse_Status int32

const (
	// rpc successful
	EvaluateUserForClosureEligibilityResponse_OK EvaluateUserForClosureEligibilityResponse_Status = 0
	// Internal error while processing the request
	EvaluateUserForClosureEligibilityResponse_INTERNAL EvaluateUserForClosureEligibilityResponse_Status = 13
	// status code when operational status api fails
	EvaluateUserForClosureEligibilityResponse_FETCH_OPERATIONAL_STATUS_FAILURE EvaluateUserForClosureEligibilityResponse_Status = 101
)

// Enum value maps for EvaluateUserForClosureEligibilityResponse_Status.
var (
	EvaluateUserForClosureEligibilityResponse_Status_name = map[int32]string{
		0:   "OK",
		13:  "INTERNAL",
		101: "FETCH_OPERATIONAL_STATUS_FAILURE",
	}
	EvaluateUserForClosureEligibilityResponse_Status_value = map[string]int32{
		"OK":                               0,
		"INTERNAL":                         13,
		"FETCH_OPERATIONAL_STATUS_FAILURE": 101,
	}
)

func (x EvaluateUserForClosureEligibilityResponse_Status) Enum() *EvaluateUserForClosureEligibilityResponse_Status {
	p := new(EvaluateUserForClosureEligibilityResponse_Status)
	*p = x
	return p
}

func (x EvaluateUserForClosureEligibilityResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EvaluateUserForClosureEligibilityResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_frontend_account_sa_closure_service_proto_enumTypes[3].Descriptor()
}

func (EvaluateUserForClosureEligibilityResponse_Status) Type() protoreflect.EnumType {
	return &file_api_frontend_account_sa_closure_service_proto_enumTypes[3]
}

func (x EvaluateUserForClosureEligibilityResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EvaluateUserForClosureEligibilityResponse_Status.Descriptor instead.
func (EvaluateUserForClosureEligibilityResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{22, 0}
}

type GetSaClosureFlowRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req        *header.RequestHeader            `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	EntryPoint enums.SAClosureRequestEntryPoint `protobuf:"varint,2,opt,name=entry_point,json=entryPoint,proto3,enum=frontend.account.enums.SAClosureRequestEntryPoint" json:"entry_point,omitempty"`
}

func (x *GetSaClosureFlowRequest) Reset() {
	*x = GetSaClosureFlowRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureFlowRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureFlowRequest) ProtoMessage() {}

func (x *GetSaClosureFlowRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureFlowRequest.ProtoReflect.Descriptor instead.
func (*GetSaClosureFlowRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetSaClosureFlowRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *GetSaClosureFlowRequest) GetEntryPoint() enums.SAClosureRequestEntryPoint {
	if x != nil {
		return x.EntryPoint
	}
	return enums.SAClosureRequestEntryPoint(0)
}

type GetSaClosureFlowResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// deeplink to redirect user on entering account closure flow
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *GetSaClosureFlowResponse) Reset() {
	*x = GetSaClosureFlowResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureFlowResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureFlowResponse) ProtoMessage() {}

func (x *GetSaClosureFlowResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureFlowResponse.ProtoReflect.Descriptor instead.
func (*GetSaClosureFlowResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetSaClosureFlowResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSaClosureFlowResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type GetSaClosureBenefitsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *GetSaClosureBenefitsRequest) Reset() {
	*x = GetSaClosureBenefitsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureBenefitsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureBenefitsRequest) ProtoMessage() {}

func (x *GetSaClosureBenefitsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureBenefitsRequest.ProtoReflect.Descriptor instead.
func (*GetSaClosureBenefitsRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetSaClosureBenefitsRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type GetSaClosureBenefitsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader            *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	PageTitle             *common.Text           `protobuf:"bytes,2,opt,name=page_title,json=pageTitle,proto3" json:"page_title,omitempty"`
	BenefitsSectionHeader *ui.IconTextComponent  `protobuf:"bytes,3,opt,name=benefits_section_header,json=benefitsSectionHeader,proto3" json:"benefits_section_header,omitempty"`
	// list of benefit items with title, subtitle and a visual element
	BenefitItems []*widget.VisualElementTitleSubtitleElement `protobuf:"bytes,4,rep,name=benefit_items,json=benefitItems,proto3" json:"benefit_items,omitempty"`
	// proceeds to next screen of account closure
	ProceedCta *deeplink.Cta `protobuf:"bytes,5,opt,name=proceed_cta,json=proceedCta,proto3" json:"proceed_cta,omitempty"`
	// return user to previous screen
	CancelCta *deeplink.Cta `protobuf:"bytes,6,opt,name=cancel_cta,json=cancelCta,proto3" json:"cancel_cta,omitempty"`
}

func (x *GetSaClosureBenefitsResponse) Reset() {
	*x = GetSaClosureBenefitsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSaClosureBenefitsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSaClosureBenefitsResponse) ProtoMessage() {}

func (x *GetSaClosureBenefitsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSaClosureBenefitsResponse.ProtoReflect.Descriptor instead.
func (*GetSaClosureBenefitsResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetSaClosureBenefitsResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *GetSaClosureBenefitsResponse) GetPageTitle() *common.Text {
	if x != nil {
		return x.PageTitle
	}
	return nil
}

func (x *GetSaClosureBenefitsResponse) GetBenefitsSectionHeader() *ui.IconTextComponent {
	if x != nil {
		return x.BenefitsSectionHeader
	}
	return nil
}

func (x *GetSaClosureBenefitsResponse) GetBenefitItems() []*widget.VisualElementTitleSubtitleElement {
	if x != nil {
		return x.BenefitItems
	}
	return nil
}

func (x *GetSaClosureBenefitsResponse) GetProceedCta() *deeplink.Cta {
	if x != nil {
		return x.ProceedCta
	}
	return nil
}

func (x *GetSaClosureBenefitsResponse) GetCancelCta() *deeplink.Cta {
	if x != nil {
		return x.CancelCta
	}
	return nil
}

type SubmitSaClosureUserFeedbackRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// feedback given by user
	// either user selected feedback option or user's custom feedback text
	FeedbackText string `protobuf:"bytes,2,opt,name=feedback_text,json=feedbackText,proto3" json:"feedback_text,omitempty"`
	// when user clicks resolve my issues, a support ticket is created
	CreateSupportTicket bool `protobuf:"varint,3,opt,name=create_support_ticket,json=createSupportTicket,proto3" json:"create_support_ticket,omitempty"`
}

func (x *SubmitSaClosureUserFeedbackRequest) Reset() {
	*x = SubmitSaClosureUserFeedbackRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitSaClosureUserFeedbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitSaClosureUserFeedbackRequest) ProtoMessage() {}

func (x *SubmitSaClosureUserFeedbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitSaClosureUserFeedbackRequest.ProtoReflect.Descriptor instead.
func (*SubmitSaClosureUserFeedbackRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{4}
}

func (x *SubmitSaClosureUserFeedbackRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *SubmitSaClosureUserFeedbackRequest) GetFeedbackText() string {
	if x != nil {
		return x.FeedbackText
	}
	return ""
}

func (x *SubmitSaClosureUserFeedbackRequest) GetCreateSupportTicket() bool {
	if x != nil {
		return x.CreateSupportTicket
	}
	return false
}

type SubmitSaClosureUserFeedbackResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *SubmitSaClosureUserFeedbackResponse) Reset() {
	*x = SubmitSaClosureUserFeedbackResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitSaClosureUserFeedbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitSaClosureUserFeedbackResponse) ProtoMessage() {}

func (x *SubmitSaClosureUserFeedbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitSaClosureUserFeedbackResponse.ProtoReflect.Descriptor instead.
func (*SubmitSaClosureUserFeedbackResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{5}
}

func (x *SubmitSaClosureUserFeedbackResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *SubmitSaClosureUserFeedbackResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type FetchSaClosureCriteriasForUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *FetchSaClosureCriteriasForUserRequest) Reset() {
	*x = FetchSaClosureCriteriasForUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchSaClosureCriteriasForUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchSaClosureCriteriasForUserRequest) ProtoMessage() {}

func (x *FetchSaClosureCriteriasForUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchSaClosureCriteriasForUserRequest.ProtoReflect.Descriptor instead.
func (*FetchSaClosureCriteriasForUserRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{6}
}

func (x *FetchSaClosureCriteriasForUserRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type FetchSaClosureCriteriasForUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader   *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	PageTitle    *common.Text           `protobuf:"bytes,2,opt,name=page_title,json=pageTitle,proto3" json:"page_title,omitempty"`
	PageSubtitle *common.Text           `protobuf:"bytes,3,opt,name=page_subtitle,json=pageSubtitle,proto3" json:"page_subtitle,omitempty"`
	// list of criterias to be fulfilled in groups
	CriteriaGroups []*CriteriaGroup `protobuf:"bytes,4,rep,name=criteria_groups,json=criteriaGroups,proto3" json:"criteria_groups,omitempty"`
	// cta is disabled when any one of the eligible criterias are not fulfilled
	ProceedCta *deeplink.Cta `protobuf:"bytes,5,opt,name=proceed_cta,json=proceedCta,proto3" json:"proceed_cta,omitempty"`
	// text to be displayed when user tries to click proceed button without fulfilling all criterion
	DisallowProceedText *common.Text `protobuf:"bytes,6,opt,name=disallow_proceed_text,json=disallowProceedText,proto3" json:"disallow_proceed_text,omitempty"`
}

func (x *FetchSaClosureCriteriasForUserResponse) Reset() {
	*x = FetchSaClosureCriteriasForUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FetchSaClosureCriteriasForUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchSaClosureCriteriasForUserResponse) ProtoMessage() {}

func (x *FetchSaClosureCriteriasForUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchSaClosureCriteriasForUserResponse.ProtoReflect.Descriptor instead.
func (*FetchSaClosureCriteriasForUserResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{7}
}

func (x *FetchSaClosureCriteriasForUserResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *FetchSaClosureCriteriasForUserResponse) GetPageTitle() *common.Text {
	if x != nil {
		return x.PageTitle
	}
	return nil
}

func (x *FetchSaClosureCriteriasForUserResponse) GetPageSubtitle() *common.Text {
	if x != nil {
		return x.PageSubtitle
	}
	return nil
}

func (x *FetchSaClosureCriteriasForUserResponse) GetCriteriaGroups() []*CriteriaGroup {
	if x != nil {
		return x.CriteriaGroups
	}
	return nil
}

func (x *FetchSaClosureCriteriasForUserResponse) GetProceedCta() *deeplink.Cta {
	if x != nil {
		return x.ProceedCta
	}
	return nil
}

func (x *FetchSaClosureCriteriasForUserResponse) GetDisallowProceedText() *common.Text {
	if x != nil {
		return x.DisallowProceedText
	}
	return nil
}

type CriteriaGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupNumber *ui.IconTextComponent `protobuf:"bytes,1,opt,name=group_number,json=groupNumber,proto3" json:"group_number,omitempty"`
	// eg: clear outstanding loans
	GroupHeading *common.Text `protobuf:"bytes,2,opt,name=group_heading,json=groupHeading,proto3" json:"group_heading,omitempty"`
	// zero state components
	ZeroState *CriteriaGroupZeroState `protobuf:"bytes,3,opt,name=zero_state,json=zeroState,proto3" json:"zero_state,omitempty"`
	// list of criteria in a particular group
	// empty for zero state
	//
	// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
	CriteriaItems []*CriteriaItem `protobuf:"bytes,4,rep,name=criteria_items,json=criteriaItems,proto3" json:"criteria_items,omitempty"`
	// optional: any info text that needs to be conveyed to the user
	// eg: If left unused, your Fi-Coins will automatically expire once you close your account.
	InfoText *ui.IconTextComponent `protobuf:"bytes,5,opt,name=info_text,json=infoText,proto3" json:"info_text,omitempty"`
	BgColor  string                `protobuf:"bytes,6,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// up arrow icon is sent
	// on click the group is collapsed and icon is inverted
	CollapseIcon *common.VisualElement `protobuf:"bytes,7,opt,name=collapse_icon,json=collapseIcon,proto3" json:"collapse_icon,omitempty"`
	IsCollapsed  bool                  `protobuf:"varint,8,opt,name=is_collapsed,json=isCollapsed,proto3" json:"is_collapsed,omitempty"`
	// enums.SaClosureCriteriaGroup.String() - used to identify the group
	GroupIdentifier string `protobuf:"bytes,9,opt,name=group_identifier,json=groupIdentifier,proto3" json:"group_identifier,omitempty"`
	// Types that are assignable to CriteriaItemsV2:
	//
	//	*CriteriaGroup_CriteriaItemList
	//	*CriteriaGroup_FullGroupCriteriaItem
	CriteriaItemsV2 isCriteriaGroup_CriteriaItemsV2 `protobuf_oneof:"criteria_items_v2"`
}

func (x *CriteriaGroup) Reset() {
	*x = CriteriaGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaGroup) ProtoMessage() {}

func (x *CriteriaGroup) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaGroup.ProtoReflect.Descriptor instead.
func (*CriteriaGroup) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{8}
}

func (x *CriteriaGroup) GetGroupNumber() *ui.IconTextComponent {
	if x != nil {
		return x.GroupNumber
	}
	return nil
}

func (x *CriteriaGroup) GetGroupHeading() *common.Text {
	if x != nil {
		return x.GroupHeading
	}
	return nil
}

func (x *CriteriaGroup) GetZeroState() *CriteriaGroupZeroState {
	if x != nil {
		return x.ZeroState
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
func (x *CriteriaGroup) GetCriteriaItems() []*CriteriaItem {
	if x != nil {
		return x.CriteriaItems
	}
	return nil
}

func (x *CriteriaGroup) GetInfoText() *ui.IconTextComponent {
	if x != nil {
		return x.InfoText
	}
	return nil
}

func (x *CriteriaGroup) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *CriteriaGroup) GetCollapseIcon() *common.VisualElement {
	if x != nil {
		return x.CollapseIcon
	}
	return nil
}

func (x *CriteriaGroup) GetIsCollapsed() bool {
	if x != nil {
		return x.IsCollapsed
	}
	return false
}

func (x *CriteriaGroup) GetGroupIdentifier() string {
	if x != nil {
		return x.GroupIdentifier
	}
	return ""
}

func (m *CriteriaGroup) GetCriteriaItemsV2() isCriteriaGroup_CriteriaItemsV2 {
	if m != nil {
		return m.CriteriaItemsV2
	}
	return nil
}

func (x *CriteriaGroup) GetCriteriaItemList() *CriteriaItemList {
	if x, ok := x.GetCriteriaItemsV2().(*CriteriaGroup_CriteriaItemList); ok {
		return x.CriteriaItemList
	}
	return nil
}

func (x *CriteriaGroup) GetFullGroupCriteriaItem() *FullGroupCriteriaItem {
	if x, ok := x.GetCriteriaItemsV2().(*CriteriaGroup_FullGroupCriteriaItem); ok {
		return x.FullGroupCriteriaItem
	}
	return nil
}

type isCriteriaGroup_CriteriaItemsV2 interface {
	isCriteriaGroup_CriteriaItemsV2()
}

type CriteriaGroup_CriteriaItemList struct {
	CriteriaItemList *CriteriaItemList `protobuf:"bytes,10,opt,name=criteria_item_list,json=criteriaItemList,proto3,oneof"`
}

type CriteriaGroup_FullGroupCriteriaItem struct {
	// https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=6594-44095&t=Ck5egBSrYgsB7XLy-4
	FullGroupCriteriaItem *FullGroupCriteriaItem `protobuf:"bytes,11,opt,name=full_group_criteria_item,json=fullGroupCriteriaItem,proto3,oneof"`
}

func (*CriteriaGroup_CriteriaItemList) isCriteriaGroup_CriteriaItemsV2() {}

func (*CriteriaGroup_FullGroupCriteriaItem) isCriteriaGroup_CriteriaItemsV2() {}

type CriteriaItemList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CriteriaItems []*CriteriaItem `protobuf:"bytes,1,rep,name=criteria_items,json=criteriaItems,proto3" json:"criteria_items,omitempty"`
}

func (x *CriteriaItemList) Reset() {
	*x = CriteriaItemList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaItemList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaItemList) ProtoMessage() {}

func (x *CriteriaItemList) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaItemList.ProtoReflect.Descriptor instead.
func (*CriteriaItemList) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{9}
}

func (x *CriteriaItemList) GetCriteriaItems() []*CriteriaItem {
	if x != nil {
		return x.CriteriaItems
	}
	return nil
}

type CriteriaGroupZeroState struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// icon to display when no action has to be taken on the criteria group
	ZeroStateIcon *common.VisualElement `protobuf:"bytes,4,opt,name=zero_state_icon,json=zeroStateIcon,proto3" json:"zero_state_icon,omitempty"`
	// text to display when no action has to be taken on the criteria group
	ZeroStateText *common.Text `protobuf:"bytes,5,opt,name=zero_state_text,json=zeroStateText,proto3" json:"zero_state_text,omitempty"`
}

func (x *CriteriaGroupZeroState) Reset() {
	*x = CriteriaGroupZeroState{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaGroupZeroState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaGroupZeroState) ProtoMessage() {}

func (x *CriteriaGroupZeroState) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaGroupZeroState.ProtoReflect.Descriptor instead.
func (*CriteriaGroupZeroState) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{10}
}

func (x *CriteriaGroupZeroState) GetZeroStateIcon() *common.VisualElement {
	if x != nil {
		return x.ZeroStateIcon
	}
	return nil
}

func (x *CriteriaGroupZeroState) GetZeroStateText() *common.Text {
	if x != nil {
		return x.ZeroStateText
	}
	return nil
}

type CriteriaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// eg: Fi coin icon
	Icon *common.VisualElement `protobuf:"bytes,1,opt,name=icon,proto3" json:"icon,omitempty"`
	// eg: Smart Deposit (total)
	CriteriaName *ui.IconTextComponent `protobuf:"bytes,2,opt,name=criteria_name,json=criteriaName,proto3" json:"criteria_name,omitempty"`
	// eg: ₹8000 - amount remaining in smart deposit
	// can display failure response as well if fetching of criteria data fails
	CriteriaValue *ui.IconTextComponent `protobuf:"bytes,3,opt,name=criteria_value,json=criteriaValue,proto3" json:"criteria_value,omitempty"`
	// Cta to fulfill the criteria
	// takes the user to a screen to fulfill a criteria
	CompleteCta *ui.IconTextComponent `protobuf:"bytes,4,opt,name=complete_cta,json=completeCta,proto3" json:"complete_cta,omitempty"`
	BgColor     string                `protobuf:"bytes,5,opt,name=bg_color,json=bgColor,proto3" json:"bg_color,omitempty"`
	// information needed by BE for validations
	Meta *CriteriaItemMeta `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`
	// enums.SaClosureCriteriaItem.String() - used to identify the item
	ItemIdentifier string `protobuf:"bytes,7,opt,name=item_identifier,json=itemIdentifier,proto3" json:"item_identifier,omitempty"`
}

func (x *CriteriaItem) Reset() {
	*x = CriteriaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaItem) ProtoMessage() {}

func (x *CriteriaItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaItem.ProtoReflect.Descriptor instead.
func (*CriteriaItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{11}
}

func (x *CriteriaItem) GetIcon() *common.VisualElement {
	if x != nil {
		return x.Icon
	}
	return nil
}

func (x *CriteriaItem) GetCriteriaName() *ui.IconTextComponent {
	if x != nil {
		return x.CriteriaName
	}
	return nil
}

func (x *CriteriaItem) GetCriteriaValue() *ui.IconTextComponent {
	if x != nil {
		return x.CriteriaValue
	}
	return nil
}

func (x *CriteriaItem) GetCompleteCta() *ui.IconTextComponent {
	if x != nil {
		return x.CompleteCta
	}
	return nil
}

func (x *CriteriaItem) GetBgColor() string {
	if x != nil {
		return x.BgColor
	}
	return ""
}

func (x *CriteriaItem) GetMeta() *CriteriaItemMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *CriteriaItem) GetItemIdentifier() string {
	if x != nil {
		return x.ItemIdentifier
	}
	return ""
}

type FullGroupCriteriaItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// information needed by BE for validations
	Meta        *CriteriaItemMeta       `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	DisplayInfo *CriteriaGroupZeroState `protobuf:"bytes,2,opt,name=display_info,json=displayInfo,proto3" json:"display_info,omitempty"`
	RetryCta    *ui.IconTextComponent   `protobuf:"bytes,6,opt,name=retry_cta,json=retryCta,proto3" json:"retry_cta,omitempty"`
	// enums.SaClosureCriteriaItem.String() - used to identify the item
	ItemIdentifier string `protobuf:"bytes,7,opt,name=item_identifier,json=itemIdentifier,proto3" json:"item_identifier,omitempty"`
}

func (x *FullGroupCriteriaItem) Reset() {
	*x = FullGroupCriteriaItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FullGroupCriteriaItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FullGroupCriteriaItem) ProtoMessage() {}

func (x *FullGroupCriteriaItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FullGroupCriteriaItem.ProtoReflect.Descriptor instead.
func (*FullGroupCriteriaItem) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{12}
}

func (x *FullGroupCriteriaItem) GetMeta() *CriteriaItemMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

func (x *FullGroupCriteriaItem) GetDisplayInfo() *CriteriaGroupZeroState {
	if x != nil {
		return x.DisplayInfo
	}
	return nil
}

func (x *FullGroupCriteriaItem) GetRetryCta() *ui.IconTextComponent {
	if x != nil {
		return x.RetryCta
	}
	return nil
}

func (x *FullGroupCriteriaItem) GetItemIdentifier() string {
	if x != nil {
		return x.ItemIdentifier
	}
	return ""
}

type CriteriaItemMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FetchFailed bool `protobuf:"varint,1,opt,name=fetch_failed,json=fetchFailed,proto3" json:"fetch_failed,omitempty"`
	// if criteria is soft, next action is enabled without satisfying the criteria
	SoftCriteria bool `protobuf:"varint,2,opt,name=soft_criteria,json=softCriteria,proto3" json:"soft_criteria,omitempty"`
	// Types that are assignable to Value:
	//
	//	*CriteriaItemMeta_Money
	//	*CriteriaItemMeta_FiCoins
	//	*CriteriaItemMeta_IsBlocking
	Value isCriteriaItemMeta_Value `protobuf_oneof:"value"`
}

func (x *CriteriaItemMeta) Reset() {
	*x = CriteriaItemMeta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CriteriaItemMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CriteriaItemMeta) ProtoMessage() {}

func (x *CriteriaItemMeta) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CriteriaItemMeta.ProtoReflect.Descriptor instead.
func (*CriteriaItemMeta) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{13}
}

func (x *CriteriaItemMeta) GetFetchFailed() bool {
	if x != nil {
		return x.FetchFailed
	}
	return false
}

func (x *CriteriaItemMeta) GetSoftCriteria() bool {
	if x != nil {
		return x.SoftCriteria
	}
	return false
}

func (m *CriteriaItemMeta) GetValue() isCriteriaItemMeta_Value {
	if m != nil {
		return m.Value
	}
	return nil
}

func (x *CriteriaItemMeta) GetMoney() *money.Money {
	if x, ok := x.GetValue().(*CriteriaItemMeta_Money); ok {
		return x.Money
	}
	return nil
}

func (x *CriteriaItemMeta) GetFiCoins() int32 {
	if x, ok := x.GetValue().(*CriteriaItemMeta_FiCoins); ok {
		return x.FiCoins
	}
	return 0
}

func (x *CriteriaItemMeta) GetIsBlocking() bool {
	if x, ok := x.GetValue().(*CriteriaItemMeta_IsBlocking); ok {
		return x.IsBlocking
	}
	return false
}

type isCriteriaItemMeta_Value interface {
	isCriteriaItemMeta_Value()
}

type CriteriaItemMeta_Money struct {
	Money *money.Money `protobuf:"bytes,3,opt,name=money,proto3,oneof"`
}

type CriteriaItemMeta_FiCoins struct {
	FiCoins int32 `protobuf:"varint,4,opt,name=fi_coins,json=fiCoins,proto3,oneof"`
}

type CriteriaItemMeta_IsBlocking struct {
	// for crierias which doesn't have a value, but will be blocking the closure
	// eg. fi account linked as UPI lite in other platforms
	IsBlocking bool `protobuf:"varint,5,opt,name=is_blocking,json=isBlocking,proto3,oneof"`
}

func (*CriteriaItemMeta_Money) isCriteriaItemMeta_Value() {}

func (*CriteriaItemMeta_FiCoins) isCriteriaItemMeta_Value() {}

func (*CriteriaItemMeta_IsBlocking) isCriteriaItemMeta_Value() {}

type ValidatePanDobForSaClosureRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	// user entered pan
	Pan string `protobuf:"bytes,2,opt,name=pan,proto3" json:"pan,omitempty"`
	// user entered DOB
	Dob *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=dob,proto3" json:"dob,omitempty"`
}

func (x *ValidatePanDobForSaClosureRequest) Reset() {
	*x = ValidatePanDobForSaClosureRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidatePanDobForSaClosureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidatePanDobForSaClosureRequest) ProtoMessage() {}

func (x *ValidatePanDobForSaClosureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidatePanDobForSaClosureRequest.ProtoReflect.Descriptor instead.
func (*ValidatePanDobForSaClosureRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{14}
}

func (x *ValidatePanDobForSaClosureRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *ValidatePanDobForSaClosureRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *ValidatePanDobForSaClosureRequest) GetDob() *timestamppb.Timestamp {
	if x != nil {
		return x.Dob
	}
	return nil
}

type ValidatePanDobForSaClosureResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// next action for user after successful validation
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *ValidatePanDobForSaClosureResponse) Reset() {
	*x = ValidatePanDobForSaClosureResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ValidatePanDobForSaClosureResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidatePanDobForSaClosureResponse) ProtoMessage() {}

func (x *ValidatePanDobForSaClosureResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidatePanDobForSaClosureResponse.ProtoReflect.Descriptor instead.
func (*ValidatePanDobForSaClosureResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{15}
}

func (x *ValidatePanDobForSaClosureResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *ValidatePanDobForSaClosureResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type SubmitClosureRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *SubmitClosureRequestRequest) Reset() {
	*x = SubmitClosureRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitClosureRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClosureRequestRequest) ProtoMessage() {}

func (x *SubmitClosureRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClosureRequestRequest.ProtoReflect.Descriptor instead.
func (*SubmitClosureRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{16}
}

func (x *SubmitClosureRequestRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type SubmitClosureRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// next action for user after submission of closure request
	NextAction *deeplink.Deeplink `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *SubmitClosureRequestResponse) Reset() {
	*x = SubmitClosureRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubmitClosureRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubmitClosureRequestResponse) ProtoMessage() {}

func (x *SubmitClosureRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubmitClosureRequestResponse.ProtoReflect.Descriptor instead.
func (*SubmitClosureRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{17}
}

func (x *SubmitClosureRequestResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *SubmitClosureRequestResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type CancelClosureRequestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
}

func (x *CancelClosureRequestRequest) Reset() {
	*x = CancelClosureRequestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelClosureRequestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelClosureRequestRequest) ProtoMessage() {}

func (x *CancelClosureRequestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelClosureRequestRequest.ProtoReflect.Descriptor instead.
func (*CancelClosureRequestRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{18}
}

func (x *CancelClosureRequestRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

type CancelClosureRequestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	NextAction *deeplink.Deeplink     `protobuf:"bytes,2,opt,name=next_action,json=nextAction,proto3" json:"next_action,omitempty"`
}

func (x *CancelClosureRequestResponse) Reset() {
	*x = CancelClosureRequestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelClosureRequestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelClosureRequestResponse) ProtoMessage() {}

func (x *CancelClosureRequestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelClosureRequestResponse.ProtoReflect.Descriptor instead.
func (*CancelClosureRequestResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{19}
}

func (x *CancelClosureRequestResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *CancelClosureRequestResponse) GetNextAction() *deeplink.Deeplink {
	if x != nil {
		return x.NextAction
	}
	return nil
}

type EvaluateUserForClosureEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Req     *header.RequestHeader `protobuf:"bytes,1,opt,name=req,proto3" json:"req,omitempty"`
	ActorId string                `protobuf:"bytes,2,opt,name=actor_id,json=actorId,proto3" json:"actor_id,omitempty"`
}

func (x *EvaluateUserForClosureEligibilityRequest) Reset() {
	*x = EvaluateUserForClosureEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateUserForClosureEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateUserForClosureEligibilityRequest) ProtoMessage() {}

func (x *EvaluateUserForClosureEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateUserForClosureEligibilityRequest.ProtoReflect.Descriptor instead.
func (*EvaluateUserForClosureEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{20}
}

func (x *EvaluateUserForClosureEligibilityRequest) GetReq() *header.RequestHeader {
	if x != nil {
		return x.Req
	}
	return nil
}

func (x *EvaluateUserForClosureEligibilityRequest) GetActorId() string {
	if x != nil {
		return x.ActorId
	}
	return ""
}

// Represents the status of a specific SA closure criteria item
type SaClosureCriteriaItemStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The criteria item enum
	CriteriaItem enums.SaClosureCriteriaItem `protobuf:"varint,1,opt,name=criteria_item,json=criteriaItem,proto3,enum=frontend.account.enums.SaClosureCriteriaItem" json:"criteria_item,omitempty"`
	// Whether this criteria item has failed (true if failed, false if completed/satisfied)
	IsFailed bool `protobuf:"varint,2,opt,name=is_failed,json=isFailed,proto3" json:"is_failed,omitempty"`
}

func (x *SaClosureCriteriaItemStatus) Reset() {
	*x = SaClosureCriteriaItemStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SaClosureCriteriaItemStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SaClosureCriteriaItemStatus) ProtoMessage() {}

func (x *SaClosureCriteriaItemStatus) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SaClosureCriteriaItemStatus.ProtoReflect.Descriptor instead.
func (*SaClosureCriteriaItemStatus) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{21}
}

func (x *SaClosureCriteriaItemStatus) GetCriteriaItem() enums.SaClosureCriteriaItem {
	if x != nil {
		return x.CriteriaItem
	}
	return enums.SaClosureCriteriaItem(0)
}

func (x *SaClosureCriteriaItemStatus) GetIsFailed() bool {
	if x != nil {
		return x.IsFailed
	}
	return false
}

type EvaluateUserForClosureEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespHeader *header.ResponseHeader `protobuf:"bytes,1,opt,name=resp_header,json=respHeader,proto3" json:"resp_header,omitempty"`
	// true if account has any freeze
	HasFreeze bool `protobuf:"varint,2,opt,name=has_freeze,json=hasFreeze,proto3" json:"has_freeze,omitempty"`
	// true if user did not satisfy any criteria item
	// eg. has not closed jump investments
	FailedCriteriaCheck bool `protobuf:"varint,3,opt,name=failed_criteria_check,json=failedCriteriaCheck,proto3" json:"failed_criteria_check,omitempty"`
	HasLien             bool `protobuf:"varint,4,opt,name=has_lien,json=hasLien,proto3" json:"has_lien,omitempty"`
	// list of criteria items that user has not fulfilled for account closure
	// Deprecated: Use failed_criteria_items, has_lien, has_freeze, pending_charges_amount, and is_account_closed fields instead.
	// Callers should process the structured data from these fields and create display strings on their end.
	//
	// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
	FailureReasons []string `protobuf:"bytes,5,rep,name=failure_reasons,json=failureReasons,proto3" json:"failure_reasons,omitempty"`
	// true if user has any pending charges on the account
	// Deprecated: Use pending_charges_amount instead. Check if amount > 0 to determine if charges exist.
	//
	// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
	HasPendingCharges bool `protobuf:"varint,6,opt,name=has_pending_charges,json=hasPendingCharges,proto3" json:"has_pending_charges,omitempty"`
	// List of SA closure criteria items with their failure status (true if failed)
	// does not contain soft marked criteria items
	EvaluatedCriteriaItems []*SaClosureCriteriaItemStatus `protobuf:"bytes,7,rep,name=evaluated_criteria_items,json=evaluatedCriteriaItems,proto3" json:"evaluated_criteria_items,omitempty"`
	// Indicates whether the account is already closed
	IsAccountClosed bool `protobuf:"varint,8,opt,name=is_account_closed,json=isAccountClosed,proto3" json:"is_account_closed,omitempty"`
	// Pending charges amount (includes GST, rounded up).
	// This field is populated  if the user has pending charges.
	// Clients should check for both a nil value and a zero amount
	// to determine whether the user has any pending charges.
	PendingChargesAmount *money.Money `protobuf:"bytes,9,opt,name=pending_charges_amount,json=pendingChargesAmount,proto3" json:"pending_charges_amount,omitempty"`
}

func (x *EvaluateUserForClosureEligibilityResponse) Reset() {
	*x = EvaluateUserForClosureEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluateUserForClosureEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluateUserForClosureEligibilityResponse) ProtoMessage() {}

func (x *EvaluateUserForClosureEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_frontend_account_sa_closure_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluateUserForClosureEligibilityResponse.ProtoReflect.Descriptor instead.
func (*EvaluateUserForClosureEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_frontend_account_sa_closure_service_proto_rawDescGZIP(), []int{22}
}

func (x *EvaluateUserForClosureEligibilityResponse) GetRespHeader() *header.ResponseHeader {
	if x != nil {
		return x.RespHeader
	}
	return nil
}

func (x *EvaluateUserForClosureEligibilityResponse) GetHasFreeze() bool {
	if x != nil {
		return x.HasFreeze
	}
	return false
}

func (x *EvaluateUserForClosureEligibilityResponse) GetFailedCriteriaCheck() bool {
	if x != nil {
		return x.FailedCriteriaCheck
	}
	return false
}

func (x *EvaluateUserForClosureEligibilityResponse) GetHasLien() bool {
	if x != nil {
		return x.HasLien
	}
	return false
}

// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
func (x *EvaluateUserForClosureEligibilityResponse) GetFailureReasons() []string {
	if x != nil {
		return x.FailureReasons
	}
	return nil
}

// Deprecated: Marked as deprecated in api/frontend/account/sa_closure/service.proto.
func (x *EvaluateUserForClosureEligibilityResponse) GetHasPendingCharges() bool {
	if x != nil {
		return x.HasPendingCharges
	}
	return false
}

func (x *EvaluateUserForClosureEligibilityResponse) GetEvaluatedCriteriaItems() []*SaClosureCriteriaItemStatus {
	if x != nil {
		return x.EvaluatedCriteriaItems
	}
	return nil
}

func (x *EvaluateUserForClosureEligibilityResponse) GetIsAccountClosed() bool {
	if x != nil {
		return x.IsAccountClosed
	}
	return false
}

func (x *EvaluateUserForClosureEligibilityResponse) GetPendingChargesAmount() *money.Money {
	if x != nil {
		return x.PendingChargesAmount
	}
	return nil
}

var File_api_frontend_account_sa_closure_service_proto protoreflect.FileDescriptor

var file_api_frontend_account_sa_closure_service_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2f, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1b, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x1a, 0x2b, 0x61, 0x70,
	0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x2f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2f, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x21, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x22, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f,
	0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x65, 0x78, 0x74, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x69, 0x2f, 0x77, 0x69, 0x64, 0x67, 0x65,
	0x74, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x27,
	0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x76, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x5f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x28, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2f, 0x75, 0x69, 0x2f, 0x69, 0x63, 0x6f, 0x6e, 0x5f, 0x74, 0x65, 0x78,
	0x74, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa0, 0x01, 0x0a, 0x17,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x53, 0x0a, 0x0b, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x53, 0x41, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x0a, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xe0,
	0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x46,
	0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72,
	0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a,
	0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52,
	0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x06, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a,
	0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x24, 0x0a, 0x20, 0x46,
	0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10,
	0x65, 0x22, 0x4f, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72,
	0x65, 0x71, 0x22, 0xca, 0x03, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69,
	0x74, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54,
	0x65, 0x78, 0x74, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x59,
	0x0a, 0x17, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x5f, 0x73, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x15, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x53, 0x65, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x64, 0x0a, 0x0d, 0x62, 0x65, 0x6e,
	0x65, 0x66, 0x69, 0x74, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x3f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x75, 0x69, 0x2e, 0x77, 0x69, 0x64, 0x67, 0x65, 0x74, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x0c, 0x62, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x37, 0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x63, 0x65, 0x65, 0x64, 0x43, 0x74, 0x61, 0x12, 0x35, 0x0a, 0x0a, 0x63, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b,
	0x2e, 0x43, 0x74, 0x61, 0x52, 0x09, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x74, 0x61, 0x22,
	0xaf, 0x01, 0x0a, 0x22, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x23, 0x0a, 0x0d, 0x66, 0x65, 0x65, 0x64,
	0x62, 0x61, 0x63, 0x6b, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x66, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x65, 0x78, 0x74, 0x12, 0x32, 0x0a,
	0x15, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x5f,
	0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x70, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x22, 0xa5, 0x01, 0x0a, 0x23, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x61, 0x43, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63,
	0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73,
	0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e,
	0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e,
	0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x59, 0x0a, 0x25, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x03, 0x72, 0x65, 0x71, 0x22, 0xf0, 0x03, 0x0a, 0x26, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61,
	0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73,
	0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x37, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x3d, 0x0a, 0x0d, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0c, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x75, 0x62, 0x74, 0x69, 0x74, 0x6c, 0x65, 0x12, 0x53, 0x0a, 0x0f, 0x63, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0e,
	0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x37,
	0x0a, 0x0b, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x63, 0x74, 0x61, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64,
	0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x43, 0x74, 0x61, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x63, 0x65, 0x65, 0x64, 0x43, 0x74, 0x61, 0x12, 0x4c, 0x0a, 0x15, 0x64, 0x69, 0x73, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x78, 0x74,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x13, 0x64, 0x69, 0x73, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x65,
	0x64, 0x54, 0x65, 0x78, 0x74, 0x22, 0x30, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52,
	0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x10, 0x0a, 0x0c, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x65, 0x22, 0x92, 0x06, 0x0a, 0x0d, 0x43, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x44, 0x0a, 0x0c, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69,
	0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x52, 0x0b, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x3d, 0x0a, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74,
	0x52, 0x0c, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x48, 0x65, 0x61, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x52,
	0x0a, 0x0a, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x5a, 0x65,
	0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x09, 0x7a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x54, 0x0a, 0x0e, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69,
	0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f,
	0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08,
	0x69, 0x6e, 0x66, 0x6f, 0x54, 0x65, 0x78, 0x74, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67, 0x5f, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67, 0x43, 0x6f,
	0x6c, 0x6f, 0x72, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x5f,
	0x69, 0x63, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x56, 0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63,
	0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x73, 0x5f, 0x63, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x0b, 0x69, 0x73, 0x43, 0x6f, 0x6c, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x12, 0x29,
	0x0a, 0x10, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69,
	0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x12, 0x63, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x10, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x6d, 0x0a, 0x18, 0x66, 0x75, 0x6c, 0x6c,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x46, 0x75, 0x6c, 0x6c, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x48, 0x00,
	0x52, 0x15, 0x66, 0x75, 0x6c, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x42, 0x13, 0x0a, 0x11, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x5f, 0x76, 0x32, 0x22, 0x64, 0x0a, 0x10,
	0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x4c, 0x69, 0x73, 0x74,
	0x12, 0x50, 0x0a, 0x0e, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x69, 0x74, 0x65,
	0x6d, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49,
	0x74, 0x65, 0x6d, 0x52, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65,
	0x6d, 0x73, 0x22, 0xa5, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x5a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x49, 0x0a,
	0x0f, 0x7a, 0x65, 0x72, 0x6f, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x63, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56, 0x69, 0x73, 0x75,
	0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x7a, 0x65, 0x72, 0x6f, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x49, 0x63, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0f, 0x7a, 0x65, 0x72, 0x6f,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x65, 0x78, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x54, 0x65, 0x78, 0x74, 0x52, 0x0d, 0x7a, 0x65, 0x72,
	0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x54, 0x65, 0x78, 0x74, 0x22, 0xa4, 0x03, 0x0a, 0x0c, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x35, 0x0a, 0x04, 0x69,
	0x63, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x56,
	0x69, 0x73, 0x75, 0x61, 0x6c, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x04, 0x69, 0x63,
	0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0c, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x48, 0x0a, 0x0e, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32,
	0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x12, 0x44, 0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65,
	0x5f, 0x63, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e,
	0x54, 0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x63,
	0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x74, 0x61, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x67,
	0x5f, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x62, 0x67,
	0x43, 0x6f, 0x6c, 0x6f, 0x72, 0x12, 0x41, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x74, 0x65, 0x6d,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x22, 0x9b, 0x02, 0x0a, 0x15, 0x46, 0x75, 0x6c, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x41, 0x0a, 0x04, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61,
	0x49, 0x74, 0x65, 0x6d, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65, 0x74, 0x61, 0x12, 0x56,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x2e, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x5a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f,
	0x63, 0x74, 0x61, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x75, 0x69, 0x2e, 0x49, 0x63, 0x6f, 0x6e, 0x54,
	0x65, 0x78, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52, 0x08, 0x72, 0x65,
	0x74, 0x72, 0x79, 0x43, 0x74, 0x61, 0x12, 0x27, 0x0a, 0x0f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x69,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x69, 0x74, 0x65, 0x6d, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x22,
	0xcf, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x65, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x66, 0x65, 0x74, 0x63,
	0x68, 0x46, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x66, 0x74, 0x5f,
	0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x73, 0x6f, 0x66, 0x74, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x12, 0x2a, 0x0a, 0x05,
	0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x48,
	0x00, 0x52, 0x05, 0x6d, 0x6f, 0x6e, 0x65, 0x79, 0x12, 0x1b, 0x0a, 0x08, 0x66, 0x69, 0x5f, 0x63,
	0x6f, 0x69, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x07, 0x66, 0x69,
	0x43, 0x6f, 0x69, 0x6e, 0x73, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x73,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x42, 0x07, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x22, 0x95, 0x01, 0x0a, 0x21, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61,
	0x6e, 0x44, 0x6f, 0x62, 0x46, 0x6f, 0x72, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x70, 0x61, 0x6e, 0x12, 0x2c, 0x0a, 0x03, 0x64,
	0x6f, 0x62, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x22, 0xa4, 0x01, 0x0a, 0x22, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x6e, 0x44, 0x6f, 0x62, 0x46, 0x6f, 0x72, 0x53,
	0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70,
	0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x22, 0x4f, 0x0a, 0x1b, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65,
	0x71, 0x22, 0xe4, 0x01, 0x0a, 0x1c, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e,
	0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65,
	0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a, 0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x44, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c,
	0x10, 0x0d, 0x12, 0x24, 0x0a, 0x20, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x45, 0x52,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46,
	0x41, 0x49, 0x4c, 0x55, 0x52, 0x45, 0x10, 0x65, 0x22, 0x4f, 0x0a, 0x1b, 0x43, 0x61, 0x6e, 0x63,
	0x65, 0x6c, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x22, 0x9e, 0x01, 0x0a, 0x1c, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x3c, 0x0a, 0x0b,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1b, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x64, 0x65, 0x65,
	0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x2e, 0x44, 0x65, 0x65, 0x70, 0x6c, 0x69, 0x6e, 0x6b, 0x52, 0x0a,
	0x6e, 0x65, 0x78, 0x74, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x77, 0x0a, 0x28, 0x45, 0x76,
	0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x03, 0x72, 0x65, 0x71, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x52, 0x03, 0x72, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x22, 0x8e, 0x01, 0x0a, 0x1b, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72,
	0x65, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x52, 0x0a, 0x0d, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f,
	0x69, 0x74, 0x65, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x52, 0x0c, 0x63, 0x72, 0x69, 0x74, 0x65,
	0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x73, 0x5f, 0x66, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x69, 0x73, 0x46, 0x61,
	0x69, 0x6c, 0x65, 0x64, 0x22, 0xec, 0x04, 0x0a, 0x29, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74,
	0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x70, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x68, 0x61, 0x73, 0x5f, 0x66, 0x72, 0x65, 0x65,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x68, 0x61, 0x73, 0x46, 0x72, 0x65,
	0x65, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x5f, 0x63, 0x72,
	0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x13, 0x66, 0x61, 0x69, 0x6c, 0x65, 0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x19, 0x0a, 0x08, 0x68, 0x61, 0x73, 0x5f, 0x6c,
	0x69, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x68, 0x61, 0x73, 0x4c, 0x69,
	0x65, 0x6e, 0x12, 0x2b, 0x0a, 0x0f, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52,
	0x0e, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x73, 0x12,
	0x32, 0x0a, 0x13, 0x68, 0x61, 0x73, 0x5f, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x11, 0x68, 0x61, 0x73, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x43, 0x68, 0x61, 0x72,
	0x67, 0x65, 0x73, 0x12, 0x72, 0x0a, 0x18, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x63, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18,
	0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64,
	0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x2e, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x16, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x69, 0x61, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x69, 0x73, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0f, 0x69, 0x73, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f,
	0x73, 0x65, 0x64, 0x12, 0x48, 0x0a, 0x16, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x63,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x70, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x44, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x10, 0x0d, 0x12, 0x24, 0x0a,
	0x20, 0x46, 0x45, 0x54, 0x43, 0x48, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x41, 0x4c, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x55, 0x52,
	0x45, 0x10, 0x65, 0x32, 0x86, 0x0a, 0x0a, 0x15, 0x53, 0x61, 0x76, 0x69, 0x6e, 0x67, 0x73, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x7f, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x6f,
	0x77, 0x12, 0x34, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x6f, 0x77,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x46, 0x6c, 0x6f, 0x77, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b,
	0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x42,
	0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x12, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65,
	0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c,
	0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x42, 0x65, 0x6e, 0x65, 0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x39, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e,
	0x47, 0x65, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x42, 0x65, 0x6e, 0x65,
	0x66, 0x69, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xa0, 0x01, 0x0a,
	0x1b, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x12, 0x3f, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x65,
	0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d,
	0x69, 0x74, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0xa9, 0x01, 0x0a, 0x1e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73,
	0x65, 0x72, 0x12, 0x42, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x43, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x43, 0x72, 0x69, 0x74, 0x65, 0x72, 0x69, 0x61, 0x73, 0x46, 0x6f, 0x72, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9d, 0x01, 0x0a, 0x1a,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x50, 0x61, 0x6e, 0x44, 0x6f, 0x62, 0x46, 0x6f,
	0x72, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x12, 0x3e, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x6e, 0x44, 0x6f, 0x62, 0x46, 0x6f, 0x72, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61,
	0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x61, 0x6e, 0x44, 0x6f, 0x62, 0x46, 0x6f, 0x72, 0x53, 0x61, 0x43, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x14,
	0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x2e, 0x53, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39,
	0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x53, 0x75, 0x62,
	0x6d, 0x69, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8b, 0x01, 0x0a, 0x14, 0x43, 0x61,
	0x6e, 0x63, 0x65, 0x6c, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x38, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65,
	0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e,
	0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xd2, 0x01, 0x0a, 0x21, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x45, 0x2e,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x2e, 0x45, 0x76, 0x61, 0x6c,
	0x75, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2e,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75,
	0x72, 0x65, 0x2e, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x6f, 0x72, 0x43, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x80, 0x9e,
	0xd7, 0x0a, 0x00, 0x88, 0x9e, 0xd7, 0x0a, 0x00, 0x90, 0x9e, 0xd7, 0x0a, 0x01, 0x98, 0x9e, 0xd7,
	0x0a, 0x03, 0xb0, 0x9e, 0xd7, 0x0a, 0x01, 0xd0, 0x9e, 0xd7, 0x0a, 0x00, 0x42, 0x70, 0x0a, 0x36,
	0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69,
	0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x66, 0x72, 0x6f, 0x6e, 0x74,
	0x65, 0x6e, 0x64, 0x2e, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2e, 0x73, 0x61, 0x5f, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x2f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x2f, 0x73, 0x61, 0x5f, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_frontend_account_sa_closure_service_proto_rawDescOnce sync.Once
	file_api_frontend_account_sa_closure_service_proto_rawDescData = file_api_frontend_account_sa_closure_service_proto_rawDesc
)

func file_api_frontend_account_sa_closure_service_proto_rawDescGZIP() []byte {
	file_api_frontend_account_sa_closure_service_proto_rawDescOnce.Do(func() {
		file_api_frontend_account_sa_closure_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_frontend_account_sa_closure_service_proto_rawDescData)
	})
	return file_api_frontend_account_sa_closure_service_proto_rawDescData
}

var file_api_frontend_account_sa_closure_service_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_api_frontend_account_sa_closure_service_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_api_frontend_account_sa_closure_service_proto_goTypes = []interface{}{
	(GetSaClosureFlowResponse_Status)(0),                  // 0: frontend.account.sa_closure.GetSaClosureFlowResponse.Status
	(FetchSaClosureCriteriasForUserResponse_Status)(0),    // 1: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.Status
	(SubmitClosureRequestResponse_Status)(0),              // 2: frontend.account.sa_closure.SubmitClosureRequestResponse.Status
	(EvaluateUserForClosureEligibilityResponse_Status)(0), // 3: frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse.Status
	(*GetSaClosureFlowRequest)(nil),                       // 4: frontend.account.sa_closure.GetSaClosureFlowRequest
	(*GetSaClosureFlowResponse)(nil),                      // 5: frontend.account.sa_closure.GetSaClosureFlowResponse
	(*GetSaClosureBenefitsRequest)(nil),                   // 6: frontend.account.sa_closure.GetSaClosureBenefitsRequest
	(*GetSaClosureBenefitsResponse)(nil),                  // 7: frontend.account.sa_closure.GetSaClosureBenefitsResponse
	(*SubmitSaClosureUserFeedbackRequest)(nil),            // 8: frontend.account.sa_closure.SubmitSaClosureUserFeedbackRequest
	(*SubmitSaClosureUserFeedbackResponse)(nil),           // 9: frontend.account.sa_closure.SubmitSaClosureUserFeedbackResponse
	(*FetchSaClosureCriteriasForUserRequest)(nil),         // 10: frontend.account.sa_closure.FetchSaClosureCriteriasForUserRequest
	(*FetchSaClosureCriteriasForUserResponse)(nil),        // 11: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse
	(*CriteriaGroup)(nil),                                 // 12: frontend.account.sa_closure.CriteriaGroup
	(*CriteriaItemList)(nil),                              // 13: frontend.account.sa_closure.CriteriaItemList
	(*CriteriaGroupZeroState)(nil),                        // 14: frontend.account.sa_closure.CriteriaGroupZeroState
	(*CriteriaItem)(nil),                                  // 15: frontend.account.sa_closure.CriteriaItem
	(*FullGroupCriteriaItem)(nil),                         // 16: frontend.account.sa_closure.FullGroupCriteriaItem
	(*CriteriaItemMeta)(nil),                              // 17: frontend.account.sa_closure.CriteriaItemMeta
	(*ValidatePanDobForSaClosureRequest)(nil),             // 18: frontend.account.sa_closure.ValidatePanDobForSaClosureRequest
	(*ValidatePanDobForSaClosureResponse)(nil),            // 19: frontend.account.sa_closure.ValidatePanDobForSaClosureResponse
	(*SubmitClosureRequestRequest)(nil),                   // 20: frontend.account.sa_closure.SubmitClosureRequestRequest
	(*SubmitClosureRequestResponse)(nil),                  // 21: frontend.account.sa_closure.SubmitClosureRequestResponse
	(*CancelClosureRequestRequest)(nil),                   // 22: frontend.account.sa_closure.CancelClosureRequestRequest
	(*CancelClosureRequestResponse)(nil),                  // 23: frontend.account.sa_closure.CancelClosureRequestResponse
	(*EvaluateUserForClosureEligibilityRequest)(nil),      // 24: frontend.account.sa_closure.EvaluateUserForClosureEligibilityRequest
	(*SaClosureCriteriaItemStatus)(nil),                   // 25: frontend.account.sa_closure.SaClosureCriteriaItemStatus
	(*EvaluateUserForClosureEligibilityResponse)(nil),     // 26: frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse
	(*header.RequestHeader)(nil),                          // 27: frontend.header.RequestHeader
	(enums.SAClosureRequestEntryPoint)(0),                 // 28: frontend.account.enums.SAClosureRequestEntryPoint
	(*header.ResponseHeader)(nil),                         // 29: frontend.header.ResponseHeader
	(*deeplink.Deeplink)(nil),                             // 30: frontend.deeplink.Deeplink
	(*common.Text)(nil),                                   // 31: api.typesv2.common.Text
	(*ui.IconTextComponent)(nil),                          // 32: api.typesv2.ui.IconTextComponent
	(*widget.VisualElementTitleSubtitleElement)(nil),      // 33: api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
	(*deeplink.Cta)(nil),                                  // 34: frontend.deeplink.Cta
	(*common.VisualElement)(nil),                          // 35: api.typesv2.common.VisualElement
	(*money.Money)(nil),                                   // 36: google.type.Money
	(*timestamppb.Timestamp)(nil),                         // 37: google.protobuf.Timestamp
	(enums.SaClosureCriteriaItem)(0),                      // 38: frontend.account.enums.SaClosureCriteriaItem
}
var file_api_frontend_account_sa_closure_service_proto_depIdxs = []int32{
	27, // 0: frontend.account.sa_closure.GetSaClosureFlowRequest.req:type_name -> frontend.header.RequestHeader
	28, // 1: frontend.account.sa_closure.GetSaClosureFlowRequest.entry_point:type_name -> frontend.account.enums.SAClosureRequestEntryPoint
	29, // 2: frontend.account.sa_closure.GetSaClosureFlowResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 3: frontend.account.sa_closure.GetSaClosureFlowResponse.next_action:type_name -> frontend.deeplink.Deeplink
	27, // 4: frontend.account.sa_closure.GetSaClosureBenefitsRequest.req:type_name -> frontend.header.RequestHeader
	29, // 5: frontend.account.sa_closure.GetSaClosureBenefitsResponse.resp_header:type_name -> frontend.header.ResponseHeader
	31, // 6: frontend.account.sa_closure.GetSaClosureBenefitsResponse.page_title:type_name -> api.typesv2.common.Text
	32, // 7: frontend.account.sa_closure.GetSaClosureBenefitsResponse.benefits_section_header:type_name -> api.typesv2.ui.IconTextComponent
	33, // 8: frontend.account.sa_closure.GetSaClosureBenefitsResponse.benefit_items:type_name -> api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement
	34, // 9: frontend.account.sa_closure.GetSaClosureBenefitsResponse.proceed_cta:type_name -> frontend.deeplink.Cta
	34, // 10: frontend.account.sa_closure.GetSaClosureBenefitsResponse.cancel_cta:type_name -> frontend.deeplink.Cta
	27, // 11: frontend.account.sa_closure.SubmitSaClosureUserFeedbackRequest.req:type_name -> frontend.header.RequestHeader
	29, // 12: frontend.account.sa_closure.SubmitSaClosureUserFeedbackResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 13: frontend.account.sa_closure.SubmitSaClosureUserFeedbackResponse.next_action:type_name -> frontend.deeplink.Deeplink
	27, // 14: frontend.account.sa_closure.FetchSaClosureCriteriasForUserRequest.req:type_name -> frontend.header.RequestHeader
	29, // 15: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.resp_header:type_name -> frontend.header.ResponseHeader
	31, // 16: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.page_title:type_name -> api.typesv2.common.Text
	31, // 17: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.page_subtitle:type_name -> api.typesv2.common.Text
	12, // 18: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.criteria_groups:type_name -> frontend.account.sa_closure.CriteriaGroup
	34, // 19: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.proceed_cta:type_name -> frontend.deeplink.Cta
	31, // 20: frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse.disallow_proceed_text:type_name -> api.typesv2.common.Text
	32, // 21: frontend.account.sa_closure.CriteriaGroup.group_number:type_name -> api.typesv2.ui.IconTextComponent
	31, // 22: frontend.account.sa_closure.CriteriaGroup.group_heading:type_name -> api.typesv2.common.Text
	14, // 23: frontend.account.sa_closure.CriteriaGroup.zero_state:type_name -> frontend.account.sa_closure.CriteriaGroupZeroState
	15, // 24: frontend.account.sa_closure.CriteriaGroup.criteria_items:type_name -> frontend.account.sa_closure.CriteriaItem
	32, // 25: frontend.account.sa_closure.CriteriaGroup.info_text:type_name -> api.typesv2.ui.IconTextComponent
	35, // 26: frontend.account.sa_closure.CriteriaGroup.collapse_icon:type_name -> api.typesv2.common.VisualElement
	13, // 27: frontend.account.sa_closure.CriteriaGroup.criteria_item_list:type_name -> frontend.account.sa_closure.CriteriaItemList
	16, // 28: frontend.account.sa_closure.CriteriaGroup.full_group_criteria_item:type_name -> frontend.account.sa_closure.FullGroupCriteriaItem
	15, // 29: frontend.account.sa_closure.CriteriaItemList.criteria_items:type_name -> frontend.account.sa_closure.CriteriaItem
	35, // 30: frontend.account.sa_closure.CriteriaGroupZeroState.zero_state_icon:type_name -> api.typesv2.common.VisualElement
	31, // 31: frontend.account.sa_closure.CriteriaGroupZeroState.zero_state_text:type_name -> api.typesv2.common.Text
	35, // 32: frontend.account.sa_closure.CriteriaItem.icon:type_name -> api.typesv2.common.VisualElement
	32, // 33: frontend.account.sa_closure.CriteriaItem.criteria_name:type_name -> api.typesv2.ui.IconTextComponent
	32, // 34: frontend.account.sa_closure.CriteriaItem.criteria_value:type_name -> api.typesv2.ui.IconTextComponent
	32, // 35: frontend.account.sa_closure.CriteriaItem.complete_cta:type_name -> api.typesv2.ui.IconTextComponent
	17, // 36: frontend.account.sa_closure.CriteriaItem.meta:type_name -> frontend.account.sa_closure.CriteriaItemMeta
	17, // 37: frontend.account.sa_closure.FullGroupCriteriaItem.meta:type_name -> frontend.account.sa_closure.CriteriaItemMeta
	14, // 38: frontend.account.sa_closure.FullGroupCriteriaItem.display_info:type_name -> frontend.account.sa_closure.CriteriaGroupZeroState
	32, // 39: frontend.account.sa_closure.FullGroupCriteriaItem.retry_cta:type_name -> api.typesv2.ui.IconTextComponent
	36, // 40: frontend.account.sa_closure.CriteriaItemMeta.money:type_name -> google.type.Money
	27, // 41: frontend.account.sa_closure.ValidatePanDobForSaClosureRequest.req:type_name -> frontend.header.RequestHeader
	37, // 42: frontend.account.sa_closure.ValidatePanDobForSaClosureRequest.dob:type_name -> google.protobuf.Timestamp
	29, // 43: frontend.account.sa_closure.ValidatePanDobForSaClosureResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 44: frontend.account.sa_closure.ValidatePanDobForSaClosureResponse.next_action:type_name -> frontend.deeplink.Deeplink
	27, // 45: frontend.account.sa_closure.SubmitClosureRequestRequest.req:type_name -> frontend.header.RequestHeader
	29, // 46: frontend.account.sa_closure.SubmitClosureRequestResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 47: frontend.account.sa_closure.SubmitClosureRequestResponse.next_action:type_name -> frontend.deeplink.Deeplink
	27, // 48: frontend.account.sa_closure.CancelClosureRequestRequest.req:type_name -> frontend.header.RequestHeader
	29, // 49: frontend.account.sa_closure.CancelClosureRequestResponse.resp_header:type_name -> frontend.header.ResponseHeader
	30, // 50: frontend.account.sa_closure.CancelClosureRequestResponse.next_action:type_name -> frontend.deeplink.Deeplink
	27, // 51: frontend.account.sa_closure.EvaluateUserForClosureEligibilityRequest.req:type_name -> frontend.header.RequestHeader
	38, // 52: frontend.account.sa_closure.SaClosureCriteriaItemStatus.criteria_item:type_name -> frontend.account.enums.SaClosureCriteriaItem
	29, // 53: frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse.resp_header:type_name -> frontend.header.ResponseHeader
	25, // 54: frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse.evaluated_criteria_items:type_name -> frontend.account.sa_closure.SaClosureCriteriaItemStatus
	36, // 55: frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse.pending_charges_amount:type_name -> google.type.Money
	4,  // 56: frontend.account.sa_closure.SavingsAccountClosure.GetSaClosureFlow:input_type -> frontend.account.sa_closure.GetSaClosureFlowRequest
	6,  // 57: frontend.account.sa_closure.SavingsAccountClosure.GetSaClosureBenefits:input_type -> frontend.account.sa_closure.GetSaClosureBenefitsRequest
	8,  // 58: frontend.account.sa_closure.SavingsAccountClosure.SubmitSaClosureUserFeedback:input_type -> frontend.account.sa_closure.SubmitSaClosureUserFeedbackRequest
	10, // 59: frontend.account.sa_closure.SavingsAccountClosure.FetchSaClosureCriteriasForUser:input_type -> frontend.account.sa_closure.FetchSaClosureCriteriasForUserRequest
	18, // 60: frontend.account.sa_closure.SavingsAccountClosure.ValidatePanDobForSaClosure:input_type -> frontend.account.sa_closure.ValidatePanDobForSaClosureRequest
	20, // 61: frontend.account.sa_closure.SavingsAccountClosure.SubmitClosureRequest:input_type -> frontend.account.sa_closure.SubmitClosureRequestRequest
	22, // 62: frontend.account.sa_closure.SavingsAccountClosure.CancelClosureRequest:input_type -> frontend.account.sa_closure.CancelClosureRequestRequest
	24, // 63: frontend.account.sa_closure.SavingsAccountClosure.EvaluateUserForClosureEligibility:input_type -> frontend.account.sa_closure.EvaluateUserForClosureEligibilityRequest
	5,  // 64: frontend.account.sa_closure.SavingsAccountClosure.GetSaClosureFlow:output_type -> frontend.account.sa_closure.GetSaClosureFlowResponse
	7,  // 65: frontend.account.sa_closure.SavingsAccountClosure.GetSaClosureBenefits:output_type -> frontend.account.sa_closure.GetSaClosureBenefitsResponse
	9,  // 66: frontend.account.sa_closure.SavingsAccountClosure.SubmitSaClosureUserFeedback:output_type -> frontend.account.sa_closure.SubmitSaClosureUserFeedbackResponse
	11, // 67: frontend.account.sa_closure.SavingsAccountClosure.FetchSaClosureCriteriasForUser:output_type -> frontend.account.sa_closure.FetchSaClosureCriteriasForUserResponse
	19, // 68: frontend.account.sa_closure.SavingsAccountClosure.ValidatePanDobForSaClosure:output_type -> frontend.account.sa_closure.ValidatePanDobForSaClosureResponse
	21, // 69: frontend.account.sa_closure.SavingsAccountClosure.SubmitClosureRequest:output_type -> frontend.account.sa_closure.SubmitClosureRequestResponse
	23, // 70: frontend.account.sa_closure.SavingsAccountClosure.CancelClosureRequest:output_type -> frontend.account.sa_closure.CancelClosureRequestResponse
	26, // 71: frontend.account.sa_closure.SavingsAccountClosure.EvaluateUserForClosureEligibility:output_type -> frontend.account.sa_closure.EvaluateUserForClosureEligibilityResponse
	64, // [64:72] is the sub-list for method output_type
	56, // [56:64] is the sub-list for method input_type
	56, // [56:56] is the sub-list for extension type_name
	56, // [56:56] is the sub-list for extension extendee
	0,  // [0:56] is the sub-list for field type_name
}

func init() { file_api_frontend_account_sa_closure_service_proto_init() }
func file_api_frontend_account_sa_closure_service_proto_init() {
	if File_api_frontend_account_sa_closure_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_frontend_account_sa_closure_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureFlowRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureFlowResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureBenefitsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSaClosureBenefitsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitSaClosureUserFeedbackRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitSaClosureUserFeedbackResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchSaClosureCriteriasForUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FetchSaClosureCriteriasForUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaItemList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaGroupZeroState); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FullGroupCriteriaItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CriteriaItemMeta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidatePanDobForSaClosureRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ValidatePanDobForSaClosureResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitClosureRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubmitClosureRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelClosureRequestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelClosureRequestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateUserForClosureEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SaClosureCriteriaItemStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_frontend_account_sa_closure_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluateUserForClosureEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_api_frontend_account_sa_closure_service_proto_msgTypes[8].OneofWrappers = []interface{}{
		(*CriteriaGroup_CriteriaItemList)(nil),
		(*CriteriaGroup_FullGroupCriteriaItem)(nil),
	}
	file_api_frontend_account_sa_closure_service_proto_msgTypes[13].OneofWrappers = []interface{}{
		(*CriteriaItemMeta_Money)(nil),
		(*CriteriaItemMeta_FiCoins)(nil),
		(*CriteriaItemMeta_IsBlocking)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_frontend_account_sa_closure_service_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_frontend_account_sa_closure_service_proto_goTypes,
		DependencyIndexes: file_api_frontend_account_sa_closure_service_proto_depIdxs,
		EnumInfos:         file_api_frontend_account_sa_closure_service_proto_enumTypes,
		MessageInfos:      file_api_frontend_account_sa_closure_service_proto_msgTypes,
	}.Build()
	File_api_frontend_account_sa_closure_service_proto = out.File
	file_api_frontend_account_sa_closure_service_proto_rawDesc = nil
	file_api_frontend_account_sa_closure_service_proto_goTypes = nil
	file_api_frontend_account_sa_closure_service_proto_depIdxs = nil
}
