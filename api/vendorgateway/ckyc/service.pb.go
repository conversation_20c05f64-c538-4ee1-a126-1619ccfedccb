// protolint:disable MAX_LINE_LENGTH

// Defines a service for integrating with the "Central KYC Registry" which is
// a centralized repository of KYC records of customers in the financial sector.
// APIs here are based on the patchy documents. Refinement will be done later.
// Please refer https://testbed.ckycindia.in/ckyc/?r=download

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/ckyc/service.proto

package ckyc

import (
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	kyc "github.com/epifi/gamma/api/kyc"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AuthFactorType int32

const (
	AuthFactorType_AUTH_FACTOR_TYPE_UNSPECIFIED AuthFactorType = 0
	// deprrecated dob and pincode auth factor as in V3 api only mobile number is required and it is mandatory
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
	AuthFactorType_DATE_OF_BIRTH_OR_INCORPORATION AuthFactorType = 1
	// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
	AuthFactorType_PINCODE_AND_YEAR_OF_BIRTH AuthFactorType = 2
	AuthFactorType_MOBILE_NUMBER             AuthFactorType = 3
)

// Enum value maps for AuthFactorType.
var (
	AuthFactorType_name = map[int32]string{
		0: "AUTH_FACTOR_TYPE_UNSPECIFIED",
		1: "DATE_OF_BIRTH_OR_INCORPORATION",
		2: "PINCODE_AND_YEAR_OF_BIRTH",
		3: "MOBILE_NUMBER",
	}
	AuthFactorType_value = map[string]int32{
		"AUTH_FACTOR_TYPE_UNSPECIFIED":   0,
		"DATE_OF_BIRTH_OR_INCORPORATION": 1,
		"PINCODE_AND_YEAR_OF_BIRTH":      2,
		"MOBILE_NUMBER":                  3,
	}
)

func (x AuthFactorType) Enum() *AuthFactorType {
	p := new(AuthFactorType)
	*p = x
	return p
}

func (x AuthFactorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuthFactorType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_ckyc_service_proto_enumTypes[0].Descriptor()
}

func (AuthFactorType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_ckyc_service_proto_enumTypes[0]
}

func (x AuthFactorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuthFactorType.Descriptor instead.
func (AuthFactorType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{0}
}

type GetDataResponse_Status int32

const (
	GetDataResponse_OK                      GetDataResponse_Status = 0
	GetDataResponse_DOB_MISMATCH            GetDataResponse_Status = 101
	GetDataResponse_PHONE_NUMBER_MISMATCH   GetDataResponse_Status = 102
	GetDataResponse_PHONE_NUMBER_NOT_LINKED GetDataResponse_Status = 103
)

// Enum value maps for GetDataResponse_Status.
var (
	GetDataResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "DOB_MISMATCH",
		102: "PHONE_NUMBER_MISMATCH",
		103: "PHONE_NUMBER_NOT_LINKED",
	}
	GetDataResponse_Status_value = map[string]int32{
		"OK":                      0,
		"DOB_MISMATCH":            101,
		"PHONE_NUMBER_MISMATCH":   102,
		"PHONE_NUMBER_NOT_LINKED": 103,
	}
)

func (x GetDataResponse_Status) Enum() *GetDataResponse_Status {
	p := new(GetDataResponse_Status)
	*p = x
	return p
}

func (x GetDataResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetDataResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_ckyc_service_proto_enumTypes[1].Descriptor()
}

func (GetDataResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_ckyc_service_proto_enumTypes[1]
}

func (x GetDataResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetDataResponse_Status.Descriptor instead.
func (GetDataResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{3, 0}
}

type VerifyOTPResponse_Status int32

const (
	VerifyOTPResponse_OK VerifyOTPResponse_Status = 0
	// User entered a wrong OTP
	VerifyOTPResponse_INVALID_OTP VerifyOTPResponse_Status = 101
	// User submitted OTP after it's expiry
	VerifyOTPResponse_OTP_EXPIRED VerifyOTPResponse_Status = 102
	// User temporarily blocked to prevent excessive retries
	VerifyOTPResponse_VERIFICATION_TEMP_BLOCKED VerifyOTPResponse_Status = 103
	// Resend OTP limit exceeded
	VerifyOTPResponse_RESEND_OTP_LIMIT_EXCEEDED VerifyOTPResponse_Status = 104
	// This is expected to be returned if OTP is requested to be resent within 90 seconds of the previous request.
	VerifyOTPResponse_RESEND_OTP_COOLDOWN_ACTIVE VerifyOTPResponse_Status = 105
	// This is returned when a user has exhausted all retries for OTP validation
	VerifyOTPResponse_OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED VerifyOTPResponse_Status = 106
	VerifyOTPResponse_OTP_RESENT_SUCCESSFULLY                   VerifyOTPResponse_Status = 107
)

// Enum value maps for VerifyOTPResponse_Status.
var (
	VerifyOTPResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "INVALID_OTP",
		102: "OTP_EXPIRED",
		103: "VERIFICATION_TEMP_BLOCKED",
		104: "RESEND_OTP_LIMIT_EXCEEDED",
		105: "RESEND_OTP_COOLDOWN_ACTIVE",
		106: "OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED",
		107: "OTP_RESENT_SUCCESSFULLY",
	}
	VerifyOTPResponse_Status_value = map[string]int32{
		"OK":                         0,
		"INVALID_OTP":                101,
		"OTP_EXPIRED":                102,
		"VERIFICATION_TEMP_BLOCKED":  103,
		"RESEND_OTP_LIMIT_EXCEEDED":  104,
		"RESEND_OTP_COOLDOWN_ACTIVE": 105,
		"OTP_VALIDATION_FAILED_AND_RETRY_EXHAUSTED": 106,
		"OTP_RESENT_SUCCESSFULLY":                   107,
	}
)

func (x VerifyOTPResponse_Status) Enum() *VerifyOTPResponse_Status {
	p := new(VerifyOTPResponse_Status)
	*p = x
	return p
}

func (x VerifyOTPResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyOTPResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_ckyc_service_proto_enumTypes[2].Descriptor()
}

func (VerifyOTPResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_ckyc_service_proto_enumTypes[2]
}

func (x VerifyOTPResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyOTPResponse_Status.Descriptor instead.
func (VerifyOTPResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{6, 0}
}

type SearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	KycDocument  *kyc.IdProof                 `protobuf:"bytes,2,opt,name=kyc_document,json=kycDocument,proto3" json:"kyc_document,omitempty"`
	MobileNumber *common.PhoneNumber          `protobuf:"bytes,3,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{0}
}

func (x *SearchRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SearchRequest) GetKycDocument() *kyc.IdProof {
	if x != nil {
		return x.KycDocument
	}
	return nil
}

func (x *SearchRequest) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

type SearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,3,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	// In the search response these records are partially filled.
	Records []*Record `protobuf:"bytes,1,rep,name=records,proto3" json:"records,omitempty"`
	// reference id for the ckyc record using which download request will be made
	CkycReferenceId string `protobuf:"bytes,4,opt,name=ckyc_reference_id,json=ckycReferenceId,proto3" json:"ckyc_reference_id,omitempty"`
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{1}
}

func (x *SearchResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SearchResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

func (x *SearchResponse) GetRecords() []*Record {
	if x != nil {
		return x.Records
	}
	return nil
}

func (x *SearchResponse) GetCkycReferenceId() string {
	if x != nil {
		return x.CkycReferenceId
	}
	return ""
}

type GetDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// CKYC record number for which data is to be retrieved.
	// Deprecated: use ckyc reference id instead
	//
	// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
	CkycNumber     string         `protobuf:"bytes,2,opt,name=ckyc_number,json=ckycNumber,proto3" json:"ckyc_number,omitempty"`
	AuthFactorType AuthFactorType `protobuf:"varint,3,opt,name=auth_factor_type,json=authFactorType,proto3,enum=vendorgateway.ckyc.AuthFactorType" json:"auth_factor_type,omitempty"`
	// For DATE_OF_BIRTH_OR_INCORPORATION the date in DD-MM-YYYY format.
	// For PINCODE_AND_YEAR_OF_BIRTH 6 digit pin code + 4 digit year.
	// For MOBILE_NUMBER 10 digit phone number.
	AuthFactorValue string              `protobuf:"bytes,4,opt,name=auth_factor_value,json=authFactorValue,proto3" json:"auth_factor_value,omitempty"`
	MobileNumber    *common.PhoneNumber `protobuf:"bytes,5,opt,name=mobile_number,json=mobileNumber,proto3" json:"mobile_number,omitempty"`
	// ckyc reference id received in the CKYC search response
	CkycReferenceId string `protobuf:"bytes,6,opt,name=ckyc_reference_id,json=ckycReferenceId,proto3" json:"ckyc_reference_id,omitempty"`
}

func (x *GetDataRequest) Reset() {
	*x = GetDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRequest) ProtoMessage() {}

func (x *GetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRequest.ProtoReflect.Descriptor instead.
func (*GetDataRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{2}
}

func (x *GetDataRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
func (x *GetDataRequest) GetCkycNumber() string {
	if x != nil {
		return x.CkycNumber
	}
	return ""
}

func (x *GetDataRequest) GetAuthFactorType() AuthFactorType {
	if x != nil {
		return x.AuthFactorType
	}
	return AuthFactorType_AUTH_FACTOR_TYPE_UNSPECIFIED
}

func (x *GetDataRequest) GetAuthFactorValue() string {
	if x != nil {
		return x.AuthFactorValue
	}
	return ""
}

func (x *GetDataRequest) GetMobileNumber() *common.PhoneNumber {
	if x != nil {
		return x.MobileNumber
	}
	return nil
}

func (x *GetDataRequest) GetCkycReferenceId() string {
	if x != nil {
		return x.CkycReferenceId
	}
	return ""
}

type GetDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,4,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
	Payload      *kyc.CKYCDownloadPayload `protobuf:"bytes,1,opt,name=payload,proto3" json:"payload,omitempty"`
	RequestId    string                   `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	OtpRequestId string                   `protobuf:"bytes,5,opt,name=otp_request_id,json=otpRequestId,proto3" json:"otp_request_id,omitempty"`
}

func (x *GetDataResponse) Reset() {
	*x = GetDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataResponse) ProtoMessage() {}

func (x *GetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataResponse.ProtoReflect.Descriptor instead.
func (*GetDataResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDataResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

// Deprecated: Marked as deprecated in api/vendorgateway/ckyc/service.proto.
func (x *GetDataResponse) GetPayload() *kyc.CKYCDownloadPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *GetDataResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetDataResponse) GetOtpRequestId() string {
	if x != nil {
		return x.OtpRequestId
	}
	return ""
}

type Record struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycNumber  string        `protobuf:"bytes,1,opt,name=ckyc_number,json=ckycNumber,proto3" json:"ckyc_number,omitempty"`
	Name        *common.Name  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	FathersName *common.Name  `protobuf:"bytes,3,opt,name=fathers_name,json=fathersName,proto3" json:"fathers_name,omitempty"`
	Photo       *common.Image `protobuf:"bytes,4,opt,name=photo,proto3" json:"photo,omitempty"`
	// date when kyc was first recorded
	KycDate *date.Date `protobuf:"bytes,6,opt,name=kyc_date,json=kycDate,proto3" json:"kyc_date,omitempty"`
	// date when kyc record was last updated
	UpdatedDate *date.Date   `protobuf:"bytes,7,opt,name=updated_date,json=updatedDate,proto3" json:"updated_date,omitempty"`
	KycDocument *kyc.IdProof `protobuf:"bytes,8,opt,name=kyc_document,json=kycDocument,proto3" json:"kyc_document,omitempty"`
	Age         int32        `protobuf:"varint,9,opt,name=age,proto3" json:"age,omitempty"`
}

func (x *Record) Reset() {
	*x = Record{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Record) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Record) ProtoMessage() {}

func (x *Record) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Record.ProtoReflect.Descriptor instead.
func (*Record) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{4}
}

func (x *Record) GetCkycNumber() string {
	if x != nil {
		return x.CkycNumber
	}
	return ""
}

func (x *Record) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *Record) GetFathersName() *common.Name {
	if x != nil {
		return x.FathersName
	}
	return nil
}

func (x *Record) GetPhoto() *common.Image {
	if x != nil {
		return x.Photo
	}
	return nil
}

func (x *Record) GetKycDate() *date.Date {
	if x != nil {
		return x.KycDate
	}
	return nil
}

func (x *Record) GetUpdatedDate() *date.Date {
	if x != nil {
		return x.UpdatedDate
	}
	return nil
}

func (x *Record) GetKycDocument() *kyc.IdProof {
	if x != nil {
		return x.KycDocument
	}
	return nil
}

func (x *Record) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

type VerifyOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header    *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	RequestId string                       `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	// otp request id received in GetDataResponse
	OtpRequestId string `protobuf:"bytes,3,opt,name=otp_request_id,json=otpRequestId,proto3" json:"otp_request_id,omitempty"`
	// if otp is present in the request then it will be verified.
	// if otp is empty in the request then it will resend otp
	Otp string `protobuf:"bytes,4,opt,name=otp,proto3" json:"otp,omitempty"`
}

func (x *VerifyOTPRequest) Reset() {
	*x = VerifyOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOTPRequest) ProtoMessage() {}

func (x *VerifyOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOTPRequest.ProtoReflect.Descriptor instead.
func (*VerifyOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{5}
}

func (x *VerifyOTPRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *VerifyOTPRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VerifyOTPRequest) GetOtpRequestId() string {
	if x != nil {
		return x.OtpRequestId
	}
	return ""
}

func (x *VerifyOTPRequest) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

type VerifyOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status       *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	VendorStatus *vendorgateway.VendorStatus `protobuf:"bytes,2,opt,name=vendor_status,json=vendorStatus,proto3" json:"vendor_status,omitempty"`
	Payload      *kyc.CKYCDownloadPayload    `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	RequestId    string                      `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
}

func (x *VerifyOTPResponse) Reset() {
	*x = VerifyOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOTPResponse) ProtoMessage() {}

func (x *VerifyOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_ckyc_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOTPResponse.ProtoReflect.Descriptor instead.
func (*VerifyOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_ckyc_service_proto_rawDescGZIP(), []int{6}
}

func (x *VerifyOTPResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *VerifyOTPResponse) GetVendorStatus() *vendorgateway.VendorStatus {
	if x != nil {
		return x.VendorStatus
	}
	return nil
}

func (x *VerifyOTPResponse) GetPayload() *kyc.CKYCDownloadPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *VerifyOTPResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

var File_api_vendorgateway_ckyc_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_ckyc_service_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x63, 0x6b, 0x79, 0x63, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x12, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79, 0x63, 0x1a, 0x11, 0x61, 0x70, 0x69, 0x2f,
	0x6b, 0x79, 0x63, 0x2f, 0x6b, 0x79, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x61,
	0x70, 0x69, 0x2f, 0x6b, 0x79, 0x63, 0x2f, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x69,
	0x6d, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e,
	0x61, 0x6d, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74,
	0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x70,
	0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xbc, 0x01,
	0x0a, 0x0d, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x2f, 0x0a, 0x0c, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x6f, 0x63,
	0x75, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x49, 0x64, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x0b, 0x6b, 0x79, 0x63, 0x44, 0x6f,
	0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c,
	0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x22, 0xd9, 0x01, 0x0a,
	0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79, 0x63, 0x2e, 0x52, 0x65, 0x63,
	0x6f, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x63, 0x6f, 0x72, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x63, 0x6b, 0x79, 0x63, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x63, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x22, 0xd7, 0x02, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x23, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x63, 0x6b, 0x79, 0x63,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x4c, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x63, 0x6b, 0x79, 0x63, 0x2e, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x0e, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x61, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x44, 0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x52, 0x0c, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65,
	0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x63, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65,
	0x49, 0x64, 0x22, 0xd1, 0x02, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x43, 0x4b, 0x59, 0x43, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x42, 0x02, 0x18, 0x01, 0x52, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x6f, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x74,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0x5a, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c,
	0x44, 0x4f, 0x42, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x65, 0x12, 0x19,
	0x0a, 0x15, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4d,
	0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x66, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x48, 0x4f,
	0x4e, 0x45, 0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x4c, 0x49,
	0x4e, 0x4b, 0x45, 0x44, 0x10, 0x67, 0x22, 0xec, 0x02, 0x0a, 0x06, 0x52, 0x65, 0x63, 0x6f, 0x72,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6b, 0x79, 0x63, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x52, 0x0b, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2f, 0x0a,
	0x05, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x2c,
	0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44,
	0x61, 0x74, 0x65, 0x52, 0x07, 0x6b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x65, 0x12, 0x34, 0x0a, 0x0c,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x2f, 0x0a, 0x0c, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0c, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x49,
	0x64, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x52, 0x0b, 0x6b, 0x79, 0x63, 0x44, 0x6f, 0x63, 0x75, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x03, 0x61, 0x67, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12,
	0x24, 0x0a, 0x0e, 0x6f, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x74, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x6f, 0x74, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6f, 0x74, 0x70, 0x22, 0xac, 0x03, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e,
	0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x40, 0x0a, 0x0d, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x56, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x43, 0x4b, 0x59, 0x43,
	0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x52,
	0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x22, 0xdc, 0x01, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x49, 0x4e,
	0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x10, 0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x4f,
	0x54, 0x50, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x66, 0x12, 0x1d, 0x0a, 0x19,
	0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x54, 0x45, 0x4d,
	0x50, 0x5f, 0x42, 0x4c, 0x4f, 0x43, 0x4b, 0x45, 0x44, 0x10, 0x67, 0x12, 0x1d, 0x0a, 0x19, 0x52,
	0x45, 0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x4c, 0x49, 0x4d, 0x49, 0x54, 0x5f,
	0x45, 0x58, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x68, 0x12, 0x1e, 0x0a, 0x1a, 0x52, 0x45,
	0x53, 0x45, 0x4e, 0x44, 0x5f, 0x4f, 0x54, 0x50, 0x5f, 0x43, 0x4f, 0x4f, 0x4c, 0x44, 0x4f, 0x57,
	0x4e, 0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x69, 0x12, 0x2d, 0x0a, 0x29, 0x4f, 0x54,
	0x50, 0x5f, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x52, 0x45, 0x54, 0x52, 0x59, 0x5f, 0x45, 0x58,
	0x48, 0x41, 0x55, 0x53, 0x54, 0x45, 0x44, 0x10, 0x6a, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x54, 0x50,
	0x5f, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x46,
	0x55, 0x4c, 0x4c, 0x59, 0x10, 0x6b, 0x2a, 0x90, 0x01, 0x0a, 0x0e, 0x41, 0x75, 0x74, 0x68, 0x46,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x1c, 0x41, 0x55, 0x54,
	0x48, 0x5f, 0x46, 0x41, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x26, 0x0a, 0x1e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x49, 0x52, 0x54, 0x48, 0x5f, 0x4f, 0x52, 0x5f,
	0x49, 0x4e, 0x43, 0x4f, 0x52, 0x50, 0x4f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x1a,
	0x02, 0x08, 0x01, 0x12, 0x21, 0x0a, 0x19, 0x50, 0x49, 0x4e, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x41,
	0x4e, 0x44, 0x5f, 0x59, 0x45, 0x41, 0x52, 0x5f, 0x4f, 0x46, 0x5f, 0x42, 0x49, 0x52, 0x54, 0x48,
	0x10, 0x02, 0x1a, 0x02, 0x08, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x4d, 0x4f, 0x42, 0x49, 0x4c, 0x45,
	0x5f, 0x4e, 0x55, 0x4d, 0x42, 0x45, 0x52, 0x10, 0x03, 0x32, 0x85, 0x02, 0x0a, 0x04, 0x43, 0x4b,
	0x79, 0x63, 0x12, 0x4f, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x21, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79,
	0x63, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x63, 0x6b, 0x79, 0x63, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x52, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63,
	0x6b, 0x79, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x58, 0x0a, 0x09, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x4f, 0x54, 0x50, 0x12, 0x24, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79, 0x63, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79,
	0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b, 0x79, 0x63, 0x2e,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x42, 0x5e, 0x0a, 0x2d, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x63, 0x6b,
	0x79, 0x63, 0x5a, 0x2d, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65,
	0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x63, 0x6b, 0x79,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_ckyc_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_ckyc_service_proto_rawDescData = file_api_vendorgateway_ckyc_service_proto_rawDesc
)

func file_api_vendorgateway_ckyc_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_ckyc_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_ckyc_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_ckyc_service_proto_rawDescData)
	})
	return file_api_vendorgateway_ckyc_service_proto_rawDescData
}

var file_api_vendorgateway_ckyc_service_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_api_vendorgateway_ckyc_service_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_api_vendorgateway_ckyc_service_proto_goTypes = []interface{}{
	(AuthFactorType)(0),                 // 0: vendorgateway.ckyc.AuthFactorType
	(GetDataResponse_Status)(0),         // 1: vendorgateway.ckyc.GetDataResponse.Status
	(VerifyOTPResponse_Status)(0),       // 2: vendorgateway.ckyc.VerifyOTPResponse.Status
	(*SearchRequest)(nil),               // 3: vendorgateway.ckyc.SearchRequest
	(*SearchResponse)(nil),              // 4: vendorgateway.ckyc.SearchResponse
	(*GetDataRequest)(nil),              // 5: vendorgateway.ckyc.GetDataRequest
	(*GetDataResponse)(nil),             // 6: vendorgateway.ckyc.GetDataResponse
	(*Record)(nil),                      // 7: vendorgateway.ckyc.Record
	(*VerifyOTPRequest)(nil),            // 8: vendorgateway.ckyc.VerifyOTPRequest
	(*VerifyOTPResponse)(nil),           // 9: vendorgateway.ckyc.VerifyOTPResponse
	(*vendorgateway.RequestHeader)(nil), // 10: vendorgateway.RequestHeader
	(*kyc.IdProof)(nil),                 // 11: kyc.IdProof
	(*common.PhoneNumber)(nil),          // 12: api.typesv2.common.PhoneNumber
	(*rpc.Status)(nil),                  // 13: rpc.Status
	(*vendorgateway.VendorStatus)(nil),  // 14: vendorgateway.VendorStatus
	(*kyc.CKYCDownloadPayload)(nil),     // 15: kyc.CKYCDownloadPayload
	(*common.Name)(nil),                 // 16: api.typesv2.common.Name
	(*common.Image)(nil),                // 17: api.typesv2.common.Image
	(*date.Date)(nil),                   // 18: google.type.Date
}
var file_api_vendorgateway_ckyc_service_proto_depIdxs = []int32{
	10, // 0: vendorgateway.ckyc.SearchRequest.header:type_name -> vendorgateway.RequestHeader
	11, // 1: vendorgateway.ckyc.SearchRequest.kyc_document:type_name -> kyc.IdProof
	12, // 2: vendorgateway.ckyc.SearchRequest.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	13, // 3: vendorgateway.ckyc.SearchResponse.status:type_name -> rpc.Status
	14, // 4: vendorgateway.ckyc.SearchResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	7,  // 5: vendorgateway.ckyc.SearchResponse.records:type_name -> vendorgateway.ckyc.Record
	10, // 6: vendorgateway.ckyc.GetDataRequest.header:type_name -> vendorgateway.RequestHeader
	0,  // 7: vendorgateway.ckyc.GetDataRequest.auth_factor_type:type_name -> vendorgateway.ckyc.AuthFactorType
	12, // 8: vendorgateway.ckyc.GetDataRequest.mobile_number:type_name -> api.typesv2.common.PhoneNumber
	13, // 9: vendorgateway.ckyc.GetDataResponse.status:type_name -> rpc.Status
	14, // 10: vendorgateway.ckyc.GetDataResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	15, // 11: vendorgateway.ckyc.GetDataResponse.payload:type_name -> kyc.CKYCDownloadPayload
	16, // 12: vendorgateway.ckyc.Record.name:type_name -> api.typesv2.common.Name
	16, // 13: vendorgateway.ckyc.Record.fathers_name:type_name -> api.typesv2.common.Name
	17, // 14: vendorgateway.ckyc.Record.photo:type_name -> api.typesv2.common.Image
	18, // 15: vendorgateway.ckyc.Record.kyc_date:type_name -> google.type.Date
	18, // 16: vendorgateway.ckyc.Record.updated_date:type_name -> google.type.Date
	11, // 17: vendorgateway.ckyc.Record.kyc_document:type_name -> kyc.IdProof
	10, // 18: vendorgateway.ckyc.VerifyOTPRequest.header:type_name -> vendorgateway.RequestHeader
	13, // 19: vendorgateway.ckyc.VerifyOTPResponse.status:type_name -> rpc.Status
	14, // 20: vendorgateway.ckyc.VerifyOTPResponse.vendor_status:type_name -> vendorgateway.VendorStatus
	15, // 21: vendorgateway.ckyc.VerifyOTPResponse.payload:type_name -> kyc.CKYCDownloadPayload
	3,  // 22: vendorgateway.ckyc.CKyc.Search:input_type -> vendorgateway.ckyc.SearchRequest
	5,  // 23: vendorgateway.ckyc.CKyc.GetData:input_type -> vendorgateway.ckyc.GetDataRequest
	8,  // 24: vendorgateway.ckyc.CKyc.VerifyOTP:input_type -> vendorgateway.ckyc.VerifyOTPRequest
	4,  // 25: vendorgateway.ckyc.CKyc.Search:output_type -> vendorgateway.ckyc.SearchResponse
	6,  // 26: vendorgateway.ckyc.CKyc.GetData:output_type -> vendorgateway.ckyc.GetDataResponse
	9,  // 27: vendorgateway.ckyc.CKyc.VerifyOTP:output_type -> vendorgateway.ckyc.VerifyOTPResponse
	25, // [25:28] is the sub-list for method output_type
	22, // [22:25] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_ckyc_service_proto_init() }
func file_api_vendorgateway_ckyc_service_proto_init() {
	if File_api_vendorgateway_ckyc_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_ckyc_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Record); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_ckyc_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_ckyc_service_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_ckyc_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_ckyc_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_ckyc_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_ckyc_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_ckyc_service_proto = out.File
	file_api_vendorgateway_ckyc_service_proto_rawDesc = nil
	file_api_vendorgateway_ckyc_service_proto_goTypes = nil
	file_api_vendorgateway_ckyc_service_proto_depIdxs = nil
}
