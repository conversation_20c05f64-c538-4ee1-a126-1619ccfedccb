// protolint:disable MAX_LINE_LENGTH

// Defines a service for integrating with the "Central KYC Registry" which is
// a centralized repository of KYC records of customers in the financial sector.
// APIs here are based on the patchy documents. Refinement will be done later.
// Please refer https://testbed.ckycindia.in/ckyc/?r=download

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/ckyc/service.proto

package ckyc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CKyc_Search_FullMethodName    = "/vendorgateway.ckyc.CKyc/Search"
	CKyc_GetData_FullMethodName   = "/vendorgateway.ckyc.CKyc/GetData"
	CKyc_VerifyOTP_FullMethodName = "/vendorgateway.ckyc.CKyc/VerifyOTP"
)

// CKycClient is the client API for CKyc service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CKycClient interface {
	// Searches the ckyc database and returns ckyc records that match the search
	// criteria. Search criteria include different kyc document identifiers along
	// data like date of birth gender etc.
	Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error)
	// GetData is used to initiate the otp verification procedure for first time for a customer
	GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*GetDataResponse, error)
	// VerifyOTP is used to verify the otp, resend the otp, if the otp is verified then it returns the data of coustomer as well
	// if otp is present in the request then it will be verified.
	// if otp is empty in the request then it will resend otp
	// https://docs.google.com/document/d/1gn-Jq9O71qrytXjRCnBmh3sbLkHY4Lx0/edit
	VerifyOTP(ctx context.Context, in *VerifyOTPRequest, opts ...grpc.CallOption) (*VerifyOTPResponse, error)
}

type cKycClient struct {
	cc grpc.ClientConnInterface
}

func NewCKycClient(cc grpc.ClientConnInterface) CKycClient {
	return &cKycClient{cc}
}

func (c *cKycClient) Search(ctx context.Context, in *SearchRequest, opts ...grpc.CallOption) (*SearchResponse, error) {
	out := new(SearchResponse)
	err := c.cc.Invoke(ctx, CKyc_Search_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cKycClient) GetData(ctx context.Context, in *GetDataRequest, opts ...grpc.CallOption) (*GetDataResponse, error) {
	out := new(GetDataResponse)
	err := c.cc.Invoke(ctx, CKyc_GetData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cKycClient) VerifyOTP(ctx context.Context, in *VerifyOTPRequest, opts ...grpc.CallOption) (*VerifyOTPResponse, error) {
	out := new(VerifyOTPResponse)
	err := c.cc.Invoke(ctx, CKyc_VerifyOTP_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CKycServer is the server API for CKyc service.
// All implementations should embed UnimplementedCKycServer
// for forward compatibility
type CKycServer interface {
	// Searches the ckyc database and returns ckyc records that match the search
	// criteria. Search criteria include different kyc document identifiers along
	// data like date of birth gender etc.
	Search(context.Context, *SearchRequest) (*SearchResponse, error)
	// GetData is used to initiate the otp verification procedure for first time for a customer
	GetData(context.Context, *GetDataRequest) (*GetDataResponse, error)
	// VerifyOTP is used to verify the otp, resend the otp, if the otp is verified then it returns the data of coustomer as well
	// if otp is present in the request then it will be verified.
	// if otp is empty in the request then it will resend otp
	// https://docs.google.com/document/d/1gn-Jq9O71qrytXjRCnBmh3sbLkHY4Lx0/edit
	VerifyOTP(context.Context, *VerifyOTPRequest) (*VerifyOTPResponse, error)
}

// UnimplementedCKycServer should be embedded to have forward compatible implementations.
type UnimplementedCKycServer struct {
}

func (UnimplementedCKycServer) Search(context.Context, *SearchRequest) (*SearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Search not implemented")
}
func (UnimplementedCKycServer) GetData(context.Context, *GetDataRequest) (*GetDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetData not implemented")
}
func (UnimplementedCKycServer) VerifyOTP(context.Context, *VerifyOTPRequest) (*VerifyOTPResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VerifyOTP not implemented")
}

// UnsafeCKycServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CKycServer will
// result in compilation errors.
type UnsafeCKycServer interface {
	mustEmbedUnimplementedCKycServer()
}

func RegisterCKycServer(s grpc.ServiceRegistrar, srv CKycServer) {
	s.RegisterService(&CKyc_ServiceDesc, srv)
}

func _CKyc_Search_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CKycServer).Search(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CKyc_Search_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CKycServer).Search(ctx, req.(*SearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CKyc_GetData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CKycServer).GetData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CKyc_GetData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CKycServer).GetData(ctx, req.(*GetDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CKyc_VerifyOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VerifyOTPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CKycServer).VerifyOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CKyc_VerifyOTP_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CKycServer).VerifyOTP(ctx, req.(*VerifyOTPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CKyc_ServiceDesc is the grpc.ServiceDesc for CKyc service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CKyc_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.ckyc.CKyc",
	HandlerType: (*CKycServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Search",
			Handler:    _CKyc_Search_Handler,
		},
		{
			MethodName: "GetData",
			Handler:    _CKyc_GetData_Handler,
		},
		{
			MethodName: "VerifyOTP",
			Handler:    _CKyc_VerifyOTP_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/ckyc/service.proto",
}
