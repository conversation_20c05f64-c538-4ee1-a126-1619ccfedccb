// Code generated by MockGen. DO NOT EDIT.
// Source: api/vendorgateway/ckyc/service_grpc.pb.go

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	ckyc "github.com/epifi/gamma/api/vendorgateway/ckyc"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCKycClient is a mock of CKycClient interface.
type MockCKycClient struct {
	ctrl     *gomock.Controller
	recorder *MockCKycClientMockRecorder
}

// MockCKycClientMockRecorder is the mock recorder for MockCKycClient.
type MockCKycClientMockRecorder struct {
	mock *MockCKycClient
}

// NewMockCKycClient creates a new mock instance.
func NewMockCKycClient(ctrl *gomock.Controller) *MockCKycClient {
	mock := &MockCKycClient{ctrl: ctrl}
	mock.recorder = &MockCKycClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCKycClient) EXPECT() *MockCKycClientMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockCKycClient) GetData(ctx context.Context, in *ckyc.GetDataRequest, opts ...grpc.CallOption) (*ckyc.GetDataResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetData", varargs...)
	ret0, _ := ret[0].(*ckyc.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockCKycClientMockRecorder) GetData(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockCKycClient)(nil).GetData), varargs...)
}

// Search mocks base method.
func (m *MockCKycClient) Search(ctx context.Context, in *ckyc.SearchRequest, opts ...grpc.CallOption) (*ckyc.SearchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Search", varargs...)
	ret0, _ := ret[0].(*ckyc.SearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Search indicates an expected call of Search.
func (mr *MockCKycClientMockRecorder) Search(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockCKycClient)(nil).Search), varargs...)
}

// VerifyOTP mocks base method.
func (m *MockCKycClient) VerifyOTP(ctx context.Context, in *ckyc.VerifyOTPRequest, opts ...grpc.CallOption) (*ckyc.VerifyOTPResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VerifyOTP", varargs...)
	ret0, _ := ret[0].(*ckyc.VerifyOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOTP indicates an expected call of VerifyOTP.
func (mr *MockCKycClientMockRecorder) VerifyOTP(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOTP", reflect.TypeOf((*MockCKycClient)(nil).VerifyOTP), varargs...)
}

// MockCKycServer is a mock of CKycServer interface.
type MockCKycServer struct {
	ctrl     *gomock.Controller
	recorder *MockCKycServerMockRecorder
}

// MockCKycServerMockRecorder is the mock recorder for MockCKycServer.
type MockCKycServerMockRecorder struct {
	mock *MockCKycServer
}

// NewMockCKycServer creates a new mock instance.
func NewMockCKycServer(ctrl *gomock.Controller) *MockCKycServer {
	mock := &MockCKycServer{ctrl: ctrl}
	mock.recorder = &MockCKycServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCKycServer) EXPECT() *MockCKycServerMockRecorder {
	return m.recorder
}

// GetData mocks base method.
func (m *MockCKycServer) GetData(arg0 context.Context, arg1 *ckyc.GetDataRequest) (*ckyc.GetDataResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetData", arg0, arg1)
	ret0, _ := ret[0].(*ckyc.GetDataResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetData indicates an expected call of GetData.
func (mr *MockCKycServerMockRecorder) GetData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetData", reflect.TypeOf((*MockCKycServer)(nil).GetData), arg0, arg1)
}

// Search mocks base method.
func (m *MockCKycServer) Search(arg0 context.Context, arg1 *ckyc.SearchRequest) (*ckyc.SearchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Search", arg0, arg1)
	ret0, _ := ret[0].(*ckyc.SearchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Search indicates an expected call of Search.
func (mr *MockCKycServerMockRecorder) Search(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Search", reflect.TypeOf((*MockCKycServer)(nil).Search), arg0, arg1)
}

// VerifyOTP mocks base method.
func (m *MockCKycServer) VerifyOTP(arg0 context.Context, arg1 *ckyc.VerifyOTPRequest) (*ckyc.VerifyOTPResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyOTP", arg0, arg1)
	ret0, _ := ret[0].(*ckyc.VerifyOTPResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VerifyOTP indicates an expected call of VerifyOTP.
func (mr *MockCKycServerMockRecorder) VerifyOTP(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyOTP", reflect.TypeOf((*MockCKycServer)(nil).VerifyOTP), arg0, arg1)
}

// MockUnsafeCKycServer is a mock of UnsafeCKycServer interface.
type MockUnsafeCKycServer struct {
	ctrl     *gomock.Controller
	recorder *MockUnsafeCKycServerMockRecorder
}

// MockUnsafeCKycServerMockRecorder is the mock recorder for MockUnsafeCKycServer.
type MockUnsafeCKycServerMockRecorder struct {
	mock *MockUnsafeCKycServer
}

// NewMockUnsafeCKycServer creates a new mock instance.
func NewMockUnsafeCKycServer(ctrl *gomock.Controller) *MockUnsafeCKycServer {
	mock := &MockUnsafeCKycServer{ctrl: ctrl}
	mock.recorder = &MockUnsafeCKycServerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockUnsafeCKycServer) EXPECT() *MockUnsafeCKycServerMockRecorder {
	return m.recorder
}

// mustEmbedUnimplementedCKycServer mocks base method.
func (m *MockUnsafeCKycServer) mustEmbedUnimplementedCKycServer() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "mustEmbedUnimplementedCKycServer")
}

// mustEmbedUnimplementedCKycServer indicates an expected call of mustEmbedUnimplementedCKycServer.
func (mr *MockUnsafeCKycServerMockRecorder) mustEmbedUnimplementedCKycServer() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "mustEmbedUnimplementedCKycServer", reflect.TypeOf((*MockUnsafeCKycServer)(nil).mustEmbedUnimplementedCKycServer))
}
