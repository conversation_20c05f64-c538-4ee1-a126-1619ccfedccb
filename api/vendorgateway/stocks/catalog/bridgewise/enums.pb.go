// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/stocks/catalog/bridgewise/enums.proto

package bridgewise

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// IdentifierType specifies if the request contains ISIN or ticket_exchange pair
type IdentifierType int32

const (
	IdentifierType_IDENTIFIER_TYPE_UNSPECIFIED     IdentifierType = 0
	IdentifierType_IDENTIFIER_TYPE_ISIN            IdentifierType = 1
	IdentifierType_IDENTIFIER_TYPE_TICKER_EXCHANGE IdentifierType = 2
)

// Enum value maps for IdentifierType.
var (
	IdentifierType_name = map[int32]string{
		0: "IDENTIFIER_TYPE_UNSPECIFIED",
		1: "IDENTIFIER_TYPE_ISIN",
		2: "IDENTIFIER_TYPE_TICKER_EXCHANGE",
	}
	IdentifierType_value = map[string]int32{
		"IDENTIFIER_TYPE_UNSPECIFIED":     0,
		"IDENTIFIER_TYPE_ISIN":            1,
		"IDENTIFIER_TYPE_TICKER_EXCHANGE": 2,
	}
)

func (x IdentifierType) Enum() *IdentifierType {
	p := new(IdentifierType)
	*p = x
	return p
}

func (x IdentifierType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IdentifierType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[0].Descriptor()
}

func (IdentifierType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[0]
}

func (x IdentifierType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IdentifierType.Descriptor instead.
func (IdentifierType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP(), []int{0}
}

// PeriodType specifies if the requested data is needed annually, quarterly etc.
type PeriodType int32

const (
	PeriodType_PERIOD_TYPE_UNSPECIFIED PeriodType = 0
	PeriodType_PERIOD_TYPE_ANNUAL      PeriodType = 1
	PeriodType_PERIOD_TYPE_QUARTER     PeriodType = 2
	PeriodType_PERIOD_TYPE_TTM         PeriodType = 3
)

// Enum value maps for PeriodType.
var (
	PeriodType_name = map[int32]string{
		0: "PERIOD_TYPE_UNSPECIFIED",
		1: "PERIOD_TYPE_ANNUAL",
		2: "PERIOD_TYPE_QUARTER",
		3: "PERIOD_TYPE_TTM",
	}
	PeriodType_value = map[string]int32{
		"PERIOD_TYPE_UNSPECIFIED": 0,
		"PERIOD_TYPE_ANNUAL":      1,
		"PERIOD_TYPE_QUARTER":     2,
		"PERIOD_TYPE_TTM":         3,
	}
)

func (x PeriodType) Enum() *PeriodType {
	p := new(PeriodType)
	*p = x
	return p
}

func (x PeriodType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PeriodType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[1].Descriptor()
}

func (PeriodType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[1]
}

func (x PeriodType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PeriodType.Descriptor instead.
func (PeriodType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP(), []int{1}
}

// SecurityAssetType specifies if the security is a stock or fund(ETF)
type SecurityAssetType int32

const (
	SecurityAssetType_SECURITY_ASSET_TYPE_UNSPECIFIED SecurityAssetType = 0
	SecurityAssetType_SECURITY_ASSET_TYPE_STOCK       SecurityAssetType = 1
	// FUND refers to ETF
	SecurityAssetType_SECURITY_ASSET_TYPE_FUND SecurityAssetType = 2
)

// Enum value maps for SecurityAssetType.
var (
	SecurityAssetType_name = map[int32]string{
		0: "SECURITY_ASSET_TYPE_UNSPECIFIED",
		1: "SECURITY_ASSET_TYPE_STOCK",
		2: "SECURITY_ASSET_TYPE_FUND",
	}
	SecurityAssetType_value = map[string]int32{
		"SECURITY_ASSET_TYPE_UNSPECIFIED": 0,
		"SECURITY_ASSET_TYPE_STOCK":       1,
		"SECURITY_ASSET_TYPE_FUND":        2,
	}
)

func (x SecurityAssetType) Enum() *SecurityAssetType {
	p := new(SecurityAssetType)
	*p = x
	return p
}

func (x SecurityAssetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityAssetType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[2].Descriptor()
}

func (SecurityAssetType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[2]
}

func (x SecurityAssetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityAssetType.Descriptor instead.
func (SecurityAssetType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP(), []int{2}
}

// SecurityParagraphType refers to the different types of paragraphs related to a security, e.g, Description, Fundamentals etc.
// Add additional enums here if other types of paragraphs are needed
type SecurityParagraphType int32

const (
	SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_UNSPECIFIED SecurityParagraphType = 0
	SecurityParagraphType_SECURITY_PARAGRAPH_TYPE_DESCRIPTION SecurityParagraphType = 1
)

// Enum value maps for SecurityParagraphType.
var (
	SecurityParagraphType_name = map[int32]string{
		0: "SECURITY_PARAGRAPH_TYPE_UNSPECIFIED",
		1: "SECURITY_PARAGRAPH_TYPE_DESCRIPTION",
	}
	SecurityParagraphType_value = map[string]int32{
		"SECURITY_PARAGRAPH_TYPE_UNSPECIFIED": 0,
		"SECURITY_PARAGRAPH_TYPE_DESCRIPTION": 1,
	}
)

func (x SecurityParagraphType) Enum() *SecurityParagraphType {
	p := new(SecurityParagraphType)
	*p = x
	return p
}

func (x SecurityParagraphType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityParagraphType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[3].Descriptor()
}

func (SecurityParagraphType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[3]
}

func (x SecurityParagraphType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityParagraphType.Descriptor instead.
func (SecurityParagraphType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP(), []int{3}
}

// SecurityParameterType refers to the different types of analytical metrics related to a security, e.g, PE ratio, PB ratio
// Add additional enums here if other types of parameters are needed
type SecurityParameterType int32

const (
	SecurityParameterType_SECURITY_PARAMETER_TYPE_UNSPECIFIED               SecurityParameterType = 0
	SecurityParameterType_SECURITY_PARAMETER_TYPE_BOOK_VALUE_PER_SHARE      SecurityParameterType = 1
	SecurityParameterType_SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD            SecurityParameterType = 2
	SecurityParameterType_SECURITY_PARAMETER_TYPE_PE_RATIO                  SecurityParameterType = 3
	SecurityParameterType_SECURITY_PARAMETER_TYPE_PB_RATIO                  SecurityParameterType = 4
	SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY          SecurityParameterType = 5
	SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARES_OUTSTANDING        SecurityParameterType = 6
	SecurityParameterType_SECURITY_PARAMETER_TYPE_SHARPE_RATIO              SecurityParameterType = 7
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE SecurityParameterType = 8
	SecurityParameterType_SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE  SecurityParameterType = 9
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_REVENUE             SecurityParameterType = 10
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_EXPENSES            SecurityParameterType = 11
	SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_PROFIT              SecurityParameterType = 12
	SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_INCOME                SecurityParameterType = 13
	SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA                    SecurityParameterType = 14
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_ASSETS              SecurityParameterType = 15
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_LIABILITIES         SecurityParameterType = 16
	SecurityParameterType_SECURITY_PARAMETER_TYPE_CURRENT_RATIO             SecurityParameterType = 17
	SecurityParameterType_SECURITY_PARAMETER_TYPE_FREE_CASH_FLOW            SecurityParameterType = 18
	SecurityParameterType_SECURITY_PARAMETER_TYPE_OPERATING_CASH_FLOW       SecurityParameterType = 19
	SecurityParameterType_SECURITY_PARAMETER_TYPE_INVESTING_CASH_FLOW       SecurityParameterType = 20
	SecurityParameterType_SECURITY_PARAMETER_TYPE_FINANCING_CASH_FLOW       SecurityParameterType = 21
	SecurityParameterType_SECURITY_PARAMETER_TYPE_GROSS_MARGIN              SecurityParameterType = 22
	SecurityParameterType_SECURITY_PARAMETER_TYPE_EBITDA_MARGIN             SecurityParameterType = 23
	SecurityParameterType_SECURITY_PARAMETER_TYPE_NET_MARGIN                SecurityParameterType = 24
	SecurityParameterType_SECURITY_PARAMETER_TYPE_RETURN_ON_TOTAL_CAPITAL   SecurityParameterType = 25
	SecurityParameterType_SECURITY_PARAMETER_TYPE_TOTAL_DEBT_TO_EQUITY      SecurityParameterType = 26
	SecurityParameterType_SECURITY_PARAMETER_TYPE_DILUTED_EPS_GROWTH        SecurityParameterType = 27
	SecurityParameterType_SECURITY_PARAMETER_TYPE_REVENUE_GROWTH            SecurityParameterType = 28
	SecurityParameterType_SECURITY_PARAMETER_TYPE_EV_TO_EBITDA              SecurityParameterType = 29
)

// Enum value maps for SecurityParameterType.
var (
	SecurityParameterType_name = map[int32]string{
		0:  "SECURITY_PARAMETER_TYPE_UNSPECIFIED",
		1:  "SECURITY_PARAMETER_TYPE_BOOK_VALUE_PER_SHARE",
		2:  "SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD",
		3:  "SECURITY_PARAMETER_TYPE_PE_RATIO",
		4:  "SECURITY_PARAMETER_TYPE_PB_RATIO",
		5:  "SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY",
		6:  "SECURITY_PARAMETER_TYPE_SHARES_OUTSTANDING",
		7:  "SECURITY_PARAMETER_TYPE_SHARPE_RATIO",
		8:  "SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE",
		9:  "SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE",
		10: "SECURITY_PARAMETER_TYPE_TOTAL_REVENUE",
		11: "SECURITY_PARAMETER_TYPE_TOTAL_EXPENSES",
		12: "SECURITY_PARAMETER_TYPE_GROSS_PROFIT",
		13: "SECURITY_PARAMETER_TYPE_NET_INCOME",
		14: "SECURITY_PARAMETER_TYPE_EBITDA",
		15: "SECURITY_PARAMETER_TYPE_TOTAL_ASSETS",
		16: "SECURITY_PARAMETER_TYPE_TOTAL_LIABILITIES",
		17: "SECURITY_PARAMETER_TYPE_CURRENT_RATIO",
		18: "SECURITY_PARAMETER_TYPE_FREE_CASH_FLOW",
		19: "SECURITY_PARAMETER_TYPE_OPERATING_CASH_FLOW",
		20: "SECURITY_PARAMETER_TYPE_INVESTING_CASH_FLOW",
		21: "SECURITY_PARAMETER_TYPE_FINANCING_CASH_FLOW",
		22: "SECURITY_PARAMETER_TYPE_GROSS_MARGIN",
		23: "SECURITY_PARAMETER_TYPE_EBITDA_MARGIN",
		24: "SECURITY_PARAMETER_TYPE_NET_MARGIN",
		25: "SECURITY_PARAMETER_TYPE_RETURN_ON_TOTAL_CAPITAL",
		26: "SECURITY_PARAMETER_TYPE_TOTAL_DEBT_TO_EQUITY",
		27: "SECURITY_PARAMETER_TYPE_DILUTED_EPS_GROWTH",
		28: "SECURITY_PARAMETER_TYPE_REVENUE_GROWTH",
		29: "SECURITY_PARAMETER_TYPE_EV_TO_EBITDA",
	}
	SecurityParameterType_value = map[string]int32{
		"SECURITY_PARAMETER_TYPE_UNSPECIFIED":               0,
		"SECURITY_PARAMETER_TYPE_BOOK_VALUE_PER_SHARE":      1,
		"SECURITY_PARAMETER_TYPE_DIVIDEND_YIELD":            2,
		"SECURITY_PARAMETER_TYPE_PE_RATIO":                  3,
		"SECURITY_PARAMETER_TYPE_PB_RATIO":                  4,
		"SECURITY_PARAMETER_TYPE_RETURN_ON_EQUITY":          5,
		"SECURITY_PARAMETER_TYPE_SHARES_OUTSTANDING":        6,
		"SECURITY_PARAMETER_TYPE_SHARPE_RATIO":              7,
		"SECURITY_PARAMETER_TYPE_TRACKING_ERROR_PERCENTAGE": 8,
		"SECURITY_PARAMETER_TYPE_EXPENSE_RATIO_PERCENTAGE":  9,
		"SECURITY_PARAMETER_TYPE_TOTAL_REVENUE":             10,
		"SECURITY_PARAMETER_TYPE_TOTAL_EXPENSES":            11,
		"SECURITY_PARAMETER_TYPE_GROSS_PROFIT":              12,
		"SECURITY_PARAMETER_TYPE_NET_INCOME":                13,
		"SECURITY_PARAMETER_TYPE_EBITDA":                    14,
		"SECURITY_PARAMETER_TYPE_TOTAL_ASSETS":              15,
		"SECURITY_PARAMETER_TYPE_TOTAL_LIABILITIES":         16,
		"SECURITY_PARAMETER_TYPE_CURRENT_RATIO":             17,
		"SECURITY_PARAMETER_TYPE_FREE_CASH_FLOW":            18,
		"SECURITY_PARAMETER_TYPE_OPERATING_CASH_FLOW":       19,
		"SECURITY_PARAMETER_TYPE_INVESTING_CASH_FLOW":       20,
		"SECURITY_PARAMETER_TYPE_FINANCING_CASH_FLOW":       21,
		"SECURITY_PARAMETER_TYPE_GROSS_MARGIN":              22,
		"SECURITY_PARAMETER_TYPE_EBITDA_MARGIN":             23,
		"SECURITY_PARAMETER_TYPE_NET_MARGIN":                24,
		"SECURITY_PARAMETER_TYPE_RETURN_ON_TOTAL_CAPITAL":   25,
		"SECURITY_PARAMETER_TYPE_TOTAL_DEBT_TO_EQUITY":      26,
		"SECURITY_PARAMETER_TYPE_DILUTED_EPS_GROWTH":        27,
		"SECURITY_PARAMETER_TYPE_REVENUE_GROWTH":            28,
		"SECURITY_PARAMETER_TYPE_EV_TO_EBITDA":              29,
	}
)

func (x SecurityParameterType) Enum() *SecurityParameterType {
	p := new(SecurityParameterType)
	*p = x
	return p
}

func (x SecurityParameterType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityParameterType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[4].Descriptor()
}

func (SecurityParameterType) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes[4]
}

func (x SecurityParameterType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityParameterType.Descriptor instead.
func (SecurityParameterType) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP(), []int{4}
}

var File_api_vendorgateway_stocks_catalog_bridgewise_enums_proto protoreflect.FileDescriptor

var file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDesc = []byte{
	0x0a, 0x37, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x63, 0x61, 0x74, 0x61, 0x6c,
	0x6f, 0x67, 0x2f, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x77, 0x69, 0x73, 0x65, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x77,
	0x69, 0x73, 0x65, 0x2a, 0x70, 0x0a, 0x0e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65,
	0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46,
	0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49,
	0x46, 0x49, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x49, 0x53, 0x49, 0x4e, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x49, 0x44, 0x45, 0x4e, 0x54, 0x49, 0x46, 0x49, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x54, 0x49, 0x43, 0x4b, 0x45, 0x52, 0x5f, 0x45, 0x58, 0x43, 0x48, 0x41,
	0x4e, 0x47, 0x45, 0x10, 0x02, 0x2a, 0x6f, 0x0a, 0x0a, 0x50, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x41, 0x4e, 0x4e, 0x55, 0x41, 0x4c, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x45, 0x52, 0x49,
	0x4f, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x51, 0x55, 0x41, 0x52, 0x54, 0x45, 0x52, 0x10,
	0x02, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x45, 0x52, 0x49, 0x4f, 0x44, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x54, 0x4d, 0x10, 0x03, 0x2a, 0x75, 0x0a, 0x11, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x41, 0x73, 0x73, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x23, 0x0a, 0x1f, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x1d, 0x0a, 0x19, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x53, 0x53,
	0x45, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53, 0x54, 0x4f, 0x43, 0x4b, 0x10, 0x01, 0x12,
	0x1c, 0x0a, 0x18, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x41, 0x53, 0x53, 0x45,
	0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x2a, 0x69, 0x0a,
	0x15, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x67, 0x72, 0x61,
	0x70, 0x68, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49,
	0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x47, 0x52, 0x41, 0x50, 0x48, 0x5f, 0x54, 0x59, 0x50,
	0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x27, 0x0a, 0x23, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x47, 0x52, 0x41, 0x50, 0x48, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x45, 0x53, 0x43, 0x52,
	0x49, 0x50, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x2a, 0xd9, 0x0a, 0x0a, 0x15, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x27, 0x0a, 0x23, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50,
	0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x30, 0x0a, 0x2c, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x42, 0x4f, 0x4f, 0x4b, 0x5f, 0x56, 0x41, 0x4c, 0x55,
	0x45, 0x5f, 0x50, 0x45, 0x52, 0x5f, 0x53, 0x48, 0x41, 0x52, 0x45, 0x10, 0x01, 0x12, 0x2a, 0x0a,
	0x26, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45,
	0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x56, 0x49, 0x44, 0x45, 0x4e,
	0x44, 0x5f, 0x59, 0x49, 0x45, 0x4c, 0x44, 0x10, 0x02, 0x12, 0x24, 0x0a, 0x20, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x03, 0x12,
	0x24, 0x0a, 0x20, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x50, 0x42, 0x5f, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x10, 0x04, 0x12, 0x2c, 0x0a, 0x28, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4f, 0x4e, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54,
	0x59, 0x10, 0x05, 0x12, 0x2e, 0x0a, 0x2a, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x48, 0x41, 0x52, 0x45, 0x53, 0x5f, 0x4f, 0x55, 0x54, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x06, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x53,
	0x48, 0x41, 0x52, 0x50, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x07, 0x12, 0x35, 0x0a,
	0x31, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45,
	0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x43, 0x4b, 0x49, 0x4e,
	0x47, 0x5f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x5f, 0x50, 0x45, 0x52, 0x43, 0x45, 0x4e, 0x54, 0x41,
	0x47, 0x45, 0x10, 0x08, 0x12, 0x34, 0x0a, 0x30, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x5f, 0x50, 0x45,
	0x52, 0x43, 0x45, 0x4e, 0x54, 0x41, 0x47, 0x45, 0x10, 0x09, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x52, 0x45, 0x56, 0x45,
	0x4e, 0x55, 0x45, 0x10, 0x0a, 0x12, 0x2a, 0x0a, 0x26, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54,
	0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45,
	0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x4e, 0x53, 0x45, 0x53, 0x10,
	0x0b, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x52, 0x4f,
	0x53, 0x53, 0x5f, 0x50, 0x52, 0x4f, 0x46, 0x49, 0x54, 0x10, 0x0c, 0x12, 0x26, 0x0a, 0x22, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x49, 0x4e, 0x43, 0x4f, 0x4d,
	0x45, 0x10, 0x0d, 0x12, 0x22, 0x0a, 0x1e, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f,
	0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45,
	0x42, 0x49, 0x54, 0x44, 0x41, 0x10, 0x0e, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x41, 0x53, 0x53, 0x45, 0x54, 0x53, 0x10,
	0x0f, 0x12, 0x2d, 0x0a, 0x29, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41,
	0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x54,
	0x41, 0x4c, 0x5f, 0x4c, 0x49, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x49, 0x45, 0x53, 0x10, 0x10,
	0x12, 0x29, 0x0a, 0x25, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52,
	0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x55, 0x52, 0x52,
	0x45, 0x4e, 0x54, 0x5f, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x10, 0x11, 0x12, 0x2a, 0x0a, 0x26, 0x53,
	0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45,
	0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x52, 0x45, 0x45, 0x5f, 0x43, 0x41, 0x53, 0x48,
	0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x12, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41, 0x53,
	0x48, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x13, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x45, 0x43, 0x55,
	0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x49, 0x4e, 0x56, 0x45, 0x53, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x43, 0x41,
	0x53, 0x48, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x14, 0x12, 0x2f, 0x0a, 0x2b, 0x53, 0x45, 0x43,
	0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f,
	0x54, 0x59, 0x50, 0x45, 0x5f, 0x46, 0x49, 0x4e, 0x41, 0x4e, 0x43, 0x49, 0x4e, 0x47, 0x5f, 0x43,
	0x41, 0x53, 0x48, 0x5f, 0x46, 0x4c, 0x4f, 0x57, 0x10, 0x15, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x47, 0x52, 0x4f, 0x53, 0x53, 0x5f, 0x4d, 0x41, 0x52, 0x47,
	0x49, 0x4e, 0x10, 0x16, 0x12, 0x29, 0x0a, 0x25, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59,
	0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x45, 0x42, 0x49, 0x54, 0x44, 0x41, 0x5f, 0x4d, 0x41, 0x52, 0x47, 0x49, 0x4e, 0x10, 0x17, 0x12,
	0x26, 0x0a, 0x22, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41,
	0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x45, 0x54, 0x5f, 0x4d,
	0x41, 0x52, 0x47, 0x49, 0x4e, 0x10, 0x18, 0x12, 0x33, 0x0a, 0x2f, 0x53, 0x45, 0x43, 0x55, 0x52,
	0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x52, 0x45, 0x54, 0x55, 0x52, 0x4e, 0x5f, 0x4f, 0x4e, 0x5f, 0x54, 0x4f, 0x54,
	0x41, 0x4c, 0x5f, 0x43, 0x41, 0x50, 0x49, 0x54, 0x41, 0x4c, 0x10, 0x19, 0x12, 0x30, 0x0a, 0x2c,
	0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54,
	0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x54, 0x4f, 0x54, 0x41, 0x4c, 0x5f, 0x44, 0x45,
	0x42, 0x54, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x51, 0x55, 0x49, 0x54, 0x59, 0x10, 0x1a, 0x12, 0x2e,
	0x0a, 0x2a, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x44, 0x49, 0x4c, 0x55, 0x54, 0x45,
	0x44, 0x5f, 0x45, 0x50, 0x53, 0x5f, 0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x10, 0x1b, 0x12, 0x2a,
	0x0a, 0x26, 0x53, 0x45, 0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d,
	0x45, 0x54, 0x45, 0x52, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x52, 0x45, 0x56, 0x45, 0x4e, 0x55,
	0x45, 0x5f, 0x47, 0x52, 0x4f, 0x57, 0x54, 0x48, 0x10, 0x1c, 0x12, 0x28, 0x0a, 0x24, 0x53, 0x45,
	0x43, 0x55, 0x52, 0x49, 0x54, 0x59, 0x5f, 0x50, 0x41, 0x52, 0x41, 0x4d, 0x45, 0x54, 0x45, 0x52,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x45, 0x56, 0x5f, 0x54, 0x4f, 0x5f, 0x45, 0x42, 0x49, 0x54,
	0x44, 0x41, 0x10, 0x1d, 0x42, 0x88, 0x01, 0x0a, 0x42, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2e, 0x63, 0x61, 0x74, 0x61, 0x6c, 0x6f, 0x67,
	0x2e, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x77, 0x69, 0x73, 0x65, 0x5a, 0x42, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61,
	0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x73, 0x74, 0x6f, 0x63, 0x6b, 0x73, 0x2f, 0x63, 0x61, 0x74,
	0x61, 0x6c, 0x6f, 0x67, 0x2f, 0x62, 0x72, 0x69, 0x64, 0x67, 0x65, 0x77, 0x69, 0x73, 0x65, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescOnce sync.Once
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescData = file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDesc
)

func file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescData)
	})
	return file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDescData
}

var file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_goTypes = []interface{}{
	(IdentifierType)(0),        // 0: vendorgateway.bridgewise.IdentifierType
	(PeriodType)(0),            // 1: vendorgateway.bridgewise.PeriodType
	(SecurityAssetType)(0),     // 2: vendorgateway.bridgewise.SecurityAssetType
	(SecurityParagraphType)(0), // 3: vendorgateway.bridgewise.SecurityParagraphType
	(SecurityParameterType)(0), // 4: vendorgateway.bridgewise.SecurityParameterType
}
var file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_init() }
func file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_init() {
	if File_api_vendorgateway_stocks_catalog_bridgewise_enums_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDesc,
			NumEnums:      5,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_enumTypes,
	}.Build()
	File_api_vendorgateway_stocks_catalog_bridgewise_enums_proto = out.File
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_rawDesc = nil
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_goTypes = nil
	file_api_vendorgateway_stocks_catalog_bridgewise_enums_proto_depIdxs = nil
}
