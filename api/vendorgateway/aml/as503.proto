syntax = "proto3";

package vendorgateway.aml;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aml";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aml";

// AS503 API - Specific Case ID Details API for vendorgateway
// This API provides details of the specific Case ID passed in the request

// Request message for AS503 API in vendorgateway

// Case category enum (reused from AS502)
enum CaseCategory {
  CASE_CATEGORY_UNSPECIFIED = 0;
  CASE_CATEGORY_OPEN = 1;
  CASE_CATEGORY_PENDING = 2;
  CASE_CATEGORY_COMPLETED = 3;
}

// Case type enum (reused from AS502)
enum CaseType {
  CASE_TYPE_UNSPECIFIED = 0;
  CASE_TYPE_INITIAL = 1;
  CASE_TYPE_WATCHLIST_ADDED = 2;
}

// Initial screening mode enum (reused from AS502)
enum InitialScreeningMode {
  INITIAL_SCREENING_MODE_UNSPECIFIED = 0;
  INITIAL_SCREENING_MODE_API = 1;
  INITIAL_SCREENING_MODE_LOOKUP_IN_BULK = 2;
}

// Onboarding decision enum (reused from AS502)
enum OnboardingDecision {
  ONBOARDING_DECISION_UNSPECIFIED = 0;
  ONBOARDING_DECISION_PROCEED = 1;
  ONBOARDING_DECISION_DECLINE = 2;
}

// Case of enum (reused from AS502)
enum CaseOf {
  CASE_OF_UNSPECIFIED = 0;
  CASE_OF_MAIN_KYC = 1;
  CASE_OF_RELATED_PERSON_KYC = 2;
}

// Match type enum (reused from AS502)
enum MatchType {
  MATCH_TYPE_UNSPECIFIED = 0;
  MATCH_TYPE_CONFIRMED = 1;
  MATCH_TYPE_PROBABLE = 2;
}

// Alert decision enum (reused from AS502)
enum AlertDecision {
  ALERT_DECISION_UNSPECIFIED = 0;
  ALERT_DECISION_TRUE_MATCH = 1;
  ALERT_DECISION_NO_MATCH = 2;
  ALERT_DECISION_PENDING = 3;
}

// Case details structure with report data for AS503
message CaseDetailsWithReport {
  string case_id = 1;
  google.protobuf.Timestamp case_creation_date_time = 2;
  string source_system_name = 3;
  string source_system_customer_code = 4;
  string application_ref_number = 5;
  CaseOf case_of = 6;
  string linked_to_source_system_customer_code = 7;
  string relation = 8;
  string screening_profile = 9;
  string screening_profile_name = 10;
  string customer_name = 11;
  CaseType case_type = 12;
  InitialScreeningMode initial_screening_mode = 13;
  OnboardingDecision onboarding_decision = 14;
  int32 total_alert_count = 15;
  int32 confirmed_alert_count = 16;
  int32 probable_alert_count = 17;
  int32 pending_for_decision = 18;
  int32 no_match_count = 19;
  int32 true_match_count = 20;
  string case_stage = 21;
  CaseCategory case_category = 22;
  string current_assignee = 23;
  google.protobuf.Timestamp case_closure_date_time = 24;
  string final_remarks = 25;
  repeated CaseAction case_actions = 26;
  string report_data_in_base64 = 27;
  repeated AlertDetails alert_details = 28;
}

// Case action structure
message CaseAction {
  string user_name = 1;
  google.protobuf.Timestamp date_time = 2;
  string action = 3;
}

// Alert details structure
message AlertDetails {
  string alert_id = 1;
  string source = 2;
  string watchlist_source_id = 3;
  MatchType match_type = 4;
  repeated string matching_attributes = 5;
  repeated SourceIdentification source_identification = 6;
  string watchlist_name = 7;
  AlertDecision alert_decision = 8;
  repeated Comment comments = 9;
}

// Source identification structure
message SourceIdentification {
  string source_identification_id = 1;
  string source_identification_key = 2;
  string source_identification_value = 3;
}

// Comment structure
message Comment {
  string user_name = 1;
  google.protobuf.Timestamp date_time = 2;
  string comment = 3;
}
