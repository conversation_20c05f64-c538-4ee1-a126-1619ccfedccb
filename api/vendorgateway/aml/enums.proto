syntax = "proto3";

package vendorgateway.aml;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aml";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aml";

enum CaseCategory {
  CASE_CATEGORY_UNSPECIFIED = 0;
  CASE_CATEGORY_OPEN = 1;
  CASE_CATEGORY_PENDING = 2;
  CASE_CATEGORY_COMPLETED = 3;
}

enum CaseType {
  CASE_TYPE_UNSPECIFIED = 0;
  CASE_TYPE_INITIAL = 1;
  CASE_TYPE_WATCHLIST_ADDED = 2;
}
