// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/lenden/service.proto

package lenden

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Lenden_CreateUser_FullMethodName                = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/CreateUser"
	Lenden_ApplyForLoan_FullMethodName              = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/ApplyForLoan"
	Lenden_CheckHardEligibility_FullMethodName      = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/CheckHardEligibility"
	Lenden_SelectOffer_FullMethodName               = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/SelectOffer"
	Lenden_ModifyRateOfInterest_FullMethodName      = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/ModifyRateOfInterest"
	Lenden_KycInit_FullMethodName                   = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/KycInit"
	Lenden_CheckKycStatus_FullMethodName            = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/CheckKycStatus"
	Lenden_AddBankDetails_FullMethodName            = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/AddBankDetails"
	Lenden_InitMandate_FullMethodName               = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/InitMandate"
	Lenden_CheckMandateStatus_FullMethodName        = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/CheckMandateStatus"
	Lenden_GenerateKfsLa_FullMethodName             = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GenerateKfsLa"
	Lenden_SignKfsLa_FullMethodName                 = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/SignKfsLa"
	Lenden_GetLoanDetails_FullMethodName            = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GetLoanDetails"
	Lenden_GetPreDisbursementDetails_FullMethodName = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GetPreDisbursementDetails"
	Lenden_PostExternalData_FullMethodName          = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/PostExternalData"
	Lenden_GetAmortizationSchedule_FullMethodName   = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GetAmortizationSchedule"
	Lenden_GetForeclosureDetails_FullMethodName     = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GetForeclosureDetails"
	Lenden_GeneratePaymentLink_FullMethodName       = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GeneratePaymentLink"
	Lenden_GetPaymentStatus_FullMethodName          = "/vendorgateway.lending.preapprovedloan.lenden.Lenden/GetPaymentStatus"
)

// LendenClient is the client API for Lenden service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LendenClient interface {
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error)
	ApplyForLoan(ctx context.Context, in *ApplyForLoanRequest, opts ...grpc.CallOption) (*ApplyForLoanResponse, error)
	CheckHardEligibility(ctx context.Context, in *CheckHardEligibilityRequest, opts ...grpc.CallOption) (*CheckHardEligibilityResponse, error)
	SelectOffer(ctx context.Context, in *SelectOfferRequest, opts ...grpc.CallOption) (*SelectOfferResponse, error)
	ModifyRateOfInterest(ctx context.Context, in *ModifyRateOfInterestRequest, opts ...grpc.CallOption) (*ModifyRateOfInterestResponse, error)
	KycInit(ctx context.Context, in *KycInitRequest, opts ...grpc.CallOption) (*KycInitResponse, error)
	CheckKycStatus(ctx context.Context, in *CheckKycStatusRequest, opts ...grpc.CallOption) (*CheckKycStatusResponse, error)
	AddBankDetails(ctx context.Context, in *AddBankDetailsRequest, opts ...grpc.CallOption) (*AddBankDetailsResponse, error)
	InitMandate(ctx context.Context, in *InitMandateRequest, opts ...grpc.CallOption) (*InitMandateResponse, error)
	CheckMandateStatus(ctx context.Context, in *CheckMandateStatusRequest, opts ...grpc.CallOption) (*CheckMandateStatusResponse, error)
	GenerateKfsLa(ctx context.Context, in *GenerateKfsLaRequest, opts ...grpc.CallOption) (*GenerateKfsLaResponse, error)
	SignKfsLa(ctx context.Context, in *SignKfsLaRequest, opts ...grpc.CallOption) (*SignKfsLaResponse, error)
	GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error)
	GetPreDisbursementDetails(ctx context.Context, in *GetPreDisbursementDetailsRequest, opts ...grpc.CallOption) (*GetPreDisbursementDetailsResponse, error)
	PostExternalData(ctx context.Context, in *PostExternalDataRequest, opts ...grpc.CallOption) (*PostExternalDataResponse, error)
	GetAmortizationSchedule(ctx context.Context, in *GetAmortizationScheduleRequest, opts ...grpc.CallOption) (*GetAmortizationScheduleResponse, error)
	// GetForeclosureDetails returns details like the amount needed to close a loan before its due date
	// with breakup of principal, interest, foreclosure charges etc.
	// RBI guidelines require lenders to allow users to cancel the loan in the first few days post disbursal
	// by paying back the disbursed amount without any interest charges. This period is also called as the cool-off period.
	// When a loan is in cool-off period, GetForeclosureDetails should be called with purpose as PURPOSE_COOL_OFF
	// to get the amount needed to cancel the loan. Typically, in this period the foreclosure charges will be 0.
	GetForeclosureDetails(ctx context.Context, in *GetForeclosureDetailsRequest, opts ...grpc.CallOption) (*GetForeclosureDetailsResponse, error)
	GeneratePaymentLink(ctx context.Context, in *GeneratePaymentLinkRequest, opts ...grpc.CallOption) (*GeneratePaymentLinkResponse, error)
	GetPaymentStatus(ctx context.Context, in *GetPaymentStatusRequest, opts ...grpc.CallOption) (*GetPaymentStatusResponse, error)
}

type lendenClient struct {
	cc grpc.ClientConnInterface
}

func NewLendenClient(cc grpc.ClientConnInterface) LendenClient {
	return &lendenClient{cc}
}

func (c *lendenClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error) {
	out := new(CreateUserResponse)
	err := c.cc.Invoke(ctx, Lenden_CreateUser_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) ApplyForLoan(ctx context.Context, in *ApplyForLoanRequest, opts ...grpc.CallOption) (*ApplyForLoanResponse, error) {
	out := new(ApplyForLoanResponse)
	err := c.cc.Invoke(ctx, Lenden_ApplyForLoan_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) CheckHardEligibility(ctx context.Context, in *CheckHardEligibilityRequest, opts ...grpc.CallOption) (*CheckHardEligibilityResponse, error) {
	out := new(CheckHardEligibilityResponse)
	err := c.cc.Invoke(ctx, Lenden_CheckHardEligibility_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) SelectOffer(ctx context.Context, in *SelectOfferRequest, opts ...grpc.CallOption) (*SelectOfferResponse, error) {
	out := new(SelectOfferResponse)
	err := c.cc.Invoke(ctx, Lenden_SelectOffer_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) ModifyRateOfInterest(ctx context.Context, in *ModifyRateOfInterestRequest, opts ...grpc.CallOption) (*ModifyRateOfInterestResponse, error) {
	out := new(ModifyRateOfInterestResponse)
	err := c.cc.Invoke(ctx, Lenden_ModifyRateOfInterest_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) KycInit(ctx context.Context, in *KycInitRequest, opts ...grpc.CallOption) (*KycInitResponse, error) {
	out := new(KycInitResponse)
	err := c.cc.Invoke(ctx, Lenden_KycInit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) CheckKycStatus(ctx context.Context, in *CheckKycStatusRequest, opts ...grpc.CallOption) (*CheckKycStatusResponse, error) {
	out := new(CheckKycStatusResponse)
	err := c.cc.Invoke(ctx, Lenden_CheckKycStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) AddBankDetails(ctx context.Context, in *AddBankDetailsRequest, opts ...grpc.CallOption) (*AddBankDetailsResponse, error) {
	out := new(AddBankDetailsResponse)
	err := c.cc.Invoke(ctx, Lenden_AddBankDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) InitMandate(ctx context.Context, in *InitMandateRequest, opts ...grpc.CallOption) (*InitMandateResponse, error) {
	out := new(InitMandateResponse)
	err := c.cc.Invoke(ctx, Lenden_InitMandate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) CheckMandateStatus(ctx context.Context, in *CheckMandateStatusRequest, opts ...grpc.CallOption) (*CheckMandateStatusResponse, error) {
	out := new(CheckMandateStatusResponse)
	err := c.cc.Invoke(ctx, Lenden_CheckMandateStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GenerateKfsLa(ctx context.Context, in *GenerateKfsLaRequest, opts ...grpc.CallOption) (*GenerateKfsLaResponse, error) {
	out := new(GenerateKfsLaResponse)
	err := c.cc.Invoke(ctx, Lenden_GenerateKfsLa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) SignKfsLa(ctx context.Context, in *SignKfsLaRequest, opts ...grpc.CallOption) (*SignKfsLaResponse, error) {
	out := new(SignKfsLaResponse)
	err := c.cc.Invoke(ctx, Lenden_SignKfsLa_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GetLoanDetails(ctx context.Context, in *GetLoanDetailsRequest, opts ...grpc.CallOption) (*GetLoanDetailsResponse, error) {
	out := new(GetLoanDetailsResponse)
	err := c.cc.Invoke(ctx, Lenden_GetLoanDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GetPreDisbursementDetails(ctx context.Context, in *GetPreDisbursementDetailsRequest, opts ...grpc.CallOption) (*GetPreDisbursementDetailsResponse, error) {
	out := new(GetPreDisbursementDetailsResponse)
	err := c.cc.Invoke(ctx, Lenden_GetPreDisbursementDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) PostExternalData(ctx context.Context, in *PostExternalDataRequest, opts ...grpc.CallOption) (*PostExternalDataResponse, error) {
	out := new(PostExternalDataResponse)
	err := c.cc.Invoke(ctx, Lenden_PostExternalData_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GetAmortizationSchedule(ctx context.Context, in *GetAmortizationScheduleRequest, opts ...grpc.CallOption) (*GetAmortizationScheduleResponse, error) {
	out := new(GetAmortizationScheduleResponse)
	err := c.cc.Invoke(ctx, Lenden_GetAmortizationSchedule_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GetForeclosureDetails(ctx context.Context, in *GetForeclosureDetailsRequest, opts ...grpc.CallOption) (*GetForeclosureDetailsResponse, error) {
	out := new(GetForeclosureDetailsResponse)
	err := c.cc.Invoke(ctx, Lenden_GetForeclosureDetails_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GeneratePaymentLink(ctx context.Context, in *GeneratePaymentLinkRequest, opts ...grpc.CallOption) (*GeneratePaymentLinkResponse, error) {
	out := new(GeneratePaymentLinkResponse)
	err := c.cc.Invoke(ctx, Lenden_GeneratePaymentLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lendenClient) GetPaymentStatus(ctx context.Context, in *GetPaymentStatusRequest, opts ...grpc.CallOption) (*GetPaymentStatusResponse, error) {
	out := new(GetPaymentStatusResponse)
	err := c.cc.Invoke(ctx, Lenden_GetPaymentStatus_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LendenServer is the server API for Lenden service.
// All implementations should embed UnimplementedLendenServer
// for forward compatibility
type LendenServer interface {
	CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error)
	ApplyForLoan(context.Context, *ApplyForLoanRequest) (*ApplyForLoanResponse, error)
	CheckHardEligibility(context.Context, *CheckHardEligibilityRequest) (*CheckHardEligibilityResponse, error)
	SelectOffer(context.Context, *SelectOfferRequest) (*SelectOfferResponse, error)
	ModifyRateOfInterest(context.Context, *ModifyRateOfInterestRequest) (*ModifyRateOfInterestResponse, error)
	KycInit(context.Context, *KycInitRequest) (*KycInitResponse, error)
	CheckKycStatus(context.Context, *CheckKycStatusRequest) (*CheckKycStatusResponse, error)
	AddBankDetails(context.Context, *AddBankDetailsRequest) (*AddBankDetailsResponse, error)
	InitMandate(context.Context, *InitMandateRequest) (*InitMandateResponse, error)
	CheckMandateStatus(context.Context, *CheckMandateStatusRequest) (*CheckMandateStatusResponse, error)
	GenerateKfsLa(context.Context, *GenerateKfsLaRequest) (*GenerateKfsLaResponse, error)
	SignKfsLa(context.Context, *SignKfsLaRequest) (*SignKfsLaResponse, error)
	GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error)
	GetPreDisbursementDetails(context.Context, *GetPreDisbursementDetailsRequest) (*GetPreDisbursementDetailsResponse, error)
	PostExternalData(context.Context, *PostExternalDataRequest) (*PostExternalDataResponse, error)
	GetAmortizationSchedule(context.Context, *GetAmortizationScheduleRequest) (*GetAmortizationScheduleResponse, error)
	// GetForeclosureDetails returns details like the amount needed to close a loan before its due date
	// with breakup of principal, interest, foreclosure charges etc.
	// RBI guidelines require lenders to allow users to cancel the loan in the first few days post disbursal
	// by paying back the disbursed amount without any interest charges. This period is also called as the cool-off period.
	// When a loan is in cool-off period, GetForeclosureDetails should be called with purpose as PURPOSE_COOL_OFF
	// to get the amount needed to cancel the loan. Typically, in this period the foreclosure charges will be 0.
	GetForeclosureDetails(context.Context, *GetForeclosureDetailsRequest) (*GetForeclosureDetailsResponse, error)
	GeneratePaymentLink(context.Context, *GeneratePaymentLinkRequest) (*GeneratePaymentLinkResponse, error)
	GetPaymentStatus(context.Context, *GetPaymentStatusRequest) (*GetPaymentStatusResponse, error)
}

// UnimplementedLendenServer should be embedded to have forward compatible implementations.
type UnimplementedLendenServer struct {
}

func (UnimplementedLendenServer) CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedLendenServer) ApplyForLoan(context.Context, *ApplyForLoanRequest) (*ApplyForLoanResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ApplyForLoan not implemented")
}
func (UnimplementedLendenServer) CheckHardEligibility(context.Context, *CheckHardEligibilityRequest) (*CheckHardEligibilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckHardEligibility not implemented")
}
func (UnimplementedLendenServer) SelectOffer(context.Context, *SelectOfferRequest) (*SelectOfferResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SelectOffer not implemented")
}
func (UnimplementedLendenServer) ModifyRateOfInterest(context.Context, *ModifyRateOfInterestRequest) (*ModifyRateOfInterestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModifyRateOfInterest not implemented")
}
func (UnimplementedLendenServer) KycInit(context.Context, *KycInitRequest) (*KycInitResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method KycInit not implemented")
}
func (UnimplementedLendenServer) CheckKycStatus(context.Context, *CheckKycStatusRequest) (*CheckKycStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckKycStatus not implemented")
}
func (UnimplementedLendenServer) AddBankDetails(context.Context, *AddBankDetailsRequest) (*AddBankDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddBankDetails not implemented")
}
func (UnimplementedLendenServer) InitMandate(context.Context, *InitMandateRequest) (*InitMandateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InitMandate not implemented")
}
func (UnimplementedLendenServer) CheckMandateStatus(context.Context, *CheckMandateStatusRequest) (*CheckMandateStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckMandateStatus not implemented")
}
func (UnimplementedLendenServer) GenerateKfsLa(context.Context, *GenerateKfsLaRequest) (*GenerateKfsLaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateKfsLa not implemented")
}
func (UnimplementedLendenServer) SignKfsLa(context.Context, *SignKfsLaRequest) (*SignKfsLaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignKfsLa not implemented")
}
func (UnimplementedLendenServer) GetLoanDetails(context.Context, *GetLoanDetailsRequest) (*GetLoanDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLoanDetails not implemented")
}
func (UnimplementedLendenServer) GetPreDisbursementDetails(context.Context, *GetPreDisbursementDetailsRequest) (*GetPreDisbursementDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPreDisbursementDetails not implemented")
}
func (UnimplementedLendenServer) PostExternalData(context.Context, *PostExternalDataRequest) (*PostExternalDataResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PostExternalData not implemented")
}
func (UnimplementedLendenServer) GetAmortizationSchedule(context.Context, *GetAmortizationScheduleRequest) (*GetAmortizationScheduleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAmortizationSchedule not implemented")
}
func (UnimplementedLendenServer) GetForeclosureDetails(context.Context, *GetForeclosureDetailsRequest) (*GetForeclosureDetailsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetForeclosureDetails not implemented")
}
func (UnimplementedLendenServer) GeneratePaymentLink(context.Context, *GeneratePaymentLinkRequest) (*GeneratePaymentLinkResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GeneratePaymentLink not implemented")
}
func (UnimplementedLendenServer) GetPaymentStatus(context.Context, *GetPaymentStatusRequest) (*GetPaymentStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPaymentStatus not implemented")
}

// UnsafeLendenServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LendenServer will
// result in compilation errors.
type UnsafeLendenServer interface {
	mustEmbedUnimplementedLendenServer()
}

func RegisterLendenServer(s grpc.ServiceRegistrar, srv LendenServer) {
	s.RegisterService(&Lenden_ServiceDesc, srv)
}

func _Lenden_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_ApplyForLoan_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ApplyForLoanRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).ApplyForLoan(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_ApplyForLoan_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).ApplyForLoan(ctx, req.(*ApplyForLoanRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_CheckHardEligibility_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckHardEligibilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).CheckHardEligibility(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_CheckHardEligibility_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).CheckHardEligibility(ctx, req.(*CheckHardEligibilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_SelectOffer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SelectOfferRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).SelectOffer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_SelectOffer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).SelectOffer(ctx, req.(*SelectOfferRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_ModifyRateOfInterest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModifyRateOfInterestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).ModifyRateOfInterest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_ModifyRateOfInterest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).ModifyRateOfInterest(ctx, req.(*ModifyRateOfInterestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_KycInit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(KycInitRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).KycInit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_KycInit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).KycInit(ctx, req.(*KycInitRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_CheckKycStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckKycStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).CheckKycStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_CheckKycStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).CheckKycStatus(ctx, req.(*CheckKycStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_AddBankDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddBankDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).AddBankDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_AddBankDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).AddBankDetails(ctx, req.(*AddBankDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_InitMandate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InitMandateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).InitMandate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_InitMandate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).InitMandate(ctx, req.(*InitMandateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_CheckMandateStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckMandateStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).CheckMandateStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_CheckMandateStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).CheckMandateStatus(ctx, req.(*CheckMandateStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GenerateKfsLa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateKfsLaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GenerateKfsLa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GenerateKfsLa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GenerateKfsLa(ctx, req.(*GenerateKfsLaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_SignKfsLa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignKfsLaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).SignKfsLa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_SignKfsLa_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).SignKfsLa(ctx, req.(*SignKfsLaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GetLoanDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLoanDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GetLoanDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GetLoanDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GetLoanDetails(ctx, req.(*GetLoanDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GetPreDisbursementDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPreDisbursementDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GetPreDisbursementDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GetPreDisbursementDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GetPreDisbursementDetails(ctx, req.(*GetPreDisbursementDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_PostExternalData_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PostExternalDataRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).PostExternalData(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_PostExternalData_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).PostExternalData(ctx, req.(*PostExternalDataRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GetAmortizationSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAmortizationScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GetAmortizationSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GetAmortizationSchedule_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GetAmortizationSchedule(ctx, req.(*GetAmortizationScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GetForeclosureDetails_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetForeclosureDetailsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GetForeclosureDetails(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GetForeclosureDetails_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GetForeclosureDetails(ctx, req.(*GetForeclosureDetailsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GeneratePaymentLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GeneratePaymentLinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GeneratePaymentLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GeneratePaymentLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GeneratePaymentLink(ctx, req.(*GeneratePaymentLinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Lenden_GetPaymentStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPaymentStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LendenServer).GetPaymentStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Lenden_GetPaymentStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LendenServer).GetPaymentStatus(ctx, req.(*GetPaymentStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Lenden_ServiceDesc is the grpc.ServiceDesc for Lenden service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Lenden_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "vendorgateway.lending.preapprovedloan.lenden.Lenden",
	HandlerType: (*LendenServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUser",
			Handler:    _Lenden_CreateUser_Handler,
		},
		{
			MethodName: "ApplyForLoan",
			Handler:    _Lenden_ApplyForLoan_Handler,
		},
		{
			MethodName: "CheckHardEligibility",
			Handler:    _Lenden_CheckHardEligibility_Handler,
		},
		{
			MethodName: "SelectOffer",
			Handler:    _Lenden_SelectOffer_Handler,
		},
		{
			MethodName: "ModifyRateOfInterest",
			Handler:    _Lenden_ModifyRateOfInterest_Handler,
		},
		{
			MethodName: "KycInit",
			Handler:    _Lenden_KycInit_Handler,
		},
		{
			MethodName: "CheckKycStatus",
			Handler:    _Lenden_CheckKycStatus_Handler,
		},
		{
			MethodName: "AddBankDetails",
			Handler:    _Lenden_AddBankDetails_Handler,
		},
		{
			MethodName: "InitMandate",
			Handler:    _Lenden_InitMandate_Handler,
		},
		{
			MethodName: "CheckMandateStatus",
			Handler:    _Lenden_CheckMandateStatus_Handler,
		},
		{
			MethodName: "GenerateKfsLa",
			Handler:    _Lenden_GenerateKfsLa_Handler,
		},
		{
			MethodName: "SignKfsLa",
			Handler:    _Lenden_SignKfsLa_Handler,
		},
		{
			MethodName: "GetLoanDetails",
			Handler:    _Lenden_GetLoanDetails_Handler,
		},
		{
			MethodName: "GetPreDisbursementDetails",
			Handler:    _Lenden_GetPreDisbursementDetails_Handler,
		},
		{
			MethodName: "PostExternalData",
			Handler:    _Lenden_PostExternalData_Handler,
		},
		{
			MethodName: "GetAmortizationSchedule",
			Handler:    _Lenden_GetAmortizationSchedule_Handler,
		},
		{
			MethodName: "GetForeclosureDetails",
			Handler:    _Lenden_GetForeclosureDetails_Handler,
		},
		{
			MethodName: "GeneratePaymentLink",
			Handler:    _Lenden_GeneratePaymentLink_Handler,
		},
		{
			MethodName: "GetPaymentStatus",
			Handler:    _Lenden_GetPaymentStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/vendorgateway/lending/preapprovedloan/lenden/service.proto",
}
