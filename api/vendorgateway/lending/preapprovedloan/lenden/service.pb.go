// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/vendorgateway/lending/preapprovedloan/lenden/service.proto

package lenden

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	rpc "github.com/epifi/be-common/api/rpc"
	common "github.com/epifi/be-common/api/typesv2/common"
	vendorgateway "github.com/epifi/be-common/api/vendorgateway"
	lenden "github.com/epifi/gamma/api/vendors/lenden"
	date "google.golang.org/genproto/googleapis/type/date"
	decimal "google.golang.org/genproto/googleapis/type/decimal"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KYCStatus int32

const (
	KYCStatus_KYC_STATUS_UNSPECIFIED KYCStatus = 0
	// A web link has been generated to complete the KYC process.
	KYCStatus_KYC_STATUS_INITIATED KYCStatus = 1
	// The KYC link has been opened and thus the KYC process is in progress.
	KYCStatus_KYC_STATUS_IN_PROGRESS KYCStatus = 2
	// KYC process has been completed successfully.
	KYCStatus_KYC_STATUS_COMPLETED KYCStatus = 3
	// KYC was not completed successfully, and the process should be re-initiated.
	KYCStatus_KYC_STATUS_FAILED KYCStatus = 4
	// The KYC link has expired and a new web link needs to be generated by re-initiating the KYC process.
	KYCStatus_KYC_STATUS_EXPIRED KYCStatus = 5
)

// Enum value maps for KYCStatus.
var (
	KYCStatus_name = map[int32]string{
		0: "KYC_STATUS_UNSPECIFIED",
		1: "KYC_STATUS_INITIATED",
		2: "KYC_STATUS_IN_PROGRESS",
		3: "KYC_STATUS_COMPLETED",
		4: "KYC_STATUS_FAILED",
		5: "KYC_STATUS_EXPIRED",
	}
	KYCStatus_value = map[string]int32{
		"KYC_STATUS_UNSPECIFIED": 0,
		"KYC_STATUS_INITIATED":   1,
		"KYC_STATUS_IN_PROGRESS": 2,
		"KYC_STATUS_COMPLETED":   3,
		"KYC_STATUS_FAILED":      4,
		"KYC_STATUS_EXPIRED":     5,
	}
)

func (x KYCStatus) Enum() *KYCStatus {
	p := new(KYCStatus)
	*p = x
	return p
}

func (x KYCStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KYCStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[0].Descriptor()
}

func (KYCStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[0]
}

func (x KYCStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KYCStatus.Descriptor instead.
func (KYCStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{0}
}

type MandateStatus int32

const (
	MandateStatus_MANDATE_STATUS_UNSPECIFIED MandateStatus = 0
	MandateStatus_MANDATE_STATUS_IN_PROGRESS MandateStatus = 1
	MandateStatus_MANDATE_STATUS_COMPLETED   MandateStatus = 2
	MandateStatus_MANDATE_STATUS_FAILED      MandateStatus = 3
	MandateStatus_MANDATE_STATUS_EXPIRED     MandateStatus = 4
)

// Enum value maps for MandateStatus.
var (
	MandateStatus_name = map[int32]string{
		0: "MANDATE_STATUS_UNSPECIFIED",
		1: "MANDATE_STATUS_IN_PROGRESS",
		2: "MANDATE_STATUS_COMPLETED",
		3: "MANDATE_STATUS_FAILED",
		4: "MANDATE_STATUS_EXPIRED",
	}
	MandateStatus_value = map[string]int32{
		"MANDATE_STATUS_UNSPECIFIED": 0,
		"MANDATE_STATUS_IN_PROGRESS": 1,
		"MANDATE_STATUS_COMPLETED":   2,
		"MANDATE_STATUS_FAILED":      3,
		"MANDATE_STATUS_EXPIRED":     4,
	}
)

func (x MandateStatus) Enum() *MandateStatus {
	p := new(MandateStatus)
	*p = x
	return p
}

func (x MandateStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MandateStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[1].Descriptor()
}

func (MandateStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[1]
}

func (x MandateStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MandateStatus.Descriptor instead.
func (MandateStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{1}
}

type AmortizationComponentPurpose int32

const (
	AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_UNSPECIFIED AmortizationComponentPurpose = 0
	AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL   AmortizationComponentPurpose = 1
	AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_INTEREST    AmortizationComponentPurpose = 2
)

// Enum value maps for AmortizationComponentPurpose.
var (
	AmortizationComponentPurpose_name = map[int32]string{
		0: "AMORTIZATION_COMPONENT_PURPOSE_UNSPECIFIED",
		1: "AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL",
		2: "AMORTIZATION_COMPONENT_PURPOSE_INTEREST",
	}
	AmortizationComponentPurpose_value = map[string]int32{
		"AMORTIZATION_COMPONENT_PURPOSE_UNSPECIFIED": 0,
		"AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL":   1,
		"AMORTIZATION_COMPONENT_PURPOSE_INTEREST":    2,
	}
)

func (x AmortizationComponentPurpose) Enum() *AmortizationComponentPurpose {
	p := new(AmortizationComponentPurpose)
	*p = x
	return p
}

func (x AmortizationComponentPurpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmortizationComponentPurpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[2].Descriptor()
}

func (AmortizationComponentPurpose) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[2]
}

func (x AmortizationComponentPurpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmortizationComponentPurpose.Descriptor instead.
func (AmortizationComponentPurpose) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{2}
}

type AmortizationScheduleItemStatus int32

const (
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UNSPECIFIED     AmortizationScheduleItemStatus = 0
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING        AmortizationScheduleItemStatus = 1
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID            AmortizationScheduleItemStatus = 2
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE AmortizationScheduleItemStatus = 3
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE             AmortizationScheduleItemStatus = 4
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE         AmortizationScheduleItemStatus = 5
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT    AmortizationScheduleItemStatus = 6
	AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID  AmortizationScheduleItemStatus = 7
)

// Enum value maps for AmortizationScheduleItemStatus.
var (
	AmortizationScheduleItemStatus_name = map[int32]string{
		0: "AMORTIZATION_SCHEDULE_ITEM_STATUS_UNSPECIFIED",
		1: "AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING",
		2: "AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID",
		3: "AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE",
		4: "AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE",
		5: "AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE",
		6: "AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT",
		7: "AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID",
	}
	AmortizationScheduleItemStatus_value = map[string]int32{
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_UNSPECIFIED":     0,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING":        1,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID":            2,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE": 3,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE":             4,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE":         5,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT":    6,
		"AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID":  7,
	}
)

func (x AmortizationScheduleItemStatus) Enum() *AmortizationScheduleItemStatus {
	p := new(AmortizationScheduleItemStatus)
	*p = x
	return p
}

func (x AmortizationScheduleItemStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AmortizationScheduleItemStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[3].Descriptor()
}

func (AmortizationScheduleItemStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[3]
}

func (x AmortizationScheduleItemStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AmortizationScheduleItemStatus.Descriptor instead.
func (AmortizationScheduleItemStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{3}
}

type PaymentStatus int32

const (
	PaymentStatus_PAYMENT_STATUS_UNSPECIFIED PaymentStatus = 0
	PaymentStatus_PAYMENT_STATUS_PENDING     PaymentStatus = 1
	PaymentStatus_PAYMENT_STATUS_SUCCESS     PaymentStatus = 2
	PaymentStatus_PAYMENT_STATUS_FAILED      PaymentStatus = 3
	// This can happen in case like when the user does not act on the payment link
	// and it expires after a certain time like 15 mins
	PaymentStatus_PAYMENT_STATUS_CANCELLED PaymentStatus = 4
)

// Enum value maps for PaymentStatus.
var (
	PaymentStatus_name = map[int32]string{
		0: "PAYMENT_STATUS_UNSPECIFIED",
		1: "PAYMENT_STATUS_PENDING",
		2: "PAYMENT_STATUS_SUCCESS",
		3: "PAYMENT_STATUS_FAILED",
		4: "PAYMENT_STATUS_CANCELLED",
	}
	PaymentStatus_value = map[string]int32{
		"PAYMENT_STATUS_UNSPECIFIED": 0,
		"PAYMENT_STATUS_PENDING":     1,
		"PAYMENT_STATUS_SUCCESS":     2,
		"PAYMENT_STATUS_FAILED":      3,
		"PAYMENT_STATUS_CANCELLED":   4,
	}
)

func (x PaymentStatus) Enum() *PaymentStatus {
	p := new(PaymentStatus)
	*p = x
	return p
}

func (x PaymentStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PaymentStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[4].Descriptor()
}

func (PaymentStatus) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[4]
}

func (x PaymentStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PaymentStatus.Descriptor instead.
func (PaymentStatus) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{4}
}

type ApplyForLoanResponse_Status int32

const (
	ApplyForLoanResponse_OK                                 ApplyForLoanResponse_Status = 0
	ApplyForLoanResponse_MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR ApplyForLoanResponse_Status = 101
)

// Enum value maps for ApplyForLoanResponse_Status.
var (
	ApplyForLoanResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR",
	}
	ApplyForLoanResponse_Status_value = map[string]int32{
		"OK":                                 0,
		"MAX_LOAN_ACCOUNT_REACHED_AT_VENDOR": 101,
	}
)

func (x ApplyForLoanResponse_Status) Enum() *ApplyForLoanResponse_Status {
	p := new(ApplyForLoanResponse_Status)
	*p = x
	return p
}

func (x ApplyForLoanResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ApplyForLoanResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[5].Descriptor()
}

func (ApplyForLoanResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[5]
}

func (x ApplyForLoanResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ApplyForLoanResponse_Status.Descriptor instead.
func (ApplyForLoanResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{3, 0}
}

type AddBankDetailsResponse_Status int32

const (
	AddBankDetailsResponse_OK                      AddBankDetailsResponse_Status = 0
	AddBankDetailsResponse_BANK_ACCOUNT_NOT_ACTIVE AddBankDetailsResponse_Status = 101
	AddBankDetailsResponse_NAME_MISMATCH           AddBankDetailsResponse_Status = 102
	AddBankDetailsResponse_BANK_CONNECTION_ERROR   AddBankDetailsResponse_Status = 103
	AddBankDetailsResponse_INVALID_BANK_DETAILS    AddBankDetailsResponse_Status = 104
)

// Enum value maps for AddBankDetailsResponse_Status.
var (
	AddBankDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "BANK_ACCOUNT_NOT_ACTIVE",
		102: "NAME_MISMATCH",
		103: "BANK_CONNECTION_ERROR",
		104: "INVALID_BANK_DETAILS",
	}
	AddBankDetailsResponse_Status_value = map[string]int32{
		"OK":                      0,
		"BANK_ACCOUNT_NOT_ACTIVE": 101,
		"NAME_MISMATCH":           102,
		"BANK_CONNECTION_ERROR":   103,
		"INVALID_BANK_DETAILS":    104,
	}
)

func (x AddBankDetailsResponse_Status) Enum() *AddBankDetailsResponse_Status {
	p := new(AddBankDetailsResponse_Status)
	*p = x
	return p
}

func (x AddBankDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddBankDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[6].Descriptor()
}

func (AddBankDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[6]
}

func (x AddBankDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddBankDetailsResponse_Status.Descriptor instead.
func (AddBankDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{17, 0}
}

type InitMandateResponse_Status int32

const (
	InitMandateResponse_OK                                 InitMandateResponse_Status = 0
	InitMandateResponse_INVALID_LOAN_STATUS                InitMandateResponse_Status = 101
	InitMandateResponse_MANDATE_VERIFICATION_FAILED        InitMandateResponse_Status = 102
	InitMandateResponse_OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND InitMandateResponse_Status = 103
	InitMandateResponse_BANK_ACCOUNT_NOT_VERIFIED          InitMandateResponse_Status = 104
	InitMandateResponse_ENACH_ALREADY_COMPLETED            InitMandateResponse_Status = 105
)

// Enum value maps for InitMandateResponse_Status.
var (
	InitMandateResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "INVALID_LOAN_STATUS",
		102: "MANDATE_VERIFICATION_FAILED",
		103: "OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND",
		104: "BANK_ACCOUNT_NOT_VERIFIED",
		105: "ENACH_ALREADY_COMPLETED",
	}
	InitMandateResponse_Status_value = map[string]int32{
		"OK":                                 0,
		"INVALID_LOAN_STATUS":                101,
		"MANDATE_VERIFICATION_FAILED":        102,
		"OFFER_OR_ACCOUNT_DETAILS_NOT_FOUND": 103,
		"BANK_ACCOUNT_NOT_VERIFIED":          104,
		"ENACH_ALREADY_COMPLETED":            105,
	}
)

func (x InitMandateResponse_Status) Enum() *InitMandateResponse_Status {
	p := new(InitMandateResponse_Status)
	*p = x
	return p
}

func (x InitMandateResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (InitMandateResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[7].Descriptor()
}

func (InitMandateResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[7]
}

func (x InitMandateResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use InitMandateResponse_Status.Descriptor instead.
func (InitMandateResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{19, 0}
}

type GetPreDisbursementDetailsResponse_Status int32

const (
	GetPreDisbursementDetailsResponse_OK GetPreDisbursementDetailsResponse_Status = 0
	// When values of one or more field in input is out of range
	GetPreDisbursementDetailsResponse_OUT_OF_RANGE GetPreDisbursementDetailsResponse_Status = 101
)

// Enum value maps for GetPreDisbursementDetailsResponse_Status.
var (
	GetPreDisbursementDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "OUT_OF_RANGE",
	}
	GetPreDisbursementDetailsResponse_Status_value = map[string]int32{
		"OK":           0,
		"OUT_OF_RANGE": 101,
	}
)

func (x GetPreDisbursementDetailsResponse_Status) Enum() *GetPreDisbursementDetailsResponse_Status {
	p := new(GetPreDisbursementDetailsResponse_Status)
	*p = x
	return p
}

func (x GetPreDisbursementDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetPreDisbursementDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[8].Descriptor()
}

func (GetPreDisbursementDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[8]
}

func (x GetPreDisbursementDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetPreDisbursementDetailsResponse_Status.Descriptor instead.
func (GetPreDisbursementDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{29, 0}
}

type GetForeclosureDetailsRequest_Purpose int32

const (
	GetForeclosureDetailsRequest_PURPOSE_UNSPECIFIED GetForeclosureDetailsRequest_Purpose = 0
	GetForeclosureDetailsRequest_PURPOSE_FORECLOSURE GetForeclosureDetailsRequest_Purpose = 1
	// For getting foreclosure details of a loan in the cool-off / cancellation period
	GetForeclosureDetailsRequest_PURPOSE_COOL_OFF GetForeclosureDetailsRequest_Purpose = 2
)

// Enum value maps for GetForeclosureDetailsRequest_Purpose.
var (
	GetForeclosureDetailsRequest_Purpose_name = map[int32]string{
		0: "PURPOSE_UNSPECIFIED",
		1: "PURPOSE_FORECLOSURE",
		2: "PURPOSE_COOL_OFF",
	}
	GetForeclosureDetailsRequest_Purpose_value = map[string]int32{
		"PURPOSE_UNSPECIFIED": 0,
		"PURPOSE_FORECLOSURE": 1,
		"PURPOSE_COOL_OFF":    2,
	}
)

func (x GetForeclosureDetailsRequest_Purpose) Enum() *GetForeclosureDetailsRequest_Purpose {
	p := new(GetForeclosureDetailsRequest_Purpose)
	*p = x
	return p
}

func (x GetForeclosureDetailsRequest_Purpose) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetForeclosureDetailsRequest_Purpose) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[9].Descriptor()
}

func (GetForeclosureDetailsRequest_Purpose) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[9]
}

func (x GetForeclosureDetailsRequest_Purpose) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetForeclosureDetailsRequest_Purpose.Descriptor instead.
func (GetForeclosureDetailsRequest_Purpose) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{38, 0}
}

type GetForeclosureDetailsResponse_Status int32

const (
	GetForeclosureDetailsResponse_OK GetForeclosureDetailsResponse_Status = 0
	// Can be sent when a loan is already closed and thus foreclosure isn't applicable
	GetForeclosureDetailsResponse_INVALID_LOAN_STATUS GetForeclosureDetailsResponse_Status = 101
	// If a user pays more than their EMI, LDC allocates the excess amount to principal first,
	// and then to interest. As a result, a user may fully repay the principal while still
	// having pending interest. In this case, foreclosure charges do not apply.
	// The loan can be closed by simply paying the remaining interest amount.
	// LDC refers to this as a "normal closure" instead of a foreclosure.
	// The remaining amount for closing the loan can be retrieved using the GetLoanDetails API.
	GetForeclosureDetailsResponse_PRINCIPAL_PAID GetForeclosureDetailsResponse_Status = 102
	// A user can't foreclose a loan if their last EMI due date is less than 3 days away.
	// They can close the loan via normal route in such a situation.
	GetForeclosureDetailsResponse_LAST_DUE_DATE_CLOSE GetForeclosureDetailsResponse_Status = 103
)

// Enum value maps for GetForeclosureDetailsResponse_Status.
var (
	GetForeclosureDetailsResponse_Status_name = map[int32]string{
		0:   "OK",
		101: "INVALID_LOAN_STATUS",
		102: "PRINCIPAL_PAID",
		103: "LAST_DUE_DATE_CLOSE",
	}
	GetForeclosureDetailsResponse_Status_value = map[string]int32{
		"OK":                  0,
		"INVALID_LOAN_STATUS": 101,
		"PRINCIPAL_PAID":      102,
		"LAST_DUE_DATE_CLOSE": 103,
	}
)

func (x GetForeclosureDetailsResponse_Status) Enum() *GetForeclosureDetailsResponse_Status {
	p := new(GetForeclosureDetailsResponse_Status)
	*p = x
	return p
}

func (x GetForeclosureDetailsResponse_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GetForeclosureDetailsResponse_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[10].Descriptor()
}

func (GetForeclosureDetailsResponse_Status) Type() protoreflect.EnumType {
	return &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes[10]
}

func (x GetForeclosureDetailsResponse_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GetForeclosureDetailsResponse_Status.Descriptor instead.
func (GetForeclosureDetailsResponse_Status) EnumDescriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{39, 0}
}

type CreateUserRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Name        *common.Name                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Pan         string                       `protobuf:"bytes,3,opt,name=pan,proto3" json:"pan,omitempty"`
	PhoneNumber *common.PhoneNumber          `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	Email       string                       `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Dob         *date.Date                   `protobuf:"bytes,6,opt,name=dob,proto3" json:"dob,omitempty"`
	// this denotes a unique identifier for the user creation request for idempotency purposes
	ReferenceId string `protobuf:"bytes,7,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// ONLY VALID for Salaried and Self Employed
	EmploymentType LendenEmploymentType `protobuf:"varint,8,opt,name=employment_type,json=employmentType,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.LendenEmploymentType" json:"employment_type,omitempty"`
	// Name of the org where the user is employed
	EmploymentOrganizationName string `protobuf:"bytes,9,opt,name=employment_organization_name,json=employmentOrganizationName,proto3" json:"employment_organization_name,omitempty"`
	// List of consent codes that the user has consented to
	ConsentTypeList []ConsentType `protobuf:"varint,10,rep,packed,name=consent_type_list,json=consentTypeList,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.ConsentType" json:"consent_type_list,omitempty"`
	// denotes the user's device ip from which they are applying for the loan.
	UserIp string `protobuf:"bytes,11,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// denotes the user's device id from which they are applying for the loan.
	DeviceId string `protobuf:"bytes,12,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// [Optional] latitude and longitude of the user from where they are applying for the loan.
	Latitude  string `protobuf:"bytes,13,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,14,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// denotes the time when the user has given consent
	ConsentTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=consent_time,json=consentTime,proto3" json:"consent_time,omitempty"`
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CreateUserRequest) GetName() *common.Name {
	if x != nil {
		return x.Name
	}
	return nil
}

func (x *CreateUserRequest) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *CreateUserRequest) GetPhoneNumber() *common.PhoneNumber {
	if x != nil {
		return x.PhoneNumber
	}
	return nil
}

func (x *CreateUserRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *CreateUserRequest) GetDob() *date.Date {
	if x != nil {
		return x.Dob
	}
	return nil
}

func (x *CreateUserRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *CreateUserRequest) GetEmploymentType() LendenEmploymentType {
	if x != nil {
		return x.EmploymentType
	}
	return LendenEmploymentType_LENDEN_EMPLOYMENT_TYPE_UNSPECIFIED
}

func (x *CreateUserRequest) GetEmploymentOrganizationName() string {
	if x != nil {
		return x.EmploymentOrganizationName
	}
	return ""
}

func (x *CreateUserRequest) GetConsentTypeList() []ConsentType {
	if x != nil {
		return x.ConsentTypeList
	}
	return nil
}

func (x *CreateUserRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *CreateUserRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CreateUserRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *CreateUserRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *CreateUserRequest) GetConsentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConsentTime
	}
	return nil
}

type CreateUserResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// denotes a unique id of a user in LDC system.
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *CreateUserResponse) Reset() {
	*x = CreateUserResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateUserResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserResponse) ProtoMessage() {}

func (x *CreateUserResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserResponse.ProtoReflect.Descriptor instead.
func (*CreateUserResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CreateUserResponse) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type ApplyForLoanRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// unique id of the user in LDC system
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// this denotes a unique identifier for the user creation request for idempotency purposes
	ReferenceId string `protobuf:"bytes,3,opt,name=reference_id,json=referenceId,proto3" json:"reference_id,omitempty"`
	// denotes the amount of loan requested by the user
	RequestedAmount *money.Money                  `protobuf:"bytes,4,opt,name=requested_amount,json=requestedAmount,proto3" json:"requested_amount,omitempty"`
	Interest        *ApplyForLoanRequest_Interest `protobuf:"bytes,5,opt,name=interest,proto3" json:"interest,omitempty"`
	AddressType     AddressType                   `protobuf:"varint,6,opt,name=address_type,json=addressType,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.AddressType" json:"address_type,omitempty"`
	// Address of the user accepting the loan
	// [Required] state in address.administrative_area
	// [Required] pincode in address.postal_code
	// [Required] address in address.address_lines
	Address *common.PostalAddress `protobuf:"bytes,7,opt,name=address,proto3" json:"address,omitempty"`
}

func (x *ApplyForLoanRequest) Reset() {
	*x = ApplyForLoanRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyForLoanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyForLoanRequest) ProtoMessage() {}

func (x *ApplyForLoanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyForLoanRequest.ProtoReflect.Descriptor instead.
func (*ApplyForLoanRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{2}
}

func (x *ApplyForLoanRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ApplyForLoanRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ApplyForLoanRequest) GetReferenceId() string {
	if x != nil {
		return x.ReferenceId
	}
	return ""
}

func (x *ApplyForLoanRequest) GetRequestedAmount() *money.Money {
	if x != nil {
		return x.RequestedAmount
	}
	return nil
}

func (x *ApplyForLoanRequest) GetInterest() *ApplyForLoanRequest_Interest {
	if x != nil {
		return x.Interest
	}
	return nil
}

func (x *ApplyForLoanRequest) GetAddressType() AddressType {
	if x != nil {
		return x.AddressType
	}
	return AddressType_ADDRESS_TYPE_UNSPECIFIED
}

func (x *ApplyForLoanRequest) GetAddress() *common.PostalAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

type ApplyForLoanResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanId string      `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
}

func (x *ApplyForLoanResponse) Reset() {
	*x = ApplyForLoanResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyForLoanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyForLoanResponse) ProtoMessage() {}

func (x *ApplyForLoanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyForLoanResponse.ProtoReflect.Descriptor instead.
func (*ApplyForLoanResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{3}
}

func (x *ApplyForLoanResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ApplyForLoanResponse) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type CheckHardEligibilityRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	// Loan Id to check hard Eligibility
	LoanId string `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// LDC user_id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *CheckHardEligibilityRequest) Reset() {
	*x = CheckHardEligibilityRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckHardEligibilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckHardEligibilityRequest) ProtoMessage() {}

func (x *CheckHardEligibilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckHardEligibilityRequest.ProtoReflect.Descriptor instead.
func (*CheckHardEligibilityRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{4}
}

func (x *CheckHardEligibilityRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckHardEligibilityRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *CheckHardEligibilityRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type CheckHardEligibilityResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// denotes the eligibility status of the user for the loan
	EligibilityStatus EligibilityStatus `protobuf:"varint,2,opt,name=eligibility_status,json=eligibilityStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.EligibilityStatus" json:"eligibility_status,omitempty"`
	// possible offer details
	OfferData *OfferData `protobuf:"bytes,3,opt,name=offer_data,json=offerData,proto3" json:"offer_data,omitempty"`
}

func (x *CheckHardEligibilityResponse) Reset() {
	*x = CheckHardEligibilityResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckHardEligibilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckHardEligibilityResponse) ProtoMessage() {}

func (x *CheckHardEligibilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckHardEligibilityResponse.ProtoReflect.Descriptor instead.
func (*CheckHardEligibilityResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{5}
}

func (x *CheckHardEligibilityResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckHardEligibilityResponse) GetEligibilityStatus() EligibilityStatus {
	if x != nil {
		return x.EligibilityStatus
	}
	return EligibilityStatus_ELIGIBILITY_STATUS_UNSPECIFIED
}

func (x *CheckHardEligibilityResponse) GetOfferData() *OfferData {
	if x != nil {
		return x.OfferData
	}
	return nil
}

type OfferData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Offers                 []*P2POfferDetails                    `protobuf:"bytes,1,rep,name=offers,proto3" json:"offers,omitempty"`
	OfferSelectionMultiple int32                                 `protobuf:"varint,2,opt,name=offer_selection_multiple,json=offerSelectionMultiple,proto3" json:"offer_selection_multiple,omitempty"`
	MinAmount              *money.Money                          `protobuf:"bytes,3,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount              *money.Money                          `protobuf:"bytes,4,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	ApplicableTenures      []*OfferData_ApplicableAmountToTenure `protobuf:"bytes,5,rep,name=applicable_tenures,json=applicableTenures,proto3" json:"applicable_tenures,omitempty"`
}

func (x *OfferData) Reset() {
	*x = OfferData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferData) ProtoMessage() {}

func (x *OfferData) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferData.ProtoReflect.Descriptor instead.
func (*OfferData) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{6}
}

func (x *OfferData) GetOffers() []*P2POfferDetails {
	if x != nil {
		return x.Offers
	}
	return nil
}

func (x *OfferData) GetOfferSelectionMultiple() int32 {
	if x != nil {
		return x.OfferSelectionMultiple
	}
	return 0
}

func (x *OfferData) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *OfferData) GetMaxAmount() *money.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *OfferData) GetApplicableTenures() []*OfferData_ApplicableAmountToTenure {
	if x != nil {
		return x.ApplicableTenures
	}
	return nil
}

type P2POfferDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OfferCode                string    `protobuf:"bytes,1,opt,name=offer_code,json=offerCode,proto3" json:"offer_code,omitempty"`
	Roi                      float64   `protobuf:"fixed64,2,opt,name=roi,proto3" json:"roi,omitempty"`
	FundingProbability       string    `protobuf:"bytes,3,opt,name=funding_probability,json=fundingProbability,proto3" json:"funding_probability,omitempty"`
	ExpectedTimeToGetFunding string    `protobuf:"bytes,4,opt,name=expected_time_to_get_funding,json=expectedTimeToGetFunding,proto3" json:"expected_time_to_get_funding,omitempty"`
	IsRecommended            bool      `protobuf:"varint,5,opt,name=is_recommended,json=isRecommended,proto3" json:"is_recommended,omitempty"`
	ModifyRoiList            []float64 `protobuf:"fixed64,6,rep,packed,name=modify_roi_list,json=modifyRoiList,proto3" json:"modify_roi_list,omitempty"`
}

func (x *P2POfferDetails) Reset() {
	*x = P2POfferDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *P2POfferDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*P2POfferDetails) ProtoMessage() {}

func (x *P2POfferDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use P2POfferDetails.ProtoReflect.Descriptor instead.
func (*P2POfferDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{7}
}

func (x *P2POfferDetails) GetOfferCode() string {
	if x != nil {
		return x.OfferCode
	}
	return ""
}

func (x *P2POfferDetails) GetRoi() float64 {
	if x != nil {
		return x.Roi
	}
	return 0
}

func (x *P2POfferDetails) GetFundingProbability() string {
	if x != nil {
		return x.FundingProbability
	}
	return ""
}

func (x *P2POfferDetails) GetExpectedTimeToGetFunding() string {
	if x != nil {
		return x.ExpectedTimeToGetFunding
	}
	return ""
}

func (x *P2POfferDetails) GetIsRecommended() bool {
	if x != nil {
		return x.IsRecommended
	}
	return false
}

func (x *P2POfferDetails) GetModifyRoiList() []float64 {
	if x != nil {
		return x.ModifyRoiList
	}
	return nil
}

type SelectOfferRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId         string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	UserId         string                       `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	SelectedAmount *money.Money                 `protobuf:"bytes,4,opt,name=selected_amount,json=selectedAmount,proto3" json:"selected_amount,omitempty"`
	Tenure         int32                        `protobuf:"varint,5,opt,name=tenure,proto3" json:"tenure,omitempty"`
	// denoted the selected offer id from the list of offers
	SelectedOfferId string `protobuf:"bytes,6,opt,name=selected_offer_id,json=selectedOfferId,proto3" json:"selected_offer_id,omitempty"`
}

func (x *SelectOfferRequest) Reset() {
	*x = SelectOfferRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferRequest) ProtoMessage() {}

func (x *SelectOfferRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferRequest.ProtoReflect.Descriptor instead.
func (*SelectOfferRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{8}
}

func (x *SelectOfferRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SelectOfferRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SelectOfferRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SelectOfferRequest) GetSelectedAmount() *money.Money {
	if x != nil {
		return x.SelectedAmount
	}
	return nil
}

func (x *SelectOfferRequest) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *SelectOfferRequest) GetSelectedOfferId() string {
	if x != nil {
		return x.SelectedOfferId
	}
	return ""
}

type SelectOfferResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// ID / code of the offer selected in current or a previous API call
	OfferCode string `protobuf:"bytes,2,opt,name=offer_code,json=offerCode,proto3" json:"offer_code,omitempty"`
}

func (x *SelectOfferResponse) Reset() {
	*x = SelectOfferResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SelectOfferResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SelectOfferResponse) ProtoMessage() {}

func (x *SelectOfferResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SelectOfferResponse.ProtoReflect.Descriptor instead.
func (*SelectOfferResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{9}
}

func (x *SelectOfferResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SelectOfferResponse) GetOfferCode() string {
	if x != nil {
		return x.OfferCode
	}
	return ""
}

type ModifyRateOfInterestRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header       *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId       string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	InterestRate float32                      `protobuf:"fixed32,3,opt,name=interest_rate,json=interestRate,proto3" json:"interest_rate,omitempty"`
	// List of consent codes that the user has consented to, Only accepted MODIFY_ROI
	ConsentCodeList []ConsentType `protobuf:"varint,4,rep,packed,name=consent_code_list,json=consentCodeList,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.ConsentType" json:"consent_code_list,omitempty"`
	// denotes the user's device ip from which they are applying for the loan.
	UserIp string `protobuf:"bytes,5,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// denotes the user's device id from which they are applying for the loan.
	DeviceId string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// [Optional] latitude and longitude of the user from where they are applying for the loan.
	Latitude  string `protobuf:"bytes,7,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	UserId    string `protobuf:"bytes,9,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Timestamp at which user consented to modifying ROI
	ConsentedAt *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=consented_at,json=consentedAt,proto3" json:"consented_at,omitempty"`
}

func (x *ModifyRateOfInterestRequest) Reset() {
	*x = ModifyRateOfInterestRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyRateOfInterestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyRateOfInterestRequest) ProtoMessage() {}

func (x *ModifyRateOfInterestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyRateOfInterestRequest.ProtoReflect.Descriptor instead.
func (*ModifyRateOfInterestRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{10}
}

func (x *ModifyRateOfInterestRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *ModifyRateOfInterestRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetInterestRate() float32 {
	if x != nil {
		return x.InterestRate
	}
	return 0
}

func (x *ModifyRateOfInterestRequest) GetConsentCodeList() []ConsentType {
	if x != nil {
		return x.ConsentCodeList
	}
	return nil
}

func (x *ModifyRateOfInterestRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *ModifyRateOfInterestRequest) GetConsentedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ConsentedAt
	}
	return nil
}

type ModifyRateOfInterestResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status  `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	InstallmentAmount   *money.Money `protobuf:"bytes,2,opt,name=installment_amount,json=installmentAmount,proto3" json:"installment_amount,omitempty"`
	KfsDocUrl           string       `protobuf:"bytes,3,opt,name=kfs_doc_url,json=kfsDocUrl,proto3" json:"kfs_doc_url,omitempty"`
	LoanAgreementDocUrl string       `protobuf:"bytes,4,opt,name=loan_agreement_doc_url,json=loanAgreementDocUrl,proto3" json:"loan_agreement_doc_url,omitempty"`
}

func (x *ModifyRateOfInterestResponse) Reset() {
	*x = ModifyRateOfInterestResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ModifyRateOfInterestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModifyRateOfInterestResponse) ProtoMessage() {}

func (x *ModifyRateOfInterestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModifyRateOfInterestResponse.ProtoReflect.Descriptor instead.
func (*ModifyRateOfInterestResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{11}
}

func (x *ModifyRateOfInterestResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *ModifyRateOfInterestResponse) GetInstallmentAmount() *money.Money {
	if x != nil {
		return x.InstallmentAmount
	}
	return nil
}

func (x *ModifyRateOfInterestResponse) GetKfsDocUrl() string {
	if x != nil {
		return x.KfsDocUrl
	}
	return ""
}

func (x *ModifyRateOfInterestResponse) GetLoanAgreementDocUrl() string {
	if x != nil {
		return x.LoanAgreementDocUrl
	}
	return ""
}

type KycInitRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	UserId string                       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LoanId string                       `protobuf:"bytes,3,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// List of consent codes that the user has consented to
	ConsentCodeList []ConsentType `protobuf:"varint,4,rep,packed,name=consent_code_list,json=consentCodeList,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.ConsentType" json:"consent_code_list,omitempty"`
	// denotes the user's device ip from which they are applying for the loan.
	UserIp string `protobuf:"bytes,5,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// denotes the user's device id from which they are applying for the loan.
	DeviceId string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// [Optional] latitude and longitude of the user from where they are applying for the loan.
	Latitude  string `protobuf:"bytes,7,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// denotes the time when the user has given consent
	ConsentTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=consent_time,json=consentTime,proto3" json:"consent_time,omitempty"`
}

func (x *KycInitRequest) Reset() {
	*x = KycInitRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycInitRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycInitRequest) ProtoMessage() {}

func (x *KycInitRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycInitRequest.ProtoReflect.Descriptor instead.
func (*KycInitRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{12}
}

func (x *KycInitRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *KycInitRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *KycInitRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *KycInitRequest) GetConsentCodeList() []ConsentType {
	if x != nil {
		return x.ConsentCodeList
	}
	return nil
}

func (x *KycInitRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *KycInitRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *KycInitRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *KycInitRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *KycInitRequest) GetConsentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConsentTime
	}
	return nil
}

type KycInitResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	KycStatus      KYCStatus   `protobuf:"varint,2,opt,name=kyc_status,json=kycStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.KYCStatus" json:"kyc_status,omitempty"`
	RedirectionUrl string      `protobuf:"bytes,3,opt,name=redirection_url,json=redirectionUrl,proto3" json:"redirection_url,omitempty"`
	// useful for tracking the status of kyc setup
	TrackingId string `protobuf:"bytes,4,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	KycMessage string `protobuf:"bytes,5,opt,name=kyc_message,json=kycMessage,proto3" json:"kyc_message,omitempty"`
}

func (x *KycInitResponse) Reset() {
	*x = KycInitResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *KycInitResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KycInitResponse) ProtoMessage() {}

func (x *KycInitResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KycInitResponse.ProtoReflect.Descriptor instead.
func (*KycInitResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{13}
}

func (x *KycInitResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *KycInitResponse) GetKycStatus() KYCStatus {
	if x != nil {
		return x.KycStatus
	}
	return KYCStatus_KYC_STATUS_UNSPECIFIED
}

func (x *KycInitResponse) GetRedirectionUrl() string {
	if x != nil {
		return x.RedirectionUrl
	}
	return ""
}

func (x *KycInitResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *KycInitResponse) GetKycMessage() string {
	if x != nil {
		return x.KycMessage
	}
	return ""
}

type CheckKycStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	UserId string                       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// The tracking_id that is present in KycInitResponse
	TrackingId string `protobuf:"bytes,3,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
}

func (x *CheckKycStatusRequest) Reset() {
	*x = CheckKycStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckKycStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckKycStatusRequest) ProtoMessage() {}

func (x *CheckKycStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckKycStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckKycStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{14}
}

func (x *CheckKycStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckKycStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckKycStatusRequest) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

type CheckKycStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status    *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	KycStatus KYCStatus   `protobuf:"varint,2,opt,name=kyc_status,json=kycStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.KYCStatus" json:"kyc_status,omitempty"`
}

func (x *CheckKycStatusResponse) Reset() {
	*x = CheckKycStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckKycStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckKycStatusResponse) ProtoMessage() {}

func (x *CheckKycStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckKycStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckKycStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{15}
}

func (x *CheckKycStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckKycStatusResponse) GetKycStatus() KYCStatus {
	if x != nil {
		return x.KycStatus
	}
	return KYCStatus_KYC_STATUS_UNSPECIFIED
}

type AddBankDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	UserId string                       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LoanId string                       `protobuf:"bytes,3,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// Only supported Account Type is Savings or Current Account
	// BankAccountDetails.bank_name is optional
	BankAccountDetails *common.BankAccountDetails `protobuf:"bytes,4,opt,name=bank_account_details,json=bankAccountDetails,proto3" json:"bank_account_details,omitempty"`
}

func (x *AddBankDetailsRequest) Reset() {
	*x = AddBankDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBankDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBankDetailsRequest) ProtoMessage() {}

func (x *AddBankDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBankDetailsRequest.ProtoReflect.Descriptor instead.
func (*AddBankDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{16}
}

func (x *AddBankDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *AddBankDetailsRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *AddBankDetailsRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *AddBankDetailsRequest) GetBankAccountDetails() *common.BankAccountDetails {
	if x != nil {
		return x.BankAccountDetails
	}
	return nil
}

type AddBankDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *AddBankDetailsResponse) Reset() {
	*x = AddBankDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AddBankDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddBankDetailsResponse) ProtoMessage() {}

func (x *AddBankDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddBankDetailsResponse.ProtoReflect.Descriptor instead.
func (*AddBankDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{17}
}

func (x *AddBankDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type InitMandateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	UserId string                       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	LoanId string                       `protobuf:"bytes,3,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// Mandate Type "NACH_MANDATE" / "UPI_MANDATE",
	MandateType MandateType `protobuf:"varint,4,opt,name=mandate_type,json=mandateType,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.MandateType" json:"mandate_type,omitempty"`
	// List of consent codes that the user has consented to
	ConsentCodeList []ConsentType `protobuf:"varint,5,rep,packed,name=consent_code_list,json=consentCodeList,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.ConsentType" json:"consent_code_list,omitempty"`
	// denotes the user's device ip from which they are applying for the loan.
	UserIp string `protobuf:"bytes,6,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// denotes the user's device id from which they are applying for the loan.
	DeviceId string `protobuf:"bytes,7,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// [Optional] latitude and longitude of the user from where they are applying for the loan.
	Latitude  string `protobuf:"bytes,8,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,9,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// denotes the time when the user has given consent
	ConsentTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=consent_time,json=consentTime,proto3" json:"consent_time,omitempty"`
}

func (x *InitMandateRequest) Reset() {
	*x = InitMandateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateRequest) ProtoMessage() {}

func (x *InitMandateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateRequest.ProtoReflect.Descriptor instead.
func (*InitMandateRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{18}
}

func (x *InitMandateRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *InitMandateRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *InitMandateRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *InitMandateRequest) GetMandateType() MandateType {
	if x != nil {
		return x.MandateType
	}
	return MandateType_MANDATE_TYPE_UNSPECIFIED
}

func (x *InitMandateRequest) GetConsentCodeList() []ConsentType {
	if x != nil {
		return x.ConsentCodeList
	}
	return nil
}

func (x *InitMandateRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *InitMandateRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *InitMandateRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *InitMandateRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *InitMandateRequest) GetConsentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConsentTime
	}
	return nil
}

type InitMandateResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status         *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RedirectionUrl string      `protobuf:"bytes,2,opt,name=redirection_url,json=redirectionUrl,proto3" json:"redirection_url,omitempty"`
	// useful for tracking the status of mandate setup
	TrackingId         string                 `protobuf:"bytes,3,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	MandateUrlValidity *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=mandate_url_validity,json=mandateUrlValidity,proto3" json:"mandate_url_validity,omitempty"`
	MandateAmount      int64                  `protobuf:"varint,5,opt,name=mandate_amount,json=mandateAmount,proto3" json:"mandate_amount,omitempty"`
	// UMRN (Unique Mandate Reference Number) is a unique identifier generated by NPCI
	// when an e-NACH mandate is successfully registered. This field will be populated
	// when the mandate is already completed.
	Umrn string `protobuf:"bytes,6,opt,name=umrn,proto3" json:"umrn,omitempty"`
}

func (x *InitMandateResponse) Reset() {
	*x = InitMandateResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *InitMandateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitMandateResponse) ProtoMessage() {}

func (x *InitMandateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitMandateResponse.ProtoReflect.Descriptor instead.
func (*InitMandateResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{19}
}

func (x *InitMandateResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *InitMandateResponse) GetRedirectionUrl() string {
	if x != nil {
		return x.RedirectionUrl
	}
	return ""
}

func (x *InitMandateResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *InitMandateResponse) GetMandateUrlValidity() *timestamppb.Timestamp {
	if x != nil {
		return x.MandateUrlValidity
	}
	return nil
}

func (x *InitMandateResponse) GetMandateAmount() int64 {
	if x != nil {
		return x.MandateAmount
	}
	return 0
}

func (x *InitMandateResponse) GetUmrn() string {
	if x != nil {
		return x.Umrn
	}
	return ""
}

type CheckMandateStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	UserId string                       `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// Mandate Type "NACH_MANDATE" / "UPI_MANDATE",
	MandateType MandateType `protobuf:"varint,3,opt,name=mandate_type,json=mandateType,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.MandateType" json:"mandate_type,omitempty"`
	// The tracking_id that is present in InitMandateResponse
	TrackingId string `protobuf:"bytes,4,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
}

func (x *CheckMandateStatusRequest) Reset() {
	*x = CheckMandateStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusRequest) ProtoMessage() {}

func (x *CheckMandateStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusRequest.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{20}
}

func (x *CheckMandateStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *CheckMandateStatusRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CheckMandateStatusRequest) GetMandateType() MandateType {
	if x != nil {
		return x.MandateType
	}
	return MandateType_MANDATE_TYPE_UNSPECIFIED
}

func (x *CheckMandateStatusRequest) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

type CheckMandateStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	MandateStatus MandateStatus          `protobuf:"varint,2,opt,name=mandate_status,json=mandateStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.MandateStatus" json:"mandate_status,omitempty"`
	TrackingId    string                 `protobuf:"bytes,3,opt,name=tracking_id,json=trackingId,proto3" json:"tracking_id,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=completed_at,json=completedAt,proto3" json:"completed_at,omitempty"`
}

func (x *CheckMandateStatusResponse) Reset() {
	*x = CheckMandateStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckMandateStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckMandateStatusResponse) ProtoMessage() {}

func (x *CheckMandateStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckMandateStatusResponse.ProtoReflect.Descriptor instead.
func (*CheckMandateStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{21}
}

func (x *CheckMandateStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *CheckMandateStatusResponse) GetMandateStatus() MandateStatus {
	if x != nil {
		return x.MandateStatus
	}
	return MandateStatus_MANDATE_STATUS_UNSPECIFIED
}

func (x *CheckMandateStatusResponse) GetTrackingId() string {
	if x != nil {
		return x.TrackingId
	}
	return ""
}

func (x *CheckMandateStatusResponse) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

type GenerateKfsLaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// User id of the user who is signing the KFS LA
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
}

func (x *GenerateKfsLaRequest) Reset() {
	*x = GenerateKfsLaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateKfsLaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateKfsLaRequest) ProtoMessage() {}

func (x *GenerateKfsLaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateKfsLaRequest.ProtoReflect.Descriptor instead.
func (*GenerateKfsLaRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{22}
}

func (x *GenerateKfsLaRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GenerateKfsLaRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GenerateKfsLaRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GenerateKfsLaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status              *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	KfsDocUrl           string      `protobuf:"bytes,2,opt,name=kfs_doc_url,json=kfsDocUrl,proto3" json:"kfs_doc_url,omitempty"`
	LoanAgreementDocUrl string      `protobuf:"bytes,3,opt,name=loan_agreement_doc_url,json=loanAgreementDocUrl,proto3" json:"loan_agreement_doc_url,omitempty"`
}

func (x *GenerateKfsLaResponse) Reset() {
	*x = GenerateKfsLaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GenerateKfsLaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateKfsLaResponse) ProtoMessage() {}

func (x *GenerateKfsLaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateKfsLaResponse.ProtoReflect.Descriptor instead.
func (*GenerateKfsLaResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{23}
}

func (x *GenerateKfsLaResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GenerateKfsLaResponse) GetKfsDocUrl() string {
	if x != nil {
		return x.KfsDocUrl
	}
	return ""
}

func (x *GenerateKfsLaResponse) GetLoanAgreementDocUrl() string {
	if x != nil {
		return x.LoanAgreementDocUrl
	}
	return ""
}

type SignKfsLaRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	// User id of the user who is signing the KFS LA
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// List of consent codes that the user has consented to
	ConsentCodeList []ConsentType `protobuf:"varint,4,rep,packed,name=consent_code_list,json=consentCodeList,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.ConsentType" json:"consent_code_list,omitempty"`
	// denotes the user's device ip from which they are applying for the loan.
	UserIp string `protobuf:"bytes,5,opt,name=user_ip,json=userIp,proto3" json:"user_ip,omitempty"`
	// denotes the user's device id from which they are applying for the loan.
	DeviceId string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// [Optional] latitude and longitude of the user from where they are applying for the loan.
	Latitude  string `protobuf:"bytes,7,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Longitude string `protobuf:"bytes,8,opt,name=longitude,proto3" json:"longitude,omitempty"`
	// denotes the time when the user has given consent
	ConsentTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=consent_time,json=consentTime,proto3" json:"consent_time,omitempty"`
}

func (x *SignKfsLaRequest) Reset() {
	*x = SignKfsLaRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignKfsLaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignKfsLaRequest) ProtoMessage() {}

func (x *SignKfsLaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignKfsLaRequest.ProtoReflect.Descriptor instead.
func (*SignKfsLaRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{24}
}

func (x *SignKfsLaRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *SignKfsLaRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *SignKfsLaRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *SignKfsLaRequest) GetConsentCodeList() []ConsentType {
	if x != nil {
		return x.ConsentCodeList
	}
	return nil
}

func (x *SignKfsLaRequest) GetUserIp() string {
	if x != nil {
		return x.UserIp
	}
	return ""
}

func (x *SignKfsLaRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SignKfsLaRequest) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *SignKfsLaRequest) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *SignKfsLaRequest) GetConsentTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ConsentTime
	}
	return nil
}

type SignKfsLaResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                  *rpc.Status            `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	KfsDocUrl               string                 `protobuf:"bytes,2,opt,name=kfs_doc_url,json=kfsDocUrl,proto3" json:"kfs_doc_url,omitempty"`
	LoanAgreementDocUrl     string                 `protobuf:"bytes,3,opt,name=loan_agreement_doc_url,json=loanAgreementDocUrl,proto3" json:"loan_agreement_doc_url,omitempty"`
	ModifyRoiExpirationTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=modify_roi_expiration_time,json=modifyRoiExpirationTime,proto3" json:"modify_roi_expiration_time,omitempty"`
}

func (x *SignKfsLaResponse) Reset() {
	*x = SignKfsLaResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignKfsLaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignKfsLaResponse) ProtoMessage() {}

func (x *SignKfsLaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignKfsLaResponse.ProtoReflect.Descriptor instead.
func (*SignKfsLaResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{25}
}

func (x *SignKfsLaResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *SignKfsLaResponse) GetKfsDocUrl() string {
	if x != nil {
		return x.KfsDocUrl
	}
	return ""
}

func (x *SignKfsLaResponse) GetLoanAgreementDocUrl() string {
	if x != nil {
		return x.LoanAgreementDocUrl
	}
	return ""
}

func (x *SignKfsLaResponse) GetModifyRoiExpirationTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ModifyRoiExpirationTime
	}
	return nil
}

type GetLoanDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
}

func (x *GetLoanDetailsRequest) Reset() {
	*x = GetLoanDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsRequest) ProtoMessage() {}

func (x *GetLoanDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{26}
}

func (x *GetLoanDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetLoanDetailsRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type GetLoanDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status      *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	LoanStatus  LoanStatus          `protobuf:"varint,2,opt,name=loan_status,json=loanStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.LoanStatus" json:"loan_status,omitempty"`
	LoanDetails *lenden.LoanDetails `protobuf:"bytes,3,opt,name=loan_details,json=loanDetails,proto3" json:"loan_details,omitempty"`
}

func (x *GetLoanDetailsResponse) Reset() {
	*x = GetLoanDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetLoanDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLoanDetailsResponse) ProtoMessage() {}

func (x *GetLoanDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLoanDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetLoanDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{27}
}

func (x *GetLoanDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetLoanDetailsResponse) GetLoanStatus() LoanStatus {
	if x != nil {
		return x.LoanStatus
	}
	return LoanStatus_LOAN_STATUS_UNSPECIFIED
}

func (x *GetLoanDetailsResponse) GetLoanDetails() *lenden.LoanDetails {
	if x != nil {
		return x.LoanDetails
	}
	return nil
}

type GetPreDisbursementDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header         *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	Amount         *money.Money                 `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
	Tenure         int32                        `protobuf:"varint,3,opt,name=tenure,proto3" json:"tenure,omitempty"`
	RateOfInterest *decimal.Decimal             `protobuf:"bytes,4,opt,name=rate_of_interest,json=rateOfInterest,proto3" json:"rate_of_interest,omitempty"`
}

func (x *GetPreDisbursementDetailsRequest) Reset() {
	*x = GetPreDisbursementDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreDisbursementDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreDisbursementDetailsRequest) ProtoMessage() {}

func (x *GetPreDisbursementDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreDisbursementDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetPreDisbursementDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{28}
}

func (x *GetPreDisbursementDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetPreDisbursementDetailsRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

func (x *GetPreDisbursementDetailsRequest) GetTenure() int32 {
	if x != nil {
		return x.Tenure
	}
	return 0
}

func (x *GetPreDisbursementDetailsRequest) GetRateOfInterest() *decimal.Decimal {
	if x != nil {
		return x.RateOfInterest
	}
	return nil
}

type GetPreDisbursementDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status                 *rpc.Status             `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PreDisbursementDetails *PreDisbursementDetails `protobuf:"bytes,2,opt,name=pre_disbursement_details,json=preDisbursementDetails,proto3" json:"pre_disbursement_details,omitempty"`
}

func (x *GetPreDisbursementDetailsResponse) Reset() {
	*x = GetPreDisbursementDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPreDisbursementDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPreDisbursementDetailsResponse) ProtoMessage() {}

func (x *GetPreDisbursementDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPreDisbursementDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetPreDisbursementDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{29}
}

func (x *GetPreDisbursementDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPreDisbursementDetailsResponse) GetPreDisbursementDetails() *PreDisbursementDetails {
	if x != nil {
		return x.PreDisbursementDetails
	}
	return nil
}

type PreDisbursementDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fee charged for processing the loan.
	ProcessingFee *money.Money `protobuf:"bytes,1,opt,name=processing_fee,json=processingFee,proto3" json:"processing_fee,omitempty"`
	// The amount of money that will be disbursed to the borrower.
	DisbursalAmount *money.Money `protobuf:"bytes,2,opt,name=disbursal_amount,json=disbursalAmount,proto3" json:"disbursal_amount,omitempty"`
	// The total interest that will be paid over the life of the loan.
	TotalInterest *money.Money `protobuf:"bytes,3,opt,name=total_interest,json=totalInterest,proto3" json:"total_interest,omitempty"`
	// The total amount that will be repaid by the borrower, including principal and interest.
	TotalRepaymentAmount *money.Money `protobuf:"bytes,4,opt,name=total_repayment_amount,json=totalRepaymentAmount,proto3" json:"total_repayment_amount,omitempty"`
	// The amount of each installment payment.
	InstallmentAmount *money.Money `protobuf:"bytes,5,opt,name=installment_amount,json=installmentAmount,proto3" json:"installment_amount,omitempty"`
	// The date of the first installment payment.
	FirstInstallmentDate *date.Date `protobuf:"bytes,6,opt,name=first_installment_date,json=firstInstallmentDate,proto3" json:"first_installment_date,omitempty"`
	// The annual percentage rate (APR) of the loan.
	Apr float64 `protobuf:"fixed64,7,opt,name=apr,proto3" json:"apr,omitempty"`
	// The interest charged for the gap period between disbursement and the first installment.
	GapInterest *money.Money `protobuf:"bytes,8,opt,name=gap_interest,json=gapInterest,proto3" json:"gap_interest,omitempty"`
}

func (x *PreDisbursementDetails) Reset() {
	*x = PreDisbursementDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PreDisbursementDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreDisbursementDetails) ProtoMessage() {}

func (x *PreDisbursementDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreDisbursementDetails.ProtoReflect.Descriptor instead.
func (*PreDisbursementDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{30}
}

func (x *PreDisbursementDetails) GetProcessingFee() *money.Money {
	if x != nil {
		return x.ProcessingFee
	}
	return nil
}

func (x *PreDisbursementDetails) GetDisbursalAmount() *money.Money {
	if x != nil {
		return x.DisbursalAmount
	}
	return nil
}

func (x *PreDisbursementDetails) GetTotalInterest() *money.Money {
	if x != nil {
		return x.TotalInterest
	}
	return nil
}

func (x *PreDisbursementDetails) GetTotalRepaymentAmount() *money.Money {
	if x != nil {
		return x.TotalRepaymentAmount
	}
	return nil
}

func (x *PreDisbursementDetails) GetInstallmentAmount() *money.Money {
	if x != nil {
		return x.InstallmentAmount
	}
	return nil
}

func (x *PreDisbursementDetails) GetFirstInstallmentDate() *date.Date {
	if x != nil {
		return x.FirstInstallmentDate
	}
	return nil
}

func (x *PreDisbursementDetails) GetApr() float64 {
	if x != nil {
		return x.Apr
	}
	return 0
}

func (x *PreDisbursementDetails) GetGapInterest() *money.Money {
	if x != nil {
		return x.GapInterest
	}
	return nil
}

type PostExternalDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header      *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId      string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	UserId      string                       `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Data        []byte                       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	BankDetails *BankDetails                 `protobuf:"bytes,5,opt,name=bank_details,json=bankDetails,proto3" json:"bank_details,omitempty"`
	Type        EligibilityDataType          `protobuf:"varint,6,opt,name=type,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.EligibilityDataType" json:"type,omitempty"`
}

func (x *PostExternalDataRequest) Reset() {
	*x = PostExternalDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostExternalDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostExternalDataRequest) ProtoMessage() {}

func (x *PostExternalDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostExternalDataRequest.ProtoReflect.Descriptor instead.
func (*PostExternalDataRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{31}
}

func (x *PostExternalDataRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *PostExternalDataRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *PostExternalDataRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *PostExternalDataRequest) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *PostExternalDataRequest) GetBankDetails() *BankDetails {
	if x != nil {
		return x.BankDetails
	}
	return nil
}

func (x *PostExternalDataRequest) GetType() EligibilityDataType {
	if x != nil {
		return x.Type
	}
	return EligibilityDataType_ELIGIBILITY_DATA_TYPE_UNSPECIFIED
}

type BankDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	HolderName    string      `protobuf:"bytes,1,opt,name=holder_name,json=holderName,proto3" json:"holder_name,omitempty"`
	AccountNumber string      `protobuf:"bytes,2,opt,name=account_number,json=accountNumber,proto3" json:"account_number,omitempty"`
	Ifsc          string      `protobuf:"bytes,3,opt,name=ifsc,proto3" json:"ifsc,omitempty"`
	Type          AccountType `protobuf:"varint,4,opt,name=type,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.AccountType" json:"type,omitempty"`
}

func (x *BankDetails) Reset() {
	*x = BankDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BankDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BankDetails) ProtoMessage() {}

func (x *BankDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BankDetails.ProtoReflect.Descriptor instead.
func (*BankDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{32}
}

func (x *BankDetails) GetHolderName() string {
	if x != nil {
		return x.HolderName
	}
	return ""
}

func (x *BankDetails) GetAccountNumber() string {
	if x != nil {
		return x.AccountNumber
	}
	return ""
}

func (x *BankDetails) GetIfsc() string {
	if x != nil {
		return x.Ifsc
	}
	return ""
}

func (x *BankDetails) GetType() AccountType {
	if x != nil {
		return x.Type
	}
	return AccountType_ACCOUNT_TYPE_UNSPECIFIED
}

type PostExternalDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *PostExternalDataResponse) Reset() {
	*x = PostExternalDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PostExternalDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostExternalDataResponse) ProtoMessage() {}

func (x *PostExternalDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostExternalDataResponse.ProtoReflect.Descriptor instead.
func (*PostExternalDataResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{33}
}

func (x *PostExternalDataResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetAmortizationScheduleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
}

func (x *GetAmortizationScheduleRequest) Reset() {
	*x = GetAmortizationScheduleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmortizationScheduleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmortizationScheduleRequest) ProtoMessage() {}

func (x *GetAmortizationScheduleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmortizationScheduleRequest.ProtoReflect.Descriptor instead.
func (*GetAmortizationScheduleRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{34}
}

func (x *GetAmortizationScheduleRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetAmortizationScheduleRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

type AmortizationAmountBreakupComponent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Purpose AmortizationComponentPurpose `protobuf:"varint,1,opt,name=purpose,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.AmortizationComponentPurpose" json:"purpose,omitempty"`
	Amount  *money.Money                 `protobuf:"bytes,2,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *AmortizationAmountBreakupComponent) Reset() {
	*x = AmortizationAmountBreakupComponent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmortizationAmountBreakupComponent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmortizationAmountBreakupComponent) ProtoMessage() {}

func (x *AmortizationAmountBreakupComponent) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmortizationAmountBreakupComponent.ProtoReflect.Descriptor instead.
func (*AmortizationAmountBreakupComponent) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{35}
}

func (x *AmortizationAmountBreakupComponent) GetPurpose() AmortizationComponentPurpose {
	if x != nil {
		return x.Purpose
	}
	return AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_UNSPECIFIED
}

func (x *AmortizationAmountBreakupComponent) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type AmortizationScheduleItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DueDate           *date.Date                            `protobuf:"bytes,1,opt,name=due_date,json=dueDate,proto3" json:"due_date,omitempty"`
	DueAmount         *money.Money                          `protobuf:"bytes,2,opt,name=due_amount,json=dueAmount,proto3" json:"due_amount,omitempty"`
	Breakup           []*AmortizationAmountBreakupComponent `protobuf:"bytes,3,rep,name=breakup,proto3" json:"breakup,omitempty"`
	Status            AmortizationScheduleItemStatus        `protobuf:"varint,4,opt,name=status,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItemStatus" json:"status,omitempty"`
	OutstandingAmount *money.Money                          `protobuf:"bytes,5,opt,name=outstanding_amount,json=outstandingAmount,proto3" json:"outstanding_amount,omitempty"`
}

func (x *AmortizationScheduleItem) Reset() {
	*x = AmortizationScheduleItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AmortizationScheduleItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AmortizationScheduleItem) ProtoMessage() {}

func (x *AmortizationScheduleItem) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AmortizationScheduleItem.ProtoReflect.Descriptor instead.
func (*AmortizationScheduleItem) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{36}
}

func (x *AmortizationScheduleItem) GetDueDate() *date.Date {
	if x != nil {
		return x.DueDate
	}
	return nil
}

func (x *AmortizationScheduleItem) GetDueAmount() *money.Money {
	if x != nil {
		return x.DueAmount
	}
	return nil
}

func (x *AmortizationScheduleItem) GetBreakup() []*AmortizationAmountBreakupComponent {
	if x != nil {
		return x.Breakup
	}
	return nil
}

func (x *AmortizationScheduleItem) GetStatus() AmortizationScheduleItemStatus {
	if x != nil {
		return x.Status
	}
	return AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UNSPECIFIED
}

func (x *AmortizationScheduleItem) GetOutstandingAmount() *money.Money {
	if x != nil {
		return x.OutstandingAmount
	}
	return nil
}

type GetAmortizationScheduleResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status               *rpc.Status                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	AmortizationSchedule []*AmortizationScheduleItem `protobuf:"bytes,2,rep,name=amortization_schedule,json=amortizationSchedule,proto3" json:"amortization_schedule,omitempty"`
}

func (x *GetAmortizationScheduleResponse) Reset() {
	*x = GetAmortizationScheduleResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAmortizationScheduleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAmortizationScheduleResponse) ProtoMessage() {}

func (x *GetAmortizationScheduleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAmortizationScheduleResponse.ProtoReflect.Descriptor instead.
func (*GetAmortizationScheduleResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{37}
}

func (x *GetAmortizationScheduleResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetAmortizationScheduleResponse) GetAmortizationSchedule() []*AmortizationScheduleItem {
	if x != nil {
		return x.AmortizationSchedule
	}
	return nil
}

type GetForeclosureDetailsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *vendorgateway.RequestHeader         `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId  string                               `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	Purpose GetForeclosureDetailsRequest_Purpose `protobuf:"varint,3,opt,name=purpose,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest_Purpose" json:"purpose,omitempty"`
}

func (x *GetForeclosureDetailsRequest) Reset() {
	*x = GetForeclosureDetailsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetForeclosureDetailsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetForeclosureDetailsRequest) ProtoMessage() {}

func (x *GetForeclosureDetailsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetForeclosureDetailsRequest.ProtoReflect.Descriptor instead.
func (*GetForeclosureDetailsRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{38}
}

func (x *GetForeclosureDetailsRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetForeclosureDetailsRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GetForeclosureDetailsRequest) GetPurpose() GetForeclosureDetailsRequest_Purpose {
	if x != nil {
		return x.Purpose
	}
	return GetForeclosureDetailsRequest_PURPOSE_UNSPECIFIED
}

type GetForeclosureDetailsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status             *rpc.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ForeclosureDetails *ForeclosureDetails `protobuf:"bytes,2,opt,name=foreclosure_details,json=foreclosureDetails,proto3" json:"foreclosure_details,omitempty"`
}

func (x *GetForeclosureDetailsResponse) Reset() {
	*x = GetForeclosureDetailsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetForeclosureDetailsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetForeclosureDetailsResponse) ProtoMessage() {}

func (x *GetForeclosureDetailsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetForeclosureDetailsResponse.ProtoReflect.Descriptor instead.
func (*GetForeclosureDetailsResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{39}
}

func (x *GetForeclosureDetailsResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetForeclosureDetailsResponse) GetForeclosureDetails() *ForeclosureDetails {
	if x != nil {
		return x.ForeclosureDetails
	}
	return nil
}

type ForeclosureDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PrincipalOutstanding    *money.Money `protobuf:"bytes,1,opt,name=principal_outstanding,json=principalOutstanding,proto3" json:"principal_outstanding,omitempty"`
	InterestOutstanding     *money.Money `protobuf:"bytes,2,opt,name=interest_outstanding,json=interestOutstanding,proto3" json:"interest_outstanding,omitempty"`
	DelayChargesOutstanding *money.Money `protobuf:"bytes,3,opt,name=delay_charges_outstanding,json=delayChargesOutstanding,proto3" json:"delay_charges_outstanding,omitempty"`
	LateFeeOutstanding      *money.Money `protobuf:"bytes,4,opt,name=late_fee_outstanding,json=lateFeeOutstanding,proto3" json:"late_fee_outstanding,omitempty"`
	ForeclosureAmount       *money.Money `protobuf:"bytes,5,opt,name=foreclosure_amount,json=foreclosureAmount,proto3" json:"foreclosure_amount,omitempty"`
	ForeclosureCharges      *money.Money `protobuf:"bytes,6,opt,name=foreclosure_charges,json=foreclosureCharges,proto3" json:"foreclosure_charges,omitempty"`
	RepaymentReceived       *money.Money `protobuf:"bytes,7,opt,name=repayment_received,json=repaymentReceived,proto3" json:"repayment_received,omitempty"`
}

func (x *ForeclosureDetails) Reset() {
	*x = ForeclosureDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForeclosureDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForeclosureDetails) ProtoMessage() {}

func (x *ForeclosureDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForeclosureDetails.ProtoReflect.Descriptor instead.
func (*ForeclosureDetails) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{40}
}

func (x *ForeclosureDetails) GetPrincipalOutstanding() *money.Money {
	if x != nil {
		return x.PrincipalOutstanding
	}
	return nil
}

func (x *ForeclosureDetails) GetInterestOutstanding() *money.Money {
	if x != nil {
		return x.InterestOutstanding
	}
	return nil
}

func (x *ForeclosureDetails) GetDelayChargesOutstanding() *money.Money {
	if x != nil {
		return x.DelayChargesOutstanding
	}
	return nil
}

func (x *ForeclosureDetails) GetLateFeeOutstanding() *money.Money {
	if x != nil {
		return x.LateFeeOutstanding
	}
	return nil
}

func (x *ForeclosureDetails) GetForeclosureAmount() *money.Money {
	if x != nil {
		return x.ForeclosureAmount
	}
	return nil
}

func (x *ForeclosureDetails) GetForeclosureCharges() *money.Money {
	if x != nil {
		return x.ForeclosureCharges
	}
	return nil
}

func (x *ForeclosureDetails) GetRepaymentReceived() *money.Money {
	if x != nil {
		return x.RepaymentReceived
	}
	return nil
}

type GeneratePaymentLinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	LoanId string                       `protobuf:"bytes,2,opt,name=loan_id,json=loanId,proto3" json:"loan_id,omitempty"`
	Amount *money.Money                 `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
}

func (x *GeneratePaymentLinkRequest) Reset() {
	*x = GeneratePaymentLinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePaymentLinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePaymentLinkRequest) ProtoMessage() {}

func (x *GeneratePaymentLinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePaymentLinkRequest.ProtoReflect.Descriptor instead.
func (*GeneratePaymentLinkRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{41}
}

func (x *GeneratePaymentLinkRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GeneratePaymentLinkRequest) GetLoanId() string {
	if x != nil {
		return x.LoanId
	}
	return ""
}

func (x *GeneratePaymentLinkRequest) GetAmount() *money.Money {
	if x != nil {
		return x.Amount
	}
	return nil
}

type GeneratePaymentLinkResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status  *rpc.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Url     string      `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	OrderId string      `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *GeneratePaymentLinkResponse) Reset() {
	*x = GeneratePaymentLinkResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GeneratePaymentLinkResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GeneratePaymentLinkResponse) ProtoMessage() {}

func (x *GeneratePaymentLinkResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GeneratePaymentLinkResponse.ProtoReflect.Descriptor instead.
func (*GeneratePaymentLinkResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{42}
}

func (x *GeneratePaymentLinkResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GeneratePaymentLinkResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *GeneratePaymentLinkResponse) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type GetPaymentStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header  *vendorgateway.RequestHeader `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	OrderId string                       `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *GetPaymentStatusRequest) Reset() {
	*x = GetPaymentStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentStatusRequest) ProtoMessage() {}

func (x *GetPaymentStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentStatusRequest.ProtoReflect.Descriptor instead.
func (*GetPaymentStatusRequest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{43}
}

func (x *GetPaymentStatusRequest) GetHeader() *vendorgateway.RequestHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *GetPaymentStatusRequest) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

type GetPaymentStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        *rpc.Status   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PaymentStatus PaymentStatus `protobuf:"varint,2,opt,name=payment_status,json=paymentStatus,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.PaymentStatus" json:"payment_status,omitempty"`
	FailureReason string        `protobuf:"bytes,3,opt,name=failure_reason,json=failureReason,proto3" json:"failure_reason,omitempty"`
}

func (x *GetPaymentStatusResponse) Reset() {
	*x = GetPaymentStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetPaymentStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPaymentStatusResponse) ProtoMessage() {}

func (x *GetPaymentStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPaymentStatusResponse.ProtoReflect.Descriptor instead.
func (*GetPaymentStatusResponse) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{44}
}

func (x *GetPaymentStatusResponse) GetStatus() *rpc.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPaymentStatusResponse) GetPaymentStatus() PaymentStatus {
	if x != nil {
		return x.PaymentStatus
	}
	return PaymentStatus_PAYMENT_STATUS_UNSPECIFIED
}

func (x *GetPaymentStatusResponse) GetFailureReason() string {
	if x != nil {
		return x.FailureReason
	}
	return ""
}

type ApplyForLoanRequest_Interest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type      InterestType      `protobuf:"varint,1,opt,name=type,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.InterestType" json:"type,omitempty"`
	Frequency InterestFrequency `protobuf:"varint,2,opt,name=frequency,proto3,enum=vendorgateway.lending.preapprovedloan.lenden.InterestFrequency" json:"frequency,omitempty"`
	Value     float32           `protobuf:"fixed32,3,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *ApplyForLoanRequest_Interest) Reset() {
	*x = ApplyForLoanRequest_Interest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ApplyForLoanRequest_Interest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyForLoanRequest_Interest) ProtoMessage() {}

func (x *ApplyForLoanRequest_Interest) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyForLoanRequest_Interest.ProtoReflect.Descriptor instead.
func (*ApplyForLoanRequest_Interest) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ApplyForLoanRequest_Interest) GetType() InterestType {
	if x != nil {
		return x.Type
	}
	return InterestType_INTEREST_TYPE_UNSPECIFIED
}

func (x *ApplyForLoanRequest_Interest) GetFrequency() InterestFrequency {
	if x != nil {
		return x.Frequency
	}
	return InterestFrequency_INTEREST_FREQUENCY_UNSPECIFIED
}

func (x *ApplyForLoanRequest_Interest) GetValue() float32 {
	if x != nil {
		return x.Value
	}
	return 0
}

type OfferData_ApplicableAmountToTenure struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	MinAmount *money.Money `protobuf:"bytes,1,opt,name=min_amount,json=minAmount,proto3" json:"min_amount,omitempty"`
	MaxAmount *money.Money `protobuf:"bytes,2,opt,name=max_amount,json=maxAmount,proto3" json:"max_amount,omitempty"`
	MinTenure int32        `protobuf:"varint,3,opt,name=min_tenure,json=minTenure,proto3" json:"min_tenure,omitempty"`
	MaxTenure int32        `protobuf:"varint,4,opt,name=max_tenure,json=maxTenure,proto3" json:"max_tenure,omitempty"`
}

func (x *OfferData_ApplicableAmountToTenure) Reset() {
	*x = OfferData_ApplicableAmountToTenure{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OfferData_ApplicableAmountToTenure) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OfferData_ApplicableAmountToTenure) ProtoMessage() {}

func (x *OfferData_ApplicableAmountToTenure) ProtoReflect() protoreflect.Message {
	mi := &file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OfferData_ApplicableAmountToTenure.ProtoReflect.Descriptor instead.
func (*OfferData_ApplicableAmountToTenure) Descriptor() ([]byte, []int) {
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP(), []int{6, 0}
}

func (x *OfferData_ApplicableAmountToTenure) GetMinAmount() *money.Money {
	if x != nil {
		return x.MinAmount
	}
	return nil
}

func (x *OfferData_ApplicableAmountToTenure) GetMaxAmount() *money.Money {
	if x != nil {
		return x.MaxAmount
	}
	return nil
}

func (x *OfferData_ApplicableAmountToTenure) GetMinTenure() int32 {
	if x != nil {
		return x.MinTenure
	}
	return 0
}

func (x *OfferData_ApplicableAmountToTenure) GetMaxTenure() int32 {
	if x != nil {
		return x.MaxTenure
	}
	return 0
}

var File_api_vendorgateway_lending_preapprovedloan_lenden_service_proto protoreflect.FileDescriptor

var file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDesc = []byte{
	0x0a, 0x3e, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x2c, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x1a, 0x14,
	0x61, 0x70, 0x69, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x2d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x73, 0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x6e, 0x6b, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1d, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6e, 0x61, 0x6d, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x25, 0x61, 0x70, 0x69, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76,
	0x32, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x3c, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x26, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x29, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x73, 0x2f, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2f, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79,
	0x70, 0x65, 0x2f, 0x64, 0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x6d, 0x6f,
	0x6e, 0x65, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xcf, 0x06, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x36,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x03, 0x70, 0x61,
	0x6e, 0x12, 0x4c, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02,
	0x10, 0x01, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12,
	0x1d, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x60, 0x01, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x2d,
	0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x03, 0x64, 0x6f, 0x62, 0x12, 0x2a, 0x0a,
	0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x12, 0x75, 0x0a, 0x0f, 0x65, 0x6d, 0x70,
	0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x42, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x0e, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x40, 0x0a, 0x1c, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x65, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x65, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72,
	0x02, 0x70, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a, 0x09, 0x64,
	0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49,
	0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a,
	0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x22, 0x52, 0x0a, 0x12, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xeb, 0x05, 0x0a, 0x13, 0x41, 0x70, 0x70,
	0x6c, 0x79, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01,
	0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0b, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x10, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65,
	0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e,
	0x65, 0x79, 0x52, 0x0f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x70, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x08, 0x69, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x66, 0x0a, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00,
	0x52, 0x0b, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x79, 0x70, 0x65, 0x12, 0x45, 0x0a,
	0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73, 0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x07, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x1a, 0xef, 0x01, 0x0a, 0x08, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x12, 0x58, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x67, 0x0a, 0x09, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x42,
	0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x09, 0x66, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x79, 0x12, 0x20, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x02, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x0a, 0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x8e, 0x01, 0x0a, 0x14, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x22, 0x38, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x26, 0x0a, 0x22, 0x4d, 0x41, 0x58, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x41, 0x43, 0x43, 0x4f,
	0x55, 0x4e, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x43, 0x48, 0x45, 0x44, 0x5f, 0x41, 0x54, 0x5f, 0x56,
	0x45, 0x4e, 0x44, 0x4f, 0x52, 0x10, 0x65, 0x22, 0x97, 0x01, 0x0a, 0x1b, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x48, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a,
	0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12,
	0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x22, 0x8b, 0x02, 0x0a, 0x1c, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x61, 0x72, 0x64, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x6e, 0x0a, 0x12, 0x65, 0x6c, 0x69, 0x67, 0x69,
	0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x2e, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x11, 0x65, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x0a, 0x6f, 0x66, 0x66, 0x65, 0x72,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x22,
	0xc4, 0x04, 0x0a, 0x09, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x12, 0x55, 0x0a,
	0x06, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3d, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50, 0x32, 0x70,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x06, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x18, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x16, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x12, 0x31,
	0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x61, 0x78, 0x41, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x7f, 0x0a, 0x12, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x50, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72,
	0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e,
	0x4f, 0x66, 0x66, 0x65, 0x72, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x54, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x52, 0x11, 0x61, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x62, 0x6c, 0x65, 0x54, 0x65,
	0x6e, 0x75, 0x72, 0x65, 0x73, 0x1a, 0xbe, 0x01, 0x0a, 0x18, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63,
	0x61, 0x62, 0x6c, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x6f, 0x54, 0x65, 0x6e, 0x75,
	0x72, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d, 0x69, 0x6e, 0x41,
	0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x31, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x09, 0x6d,
	0x61, 0x78, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x69, 0x6e, 0x5f,
	0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x69,
	0x6e, 0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61, 0x78, 0x5f, 0x74,
	0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6d, 0x61, 0x78,
	0x54, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x22, 0x82, 0x02, 0x0a, 0x0f, 0x50, 0x32, 0x70, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x72, 0x6f, 0x69,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x01, 0x52, 0x03, 0x72, 0x6f, 0x69, 0x12, 0x2f, 0x0a, 0x13, 0x66,
	0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x70, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69,
	0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x6f, 0x62, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x1c,
	0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x74, 0x6f,
	0x5f, 0x67, 0x65, 0x74, 0x5f, 0x66, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x18, 0x65, 0x78, 0x70, 0x65, 0x63, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x54, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x75, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x25, 0x0a, 0x0e,
	0x69, 0x73, 0x5f, 0x72, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e, 0x64, 0x65, 0x64, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x0d, 0x69, 0x73, 0x52, 0x65, 0x63, 0x6f, 0x6d, 0x6d, 0x65, 0x6e,
	0x64, 0x65, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f, 0x72, 0x6f,
	0x69, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x06, 0x20, 0x03, 0x28, 0x01, 0x52, 0x0d, 0x6d, 0x6f,
	0x64, 0x69, 0x66, 0x79, 0x52, 0x6f, 0x69, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xa2, 0x02, 0x0a, 0x12,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02,
	0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0f,
	0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74,
	0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01,
	0x02, 0x10, 0x01, 0x52, 0x0e, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x12, 0x33, 0x0a, 0x11, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x0f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x65, 0x64, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x59, 0x0a, 0x13, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a,
	0x6f, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x22, 0xe7, 0x03, 0x0a, 0x1b,
	0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61,
	0x6e, 0x49, 0x64, 0x12, 0x2f, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f,
	0x72, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x02, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x0a,
	0x05, 0x25, 0x00, 0x00, 0x00, 0x00, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x52, 0x61, 0x74, 0x65, 0x12, 0x65, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32,
	0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x70, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a,
	0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0xdb, 0x01, 0x0a, 0x1c, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79,
	0x52, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41, 0x0a, 0x12, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1e,
	0x0a, 0x0b, 0x6b, 0x66, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x33,
	0x0a, 0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x6f, 0x63,
	0x55, 0x72, 0x6c, 0x22, 0xbc, 0x03, 0x0a, 0x0e, 0x4b, 0x79, 0x63, 0x49, 0x6e, 0x69, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x65, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43,
	0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x70,
	0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c,
	0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x6f, 0x6e,
	0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05,
	0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x22, 0xf9, 0x01, 0x0a, 0x0f, 0x4b, 0x79, 0x63, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x0a, 0x6b,
	0x79, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x37, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4b,
	0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x72, 0x65,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x1f, 0x0a,
	0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6b, 0x79, 0x63, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x99,
	0x01, 0x0a, 0x15, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a,
	0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x95, 0x01, 0x0a, 0x16, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x56, 0x0a, 0x0a, 0x6b, 0x79,
	0x63, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x37,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4b, 0x59,
	0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x09, 0x6b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xf5, 0x01, 0x0a, 0x15, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06,
	0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06,
	0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x62, 0x0a, 0x14, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x73,
	0x76, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x42, 0x08, 0xfa, 0x42,
	0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x12, 0x62, 0x61, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0xb4, 0x01, 0x0a, 0x16, 0x41,
	0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x75, 0x0a, 0x06, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x4e, 0x4f, 0x54,
	0x5f, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x41, 0x4d,
	0x45, 0x5f, 0x4d, 0x49, 0x53, 0x4d, 0x41, 0x54, 0x43, 0x48, 0x10, 0x66, 0x12, 0x19, 0x0a, 0x15,
	0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x43, 0x4f, 0x4e, 0x4e, 0x45, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x67, 0x12, 0x18, 0x0a, 0x14, 0x49, 0x4e, 0x56, 0x41, 0x4c,
	0x49, 0x44, 0x5f, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x44, 0x45, 0x54, 0x41, 0x49, 0x4c, 0x53, 0x10,
	0x68, 0x22, 0xa8, 0x04, 0x0a, 0x12, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e,
	0x49, 0x64, 0x12, 0x66, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x65, 0x0a, 0x11, 0x63, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x70, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x70, 0x12, 0x24, 0x0a, 0x09, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52,
	0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74,
	0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75,
	0x64, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74,
	0x75, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x0c, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08, 0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52,
	0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xbe, 0x03, 0x0a,
	0x13, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x65, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x72, 0x65, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55,
	0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x14, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x75,
	0x72, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x12, 0x6d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x55, 0x72, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x69, 0x74,
	0x79, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0d, 0x6d, 0x61, 0x6e, 0x64, 0x61,
	0x74, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6d, 0x72, 0x6e,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6d, 0x72, 0x6e, 0x22, 0xae, 0x01, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12,
	0x17, 0x0a, 0x13, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f, 0x4c, 0x4f, 0x41, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x4d, 0x41, 0x4e, 0x44,
	0x41, 0x54, 0x45, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x66, 0x12, 0x26, 0x0a, 0x22, 0x4f, 0x46, 0x46,
	0x45, 0x52, 0x5f, 0x4f, 0x52, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e, 0x54, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10,
	0x67, 0x12, 0x1d, 0x0a, 0x19, 0x42, 0x41, 0x4e, 0x4b, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x56, 0x45, 0x52, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x68,
	0x12, 0x1b, 0x0a, 0x17, 0x45, 0x4e, 0x41, 0x43, 0x48, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44,
	0x59, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x69, 0x22, 0x85, 0x02,
	0x0a, 0x19, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x66, 0x0a, 0x0c, 0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x82, 0x01, 0x02, 0x20, 0x00, 0x52, 0x0b,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x28, 0x0a, 0x0b, 0x74,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x49, 0x64, 0x22, 0x85, 0x02, 0x0a, 0x1a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d,
	0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x0e, 0x6d, 0x61, 0x6e,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x2e, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d,
	0x6d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x3d,
	0x0a, 0x0c, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0b, 0x63, 0x6f, 0x6d, 0x70, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x41, 0x74, 0x22, 0x90, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa,
	0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x20,
	0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x91, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x66, 0x73,
	0x4c, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x1e, 0x0a, 0x0b, 0x6b, 0x66, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x55, 0x72, 0x6c, 0x12,
	0x33, 0x0a, 0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x6f,
	0x63, 0x55, 0x72, 0x6c, 0x22, 0xbe, 0x03, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x4b, 0x66, 0x73,
	0x4c, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49,
	0x64, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x65, 0x0a, 0x11, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x63,
	0x6f, 0x64, 0x65, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x39,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x6f,
	0x6e, 0x73, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x63, 0x6f, 0x6e, 0x73, 0x65,
	0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x07, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x72, 0x02, 0x70, 0x01, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x70, 0x12, 0x24, 0x0a, 0x09,
	0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x08, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x74, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x1c,
	0x0a, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x6f, 0x6e, 0x67, 0x69, 0x74, 0x75, 0x64, 0x65, 0x12, 0x47, 0x0a, 0x0c,
	0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x08,
	0xfa, 0x42, 0x05, 0xb2, 0x01, 0x02, 0x08, 0x01, 0x52, 0x0b, 0x63, 0x6f, 0x6e, 0x73, 0x65, 0x6e,
	0x74, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xe6, 0x01, 0x0a, 0x11, 0x53, 0x69, 0x67, 0x6e, 0x4b, 0x66,
	0x73, 0x4c, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70,
	0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1e, 0x0a, 0x0b, 0x6b, 0x66, 0x73, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6b, 0x66, 0x73, 0x44, 0x6f, 0x63, 0x55, 0x72, 0x6c,
	0x12, 0x33, 0x0a, 0x16, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x61, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x64, 0x6f, 0x63, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x6c, 0x6f, 0x61, 0x6e, 0x41, 0x67, 0x72, 0x65, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44,
	0x6f, 0x63, 0x55, 0x72, 0x6c, 0x12, 0x57, 0x0a, 0x1a, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x5f,
	0x72, 0x6f, 0x69, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x17, 0x6d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x6f, 0x69,
	0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x66,
	0x0a, 0x15, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x22, 0xd8, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x6c, 0x6f, 0x61, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x3e, 0x0a, 0x0c, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x73, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x6c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x22, 0xf9, 0x01, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62,
	0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x06,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x06, 0x74, 0x65, 0x6e, 0x75, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x20, 0x00, 0x52, 0x06, 0x74, 0x65, 0x6e,
	0x75, 0x72, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x5f, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x65, 0x63, 0x69,
	0x6d, 0x61, 0x6c, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x0e, 0x72,
	0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x22, 0xec, 0x01,
	0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7e, 0x0a, 0x18, 0x70, 0x72, 0x65, 0x5f,
	0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x44, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73,
	0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x16, 0x70, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x22, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x06, 0x0a, 0x02, 0x4f, 0x4b, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x55,
	0x54, 0x5f, 0x4f, 0x46, 0x5f, 0x52, 0x41, 0x4e, 0x47, 0x45, 0x10, 0x65, 0x22, 0xec, 0x03, 0x0a,
	0x16, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x0e, 0x70, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x5f, 0x66, 0x65, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x0d, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x46,
	0x65, 0x65, 0x12, 0x3d, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x0f, 0x64, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x61, 0x6c, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x39, 0x0a, 0x0e, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0d, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x12, 0x48, 0x0a, 0x16,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79,
	0x52, 0x14, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x52, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x41, 0x0a, 0x12, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d,
	0x65, 0x6e, 0x74, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x47, 0x0a, 0x16, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x14, 0x66, 0x69,
	0x72, 0x73, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x61,
	0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x70, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52,
	0x03, 0x61, 0x70, 0x72, 0x12, 0x35, 0x0a, 0x0c, 0x67, 0x61, 0x70, 0x5f, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x65, 0x73, 0x74, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x0b,
	0x67, 0x61, 0x70, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x22, 0xca, 0x02, 0x0a, 0x17,
	0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a,
	0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x12, 0x5c, 0x0a, 0x0c, 0x62, 0x61, 0x6e, 0x6b, 0x5f, 0x64, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x73, 0x52, 0x0b, 0x62, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x55, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x45,
	0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x6e,
	0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x68, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x68,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x69, 0x66, 0x73, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x69, 0x66, 0x73, 0x63, 0x12, 0x4d, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x39, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x3f, 0x0a, 0x18, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x6f, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x72, 0x74,
	0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65,
	0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17, 0x0a, 0x07,
	0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6c,
	0x6f, 0x61, 0x6e, 0x49, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x22, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x72, 0x65, 0x61,
	0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x64, 0x0a, 0x07,
	0x70, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4a, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x12, 0x2a, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65,
	0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x90,
	0x03, 0x0a, 0x18, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x2c, 0x0a, 0x08, 0x64,
	0x75, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x07, 0x64, 0x75, 0x65, 0x44, 0x61, 0x74, 0x65, 0x12, 0x31, 0x0a, 0x0a, 0x64, 0x75, 0x65,
	0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65,
	0x79, 0x52, 0x09, 0x64, 0x75, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x6a, 0x0a, 0x07,
	0x62, 0x72, 0x65, 0x61, 0x6b, 0x75, 0x70, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x50, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x6d, 0x6f,
	0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x42,
	0x72, 0x65, 0x61, 0x6b, 0x75, 0x70, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x52,
	0x07, 0x62, 0x72, 0x65, 0x61, 0x6b, 0x75, 0x70, 0x12, 0x64, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x41,
	0x0a, 0x12, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11,
	0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xc3, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x7b, 0x0a, 0x15, 0x61, 0x6d,
	0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x46, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x74, 0x65,
	0x6d, 0x52, 0x14, 0x61, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x22, 0xae, 0x02, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x46,
	0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x17,
	0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x6c, 0x0a, 0x07, 0x70, 0x75, 0x72, 0x70, 0x6f,
	0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x52, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65, 0x52, 0x07, 0x70, 0x75,
	0x72, 0x70, 0x6f, 0x73, 0x65, 0x22, 0x51, 0x0a, 0x07, 0x50, 0x75, 0x72, 0x70, 0x6f, 0x73, 0x65,
	0x12, 0x17, 0x0a, 0x13, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x55, 0x52,
	0x50, 0x4f, 0x53, 0x45, 0x5f, 0x46, 0x4f, 0x52, 0x45, 0x43, 0x4c, 0x4f, 0x53, 0x55, 0x52, 0x45,
	0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x55, 0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x43, 0x4f,
	0x4f, 0x4c, 0x5f, 0x4f, 0x46, 0x46, 0x10, 0x02, 0x22, 0x8f, 0x02, 0x0a, 0x1d, 0x47, 0x65, 0x74,
	0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x71, 0x0a, 0x13, 0x66, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x64,
	0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x40, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x46, 0x6f, 0x72, 0x65,
	0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x12,
	0x66, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x22, 0x56, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x06, 0x0a, 0x02,
	0x4f, 0x4b, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x5f,
	0x4c, 0x4f, 0x41, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x65, 0x12, 0x12, 0x0a,
	0x0e, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10,
	0x66, 0x12, 0x17, 0x0a, 0x13, 0x4c, 0x41, 0x53, 0x54, 0x5f, 0x44, 0x55, 0x45, 0x5f, 0x44, 0x41,
	0x54, 0x45, 0x5f, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x10, 0x67, 0x22, 0x85, 0x04, 0x0a, 0x12, 0x46,
	0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x73, 0x12, 0x47, 0x0a, 0x15, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x6f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d,
	0x6f, 0x6e, 0x65, 0x79, 0x52, 0x14, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x4f,
	0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x45, 0x0a, 0x14, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x13, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x65, 0x73, 0x74, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x4e, 0x0a, 0x19, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67,
	0x65, 0x73, 0x5f, 0x6f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x17, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x43,
	0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x4f, 0x75, 0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x12, 0x44, 0x0a, 0x14, 0x6c, 0x61, 0x74, 0x65, 0x5f, 0x66, 0x65, 0x65, 0x5f, 0x6f, 0x75,
	0x74, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f,
	0x6e, 0x65, 0x79, 0x52, 0x12, 0x6c, 0x61, 0x74, 0x65, 0x46, 0x65, 0x65, 0x4f, 0x75, 0x74, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x41, 0x0a, 0x12, 0x66, 0x6f, 0x72, 0x65, 0x63,
	0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x11, 0x66, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x43, 0x0a, 0x13, 0x66, 0x6f,
	0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x67, 0x65,
	0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52, 0x12, 0x66, 0x6f, 0x72,
	0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x73, 0x12,
	0x41, 0x0a, 0x12, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x72, 0x65, 0x63,
	0x65, 0x69, 0x76, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x52,
	0x11, 0x72, 0x65, 0x70, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76,
	0x65, 0x64, 0x22, 0xaa, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x34, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52,
	0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x20, 0x0a, 0x07, 0x6c, 0x6f, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10,
	0x01, 0x52, 0x06, 0x6c, 0x6f, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x06, 0x61, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x6f, 0x6e, 0x65, 0x79, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x22,
	0x6f, 0x0a, 0x1b, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0b,
	0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64,
	0x22, 0x73, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x34, 0x0a, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x22, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x10, 0x01, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x64, 0x22, 0xca, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x62, 0x0a, 0x0e, 0x70, 0x61, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x70, 0x61,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e, 0x66,
	0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x5f, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x66, 0x61, 0x69, 0x6c, 0x75, 0x72, 0x65, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x2a, 0xa6, 0x01, 0x0a, 0x09, 0x4b, 0x59, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x1a, 0x0a, 0x16, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14,
	0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x49, 0x54, 0x49,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54,
	0x41, 0x54, 0x55, 0x53, 0x5f, 0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53,
	0x10, 0x02, 0x12, 0x18, 0x0a, 0x14, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53,
	0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11,
	0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45,
	0x44, 0x10, 0x04, 0x12, 0x16, 0x0a, 0x12, 0x4b, 0x59, 0x43, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55,
	0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x10, 0x05, 0x2a, 0xa4, 0x01, 0x0a, 0x0d,
	0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x1a, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1e, 0x0a,
	0x1a, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x49, 0x4e, 0x5f, 0x50, 0x52, 0x4f, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x1c, 0x0a,
	0x18, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x43, 0x4f, 0x4d, 0x50, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4d,
	0x41, 0x4e, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41,
	0x49, 0x4c, 0x45, 0x44, 0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x4d, 0x41, 0x4e, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x10, 0x04, 0x2a, 0xa9, 0x01, 0x0a, 0x1c, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x50, 0x75, 0x72, 0x70,
	0x6f, 0x73, 0x65, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x55,
	0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x2c, 0x0a, 0x28, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x55,
	0x52, 0x50, 0x4f, 0x53, 0x45, 0x5f, 0x50, 0x52, 0x49, 0x4e, 0x43, 0x49, 0x50, 0x41, 0x4c, 0x10,
	0x01, 0x12, 0x2b, 0x0a, 0x27, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x50, 0x55, 0x52, 0x50,
	0x4f, 0x53, 0x45, 0x5f, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x45, 0x53, 0x54, 0x10, 0x02, 0x2a, 0xaa,
	0x03, 0x0a, 0x1e, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x31, 0x0a, 0x2d, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x2e, 0x0a, 0x2a, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x50, 0x43, 0x4f, 0x4d, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x2a, 0x0a, 0x26, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54,
	0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x02,
	0x12, 0x35, 0x0a, 0x31, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x5f, 0x49, 0x4e, 0x5f, 0x41, 0x44,
	0x56, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x41, 0x4d, 0x4f, 0x52, 0x54,
	0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45,
	0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x44, 0x55, 0x45,
	0x10, 0x04, 0x12, 0x2d, 0x0a, 0x29, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4f, 0x56, 0x45, 0x52, 0x44, 0x55, 0x45, 0x10,
	0x05, 0x12, 0x32, 0x0a, 0x2e, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49, 0x54, 0x45, 0x4d, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x4c, 0x41, 0x54, 0x45, 0x5f, 0x50, 0x41, 0x59, 0x4d,
	0x45, 0x4e, 0x54, 0x10, 0x06, 0x12, 0x34, 0x0a, 0x30, 0x41, 0x4d, 0x4f, 0x52, 0x54, 0x49, 0x5a,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x5f, 0x49,
	0x54, 0x45, 0x4d, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x50, 0x41, 0x52, 0x54, 0x49,
	0x41, 0x4c, 0x4c, 0x59, 0x5f, 0x50, 0x41, 0x49, 0x44, 0x10, 0x07, 0x2a, 0xa0, 0x01, 0x0a, 0x0d,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1e, 0x0a,
	0x1a, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1a, 0x0a,
	0x16, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f,
	0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x50, 0x41, 0x59,
	0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x53, 0x55, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x03,
	0x12, 0x1c, 0x0a, 0x18, 0x50, 0x41, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x55, 0x53, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x04, 0x32, 0x93,
	0x18, 0x0a, 0x06, 0x4c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x12, 0x8f, 0x01, 0x0a, 0x0a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x12, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x95, 0x01, 0x0a, 0x0c,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x12, 0x41, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x42, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41,
	0x70, 0x70, 0x6c, 0x79, 0x46, 0x6f, 0x72, 0x4c, 0x6f, 0x61, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x14, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x61, 0x72,
	0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x49, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x48, 0x61, 0x72, 0x64, 0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x48, 0x61, 0x72, 0x64,
	0x45, 0x6c, 0x69, 0x67, 0x69, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x92, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66,
	0x66, 0x65, 0x72, 0x12, 0x40, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4f, 0x66, 0x66, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xad, 0x01, 0x0a, 0x14, 0x4d, 0x6f, 0x64,
	0x69, 0x66, 0x79, 0x52, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73,
	0x74, 0x12, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x2e, 0x4d, 0x6f, 0x64, 0x69, 0x66, 0x79, 0x52, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74,
	0x65, 0x72, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x4d, 0x6f, 0x64, 0x69,
	0x66, 0x79, 0x52, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x65, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x86, 0x01, 0x0a, 0x07, 0x4b, 0x79, 0x63,
	0x49, 0x6e, 0x69, 0x74, 0x12, 0x3c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x3d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x4b, 0x79, 0x63, 0x49, 0x6e, 0x69, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x9b, 0x01, 0x0a, 0x0e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x79, 0x63, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65,
	0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x79, 0x63, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4b, 0x79,
	0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x9b, 0x01, 0x0a, 0x0e, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72,
	0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x41, 0x64, 0x64, 0x42, 0x61, 0x6e, 0x6b, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x92, 0x01,
	0x0a, 0x0b, 0x49, 0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x12, 0x40, 0x2e,
	0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65,
	0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49, 0x6e, 0x69,
	0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x41, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x49,
	0x6e, 0x69, 0x74, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0xa7, 0x01, 0x0a, 0x12, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x47, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61,
	0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x48, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4d, 0x61, 0x6e, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x98, 0x01, 0x0a,
	0x0d, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x12, 0x42,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65,
	0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x8c, 0x01, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e,
	0x4b, 0x66, 0x73, 0x4c, 0x61, 0x12, 0x3e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x4b, 0x66, 0x73, 0x4c, 0x61, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x9b, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x4c, 0x6f,
	0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x43, 0x2e, 0x76, 0x65, 0x6e, 0x64,
	0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61,
	0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x6f, 0x61, 0x6e,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x44,
	0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76,
	0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65,
	0x74, 0x4c, 0x6f, 0x61, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0xbc, 0x01, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x44,
	0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x4e, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x4f, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70,
	0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65,
	0x6e, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x65, 0x44, 0x69, 0x73, 0x62, 0x75, 0x72, 0x73, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xa1, 0x01, 0x0a, 0x10, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x45, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f,
	0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e,
	0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x46, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e,
	0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f,
	0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x50,
	0x6f, 0x73, 0x74, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x44, 0x61, 0x74, 0x61, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0xb6, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x41,
	0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x4c, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61,
	0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x4d, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x2e, 0x47, 0x65, 0x74, 0x41, 0x6d, 0x6f, 0x72, 0x74, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xb0, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f, 0x73,
	0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x4a, 0x2e, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72,
	0x65, 0x63, 0x6c, 0x6f, 0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4b, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c,
	0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x6f, 0x72, 0x65, 0x63, 0x6c, 0x6f,
	0x73, 0x75, 0x72, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0xaa, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x12, 0x48, 0x2e, 0x76, 0x65,
	0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c,
	0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72,
	0x61, 0x74, 0x65, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x49, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x50, 0x61, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0xa1, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x45, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72,
	0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65,
	0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x46, 0x2e, 0x76,
	0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x6c, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64,
	0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x42, 0x92, 0x01, 0x0a, 0x47, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x76, 0x65, 0x6e, 0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70,
	0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f, 0x61, 0x6e, 0x2e, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e,
	0x5a, 0x47, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69,
	0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x65, 0x6e,
	0x64, 0x6f, 0x72, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2f, 0x70, 0x72, 0x65, 0x61, 0x70, 0x70, 0x72, 0x6f, 0x76, 0x65, 0x64, 0x6c, 0x6f,
	0x61, 0x6e, 0x2f, 0x6c, 0x65, 0x6e, 0x64, 0x65, 0x6e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescOnce sync.Once
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescData = file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDesc
)

func file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescGZIP() []byte {
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescOnce.Do(func() {
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescData)
	})
	return file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDescData
}

var file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes = make([]protoimpl.EnumInfo, 11)
var file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes = make([]protoimpl.MessageInfo, 47)
var file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_goTypes = []interface{}{
	(KYCStatus)(0),                                // 0: vendorgateway.lending.preapprovedloan.lenden.KYCStatus
	(MandateStatus)(0),                            // 1: vendorgateway.lending.preapprovedloan.lenden.MandateStatus
	(AmortizationComponentPurpose)(0),             // 2: vendorgateway.lending.preapprovedloan.lenden.AmortizationComponentPurpose
	(AmortizationScheduleItemStatus)(0),           // 3: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItemStatus
	(PaymentStatus)(0),                            // 4: vendorgateway.lending.preapprovedloan.lenden.PaymentStatus
	(ApplyForLoanResponse_Status)(0),              // 5: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanResponse.Status
	(AddBankDetailsResponse_Status)(0),            // 6: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsResponse.Status
	(InitMandateResponse_Status)(0),               // 7: vendorgateway.lending.preapprovedloan.lenden.InitMandateResponse.Status
	(GetPreDisbursementDetailsResponse_Status)(0), // 8: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsResponse.Status
	(GetForeclosureDetailsRequest_Purpose)(0),     // 9: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest.Purpose
	(GetForeclosureDetailsResponse_Status)(0),     // 10: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsResponse.Status
	(*CreateUserRequest)(nil),                     // 11: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest
	(*CreateUserResponse)(nil),                    // 12: vendorgateway.lending.preapprovedloan.lenden.CreateUserResponse
	(*ApplyForLoanRequest)(nil),                   // 13: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest
	(*ApplyForLoanResponse)(nil),                  // 14: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanResponse
	(*CheckHardEligibilityRequest)(nil),           // 15: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityRequest
	(*CheckHardEligibilityResponse)(nil),          // 16: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityResponse
	(*OfferData)(nil),                             // 17: vendorgateway.lending.preapprovedloan.lenden.OfferData
	(*P2POfferDetails)(nil),                       // 18: vendorgateway.lending.preapprovedloan.lenden.P2pOfferDetails
	(*SelectOfferRequest)(nil),                    // 19: vendorgateway.lending.preapprovedloan.lenden.SelectOfferRequest
	(*SelectOfferResponse)(nil),                   // 20: vendorgateway.lending.preapprovedloan.lenden.SelectOfferResponse
	(*ModifyRateOfInterestRequest)(nil),           // 21: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestRequest
	(*ModifyRateOfInterestResponse)(nil),          // 22: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestResponse
	(*KycInitRequest)(nil),                        // 23: vendorgateway.lending.preapprovedloan.lenden.KycInitRequest
	(*KycInitResponse)(nil),                       // 24: vendorgateway.lending.preapprovedloan.lenden.KycInitResponse
	(*CheckKycStatusRequest)(nil),                 // 25: vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusRequest
	(*CheckKycStatusResponse)(nil),                // 26: vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusResponse
	(*AddBankDetailsRequest)(nil),                 // 27: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsRequest
	(*AddBankDetailsResponse)(nil),                // 28: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsResponse
	(*InitMandateRequest)(nil),                    // 29: vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest
	(*InitMandateResponse)(nil),                   // 30: vendorgateway.lending.preapprovedloan.lenden.InitMandateResponse
	(*CheckMandateStatusRequest)(nil),             // 31: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusRequest
	(*CheckMandateStatusResponse)(nil),            // 32: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusResponse
	(*GenerateKfsLaRequest)(nil),                  // 33: vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaRequest
	(*GenerateKfsLaResponse)(nil),                 // 34: vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaResponse
	(*SignKfsLaRequest)(nil),                      // 35: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaRequest
	(*SignKfsLaResponse)(nil),                     // 36: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaResponse
	(*GetLoanDetailsRequest)(nil),                 // 37: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsRequest
	(*GetLoanDetailsResponse)(nil),                // 38: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsResponse
	(*GetPreDisbursementDetailsRequest)(nil),      // 39: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsRequest
	(*GetPreDisbursementDetailsResponse)(nil),     // 40: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsResponse
	(*PreDisbursementDetails)(nil),                // 41: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails
	(*PostExternalDataRequest)(nil),               // 42: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataRequest
	(*BankDetails)(nil),                           // 43: vendorgateway.lending.preapprovedloan.lenden.BankDetails
	(*PostExternalDataResponse)(nil),              // 44: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataResponse
	(*GetAmortizationScheduleRequest)(nil),        // 45: vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleRequest
	(*AmortizationAmountBreakupComponent)(nil),    // 46: vendorgateway.lending.preapprovedloan.lenden.AmortizationAmountBreakupComponent
	(*AmortizationScheduleItem)(nil),              // 47: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem
	(*GetAmortizationScheduleResponse)(nil),       // 48: vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleResponse
	(*GetForeclosureDetailsRequest)(nil),          // 49: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest
	(*GetForeclosureDetailsResponse)(nil),         // 50: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsResponse
	(*ForeclosureDetails)(nil),                    // 51: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails
	(*GeneratePaymentLinkRequest)(nil),            // 52: vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkRequest
	(*GeneratePaymentLinkResponse)(nil),           // 53: vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkResponse
	(*GetPaymentStatusRequest)(nil),               // 54: vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusRequest
	(*GetPaymentStatusResponse)(nil),              // 55: vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusResponse
	(*ApplyForLoanRequest_Interest)(nil),          // 56: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.Interest
	(*OfferData_ApplicableAmountToTenure)(nil),    // 57: vendorgateway.lending.preapprovedloan.lenden.OfferData.ApplicableAmountToTenure
	(*vendorgateway.RequestHeader)(nil),           // 58: vendorgateway.RequestHeader
	(*common.Name)(nil),                           // 59: api.typesv2.common.Name
	(*common.PhoneNumber)(nil),                    // 60: api.typesv2.common.PhoneNumber
	(*date.Date)(nil),                             // 61: google.type.Date
	(LendenEmploymentType)(0),                     // 62: vendorgateway.lending.preapprovedloan.lenden.LendenEmploymentType
	(ConsentType)(0),                              // 63: vendorgateway.lending.preapprovedloan.lenden.ConsentType
	(*timestamppb.Timestamp)(nil),                 // 64: google.protobuf.Timestamp
	(*rpc.Status)(nil),                            // 65: rpc.Status
	(*money.Money)(nil),                           // 66: google.type.Money
	(AddressType)(0),                              // 67: vendorgateway.lending.preapprovedloan.lenden.AddressType
	(*common.PostalAddress)(nil),                  // 68: api.typesv2.common.PostalAddress
	(EligibilityStatus)(0),                        // 69: vendorgateway.lending.preapprovedloan.lenden.EligibilityStatus
	(*common.BankAccountDetails)(nil),             // 70: api.typesv2.common.BankAccountDetails
	(MandateType)(0),                              // 71: vendorgateway.lending.preapprovedloan.lenden.MandateType
	(LoanStatus)(0),                               // 72: vendorgateway.lending.preapprovedloan.lenden.LoanStatus
	(*lenden.LoanDetails)(nil),                    // 73: vendors.lenden.LoanDetails
	(*decimal.Decimal)(nil),                       // 74: google.type.Decimal
	(EligibilityDataType)(0),                      // 75: vendorgateway.lending.preapprovedloan.lenden.EligibilityDataType
	(AccountType)(0),                              // 76: vendorgateway.lending.preapprovedloan.lenden.AccountType
	(InterestType)(0),                             // 77: vendorgateway.lending.preapprovedloan.lenden.InterestType
	(InterestFrequency)(0),                        // 78: vendorgateway.lending.preapprovedloan.lenden.InterestFrequency
}
var file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_depIdxs = []int32{
	58,  // 0: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.header:type_name -> vendorgateway.RequestHeader
	59,  // 1: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.name:type_name -> api.typesv2.common.Name
	60,  // 2: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.phone_number:type_name -> api.typesv2.common.PhoneNumber
	61,  // 3: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.dob:type_name -> google.type.Date
	62,  // 4: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.employment_type:type_name -> vendorgateway.lending.preapprovedloan.lenden.LendenEmploymentType
	63,  // 5: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.consent_type_list:type_name -> vendorgateway.lending.preapprovedloan.lenden.ConsentType
	64,  // 6: vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest.consent_time:type_name -> google.protobuf.Timestamp
	65,  // 7: vendorgateway.lending.preapprovedloan.lenden.CreateUserResponse.status:type_name -> rpc.Status
	58,  // 8: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.header:type_name -> vendorgateway.RequestHeader
	66,  // 9: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.requested_amount:type_name -> google.type.Money
	56,  // 10: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.interest:type_name -> vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.Interest
	67,  // 11: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.address_type:type_name -> vendorgateway.lending.preapprovedloan.lenden.AddressType
	68,  // 12: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.address:type_name -> api.typesv2.common.PostalAddress
	65,  // 13: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanResponse.status:type_name -> rpc.Status
	58,  // 14: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityRequest.header:type_name -> vendorgateway.RequestHeader
	65,  // 15: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityResponse.status:type_name -> rpc.Status
	69,  // 16: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityResponse.eligibility_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.EligibilityStatus
	17,  // 17: vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityResponse.offer_data:type_name -> vendorgateway.lending.preapprovedloan.lenden.OfferData
	18,  // 18: vendorgateway.lending.preapprovedloan.lenden.OfferData.offers:type_name -> vendorgateway.lending.preapprovedloan.lenden.P2pOfferDetails
	66,  // 19: vendorgateway.lending.preapprovedloan.lenden.OfferData.min_amount:type_name -> google.type.Money
	66,  // 20: vendorgateway.lending.preapprovedloan.lenden.OfferData.max_amount:type_name -> google.type.Money
	57,  // 21: vendorgateway.lending.preapprovedloan.lenden.OfferData.applicable_tenures:type_name -> vendorgateway.lending.preapprovedloan.lenden.OfferData.ApplicableAmountToTenure
	58,  // 22: vendorgateway.lending.preapprovedloan.lenden.SelectOfferRequest.header:type_name -> vendorgateway.RequestHeader
	66,  // 23: vendorgateway.lending.preapprovedloan.lenden.SelectOfferRequest.selected_amount:type_name -> google.type.Money
	65,  // 24: vendorgateway.lending.preapprovedloan.lenden.SelectOfferResponse.status:type_name -> rpc.Status
	58,  // 25: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestRequest.header:type_name -> vendorgateway.RequestHeader
	63,  // 26: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestRequest.consent_code_list:type_name -> vendorgateway.lending.preapprovedloan.lenden.ConsentType
	64,  // 27: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestRequest.consented_at:type_name -> google.protobuf.Timestamp
	65,  // 28: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestResponse.status:type_name -> rpc.Status
	66,  // 29: vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestResponse.installment_amount:type_name -> google.type.Money
	58,  // 30: vendorgateway.lending.preapprovedloan.lenden.KycInitRequest.header:type_name -> vendorgateway.RequestHeader
	63,  // 31: vendorgateway.lending.preapprovedloan.lenden.KycInitRequest.consent_code_list:type_name -> vendorgateway.lending.preapprovedloan.lenden.ConsentType
	64,  // 32: vendorgateway.lending.preapprovedloan.lenden.KycInitRequest.consent_time:type_name -> google.protobuf.Timestamp
	65,  // 33: vendorgateway.lending.preapprovedloan.lenden.KycInitResponse.status:type_name -> rpc.Status
	0,   // 34: vendorgateway.lending.preapprovedloan.lenden.KycInitResponse.kyc_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.KYCStatus
	58,  // 35: vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusRequest.header:type_name -> vendorgateway.RequestHeader
	65,  // 36: vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusResponse.status:type_name -> rpc.Status
	0,   // 37: vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusResponse.kyc_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.KYCStatus
	58,  // 38: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	70,  // 39: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsRequest.bank_account_details:type_name -> api.typesv2.common.BankAccountDetails
	65,  // 40: vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsResponse.status:type_name -> rpc.Status
	58,  // 41: vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest.header:type_name -> vendorgateway.RequestHeader
	71,  // 42: vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest.mandate_type:type_name -> vendorgateway.lending.preapprovedloan.lenden.MandateType
	63,  // 43: vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest.consent_code_list:type_name -> vendorgateway.lending.preapprovedloan.lenden.ConsentType
	64,  // 44: vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest.consent_time:type_name -> google.protobuf.Timestamp
	65,  // 45: vendorgateway.lending.preapprovedloan.lenden.InitMandateResponse.status:type_name -> rpc.Status
	64,  // 46: vendorgateway.lending.preapprovedloan.lenden.InitMandateResponse.mandate_url_validity:type_name -> google.protobuf.Timestamp
	58,  // 47: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusRequest.header:type_name -> vendorgateway.RequestHeader
	71,  // 48: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusRequest.mandate_type:type_name -> vendorgateway.lending.preapprovedloan.lenden.MandateType
	65,  // 49: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusResponse.status:type_name -> rpc.Status
	1,   // 50: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusResponse.mandate_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.MandateStatus
	64,  // 51: vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusResponse.completed_at:type_name -> google.protobuf.Timestamp
	58,  // 52: vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaRequest.header:type_name -> vendorgateway.RequestHeader
	65,  // 53: vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaResponse.status:type_name -> rpc.Status
	58,  // 54: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaRequest.header:type_name -> vendorgateway.RequestHeader
	63,  // 55: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaRequest.consent_code_list:type_name -> vendorgateway.lending.preapprovedloan.lenden.ConsentType
	64,  // 56: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaRequest.consent_time:type_name -> google.protobuf.Timestamp
	65,  // 57: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaResponse.status:type_name -> rpc.Status
	64,  // 58: vendorgateway.lending.preapprovedloan.lenden.SignKfsLaResponse.modify_roi_expiration_time:type_name -> google.protobuf.Timestamp
	58,  // 59: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	65,  // 60: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsResponse.status:type_name -> rpc.Status
	72,  // 61: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsResponse.loan_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.LoanStatus
	73,  // 62: vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsResponse.loan_details:type_name -> vendors.lenden.LoanDetails
	58,  // 63: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	66,  // 64: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsRequest.amount:type_name -> google.type.Money
	74,  // 65: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsRequest.rate_of_interest:type_name -> google.type.Decimal
	65,  // 66: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsResponse.status:type_name -> rpc.Status
	41,  // 67: vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsResponse.pre_disbursement_details:type_name -> vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails
	66,  // 68: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.processing_fee:type_name -> google.type.Money
	66,  // 69: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.disbursal_amount:type_name -> google.type.Money
	66,  // 70: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.total_interest:type_name -> google.type.Money
	66,  // 71: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.total_repayment_amount:type_name -> google.type.Money
	66,  // 72: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.installment_amount:type_name -> google.type.Money
	61,  // 73: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.first_installment_date:type_name -> google.type.Date
	66,  // 74: vendorgateway.lending.preapprovedloan.lenden.PreDisbursementDetails.gap_interest:type_name -> google.type.Money
	58,  // 75: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataRequest.header:type_name -> vendorgateway.RequestHeader
	43,  // 76: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataRequest.bank_details:type_name -> vendorgateway.lending.preapprovedloan.lenden.BankDetails
	75,  // 77: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataRequest.type:type_name -> vendorgateway.lending.preapprovedloan.lenden.EligibilityDataType
	76,  // 78: vendorgateway.lending.preapprovedloan.lenden.BankDetails.type:type_name -> vendorgateway.lending.preapprovedloan.lenden.AccountType
	65,  // 79: vendorgateway.lending.preapprovedloan.lenden.PostExternalDataResponse.status:type_name -> rpc.Status
	58,  // 80: vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleRequest.header:type_name -> vendorgateway.RequestHeader
	2,   // 81: vendorgateway.lending.preapprovedloan.lenden.AmortizationAmountBreakupComponent.purpose:type_name -> vendorgateway.lending.preapprovedloan.lenden.AmortizationComponentPurpose
	66,  // 82: vendorgateway.lending.preapprovedloan.lenden.AmortizationAmountBreakupComponent.amount:type_name -> google.type.Money
	61,  // 83: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem.due_date:type_name -> google.type.Date
	66,  // 84: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem.due_amount:type_name -> google.type.Money
	46,  // 85: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem.breakup:type_name -> vendorgateway.lending.preapprovedloan.lenden.AmortizationAmountBreakupComponent
	3,   // 86: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem.status:type_name -> vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItemStatus
	66,  // 87: vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem.outstanding_amount:type_name -> google.type.Money
	65,  // 88: vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleResponse.status:type_name -> rpc.Status
	47,  // 89: vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleResponse.amortization_schedule:type_name -> vendorgateway.lending.preapprovedloan.lenden.AmortizationScheduleItem
	58,  // 90: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest.header:type_name -> vendorgateway.RequestHeader
	9,   // 91: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest.purpose:type_name -> vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest.Purpose
	65,  // 92: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsResponse.status:type_name -> rpc.Status
	51,  // 93: vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsResponse.foreclosure_details:type_name -> vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails
	66,  // 94: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.principal_outstanding:type_name -> google.type.Money
	66,  // 95: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.interest_outstanding:type_name -> google.type.Money
	66,  // 96: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.delay_charges_outstanding:type_name -> google.type.Money
	66,  // 97: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.late_fee_outstanding:type_name -> google.type.Money
	66,  // 98: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.foreclosure_amount:type_name -> google.type.Money
	66,  // 99: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.foreclosure_charges:type_name -> google.type.Money
	66,  // 100: vendorgateway.lending.preapprovedloan.lenden.ForeclosureDetails.repayment_received:type_name -> google.type.Money
	58,  // 101: vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkRequest.header:type_name -> vendorgateway.RequestHeader
	66,  // 102: vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkRequest.amount:type_name -> google.type.Money
	65,  // 103: vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkResponse.status:type_name -> rpc.Status
	58,  // 104: vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusRequest.header:type_name -> vendorgateway.RequestHeader
	65,  // 105: vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusResponse.status:type_name -> rpc.Status
	4,   // 106: vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusResponse.payment_status:type_name -> vendorgateway.lending.preapprovedloan.lenden.PaymentStatus
	77,  // 107: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.Interest.type:type_name -> vendorgateway.lending.preapprovedloan.lenden.InterestType
	78,  // 108: vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest.Interest.frequency:type_name -> vendorgateway.lending.preapprovedloan.lenden.InterestFrequency
	66,  // 109: vendorgateway.lending.preapprovedloan.lenden.OfferData.ApplicableAmountToTenure.min_amount:type_name -> google.type.Money
	66,  // 110: vendorgateway.lending.preapprovedloan.lenden.OfferData.ApplicableAmountToTenure.max_amount:type_name -> google.type.Money
	11,  // 111: vendorgateway.lending.preapprovedloan.lenden.Lenden.CreateUser:input_type -> vendorgateway.lending.preapprovedloan.lenden.CreateUserRequest
	13,  // 112: vendorgateway.lending.preapprovedloan.lenden.Lenden.ApplyForLoan:input_type -> vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanRequest
	15,  // 113: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckHardEligibility:input_type -> vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityRequest
	19,  // 114: vendorgateway.lending.preapprovedloan.lenden.Lenden.SelectOffer:input_type -> vendorgateway.lending.preapprovedloan.lenden.SelectOfferRequest
	21,  // 115: vendorgateway.lending.preapprovedloan.lenden.Lenden.ModifyRateOfInterest:input_type -> vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestRequest
	23,  // 116: vendorgateway.lending.preapprovedloan.lenden.Lenden.KycInit:input_type -> vendorgateway.lending.preapprovedloan.lenden.KycInitRequest
	25,  // 117: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckKycStatus:input_type -> vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusRequest
	27,  // 118: vendorgateway.lending.preapprovedloan.lenden.Lenden.AddBankDetails:input_type -> vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsRequest
	29,  // 119: vendorgateway.lending.preapprovedloan.lenden.Lenden.InitMandate:input_type -> vendorgateway.lending.preapprovedloan.lenden.InitMandateRequest
	31,  // 120: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckMandateStatus:input_type -> vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusRequest
	33,  // 121: vendorgateway.lending.preapprovedloan.lenden.Lenden.GenerateKfsLa:input_type -> vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaRequest
	35,  // 122: vendorgateway.lending.preapprovedloan.lenden.Lenden.SignKfsLa:input_type -> vendorgateway.lending.preapprovedloan.lenden.SignKfsLaRequest
	37,  // 123: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetLoanDetails:input_type -> vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsRequest
	39,  // 124: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetPreDisbursementDetails:input_type -> vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsRequest
	42,  // 125: vendorgateway.lending.preapprovedloan.lenden.Lenden.PostExternalData:input_type -> vendorgateway.lending.preapprovedloan.lenden.PostExternalDataRequest
	45,  // 126: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetAmortizationSchedule:input_type -> vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleRequest
	49,  // 127: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetForeclosureDetails:input_type -> vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsRequest
	52,  // 128: vendorgateway.lending.preapprovedloan.lenden.Lenden.GeneratePaymentLink:input_type -> vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkRequest
	54,  // 129: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetPaymentStatus:input_type -> vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusRequest
	12,  // 130: vendorgateway.lending.preapprovedloan.lenden.Lenden.CreateUser:output_type -> vendorgateway.lending.preapprovedloan.lenden.CreateUserResponse
	14,  // 131: vendorgateway.lending.preapprovedloan.lenden.Lenden.ApplyForLoan:output_type -> vendorgateway.lending.preapprovedloan.lenden.ApplyForLoanResponse
	16,  // 132: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckHardEligibility:output_type -> vendorgateway.lending.preapprovedloan.lenden.CheckHardEligibilityResponse
	20,  // 133: vendorgateway.lending.preapprovedloan.lenden.Lenden.SelectOffer:output_type -> vendorgateway.lending.preapprovedloan.lenden.SelectOfferResponse
	22,  // 134: vendorgateway.lending.preapprovedloan.lenden.Lenden.ModifyRateOfInterest:output_type -> vendorgateway.lending.preapprovedloan.lenden.ModifyRateOfInterestResponse
	24,  // 135: vendorgateway.lending.preapprovedloan.lenden.Lenden.KycInit:output_type -> vendorgateway.lending.preapprovedloan.lenden.KycInitResponse
	26,  // 136: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckKycStatus:output_type -> vendorgateway.lending.preapprovedloan.lenden.CheckKycStatusResponse
	28,  // 137: vendorgateway.lending.preapprovedloan.lenden.Lenden.AddBankDetails:output_type -> vendorgateway.lending.preapprovedloan.lenden.AddBankDetailsResponse
	30,  // 138: vendorgateway.lending.preapprovedloan.lenden.Lenden.InitMandate:output_type -> vendorgateway.lending.preapprovedloan.lenden.InitMandateResponse
	32,  // 139: vendorgateway.lending.preapprovedloan.lenden.Lenden.CheckMandateStatus:output_type -> vendorgateway.lending.preapprovedloan.lenden.CheckMandateStatusResponse
	34,  // 140: vendorgateway.lending.preapprovedloan.lenden.Lenden.GenerateKfsLa:output_type -> vendorgateway.lending.preapprovedloan.lenden.GenerateKfsLaResponse
	36,  // 141: vendorgateway.lending.preapprovedloan.lenden.Lenden.SignKfsLa:output_type -> vendorgateway.lending.preapprovedloan.lenden.SignKfsLaResponse
	38,  // 142: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetLoanDetails:output_type -> vendorgateway.lending.preapprovedloan.lenden.GetLoanDetailsResponse
	40,  // 143: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetPreDisbursementDetails:output_type -> vendorgateway.lending.preapprovedloan.lenden.GetPreDisbursementDetailsResponse
	44,  // 144: vendorgateway.lending.preapprovedloan.lenden.Lenden.PostExternalData:output_type -> vendorgateway.lending.preapprovedloan.lenden.PostExternalDataResponse
	48,  // 145: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetAmortizationSchedule:output_type -> vendorgateway.lending.preapprovedloan.lenden.GetAmortizationScheduleResponse
	50,  // 146: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetForeclosureDetails:output_type -> vendorgateway.lending.preapprovedloan.lenden.GetForeclosureDetailsResponse
	53,  // 147: vendorgateway.lending.preapprovedloan.lenden.Lenden.GeneratePaymentLink:output_type -> vendorgateway.lending.preapprovedloan.lenden.GeneratePaymentLinkResponse
	55,  // 148: vendorgateway.lending.preapprovedloan.lenden.Lenden.GetPaymentStatus:output_type -> vendorgateway.lending.preapprovedloan.lenden.GetPaymentStatusResponse
	130, // [130:149] is the sub-list for method output_type
	111, // [111:130] is the sub-list for method input_type
	111, // [111:111] is the sub-list for extension type_name
	111, // [111:111] is the sub-list for extension extendee
	0,   // [0:111] is the sub-list for field type_name
}

func init() { file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_init() }
func file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_init() {
	if File_api_vendorgateway_lending_preapprovedloan_lenden_service_proto != nil {
		return
	}
	file_api_vendorgateway_lending_preapprovedloan_lenden_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateUserResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyForLoanRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyForLoanResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckHardEligibilityRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckHardEligibilityResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*P2POfferDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SelectOfferResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyRateOfInterestRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ModifyRateOfInterestResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycInitRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*KycInitResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckKycStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckKycStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBankDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AddBankDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*InitMandateResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckMandateStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateKfsLaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GenerateKfsLaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignKfsLaRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignKfsLaResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetLoanDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreDisbursementDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPreDisbursementDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PreDisbursementDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostExternalDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BankDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PostExternalDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmortizationScheduleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmortizationAmountBreakupComponent); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AmortizationScheduleItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAmortizationScheduleResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetForeclosureDetailsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetForeclosureDetailsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForeclosureDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePaymentLinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GeneratePaymentLinkResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetPaymentStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ApplyForLoanRequest_Interest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OfferData_ApplicableAmountToTenure); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDesc,
			NumEnums:      11,
			NumMessages:   47,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_goTypes,
		DependencyIndexes: file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_depIdxs,
		EnumInfos:         file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_enumTypes,
		MessageInfos:      file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_msgTypes,
	}.Build()
	File_api_vendorgateway_lending_preapprovedloan_lenden_service_proto = out.File
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_rawDesc = nil
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_goTypes = nil
	file_api_vendorgateway_lending_preapprovedloan_lenden_service_proto_depIdxs = nil
}
