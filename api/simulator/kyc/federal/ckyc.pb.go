// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v4.23.4
// source: api/simulator/kyc/federal/ckyc.proto

package federal

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SearchRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode        string `protobuf:"bytes,1,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	ServiceAccessId   string `protobuf:"bytes,2,opt,name=service_access_id,json=ServiceAccessId,proto3" json:"service_access_id,omitempty"`
	ServiceAccessCode string `protobuf:"bytes,3,opt,name=service_access_code,json=ServiceAccessCode,proto3" json:"service_access_code,omitempty"`
	RequestId         string `protobuf:"bytes,4,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	IdType            string `protobuf:"bytes,8,opt,name=id_type,json=IdType,proto3" json:"id_type,omitempty"`
	IdValue           string `protobuf:"bytes,9,opt,name=id_value,json=IdNumber,proto3" json:"id_value,omitempty"`
	// Optional. Federal uses this field for tracing the request & debugging
	MobileNumber string `protobuf:"bytes,10,opt,name=mobile_number,json=MobileNumber,proto3" json:"mobile_number,omitempty"`
}

func (x *SearchRequest) Reset() {
	*x = SearchRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchRequest) ProtoMessage() {}

func (x *SearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchRequest.ProtoReflect.Descriptor instead.
func (*SearchRequest) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{0}
}

func (x *SearchRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *SearchRequest) GetServiceAccessId() string {
	if x != nil {
		return x.ServiceAccessId
	}
	return ""
}

func (x *SearchRequest) GetServiceAccessCode() string {
	if x != nil {
		return x.ServiceAccessCode
	}
	return ""
}

func (x *SearchRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *SearchRequest) GetIdType() string {
	if x != nil {
		return x.IdType
	}
	return ""
}

func (x *SearchRequest) GetIdValue() string {
	if x != nil {
		return x.IdValue
	}
	return ""
}

func (x *SearchRequest) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

type SearchResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode   string                 `protobuf:"bytes,1,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	RequestId    string                 `protobuf:"bytes,2,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	DeviceToken  string                 `protobuf:"bytes,3,opt,name=device_token,json=DeviceToken,proto3" json:"device_token,omitempty"`
	SearchStatus string                 `protobuf:"bytes,4,opt,name=search_status,json=SearchStatus,proto3" json:"search_status,omitempty"`
	Pid          *SearchResponsePidData `protobuf:"bytes,5,opt,name=pid,json=Pid,proto3" json:"pid,omitempty"`
	// response code for request. refer to the
	// [doc](https://docs.google.com/document/d/1gnHFtQMd_6Rq0P_Z7HxXVBFhGtqS3dEYbP8ytM-fNv8/)
	// for code mapping
	Response string `protobuf:"bytes,6,opt,name=response,json=Response,proto3" json:"response,omitempty"`
	// description of response code
	Reason string `protobuf:"bytes,7,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
}

func (x *SearchResponse) Reset() {
	*x = SearchResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponse) ProtoMessage() {}

func (x *SearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponse.ProtoReflect.Descriptor instead.
func (*SearchResponse) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{1}
}

func (x *SearchResponse) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *SearchResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *SearchResponse) GetDeviceToken() string {
	if x != nil {
		return x.DeviceToken
	}
	return ""
}

func (x *SearchResponse) GetSearchStatus() string {
	if x != nil {
		return x.SearchStatus
	}
	return ""
}

func (x *SearchResponse) GetPid() *SearchResponsePidData {
	if x != nil {
		return x.Pid
	}
	return nil
}

func (x *SearchResponse) GetResponse() string {
	if x != nil {
		return x.Response
	}
	return ""
}

func (x *SearchResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type SearchResponsePidData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Age         string `protobuf:"bytes,1,opt,name=age,json=Age,proto3" json:"age,omitempty"`
	CkycNo      string `protobuf:"bytes,2,opt,name=ckyc_no,json=CkycNo,proto3" json:"ckyc_no,omitempty"`
	FathersName string `protobuf:"bytes,3,opt,name=fathers_name,json=FathersName,proto3" json:"fathers_name,omitempty"`
	ImageType   string `protobuf:"bytes,4,opt,name=image_type,json=ImageType,proto3" json:"image_type,omitempty"`
	PhotoBase64 string `protobuf:"bytes,5,opt,name=photo_base64,json=Photo,proto3" json:"photo_base64,omitempty"`
	// date when kyc was first recorded
	KycDate         string `protobuf:"bytes,6,opt,name=kyc_date,json=KycDate,proto3" json:"kyc_date,omitempty"`
	Name            string `protobuf:"bytes,7,opt,name=name,json=Name,proto3" json:"name,omitempty"`
	UpdatedDate     string `protobuf:"bytes,8,opt,name=updated_date,json=UpdatedDate,proto3" json:"updated_date,omitempty"`
	CkycReferenceId string `protobuf:"bytes,9,opt,name=ckyc_reference_id,json=CkycReferenceId,proto3" json:"ckyc_reference_id,omitempty"`
}

func (x *SearchResponsePidData) Reset() {
	*x = SearchResponsePidData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SearchResponsePidData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchResponsePidData) ProtoMessage() {}

func (x *SearchResponsePidData) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchResponsePidData.ProtoReflect.Descriptor instead.
func (*SearchResponsePidData) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{2}
}

func (x *SearchResponsePidData) GetAge() string {
	if x != nil {
		return x.Age
	}
	return ""
}

func (x *SearchResponsePidData) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *SearchResponsePidData) GetFathersName() string {
	if x != nil {
		return x.FathersName
	}
	return ""
}

func (x *SearchResponsePidData) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *SearchResponsePidData) GetPhotoBase64() string {
	if x != nil {
		return x.PhotoBase64
	}
	return ""
}

func (x *SearchResponsePidData) GetKycDate() string {
	if x != nil {
		return x.KycDate
	}
	return ""
}

func (x *SearchResponsePidData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchResponsePidData) GetUpdatedDate() string {
	if x != nil {
		return x.UpdatedDate
	}
	return ""
}

func (x *SearchResponsePidData) GetCkycReferenceId() string {
	if x != nil {
		return x.CkycReferenceId
	}
	return ""
}

type GetDataRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode        string                  `protobuf:"bytes,1,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	ServiceAccessId   string                  `protobuf:"bytes,2,opt,name=service_access_id,json=ServiceAccessId,proto3" json:"service_access_id,omitempty"`
	ServiceAccessCode string                  `protobuf:"bytes,3,opt,name=service_access_code,json=ServiceAccessCode,proto3" json:"service_access_code,omitempty"`
	RequestId         string                  `protobuf:"bytes,4,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	PidData           *DownloadRequestPidData `protobuf:"bytes,8,opt,name=pid_data,json=PidData,proto3" json:"pid_data,omitempty"`
	// Optional. Federal uses this field for tracing the request & debugging
	MobileNumber string `protobuf:"bytes,9,opt,name=mobile_number,json=MobileNumber,proto3" json:"mobile_number,omitempty"`
}

func (x *GetDataRequest) Reset() {
	*x = GetDataRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataRequest) ProtoMessage() {}

func (x *GetDataRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataRequest.ProtoReflect.Descriptor instead.
func (*GetDataRequest) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{3}
}

func (x *GetDataRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *GetDataRequest) GetServiceAccessId() string {
	if x != nil {
		return x.ServiceAccessId
	}
	return ""
}

func (x *GetDataRequest) GetServiceAccessCode() string {
	if x != nil {
		return x.ServiceAccessCode
	}
	return ""
}

func (x *GetDataRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetDataRequest) GetPidData() *DownloadRequestPidData {
	if x != nil {
		return x.PidData
	}
	return nil
}

func (x *GetDataRequest) GetMobileNumber() string {
	if x != nil {
		return x.MobileNumber
	}
	return ""
}

type VerifyOTPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SenderCode        string                   `protobuf:"bytes,1,opt,name=sender_code,json=SenderCode,proto3" json:"sender_code,omitempty"`
	ServiceAccessId   string                   `protobuf:"bytes,2,opt,name=service_access_id,json=ServiceAccessId,proto3" json:"service_access_id,omitempty"`
	ServiceAccessCode string                   `protobuf:"bytes,3,opt,name=service_access_code,json=ServiceAccessCode,proto3" json:"service_access_code,omitempty"`
	RequestId         string                   `protobuf:"bytes,4,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	PidData           *VerifyOTPRequestPidData `protobuf:"bytes,8,opt,name=pid_data,json=PidData,proto3" json:"pid_data,omitempty"`
}

func (x *VerifyOTPRequest) Reset() {
	*x = VerifyOTPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOTPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOTPRequest) ProtoMessage() {}

func (x *VerifyOTPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOTPRequest.ProtoReflect.Descriptor instead.
func (*VerifyOTPRequest) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{4}
}

func (x *VerifyOTPRequest) GetSenderCode() string {
	if x != nil {
		return x.SenderCode
	}
	return ""
}

func (x *VerifyOTPRequest) GetServiceAccessId() string {
	if x != nil {
		return x.ServiceAccessId
	}
	return ""
}

func (x *VerifyOTPRequest) GetServiceAccessCode() string {
	if x != nil {
		return x.ServiceAccessCode
	}
	return ""
}

func (x *VerifyOTPRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VerifyOTPRequest) GetPidData() *VerifyOTPRequestPidData {
	if x != nil {
		return x.PidData
	}
	return nil
}

type VerifyOTPRequestPidData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// request id received in GetDataResponse
	OtpRequestId string `protobuf:"bytes,1,opt,name=otp_request_id,json=Request_Id,proto3" json:"otp_request_id,omitempty"`
	Otp          string `protobuf:"bytes,2,opt,name=otp,json=Otp,proto3" json:"otp,omitempty"`
	ValidateOtp  string `protobuf:"bytes,3,opt,name=validate_otp,json=Validate,proto3" json:"validate_otp,omitempty"`
}

func (x *VerifyOTPRequestPidData) Reset() {
	*x = VerifyOTPRequestPidData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOTPRequestPidData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOTPRequestPidData) ProtoMessage() {}

func (x *VerifyOTPRequestPidData) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOTPRequestPidData.ProtoReflect.Descriptor instead.
func (*VerifyOTPRequestPidData) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{5}
}

func (x *VerifyOTPRequestPidData) GetOtpRequestId() string {
	if x != nil {
		return x.OtpRequestId
	}
	return ""
}

func (x *VerifyOTPRequestPidData) GetOtp() string {
	if x != nil {
		return x.Otp
	}
	return ""
}

func (x *VerifyOTPRequestPidData) GetValidateOtp() string {
	if x != nil {
		return x.ValidateOtp
	}
	return ""
}

type DownloadRequestPidData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycNo         string `protobuf:"bytes,1,opt,name=ckyc_no,json=CkycNo,proto3" json:"ckyc_no,omitempty"`
	AuthFactor     string `protobuf:"bytes,2,opt,name=auth_factor,json=AuthFactor,proto3" json:"auth_factor,omitempty"`
	AuthFactorType string `protobuf:"bytes,3,opt,name=auth_factor_type,json=AuthFactorType,proto3" json:"auth_factor_type,omitempty"`
	DownloadStatus string `protobuf:"bytes,4,opt,name=download_status,json=DownloadStatus,proto3" json:"download_status,omitempty"`
}

func (x *DownloadRequestPidData) Reset() {
	*x = DownloadRequestPidData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadRequestPidData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadRequestPidData) ProtoMessage() {}

func (x *DownloadRequestPidData) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadRequestPidData.ProtoReflect.Descriptor instead.
func (*DownloadRequestPidData) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{6}
}

func (x *DownloadRequestPidData) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *DownloadRequestPidData) GetAuthFactor() string {
	if x != nil {
		return x.AuthFactor
	}
	return ""
}

func (x *DownloadRequestPidData) GetAuthFactorType() string {
	if x != nil {
		return x.AuthFactorType
	}
	return ""
}

func (x *DownloadRequestPidData) GetDownloadStatus() string {
	if x != nil {
		return x.DownloadStatus
	}
	return ""
}

type GetDataResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycInquiry *CkycInq `protobuf:"bytes,1,opt,name=ckyc_inquiry,json=CkycInq,proto3" json:"ckyc_inquiry,omitempty"`
	RequestId   string   `protobuf:"bytes,2,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	// response code is used to identify the status of the request and type of error
	ResponseCode string `protobuf:"bytes,3,opt,name=response_code,json=Response,proto3" json:"response_code,omitempty"`
	// description of the response code
	Reason string `protobuf:"bytes,4,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
}

func (x *GetDataResponse) Reset() {
	*x = GetDataResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataResponse) ProtoMessage() {}

func (x *GetDataResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataResponse.ProtoReflect.Descriptor instead.
func (*GetDataResponse) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{7}
}

func (x *GetDataResponse) GetCkycInquiry() *CkycInq {
	if x != nil {
		return x.CkycInquiry
	}
	return nil
}

func (x *GetDataResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetDataResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetDataResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type VerifyOTPResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycInquiry *CkycInq `protobuf:"bytes,1,opt,name=ckyc_inquiry,json=CkycInq,proto3" json:"ckyc_inquiry,omitempty"`
	RequestId   string   `protobuf:"bytes,2,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	// response code is used to identify the status of the request and type of error
	ResponseCode string `protobuf:"bytes,3,opt,name=response_code,json=Response,proto3" json:"response_code,omitempty"`
	// description of the response code
	Reason string `protobuf:"bytes,4,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
}

func (x *VerifyOTPResponse) Reset() {
	*x = VerifyOTPResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyOTPResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyOTPResponse) ProtoMessage() {}

func (x *VerifyOTPResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyOTPResponse.ProtoReflect.Descriptor instead.
func (*VerifyOTPResponse) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{8}
}

func (x *VerifyOTPResponse) GetCkycInquiry() *CkycInq {
	if x != nil {
		return x.CkycInquiry
	}
	return nil
}

func (x *VerifyOTPResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *VerifyOTPResponse) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *VerifyOTPResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type GetDataResponseV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CkycInquiry *CkycInqV3 `protobuf:"bytes,1,opt,name=ckyc_inquiry,json=CkycInq,proto3" json:"ckyc_inquiry,omitempty"`
	RequestId   string     `protobuf:"bytes,2,opt,name=request_id,json=RequestId,proto3" json:"request_id,omitempty"`
	// response code is used to identify the status of the request and type of error
	ResponseCode string `protobuf:"bytes,3,opt,name=response_code,json=Response,proto3" json:"response_code,omitempty"`
	// description of the response code
	Reason string `protobuf:"bytes,4,opt,name=reason,json=Reason,proto3" json:"reason,omitempty"`
}

func (x *GetDataResponseV3) Reset() {
	*x = GetDataResponseV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDataResponseV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDataResponseV3) ProtoMessage() {}

func (x *GetDataResponseV3) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDataResponseV3.ProtoReflect.Descriptor instead.
func (*GetDataResponseV3) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{9}
}

func (x *GetDataResponseV3) GetCkycInquiry() *CkycInqV3 {
	if x != nil {
		return x.CkycInquiry
	}
	return nil
}

func (x *GetDataResponseV3) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *GetDataResponseV3) GetResponseCode() string {
	if x != nil {
		return x.ResponseCode
	}
	return ""
}

func (x *GetDataResponseV3) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type CkycInqV3 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DownloadStatus string `protobuf:"bytes,1,opt,name=download_status,json=DownloadStatus,proto3" json:"download_status,omitempty"`
	OtpRequestId   string `protobuf:"bytes,2,opt,name=otp_request_id,json=Request_id,proto3" json:"otp_request_id,omitempty"`
	Message        string `protobuf:"bytes,3,opt,name=message,json=Error,proto3" json:"message,omitempty"`
}

func (x *CkycInqV3) Reset() {
	*x = CkycInqV3{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkycInqV3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkycInqV3) ProtoMessage() {}

func (x *CkycInqV3) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkycInqV3.ProtoReflect.Descriptor instead.
func (*CkycInqV3) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{10}
}

func (x *CkycInqV3) GetDownloadStatus() string {
	if x != nil {
		return x.DownloadStatus
	}
	return ""
}

func (x *CkycInqV3) GetOtpRequestId() string {
	if x != nil {
		return x.OtpRequestId
	}
	return ""
}

func (x *CkycInqV3) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CkycInq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PidData      *DownloadResponsePidData `protobuf:"bytes,1,opt,name=pid_data,json=PID,proto3" json:"pid_data,omitempty"`
	ErrorMessage string                   `protobuf:"bytes,2,opt,name=error_message,json=Error,proto3" json:"error_message,omitempty"`
}

func (x *CkycInq) Reset() {
	*x = CkycInq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CkycInq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CkycInq) ProtoMessage() {}

func (x *CkycInq) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CkycInq.ProtoReflect.Descriptor instead.
func (*CkycInq) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{11}
}

func (x *CkycInq) GetPidData() *DownloadResponsePidData {
	if x != nil {
		return x.PidData
	}
	return nil
}

func (x *CkycInq) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type DownloadResponsePidData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PersonalDetailsData      *PersonalDetails      `protobuf:"bytes,1,opt,name=personal_details_data,json=PERSONAL_DETAILS,proto3" json:"personal_details_data,omitempty"`
	IdentityDetailsData      *IdentityDetails      `protobuf:"bytes,2,opt,name=identity_details_data,json=IdentityDetails,proto3" json:"identity_details_data,omitempty"`
	RelatedPersonDetailsData *RelatedPersonDetails `protobuf:"bytes,3,opt,name=related_person_details_data,json=RelatedPersonDetails,proto3" json:"related_person_details_data,omitempty"`
	ImageDetails             *ImageDetails         `protobuf:"bytes,4,opt,name=ImageDetails,proto3" json:"ImageDetails,omitempty"`
}

func (x *DownloadResponsePidData) Reset() {
	*x = DownloadResponsePidData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DownloadResponsePidData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DownloadResponsePidData) ProtoMessage() {}

func (x *DownloadResponsePidData) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DownloadResponsePidData.ProtoReflect.Descriptor instead.
func (*DownloadResponsePidData) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{12}
}

func (x *DownloadResponsePidData) GetPersonalDetailsData() *PersonalDetails {
	if x != nil {
		return x.PersonalDetailsData
	}
	return nil
}

func (x *DownloadResponsePidData) GetIdentityDetailsData() *IdentityDetails {
	if x != nil {
		return x.IdentityDetailsData
	}
	return nil
}

func (x *DownloadResponsePidData) GetRelatedPersonDetailsData() *RelatedPersonDetails {
	if x != nil {
		return x.RelatedPersonDetailsData
	}
	return nil
}

func (x *DownloadResponsePidData) GetImageDetails() *ImageDetails {
	if x != nil {
		return x.ImageDetails
	}
	return nil
}

type PersonalDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccType            string `protobuf:"bytes,1,opt,name=acc_type,json=AccType,proto3" json:"acc_type,omitempty"`
	CkycNo             string `protobuf:"bytes,2,opt,name=ckyc_no,json=CkycNo,proto3" json:"ckyc_no,omitempty"`
	Prefix             string `protobuf:"bytes,3,opt,name=prefix,json=Prefix,proto3" json:"prefix,omitempty"`
	Fname              string `protobuf:"bytes,4,opt,name=fname,json=Fname,proto3" json:"fname,omitempty"`
	Mname              string `protobuf:"bytes,5,opt,name=mname,json=Mname,proto3" json:"mname,omitempty"`
	Lname              string `protobuf:"bytes,6,opt,name=lname,json=Lname,proto3" json:"lname,omitempty"`
	MaidenPrefix       string `protobuf:"bytes,7,opt,name=maiden_prefix,json=MaidenPrefix,proto3" json:"maiden_prefix,omitempty"`
	MaidenFname        string `protobuf:"bytes,8,opt,name=maiden_fname,json=MaidenFname,proto3" json:"maiden_fname,omitempty"`
	MaidenMname        string `protobuf:"bytes,9,opt,name=maiden_mname,json=MaidenMname,proto3" json:"maiden_mname,omitempty"`
	MaidenLname        string `protobuf:"bytes,10,opt,name=maiden_lname,json=MaidenLname,proto3" json:"maiden_lname,omitempty"`
	FatherspouseFlag   string `protobuf:"bytes,11,opt,name=fatherspouse_flag,json=FatherspouseFlag,proto3" json:"fatherspouse_flag,omitempty"`
	FatherPrefix       string `protobuf:"bytes,12,opt,name=father_prefix,json=FatherPrefix,proto3" json:"father_prefix,omitempty"`
	FatherFname        string `protobuf:"bytes,13,opt,name=father_fname,json=FatherFname,proto3" json:"father_fname,omitempty"`
	FatherMname        string `protobuf:"bytes,14,opt,name=father_mname,json=FatherMname,proto3" json:"father_mname,omitempty"`
	FatherLname        string `protobuf:"bytes,15,opt,name=father_lname,json=FatherLname,proto3" json:"father_lname,omitempty"`
	MotherPrefix       string `protobuf:"bytes,16,opt,name=mother_prefix,json=MotherPrefix,proto3" json:"mother_prefix,omitempty"`
	MotherFname        string `protobuf:"bytes,17,opt,name=mother_fname,json=MotherFname,proto3" json:"mother_fname,omitempty"`
	MotherMname        string `protobuf:"bytes,18,opt,name=mother_mname,json=MotherMname,proto3" json:"mother_mname,omitempty"`
	MotherLname        string `protobuf:"bytes,19,opt,name=mother_lname,json=MotherLname,proto3" json:"mother_lname,omitempty"`
	Gender             string `protobuf:"bytes,20,opt,name=gender,json=Gender,proto3" json:"gender,omitempty"`
	MartialStatus      string `protobuf:"bytes,21,opt,name=martial_status,json=MartialStatus,proto3" json:"martial_status,omitempty"`
	Nationality        string `protobuf:"bytes,22,opt,name=nationality,json=Nationality,proto3" json:"nationality,omitempty"`
	Occupation         string `protobuf:"bytes,23,opt,name=occupation,json=Occupation,proto3" json:"occupation,omitempty"`
	Dob                string `protobuf:"bytes,24,opt,name=dob,json=Dob,proto3" json:"dob,omitempty"`
	ResiStatus         string `protobuf:"bytes,25,opt,name=resi_status,json=ResiStatus,proto3" json:"resi_status,omitempty"`
	JuriFlag           string `protobuf:"bytes,26,opt,name=juri_flag,json=JuriFlag,proto3" json:"juri_flag,omitempty"`
	JuriResi           string `protobuf:"bytes,27,opt,name=juri_resi,json=JuriResi,proto3" json:"juri_resi,omitempty"`
	TaxNum             string `protobuf:"bytes,28,opt,name=tax_num,json=TaxNum,proto3" json:"tax_num,omitempty"`
	BirthCountry       string `protobuf:"bytes,29,opt,name=birth_country,json=BirthCountry,proto3" json:"birth_country,omitempty"`
	BirthPlace         string `protobuf:"bytes,30,opt,name=birth_place,json=BirthPlace,proto3" json:"birth_place,omitempty"`
	PermType           string `protobuf:"bytes,31,opt,name=perm_type,json=PermType,proto3" json:"perm_type,omitempty"`
	PermLine1          string `protobuf:"bytes,32,opt,name=perm_line1,json=PermLine1,proto3" json:"perm_line1,omitempty"`
	PermLine2          string `protobuf:"bytes,33,opt,name=perm_line2,json=PermLine2,proto3" json:"perm_line2,omitempty"`
	PermLine3          string `protobuf:"bytes,34,opt,name=perm_line3,json=PermLine3,proto3" json:"perm_line3,omitempty"`
	PermCity           string `protobuf:"bytes,35,opt,name=perm_city,json=PermCity,proto3" json:"perm_city,omitempty"`
	PermDist           string `protobuf:"bytes,36,opt,name=perm_dist,json=PermDist,proto3" json:"perm_dist,omitempty"`
	PermState          string `protobuf:"bytes,37,opt,name=perm_state,json=PermState,proto3" json:"perm_state,omitempty"`
	PermCountry        string `protobuf:"bytes,38,opt,name=perm_country,json=PermCountry,proto3" json:"perm_country,omitempty"`
	PermPin            string `protobuf:"bytes,39,opt,name=perm_pin,json=PermPin,proto3" json:"perm_pin,omitempty"`
	PermPoa            string `protobuf:"bytes,40,opt,name=perm_poa,json=PermPoa,proto3" json:"perm_poa,omitempty"`
	PermPoaothers      string `protobuf:"bytes,41,opt,name=perm_poaothers,json=PermPoaothers,proto3" json:"perm_poaothers,omitempty"`
	PermCorresSameflag string `protobuf:"bytes,42,opt,name=PermCorresSameflag,proto3" json:"PermCorresSameflag,omitempty"`
	CorresLine1        string `protobuf:"bytes,43,opt,name=CorresLine1,proto3" json:"CorresLine1,omitempty"`
	CorresLine2        string `protobuf:"bytes,44,opt,name=CorresLine2,proto3" json:"CorresLine2,omitempty"`
	CorresLine3        string `protobuf:"bytes,45,opt,name=CorresLine3,proto3" json:"CorresLine3,omitempty"`
	CorresCity         string `protobuf:"bytes,46,opt,name=CorresCity,proto3" json:"CorresCity,omitempty"`
	CorresDist         string `protobuf:"bytes,47,opt,name=CorresDist,proto3" json:"CorresDist,omitempty"`
	CorresState        string `protobuf:"bytes,48,opt,name=CorresState,proto3" json:"CorresState,omitempty"`
	CorresCountry      string `protobuf:"bytes,49,opt,name=CorresCountry,proto3" json:"CorresCountry,omitempty"`
	CorresPin          string `protobuf:"bytes,50,opt,name=CorresPin,proto3" json:"CorresPin,omitempty"`
	JuriSameFlag       string `protobuf:"bytes,51,opt,name=juri_same_flag,json=JuriSameFlag,proto3" json:"juri_same_flag,omitempty"`
	JuriLine1          string `protobuf:"bytes,52,opt,name=juri_line1,json=JuriLine1,proto3" json:"juri_line1,omitempty"`
	JuriLine2          string `protobuf:"bytes,53,opt,name=juri_line2,json=JuriLine2,proto3" json:"juri_line2,omitempty"`
	JuriLine3          string `protobuf:"bytes,54,opt,name=juri_line3,json=JuriLine3,proto3" json:"juri_line3,omitempty"`
	JuriCity           string `protobuf:"bytes,55,opt,name=juri_city,json=JuriCity,proto3" json:"juri_city,omitempty"`
	JuriState          string `protobuf:"bytes,56,opt,name=juri_state,json=JuriState,proto3" json:"juri_state,omitempty"`
	JuriCountry        string `protobuf:"bytes,57,opt,name=juri_country,json=JuriCountry,proto3" json:"juri_country,omitempty"`
	JuriPin            string `protobuf:"bytes,58,opt,name=juri_pin,json=JuriPin,proto3" json:"juri_pin,omitempty"`
	ResiStdCode        string `protobuf:"bytes,59,opt,name=resi_std_code,json=ResiStdCode,proto3" json:"resi_std_code,omitempty"`
	ResiTelNum         string `protobuf:"bytes,60,opt,name=resi_tel_num,json=ResiTelNum,proto3" json:"resi_tel_num,omitempty"`
	OffStdCode         string `protobuf:"bytes,61,opt,name=off_std_code,json=OffStdCode,proto3" json:"off_std_code,omitempty"`
	OffTelNum          string `protobuf:"bytes,62,opt,name=off_tel_num,json=OffTelNum,proto3" json:"off_tel_num,omitempty"`
	MobCode            string `protobuf:"bytes,63,opt,name=mob_code,json=MobCode,proto3" json:"mob_code,omitempty"`
	MobNum             string `protobuf:"bytes,64,opt,name=mob_num,json=MobNum,proto3" json:"mob_num,omitempty"`
	FaxCode            string `protobuf:"bytes,65,opt,name=fax_code,json=FaxCode,proto3" json:"fax_code,omitempty"`
	FaxNo              string `protobuf:"bytes,66,opt,name=fax_no,json=FaxNo,proto3" json:"fax_no,omitempty"`
	Email              string `protobuf:"bytes,67,opt,name=email,json=Email,proto3" json:"email,omitempty"`
	Remarks            string `protobuf:"bytes,68,opt,name=remarks,json=Remarks,proto3" json:"remarks,omitempty"`
	DecDate            string `protobuf:"bytes,69,opt,name=dec_date,json=DecDate,proto3" json:"dec_date,omitempty"`
	DecPlace           string `protobuf:"bytes,70,opt,name=dec_place,json=DecPlace,proto3" json:"dec_place,omitempty"`
	KycDate            string `protobuf:"bytes,71,opt,name=kyc_date,json=KycDate,proto3" json:"kyc_date,omitempty"`
	DocSub             string `protobuf:"bytes,72,opt,name=doc_sub,json=DocSub,proto3" json:"doc_sub,omitempty"`
	KycName            string `protobuf:"bytes,73,opt,name=kyc_name,json=KycName,proto3" json:"kyc_name,omitempty"`
	KycDesignation     string `protobuf:"bytes,74,opt,name=kyc_designation,json=KycDesignation,proto3" json:"kyc_designation,omitempty"`
	KycBranch          string `protobuf:"bytes,75,opt,name=kyc_branch,json=KycBranch,proto3" json:"kyc_branch,omitempty"`
	KycEmpcode         string `protobuf:"bytes,76,opt,name=kyc_empcode,json=KycEmpcode,proto3" json:"kyc_empcode,omitempty"`
	OrgName            string `protobuf:"bytes,77,opt,name=org_name,json=OrgName,proto3" json:"org_name,omitempty"`
	OrgCode            string `protobuf:"bytes,78,opt,name=org_code,json=OrgCode,proto3" json:"org_code,omitempty"`
	NumIdentity        string `protobuf:"bytes,79,opt,name=num_identity,json=NumIdentity,proto3" json:"num_identity,omitempty"`
	NumRelated         string `protobuf:"bytes,80,opt,name=num_related,json=NumRelated,proto3" json:"num_related,omitempty"`
	NumImages          string `protobuf:"bytes,81,opt,name=num_images,json=NumImages,proto3" json:"num_images,omitempty"`
	Pan                string `protobuf:"bytes,82,opt,name=pan,json=PAN,proto3" json:"pan,omitempty"`
}

func (x *PersonalDetails) Reset() {
	*x = PersonalDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PersonalDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersonalDetails) ProtoMessage() {}

func (x *PersonalDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersonalDetails.ProtoReflect.Descriptor instead.
func (*PersonalDetails) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{13}
}

func (x *PersonalDetails) GetAccType() string {
	if x != nil {
		return x.AccType
	}
	return ""
}

func (x *PersonalDetails) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *PersonalDetails) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *PersonalDetails) GetFname() string {
	if x != nil {
		return x.Fname
	}
	return ""
}

func (x *PersonalDetails) GetMname() string {
	if x != nil {
		return x.Mname
	}
	return ""
}

func (x *PersonalDetails) GetLname() string {
	if x != nil {
		return x.Lname
	}
	return ""
}

func (x *PersonalDetails) GetMaidenPrefix() string {
	if x != nil {
		return x.MaidenPrefix
	}
	return ""
}

func (x *PersonalDetails) GetMaidenFname() string {
	if x != nil {
		return x.MaidenFname
	}
	return ""
}

func (x *PersonalDetails) GetMaidenMname() string {
	if x != nil {
		return x.MaidenMname
	}
	return ""
}

func (x *PersonalDetails) GetMaidenLname() string {
	if x != nil {
		return x.MaidenLname
	}
	return ""
}

func (x *PersonalDetails) GetFatherspouseFlag() string {
	if x != nil {
		return x.FatherspouseFlag
	}
	return ""
}

func (x *PersonalDetails) GetFatherPrefix() string {
	if x != nil {
		return x.FatherPrefix
	}
	return ""
}

func (x *PersonalDetails) GetFatherFname() string {
	if x != nil {
		return x.FatherFname
	}
	return ""
}

func (x *PersonalDetails) GetFatherMname() string {
	if x != nil {
		return x.FatherMname
	}
	return ""
}

func (x *PersonalDetails) GetFatherLname() string {
	if x != nil {
		return x.FatherLname
	}
	return ""
}

func (x *PersonalDetails) GetMotherPrefix() string {
	if x != nil {
		return x.MotherPrefix
	}
	return ""
}

func (x *PersonalDetails) GetMotherFname() string {
	if x != nil {
		return x.MotherFname
	}
	return ""
}

func (x *PersonalDetails) GetMotherMname() string {
	if x != nil {
		return x.MotherMname
	}
	return ""
}

func (x *PersonalDetails) GetMotherLname() string {
	if x != nil {
		return x.MotherLname
	}
	return ""
}

func (x *PersonalDetails) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *PersonalDetails) GetMartialStatus() string {
	if x != nil {
		return x.MartialStatus
	}
	return ""
}

func (x *PersonalDetails) GetNationality() string {
	if x != nil {
		return x.Nationality
	}
	return ""
}

func (x *PersonalDetails) GetOccupation() string {
	if x != nil {
		return x.Occupation
	}
	return ""
}

func (x *PersonalDetails) GetDob() string {
	if x != nil {
		return x.Dob
	}
	return ""
}

func (x *PersonalDetails) GetResiStatus() string {
	if x != nil {
		return x.ResiStatus
	}
	return ""
}

func (x *PersonalDetails) GetJuriFlag() string {
	if x != nil {
		return x.JuriFlag
	}
	return ""
}

func (x *PersonalDetails) GetJuriResi() string {
	if x != nil {
		return x.JuriResi
	}
	return ""
}

func (x *PersonalDetails) GetTaxNum() string {
	if x != nil {
		return x.TaxNum
	}
	return ""
}

func (x *PersonalDetails) GetBirthCountry() string {
	if x != nil {
		return x.BirthCountry
	}
	return ""
}

func (x *PersonalDetails) GetBirthPlace() string {
	if x != nil {
		return x.BirthPlace
	}
	return ""
}

func (x *PersonalDetails) GetPermType() string {
	if x != nil {
		return x.PermType
	}
	return ""
}

func (x *PersonalDetails) GetPermLine1() string {
	if x != nil {
		return x.PermLine1
	}
	return ""
}

func (x *PersonalDetails) GetPermLine2() string {
	if x != nil {
		return x.PermLine2
	}
	return ""
}

func (x *PersonalDetails) GetPermLine3() string {
	if x != nil {
		return x.PermLine3
	}
	return ""
}

func (x *PersonalDetails) GetPermCity() string {
	if x != nil {
		return x.PermCity
	}
	return ""
}

func (x *PersonalDetails) GetPermDist() string {
	if x != nil {
		return x.PermDist
	}
	return ""
}

func (x *PersonalDetails) GetPermState() string {
	if x != nil {
		return x.PermState
	}
	return ""
}

func (x *PersonalDetails) GetPermCountry() string {
	if x != nil {
		return x.PermCountry
	}
	return ""
}

func (x *PersonalDetails) GetPermPin() string {
	if x != nil {
		return x.PermPin
	}
	return ""
}

func (x *PersonalDetails) GetPermPoa() string {
	if x != nil {
		return x.PermPoa
	}
	return ""
}

func (x *PersonalDetails) GetPermPoaothers() string {
	if x != nil {
		return x.PermPoaothers
	}
	return ""
}

func (x *PersonalDetails) GetPermCorresSameflag() string {
	if x != nil {
		return x.PermCorresSameflag
	}
	return ""
}

func (x *PersonalDetails) GetCorresLine1() string {
	if x != nil {
		return x.CorresLine1
	}
	return ""
}

func (x *PersonalDetails) GetCorresLine2() string {
	if x != nil {
		return x.CorresLine2
	}
	return ""
}

func (x *PersonalDetails) GetCorresLine3() string {
	if x != nil {
		return x.CorresLine3
	}
	return ""
}

func (x *PersonalDetails) GetCorresCity() string {
	if x != nil {
		return x.CorresCity
	}
	return ""
}

func (x *PersonalDetails) GetCorresDist() string {
	if x != nil {
		return x.CorresDist
	}
	return ""
}

func (x *PersonalDetails) GetCorresState() string {
	if x != nil {
		return x.CorresState
	}
	return ""
}

func (x *PersonalDetails) GetCorresCountry() string {
	if x != nil {
		return x.CorresCountry
	}
	return ""
}

func (x *PersonalDetails) GetCorresPin() string {
	if x != nil {
		return x.CorresPin
	}
	return ""
}

func (x *PersonalDetails) GetJuriSameFlag() string {
	if x != nil {
		return x.JuriSameFlag
	}
	return ""
}

func (x *PersonalDetails) GetJuriLine1() string {
	if x != nil {
		return x.JuriLine1
	}
	return ""
}

func (x *PersonalDetails) GetJuriLine2() string {
	if x != nil {
		return x.JuriLine2
	}
	return ""
}

func (x *PersonalDetails) GetJuriLine3() string {
	if x != nil {
		return x.JuriLine3
	}
	return ""
}

func (x *PersonalDetails) GetJuriCity() string {
	if x != nil {
		return x.JuriCity
	}
	return ""
}

func (x *PersonalDetails) GetJuriState() string {
	if x != nil {
		return x.JuriState
	}
	return ""
}

func (x *PersonalDetails) GetJuriCountry() string {
	if x != nil {
		return x.JuriCountry
	}
	return ""
}

func (x *PersonalDetails) GetJuriPin() string {
	if x != nil {
		return x.JuriPin
	}
	return ""
}

func (x *PersonalDetails) GetResiStdCode() string {
	if x != nil {
		return x.ResiStdCode
	}
	return ""
}

func (x *PersonalDetails) GetResiTelNum() string {
	if x != nil {
		return x.ResiTelNum
	}
	return ""
}

func (x *PersonalDetails) GetOffStdCode() string {
	if x != nil {
		return x.OffStdCode
	}
	return ""
}

func (x *PersonalDetails) GetOffTelNum() string {
	if x != nil {
		return x.OffTelNum
	}
	return ""
}

func (x *PersonalDetails) GetMobCode() string {
	if x != nil {
		return x.MobCode
	}
	return ""
}

func (x *PersonalDetails) GetMobNum() string {
	if x != nil {
		return x.MobNum
	}
	return ""
}

func (x *PersonalDetails) GetFaxCode() string {
	if x != nil {
		return x.FaxCode
	}
	return ""
}

func (x *PersonalDetails) GetFaxNo() string {
	if x != nil {
		return x.FaxNo
	}
	return ""
}

func (x *PersonalDetails) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *PersonalDetails) GetRemarks() string {
	if x != nil {
		return x.Remarks
	}
	return ""
}

func (x *PersonalDetails) GetDecDate() string {
	if x != nil {
		return x.DecDate
	}
	return ""
}

func (x *PersonalDetails) GetDecPlace() string {
	if x != nil {
		return x.DecPlace
	}
	return ""
}

func (x *PersonalDetails) GetKycDate() string {
	if x != nil {
		return x.KycDate
	}
	return ""
}

func (x *PersonalDetails) GetDocSub() string {
	if x != nil {
		return x.DocSub
	}
	return ""
}

func (x *PersonalDetails) GetKycName() string {
	if x != nil {
		return x.KycName
	}
	return ""
}

func (x *PersonalDetails) GetKycDesignation() string {
	if x != nil {
		return x.KycDesignation
	}
	return ""
}

func (x *PersonalDetails) GetKycBranch() string {
	if x != nil {
		return x.KycBranch
	}
	return ""
}

func (x *PersonalDetails) GetKycEmpcode() string {
	if x != nil {
		return x.KycEmpcode
	}
	return ""
}

func (x *PersonalDetails) GetOrgName() string {
	if x != nil {
		return x.OrgName
	}
	return ""
}

func (x *PersonalDetails) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

func (x *PersonalDetails) GetNumIdentity() string {
	if x != nil {
		return x.NumIdentity
	}
	return ""
}

func (x *PersonalDetails) GetNumRelated() string {
	if x != nil {
		return x.NumRelated
	}
	return ""
}

func (x *PersonalDetails) GetNumImages() string {
	if x != nil {
		return x.NumImages
	}
	return ""
}

func (x *PersonalDetails) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

type IdentityDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Identity []*Identity `protobuf:"bytes,1,rep,name=identity,json=Identity,proto3" json:"identity,omitempty"`
}

func (x *IdentityDetails) Reset() {
	*x = IdentityDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IdentityDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IdentityDetails) ProtoMessage() {}

func (x *IdentityDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IdentityDetails.ProtoReflect.Descriptor instead.
func (*IdentityDetails) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{14}
}

func (x *IdentityDetails) GetIdentity() []*Identity {
	if x != nil {
		return x.Identity
	}
	return nil
}

type Identity struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SequenceNo       string `protobuf:"bytes,1,opt,name=sequence_no,json=SequenceNo,proto3" json:"sequence_no,omitempty"`
	IdentType        string `protobuf:"bytes,2,opt,name=ident_type,json=IdentType,proto3" json:"ident_type,omitempty"`
	IdentNum         string `protobuf:"bytes,3,opt,name=ident_num,json=IdentNum,proto3" json:"ident_num,omitempty"`
	IdExpiryDate     string `protobuf:"bytes,4,opt,name=id_expiry_date,json=IdExpiryDate,proto3" json:"id_expiry_date,omitempty"`
	IdProofSubmitted string `protobuf:"bytes,5,opt,name=id_proof_submitted,json=IdProofSubmitted,proto3" json:"id_proof_submitted,omitempty"`
	IdverStatus      string `protobuf:"bytes,6,opt,name=idver_status,json=IdverStatus,proto3" json:"idver_status,omitempty"`
}

func (x *Identity) Reset() {
	*x = Identity{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Identity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Identity) ProtoMessage() {}

func (x *Identity) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Identity.ProtoReflect.Descriptor instead.
func (*Identity) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{15}
}

func (x *Identity) GetSequenceNo() string {
	if x != nil {
		return x.SequenceNo
	}
	return ""
}

func (x *Identity) GetIdentType() string {
	if x != nil {
		return x.IdentType
	}
	return ""
}

func (x *Identity) GetIdentNum() string {
	if x != nil {
		return x.IdentNum
	}
	return ""
}

func (x *Identity) GetIdExpiryDate() string {
	if x != nil {
		return x.IdExpiryDate
	}
	return ""
}

func (x *Identity) GetIdProofSubmitted() string {
	if x != nil {
		return x.IdProofSubmitted
	}
	return ""
}

func (x *Identity) GetIdverStatus() string {
	if x != nil {
		return x.IdverStatus
	}
	return ""
}

type RelatedPersonDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RelatedPersonData []*RelatedPerson `protobuf:"bytes,1,rep,name=related_person_data,json=RelatedPerson,proto3" json:"related_person_data,omitempty"`
}

func (x *RelatedPersonDetails) Reset() {
	*x = RelatedPersonDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelatedPersonDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedPersonDetails) ProtoMessage() {}

func (x *RelatedPersonDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedPersonDetails.ProtoReflect.Descriptor instead.
func (*RelatedPersonDetails) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{16}
}

func (x *RelatedPersonDetails) GetRelatedPersonData() []*RelatedPerson {
	if x != nil {
		return x.RelatedPersonData
	}
	return nil
}

type RelatedPerson struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SequenceNo     string `protobuf:"bytes,1,opt,name=sequence_no,json=SequenceNo,proto3" json:"sequence_no,omitempty"`
	RelType        string `protobuf:"bytes,2,opt,name=rel_type,json=RelType,proto3" json:"rel_type,omitempty"`
	CkycNo         string `protobuf:"bytes,3,opt,name=ckyc_no,json=CkycNo,proto3" json:"ckyc_no,omitempty"`
	Prefix         string `protobuf:"bytes,4,opt,name=prefix,json=Prefix,proto3" json:"prefix,omitempty"`
	Fname          string `protobuf:"bytes,5,opt,name=fname,json=Fname,proto3" json:"fname,omitempty"`
	Mname          string `protobuf:"bytes,6,opt,name=mname,json=Mname,proto3" json:"mname,omitempty"`
	Lname          string `protobuf:"bytes,7,opt,name=lname,json=Lname,proto3" json:"lname,omitempty"`
	Pan            string `protobuf:"bytes,8,opt,name=pan,json=Pan,proto3" json:"pan,omitempty"`
	Uid            string `protobuf:"bytes,9,opt,name=uid,json=Uid,proto3" json:"uid,omitempty"`
	Voterid        string `protobuf:"bytes,10,opt,name=voterid,json=Voterid,proto3" json:"voterid,omitempty"`
	Nrega          string `protobuf:"bytes,11,opt,name=nrega,json=Nrega,proto3" json:"nrega,omitempty"`
	Passport       string `protobuf:"bytes,12,opt,name=passport,json=Passport,proto3" json:"passport,omitempty"`
	PassportExp    string `protobuf:"bytes,13,opt,name=passport_exp,json=PassportExp,proto3" json:"passport_exp,omitempty"`
	DrivingLicence string `protobuf:"bytes,14,opt,name=driving_licence,json=DrivingLicence,proto3" json:"driving_licence,omitempty"`
	DrivingExp     string `protobuf:"bytes,15,opt,name=driving_exp,json=DrivingExp,proto3" json:"driving_exp,omitempty"`
	OtheridName    string `protobuf:"bytes,16,opt,name=otherid_name,json=OtheridName,proto3" json:"otherid_name,omitempty"`
	OtheridNo      string `protobuf:"bytes,17,opt,name=otherid_no,json=OtheridNo,proto3" json:"otherid_no,omitempty"`
	DecDate        string `protobuf:"bytes,18,opt,name=dec_date,json=DecDate,proto3" json:"dec_date,omitempty"`
	DecPlace       string `protobuf:"bytes,19,opt,name=dec_place,json=DecPlace,proto3" json:"dec_place,omitempty"`
	KycDate        string `protobuf:"bytes,20,opt,name=kyc_date,json=KycDate,proto3" json:"kyc_date,omitempty"`
	DocSub         string `protobuf:"bytes,21,opt,name=doc_sub,json=DocSub,proto3" json:"doc_sub,omitempty"`
	KycName        string `protobuf:"bytes,22,opt,name=kyc_name,json=KycName,proto3" json:"kyc_name,omitempty"`
	KycDesignation string `protobuf:"bytes,23,opt,name=kyc_designation,json=KycDesignation,proto3" json:"kyc_designation,omitempty"`
	KycBranch      string `protobuf:"bytes,24,opt,name=kyc_branch,json=KycBranch,proto3" json:"kyc_branch,omitempty"`
	KycEmpcode     string `protobuf:"bytes,25,opt,name=kyc_empcode,json=KycEmpcode,proto3" json:"kyc_empcode,omitempty"`
	OrcName        string `protobuf:"bytes,26,opt,name=orc_name,json=OrcName,proto3" json:"orc_name,omitempty"`
	OrgCode        string `protobuf:"bytes,27,opt,name=org_code,json=OrgCode,proto3" json:"org_code,omitempty"`
}

func (x *RelatedPerson) Reset() {
	*x = RelatedPerson{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RelatedPerson) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelatedPerson) ProtoMessage() {}

func (x *RelatedPerson) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelatedPerson.ProtoReflect.Descriptor instead.
func (*RelatedPerson) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{17}
}

func (x *RelatedPerson) GetSequenceNo() string {
	if x != nil {
		return x.SequenceNo
	}
	return ""
}

func (x *RelatedPerson) GetRelType() string {
	if x != nil {
		return x.RelType
	}
	return ""
}

func (x *RelatedPerson) GetCkycNo() string {
	if x != nil {
		return x.CkycNo
	}
	return ""
}

func (x *RelatedPerson) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *RelatedPerson) GetFname() string {
	if x != nil {
		return x.Fname
	}
	return ""
}

func (x *RelatedPerson) GetMname() string {
	if x != nil {
		return x.Mname
	}
	return ""
}

func (x *RelatedPerson) GetLname() string {
	if x != nil {
		return x.Lname
	}
	return ""
}

func (x *RelatedPerson) GetPan() string {
	if x != nil {
		return x.Pan
	}
	return ""
}

func (x *RelatedPerson) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *RelatedPerson) GetVoterid() string {
	if x != nil {
		return x.Voterid
	}
	return ""
}

func (x *RelatedPerson) GetNrega() string {
	if x != nil {
		return x.Nrega
	}
	return ""
}

func (x *RelatedPerson) GetPassport() string {
	if x != nil {
		return x.Passport
	}
	return ""
}

func (x *RelatedPerson) GetPassportExp() string {
	if x != nil {
		return x.PassportExp
	}
	return ""
}

func (x *RelatedPerson) GetDrivingLicence() string {
	if x != nil {
		return x.DrivingLicence
	}
	return ""
}

func (x *RelatedPerson) GetDrivingExp() string {
	if x != nil {
		return x.DrivingExp
	}
	return ""
}

func (x *RelatedPerson) GetOtheridName() string {
	if x != nil {
		return x.OtheridName
	}
	return ""
}

func (x *RelatedPerson) GetOtheridNo() string {
	if x != nil {
		return x.OtheridNo
	}
	return ""
}

func (x *RelatedPerson) GetDecDate() string {
	if x != nil {
		return x.DecDate
	}
	return ""
}

func (x *RelatedPerson) GetDecPlace() string {
	if x != nil {
		return x.DecPlace
	}
	return ""
}

func (x *RelatedPerson) GetKycDate() string {
	if x != nil {
		return x.KycDate
	}
	return ""
}

func (x *RelatedPerson) GetDocSub() string {
	if x != nil {
		return x.DocSub
	}
	return ""
}

func (x *RelatedPerson) GetKycName() string {
	if x != nil {
		return x.KycName
	}
	return ""
}

func (x *RelatedPerson) GetKycDesignation() string {
	if x != nil {
		return x.KycDesignation
	}
	return ""
}

func (x *RelatedPerson) GetKycBranch() string {
	if x != nil {
		return x.KycBranch
	}
	return ""
}

func (x *RelatedPerson) GetKycEmpcode() string {
	if x != nil {
		return x.KycEmpcode
	}
	return ""
}

func (x *RelatedPerson) GetOrcName() string {
	if x != nil {
		return x.OrcName
	}
	return ""
}

func (x *RelatedPerson) GetOrgCode() string {
	if x != nil {
		return x.OrgCode
	}
	return ""
}

type ImageDetails struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Image []*Image `protobuf:"bytes,1,rep,name=image,json=Image,proto3" json:"image,omitempty"`
}

func (x *ImageDetails) Reset() {
	*x = ImageDetails{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ImageDetails) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImageDetails) ProtoMessage() {}

func (x *ImageDetails) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImageDetails.ProtoReflect.Descriptor instead.
func (*ImageDetails) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{18}
}

func (x *ImageDetails) GetImage() []*Image {
	if x != nil {
		return x.Image
	}
	return nil
}

type Image struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SequenceNo string `protobuf:"bytes,1,opt,name=sequence_no,json=SequenceNo,proto3" json:"sequence_no,omitempty"`
	ImageType  string `protobuf:"bytes,2,opt,name=image_type,json=ImageType,proto3" json:"image_type,omitempty"`
	ImageCode  string `protobuf:"bytes,3,opt,name=image_code,json=ImageCode,proto3" json:"image_code,omitempty"`
	ImageData  string `protobuf:"bytes,4,opt,name=image_data,json=ImageData,proto3" json:"image_data,omitempty"`
}

func (x *Image) Reset() {
	*x = Image{}
	if protoimpl.UnsafeEnabled {
		mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Image) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Image) ProtoMessage() {}

func (x *Image) ProtoReflect() protoreflect.Message {
	mi := &file_api_simulator_kyc_federal_ckyc_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Image.ProtoReflect.Descriptor instead.
func (*Image) Descriptor() ([]byte, []int) {
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP(), []int{19}
}

func (x *Image) GetSequenceNo() string {
	if x != nil {
		return x.SequenceNo
	}
	return ""
}

func (x *Image) GetImageType() string {
	if x != nil {
		return x.ImageType
	}
	return ""
}

func (x *Image) GetImageCode() string {
	if x != nil {
		return x.ImageCode
	}
	return ""
}

func (x *Image) GetImageData() string {
	if x != nil {
		return x.ImageData
	}
	return ""
}

var File_api_simulator_kyc_federal_ckyc_proto protoreflect.FileDescriptor

var file_api_simulator_kyc_federal_ckyc_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f,
	0x6b, 0x79, 0x63, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2f, 0x63, 0x6b, 0x79, 0x63,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x15, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x1a, 0x1c, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85, 0x02, 0x0a, 0x0d,
	0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a,
	0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a,
	0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x69, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x49, 0x64, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x64, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x64, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x23,
	0x0a, 0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d,
	0x62, 0x65, 0x72, 0x22, 0x8c, 0x02, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65,
	0x76, 0x69, 0x63, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3e,
	0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x50, 0x69, 0x64, 0x12, 0x1a,
	0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0x9f, 0x02, 0x0a, 0x15, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x10, 0x0a, 0x03,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x41, 0x67, 0x65, 0x12, 0x17,
	0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x43, 0x6b, 0x79, 0x63, 0x4e, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x0c, 0x70, 0x68, 0x6f,
	0x74, 0x6f, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x50, 0x68, 0x6f, 0x74, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4b, 0x79, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x64, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x63, 0x6b, 0x79, 0x63,
	0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x43, 0x6b, 0x79, 0x63, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e,
	0x63, 0x65, 0x49, 0x64, 0x22, 0x9b, 0x02, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x08, 0x70, 0x69, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x44, 0x6f,
	0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x69, 0x64,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x23, 0x0a,
	0x0d, 0x6d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x6f, 0x62, 0x69, 0x6c, 0x65, 0x4e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x22, 0xf9, 0x01, 0x0a, 0x10, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x54, 0x50,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0f, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x49, 0x64, 0x12, 0x49, 0x0a, 0x08, 0x70, 0x69, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f,
	0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x50, 0x69,
	0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x07, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x22, 0x6f,
	0x0a, 0x17, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x54, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x22, 0x0a, 0x0e, 0x6f, 0x74, 0x70,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x49, 0x64, 0x12, 0x10, 0x0a,
	0x03, 0x6f, 0x74, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x4f, 0x74, 0x70, 0x12,
	0x1e, 0x0a, 0x0c, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x74, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x22,
	0xa5, 0x01, 0x0a, 0x16, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6b,
	0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x43, 0x6b, 0x79,
	0x63, 0x4e, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x41, 0x75, 0x74, 0x68, 0x46, 0x61,
	0x63, 0x74, 0x6f, 0x72, 0x12, 0x28, 0x0a, 0x10, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x66, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x41, 0x75, 0x74, 0x68, 0x46, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x64, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61,
	0x64, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa8, 0x01, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63,
	0x6b, 0x79, 0x63, 0x5f, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e,
	0x71, 0x52, 0x07, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x22, 0xaa, 0x01, 0x0a, 0x11, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x4f, 0x54, 0x50,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0c, 0x63, 0x6b, 0x79, 0x63,
	0x5f, 0x69, 0x6e, 0x71, 0x75, 0x69, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x52, 0x07,
	0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22,
	0xac, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x56, 0x33, 0x12, 0x3f, 0x0a, 0x0c, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x69, 0x6e,
	0x71, 0x75, 0x69, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x56, 0x33, 0x52, 0x07, 0x43,
	0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x22, 0x70,
	0x0a, 0x09, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x56, 0x33, 0x12, 0x27, 0x0a, 0x0f, 0x64,
	0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x22, 0x0a, 0x0e, 0x6f, 0x74, 0x70, 0x5f, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0x6e, 0x0a, 0x07, 0x43, 0x6b, 0x79, 0x63, 0x49, 0x6e, 0x71, 0x12, 0x45, 0x0a, 0x08, 0x70,
	0x69, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x52, 0x03, 0x50,
	0x49, 0x44, 0x12, 0x1c, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0xfb, 0x02, 0x0a, 0x17, 0x44, 0x6f, 0x77, 0x6e, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x50, 0x69, 0x64, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x15,
	0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x73, 0x52, 0x10, 0x50, 0x45, 0x52, 0x53, 0x4f, 0x4e, 0x41, 0x4c, 0x5f, 0x44, 0x45,
	0x54, 0x41, 0x49, 0x4c, 0x53, 0x12, 0x56, 0x0a, 0x15, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x5f, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72,
	0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52, 0x0f, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x66, 0x0a,
	0x1b, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x5f,
	0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b,
	0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74,
	0x65, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x52,
	0x14, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x47, 0x0a, 0x0c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x74, 0x61, 0x69, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x69,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73,
	0x52, 0x0c, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x22, 0x88,
	0x14, 0x0a, 0x0f, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x61, 0x6c, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x41, 0x63, 0x63, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a,
	0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x43, 0x6b, 0x79, 0x63, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x14,
	0x0a, 0x05, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x46,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x4d, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4c, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69,
	0x78, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x50,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f,
	0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4d, 0x61, 0x69,
	0x64, 0x65, 0x6e, 0x46, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x61, 0x69, 0x64,
	0x65, 0x6e, 0x5f, 0x6d, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x4d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x4d, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d,
	0x61, 0x69, 0x64, 0x65, 0x6e, 0x5f, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x4d, 0x61, 0x69, 0x64, 0x65, 0x6e, 0x4c, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b,
	0x0a, 0x11, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x5f, 0x66,
	0x6c, 0x61, 0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x46, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x70, 0x6f, 0x75, 0x73, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x23, 0x0a, 0x0d, 0x66,
	0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78,
	0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x66, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x74, 0x68, 0x65, 0x72, 0x46, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6d, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61, 0x74, 0x68, 0x65,
	0x72, 0x4d, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x66, 0x61, 0x74, 0x68, 0x65, 0x72,
	0x5f, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x46, 0x61,
	0x74, 0x68, 0x65, 0x72, 0x4c, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x6f, 0x74,
	0x68, 0x65, 0x72, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x21,
	0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x46, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6d, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x4d,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x5f, 0x6c,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x13, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4d, 0x6f, 0x74, 0x68,
	0x65, 0x72, 0x4c, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12,
	0x25, 0x0a, 0x0e, 0x6d, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x4d, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x20, 0x0a, 0x0b, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x61, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x16, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x6f, 0x63, 0x63, 0x75,
	0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x17, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x63,
	0x63, 0x75, 0x70, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x62, 0x18,
	0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x44, 0x6f, 0x62, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65,
	0x73, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x52, 0x65, 0x73, 0x69, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x6a,
	0x75, 0x72, 0x69, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x4a, 0x75, 0x72, 0x69, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x75, 0x72, 0x69,
	0x5f, 0x72, 0x65, 0x73, 0x69, 0x18, 0x1b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4a, 0x75, 0x72,
	0x69, 0x52, 0x65, 0x73, 0x69, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x78, 0x5f, 0x6e, 0x75, 0x6d,
	0x18, 0x1c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x54, 0x61, 0x78, 0x4e, 0x75, 0x6d, 0x12, 0x23,
	0x0a, 0x0d, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18,
	0x1d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x42, 0x69, 0x72, 0x74, 0x68, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x69, 0x72, 0x74, 0x68, 0x5f, 0x70, 0x6c, 0x61,
	0x63, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x42, 0x69, 0x72, 0x74, 0x68, 0x50,
	0x6c, 0x61, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x1f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x65, 0x72, 0x6d, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18,
	0x20, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x6e, 0x65, 0x31,
	0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x21,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12,
	0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x22, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x65, 0x72, 0x6d, 0x4c, 0x69, 0x6e, 0x65, 0x33, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x63, 0x69, 0x74, 0x79, 0x18, 0x23, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x50, 0x65, 0x72, 0x6d, 0x43, 0x69, 0x74, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x65, 0x72, 0x6d, 0x5f, 0x64, 0x69, 0x73, 0x74, 0x18, 0x24, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x50, 0x65, 0x72, 0x6d, 0x44, 0x69, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x65, 0x72, 0x6d,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x25, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x50, 0x65,
	0x72, 0x6d, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x65, 0x72, 0x6d, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x26, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50,
	0x65, 0x72, 0x6d, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65,
	0x72, 0x6d, 0x5f, 0x70, 0x69, 0x6e, 0x18, 0x27, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x50, 0x65,
	0x72, 0x6d, 0x50, 0x69, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x70, 0x6f,
	0x61, 0x18, 0x28, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x50, 0x65, 0x72, 0x6d, 0x50, 0x6f, 0x61,
	0x12, 0x25, 0x0a, 0x0e, 0x70, 0x65, 0x72, 0x6d, 0x5f, 0x70, 0x6f, 0x61, 0x6f, 0x74, 0x68, 0x65,
	0x72, 0x73, 0x18, 0x29, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x50, 0x65, 0x72, 0x6d, 0x50, 0x6f,
	0x61, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x12, 0x2e, 0x0a, 0x12, 0x50, 0x65, 0x72, 0x6d, 0x43,
	0x6f, 0x72, 0x72, 0x65, 0x73, 0x53, 0x61, 0x6d, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x2a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x12, 0x50, 0x65, 0x72, 0x6d, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x53,
	0x61, 0x6d, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x72, 0x72, 0x65,
	0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x2b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f,
	0x72, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x20, 0x0a, 0x0b, 0x43, 0x6f, 0x72,
	0x72, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x2c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x20, 0x0a, 0x0b, 0x43,
	0x6f, 0x72, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x2d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x33, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x43, 0x69, 0x74, 0x79, 0x18, 0x2e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x43, 0x69, 0x74, 0x79, 0x12, 0x1e, 0x0a,
	0x0a, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x44, 0x69, 0x73, 0x74, 0x18, 0x2f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x44, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a,
	0x0b, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x18, 0x30, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x24, 0x0a, 0x0d, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x72, 0x79,
	0x18, 0x31, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x1c, 0x0a, 0x09, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73, 0x50,
	0x69, 0x6e, 0x18, 0x32, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x43, 0x6f, 0x72, 0x72, 0x65, 0x73,
	0x50, 0x69, 0x6e, 0x12, 0x24, 0x0a, 0x0e, 0x6a, 0x75, 0x72, 0x69, 0x5f, 0x73, 0x61, 0x6d, 0x65,
	0x5f, 0x66, 0x6c, 0x61, 0x67, 0x18, 0x33, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x4a, 0x75, 0x72,
	0x69, 0x53, 0x61, 0x6d, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x75, 0x72,
	0x69, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x34, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4a,
	0x75, 0x72, 0x69, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x75, 0x72, 0x69,
	0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x35, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4a, 0x75,
	0x72, 0x69, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x75, 0x72, 0x69, 0x5f,
	0x6c, 0x69, 0x6e, 0x65, 0x33, 0x18, 0x36, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4a, 0x75, 0x72,
	0x69, 0x4c, 0x69, 0x6e, 0x65, 0x33, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x75, 0x72, 0x69, 0x5f, 0x63,
	0x69, 0x74, 0x79, 0x18, 0x37, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x4a, 0x75, 0x72, 0x69, 0x43,
	0x69, 0x74, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6a, 0x75, 0x72, 0x69, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x38, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4a, 0x75, 0x72, 0x69, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6a, 0x75, 0x72, 0x69, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x72, 0x79, 0x18, 0x39, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4a, 0x75, 0x72, 0x69, 0x43, 0x6f,
	0x75, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x19, 0x0a, 0x08, 0x6a, 0x75, 0x72, 0x69, 0x5f, 0x70, 0x69,
	0x6e, 0x18, 0x3a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4a, 0x75, 0x72, 0x69, 0x50, 0x69, 0x6e,
	0x12, 0x22, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x69, 0x5f, 0x73, 0x74, 0x64, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x3b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x52, 0x65, 0x73, 0x69, 0x53, 0x74, 0x64,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x69, 0x5f, 0x74, 0x65, 0x6c,
	0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x3c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x52, 0x65, 0x73, 0x69,
	0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x20, 0x0a, 0x0c, 0x6f, 0x66, 0x66, 0x5f, 0x73, 0x74,
	0x64, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x3d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4f, 0x66,
	0x66, 0x53, 0x74, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x6f, 0x66, 0x66, 0x5f,
	0x74, 0x65, 0x6c, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x3e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4f,
	0x66, 0x66, 0x54, 0x65, 0x6c, 0x4e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x6f, 0x62, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x3f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4d, 0x6f, 0x62, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x6d, 0x6f, 0x62, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x40,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x4d, 0x6f, 0x62, 0x4e, 0x75, 0x6d, 0x12, 0x19, 0x0a, 0x08,
	0x66, 0x61, 0x78, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x41, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x46, 0x61, 0x78, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x66, 0x61, 0x78, 0x5f, 0x6e,
	0x6f, 0x18, 0x42, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x46, 0x61, 0x78, 0x4e, 0x6f, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x43, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x18,
	0x44, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x73, 0x12, 0x19,
	0x0a, 0x08, 0x64, 0x65, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x45, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x44, 0x65, 0x63, 0x44, 0x61, 0x74, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x63,
	0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x46, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x44, 0x65,
	0x63, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x18, 0x47, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4b, 0x79, 0x63, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6f, 0x63, 0x5f, 0x73, 0x75, 0x62, 0x18, 0x48, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x63, 0x53, 0x75, 0x62, 0x12, 0x19, 0x0a, 0x08, 0x6b, 0x79,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x49, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4b, 0x79,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6b, 0x79, 0x63, 0x5f, 0x64, 0x65, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x4a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x4b, 0x79, 0x63, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d,
	0x0a, 0x0a, 0x6b, 0x79, 0x63, 0x5f, 0x62, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x18, 0x4b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x4b, 0x79, 0x63, 0x42, 0x72, 0x61, 0x6e, 0x63, 0x68, 0x12, 0x1f, 0x0a,
	0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x6d, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x4c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x4b, 0x79, 0x63, 0x45, 0x6d, 0x70, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x4d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x4f, 0x72, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67,
	0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x4e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x72, 0x67,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x75, 0x6d, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x18, 0x4f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4e, 0x75, 0x6d, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x6e, 0x75, 0x6d, 0x5f, 0x72,
	0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x18, 0x50, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4e, 0x75,
	0x6d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x75, 0x6d, 0x5f,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x18, 0x51, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4e, 0x75,
	0x6d, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x52,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x50, 0x41, 0x4e, 0x22, 0x4e, 0x0a, 0x0f, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x3b, 0x0a, 0x08,
	0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x08, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22, 0xde, 0x01, 0x0a, 0x08, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x5f,
	0x6e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x4e, 0x75, 0x6d, 0x12, 0x24, 0x0a, 0x0e, 0x69, 0x64, 0x5f, 0x65, 0x78, 0x70, 0x69, 0x72, 0x79,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x49, 0x64, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x69, 0x64, 0x5f,
	0x70, 0x72, 0x6f, 0x6f, 0x66, 0x5f, 0x73, 0x75, 0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x49, 0x64, 0x50, 0x72, 0x6f, 0x6f, 0x66, 0x53, 0x75,
	0x62, 0x6d, 0x69, 0x74, 0x74, 0x65, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x64, 0x76, 0x65, 0x72,
	0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x49,
	0x64, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x68, 0x0a, 0x14, 0x52, 0x65,
	0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x73, 0x12, 0x50, 0x0a, 0x13, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50,
	0x65, 0x72, 0x73, 0x6f, 0x6e, 0x52, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x72, 0x73, 0x6f, 0x6e, 0x22, 0x83, 0x06, 0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x64,
	0x50, 0x65, 0x72, 0x73, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x72, 0x65, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x52, 0x65, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x6f, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x43, 0x6b, 0x79, 0x63, 0x4e, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x50, 0x72, 0x65,
	0x66, 0x69, 0x78, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x46, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x4d, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x6c, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x4c, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x61, 0x6e, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x50, 0x61, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x69, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x6f, 0x74,
	0x65, 0x72, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x56, 0x6f, 0x74, 0x65,
	0x72, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x6e, 0x72, 0x65, 0x67, 0x61, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x4e, 0x72, 0x65, 0x67, 0x61, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x50, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x61, 0x73, 0x73, 0x70, 0x6f, 0x72,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x50, 0x61, 0x73,
	0x73, 0x70, 0x6f, 0x72, 0x74, 0x45, 0x78, 0x70, 0x12, 0x27, 0x0a, 0x0f, 0x64, 0x72, 0x69, 0x76,
	0x69, 0x6e, 0x67, 0x5f, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x63,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x70,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x72, 0x69, 0x76, 0x69, 0x6e, 0x67, 0x45,
	0x78, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x69, 0x64, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x69,
	0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x74, 0x68, 0x65, 0x72, 0x69, 0x64,
	0x5f, 0x6e, 0x6f, 0x18, 0x11, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4f, 0x74, 0x68, 0x65, 0x72,
	0x69, 0x64, 0x4e, 0x6f, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x65, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x44, 0x65, 0x63, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x64, 0x65, 0x63, 0x5f, 0x70, 0x6c, 0x61, 0x63, 0x65, 0x18, 0x13, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x44, 0x65, 0x63, 0x50, 0x6c, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x6b, 0x79, 0x63, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x4b, 0x79, 0x63, 0x44, 0x61, 0x74, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x6f, 0x63, 0x5f, 0x73,
	0x75, 0x62, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x44, 0x6f, 0x63, 0x53, 0x75, 0x62,
	0x12, 0x19, 0x0a, 0x08, 0x6b, 0x79, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x16, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x4b, 0x79, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6b,
	0x79, 0x63, 0x5f, 0x64, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x17,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x4b, 0x79, 0x63, 0x44, 0x65, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x6b, 0x79, 0x63, 0x5f, 0x62, 0x72, 0x61, 0x6e,
	0x63, 0x68, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x4b, 0x79, 0x63, 0x42, 0x72, 0x61,
	0x6e, 0x63, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x6b, 0x79, 0x63, 0x5f, 0x65, 0x6d, 0x70, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x19, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4b, 0x79, 0x63, 0x45, 0x6d, 0x70,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x1a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4f, 0x72, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x67, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x1b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x4f, 0x72, 0x67, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x42, 0x0a, 0x0c, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x73, 0x12, 0x32, 0x0a, 0x05, 0x69, 0x6d,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x22, 0x85,
	0x01, 0x0a, 0x05, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x71, 0x75,
	0x65, 0x6e, 0x63, 0x65, 0x5f, 0x6e, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x53,
	0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x65, 0x4e, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49,
	0x6d, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x6d, 0x61,
	0x67, 0x65, 0x44, 0x61, 0x74, 0x61, 0x32, 0xeb, 0x01, 0x0a, 0x04, 0x43, 0x6b, 0x79, 0x63, 0x12,
	0x6e, 0x0a, 0x06, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12, 0x24, 0x2e, 0x73, 0x69, 0x6d, 0x75,
	0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61,
	0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x25, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e,
	0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x17, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x11, 0x3a, 0x01,
	0x2a, 0x22, 0x0c, 0x2f, 0x63, 0x6b, 0x79, 0x63, 0x2f, 0x73, 0x65, 0x61, 0x72, 0x63, 0x68, 0x12,
	0x73, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x25, 0x2e, 0x73, 0x69, 0x6d,
	0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72,
	0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79,
	0x63, 0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x61, 0x74,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x19, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x13, 0x3a, 0x01, 0x2a, 0x22, 0x0e, 0x2f, 0x63, 0x6b, 0x79, 0x63, 0x2f, 0x64, 0x6f, 0x77, 0x6e,
	0x6c, 0x6f, 0x61, 0x64, 0x42, 0x64, 0x0a, 0x30, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2e, 0x67, 0x61, 0x6d, 0x6d, 0x61, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2e, 0x6b, 0x79, 0x63,
	0x2e, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x5a, 0x30, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x70, 0x69, 0x66, 0x69, 0x2f, 0x67, 0x61, 0x6d, 0x6d, 0x61,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x73, 0x69, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x6f, 0x72, 0x2f, 0x6b,
	0x79, 0x63, 0x2f, 0x66, 0x65, 0x64, 0x65, 0x72, 0x61, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_api_simulator_kyc_federal_ckyc_proto_rawDescOnce sync.Once
	file_api_simulator_kyc_federal_ckyc_proto_rawDescData = file_api_simulator_kyc_federal_ckyc_proto_rawDesc
)

func file_api_simulator_kyc_federal_ckyc_proto_rawDescGZIP() []byte {
	file_api_simulator_kyc_federal_ckyc_proto_rawDescOnce.Do(func() {
		file_api_simulator_kyc_federal_ckyc_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_simulator_kyc_federal_ckyc_proto_rawDescData)
	})
	return file_api_simulator_kyc_federal_ckyc_proto_rawDescData
}

var file_api_simulator_kyc_federal_ckyc_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_api_simulator_kyc_federal_ckyc_proto_goTypes = []interface{}{
	(*SearchRequest)(nil),           // 0: simulator.kyc.federal.SearchRequest
	(*SearchResponse)(nil),          // 1: simulator.kyc.federal.SearchResponse
	(*SearchResponsePidData)(nil),   // 2: simulator.kyc.federal.SearchResponsePidData
	(*GetDataRequest)(nil),          // 3: simulator.kyc.federal.GetDataRequest
	(*VerifyOTPRequest)(nil),        // 4: simulator.kyc.federal.VerifyOTPRequest
	(*VerifyOTPRequestPidData)(nil), // 5: simulator.kyc.federal.VerifyOTPRequestPidData
	(*DownloadRequestPidData)(nil),  // 6: simulator.kyc.federal.DownloadRequestPidData
	(*GetDataResponse)(nil),         // 7: simulator.kyc.federal.GetDataResponse
	(*VerifyOTPResponse)(nil),       // 8: simulator.kyc.federal.VerifyOTPResponse
	(*GetDataResponseV3)(nil),       // 9: simulator.kyc.federal.GetDataResponseV3
	(*CkycInqV3)(nil),               // 10: simulator.kyc.federal.CkycInqV3
	(*CkycInq)(nil),                 // 11: simulator.kyc.federal.CkycInq
	(*DownloadResponsePidData)(nil), // 12: simulator.kyc.federal.DownloadResponsePidData
	(*PersonalDetails)(nil),         // 13: simulator.kyc.federal.PersonalDetails
	(*IdentityDetails)(nil),         // 14: simulator.kyc.federal.IdentityDetails
	(*Identity)(nil),                // 15: simulator.kyc.federal.Identity
	(*RelatedPersonDetails)(nil),    // 16: simulator.kyc.federal.RelatedPersonDetails
	(*RelatedPerson)(nil),           // 17: simulator.kyc.federal.RelatedPerson
	(*ImageDetails)(nil),            // 18: simulator.kyc.federal.ImageDetails
	(*Image)(nil),                   // 19: simulator.kyc.federal.Image
}
var file_api_simulator_kyc_federal_ckyc_proto_depIdxs = []int32{
	2,  // 0: simulator.kyc.federal.SearchResponse.pid:type_name -> simulator.kyc.federal.SearchResponsePidData
	6,  // 1: simulator.kyc.federal.GetDataRequest.pid_data:type_name -> simulator.kyc.federal.DownloadRequestPidData
	5,  // 2: simulator.kyc.federal.VerifyOTPRequest.pid_data:type_name -> simulator.kyc.federal.VerifyOTPRequestPidData
	11, // 3: simulator.kyc.federal.GetDataResponse.ckyc_inquiry:type_name -> simulator.kyc.federal.CkycInq
	11, // 4: simulator.kyc.federal.VerifyOTPResponse.ckyc_inquiry:type_name -> simulator.kyc.federal.CkycInq
	10, // 5: simulator.kyc.federal.GetDataResponseV3.ckyc_inquiry:type_name -> simulator.kyc.federal.CkycInqV3
	12, // 6: simulator.kyc.federal.CkycInq.pid_data:type_name -> simulator.kyc.federal.DownloadResponsePidData
	13, // 7: simulator.kyc.federal.DownloadResponsePidData.personal_details_data:type_name -> simulator.kyc.federal.PersonalDetails
	14, // 8: simulator.kyc.federal.DownloadResponsePidData.identity_details_data:type_name -> simulator.kyc.federal.IdentityDetails
	16, // 9: simulator.kyc.federal.DownloadResponsePidData.related_person_details_data:type_name -> simulator.kyc.federal.RelatedPersonDetails
	18, // 10: simulator.kyc.federal.DownloadResponsePidData.ImageDetails:type_name -> simulator.kyc.federal.ImageDetails
	15, // 11: simulator.kyc.federal.IdentityDetails.identity:type_name -> simulator.kyc.federal.Identity
	17, // 12: simulator.kyc.federal.RelatedPersonDetails.related_person_data:type_name -> simulator.kyc.federal.RelatedPerson
	19, // 13: simulator.kyc.federal.ImageDetails.image:type_name -> simulator.kyc.federal.Image
	0,  // 14: simulator.kyc.federal.Ckyc.Search:input_type -> simulator.kyc.federal.SearchRequest
	3,  // 15: simulator.kyc.federal.Ckyc.GetData:input_type -> simulator.kyc.federal.GetDataRequest
	1,  // 16: simulator.kyc.federal.Ckyc.Search:output_type -> simulator.kyc.federal.SearchResponse
	7,  // 17: simulator.kyc.federal.Ckyc.GetData:output_type -> simulator.kyc.federal.GetDataResponse
	16, // [16:18] is the sub-list for method output_type
	14, // [14:16] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_api_simulator_kyc_federal_ckyc_proto_init() }
func file_api_simulator_kyc_federal_ckyc_proto_init() {
	if File_api_simulator_kyc_federal_ckyc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SearchResponsePidData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOTPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOTPRequestPidData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadRequestPidData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyOTPResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDataResponseV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkycInqV3); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CkycInq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DownloadResponsePidData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PersonalDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IdentityDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Identity); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelatedPersonDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RelatedPerson); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ImageDetails); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_api_simulator_kyc_federal_ckyc_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Image); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_simulator_kyc_federal_ckyc_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_simulator_kyc_federal_ckyc_proto_goTypes,
		DependencyIndexes: file_api_simulator_kyc_federal_ckyc_proto_depIdxs,
		MessageInfos:      file_api_simulator_kyc_federal_ckyc_proto_msgTypes,
	}.Build()
	File_api_simulator_kyc_federal_ckyc_proto = out.File
	file_api_simulator_kyc_federal_ckyc_proto_rawDesc = nil
	file_api_simulator_kyc_federal_ckyc_proto_goTypes = nil
	file_api_simulator_kyc_federal_ckyc_proto_depIdxs = nil
}
