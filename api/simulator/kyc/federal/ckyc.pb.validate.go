// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/simulator/kyc/federal/ckyc.proto

package federal

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SearchRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchRequestMultiError, or
// nil if none found.
func (m *SearchRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for ServiceAccessId

	// no validation rules for ServiceAccessCode

	// no validation rules for RequestId

	// no validation rules for IdType

	// no validation rules for IdValue

	// no validation rules for MobileNumber

	if len(errors) > 0 {
		return SearchRequestMultiError(errors)
	}

	return nil
}

// SearchRequestMultiError is an error wrapping multiple validation errors
// returned by SearchRequest.ValidateAll() if the designated constraints
// aren't met.
type SearchRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchRequestMultiError) AllErrors() []error { return m }

// SearchRequestValidationError is the validation error returned by
// SearchRequest.Validate if the designated constraints aren't met.
type SearchRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchRequestValidationError) ErrorName() string { return "SearchRequestValidationError" }

// Error satisfies the builtin error interface
func (e SearchRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchRequestValidationError{}

// Validate checks the field values on SearchResponse with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SearchResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SearchResponseMultiError,
// or nil if none found.
func (m *SearchResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for RequestId

	// no validation rules for DeviceToken

	// no validation rules for SearchStatus

	if all {
		switch v := interface{}(m.GetPid()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SearchResponseValidationError{
					field:  "Pid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SearchResponseValidationError{
					field:  "Pid",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPid()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SearchResponseValidationError{
				field:  "Pid",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Response

	// no validation rules for Reason

	if len(errors) > 0 {
		return SearchResponseMultiError(errors)
	}

	return nil
}

// SearchResponseMultiError is an error wrapping multiple validation errors
// returned by SearchResponse.ValidateAll() if the designated constraints
// aren't met.
type SearchResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponseMultiError) AllErrors() []error { return m }

// SearchResponseValidationError is the validation error returned by
// SearchResponse.Validate if the designated constraints aren't met.
type SearchResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponseValidationError) ErrorName() string { return "SearchResponseValidationError" }

// Error satisfies the builtin error interface
func (e SearchResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponseValidationError{}

// Validate checks the field values on SearchResponsePidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SearchResponsePidData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SearchResponsePidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SearchResponsePidDataMultiError, or nil if none found.
func (m *SearchResponsePidData) ValidateAll() error {
	return m.validate(true)
}

func (m *SearchResponsePidData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Age

	// no validation rules for CkycNo

	// no validation rules for FathersName

	// no validation rules for ImageType

	// no validation rules for PhotoBase64

	// no validation rules for KycDate

	// no validation rules for Name

	// no validation rules for UpdatedDate

	// no validation rules for CkycReferenceId

	if len(errors) > 0 {
		return SearchResponsePidDataMultiError(errors)
	}

	return nil
}

// SearchResponsePidDataMultiError is an error wrapping multiple validation
// errors returned by SearchResponsePidData.ValidateAll() if the designated
// constraints aren't met.
type SearchResponsePidDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SearchResponsePidDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SearchResponsePidDataMultiError) AllErrors() []error { return m }

// SearchResponsePidDataValidationError is the validation error returned by
// SearchResponsePidData.Validate if the designated constraints aren't met.
type SearchResponsePidDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SearchResponsePidDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SearchResponsePidDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SearchResponsePidDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SearchResponsePidDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SearchResponsePidDataValidationError) ErrorName() string {
	return "SearchResponsePidDataValidationError"
}

// Error satisfies the builtin error interface
func (e SearchResponsePidDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSearchResponsePidData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SearchResponsePidDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SearchResponsePidDataValidationError{}

// Validate checks the field values on GetDataRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDataRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDataRequestMultiError,
// or nil if none found.
func (m *GetDataRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for ServiceAccessId

	// no validation rules for ServiceAccessCode

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPidData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataRequestValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataRequestValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPidData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataRequestValidationError{
				field:  "PidData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for MobileNumber

	if len(errors) > 0 {
		return GetDataRequestMultiError(errors)
	}

	return nil
}

// GetDataRequestMultiError is an error wrapping multiple validation errors
// returned by GetDataRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDataRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataRequestMultiError) AllErrors() []error { return m }

// GetDataRequestValidationError is the validation error returned by
// GetDataRequest.Validate if the designated constraints aren't met.
type GetDataRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataRequestValidationError) ErrorName() string { return "GetDataRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetDataRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataRequestValidationError{}

// Validate checks the field values on VerifyOTPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOTPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOTPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOTPRequestMultiError, or nil if none found.
func (m *VerifyOTPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOTPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SenderCode

	// no validation rules for ServiceAccessId

	// no validation rules for ServiceAccessCode

	// no validation rules for RequestId

	if all {
		switch v := interface{}(m.GetPidData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOTPRequestValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOTPRequestValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPidData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOTPRequestValidationError{
				field:  "PidData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VerifyOTPRequestMultiError(errors)
	}

	return nil
}

// VerifyOTPRequestMultiError is an error wrapping multiple validation errors
// returned by VerifyOTPRequest.ValidateAll() if the designated constraints
// aren't met.
type VerifyOTPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOTPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOTPRequestMultiError) AllErrors() []error { return m }

// VerifyOTPRequestValidationError is the validation error returned by
// VerifyOTPRequest.Validate if the designated constraints aren't met.
type VerifyOTPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOTPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOTPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOTPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOTPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOTPRequestValidationError) ErrorName() string { return "VerifyOTPRequestValidationError" }

// Error satisfies the builtin error interface
func (e VerifyOTPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOTPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOTPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOTPRequestValidationError{}

// Validate checks the field values on VerifyOTPRequestPidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VerifyOTPRequestPidData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOTPRequestPidData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOTPRequestPidDataMultiError, or nil if none found.
func (m *VerifyOTPRequestPidData) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOTPRequestPidData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OtpRequestId

	// no validation rules for Otp

	// no validation rules for ValidateOtp

	if len(errors) > 0 {
		return VerifyOTPRequestPidDataMultiError(errors)
	}

	return nil
}

// VerifyOTPRequestPidDataMultiError is an error wrapping multiple validation
// errors returned by VerifyOTPRequestPidData.ValidateAll() if the designated
// constraints aren't met.
type VerifyOTPRequestPidDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOTPRequestPidDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOTPRequestPidDataMultiError) AllErrors() []error { return m }

// VerifyOTPRequestPidDataValidationError is the validation error returned by
// VerifyOTPRequestPidData.Validate if the designated constraints aren't met.
type VerifyOTPRequestPidDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOTPRequestPidDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOTPRequestPidDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOTPRequestPidDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOTPRequestPidDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOTPRequestPidDataValidationError) ErrorName() string {
	return "VerifyOTPRequestPidDataValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOTPRequestPidDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOTPRequestPidData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOTPRequestPidDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOTPRequestPidDataValidationError{}

// Validate checks the field values on DownloadRequestPidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadRequestPidData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadRequestPidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadRequestPidDataMultiError, or nil if none found.
func (m *DownloadRequestPidData) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadRequestPidData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CkycNo

	// no validation rules for AuthFactor

	// no validation rules for AuthFactorType

	// no validation rules for DownloadStatus

	if len(errors) > 0 {
		return DownloadRequestPidDataMultiError(errors)
	}

	return nil
}

// DownloadRequestPidDataMultiError is an error wrapping multiple validation
// errors returned by DownloadRequestPidData.ValidateAll() if the designated
// constraints aren't met.
type DownloadRequestPidDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadRequestPidDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadRequestPidDataMultiError) AllErrors() []error { return m }

// DownloadRequestPidDataValidationError is the validation error returned by
// DownloadRequestPidData.Validate if the designated constraints aren't met.
type DownloadRequestPidDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadRequestPidDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadRequestPidDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadRequestPidDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadRequestPidDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadRequestPidDataValidationError) ErrorName() string {
	return "DownloadRequestPidDataValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadRequestPidDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadRequestPidData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadRequestPidDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadRequestPidDataValidationError{}

// Validate checks the field values on GetDataResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDataResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataResponseMultiError, or nil if none found.
func (m *GetDataResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCkycInquiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataResponseValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataResponseValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycInquiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataResponseValidationError{
				field:  "CkycInquiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for Reason

	if len(errors) > 0 {
		return GetDataResponseMultiError(errors)
	}

	return nil
}

// GetDataResponseMultiError is an error wrapping multiple validation errors
// returned by GetDataResponse.ValidateAll() if the designated constraints
// aren't met.
type GetDataResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataResponseMultiError) AllErrors() []error { return m }

// GetDataResponseValidationError is the validation error returned by
// GetDataResponse.Validate if the designated constraints aren't met.
type GetDataResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataResponseValidationError) ErrorName() string { return "GetDataResponseValidationError" }

// Error satisfies the builtin error interface
func (e GetDataResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataResponseValidationError{}

// Validate checks the field values on VerifyOTPResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VerifyOTPResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VerifyOTPResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VerifyOTPResponseMultiError, or nil if none found.
func (m *VerifyOTPResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *VerifyOTPResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCkycInquiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VerifyOTPResponseValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VerifyOTPResponseValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycInquiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VerifyOTPResponseValidationError{
				field:  "CkycInquiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for Reason

	if len(errors) > 0 {
		return VerifyOTPResponseMultiError(errors)
	}

	return nil
}

// VerifyOTPResponseMultiError is an error wrapping multiple validation errors
// returned by VerifyOTPResponse.ValidateAll() if the designated constraints
// aren't met.
type VerifyOTPResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VerifyOTPResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VerifyOTPResponseMultiError) AllErrors() []error { return m }

// VerifyOTPResponseValidationError is the validation error returned by
// VerifyOTPResponse.Validate if the designated constraints aren't met.
type VerifyOTPResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VerifyOTPResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VerifyOTPResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VerifyOTPResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VerifyOTPResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VerifyOTPResponseValidationError) ErrorName() string {
	return "VerifyOTPResponseValidationError"
}

// Error satisfies the builtin error interface
func (e VerifyOTPResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVerifyOTPResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VerifyOTPResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VerifyOTPResponseValidationError{}

// Validate checks the field values on GetDataResponseV3 with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetDataResponseV3) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDataResponseV3 with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDataResponseV3MultiError, or nil if none found.
func (m *GetDataResponseV3) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDataResponseV3) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCkycInquiry()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, GetDataResponseV3ValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, GetDataResponseV3ValidationError{
					field:  "CkycInquiry",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCkycInquiry()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return GetDataResponseV3ValidationError{
				field:  "CkycInquiry",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for RequestId

	// no validation rules for ResponseCode

	// no validation rules for Reason

	if len(errors) > 0 {
		return GetDataResponseV3MultiError(errors)
	}

	return nil
}

// GetDataResponseV3MultiError is an error wrapping multiple validation errors
// returned by GetDataResponseV3.ValidateAll() if the designated constraints
// aren't met.
type GetDataResponseV3MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDataResponseV3MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDataResponseV3MultiError) AllErrors() []error { return m }

// GetDataResponseV3ValidationError is the validation error returned by
// GetDataResponseV3.Validate if the designated constraints aren't met.
type GetDataResponseV3ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDataResponseV3ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDataResponseV3ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDataResponseV3ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDataResponseV3ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDataResponseV3ValidationError) ErrorName() string {
	return "GetDataResponseV3ValidationError"
}

// Error satisfies the builtin error interface
func (e GetDataResponseV3ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDataResponseV3.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDataResponseV3ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDataResponseV3ValidationError{}

// Validate checks the field values on CkycInqV3 with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CkycInqV3) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CkycInqV3 with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CkycInqV3MultiError, or nil
// if none found.
func (m *CkycInqV3) ValidateAll() error {
	return m.validate(true)
}

func (m *CkycInqV3) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for DownloadStatus

	// no validation rules for OtpRequestId

	// no validation rules for Message

	if len(errors) > 0 {
		return CkycInqV3MultiError(errors)
	}

	return nil
}

// CkycInqV3MultiError is an error wrapping multiple validation errors returned
// by CkycInqV3.ValidateAll() if the designated constraints aren't met.
type CkycInqV3MultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CkycInqV3MultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CkycInqV3MultiError) AllErrors() []error { return m }

// CkycInqV3ValidationError is the validation error returned by
// CkycInqV3.Validate if the designated constraints aren't met.
type CkycInqV3ValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CkycInqV3ValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CkycInqV3ValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CkycInqV3ValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CkycInqV3ValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CkycInqV3ValidationError) ErrorName() string { return "CkycInqV3ValidationError" }

// Error satisfies the builtin error interface
func (e CkycInqV3ValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCkycInqV3.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CkycInqV3ValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CkycInqV3ValidationError{}

// Validate checks the field values on CkycInq with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CkycInq) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CkycInq with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in CkycInqMultiError, or nil if none found.
func (m *CkycInq) ValidateAll() error {
	return m.validate(true)
}

func (m *CkycInq) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPidData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CkycInqValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CkycInqValidationError{
					field:  "PidData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPidData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CkycInqValidationError{
				field:  "PidData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for ErrorMessage

	if len(errors) > 0 {
		return CkycInqMultiError(errors)
	}

	return nil
}

// CkycInqMultiError is an error wrapping multiple validation errors returned
// by CkycInq.ValidateAll() if the designated constraints aren't met.
type CkycInqMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CkycInqMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CkycInqMultiError) AllErrors() []error { return m }

// CkycInqValidationError is the validation error returned by CkycInq.Validate
// if the designated constraints aren't met.
type CkycInqValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CkycInqValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CkycInqValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CkycInqValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CkycInqValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CkycInqValidationError) ErrorName() string { return "CkycInqValidationError" }

// Error satisfies the builtin error interface
func (e CkycInqValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCkycInq.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CkycInqValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CkycInqValidationError{}

// Validate checks the field values on DownloadResponsePidData with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DownloadResponsePidData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DownloadResponsePidData with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DownloadResponsePidDataMultiError, or nil if none found.
func (m *DownloadResponsePidData) ValidateAll() error {
	return m.validate(true)
}

func (m *DownloadResponsePidData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetPersonalDetailsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "PersonalDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "PersonalDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetPersonalDetailsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadResponsePidDataValidationError{
				field:  "PersonalDetailsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetIdentityDetailsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "IdentityDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "IdentityDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetIdentityDetailsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadResponsePidDataValidationError{
				field:  "IdentityDetailsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetRelatedPersonDetailsData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "RelatedPersonDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "RelatedPersonDetailsData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRelatedPersonDetailsData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadResponsePidDataValidationError{
				field:  "RelatedPersonDetailsData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetImageDetails()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "ImageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DownloadResponsePidDataValidationError{
					field:  "ImageDetails",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetImageDetails()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DownloadResponsePidDataValidationError{
				field:  "ImageDetails",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DownloadResponsePidDataMultiError(errors)
	}

	return nil
}

// DownloadResponsePidDataMultiError is an error wrapping multiple validation
// errors returned by DownloadResponsePidData.ValidateAll() if the designated
// constraints aren't met.
type DownloadResponsePidDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DownloadResponsePidDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DownloadResponsePidDataMultiError) AllErrors() []error { return m }

// DownloadResponsePidDataValidationError is the validation error returned by
// DownloadResponsePidData.Validate if the designated constraints aren't met.
type DownloadResponsePidDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DownloadResponsePidDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DownloadResponsePidDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DownloadResponsePidDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DownloadResponsePidDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DownloadResponsePidDataValidationError) ErrorName() string {
	return "DownloadResponsePidDataValidationError"
}

// Error satisfies the builtin error interface
func (e DownloadResponsePidDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDownloadResponsePidData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DownloadResponsePidDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DownloadResponsePidDataValidationError{}

// Validate checks the field values on PersonalDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *PersonalDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on PersonalDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// PersonalDetailsMultiError, or nil if none found.
func (m *PersonalDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *PersonalDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AccType

	// no validation rules for CkycNo

	// no validation rules for Prefix

	// no validation rules for Fname

	// no validation rules for Mname

	// no validation rules for Lname

	// no validation rules for MaidenPrefix

	// no validation rules for MaidenFname

	// no validation rules for MaidenMname

	// no validation rules for MaidenLname

	// no validation rules for FatherspouseFlag

	// no validation rules for FatherPrefix

	// no validation rules for FatherFname

	// no validation rules for FatherMname

	// no validation rules for FatherLname

	// no validation rules for MotherPrefix

	// no validation rules for MotherFname

	// no validation rules for MotherMname

	// no validation rules for MotherLname

	// no validation rules for Gender

	// no validation rules for MartialStatus

	// no validation rules for Nationality

	// no validation rules for Occupation

	// no validation rules for Dob

	// no validation rules for ResiStatus

	// no validation rules for JuriFlag

	// no validation rules for JuriResi

	// no validation rules for TaxNum

	// no validation rules for BirthCountry

	// no validation rules for BirthPlace

	// no validation rules for PermType

	// no validation rules for PermLine1

	// no validation rules for PermLine2

	// no validation rules for PermLine3

	// no validation rules for PermCity

	// no validation rules for PermDist

	// no validation rules for PermState

	// no validation rules for PermCountry

	// no validation rules for PermPin

	// no validation rules for PermPoa

	// no validation rules for PermPoaothers

	// no validation rules for PermCorresSameflag

	// no validation rules for CorresLine1

	// no validation rules for CorresLine2

	// no validation rules for CorresLine3

	// no validation rules for CorresCity

	// no validation rules for CorresDist

	// no validation rules for CorresState

	// no validation rules for CorresCountry

	// no validation rules for CorresPin

	// no validation rules for JuriSameFlag

	// no validation rules for JuriLine1

	// no validation rules for JuriLine2

	// no validation rules for JuriLine3

	// no validation rules for JuriCity

	// no validation rules for JuriState

	// no validation rules for JuriCountry

	// no validation rules for JuriPin

	// no validation rules for ResiStdCode

	// no validation rules for ResiTelNum

	// no validation rules for OffStdCode

	// no validation rules for OffTelNum

	// no validation rules for MobCode

	// no validation rules for MobNum

	// no validation rules for FaxCode

	// no validation rules for FaxNo

	// no validation rules for Email

	// no validation rules for Remarks

	// no validation rules for DecDate

	// no validation rules for DecPlace

	// no validation rules for KycDate

	// no validation rules for DocSub

	// no validation rules for KycName

	// no validation rules for KycDesignation

	// no validation rules for KycBranch

	// no validation rules for KycEmpcode

	// no validation rules for OrgName

	// no validation rules for OrgCode

	// no validation rules for NumIdentity

	// no validation rules for NumRelated

	// no validation rules for NumImages

	// no validation rules for Pan

	if len(errors) > 0 {
		return PersonalDetailsMultiError(errors)
	}

	return nil
}

// PersonalDetailsMultiError is an error wrapping multiple validation errors
// returned by PersonalDetails.ValidateAll() if the designated constraints
// aren't met.
type PersonalDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m PersonalDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m PersonalDetailsMultiError) AllErrors() []error { return m }

// PersonalDetailsValidationError is the validation error returned by
// PersonalDetails.Validate if the designated constraints aren't met.
type PersonalDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e PersonalDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e PersonalDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e PersonalDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e PersonalDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e PersonalDetailsValidationError) ErrorName() string { return "PersonalDetailsValidationError" }

// Error satisfies the builtin error interface
func (e PersonalDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sPersonalDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = PersonalDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = PersonalDetailsValidationError{}

// Validate checks the field values on IdentityDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *IdentityDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IdentityDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// IdentityDetailsMultiError, or nil if none found.
func (m *IdentityDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *IdentityDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIdentity() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, IdentityDetailsValidationError{
						field:  fmt.Sprintf("Identity[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, IdentityDetailsValidationError{
						field:  fmt.Sprintf("Identity[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return IdentityDetailsValidationError{
					field:  fmt.Sprintf("Identity[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return IdentityDetailsMultiError(errors)
	}

	return nil
}

// IdentityDetailsMultiError is an error wrapping multiple validation errors
// returned by IdentityDetails.ValidateAll() if the designated constraints
// aren't met.
type IdentityDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdentityDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdentityDetailsMultiError) AllErrors() []error { return m }

// IdentityDetailsValidationError is the validation error returned by
// IdentityDetails.Validate if the designated constraints aren't met.
type IdentityDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdentityDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdentityDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdentityDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdentityDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdentityDetailsValidationError) ErrorName() string { return "IdentityDetailsValidationError" }

// Error satisfies the builtin error interface
func (e IdentityDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdentityDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdentityDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdentityDetailsValidationError{}

// Validate checks the field values on Identity with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Identity) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Identity with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IdentityMultiError, or nil
// if none found.
func (m *Identity) ValidateAll() error {
	return m.validate(true)
}

func (m *Identity) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SequenceNo

	// no validation rules for IdentType

	// no validation rules for IdentNum

	// no validation rules for IdExpiryDate

	// no validation rules for IdProofSubmitted

	// no validation rules for IdverStatus

	if len(errors) > 0 {
		return IdentityMultiError(errors)
	}

	return nil
}

// IdentityMultiError is an error wrapping multiple validation errors returned
// by Identity.ValidateAll() if the designated constraints aren't met.
type IdentityMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IdentityMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IdentityMultiError) AllErrors() []error { return m }

// IdentityValidationError is the validation error returned by
// Identity.Validate if the designated constraints aren't met.
type IdentityValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IdentityValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IdentityValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IdentityValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IdentityValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IdentityValidationError) ErrorName() string { return "IdentityValidationError" }

// Error satisfies the builtin error interface
func (e IdentityValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIdentity.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IdentityValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IdentityValidationError{}

// Validate checks the field values on RelatedPersonDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RelatedPersonDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelatedPersonDetails with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RelatedPersonDetailsMultiError, or nil if none found.
func (m *RelatedPersonDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *RelatedPersonDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetRelatedPersonData() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, RelatedPersonDetailsValidationError{
						field:  fmt.Sprintf("RelatedPersonData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, RelatedPersonDetailsValidationError{
						field:  fmt.Sprintf("RelatedPersonData[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return RelatedPersonDetailsValidationError{
					field:  fmt.Sprintf("RelatedPersonData[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return RelatedPersonDetailsMultiError(errors)
	}

	return nil
}

// RelatedPersonDetailsMultiError is an error wrapping multiple validation
// errors returned by RelatedPersonDetails.ValidateAll() if the designated
// constraints aren't met.
type RelatedPersonDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelatedPersonDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelatedPersonDetailsMultiError) AllErrors() []error { return m }

// RelatedPersonDetailsValidationError is the validation error returned by
// RelatedPersonDetails.Validate if the designated constraints aren't met.
type RelatedPersonDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelatedPersonDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelatedPersonDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelatedPersonDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelatedPersonDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelatedPersonDetailsValidationError) ErrorName() string {
	return "RelatedPersonDetailsValidationError"
}

// Error satisfies the builtin error interface
func (e RelatedPersonDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelatedPersonDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelatedPersonDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelatedPersonDetailsValidationError{}

// Validate checks the field values on RelatedPerson with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *RelatedPerson) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RelatedPerson with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in RelatedPersonMultiError, or
// nil if none found.
func (m *RelatedPerson) ValidateAll() error {
	return m.validate(true)
}

func (m *RelatedPerson) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SequenceNo

	// no validation rules for RelType

	// no validation rules for CkycNo

	// no validation rules for Prefix

	// no validation rules for Fname

	// no validation rules for Mname

	// no validation rules for Lname

	// no validation rules for Pan

	// no validation rules for Uid

	// no validation rules for Voterid

	// no validation rules for Nrega

	// no validation rules for Passport

	// no validation rules for PassportExp

	// no validation rules for DrivingLicence

	// no validation rules for DrivingExp

	// no validation rules for OtheridName

	// no validation rules for OtheridNo

	// no validation rules for DecDate

	// no validation rules for DecPlace

	// no validation rules for KycDate

	// no validation rules for DocSub

	// no validation rules for KycName

	// no validation rules for KycDesignation

	// no validation rules for KycBranch

	// no validation rules for KycEmpcode

	// no validation rules for OrcName

	// no validation rules for OrgCode

	if len(errors) > 0 {
		return RelatedPersonMultiError(errors)
	}

	return nil
}

// RelatedPersonMultiError is an error wrapping multiple validation errors
// returned by RelatedPerson.ValidateAll() if the designated constraints
// aren't met.
type RelatedPersonMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RelatedPersonMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RelatedPersonMultiError) AllErrors() []error { return m }

// RelatedPersonValidationError is the validation error returned by
// RelatedPerson.Validate if the designated constraints aren't met.
type RelatedPersonValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RelatedPersonValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RelatedPersonValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RelatedPersonValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RelatedPersonValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RelatedPersonValidationError) ErrorName() string { return "RelatedPersonValidationError" }

// Error satisfies the builtin error interface
func (e RelatedPersonValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRelatedPerson.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RelatedPersonValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RelatedPersonValidationError{}

// Validate checks the field values on ImageDetails with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ImageDetails) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ImageDetails with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ImageDetailsMultiError, or
// nil if none found.
func (m *ImageDetails) ValidateAll() error {
	return m.validate(true)
}

func (m *ImageDetails) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetImage() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ImageDetailsValidationError{
						field:  fmt.Sprintf("Image[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ImageDetailsValidationError{
						field:  fmt.Sprintf("Image[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ImageDetailsValidationError{
					field:  fmt.Sprintf("Image[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ImageDetailsMultiError(errors)
	}

	return nil
}

// ImageDetailsMultiError is an error wrapping multiple validation errors
// returned by ImageDetails.ValidateAll() if the designated constraints aren't met.
type ImageDetailsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageDetailsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageDetailsMultiError) AllErrors() []error { return m }

// ImageDetailsValidationError is the validation error returned by
// ImageDetails.Validate if the designated constraints aren't met.
type ImageDetailsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageDetailsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageDetailsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageDetailsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageDetailsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageDetailsValidationError) ErrorName() string { return "ImageDetailsValidationError" }

// Error satisfies the builtin error interface
func (e ImageDetailsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImageDetails.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageDetailsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageDetailsValidationError{}

// Validate checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Image) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Image with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ImageMultiError, or nil if none found.
func (m *Image) ValidateAll() error {
	return m.validate(true)
}

func (m *Image) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SequenceNo

	// no validation rules for ImageType

	// no validation rules for ImageCode

	// no validation rules for ImageData

	if len(errors) > 0 {
		return ImageMultiError(errors)
	}

	return nil
}

// ImageMultiError is an error wrapping multiple validation errors returned by
// Image.ValidateAll() if the designated constraints aren't met.
type ImageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ImageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ImageMultiError) AllErrors() []error { return m }

// ImageValidationError is the validation error returned by Image.Validate if
// the designated constraints aren't met.
type ImageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ImageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ImageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ImageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ImageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ImageValidationError) ErrorName() string { return "ImageValidationError" }

// Error satisfies the builtin error interface
func (e ImageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sImage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ImageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ImageValidationError{}
