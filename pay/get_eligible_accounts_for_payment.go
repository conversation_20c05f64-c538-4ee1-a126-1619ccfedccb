package pay

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strings"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/mask"
	upiPkg "github.com/epifi/gamma/pkg/upi"

	accountspb "github.com/epifi/gamma/api/accounts"
	payPb "github.com/epifi/gamma/api/pay"
	payAccountsPb "github.com/epifi/gamma/api/pay/accounts"
	payEnumsPb "github.com/epifi/gamma/api/pay/enums"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	accountPb "github.com/epifi/gamma/api/typesv2/account"
	upiOnboarding "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnums "github.com/epifi/gamma/api/upi/onboarding/enums"
	"github.com/epifi/gamma/pkg/feature/release"
	payPkg "github.com/epifi/gamma/pkg/pay"
)

const oldVpaHandle = "fbl"

var (
	errorVpaNotFound     = errors.New("no vpa found")
	accountPreferenceMap = map[upiOnboardingEnums.UpiAccountPreference]payAccountsPb.AccountInfo_AccountPreference{
		upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY:                   payAccountsPb.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
		upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS: payAccountsPb.AccountInfo_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
	}

	// map from entry point to feature. Mainly getting used to use release evaluator which is coupled to a feature
	entryPointToFeature = map[payPb.EligibleAccountsUIEntryPoint]typesPb.Feature{
		payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD: typesPb.Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS,
	}

	// temporary list to check if tpap account id should be included in derived account id for internal accounts
	entryPointsToUseTpapInDerivedAccountId = []payPb.EligibleAccountsUIEntryPoint{
		payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD,
	}
)

func (s *Service) GetEligibleAccountsForPayment(ctx context.Context, req *payPb.GetEligibleAccountsForPaymentRequest) (*payPb.GetEligibleAccountsForPaymentResponse, error) {
	var (
		res              = &payPb.GetEligibleAccountsForPaymentResponse{}
		internalAccounts []*savingsPb.Account
		err              error
		accountInfos     = make([]*payAccountsPb.AccountInfo, 0)
		accInfoErr       error
	)
	// fetching list of tpap accounts
	tpapAccountsRes, tpapAccountsErr := s.upiOnboardingClient.GetAccounts(ctx, &upiOnboarding.GetAccountsRequest{
		ActorId: req.GetActorId(),
		AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
			upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
			upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
		},
	})
	if err = epifigrpc.RPCError(tpapAccountsRes, tpapAccountsErr); err != nil && !tpapAccountsRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "error in calling GetAccounts RPC", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		res.Status = rpc.StatusFromError(err)
		return res, nil
	}

	isUserFiLite := s.isFiLiteUser(ctx, req.GetActorId())
	// fetching internal account only for non fil lite user
	if !isUserFiLite {
		internalAccounts, err = s.getInternalAccounts(ctx, req.GetActorId())
		if err != nil {
			logger.Error(ctx, "error fetching internal accounts", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				res.Status = rpc.StatusRecordNotFound()
			}
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	// in case tpap is not enabled for the given entry point, then only the fi savings account will
	// be shown, In the event that the savings account does not exist, then an empty list will be returned.
	if !s.isTpapEnabledForEntrypoint(ctx, req.GetEligibleAccountsUiEntryPoint(), isUserFiLite, req.GetActorId()) {
		if len(internalAccounts) != 0 {
			for _, internalAccount := range internalAccounts {
				var tpapAcc *upiOnboarding.UpiAccount
				// TODO(akk): remove or extend this logic to other use cases after checking further. restricting it to this list to reduce unexpected impact for other use cases
				if lo.Contains(entryPointsToUseTpapInDerivedAccountId, req.GetEligibleAccountsUiEntryPoint()) {
					tpapAcc = s.findTpapAccountForInternalAccount(internalAccount, tpapAccountsRes.GetAccounts())
				}
				internalPayAcc, payAccErr := s.getPayAccountInfoFromInternalAccount(ctx, internalAccount, tpapAcc)
				if payAccErr != nil {
					logger.Error(ctx, "error in fetching account info for internal fi account ", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(payAccErr))
					res.Status = rpc.StatusInternal()
					return res, nil
				}
				accountInfos = append(accountInfos, internalPayAcc)
			}
		}
		res.AccountInfos = accountInfos
		res.Status = rpc.StatusOk()
		return res, nil
	}

	// In case of Autopay, we only need to show those banks which supports autopay, hence we filter those tpap accounts.
	if req.GetEligibleAccountsUiEntryPoint() == payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_RECURRING_PAYMENT {
		// TODO : (Ashutosh to remove these debug logs in 2-3 days)
		logger.Debug(ctx, "eligible accounts before filtering", zap.Any("eligibleAccountsBeforeFiltering", tpapAccountsRes.GetAccounts()))
		tpapAccountsRes.Accounts = s.filterTPAPAccountsWhichSupportsAutopay(ctx, req.GetActorId(), tpapAccountsRes.GetAccounts())
		logger.Debug(ctx, "eligible accounts before filtering", zap.Any("eligibleAccountsAfterFiltering", tpapAccountsRes.GetAccounts()))
	}

	accountInfos, accInfoErr = s.getEligibleAccountsInfo(ctx, tpapAccountsRes.GetAccounts(), internalAccounts, req.GetPaymentScope(), req.GetSecondActorId(), req.GetEligibleAccountsUiEntryPoint(), req.GetPayeePiId())
	if accInfoErr != nil {
		logger.Error(ctx, "error fetching account info", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(accInfoErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	res.AccountInfos = accountInfos
	res.Status = rpc.StatusOk()
	return res, nil
}

// isTpapEnabledForEntrypoint checks config to identify whether tpap is currently enabled for the given entrypoint
// or not. In case we dont have a config entry for the given entrypoint, true will be returned to ensure backward
// compatibility
func (s *Service) isTpapEnabledForEntrypoint(ctx context.Context, entryPoint payPb.EligibleAccountsUIEntryPoint, isNonSaUser bool, actorId string) bool {
	var (
		evalRes bool
		err     error
	)
	tpapFeature, ok := entryPointToFeature[entryPoint]
	if !ok {
		return true
	}
	if isNonSaUser {
		evalRes, err = s.tpapForNonSaUserEvaluator.Evaluate(ctx, release.NewCommonConstraintData(tpapFeature).WithActorId(actorId))
	} else {
		evalRes, err = s.tpapForSaUserEvaluator.Evaluate(ctx, release.NewCommonConstraintData(tpapFeature).WithActorId(actorId))
	}
	if err != nil {
		logger.Debug(ctx, "error in release config evaluation", zap.Error(err))
		return false
	}
	return evalRes
}

// nolint:funlen
// getEligibleAccountsInfo converts all account(tpap, internal) infos into pay account Info
func (s *Service) getEligibleAccountsInfo(ctx context.Context, tpapAccounts []*upiOnboarding.UpiAccount, internalAccounts []*savingsPb.Account,
	paymentScope payEnumsPb.PaymentScope, secondActorId string, entryPoint payPb.EligibleAccountsUIEntryPoint, payeePiId string) ([]*payAccountsPb.AccountInfo, error) {
	var (
		eligibleAccounts              []*payAccountsPb.AccountInfo
		derivedAccountId              string
		vpa                           string
		accountIdToFetchVpa           string
		isInternalAccountPresent      bool
		err                           error
		primaryAccount                *payAccountsPb.AccountInfo
		defaultMerchantPaymentAccount *payAccountsPb.AccountInfo
		defaultAccount                *payAccountsPb.AccountInfo
	)
	for _, tpapAccount := range tpapAccounts {
		var (
			accountInfo = &payAccountsPb.AccountInfo{}
		)

		accountIdToFetchVpa = tpapAccount.GetId()
		if tpapAccount.IsInternal() {
			isInternalAccountPresent = true
			accountIdToFetchVpa = tpapAccount.GetAccountRefId()
		}

		if paymentScope == payEnumsPb.PaymentScope_PAYMENT_SCOPE_INTERNATIONAL && !isInternationalPaymentActivated(tpapAccount) {
			continue
		}

		// we do not show credit card tpap accounts as eligible account in case of recurring payments creation and self qr screen
		if tpapAccount.GetAccountType() == accountspb.Type_CREDIT {
			isSecondActorMerchant, procErr := s.isSecondActorMerchant(ctx, secondActorId)
			if procErr != nil {
				// if we face error while checking if second actor is merchant we do not return error, as we fallback to other account as preferred
				logger.Error(ctx, "error checking if second actor is merchant or not", zap.Error(err))
				continue
			}

			if entryPoint == payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_SELF_QR ||
				entryPoint == payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_RECURRING_PAYMENT || !isSecondActorMerchant {
				continue
			}

			// In case when Merchant is not accepting RuPay CC, we do not show RuPay CC account
			if s.isAccountRestrictedForPayee(ctx, payeePiId) {
				continue
			}
		}

		if tpapAccount.IsInternal() {
			derivedAccountId, err = payPkg.GetEncodedDerivedAccountId(tpapAccount.GetAccountRefId(), tpapAccount.GetId(), "")
			if err != nil {
				return nil, fmt.Errorf("failed to fetch derivedAccountId for accountIds: %w", err)
			}
		} else {
			derivedAccountId, err = payPkg.GetEncodedDerivedAccountId("", tpapAccount.GetId(), "")
			if err != nil {
				return nil, fmt.Errorf("failed to fetch derivedAccountId for accountIds: %w", err)
			}
		}

		vpa, err = s.getVpaByAccountId(ctx, accountIdToFetchVpa, tpapAccount.GetAccountType(), []paymentinstrument.PaymentInstrumentState{
			paymentinstrument.PaymentInstrumentState_CREATED,
			paymentinstrument.PaymentInstrumentState_VERIFIED,
			// For non-Fi lite users, internal account always exists
			// but for Fi lite users, there might be a case when
			// user doesn't see any account, when all vpas are disabled
			// So we will show the account, Let user pay and get enableVpa
			// cta and activate.
			// TODO (yatin, Saurabh): Improve this flow by showing some
			// enableVpa cta in front of accounts to activate the vpa.
			paymentinstrument.PaymentInstrumentState_SUSPENDED,
		})

		switch {
		case err != nil && !errors.Is(err, errorVpaNotFound):
			return nil, fmt.Errorf("failed to fetch vpa for the accountId: %s, err:%w", accountIdToFetchVpa, err)
		case errors.Is(err, errorVpaNotFound) && !tpapAccount.IsInternal():
			continue
		default:
			// we want to show internal account
			// as an eligible account, even if
			// vpa is not found for the account
			// because payment is still possible
			// using other payment protocols in
			// case upi is not feasible.
		}

		accountInfo = convertTpapAccountInfoToPayAccountInfo(tpapAccount, vpa, derivedAccountId)
		switch {
		case accountInfo.GetAccountPreference() == payAccountsPb.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY:
			// isAccountPrimary basically tells that one of tpap accounts is primary.
			primaryAccount = accountInfo
		case accountInfo.GetAccountPreference() == payAccountsPb.AccountInfo_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS:
			defaultMerchantPaymentAccount = accountInfo
		case defaultAccount == nil || isAccountInternalWithHigherAPO(tpapAccount.GetAccountRefId(), internalAccounts, defaultAccount):
			// isAccountInternalWithHigherAPO basically tells that current tpap account is one of the internal accounts.
			// In case of multiple accounts it will return true if current one is having higher priority on basis of APO.
			defaultAccount = accountInfo
		}

		eligibleAccounts = append(eligibleAccounts, accountInfo)
	}

	if !isInternalAccountPresent && len(internalAccounts) != 0 {
		internalAccountInfoList := make([]*payAccountsPb.AccountInfo, 0)
		for _, internalAccount := range internalAccounts {
			// TODO(akk): Need to revisit this logic to see if tpap account should be sent if it is linked to internal account
			internalAccountInfo, err := s.getPayAccountInfoFromInternalAccount(ctx, internalAccount, nil)
			if err != nil {
				return nil, fmt.Errorf("failed to convert internal account infos into pay account infos: %w", err)
			}
			internalAccountInfoList = append(internalAccountInfoList, internalAccountInfo)
		}
		if len(internalAccountInfoList) != 0 {
			// Sort the list directly within this function
			sort.Slice(internalAccountInfoList, func(first int, second int) bool {
				// Sort by AccountProductOffering field
				return payPkg.ApoPriorityMap[internalAccountInfoList[first].GetAccountProductOffering()] < payPkg.ApoPriorityMap[internalAccountInfoList[second].GetAccountProductOffering()]
			})
			return internalAccountInfoList, nil
		}
	}

	return s.setEligibleAccountsSequence(ctx, secondActorId, eligibleAccounts, primaryAccount, defaultMerchantPaymentAccount, defaultAccount), nil
}

// isAccountRestrictedForPayee checks if the account is restricted for the given payee
// We are using this to check to restrict Rupay CC account for merchants which do not accept Rupay CC. [It can be extended in future for other account types]
func (s *Service) isAccountRestrictedForPayee(ctx context.Context, payeePiId string) bool {
	piTo, piErr := s.piProcessor.GetPiById(ctx, payeePiId)
	if piErr != nil {
		logger.Error(ctx, "error fetching PiTo for payeePiId", zap.String(logger.PI_TO, payeePiId), zap.Error(piErr))
		return true
	}

	// some MCCs are restricted for RuPay CC Account Type, we can early return in those cases.
	if lo.Contains(s.conf.RupayCCRestrictedMCCs, piTo.GetUpi().GetMerchantDetails().GetMcc()) {
		return true
	}

	merchantVpa := piTo.GetUpi().GetVpa()
	if merchantVpa == "" {
		// In case of empty merchantVpa, we are not sure if merchant supports Rupaay CC or not
		return true
	}

	isRuPayAccepted, isRuPayErr := payPkg.IsRuPayCCAcceptedForMerchant(ctx, s.upiClient, merchantVpa, nil) // amount is nil as we don't have context of amount
	if isRuPayErr != nil {
		logger.Error(ctx, "Error in checking if RuPay CC is accepted for merchant", zap.String(logger.PI_TO, payeePiId), zap.Error(isRuPayErr))
		// In case of any error in checking if RuPay CC is accepted for merchant, we are not sure if merchant supports RuPay CC or not
		// Hence we return true to restrict the account type (RuPay CC) to be shown as eligible account
		return true
	}
	return !isRuPayAccepted
}

// isAccountInternalWithHigherAPO basically tells that current tpap account is one of the internal accounts.
// In case of multiple accounts it will return true if current one is having higher priority on basis of APO.
func isAccountInternalWithHigherAPO(accountRefId string, internalAccounts []*savingsPb.Account, currentDefaultAccount *payAccountsPb.AccountInfo) bool {
	for _, internalAccount := range internalAccounts {
		if internalAccount.GetId() == accountRefId {
			internalAccountAPO := internalAccount.GetSkuInfo().GetAccountProductOffering()
			currentDefaultAccountAPO := currentDefaultAccount.GetAccountProductOffering()
			// Check if internal account has higher priority than the current default account
			// Note: In the map, lower values represent higher priorities, so we use '<' to check if priority of current account is higher than currentDefaultAccount.
			if payPkg.ApoPriorityMap[internalAccountAPO] < payPkg.ApoPriorityMap[currentDefaultAccountAPO] {
				return true
			}
		}
	}
	return false
}

// getPayAccountInfoFromInternalAccount converts internal account infos into pay account Info
func (s *Service) getPayAccountInfoFromInternalAccount(ctx context.Context, internalAccount *savingsPb.Account, tpapAccount *upiOnboarding.UpiAccount) (*payAccountsPb.AccountInfo, error) {
	derivedAccountIdString, err := payPkg.GetEncodedDerivedAccountId(internalAccount.GetId(), tpapAccount.GetId(), "")
	if err != nil {
		return nil, fmt.Errorf("failed to fetch derivedAccountId for the internal accountId: %s, err:%w", internalAccount.GetId(), err)
	}

	vpa, err := s.getVpaByAccountId(ctx, internalAccount.GetId(), accountspb.Type_SAVINGS, []paymentinstrument.PaymentInstrumentState{
		paymentinstrument.PaymentInstrumentState_CREATED,
		paymentinstrument.PaymentInstrumentState_VERIFIED,
		paymentinstrument.PaymentInstrumentState_SUSPENDED,
	})
	if err != nil {
		if errors.Is(err, errorVpaNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to fetch vpa for the internal accountId: %s, err:%w", internalAccount.GetId(), err)
	}

	internalAccountInfo := convertInternalAccountInfoToPayAccountInfo(internalAccount, vpa, derivedAccountIdString, (*s.conf.VendorToNameMap)[internalAccount.GetPartnerBank().String()])
	internalAccountInfo.AccountPreference = payAccountsPb.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY

	// tpapAccountStatus is used to determine if account needs to be activated
	internalAccountInfo.UpiAccountStatus = tpapAccount.GetStatus()

	// For users with old vpa handle, since we don't have tpap account, we are sending Upi Account Status as Unspecified, which is not handled at Client side.
	// Hence, for those cases we will be deciding status based on UPI PI state.
	if tpapAccount == nil {
		hasActiveUpiPi, activePisErr := s.hasActiveUpiPiForOldVpa(ctx, internalAccount)
		// TODO(Ashutosh) - remove the log line after analysing the data.
		logger.Info(ctx, "user is not having any tpap account mapped to internal account",
			zap.String(logger.ACTOR_ID_V2, internalAccount.GetActorId()), zap.Bool("hasActiveUpiPi", hasActiveUpiPi), zap.Error(activePisErr))
		switch {
		case activePisErr != nil:
			internalAccountInfo.UpiAccountStatus = upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_UNSPECIFIED
		case hasActiveUpiPi:
			internalAccountInfo.UpiAccountStatus = upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE
		default:
			internalAccountInfo.UpiAccountStatus = upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE
		}
	}
	return internalAccountInfo, nil
}

func convertInternalAccountInfoToPayAccountInfo(internalAccount *savingsPb.Account, vpa, derivedAccountId, bankName string) *payAccountsPb.AccountInfo {
	return &payAccountsPb.AccountInfo{
		MaskedAccountNumber:    mask.GetMaskedAccountNumber(internalAccount.GetAccountNo(), ""),
		IfscCode:               internalAccount.GetIfscCode(),
		DerivedAccountId:       derivedAccountId,
		AccountType:            accountspb.Type_SAVINGS,
		Vpa:                    vpa,
		BankName:               bankName,
		AccountProductOffering: internalAccount.GetSkuInfo().GetAccountProductOffering(),
	}
}

func convertTpapAccountInfoToPayAccountInfo(tpapAccount *upiOnboarding.UpiAccount, vpa string, derivedAccountId string) *payAccountsPb.AccountInfo {
	res := &payAccountsPb.AccountInfo{
		MaskedAccountNumber:    mask.GetMaskedAccountNumber(tpapAccount.GetMaskedAccountNumber(), ""),
		IfscCode:               tpapAccount.GetIfscCode(),
		DerivedAccountId:       derivedAccountId,
		AccountType:            tpapAccount.GetAccountType(),
		Vpa:                    vpa,
		BankName:               tpapAccount.GetBankName(),
		UpiAccountStatus:       tpapAccount.GetStatus(),
		AccountProductOffering: tpapAccount.GetApo(),
	}
	res.AccountPreference = accountPreferenceMap[tpapAccount.GetAccountPreference()]
	return res
}

// getInternalAccount fetches the internal account for the given actor.
func (s *Service) getInternalAccount(ctx context.Context, actorId string) (*savingsPb.Account, error) {
	internalAccount, err := s.savingsProcessor.GetSavingsAccountForActor(ctx, actorId)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch internal account for the given actor:%s ,err:%w", actorId, err)
	}

	return internalAccount, nil
}

func (s *Service) getInternalAccounts(ctx context.Context, actorId string) ([]*savingsPb.Account, error) {
	acctListResp, acctErr := s.savingsClient.GetAccountsList(ctx, &savingsPb.GetAccountsListRequest{
		Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
			BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
				ActorIds:                []string{actorId},
				AccountProductOfferings: []accountPb.AccountProductOffering{accountPb.AccountProductOffering_APO_REGULAR, accountPb.AccountProductOffering_APO_NRE, accountPb.AccountProductOffering_APO_NRO},
				PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
			},
		},
	})
	if rpcErr := epifigrpc.RPCError(acctListResp, acctErr); rpcErr != nil {
		if acctListResp.GetStatus().IsRecordNotFound() {
			return nil, fmt.Errorf("account does not exist for actorId %s %w", actorId, epifierrors.ErrRecordNotFound)
		}
		return nil, fmt.Errorf("error fetching savings accounts list for actorId %s %w", actorId, rpcErr)
	}
	return acctListResp.GetAccounts(), nil
}

// getVpaByAccountId fetches the vpa for the given accountId.
// It returns one of the vpa among all the vpas for given accountId which are in CREATED AND VERIFIED STATE.
func (s *Service) getVpaByAccountId(ctx context.Context, accountId string, accountType accountspb.Type, piStates []paymentinstrument.PaymentInstrumentState) (string, error) {
	piByAccountIdResp, piByAccountIdErr := s.acPiClient.GetPiByAccountId(ctx, &accountPiPb.GetPiByAccountIdRequest{
		AccountId:   accountId,
		AccountType: accountType,
		PiTypes: []paymentinstrument.PaymentInstrumentType{
			paymentinstrument.PaymentInstrumentType_UPI,
		},
		PiStates: piStates,
	})
	if err := epifigrpc.RPCError(piByAccountIdResp, piByAccountIdErr); err != nil {
		if piByAccountIdResp.GetStatus().IsRecordNotFound() {
			return "", errorVpaNotFound
		}
		return "", fmt.Errorf("failed to fetch pi by accountId: %w", err)
	}

	pis := piByAccountIdResp.GetPaymentInstruments()
	if len(pis) < 1 {
		return "", errorVpaNotFound
	}

	for _, pi := range pis {
		if !pi.IsMandateVPA() {
			return pi.GetUpi().GetVpa(), nil
		}
	}

	logger.Info(ctx, "all pis found for given accountId are of type mandate", zap.String(logger.ACCOUNT_ID, accountId))
	return "", errorVpaNotFound
}

// isInternationalPaymentActivated - checks if international payments is enabled for given upi account
func isInternationalPaymentActivated(upiAccount *upiOnboarding.UpiAccount) bool {
	for _, upiControl := range upiAccount.GetUpiControls() {
		if upiControl == upiOnboardingEnums.UpiControl_UPI_CONTROL_INTERNATIONAL_PAYMENTS {
			return true
		}
	}

	return false
}

// setEligibleAccountsSequence - sets the list of eligible accounts in sequence based on type of transaction and preferences
// If second actor is merchant, default merchant payment account is set on top of order
// Else - primary account is set on top of order
// In case we do not find both, internal fi account is set on top of order
func (s *Service) setEligibleAccountsSequence(ctx context.Context, secondActorId string, eligibleAccountList []*payAccountsPb.AccountInfo, primaryAccount, defaultMerchantPaymentAccount, defaultAccount *payAccountsPb.AccountInfo) []*payAccountsPb.AccountInfo {
	var (
		sequencedEligibleAccounts []*payAccountsPb.AccountInfo
		preferredAccount          *payAccountsPb.AccountInfo
	)
	isSecondActorMerchant, err := s.isSecondActorMerchant(ctx, secondActorId)
	if err != nil {
		// if we face error while checking if second actor is merchant we do not return error, as we fallback to other account as preferred
		logger.Error(ctx, "error checking if second actor is merchant or not", zap.Error(err))
	}

	switch {
	case isSecondActorMerchant && defaultMerchantPaymentAccount != nil:
		preferredAccount = defaultMerchantPaymentAccount
	case primaryAccount != nil:
		preferredAccount = primaryAccount
	default:
		preferredAccount = defaultAccount
	}

	// if no preferred account is found we return the same eligible accounts list
	if preferredAccount == nil {
		return eligibleAccountList
	}

	sequencedEligibleAccounts = append(sequencedEligibleAccounts, preferredAccount)

	for _, eligibleAccount := range eligibleAccountList {
		if eligibleAccount.GetDerivedAccountId() != preferredAccount.GetDerivedAccountId() {
			sequencedEligibleAccounts = append(sequencedEligibleAccounts, eligibleAccount)
		}
	}

	return sequencedEligibleAccounts
}

// isSecondActorMerchant - checks if second actor is a merchant or not
func (s *Service) isSecondActorMerchant(ctx context.Context, secondActorId string) (bool, error) {
	secondActor, err := s.actorProcessor.GetActorById(ctx, secondActorId)
	if err != nil {
		return false, fmt.Errorf("error while fetching actor for given actor id: %s %w", secondActorId, err)
	}
	return secondActor.GetType() == typesPb.Actor_EXTERNAL_MERCHANT, nil
}

// isFiLiteUser - checks if user is a file lite user or not
func (s *Service) isFiLiteUser(ctx context.Context, actorId string) bool {
	getSavingsAccountEssentialsRes, err := s.savingsClient.GetSavingsAccountEssentials(ctx, &savingsPb.GetSavingsAccountEssentialsRequest{
		Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
			ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
				ActorId:     actorId,
				PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
			},
		},
	})
	switch {
	case err != nil:
		logger.Error(ctx, "error while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	case getSavingsAccountEssentialsRes.GetStatus().IsRecordNotFound():
		return true
	case !getSavingsAccountEssentialsRes.GetStatus().IsSuccess():
		logger.Error(ctx, "non-success code while fetching savings account essentials for the actor", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return false
	}

	return false
}

func (s *Service) findTpapAccountForInternalAccount(internalAccount *savingsPb.Account, tpapAccounts []*upiOnboarding.UpiAccount) *upiOnboarding.UpiAccount {
	for _, tpapAccount := range tpapAccounts {
		if tpapAccount.GetAccountRefId() == internalAccount.GetId() {
			return tpapAccount
		}
	}
	return nil
}

// filterTPAPAccountsWhichSupportsAutopay returns the list of tpap accounts for the user which supports autopay
// i.e. mandate is supported by the bank
func (s *Service) filterTPAPAccountsWhichSupportsAutopay(ctx context.Context, actorId string, tpapAccounts []*upiOnboarding.UpiAccount) []*upiOnboarding.UpiAccount {
	accountProvidersListRes, err := s.upiProcessor.ListAccountProviders(ctx, &upiOnboarding.ListAccountProvidersRequest{
		ActorId:        actorId,
		UpiAccountType: upiOnboardingEnums.UpiAccountType_UPI_ACCOUNT_TYPE_BANK_ACCOUNT,
	})
	if err != nil {
		logger.Error(ctx, "error getting account providers list", zap.String(logger.ACTOR_ID_V2, actorId), zap.Error(err))
		return tpapAccounts
	}
	accountProvidersBankInfoList := accountProvidersListRes.GetBankInfos()
	var eligibleAccounts []*upiOnboarding.UpiAccount
	for _, account := range tpapAccounts {
		for _, bankInfo := range accountProvidersBankInfoList {
			if strings.HasPrefix(account.GetIfscCode(), bankInfo.GetIfscCode()) && isMandateSupported(bankInfo) {
				logger.Debug(ctx, "mandate supported by the bank", zap.String("bankName", bankInfo.GetName()), zap.String("ifscCode", bankInfo.GetIfscCode()))
				eligibleAccounts = append(eligibleAccounts, account)
				break
			}
		}
	}
	return eligibleAccounts
}

// isManadateSupported checks if mandate is supported by the bank by checking the supported feature list
func isMandateSupported(bankInfo *upiOnboarding.BankInfo) bool {
	featureSupportedList := bankInfo.GetSupportedUpiFeatures()
	if lo.Contains(featureSupportedList, upiOnboarding.BankInfo_UPI_FEATURE_MANDATE) {
		return true
	}
	return false
}

// hasActiveUpiPiForOldVpa checks if there is any active UPI payment instruments for the old VPA.
func (s *Service) hasActiveUpiPiForOldVpa(ctx context.Context, internalAccount *savingsPb.Account) (bool, error) {
	piByAccountIdResp, piByAccountIdErr := s.acPiClient.GetPiByAccountId(ctx, &accountPiPb.GetPiByAccountIdRequest{
		AccountId: internalAccount.GetId(),
		PiStates: []paymentinstrument.PaymentInstrumentState{
			paymentinstrument.PaymentInstrumentState_CREATED,
			paymentinstrument.PaymentInstrumentState_VERIFIED,
		},
		PiTypes: []paymentinstrument.PaymentInstrumentType{
			paymentinstrument.PaymentInstrumentType_UPI,
		},
	})
	if te := epifigrpc.RPCError(piByAccountIdResp, piByAccountIdErr); te != nil {
		logger.Error(ctx, "failed to fetch pis for given account id", zap.String(logger.ACCOUNT_ID, internalAccount.GetId()), zap.Error(te))
		return false, te
	}
	for _, pi := range piByAccountIdResp.GetPaymentInstruments() {
		vpaHandleForInternalAccount, err := upiPkg.GetVpaHandleFromVpa(pi.GetUpi().GetVpa())
		if err != nil {
			logger.Error(ctx, "error in fetching vpa handle from vpa", zap.String(logger.ACCOUNT_ID, internalAccount.GetId()), zap.Error(err))
			continue
		} else if vpaHandleForInternalAccount == oldVpaHandle {
			return true, nil
		}
	}
	return false, nil
}
