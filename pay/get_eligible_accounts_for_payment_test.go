package pay_test

import (
	commontypes "github.com/epifi/be-common/api/typesv2/common"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	upiPb "github.com/epifi/gamma/api/upi"
	upiEnumsPb "github.com/epifi/gamma/api/upi/enums"

	"context"
	"errors"
	"testing"

	"github.com/golang/protobuf/proto"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epificontext"
	accounts2 "github.com/epifi/gamma/api/accounts"
	payPb "github.com/epifi/gamma/api/pay"
	"github.com/epifi/gamma/api/pay/accounts"
	"github.com/epifi/gamma/api/paymentinstrument"
	accountPiPb "github.com/epifi/gamma/api/paymentinstrument/account_pi"
	savingsPb "github.com/epifi/gamma/api/savings"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	upiOnboardingPb "github.com/epifi/gamma/api/upi/onboarding"
	upiOnboardingEnums "github.com/epifi/gamma/api/upi/onboarding/enums"
	onboardingPb "github.com/epifi/gamma/api/user/onboarding"
	"github.com/epifi/gamma/pkg/feature/release"
)

var (
	fixture1 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e991",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1234",
		AccountRefNumber:    "11223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		IfscCode:            "ifsc-1",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
	}

	fixture1WithRegularAPO = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e991",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1234",
		AccountRefNumber:    "11223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		IfscCode:            "ifsc-1",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_REGULAR,
	}

	fixture2 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e992",
		ActorId:             "actor-id-1",
		AccountRefId:        "FI12346",
		MaskedAccountNumber: "xxxxxxx1233",
		AccountRefNumber:    "12223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED,
		IfscCode:            "ifsc-2",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
	}

	fixture2WithRegularAPO = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e992",
		ActorId:             "actor-id-1",
		AccountRefId:        "FI12346",
		MaskedAccountNumber: "xxxxxxx1233",
		AccountRefNumber:    "12223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED,
		IfscCode:            "ifsc-2",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_REGULAR,
	}

	ccFixture = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e927",
		ActorId:             "actor-id-1",
		MaskedAccountNumber: "xxxxxxx1233",
		AccountRefNumber:    "12223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
		IfscCode:            "ifsc-2",
		AccountType:         accounts2.Type_CREDIT,
		Apo:                 account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
	}

	fixture3 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e993",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1234",
		AccountRefNumber:    "11223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		IfscCode:            "ifsc-1",
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
	}
	fixture4 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e997",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1239",
		AccountRefNumber:    "11223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		IfscCode:            "ifsc-1",
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
	}
	fixture5 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e992",
		ActorId:             "actor-id-1",
		AccountRefId:        "FI12345",
		MaskedAccountNumber: "xxxxxxx1233",
		AccountRefNumber:    "12223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED,
		IfscCode:            "ifsc-1",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_NRO,
	}
	fixture6 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e993",
		ActorId:             "actor-id-1",
		AccountRefId:        "FI12346",
		MaskedAccountNumber: "xxxxxxx1234",
		AccountRefNumber:    "11224",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		IfscCode:            "ifsc-2",
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED,
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_NRE,
	}
	fixture5WithPrimaryPreference = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e992",
		ActorId:             "actor-id-1",
		AccountRefId:        "FI12345",
		MaskedAccountNumber: "xxxxxxx1233",
		AccountRefNumber:    "12223",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_NOT_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		IfscCode:            "ifsc-1",
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_NRO,
	}
	fixture7 = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e997",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1237",
		AccountRefNumber:    "11227",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		IfscCode:            "ifsc-7",
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_UNSPECIFIED,
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_REGULAR,
	}
	fixture7WithPrimaryPreference = &upiOnboardingPb.UpiAccount{
		Id:                  "a5004f18-5d52-4991-82a9-2a1e5010e997",
		ActorId:             "actor-id-1",
		AccountRefId:        "",
		MaskedAccountNumber: "xxxxxxx1237",
		AccountRefNumber:    "11227",
		PinSetStatus:        upiOnboardingEnums.UpiPinSetStatus_UPI_PIN_SET_STATUS_PIN_SET,
		Status:              upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
		IfscCode:            "ifsc-7",
		AccountPreference:   upiOnboardingEnums.UpiAccountPreference_ACCOUNT_PREFERENCE_PRIMARY,
		AccountType:         accounts2.Type_SAVINGS,
		Apo:                 account.AccountProductOffering_APO_REGULAR,
	}
)

func TestService_GetEligibleAccountsForPayment(t *testing.T) {

	type mockGetTpapAccounts struct {
		enable bool
		req    *upiOnboardingPb.GetAccountsRequest
		res    *upiOnboardingPb.GetAccountsResponse
		err    error
	}

	type mockGetInternalAccounts struct {
		enable bool
		req    *savingsPb.GetAccountsListRequest
		want   *savingsPb.GetAccountsListResponse
		err    error
	}

	type mockGetInternalAccount struct {
		enable  bool
		actorId string
		want    *savingsPb.Account
		err     error
	}

	type mockGetPiByAccountId struct {
		enable bool
		req    *accountPiPb.GetPiByAccountIdRequest
		res    *accountPiPb.GetPiByAccountIdResponse
		err    error
	}

	type mockGetActorById struct {
		enable  bool
		actorId string
		actor   *typesPb.Actor
		times   int
		err     error
	}

	type mockNonSaUserTpapEvaluate struct {
		enable      bool
		tpapFeature typesPb.Feature
		actorId     string
		evauareRes  bool
	}

	type mockSaUserTpapEvaluate struct {
		enable      bool
		tpapFeature typesPb.Feature
		actorId     string
		evauateRes  bool
	}

	type mockGetFeatureDetails struct {
		enable bool
		req    *onboardingPb.GetFeatureDetailsRequest
		res    *onboardingPb.GetFeatureDetailsResponse
		err    error
	}

	type mockGetSavingsAccountEssentials struct {
		enable bool
		req    *savingsPb.GetSavingsAccountEssentialsRequest
		want   *savingsPb.GetSavingsAccountEssentialsResponse
		err    error
	}

	type mockGetPiById struct {
		enable bool
		req    string
		res    *paymentinstrument.PaymentInstrument
		err    error
	}

	type mockGetVpaMerchantInfo struct {
		enable bool
		req    *upiPb.GetVpaMerchantInfoRequest
		res    *upiPb.GetVpaMerchantInfoResponse
		err    error
	}

	tests := []struct {
		name                            string
		req                             *payPb.GetEligibleAccountsForPaymentRequest
		want                            *payPb.GetEligibleAccountsForPaymentResponse
		mockGetTpapAccounts             mockGetTpapAccounts
		mockGetInternalAccount          mockGetInternalAccount
		mockGetInternalAccounts         mockGetInternalAccounts
		mockGetPiByAccountIdForAccount1 []mockGetPiByAccountId
		mockGetActorById                mockGetActorById
		mockGetFeatureDetails           mockGetFeatureDetails
		mockGetSavingsAccountEssentials mockGetSavingsAccountEssentials
		mockNonSaUserEvaluate           mockNonSaUserTpapEvaluate
		mockSaUserEvaluate              mockSaUserTpapEvaluate
		mockGetPiById                   mockGetPiById
		mockGetVpaMerchantInfo          mockGetVpaMerchantInfo
		wantErr                         bool
	}{
		{
			name: "successfully fetch eligible accounts when internal account was only account",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal@fbl",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId: "FI12346",
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal@fbl",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ2",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal@fbl",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with internal account present in tpap accounts - 1",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture3,
						fixture2,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e993",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with internal account present in tpap accounts - 2",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with internal account present in tpap accounts and having APO as REGULAR - 1",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1WithRegularAPO,
						fixture2WithRegularAPO,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with internal account and no tpap accounts",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status:   rpc.StatusRecordNotFound(),
					Accounts: nil,
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal@fbl",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId: "FI12346",
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal@fbl",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ2",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal@fbl",
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "failed to fetch eligible accounts as got internal error while fetching tpap accounts",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusInternal(),
				},
				err: nil,
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "failed to fetch eligible accounts as error while fetching internal account for actor",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: nil,
				err:  errors.New("error fetching internal account"),
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status:       rpc.StatusInternal(),
				AccountInfos: nil,
			},
		},
		{
			name: "failed to fetch eligible accounts as failed to fetch vpa for given Tpap account",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
					},
				},
				err: nil,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status:             rpc.StatusInternal(),
						PaymentInstruments: nil,
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "failed to fetch fetch eligible accounts as error while fetching vpa for internal account",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusInternal(),
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusInternal(),
			},
		},
		{
			name: "successfully fetch eligible accounts for merchant payments when credit account is linked and merchant accepts all account types",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
				PayeePiId:     "payee_pi_id",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e927",
						AccountType: accounts2.Type_CREDIT,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-2",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "payee_pi_id",
				res: &paymentinstrument.PaymentInstrument{
					Identifier: &paymentinstrument.PaymentInstrument_Upi{
						Upi: &paymentinstrument.Upi{
							Vpa: "vpa-merchant@oksbi",
						},
					},
				},
				err: nil,
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "vpa-merchant@oksbi",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						Vpa:                          "vpa-merchant@oksbi",
						MerchantDetails:              nil,
						RestrictedAccountTypeDetails: &upiPb.RestrictedAccountTypeDetails{},
					},
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-2",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5Mjc=",
						AccountType:            accounts2.Type_CREDIT,
						Vpa:                    "vpa-account-2",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts but not credit card account for merchant payments when credit account is linked but merchant has restricted MCC",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
				PayeePiId:     "payee_pi_id",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "payee_pi_id",
				res: &paymentinstrument.PaymentInstrument{
					Identifier: &paymentinstrument.PaymentInstrument_Upi{
						Upi: &paymentinstrument.Upi{
							Vpa: "vpa-merchant@oksbi",
							// restricted MCC
							MerchantDetails: &paymentinstrument.Upi_MerchantDetails{
								Mcc: "7322",
							},
						},
					},
				},
				err: nil,
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts but not credit card account for merchant payments when credit account is linked but merchant do not accepts CC UPI",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
				PayeePiId:     "payee_pi_id",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "payee_pi_id",
				res: &paymentinstrument.PaymentInstrument{
					Identifier: &paymentinstrument.PaymentInstrument_Upi{
						Upi: &paymentinstrument.Upi{
							Vpa: "vpa-merchant@oksbi",
						},
					},
				},
				err: nil,
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "vpa-merchant@oksbi",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						Vpa:             "vpa-merchant@oksbi",
						MerchantDetails: nil,
						RestrictedAccountTypeDetails: &upiPb.RestrictedAccountTypeDetails{
							FeatureSupportedValues: "05",
							RestrictedAccountTypes: []accounts2.Type{accounts2.Type_CREDIT},
						},
					},
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts along with credit card account for merchant payments when credit account is linked but merchant do not accepts CC UPI, with exemption upto INR 2000",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
				PayeePiId:     "payee_pi_id",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e927",
						AccountType: accounts2.Type_CREDIT,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-2",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "payee_pi_id",
				res: &paymentinstrument.PaymentInstrument{
					Identifier: &paymentinstrument.PaymentInstrument_Upi{
						Upi: &paymentinstrument.Upi{
							Vpa: "vpa-merchant@oksbi",
						},
					},
				},
				err: nil,
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "vpa-merchant@oksbi",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						Vpa:             "vpa-merchant@oksbi",
						MerchantDetails: nil,
						RestrictedAccountTypeDetails: &upiPb.RestrictedAccountTypeDetails{
							FeatureSupportedValues:          "05|11",
							RestrictedAccountTypes:          []accounts2.Type{accounts2.Type_CREDIT},
							UpiAccountRestrictionExemptions: []upiEnumsPb.UpiAccountRestrictionExemptionType{upiEnumsPb.UpiAccountRestrictionExemptionType_UPI_ACCOUNT_RESTRICTION_EXEMPTION_TYPE_RUPAY_CREDIT_CARD},
						},
					},
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-2",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5Mjc=",
						AccountType:            accounts2.Type_CREDIT,
						Vpa:                    "vpa-account-2",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			// This test case is to test the scenario where the RestrictedAccountTypesDetails is not populated yet [Transition phase]
			name: "successfully fetch eligible accounts excluding credit card account for merchant payments when credit account is linked but RestrictedAccountTypesDetails is not populated yet",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
				PayeePiId:     "payee_pi_id",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "payee_pi_id",
				res: &paymentinstrument.PaymentInstrument{
					Identifier: &paymentinstrument.PaymentInstrument_Upi{
						Upi: &paymentinstrument.Upi{
							Vpa: "vpa-merchant@oksbi",
						},
					},
				},
				err: nil,
			},
			mockGetVpaMerchantInfo: mockGetVpaMerchantInfo{
				enable: true,
				req: &upiPb.GetVpaMerchantInfoRequest{
					Vpa: "vpa-merchant@oksbi",
				},
				res: &upiPb.GetVpaMerchantInfoResponse{
					Status: rpc.StatusOk(),
					VpaMerchantInfo: &upiPb.VpaMerchantInfo{
						Vpa:             "vpa-merchant@oksbi",
						MerchantDetails: nil,
					},
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			// This test case is to test the scenario where the Client is not sending Payee_Pi_Id [Older Clients]
			name: "successfully fetch eligible accounts but not credit card account for merchant payments when credit account is linked but we can't fetch VPAMerchantInfo since client do not send Payee_pi_id",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture1,
						fixture2,
						ccFixture,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_EXTERNAL_MERCHANT,
				},
				times: 2,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e991",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetPiById: mockGetPiById{
				enable: true,
				req:    "",
				err:    epifierrors.ErrRecordNotFound,
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTE=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber: "xxxxxxx1233",
						IfscCode:            "ifsc-2",
						DerivedAccountId:    "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:         accounts2.Type_SAVINGS,
						Vpa:                 "vpa-internal",
						UpiAccountStatus:    upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts when internal account was not present - fi lite user",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture3,
						fixture4,
					},
				},
				err: nil,
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e993",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e997",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-23",
									},
								},
								State: paymentinstrument.PaymentInstrumentState_SUSPENDED,
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1239",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTc=",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-23",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
				},
			},
		},
		{
			name: "getting tpap accounts for a savings account user for a given entrypoint",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:                      "actor-id-3",
				SecondActorId:                "actor-id-2",
				PaymentScope:                 0,
				EligibleAccountsUiEntryPoint: payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD,
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "CgdGSTEyMzQ2",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-2",
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-3",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture3,
						fixture4,
					},
				},
				err: nil,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-3"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e993",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e997",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-2",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId: "FI12346",
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-2",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetFeatureDetails: mockGetFeatureDetails{},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-3",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockSaUserEvaluate: mockSaUserTpapEvaluate{
				enable:      true,
				tpapFeature: typesPb.Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS,
				actorId:     "actor-id-3",
				evauateRes:  true,
			},
			wantErr: false,
		},
		{
			name: "getting tpap accounts for a non savings account user for a given entrypoint",
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:                      "actor-id-3",
				SecondActorId:                "actor-id-2",
				PaymentScope:                 0,
				EligibleAccountsUiEntryPoint: payPb.EligibleAccountsUIEntryPoint_ELIGIBLE_ACCOUNTS_UI_ENTRY_POINT_CREDIT_CARD,
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1239",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTc=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-1",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-1",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
					},
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-3",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture3,
						fixture4,
					},
				},
				err: nil,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: false,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-3"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e993",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-1",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e997",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			mockGetFeatureDetails: mockGetFeatureDetails{},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-3",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
			},
			mockNonSaUserEvaluate: mockNonSaUserTpapEvaluate{
				enable:      true,
				tpapFeature: typesPb.Feature_FEATURE_CREDIT_CARD_TPAP_PAYMENTS,
				actorId:     "actor-id-3",
				evauareRes:  true,
			},
			wantErr: false,
		},
		{
			name: "successfully fetch eligible accounts when multiple internal accounts were only accounts present",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId: "actor-id-1",
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusRecordNotFound(),
				},
				err: nil,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12345",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12345",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId: "FI12345",
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId: "FI12346",
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-2",
						AccountType:            accounts2.Type_SAVINGS,
						DerivedAccountId:       "CgdGSTEyMzQ2",
						Vpa:                    "vpa-internal-nre",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountProductOffering: account.AccountProductOffering_APO_NRE,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-1",
						AccountType:            accounts2.Type_SAVINGS,
						DerivedAccountId:       "CgdGSTEyMzQ1",
						Vpa:                    "vpa-internal-nro",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountProductOffering: account.AccountProductOffering_APO_NRO,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with multiple internal accounts and tpap accounts - 1",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture5,
						fixture6,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12345",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12345",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nre",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRE,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ1GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nro",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRO,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with multiple internal accounts and tpap accounts when one account has preference as Primary",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture5WithPrimaryPreference,
						fixture6,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12345",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12345",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ1GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nro",
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRO,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nre",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRE,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with multiple internal accounts and tpap accounts - 2",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture5,
						fixture6,
						fixture7,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12345",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:        "FI12346",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12345",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e997",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-regular",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nre",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRE,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ1GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nro",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRO,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1237",
						IfscCode:               "ifsc-7",
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTc=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-regular",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					},
				},
			},
		},
		{
			name: "successfully fetch eligible accounts with multiple internal accounts and tpap accounts - 2,  when one account has preference as Primary",
			req: &payPb.GetEligibleAccountsForPaymentRequest{
				ActorId:       "actor-id-1",
				SecondActorId: "actor-id-2",
			},
			mockGetTpapAccounts: mockGetTpapAccounts{
				enable: true,
				req: &upiOnboardingPb.GetAccountsRequest{
					ActorId: "actor-id-1",
					AccountStatus: []upiOnboardingEnums.UpiAccountStatus{
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_INACTIVE,
					},
				},
				res: &upiOnboardingPb.GetAccountsResponse{
					Status: rpc.StatusOk(),
					Accounts: []*upiOnboardingPb.UpiAccount{
						fixture5,
						fixture6,
						fixture7WithPrimaryPreference,
					},
				},
				err: nil,
			},
			mockGetSavingsAccountEssentials: mockGetSavingsAccountEssentials{
				enable: true,
				req: &savingsPb.GetSavingsAccountEssentialsRequest{
					Filter: &savingsPb.GetSavingsAccountEssentialsRequest_ActorIdBankFilter{
						ActorIdBankFilter: &savingsPb.ActorIdBankFilter{
							ActorId:     "actor-id-1",
							PartnerBank: commonvgpb.Vendor_FEDERAL_BANK,
						},
					},
				},
				want: &savingsPb.GetSavingsAccountEssentialsResponse{
					Status: rpc.StatusOk(),
				},
			},
			mockGetActorById: mockGetActorById{
				enable:  true,
				actorId: "actor-id-2",
				actor: &typesPb.Actor{
					Type: typesPb.Actor_USER,
				},
				times: 1,
			},
			mockGetInternalAccounts: mockGetInternalAccounts{
				enable: true,
				req: &savingsPb.GetAccountsListRequest{
					Identifier: &savingsPb.GetAccountsListRequest_BulkActorIdentifier{
						BulkActorIdentifier: &savingsPb.BulkActorIdentifier{
							ActorIds:                []string{"actor-id-1"},
							AccountProductOfferings: []account.AccountProductOffering{account.AccountProductOffering_APO_REGULAR, account.AccountProductOffering_APO_NRE, account.AccountProductOffering_APO_NRO},
							PartnerBanks:            []commonvgpb.Vendor{commonvgpb.Vendor_FEDERAL_BANK},
						},
					},
				},
				want: &savingsPb.GetAccountsListResponse{
					Status: rpc.StatusOk(),
					Accounts: []*savingsPb.Account{
						{
							Id:        "FI12345",
							AccountNo: "xxxxxxx1233",
							IfscCode:  "ifsc-1",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRO,
							},
						},
						{
							Id:        "FI12347",
							AccountNo: "xxxxxxx1234",
							IfscCode:  "ifsc-2",
							SkuInfo: &savingsPb.SKUInfo{
								AccountProductOffering: account.AccountProductOffering_APO_NRE,
							},
						},
					},
				},
				err: nil,
			},
			mockGetPiByAccountIdForAccount1: []mockGetPiByAccountId{
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12345",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nro",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "FI12346",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-internal-nre",
									},
								},
							},
						},
					},
					err: nil,
				},
				{
					enable: true,
					req: &accountPiPb.GetPiByAccountIdRequest{
						AccountId:   "a5004f18-5d52-4991-82a9-2a1e5010e997",
						AccountType: accounts2.Type_SAVINGS,
						PiTypes: []paymentinstrument.PaymentInstrumentType{
							paymentinstrument.PaymentInstrumentType_UPI,
						},
						PiStates: []paymentinstrument.PaymentInstrumentState{
							paymentinstrument.PaymentInstrumentState_CREATED,
							paymentinstrument.PaymentInstrumentState_VERIFIED,
							paymentinstrument.PaymentInstrumentState_SUSPENDED,
						},
					},
					res: &accountPiPb.GetPiByAccountIdResponse{
						Status: rpc.StatusOk(),
						PaymentInstruments: []*paymentinstrument.PaymentInstrument{
							{
								Identifier: &paymentinstrument.PaymentInstrument_Upi{
									Upi: &paymentinstrument.Upi{
										Vpa: "vpa-account-regular",
									},
								},
							},
						},
					},
					err: nil,
				},
			},
			want: &payPb.GetEligibleAccountsForPaymentResponse{
				Status: rpc.StatusOk(),
				AccountInfos: []*accounts.AccountInfo{
					{
						MaskedAccountNumber:    "xxxxxxx1237",
						IfscCode:               "ifsc-7",
						DerivedAccountId:       "GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTc=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-account-regular",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountPreference:      accounts.AccountInfo_ACCOUNT_PREFERENCE_PRIMARY,
						AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1233",
						IfscCode:               "ifsc-1",
						DerivedAccountId:       "CgdGSTEyMzQ1GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTI=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nro",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRO,
					},
					{
						MaskedAccountNumber:    "xxxxxxx1234",
						IfscCode:               "ifsc-2",
						DerivedAccountId:       "CgdGSTEyMzQ2GiRhNTAwNGYxOC01ZDUyLTQ5OTEtODJhOS0yYTFlNTAxMGU5OTM=",
						AccountType:            accounts2.Type_SAVINGS,
						Vpa:                    "vpa-internal-nre",
						UpiAccountStatus:       upiOnboardingEnums.UpiAccountStatus_UPI_ACCOUNT_STATUS_ACTIVE,
						AccountProductOffering: account.AccountProductOffering_APO_NRE,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc, md, deferFun := getPayServiceWithMock(t)
			defer deferFun()
			ctx := epificontext.CtxWithAppPlatform(context.Background(), commontypes.Platform_ANDROID)
			if tt.mockNonSaUserEvaluate.enable {
				md.tpapForNonSaUserEvaluator.EXPECT().Evaluate(ctx, release.NewCommonConstraintData(tt.mockNonSaUserEvaluate.tpapFeature).WithActorId(tt.mockNonSaUserEvaluate.actorId)).Return(tt.mockNonSaUserEvaluate.evauareRes, nil)
			}
			if tt.mockSaUserEvaluate.enable {
				md.tpapForSaUserEvaluator.EXPECT().Evaluate(ctx, release.NewCommonConstraintData(tt.mockSaUserEvaluate.tpapFeature).WithActorId(tt.mockSaUserEvaluate.actorId)).Return(tt.mockSaUserEvaluate.evauateRes, nil)
			}

			if tt.mockGetTpapAccounts.enable {
				md.mockUpiOnboardingClient.EXPECT().GetAccounts(ctx, tt.mockGetTpapAccounts.req).
					Return(tt.mockGetTpapAccounts.res, tt.mockGetTpapAccounts.err)
			}
			if tt.mockGetActorById.enable {
				md.mockActorProcessor.EXPECT().GetActorById(ctx, tt.mockGetActorById.actorId).Times(tt.mockGetActorById.times).
					Return(tt.mockGetActorById.actor, tt.mockGetActorById.err)
			}
			if tt.mockGetFeatureDetails.enable {
				md.mockOnboardingClient.EXPECT().GetFeatureDetails(ctx, tt.mockGetFeatureDetails.req).
					Return(tt.mockGetFeatureDetails.res, tt.mockGetFeatureDetails.err)
			}
			if tt.mockGetInternalAccount.enable {
				md.mockISavingProcessor.EXPECT().GetSavingsAccountForActor(ctx, tt.mockGetInternalAccount.actorId).
					Return(tt.mockGetInternalAccount.want, tt.mockGetInternalAccount.err)
			}
			if tt.mockGetInternalAccounts.enable {
				md.mockSavingsClient.EXPECT().GetAccountsList(ctx, tt.mockGetInternalAccounts.req).
					Return(tt.mockGetInternalAccounts.want, tt.mockGetInternalAccounts.err)
			}
			for _, mockGetPi := range tt.mockGetPiByAccountIdForAccount1 {
				if mockGetPi.enable {
					md.mockAccPiClient.EXPECT().GetPiByAccountId(ctx, mockGetPi.req).
						Return(mockGetPi.res, mockGetPi.err)
				}
			}

			if tt.mockGetSavingsAccountEssentials.enable {
				md.mockSavingsClient.EXPECT().GetSavingsAccountEssentials(ctx, tt.mockGetSavingsAccountEssentials.req).
					Return(tt.mockGetSavingsAccountEssentials.want, tt.mockGetSavingsAccountEssentials.err)
			}
			if tt.mockGetPiById.enable {
				md.mockIPiProcessor.EXPECT().GetPiById(ctx, tt.mockGetPiById.req).
					Return(tt.mockGetPiById.res, tt.mockGetPiById.err)
			}
			if tt.mockGetVpaMerchantInfo.enable {
				md.mockUpiClient.EXPECT().GetVpaMerchantInfo(ctx, tt.mockGetVpaMerchantInfo.req).
					Return(tt.mockGetVpaMerchantInfo.res, tt.mockGetVpaMerchantInfo.err)
			}
			got, err := svc.GetEligibleAccountsForPayment(ctx, tt.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetEligibleAccountsForPayment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if len(got.GetAccountInfos()) != len(tt.want.GetAccountInfos()) {
				t.Errorf("different number of accounts found:  gotLen = %v, wantLen %v", len(got.GetAccountInfos()), len(tt.want.GetAccountInfos()))
				return
			}

			if !proto.Equal(got, tt.want) {
				t.Errorf("GetEligibleAccountsForPayment() got = %v \n, want %v", got, tt.want)
			}
		})
	}
}
