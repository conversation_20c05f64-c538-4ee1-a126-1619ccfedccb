package tools

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/connected_account/enums"
	indianstocksPb "github.com/epifi/gamma/api/investment/indianstocks/frontend"
	"github.com/epifi/gamma/api/mcp/networth"
	"github.com/epifi/gamma/httpgw/config/genconf"
	connectedAccPkgMocks "github.com/epifi/gamma/pkg/connectedaccount/securities/mocks"
)

type fetchStockTransactionMocks struct {
	mockStockTransactionHandler *connectedAccPkgMocks.MockIMinimalStockTransactions
}

func initFetchStockTransactionMocks(ctrl *gomock.Controller) *fetchStockTransactionMocks {
	return &fetchStockTransactionMocks{
		mockStockTransactionHandler: connectedAccPkgMocks.NewMockIMinimalStockTransactions(ctrl),
	}
}

func TestNewFetchStockTransactionsHandler(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchStockTransactionMocks(ctl)

	handler := NewFetchStockTransactionsHandler(nil, nil, m.mockStockTransactionHandler)

	assert.NotNil(t, handler)
	assert.Equal(t, m.mockStockTransactionHandler, handler.stockTxnsHandler)
}

func TestNewFetchStockTransactionsHandler_GetTool(t *testing.T) {
	t.Parallel()
	ctl := gomock.NewController(t)
	defer ctl.Finish()
	m := initFetchStockTransactionMocks(ctl)
	genConf, err := genconf.Load()
	if err != nil {
		t.Errorf("failed to load dynamic config: %v", err)
	}
	handler := NewFetchStockTransactionsHandler(genConf, nil, m.mockStockTransactionHandler)
	tool := handler.GetTool()

	assert.Equal(t, "fetch_stock_transactions", tool.Name)
	assert.Equal(t, "Retrieve detailed indian stock transactions for all connected indian stock accounts", tool.Description)
}

func TestNewFetchStockTransactionsHandler_Handle(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name           string
		actorId        string
		setupMocks     func(m *fetchStockTransactionMocks)
		expectError    bool
		expectedResult *mcp.CallToolResult
	}{
		{
			name:           "Missing actor ID",
			actorId:        epificontext.UnknownId,
			setupMocks:     func(m *fetchStockTransactionMocks) {},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching stock transactions"),
		},
		{
			name:    "no indian stocks account, success",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchStockTransactionMocks) {
				m.mockStockTransactionHandler.EXPECT().GetMinimalStockTransactions(gomock.Any(), "test_actor_id", []enums.AccInstrumentType{
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
				}).Return(nil, epifierrors.ErrRecordNotFound)
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("{\"status\": \"not_found\", \"message\": \"no indian stocks account connected\"}"),
		},
		{
			name:    "GetMinimalStockTransactions failure",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchStockTransactionMocks) {
				m.mockStockTransactionHandler.EXPECT().GetMinimalStockTransactions(gomock.Any(), "test_actor_id", []enums.AccInstrumentType{
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
				}).Return(nil, errors.New("error"))
			},
			expectError:    false,
			expectedResult: mcp.NewToolResultText("something went wrong in fetching stock transactions"),
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchStockTransactionMocks(ctl)
			tt.setupMocks(m)
			genConf, err := genconf.Load()
			if err != nil {
				t.Errorf("failed to load dynamic config: %v", err)
			}
			handler := NewFetchStockTransactionsHandler(genConf, nil, m.mockStockTransactionHandler)

			ctx := createTestContext(tt.actorId)
			result, err := handler.Handle(ctx, mcp.CallToolRequest{})

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				assert.NotNil(t, result)
				if tt.expectedResult != nil {
					assert.Equal(t, tt.expectedResult, result)
				} else {
					// For success cases where expectedResult is nil, verify we got valid content
					assert.NotNil(t, result.Content)
					assert.NotEmpty(t, result.Content)

					// Check what type we actually got for debugging
					if len(result.Content) > 0 && result.Content[0] != nil {
						// The result should be text content (JSON is returned as text)
						textContent, ok := result.Content[0].(mcp.TextContent)
						assert.True(t, ok, "Expected text content, got: %T", result.Content[0])
						if ok {
							assert.NotEmpty(t, textContent.Text, "Expected non-empty text result")
							// For JSON results, verify it starts with { and ends with }
							if len(textContent.Text) > 0 && textContent.Text[0] == '{' {
								assert.True(t, len(textContent.Text) > 2 && textContent.Text[len(textContent.Text)-1] == '}', "Expected valid JSON format")
							}
						}
					} else {
						assert.Fail(t, "result.Content is empty or nil")
					}
				}
			}
		})
	}
}

func TestFetchStockTransactionsHandler_fetchStockTransactionsApiCall(t *testing.T) {
	logger.Init(cfg.TestEnv)
	t.Parallel()

	tests := []struct {
		name       string
		actorId    string
		setupMocks func(m *fetchStockTransactionMocks)
		wantErr    bool
	}{
		{
			name:    "Successful API call with all services returning data",
			actorId: "test_actor_id",
			setupMocks: func(m *fetchStockTransactionMocks) {
				m.mockStockTransactionHandler.EXPECT().GetMinimalStockTransactions(gomock.Any(), "test_actor_id", []enums.AccInstrumentType{
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
				}).Return(map[enums.AccInstrumentType][]*networth.MinimalStockTransaction{
					enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES: {
						{
							Isin:  "ISIN_1",
							Type:  indianstocksPb.TransactionType_TRANSACTION_TYPE_BUY,
							Units: 5,
						},
					},
				}, nil)
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			ctl := gomock.NewController(t)
			defer ctl.Finish()
			m := initFetchStockTransactionMocks(ctl)
			tt.setupMocks(m)
			genConf, err := genconf.Load()
			if err != nil {
				t.Errorf("failed to load dynamic config: %v", err)
			}
			handler := NewFetchStockTransactionsHandler(genConf, nil, m.mockStockTransactionHandler)
			_, err = handler.fetchStockTransactionsApiCall(context.Background(), tt.actorId)

			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
