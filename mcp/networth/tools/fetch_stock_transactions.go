package tools

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"

	"github.com/epifi/be-common/pkg/epifierrors"

	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/logger"

	"github.com/epifi/gamma/api/connected_account/enums"
	networthTools "github.com/epifi/gamma/api/mcp/networth"
	"github.com/epifi/gamma/httpgw/config/genconf"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
	connectedAccPkg "github.com/epifi/gamma/pkg/connectedaccount/securities"
	"github.com/epifi/gamma/pkg/obfuscator"
)

const (
	fetchStockTransactionsTool = "fetch_stock_transactions"
)

var (
	fetchStockTransactionsErrMsg = mcp.NewToolResultText("something went wrong in fetching stock transactions")
)

type FetchStockTransactionsHandler struct {
	config           *genconf.Config
	eventPublisher   *mcpEvents.EventPublisher
	stockTxnsHandler connectedAccPkg.IMinimalStockTransactions
}

func NewFetchStockTransactionsHandler(
	config *genconf.Config,
	eventPublisher *mcpEvents.EventPublisher,
	stockTxnsHandler connectedAccPkg.IMinimalStockTransactions,
) *FetchStockTransactionsHandler {
	return &FetchStockTransactionsHandler{
		config:           config,
		eventPublisher:   eventPublisher,
		stockTxnsHandler: stockTxnsHandler,
	}
}

// GetTool returns tool with name, description and other metadata which gets used by mcp host
func (t *FetchStockTransactionsHandler) GetTool() mcp.Tool {
	return mcp.NewTool(fetchStockTransactionsTool,
		mcp.WithDescription(t.config.NetworthMcpConfig().StocksTransactionToolDescription()),
	)
}

// Handle is the main handler call that gets called on each tool call
// nolint:dupl
func (t *FetchStockTransactionsHandler) Handle(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	sessionId := server.ClientSessionFromContext(ctx).SessionID()
	obfuscatedSessId := obfuscator.Hashed(sessionId)
	actorId := epificontext.ActorIdFromContext(ctx)
	if actorId == epificontext.UnknownId {
		logger.Error(ctx, "actorId is not populated for stock transactions handler", zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchStockTransactionsErrMsg, nil
	}
	logger.Debug(ctx, "Received fetch stock transactions call", zap.String(logger.SESSION_ID, obfuscatedSessId))

	stockTransactionRes, stockTxnErr := t.fetchStockTransactionsApiCall(ctx, actorId)
	if stockTxnErr != nil {
		logger.Error(ctx, "error in fetching stock transactions", zap.Error(stockTxnErr), zap.String(logger.ACTOR_ID_V2, actorId), zap.String(logger.SESSION_ID, obfuscatedSessId))
		return fetchStockTransactionsErrMsg, nil
	}

	// Instrument MCPChat event: indian stocks transactions tool used
	if t.eventPublisher != nil {
		t.eventPublisher.PublishChatEvent(ctx, actorId, sessionId, fetchStockTransactionsTool)
	}

	return mcp.NewToolResultText(stockTransactionRes), nil
}

func (t *FetchStockTransactionsHandler) fetchStockTransactionsApiCall(ctx context.Context, actorId string) (string, error) {
	minimalStockTransactionMap, stockTxnsErr := t.stockTxnsHandler.GetMinimalStockTransactions(ctx, actorId, []enums.AccInstrumentType{
		enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_EQUITIES,
		enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_ETF,
		enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_REIT,
		enums.AccInstrumentType_ACC_INSTRUMENT_TYPE_INVIT,
	})
	if stockTxnsErr != nil {
		if errors.Is(stockTxnsErr, epifierrors.ErrRecordNotFound) {
			return `{"status": "not_found", "message": "no indian stocks account connected"}`, nil
		}
		return "", stockTxnsErr
	}

	var minimalStockTransactions []*networthTools.MinimalStockTransaction
	for _, stockTxns := range minimalStockTransactionMap {
		minimalStockTransactions = append(minimalStockTransactions, stockTxns...)
	}

	llmRes := &networthTools.StockTransactionsResponse{
		SchemaDescription: t.config.NetworthMcpConfig().StockTransactionsResponseSchemaDesc(),
		StockTransactions: networthTools.GetMinimalStocksTransactionForLLM(minimalStockTransactions),
	}

	byteResp, marshalErr := protojson.Marshal(llmRes)
	if marshalErr != nil {
		return "", errors.Wrap(marshalErr, "error marshalling stock transactions response")
	}
	return string(byteResp), nil
}
