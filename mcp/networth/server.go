package networth

import (
	"context"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/logger"

	actorPb "github.com/epifi/gamma/api/actor"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	connectedAccPkg "github.com/epifi/gamma/pkg/connectedaccount/securities"

	"github.com/epifi/be-common/pkg/events"

	analyserVariablePb "github.com/epifi/gamma/api/analyser/variables"
	sessionPb "github.com/epifi/gamma/api/auth/session"
	connectedAccountPb "github.com/epifi/gamma/api/connected_account"
	creditReportPb "github.com/epifi/gamma/api/creditreportv2"
	beEpfPb "github.com/epifi/gamma/api/insights/epf"
	networthPb "github.com/epifi/gamma/api/insights/networth"
	mfExternalPb "github.com/epifi/gamma/api/investment/mutualfund/external"
	"github.com/epifi/gamma/httpgw/config/genconf"
	mcpEvents "github.com/epifi/gamma/mcp/networth/events"
	"github.com/epifi/gamma/mcp/networth/middlewares"
	"github.com/epifi/gamma/mcp/networth/tools"
)

func CreateMcpServer(
	config *genconf.Config,
	netWorthClient networthPb.NetWorthClient,
	sessionManagerClient sessionPb.SessionManagerClient,
	variableGeneratorClient analyserVariablePb.VariableGeneratorClient,
	connectedAccountClient connectedAccountPb.ConnectedAccountClient,
	creditReportClient creditReportPb.CreditReportManagerClient,
	mfExternalOrdersClient mfExternalPb.MFExternalOrdersClient,
	epfClient beEpfPb.EpfClient,
	actorClient actorPb.ActorClient,
	userGroupClient usergrouppb.GroupClient,
	eventBroker events.Broker,
	stockTxnsHandler connectedAccPkg.IMinimalStockTransactions) *server.MCPServer {

	eventPublisher := mcpEvents.NewEventPublisher(eventBroker)
	authMiddleware := middlewares.NewAuthMiddleware(sessionManagerClient)
	// Create a new MCP server
	s := server.NewMCPServer(
		"Fi MCP",
		"0.1.0",
		// Notifies clients when new tools gets added or any changes in tools
		server.WithInstructions(config.NetworthMcpConfig().ServerDescription()),
		server.WithToolCapabilities(true),
		server.WithResourceCapabilities(true, true),
		server.WithLogging(),
		server.WithToolHandlerMiddleware(authMiddleware.AuthMiddleware),
	)
	// logger.InfoNoCtx("server description", zap.Any("server_config", config.MyDescription()))
	fetchNetWorthHandler := tools.NewFetchNetWorthHandler(config, netWorthClient, variableGeneratorClient, connectedAccountClient, eventPublisher)
	fetchTransactionsHandler := tools.NewMfFetchTransactionsHandler(config, mfExternalOrdersClient, eventPublisher, userGroupClient, actorClient)
	fetchCreditReportHandler := tools.NewFetchCreditReportHandler(config, creditReportClient, eventPublisher)
	fetchEpfHandler := tools.NewFetchEpfHandler(config, epfClient, eventPublisher)
	fetchBankTransactions := tools.NewFetchBankTransactionsHandler(config, connectedAccountClient, userGroupClient, actorClient, eventPublisher)
	fetchStockTransactions := tools.NewFetchStockTransactionsHandler(config, eventPublisher, stockTxnsHandler)

	// Register fetch net worth tool
	s.AddTool(fetchNetWorthHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		result, fetchNetWorthErr := fetchNetWorthHandler.Handle(ctx, request)
		if fetchNetWorthErr != nil {
			logger.Error(ctx, "error fetching net worth", zap.Error(fetchNetWorthErr))
		} else {
			logger.Info(ctx, "net worth fetched successfully")
		}
		return result, fetchNetWorthErr
	})

	// Register fetch credit report details tool
	s.AddTool(fetchCreditReportHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		creditReportResult, fetchCreditReportErr := fetchCreditReportHandler.Handle(ctx, request)
		if fetchCreditReportErr != nil {
			logger.Error(ctx, "error fetching credit report details", zap.Error(fetchCreditReportErr))
		} else {
			logger.Info(ctx, "credit report details fetched successfully")
		}
		return creditReportResult, fetchCreditReportErr
	})

	// Register fetch portfolio transactions details tool
	s.AddTool(fetchTransactionsHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		portfolioTxnsResult, fetchTxnsErr := fetchTransactionsHandler.Handle(ctx, request)
		if fetchTxnsErr != nil {
			logger.Error(ctx, "error fetching transaction details", zap.Error(fetchTxnsErr))
		} else {
			logger.Info(ctx, "all transaction fetched successfully")
		}
		return portfolioTxnsResult, fetchTxnsErr
	})

	// Register fetch portfolio transactions details tool
	s.AddTool(fetchEpfHandler.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		epfTxnResult, epfTxnErr := fetchEpfHandler.Handle(ctx, request)
		if epfTxnErr != nil {
			logger.Error(ctx, "error fetching epf details", zap.Error(epfTxnErr))
		} else {
			logger.Info(ctx, "epf details fetched successfully")
		}
		return epfTxnResult, epfTxnErr
	})

	// Register fetch bank transactions details tool
	s.AddTool(fetchBankTransactions.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		bankTxnResult, bankTxnErr := fetchBankTransactions.Handle(ctx, request)
		if bankTxnErr != nil {
			logger.Error(ctx, "error fetching bank transactions", zap.Error(bankTxnErr))
		} else {
			logger.Info(ctx, "bank transactions fetched successfully")
		}
		return bankTxnResult, bankTxnErr
	})

	// Register fetch stock transactions details tool
	s.AddTool(fetchStockTransactions.GetTool(), func(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
		defer logger.RecoverPanicAndError(ctx)
		stockTxnResult, stockTxnErr := fetchStockTransactions.Handle(ctx, request)
		if stockTxnErr != nil {
			logger.Error(ctx, "error fetching stock transactions", zap.Error(stockTxnErr))
		} else {
			logger.Info(ctx, "stock transactions fetched successfully")
		}
		return stockTxnResult, stockTxnErr
	})

	return s
}
