package consumer

import (
	"context"
	"errors"
	"fmt"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"

	"github.com/google/uuid"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/queue"

	celestialPb "github.com/epifi/be-common/api/celestial"
	workflowPb "github.com/epifi/be-common/api/celestial/workflow"
	queuePb "github.com/epifi/be-common/api/queue"
	rpcPb "github.com/epifi/be-common/api/rpc"
	celestialPkg "github.com/epifi/be-common/pkg/epifitemporal/celestial"
	riskNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/risk"

	"github.com/epifi/gamma/api/cx/call_routing"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	"github.com/epifi/gamma/risk/case_management"
	"github.com/epifi/gamma/risk/case_management/consumer/processors"
	"github.com/epifi/gamma/risk/case_management/form"
	"github.com/epifi/gamma/risk/case_management/metrics"
	"github.com/epifi/gamma/risk/config"
	genConf "github.com/epifi/gamma/risk/config/genconf"
	"github.com/epifi/gamma/risk/dao"
	"github.com/epifi/gamma/risk/essential"

	protoJson "google.golang.org/protobuf/encoding/protojson"
	structPb "google.golang.org/protobuf/types/known/structpb"

	commsPb "github.com/epifi/gamma/api/comms"
	calRoutingEventPb "github.com/epifi/gamma/api/cx/call_routing/event"
	cxTicketPb "github.com/epifi/gamma/api/cx/ticket"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	escalationPb "github.com/epifi/gamma/api/risk/case_management/escalation"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"
	cmWorkflowPb "github.com/epifi/gamma/api/risk/case_management/workflow"
	bankActionComms "github.com/epifi/gamma/risk/bankactions/comms"
	alertConsumer "github.com/epifi/gamma/risk/case_management/consumer/alert"
	alertProcessor "github.com/epifi/gamma/risk/case_management/consumer/alert/processor"
	batchRuleEngine "github.com/epifi/gamma/risk/case_management/consumer/batchruleengine"
	cmDao "github.com/epifi/gamma/risk/case_management/dao"
	formErrors "github.com/epifi/gamma/risk/case_management/form/errors"

	"google.golang.org/protobuf/types/known/timestamppb"

	actorPb "github.com/epifi/gamma/api/actor"
	paymentPb "github.com/epifi/gamma/api/order/payment"
)

var (
	ProcessorNotFoundErr      = errors.New("error while getting processor for payload type")
	UnsupportedPayloadTypeErr = errors.New("unsupported payload type is passed to processor")
	ErrInvalidRuleIdentifier  = errors.New("rule identifier is invalid")
	ErrMultipleRuleFound      = errors.New("multiple rules found with rule identifier")
)

type CaseManagementConsumerService struct {
	caseManagementPb.UnimplementedRiskCaseManagementConsumerServer
	payloadProcessorFactory       IProcessorFactory
	alertProcessorFactory         alertConsumer.IProcessorFactory
	cfg                           *config.Config
	bankActionDao                 dao.RiskBankActionsDao
	bankActionCommsBuilderFactory bankActionComms.BuilderFactory
	commsClient                   commsPb.CommsClient
	actorManager                  essential.ActorManager
	ruleEngineEventProcessor      batchRuleEngine.Processor
	formHandler                   form.Handler
	celestialClient               celestialPb.CelestialClient
	formDao                       cmDao.FormDao
	transactionBlockDao           cmDao.TransactionBlockDao
	ruleDao                       cmDao.RuleDao
	alertDao                      cmDao.AlertDao
	ruleProcessor                 case_management.RuleProcessor
	genConf                       *genConf.Config
	actorClient                   actorPb.ActorClient
	paymentClient                 paymentPb.PaymentClient
}

func NewCaseManagementConsumerService(payloadProcessorFactory IProcessorFactory, alertProcessorFactory alertConsumer.IProcessorFactory,
	cfg *config.Config, bankActionCommsBuilderFactory bankActionComms.BuilderFactory, bankActionDao dao.RiskBankActionsDao,
	commsClient commsPb.CommsClient, actorManager essential.ActorManager, ruleEngineEventProcessor batchRuleEngine.Processor,
	formHandler form.Handler, celestialClient celestialPb.CelestialClient, formDao cmDao.FormDao,
	transactionBlockDao cmDao.TransactionBlockDao, ruleDao cmDao.RuleDao, alertDao cmDao.AlertDao,
	ruleProcessor case_management.RuleProcessor, genConf *genConf.Config, actorClient actorPb.ActorClient, paymentClient paymentPb.PaymentClient) *CaseManagementConsumerService {
	return &CaseManagementConsumerService{
		payloadProcessorFactory:       payloadProcessorFactory,
		alertProcessorFactory:         alertProcessorFactory,
		cfg:                           cfg,
		bankActionDao:                 bankActionDao,
		bankActionCommsBuilderFactory: bankActionCommsBuilderFactory,
		commsClient:                   commsClient,
		actorManager:                  actorManager,
		ruleEngineEventProcessor:      ruleEngineEventProcessor,
		formHandler:                   formHandler,
		celestialClient:               celestialClient,
		formDao:                       formDao,
		transactionBlockDao:           transactionBlockDao,
		ruleDao:                       ruleDao,
		alertDao:                      alertDao,
		ruleProcessor:                 ruleProcessor,
		genConf:                       genConf,
		actorClient:                   actorClient,
		paymentClient:                 paymentClient,
	}
}

var _ caseManagementPb.RiskCaseManagementConsumerServer = &CaseManagementConsumerService{}

type CaseFailure struct {
	payloadType       caseManagementPb.PayloadType
	payloadIdentifier string
	err               error
}

func (c *CaseManagementConsumerService) AddCases(ctx context.Context, event *caseManagementPb.RiskCasesIngestEvent) (*caseManagementPb.AddCasesResponse, error) {
	var failures []*CaseFailure
	// TODO(Sachin): Do this in parallel
	// Intent to group cases by payload type is to use batch processor
	payloadTypeToRiskCasesMapping := groupRiskCasesByPayloadType(event.GetRiskCases())
	for payloadType, riskCases := range payloadTypeToRiskCasesMapping {
		// Batch processor will be prioritized if it exists for payload type
		if _, err := c.payloadProcessorFactory.GetBatchProcessor(ctx, payloadType); err == nil {
			failures = append(failures, c.processPayloadsBatched(ctx, payloadType, riskCases)...)
			continue
		}
		if _, err := c.payloadProcessorFactory.GetProcessor(ctx, payloadType); err == nil {
			failures = append(failures, c.processPayloadsIndividually(ctx, payloadType, riskCases)...)
			continue
		}
		// If neither batch processor neither single payload processor exists, payload will be added to failures
		failures = append(failures, recordCaseFailures(riskCases, ProcessorNotFoundErr)...)
	}

	// TODO(sachin): Export custom metric for failures and add alerts for same,
	// Also check if we should upload csv with all failed cases to some location so it could be retried manually if needed
	// log partial failures
	if len(failures) > 0 {
		logFailures(ctx, failures)
	}

	logger.Info(ctx, "finished processing risk case ingest event",
		zap.Int(logger.COUNT, len(event.GetRiskCases())), zap.Int(logger.FAILURE_COUNT, len(failures)))
	// if processing failed for all the cases, we will return transient failure so that event can be retried
	if len(failures) == len(event.GetRiskCases()) {
		return getAddResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}
	return getAddResponse(queuePb.MessageConsumptionStatus_SUCCESS), nil
}

func (c *CaseManagementConsumerService) AddAlerts(ctx context.Context, req *caseManagementPb.FrmIngestAlertsEvent) (*caseManagementPb.AddAlertsResponse, error) {
	proc, err := c.alertProcessorFactory.GetBatchProcessor(ctx, req.GetProvenance())
	if err != nil {
		logger.Error(ctx, "unable to find batch processor for AddAlerts: %w", zap.Error(err))
		var failures []*alertProcessor.BatchAlertFailure
		for _, failure := range req.GetAlertPayload() {
			failures = append(failures, &alertProcessor.BatchAlertFailure{
				Payload: failure,
				Err:     fmt.Errorf("unable to find batch processor for AddAlerts: %w", epifierrors.ErrMethodUnimplemented),
			})
		}
		logAlertFailures(ctx, failures)
		return getAlertQueueResponse(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	}
	failures := proc.ProcessAlerts(ctx, req.GetAlertPayload(), req.GetProvenance())
	// TODO navneet : Parse this as a CSV and email
	if len(failures) > 0 {
		logAlertFailures(ctx, failures)
	}
	// if processing failed for all the cases, we will return transient failure so that event can be retried
	if len(failures) == len(req.GetAlertPayload()) {
		return getAlertQueueResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}
	return getAlertQueueResponse(queuePb.MessageConsumptionStatus_SUCCESS), nil
}

// groupRiskCasesByPayloadType groups risk cases by payload type.
func groupRiskCasesByPayloadType(riskCases []*caseManagementPb.RiskCase) map[caseManagementPb.PayloadType][]*caseManagementPb.RiskCase {
	payloadTypeToRiskCasesMapping := make(map[caseManagementPb.PayloadType][]*caseManagementPb.RiskCase)
	for _, riskCase := range riskCases {
		payloadTypeToRiskCasesMapping[riskCase.GetPayloadType()] = append(payloadTypeToRiskCasesMapping[riskCase.GetPayloadType()], riskCase)
	}
	return payloadTypeToRiskCasesMapping
}

func (c *CaseManagementConsumerService) processPayloadsBatched(ctx context.Context, payloadType caseManagementPb.PayloadType, riskCases []*caseManagementPb.RiskCase) []*CaseFailure {
	var failures []*CaseFailure
	processor, err := c.payloadProcessorFactory.GetBatchProcessor(ctx, payloadType)
	if err != nil {
		return recordCaseFailures(riskCases, UnsupportedPayloadTypeErr)
	}
	var inputs []*processors.Input
	for _, riskCase := range riskCases {
		inputs = append(inputs,
			&processors.Input{
				Payload:        riskCase.GetPayload(),
				BatchId:        riskCase.GetBatchIdentifier(),
				RuleIdentifier: riskCase.GetRuleIdentifier(),
				InitiatedAt:    riskCase.GetInitiatedAt(),
			},
		)
	}
	failedPayloads := processor.ProcessPayload(ctx, payloadType, inputs)
	for _, failedPayload := range failedPayloads {
		failures = append(failures, &CaseFailure{
			payloadType:       payloadType,
			payloadIdentifier: getIdentifierFromPayload(failedPayload.GetPayload()),
			err:               failedPayload.GetErr(),
		})
		metrics.RecordIngestionFailure(failedPayload.GetBatchId(), payloadType)
	}
	return failures
}

func recordCaseFailures(riskCases []*caseManagementPb.RiskCase, err error) []*CaseFailure {
	var failures []*CaseFailure
	for _, riskCase := range riskCases {
		failures = append(failures, &CaseFailure{
			payloadType:       riskCase.GetPayloadType(),
			payloadIdentifier: getIdentifierFromPayload(riskCase.GetPayload()),
			err:               err,
		})
		metrics.RecordIngestionFailure(riskCase.GetBatchIdentifier(), riskCase.GetPayloadType())
	}
	return failures
}

func (c *CaseManagementConsumerService) processPayloadsIndividually(ctx context.Context, payloadType caseManagementPb.PayloadType, riskCases []*caseManagementPb.RiskCase) []*CaseFailure {
	var failures []*CaseFailure
	processor, err := c.payloadProcessorFactory.GetProcessor(ctx, payloadType)
	if err != nil {
		return recordCaseFailures(riskCases, UnsupportedPayloadTypeErr)
	}
	for _, riskCase := range riskCases {
		err := processor.ProcessPayload(ctx, payloadType, riskCase.GetPayload())
		if err == nil {
			metrics.RecordIngestionSuccess(riskCase.GetBatchIdentifier(), payloadType)
			continue
		}
		failures = append(failures, &CaseFailure{
			payloadType:       payloadType,
			payloadIdentifier: getIdentifierFromPayload(riskCase.GetPayload()),
			err:               err,
		})
		metrics.RecordIngestionFailure(riskCase.GetBatchIdentifier(), payloadType)
	}
	return failures
}

func getIdentifierFromPayload(payload interface{}) string {
	switch v := payload.(type) {
	case *caseManagementPb.RiskCase_LivenessReview:
		livenessReview := v.LivenessReview
		return livenessReview.GetActorId()
	case *caseManagementPb.RiskCase_LivenessSampleReview:
		livenessSampleReview := v.LivenessSampleReview
		return fmt.Sprintf("%s::%s", livenessSampleReview.GetActorId(), livenessSampleReview.GetRequestId())
	case *caseManagementPb.RiskCase_UserReview:
		userReview := v.UserReview
		return userReview.GetActorId()
	case *caseManagementPb.RiskCase_TransactionReview:
		transactionReview := v.TransactionReview
		return fmt.Sprintf("%s::%s::%s", transactionReview.GetActorId(), transactionReview.GetAccountId(), transactionReview.GetTxnId())
	case *caseManagementPb.RiskCase_ScreenerReview:
		screenerReview := v.ScreenerReview
		return fmt.Sprintf("%s::%s", screenerReview.GetActorId(), screenerReview.GetScreenerAttemptId())
	default:
		return ""
	}
}

func logFailures(ctx context.Context, failures []*CaseFailure) {
	combinedErrorString := ""
	for _, failure := range failures {
		combinedErrorString += fmt.Sprintf("payloadType: %s, payloadIdentifier: %s, err: %s \n", failure.payloadType, failure.payloadIdentifier, failure.err.Error())
	}
	logger.Error(ctx, "partial error while processing risk case ingest event", zap.String(logger.ERROR_LIST, combinedErrorString))
}

func getAddResponse(status queuePb.MessageConsumptionStatus) *caseManagementPb.AddCasesResponse {
	return &caseManagementPb.AddCasesResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: status,
		},
	}
}

func getAlertQueueResponse(status queuePb.MessageConsumptionStatus) *caseManagementPb.AddAlertsResponse {
	return &caseManagementPb.AddAlertsResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: status,
		},
	}
}

func logAlertFailures(ctx context.Context, failures []*alertProcessor.BatchAlertFailure) {
	combinedErrorString := ""
	for _, failure := range failures {
		combinedErrorString += fmt.Sprintf("actor ID: %s, batch name: %s error: %s \n", failure.GetActorID(), failure.GetBatchName(), failure.Err.Error())
	}
	// todo (navneet) move ot email/ slack
	logger.Error(ctx, "partial error while processing risk alert ingest event", zap.String(logger.ERROR_LIST, combinedErrorString))
}

// nolint:funlen
func (c *CaseManagementConsumerService) ProcessCallRoutingEvent(ctx context.Context, event *calRoutingEventPb.CallRoutingEvent) (*caseManagementPb.ProcessCallRoutingEventResponse, error) {
	if !isProcessingRequired(event) {
		logger.Info(ctx, "skipping event processing since not a risk use case event", zap.String(logger.EVENT_TYPE, event.GetCallRoutingEventType().String()))
		// return success without doing any processing if not a risk related event
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_SUCCESS), nil
	}
	actorId := event.GetPreRecordedMessageEvent().GetActorId()
	if actorId == "" {
		logger.Error(ctx, "invalid event, actor id is missing")
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	}

	actor, err := c.actorManager.GetByActorId(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error while getting actor details", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	// get latest bank action for user
	bankActions, err := c.bankActionDao.GetByActor(ctx, actorId, 1)
	switch {
	case errors.Is(err, epifierrors.ErrRecordNotFound):
		logger.Error(ctx, "no record found for bank action for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	case err != nil:
		logger.Error(ctx, "error while fetching bank action for actor", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	builderImpl, err := c.bankActionCommsBuilderFactory.GetBuilderForAction(ctx, bankActions[0])
	if err != nil {
		logger.Error(ctx, "error while getting comms builder impl for bank action", zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	}

	// Get formId for the already sent form
	forms, _ := c.formDao.Get(ctx, formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
		Kind: &structPb.Value_StringValue{StringValue: actorId},
	}, 0)
	var (
		formID string = ""
	)
	for _, formLocal := range forms {
		if formLocal.GetOrigin() == formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE && formLocal.IsOpenForSubmission() {
			formID = formLocal.GetId()
			break
		}
	}

	communicationList, err := builderImpl.GetNotifications(ctx, bankActions[0], formID, true)
	switch {
	case errors.Is(err, bankActionComms.ErrCommsNotRequiredForAction):
		logger.Info(ctx, "skipping sending comms for action", zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_SUCCESS), nil
	case errors.Is(err, epifierrors.ErrPermanent):
		logger.Error(ctx, "permanent error received from comms builder",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_PERMANENT_FAILURE), nil
	case err != nil:
		logger.Error(ctx, "error while getting comms list for action",
			zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	resp, err := c.commsClient.SendMessageBatch(ctx, &commsPb.SendMessageBatchRequest{
		Type: commsPb.QoS_BEST_EFFORT,
		UserIdentifier: &commsPb.SendMessageBatchRequest_UserId{
			UserId: actor.GetEntityId(),
		},
		CommunicationList: communicationList,
	})
	if err = epifigrpc.RPCError(resp, err); err != nil {
		logger.Error(ctx, "error while sending comms", zap.Error(err), zap.String(logger.ACTOR_ID_V2, actorId))
		return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE), nil
	}

	return buildCallRoutingEventResponse(queuePb.MessageConsumptionStatus_SUCCESS), nil
}

func isProcessingRequired(event *calRoutingEventPb.CallRoutingEvent) bool {
	return event.GetCallRoutingEventType() == call_routing.CallRoutingEventType_CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE &&
		event.GetPreRecordedMessageEvent().GetRecordingIdentifier() == call_routing.RecordingIdentifier_RECORDING_IDENTIFIER_HIGH_RISK_SCORE
}

func buildCallRoutingEventResponse(status queuePb.MessageConsumptionStatus) *caseManagementPb.ProcessCallRoutingEventResponse {
	return &caseManagementPb.ProcessCallRoutingEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{Status: status},
	}
}

func (c *CaseManagementConsumerService) ProcessFormSubmission(ctx context.Context, req *caseManagementPb.FormSubmissionEvent) (
	*caseManagementPb.ProcessFormSubmissionResponse, error) {
	err := c.formHandler.SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses())

	if err == nil {
		return &caseManagementPb.ProcessFormSubmissionResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_SUCCESS,
				GrpcStatusCode: rpcPb.StatusOk(),
			},
		}, nil
	}

	logger.Error(ctx, "failed to submit form", zap.Error(err), zap.String(logger.ID, req.GetFormId()))
	switch {
	case errors.Is(err, formErrors.ErrFormExpired) || errors.Is(err, formErrors.ErrFormNotFound) ||
		errors.Is(err, formErrors.ErrFormAlreadySubmitted) || errors.Is(err, formErrors.ErrInvalidResponse):
		return &caseManagementPb.ProcessFormSubmissionResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	default:
		return &caseManagementPb.ProcessFormSubmissionResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}
}

func (c *CaseManagementConsumerService) ProcessBatchRuleEngineEvent(ctx context.Context, event *caseManagementPb.BatchRuleEngineEvent) (*caseManagementPb.ProcessBatchRuleEngineEventResponse, error) {
	for _, record := range event.GetRecords() {
		logger.Debug(ctx, "received batch rule engine event",
			zap.String(logger.FILE_NAME, record.GetS3().GetObject().GetKey()))
		err := c.ruleEngineEventProcessor.Process(ctx,
			&batchRuleEngine.Event{
				FilePath: record.GetS3().GetObject().GetKey(),
			})
		switch {
		case errors.Is(err, batchRuleEngine.ErrNotARuleHitsFile):
			continue
		case err != nil:
			logger.Error(ctx, "failed to process event", zap.Error(err))
			return &caseManagementPb.ProcessBatchRuleEngineEventResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queue.GetStatusFromErr(err),
				},
			}, nil
		}
	}
	return &caseManagementPb.ProcessBatchRuleEngineEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

// ProcessCXTicketUpdateEvent validates applies basic validation CX ticket and triggers workflow
func (c *CaseManagementConsumerService) ProcessCXTicketUpdateEvent(ctx context.Context, event *caseManagementPb.CXTicketUpdateEvent) (
	*caseManagementPb.ProcessCXTicketUpdateEventResponse, error) {
	ticket := event.GetTicket()
	// additional checks to validate if ticket is from the right bucket
	if !isValidRiskEscalationTicket(ticket) || ticket.GetActorId() == "" {
		logger.Error(ctx, "invalid escalation update type found", zap.Int64(logger.TICKET_ID, ticket.GetId()), zap.String(logger.ACTOR_ID_V2, ticket.GetActorId()))
		return &caseManagementPb.ProcessCXTicketUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				GrpcStatusCode: rpcPb.StatusInvalidArgument(),
			},
		}, nil
	}

	payloadBytes, err := protoJson.Marshal(&cmWorkflowPb.ProcessEscalationEventRequest{
		Event: &escalationPb.EscalationEvent{
			ActorId: ticket.GetActorId(),
			RequestEntity: &escalationPb.EscalationEvent_CxTicketEvent{
				CxTicketEvent: &escalationPb.CXTicketEvent{
					CxTicket: ticket,
				},
			},
		},
	})
	if err != nil {
		logger.Error(ctx, "error marshalling payload", zap.Error(err))
		return &caseManagementPb.ProcessCXTicketUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
				GrpcStatusCode: rpcPb.StatusInternal(),
			},
		}, nil
	}

	initiateWorkflowResp, err := c.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: ticket.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(riskNs.ProcessEscalationEvent),
			Payload: payloadBytes,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     uuid.New().String(),
				Client: workflowPb.Client_RISK,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	})
	if rpcErr := epifigrpc.RPCError(initiateWorkflowResp, err); rpcErr != nil {
		logger.Error(ctx, "error while creating a new workflow", zap.String(logger.ACTOR_ID_V2, ticket.GetActorId()), zap.Error(rpcErr))
		return &caseManagementPb.ProcessCXTicketUpdateEventResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status:         queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				GrpcStatusCode: rpcPb.StatusInternal(),
			},
		}, nil
	}

	return &caseManagementPb.ProcessCXTicketUpdateEventResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status:         queuePb.MessageConsumptionStatus_SUCCESS,
			GrpcStatusCode: rpcPb.StatusOk(),
		},
	}, nil
}

func isValidRiskEscalationTicket(ticket *cxTicketPb.Ticket) bool {
	if lo.Contains([]cxTicketPb.ProductCategory{
		cxTicketPb.ProductCategory_PRODUCT_CATEGORY_FRAUD_AND_RISK,
		cxTicketPb.ProductCategory_PRODUCT_CATEGORY_RISK,
	}, ticket.GetCustomFields().GetProductCategory()) &&
		ticket.GetGroup() == cxTicketPb.Group_GROUP_RISK_OPS {
		return true
	}

	return false
}

// getPayloadTypeFromEntityType maps entity types to corresponding payload types for metrics
func getPayloadTypeFromEntityType(entityType cmEnumsPb.EntityType) caseManagementPb.PayloadType {
	switch entityType {
	case cmEnumsPb.EntityType_ENTITY_TYPE_USER:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW
	case cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW
	case cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW // Transaction blocks are related to transaction reviews
	case cmEnumsPb.EntityType_ENTITY_TYPE_LIVENESS:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS
	case cmEnumsPb.EntityType_ENTITY_TYPE_AFU,
		cmEnumsPb.EntityType_ENTITY_TYPE_SCREENER,
		cmEnumsPb.EntityType_ENTITY_TYPE_ESCALATION:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW // These are user-level entities
	default:
		return caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW // Default fallback
	}
}

//nolint:dupl
func (c *CaseManagementConsumerService) ProcessRiskAlertEvent(ctx context.Context, event *caseManagementPb.RiskSignalIngestEvent) (*caseManagementPb.RiskSignalIngestResponse, error) {
	rawAlert := event.GetAlert().GetRawAlert()

	// Validate raw alert object
	errValidateRawAlert := c.validateRawAlertObject(ctx, rawAlert)
	if errValidateRawAlert != nil {
		logger.Error(ctx, "raw alert validation failed",
			zap.Error(errValidateRawAlert),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String("entity_type", rawAlert.GetEntityType().String()),
			zap.String("entity_id", rawAlert.GetEntityId()),
			zap.Any("identifier", rawAlert.GetIdentifier()))
		return nil, errValidateRawAlert
	}

	// Get rule details for processing decision
	rule, err := c.getRuleByIdentifier(ctx, &caseManagementPb.RuleIdentifier{
		Identifier: &caseManagementPb.RuleIdentifier_RuleId{
			RuleId: rawAlert.GetIdentifier().GetRuleId(),
		},
	})
	if err != nil {
		logger.Error(ctx, "error occurred while fetching rule",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_ID, rawAlert.GetIdentifier().GetRuleId()))

		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrInvalidArgument) ||
			errors.Is(err, ErrInvalidRuleIdentifier) || errors.Is(err, ErrMultipleRuleFound) {
			logger.Error(ctx, "failed to get rule details",
				zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
				zap.String(logger.RULE_ID, rawAlert.GetIdentifier().GetRuleId()))

			metrics.RecordIngestionFailure(rawAlert.GetBatchName(), getPayloadTypeFromEntityType(rawAlert.GetEntityType()))
			return &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			}, nil
		}
	}
	if rule.GetState() == caseManagementPb.RuleState_RULE_STATE_SHADOW {
		logger.Error(ctx, "shadow alert has been consumed in active alert consumer, returning error", zap.Any("alert", rawAlert))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}
	logger.Debug(ctx, "processing active rule alert",
		zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
		zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
		zap.Bool("process_active_rule_alerts", c.cfg.CMConsumerConfig.ProcessActiveRuleAlerts))

	return c.processActiveRuleAlert(ctx, rawAlert, rule)
}

// ProcessRiskSignalEvent processes risk signals from the queue
func (c *CaseManagementConsumerService) ProcessRiskSignalEvent(ctx context.Context, event *caseManagementPb.RiskSignalIngestEvent) (*caseManagementPb.RiskSignalIngestResponse, error) {
	rawAlert := event.GetAlert().GetRawAlert()
	logger.Info(ctx, fmt.Sprintf("processing risk signal event, ActorID:%s, entity_id:%s, entity_type:%s", rawAlert.GetActorId(), rawAlert.GetEntityId(), rawAlert.GetEntityType()),
		zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
		zap.String(logger.BATCH_NAME, rawAlert.GetBatchName()),
		zap.String(logger.PROVENANCE, event.GetAlert().GetProvenance().String()),
		zap.String("entity_type", rawAlert.GetEntityType().String()),
		zap.String("entity_id", rawAlert.GetEntityId()),
		zap.Any("raw_alert", rawAlert))

	// Validate raw alert object
	errValidateRawAlert := c.validateRawAlertObject(ctx, rawAlert)
	if errValidateRawAlert != nil {
		logger.Error(ctx, "raw alert validation failed",
			zap.Error(errValidateRawAlert),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String("entity_type", rawAlert.GetEntityType().String()),
			zap.String("entity_id", rawAlert.GetEntityId()),
			zap.Any("identifier", rawAlert.GetIdentifier()))
		return nil, errValidateRawAlert
	}

	// Get rule details for processing decision
	rule, err := c.getRuleByIdentifier(ctx, &caseManagementPb.RuleIdentifier{
		Identifier: &caseManagementPb.RuleIdentifier_RuleId{
			RuleId: rawAlert.GetIdentifier().GetRuleId(),
		},
	})

	if err != nil {
		logger.Error(ctx, "error occurred while fetching rule",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_ID, rawAlert.GetIdentifier().GetRuleId()))

		if errors.Is(err, epifierrors.ErrRecordNotFound) || errors.Is(err, epifierrors.ErrInvalidArgument) ||
			errors.Is(err, ErrInvalidRuleIdentifier) || errors.Is(err, ErrMultipleRuleFound) {
			logger.Error(ctx, "failed to get rule details",
				zap.Error(err),
				zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
				zap.String(logger.RULE_ID, rawAlert.GetIdentifier().GetRuleId()))

			metrics.RecordIngestionFailure(rawAlert.GetBatchName(), getPayloadTypeFromEntityType(rawAlert.GetEntityType()))
			return &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			}, nil
		}
	}

	logger.Debug(ctx, "fetched rule details",
		zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
		zap.String(logger.RULE_ID, rawAlert.GetIdentifier().GetRuleId()),
		zap.String("rule_state", rule.GetState().String()),
		zap.String("rule_external_id", rule.GetExternalId()))

	// Process based on rule state
	switch rule.GetState() {
	case caseManagementPb.RuleState_RULE_STATE_INACTIVE:
		logger.Info(ctx, "rule is inactive, skipping processing",
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_SUCCESS,
			},
		}, nil
	case caseManagementPb.RuleState_RULE_STATE_SHADOW:
		logger.Debug(ctx, "processing shadow rule alert",
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()))
		return c.processShadowRuleAlert(ctx, rawAlert, rule)
	default:
		logger.Debug(ctx, "processing active rule alert",
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
			zap.Bool("process_active_rule_alerts", c.cfg.CMConsumerConfig.ProcessActiveRuleAlerts))

		// Check if active rule alert processing is enabled
		if c.genConf == nil || !c.genConf.CMConsumerConfig().ProcessActiveRuleAlerts() {
			// If processing active rules is disabled
			logger.Info(ctx, "active rule alert processing is disabled",
				zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
				zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))

			return &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queuePb.ConsumerResponseHeader{
					Status: queuePb.MessageConsumptionStatus_SUCCESS,
				},
			}, nil
		}
		return c.processActiveRuleAlert(ctx, rawAlert, rule)
	}
}

// processShadowRuleAlert handles alerts triggered by shadow rules
func (c *CaseManagementConsumerService) processShadowRuleAlert(ctx context.Context, alert *caseManagementPb.RawAlert, rule *caseManagementPb.Rule) (*caseManagementPb.RiskSignalIngestResponse, error) {
	logger.Debug(ctx, "processing shadow rule alert",
		zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
		zap.String(logger.ACTOR_ID_V2, alert.GetActorId()))

	alertCreatedInDb, err := c.createAlertInDB(ctx, alert)
	if err != nil {
		logger.Error(ctx, "failed to create alert for shadow rule",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, alert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))

		metrics.RecordIngestionFailure(alert.GetBatchName(), getPayloadTypeFromEntityType(alert.GetEntityType()))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}

	// TODO: Add a factory pattern for all the handling types of shadow alerts
	// Currently we only have transaction blocks as a shadow alert type,
	// in future if more shadow alert types are added then it would be
	// bad implementation to have them called like one by one.

	// Handle transaction blocks if needed
	c.handleTransactionBlocks(ctx, alertCreatedInDb, alert)

	logger.Debug(ctx, "successfully processed shadow rule alert",
		zap.String(logger.ACTOR_ID_V2, alert.GetActorId()),
		zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
		zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()))

	metrics.RecordIngestionSuccess(alert.GetBatchName(), getPayloadTypeFromEntityType(alert.GetEntityType()))
	return &caseManagementPb.RiskSignalIngestResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

// processActiveRuleAlert handles alerts triggered by active rules by initiating the ProcessRiskAlert workflow
func (c *CaseManagementConsumerService) processActiveRuleAlert(ctx context.Context, rawAlert *caseManagementPb.RawAlert, rule *caseManagementPb.Rule) (*caseManagementPb.RiskSignalIngestResponse, error) {
	logger.Debug(ctx, "processing active rule alert",
		zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
		zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))

	// Generate UUID for workflow
	uuidStr, err := uuid.NewRandom()
	if err != nil {
		logger.Error(ctx, "failed to generate UUID for workflow",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}

	// Prepare the payload for the workflow
	payloadBytes, err := protoJson.Marshal(&cmWorkflowPb.ProcessAlertRequest{
		Alert: rawAlert,
	})
	if err != nil {
		logger.Error(ctx, "error marshalling payload for process alert workflow",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_PERMANENT_FAILURE,
			},
		}, nil
	}

	// Initiate the workflow
	initiateWorkflowResp, err := c.celestialClient.InitiateWorkflow(ctx, &celestialPb.InitiateWorkflowRequest{
		Params: &celestialPb.WorkflowCreationRequestParams{
			ActorId: rawAlert.GetActorId(),
			Version: workflowPb.Version_V0,
			Type:    celestialPkg.GetTypeEnumFromWorkflowType(riskNs.RiskProcessAlert),
			Payload: payloadBytes,
			ClientReqId: &workflowPb.ClientReqId{
				Id:     uuidStr.String(),
				Client: workflowPb.Client_RISK,
			},
			Ownership: commontypes.Ownership_EPIFI_TECH,
		},
	})

	if rpcErr := epifigrpc.RPCError(initiateWorkflowResp, err); rpcErr != nil {
		logger.Error(ctx, "error while initiating process alert workflow",
			zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
			zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
			zap.String("workflow_uuid", uuidStr.String()),
			zap.Error(rpcErr))

		metrics.RecordIngestionFailure(rawAlert.GetBatchName(), getPayloadTypeFromEntityType(rawAlert.GetEntityType()))
		return &caseManagementPb.RiskSignalIngestResponse{
			ResponseHeader: &queuePb.ConsumerResponseHeader{
				Status: queuePb.MessageConsumptionStatus_TRANSIENT_FAILURE,
			},
		}, nil
	}

	logger.Debug(ctx, "successfully processed active rule alert",
		zap.String(logger.ACTOR_ID_V2, rawAlert.GetActorId()),
		zap.String(logger.RULE_EXTERNAL_ID, rule.GetExternalId()),
		zap.String("workflow_uuid", uuidStr.String()))

	metrics.RecordIngestionSuccess(rawAlert.GetBatchName(), getPayloadTypeFromEntityType(rawAlert.GetEntityType()))
	return &caseManagementPb.RiskSignalIngestResponse{
		ResponseHeader: &queuePb.ConsumerResponseHeader{
			Status: queuePb.MessageConsumptionStatus_SUCCESS,
		},
	}, nil
}

// handleTransactionBlocks handles the creation of transaction blocks for alerts
// The function takes the alert which was already created in DB (alertCreatedInDb) to get the alert id
// It also takes into input the alert ingested via queue (alertFromQueue) to get the transaction block details
// And it calls the dao layer to add the transaction blocks with alert id into the transaction blocks table.
func (c *CaseManagementConsumerService) handleTransactionBlocks(ctx context.Context, alertCreatedInDb *caseManagementPb.Alert, alertFromQueue *caseManagementPb.RawAlert) {
	// Handle transaction block creation for alerts
	if alertFromQueue.GetEntityType() == cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK {
		// Process transaction blocks from alert's meta details
		if alertFromQueue.GetMetaDetails() != nil && len(alertFromQueue.GetMetaDetails().GetTransactionBlocks()) > 0 {
			logger.Debug(ctx, "creating transaction blocks for alert",
				zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
				zap.Int("block_count", len(alertFromQueue.GetMetaDetails().GetTransactionBlocks())))

			var failures []string
			var successCount int

			// Current timestamp for CreatedAt/UpdatedAt if not provided
			now := time.Now()

			// Process each transaction block
			for _, block := range alertFromQueue.GetMetaDetails().GetTransactionBlocks() {
				// Ensure the transaction block has the alert ID
				block.AlertId = alertCreatedInDb.GetId()

				// Set timestamps if not provided
				if block.GetCreatedAt() == nil {
					block.CreatedAt = timestamppb.New(now)
				}
				if block.GetUpdatedAt() == nil {
					block.UpdatedAt = timestamppb.New(now)
				}

				// TODO: Currently we are not verifying if the transaction id is correct or not.
				// We must do it for all the transaction ids.
				// But since the rule currently is shadow hence we are treating them correct from source.

				// Use the transaction block DAO directly
				_, errTxnBlockDao := c.transactionBlockDao.Create(ctx, block)
				if errTxnBlockDao != nil {
					logger.Error(ctx, "failed to create transaction block for alert",
						zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
						zap.String("block_id", block.GetId()),
						zap.String(logger.ACTOR_ID_V2, block.GetActorId()),
						zap.Error(errTxnBlockDao))

					failures = append(failures, block.GetId())
				} else {
					successCount++
					logger.Debug(ctx, "successfully created transaction block for alert",
						zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
						zap.String("block_id", block.GetId()))
				}
			}

			// Log summary
			if len(failures) > 0 {
				logger.Error(ctx, "failed to process some transaction blocks for alert",
					zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
					zap.Int("total", len(alertFromQueue.GetMetaDetails().GetTransactionBlocks())),
					zap.Int("successes", successCount),
					zap.Int("failures", len(failures)),
					zap.Strings("failed_block_ids", failures))
			} else {
				logger.Debug(ctx, "successfully created all transaction blocks for alert",
					zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
					zap.Int("block_count", successCount))
			}
		} else {
			logger.Debug(ctx, "alert has transaction entity type but no transaction blocks in meta details",
				zap.String(logger.ALERT_ID, alertCreatedInDb.GetId()),
				zap.String(logger.ACTOR_ID_V2, alertCreatedInDb.GetActorId()))
		}
	}
}

func (c *CaseManagementConsumerService) validateRawAlertObject(ctx context.Context, alert *caseManagementPb.RawAlert) error {
	switch {
	case alert.GetEntityType() == cmEnumsPb.EntityType_ENTITY_TYPE_UNSPECIFIED:
		logger.Error(ctx, "validation failed: entity type is unspecified")
		return errors.New("entity type field is mandatory")
	case alert.GetEntityId() == "" && alert.GetEntityType() != cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK:
		logger.Error(ctx, "validation failed: entity id is empty")
		return errors.New("entity id field is mandatory")
	case alert.GetIdentifier().GetIdentifier() == nil:
		logger.Error(ctx, "validation failed: rule identifier is nil")
		return errors.New("invalid rule identifier")
	}

	// Skip enhanced validation if clients are not initialized (for backward compatibility with tests)
	if c.actorClient == nil || c.paymentClient == nil {
		logger.Debug(ctx, "skipping enhanced entity validation - clients not initialized")
		return nil
	}

	// Validate entity_id based on entity_type
	switch alert.GetEntityType() {
	case cmEnumsPb.EntityType_ENTITY_TYPE_USER:
		// For user entity type, entity_id should be a valid actor_id
		if err := c.validateActorId(ctx, alert.GetEntityId()); err != nil {
			logger.Error(ctx, "validation failed: invalid actor_id for ENTITY_TYPE_USER",
				zap.String("entity_id", alert.GetEntityId()),
				zap.Error(err))
			return fmt.Errorf("invalid actor_id for ENTITY_TYPE_USER: %w", err)
		}

	case cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK:
		// For transaction block entity type, actor_id should be a valid actor_id
		if err := c.validateActorId(ctx, alert.GetActorId()); err != nil {
			logger.Error(ctx, "validation failed: invalid actor_id for ENTITY_TYPE_TRANSACTION_BLOCK",
				zap.String("entity_id", alert.GetEntityId()),
				zap.Error(err))
			return fmt.Errorf("invalid actor_id for ENTITY_TYPE_TRANSACTION_BLOCK: %w", err)
		}

		// Additionally, check that meta details and transaction blocks are present
		if alert.GetMetaDetails() == nil {
			logger.Error(ctx, "validation failed: meta details are required for ENTITY_TYPE_TRANSACTION_BLOCK")
			return errors.New("meta details are required for ENTITY_TYPE_TRANSACTION_BLOCK")
		}

		if len(alert.GetMetaDetails().GetTransactionBlocks()) == 0 {
			logger.Error(ctx, "validation failed: transaction blocks are required in meta details for ENTITY_TYPE_TRANSACTION_BLOCK")
			return errors.New("transaction blocks are required in meta details for ENTITY_TYPE_TRANSACTION_BLOCK")
		}

	case cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION:
		// For transaction entity type, entity_id can be either transaction id or actor id but it should not be null
		if err := c.validateActorId(ctx, alert.GetActorId()); err != nil {
			logger.Error(ctx, "validation failed: invalid actor_id for ENTITY_TYPE_TRANSACTION",
				zap.String("entity_id", alert.GetEntityId()),
				zap.Error(err))
			return fmt.Errorf("invalid actor_id for ENTITY_TYPE_TRANSACTION: %w", err)
		}
	}

	return nil
}

// validateActorId validates if the given actor_id exists and is valid
func (c *CaseManagementConsumerService) validateActorId(ctx context.Context, actorId string) error {
	if actorId == "" {
		return errors.New("actor_id cannot be empty")
	}

	actorResp, err := c.actorClient.GetActorById(ctx, &actorPb.GetActorByIdRequest{
		Id: actorId,
	})

	if rpcErr := epifigrpc.RPCError(actorResp, err); rpcErr != nil {
		if actorResp != nil && actorResp.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("actor not found for actor_id: %s", actorId)
		}
		return fmt.Errorf("error validating actor_id: %w", rpcErr)
	}

	return nil
}

// validateTransactionId validates if the given transaction_id exists and is valid
func (c *CaseManagementConsumerService) validateTransactionId(ctx context.Context, transactionId string) error {
	if transactionId == "" {
		return errors.New("transaction_id cannot be empty")
	}

	txnResp, err := c.paymentClient.GetTransaction(ctx, &paymentPb.GetTransactionRequest{
		Identifier: &paymentPb.GetTransactionRequest_TransactionId{
			TransactionId: transactionId,
		},
	})

	if rpcErr := epifigrpc.RPCError(txnResp, err); rpcErr != nil {
		if txnResp != nil && txnResp.GetStatus().IsRecordNotFound() {
			return fmt.Errorf("transaction not found for transaction_id: %s", transactionId)
		}
		return fmt.Errorf("error validating transaction_id: %w", rpcErr)
	}

	return nil
}

// getRuleByIdentifier gets a rule by its identifier, following the pattern in validate_alert.go
func (c *CaseManagementConsumerService) getRuleByIdentifier(ctx context.Context, ruleIdentifier *caseManagementPb.RuleIdentifier) (*caseManagementPb.Rule, error) {
	if ruleIdentifier == nil {
		logger.Error(ctx, "rule identifier is nil")
		return nil, fmt.Errorf("rule identifier cannot be nil")
	}

	if ruleIdentifier.GetIdentifier() == nil {
		logger.Error(ctx, "rule identifier.Identifier is nil")
		return nil, fmt.Errorf("rule identifier.Identifier cannot be nil")
	}

	var (
		ruleList []*caseManagementPb.Rule
		err      error
	)

	// fetch rule details
	switch ruleIdentifier.GetIdentifier().(type) {
	case *caseManagementPb.RuleIdentifier_ExternalId:
		externalId := ruleIdentifier.GetExternalId()
		if externalId == "" {
			logger.Error(ctx, "external ID is empty")
			return nil, fmt.Errorf("external ID cannot be empty")
		}

		ruleList, err = c.ruleDao.GetBulkByExternalId(ctx, []string{externalId})

	case *caseManagementPb.RuleIdentifier_RuleId:
		ruleId := ruleIdentifier.GetRuleId()
		if ruleId == "" {
			logger.Error(ctx, "rule ID is empty")
			return nil, fmt.Errorf("rule ID cannot be empty")
		}

		ruleList, err = c.ruleDao.GetBulkById(ctx, []string{ruleId})

	default:
		logger.Error(ctx, "invalid rule identifier type",
			zap.String("identifier_type", fmt.Sprintf("%T", ruleIdentifier.GetIdentifier())))
		return nil, fmt.Errorf("invalid rule identifier")
	}

	// handle dao result
	switch {
	case errors.Is(err, epifierrors.ErrInvalidArgument):
		logger.Error(ctx, "DAO returned InvalidArgument error", zap.Error(err))
		return nil, err
	case err != nil:
		logger.Error(ctx, "DAO returned other error", zap.Error(err))
		return nil, fmt.Errorf("error while fetching rule for identifier %w", err)
	case len(ruleList) == 0:
		logger.Error(ctx, "no rules found for identifier")
		return nil, epifierrors.ErrRecordNotFound
	case len(ruleList) > 1:
		logger.Error(ctx, "multiple rules found for identifier", zap.Int("rule_count", len(ruleList)))
		return nil, fmt.Errorf("multiple rules found for the rule id")
	default:
		return ruleList[0], nil
	}
}

// createAlertInDB creates an alert in the database, converting a RawAlert to Alert
func (c *CaseManagementConsumerService) createAlertInDB(ctx context.Context, alert *caseManagementPb.RawAlert) (*caseManagementPb.Alert, error) {
	// Convert RawAlert to Alert
	ruleId := getRuleId(alert.GetIdentifier())

	alertObj := &caseManagementPb.Alert{
		// Get rule ID from identifier
		RuleId: ruleId,

		// Copy fields directly from RawAlert
		ActorId:     alert.GetActorId(),
		AccountId:   alert.GetAccountId(),
		EntityType:  alert.GetEntityType(),
		EntityId:    alert.GetEntityId(),
		BatchName:   alert.GetBatchName(),
		InitiatedAt: alert.GetInitiatedAt(),

		// Set default values for Alert-specific fields
		Verdict:      cmEnumsPb.Verdict_VERDICT_UNSPECIFIED,
		HandlingType: cmEnumsPb.AlertHandlingType_ALERT_HANDLING_TYPE_UNSPECIFIED,

		// Copy meta details if available
		MetaDetails: alert.GetMetaDetails(),
	}

	// Create the alert in the database
	createdAlert, err := c.alertDao.Create(ctx, alertObj)
	if err != nil {
		logger.Error(ctx, "error creating alert in database",
			zap.Error(err),
			zap.String(logger.ACTOR_ID_V2, alert.GetActorId()),
			zap.String("rule_id", alertObj.GetRuleId()),
			zap.Bool("is_invalid_argument_error", errors.Is(err, epifierrors.ErrInvalidArgument)))
		return nil, fmt.Errorf("error creating alert in database: %w", err)
	}

	return createdAlert, nil
}

// getRuleId extracts the rule ID from the rule identifier
func getRuleId(identifier *caseManagementPb.RuleIdentifier) string {
	if identifier == nil {
		return ""
	}

	switch identifier.GetIdentifier().(type) {
	case *caseManagementPb.RuleIdentifier_RuleId:
		return identifier.GetRuleId()
	case *caseManagementPb.RuleIdentifier_ExternalId:
		// When using external ID, the actual rule ID will be looked up in the database
		// and populated by rule validation logic elsewhere in the system
		// ToDo: Need to add logic to handle the external id for the rule
		return ""
	default:
		return ""
	}
}
