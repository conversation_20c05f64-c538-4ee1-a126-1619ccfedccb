package consumer

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"
	"strings"
	"testing"

	"github.com/epifi/be-common/api/queue"
	"github.com/epifi/be-common/pkg/epifierrors"

	rpcPb "github.com/epifi/be-common/api/rpc"

	"github.com/epifi/gamma/api/cx/call_routing"
	"github.com/epifi/gamma/risk/config/genconf"

	structPb "google.golang.org/protobuf/types/known/structpb"

	commsPb "github.com/epifi/gamma/api/comms"
	mockComms "github.com/epifi/gamma/api/comms/mocks"

	calRoutingEventPb "github.com/epifi/gamma/api/cx/call_routing/event"
	riskPb "github.com/epifi/gamma/api/risk"
	caseManagementPb "github.com/epifi/gamma/api/risk/case_management"
	cmEnumsPb "github.com/epifi/gamma/api/risk/case_management/enums"
	formPb "github.com/epifi/gamma/api/risk/case_management/form"

	"github.com/epifi/gamma/risk/bankactions/comms"
	"github.com/epifi/gamma/risk/bankactions/comms/mocks"
	"github.com/epifi/gamma/risk/case_management/consumer/processors"

	daoMocks "github.com/epifi/gamma/risk/case_management/dao/mocks"
	formErrors "github.com/epifi/gamma/risk/case_management/form/errors"
	mockForm "github.com/epifi/gamma/risk/case_management/form/mocks"

	"github.com/epifi/gamma/risk/config"

	mockRiskDao "github.com/epifi/gamma/risk/dao/mocks"
	mockEssential "github.com/epifi/gamma/risk/essential/mocks"
	mockConsumer "github.com/epifi/gamma/risk/test/mocks/case_management/consumer"
	mockProcessors "github.com/epifi/gamma/risk/test/mocks/case_management/consumer/processors"

	mockCelestial "github.com/epifi/be-common/api/celestial/mocks"

	"github.com/golang/mock/gomock"

	"google.golang.org/protobuf/types/known/timestamppb"

	celestialPb "github.com/epifi/be-common/api/celestial"

	actorPb "github.com/epifi/gamma/api/actor"
	mockActorPb "github.com/epifi/gamma/api/actor/mocks"
	mockPaymentPb "github.com/epifi/gamma/api/order/payment/mocks"
	typesPb "github.com/epifi/gamma/api/typesv2"
)

type mockedDependencies struct {
	mockFactory                       *mockConsumer.MockIProcessorFactory
	mockProcessor                     *mockProcessors.MockPayloadProcessor
	mockBatchProcessor                *mockProcessors.MockPayloadBatchProcessor
	mockBankActionCommsBuilder        *mocks.MockBuilder
	mockBankActionCommsBuilderFactory *mocks.MockBuilderFactory
	mockCommsClient                   *mockComms.MockCommsClient
	mockBankActionsDao                *mockRiskDao.MockRiskBankActionsDao
	mockActorManager                  *mockEssential.MockActorManager
	mockFormHandler                   *mockForm.MockHandler
	mockFormDao                       *daoMocks.MockFormDao
	mockActorClient                   *mockActorPb.MockActorClient
	mockPaymentClient                 *mockPaymentPb.MockPaymentClient
}

func newServiceWithMocks(t *testing.T) (*CaseManagementConsumerService, *mockedDependencies, func()) {
	ctr := gomock.NewController(t)

	mockFactory := mockConsumer.NewMockIProcessorFactory(ctr)
	mockProcessor := mockProcessors.NewMockPayloadProcessor(ctr)
	mockBatchProcessor := mockProcessors.NewMockPayloadBatchProcessor(ctr)
	mockFormHandler := mockForm.NewMockHandler(ctr)
	mockFormDao := daoMocks.NewMockFormDao(ctr)

	md := &mockedDependencies{
		mockFactory:                       mockFactory,
		mockProcessor:                     mockProcessor,
		mockBatchProcessor:                mockBatchProcessor,
		mockBankActionCommsBuilder:        mocks.NewMockBuilder(ctr),
		mockBankActionCommsBuilderFactory: mocks.NewMockBuilderFactory(ctr),
		mockCommsClient:                   mockComms.NewMockCommsClient(ctr),
		mockBankActionsDao:                mockRiskDao.NewMockRiskBankActionsDao(ctr),
		mockActorManager:                  mockEssential.NewMockActorManager(ctr),
		mockFormHandler:                   mockFormHandler,
		mockFormDao:                       mockFormDao,
		mockActorClient:                   mockActorPb.NewMockActorClient(ctr),
		mockPaymentClient:                 mockPaymentPb.NewMockPaymentClient(ctr),
	}

	confGen, err := genconf.Load()
	if err != nil {
		log.Fatal("failed to load dynamic config", err)
	}
	svc := &CaseManagementConsumerService{
		payloadProcessorFactory:       mockFactory,
		cfg:                           riskDependencies.Conf,
		bankActionCommsBuilderFactory: md.mockBankActionCommsBuilderFactory,
		commsClient:                   md.mockCommsClient,
		bankActionDao:                 md.mockBankActionsDao,
		actorManager:                  md.mockActorManager,
		formHandler:                   mockFormHandler,
		formDao:                       mockFormDao,
		actorClient:                   md.mockActorClient,
		paymentClient:                 md.mockPaymentClient,
		genConf:                       confGen,
	}

	return svc, md,
		func() {
			ctr.Finish()
		}
}

var (
	RiskCasesIngestEvent1 = &caseManagementPb.RiskCasesIngestEvent{
		RiskCases: []*caseManagementPb.RiskCase{
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS,
				Payload: &caseManagementPb.RiskCase_LivenessReview{
					LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
				},
			},
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW,
				Payload: &caseManagementPb.RiskCase_LivenessReview{
					LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
				},
			},
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW,
				Payload: &caseManagementPb.RiskCase_TransactionReview{
					TransactionReview: &caseManagementPb.TransacionReview{
						ActorId:   "test_actor_1",
						AccountId: "test_account_1_1",
						TxnId:     "test_txn_1_1",
					},
				},
			},
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW,
				Payload: &caseManagementPb.RiskCase_LivenessReview{
					LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
				},
			},
		},
	}
	RiskCasesIngestEvent2 = &caseManagementPb.RiskCasesIngestEvent{
		RiskCases: []*caseManagementPb.RiskCase{
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS,
				Payload: &caseManagementPb.RiskCase_LivenessReview{
					LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
				},
			},
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS,
				Payload: &caseManagementPb.RiskCase_LivenessReview{
					LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_2"},
				},
			},
			{
				PayloadType: caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW,
				Payload: &caseManagementPb.RiskCase_TransactionReview{
					TransactionReview: &caseManagementPb.TransacionReview{
						ActorId:   "test_actor_1",
						AccountId: "test_account_1_1",
						TxnId:     "test_txn_1_1",
					},
				},
			},
		},
	}
)

func TestCaseManagementConsumerService_ProcessRiskCasesIngestEvent(t *testing.T) {
	type args struct {
		ctx   context.Context
		cfg   *config.Config
		event *caseManagementPb.RiskCasesIngestEvent
	}
	tests := []struct {
		name    string
		args    args
		mocks   func(mock *mockedDependencies, args args)
		want    *caseManagementPb.AddCasesResponse
		wantErr bool
	}{
		{
			name: "factory failure for all risk cases",
			args: args{
				ctx:   context.Background(),
				cfg:   riskDependencies.Conf,
				event: RiskCasesIngestEvent1,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
			},
			want:    &caseManagementPb.AddCasesResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "processor failure for all risk cases",
			args: args{
				ctx:   context.Background(),
				cfg:   riskDependencies.Conf,
				event: RiskCasesIngestEvent1,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW).
					Return(mock.mockBatchProcessor, nil).Times(2)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
				inputs := []*processors.Input{
					{
						Payload:        RiskCasesIngestEvent1.GetRiskCases()[2].GetPayload(),
						RuleIdentifier: RiskCasesIngestEvent1.GetRiskCases()[2].GetRuleIdentifier(),
					},
					{
						Payload:        RiskCasesIngestEvent1.GetRiskCases()[3].GetPayload(),
						RuleIdentifier: RiskCasesIngestEvent1.GetRiskCases()[3].GetRuleIdentifier(),
					},
				}
				mock.mockBatchProcessor.EXPECT().ProcessPayload(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW, inputs).
					Return([]*processors.Failure{
						{
							Payload: &caseManagementPb.RiskCase_LivenessReview{
								LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
							},
							Err: errors.New("invalid payload passed for payload type"),
						},
						{
							Payload: &caseManagementPb.RiskCase_TransactionReview{
								TransactionReview: &caseManagementPb.TransacionReview{
									ActorId:   "test_actor_1",
									AccountId: "test_account_1_1",
									TxnId:     "test_txn_1_1",
								},
							},
							Err: errors.New("create alerts error"),
						},
					}).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(mock.mockProcessor, nil).Times(2)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent1.GetRiskCases()[0].GetPayloadType(), RiskCasesIngestEvent1.GetRiskCases()[0].GetPayload()).
					Return(errors.New("failed")).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(mock.mockProcessor, nil).Times(2)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent1.GetRiskCases()[1].GetPayloadType(), RiskCasesIngestEvent1.GetRiskCases()[1].GetPayload()).
					Return(errors.New("failed")).Times(1)
			},
			want:    &caseManagementPb.AddCasesResponse{ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE}},
			wantErr: false,
		},
		{
			name: "partial case failures",
			args: args{
				ctx:   context.Background(),
				cfg:   riskDependencies.Conf,
				event: RiskCasesIngestEvent1,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW).
					Return(mock.mockBatchProcessor, nil).Times(2)
				// success case
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(mock.mockProcessor, nil).Times(2)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent1.GetRiskCases()[0].GetPayloadType(), RiskCasesIngestEvent1.GetRiskCases()[0].GetPayload()).
					Return(nil).Times(1)
				// failure case
				inputs := []*processors.Input{
					{
						Payload:        RiskCasesIngestEvent1.GetRiskCases()[2].GetPayload(),
						RuleIdentifier: RiskCasesIngestEvent1.GetRiskCases()[2].GetRuleIdentifier(),
					},
					{
						Payload:        RiskCasesIngestEvent1.GetRiskCases()[3].GetPayload(),
						RuleIdentifier: RiskCasesIngestEvent1.GetRiskCases()[3].GetRuleIdentifier(),
					},
				}
				mock.mockBatchProcessor.EXPECT().ProcessPayload(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW,
					inputs).Return([]*processors.Failure{
					{
						Payload: &caseManagementPb.RiskCase_LivenessReview{
							LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test_actor_1"},
						},
						Err: errors.New("invalid payload passed for payload type"),
					},
				}).Times(1)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_USER_REVIEW).
					Return(mock.mockProcessor, nil).Times(2)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent1.GetRiskCases()[1].GetPayloadType(), RiskCasesIngestEvent1.GetRiskCases()[1].GetPayload()).
					Return(errors.New("failed")).Times(1)
			},
			want: &caseManagementPb.AddCasesResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}},
			wantErr: false,
		},
		{
			name: "all success case",
			args: args{
				ctx:   context.Background(),
				cfg:   riskDependencies.Conf,
				event: RiskCasesIngestEvent2,
			},
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(nil, errors.New("invalid payload type")).Times(1)
				mock.mockFactory.EXPECT().GetBatchProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW).
					Return(mock.mockBatchProcessor, nil).Times(2)
				mock.mockFactory.EXPECT().GetProcessor(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_LIVENESS).
					Return(mock.mockProcessor, nil).Times(2)
				// success case
				inputs := []*processors.Input{
					{
						Payload:        RiskCasesIngestEvent2.GetRiskCases()[2].GetPayload(),
						RuleIdentifier: RiskCasesIngestEvent2.GetRiskCases()[2].GetRuleIdentifier(),
					},
				}
				mock.mockBatchProcessor.EXPECT().ProcessPayload(context.Background(), caseManagementPb.PayloadType_PAYLOAD_TYPE_TRANSACTION_REVIEW,
					inputs).Return(nil).Times(1)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent2.GetRiskCases()[0].GetPayloadType(), RiskCasesIngestEvent2.GetRiskCases()[0].GetPayload()).
					Return(nil).Times(1)
				mock.mockProcessor.EXPECT().ProcessPayload(context.Background(),
					RiskCasesIngestEvent2.GetRiskCases()[1].GetPayloadType(), RiskCasesIngestEvent2.GetRiskCases()[1].GetPayload()).
					Return(nil).Times(1)
			},
			want: &caseManagementPb.AddCasesResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{Status: queue.MessageConsumptionStatus_SUCCESS}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps, assertTest := newServiceWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := s.AddCases(tt.args.ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRiskCasesIngestEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRiskCasesIngestEvent() got = %v, want %v", got, tt.want)
			}
			assertTest()
		})
	}
}

func Test_getIdentifierFromPayload(t *testing.T) {
	type args struct {
		riskCase *caseManagementPb.RiskCase
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "liveness review",
			args: args{
				riskCase: &caseManagementPb.RiskCase{
					Payload: &caseManagementPb.RiskCase_LivenessReview{
						LivenessReview: &caseManagementPb.LivenessReview{ActorId: "test-actor-1"},
					},
				},
			},
			want: "test-actor-1",
		},
		{
			name: "user review",
			args: args{
				riskCase: &caseManagementPb.RiskCase{
					Payload: &caseManagementPb.RiskCase_UserReview{
						UserReview: &caseManagementPb.UserReview{ActorId: "test-actor-2"},
					},
				},
			},
			want: "test-actor-2",
		},
		{
			name: "liveness sample review",
			args: args{
				riskCase: &caseManagementPb.RiskCase{
					Payload: &caseManagementPb.RiskCase_LivenessSampleReview{
						LivenessSampleReview: &caseManagementPb.LivenessSampleReview{ActorId: "test-actor-3", RequestId: "request-id-1"},
					},
				},
			},
			want: "test-actor-3::request-id-1",
		},
		{
			name: "txn review",
			args: args{
				riskCase: &caseManagementPb.RiskCase{
					Payload: &caseManagementPb.RiskCase_TransactionReview{
						TransactionReview: &caseManagementPb.TransacionReview{ActorId: "test-actor-3", AccountId: "account-id-3-1", TxnId: "txn-id-3-1"},
					},
				},
			},
			want: "test-actor-3::account-id-3-1::txn-id-3-1",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getIdentifierFromPayload(tt.args.riskCase.GetPayload()); got != tt.want {
				t.Errorf("getIdentifierFromPayload() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaseManagementConsumerService_ProcessCallRoutingEvent(t *testing.T) {
	var (
		actorId1  = "actor-1"
		entityId1 = "entity-1"
		actor1    = &typesPb.Actor{
			Id:       actorId1,
			EntityId: entityId1,
		}
		nonRiskEvent = &calRoutingEventPb.CallRoutingEvent{
			CallRoutingEventType: call_routing.CallRoutingEventType_CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE,
			Event: &calRoutingEventPb.CallRoutingEvent_PreRecordedMessageEvent{
				PreRecordedMessageEvent: &calRoutingEventPb.PreRecordedMessageEvent{
					ActorId:             actorId1,
					RecordingIdentifier: call_routing.RecordingIdentifier_RECORDING_IDENTIFIER_SCREENER_REJECT,
				},
			},
		}
		eventWithMissingActor = &calRoutingEventPb.CallRoutingEvent{
			CallRoutingEventType: call_routing.CallRoutingEventType_CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE,
			Event: &calRoutingEventPb.CallRoutingEvent_PreRecordedMessageEvent{
				PreRecordedMessageEvent: &calRoutingEventPb.PreRecordedMessageEvent{
					ActorId:             "",
					RecordingIdentifier: call_routing.RecordingIdentifier_RECORDING_IDENTIFIER_HIGH_RISK_SCORE,
				},
			},
		}
		validEvent = &calRoutingEventPb.CallRoutingEvent{
			CallRoutingEventType: call_routing.CallRoutingEventType_CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE,
			Event: &calRoutingEventPb.CallRoutingEvent_PreRecordedMessageEvent{
				PreRecordedMessageEvent: &calRoutingEventPb.PreRecordedMessageEvent{
					ActorId:             actorId1,
					RecordingIdentifier: call_routing.RecordingIdentifier_RECORDING_IDENTIFIER_HIGH_RISK_SCORE,
				},
			},
		}
		bankAction1 = &riskPb.RiskBankActions{
			Id: "1",
		}
		commsList1 = []*commsPb.Communication{
			{
				Medium: commsPb.Medium_EMAIL,
			},
		}
		sendBatchReq1 = &commsPb.SendMessageBatchRequest{
			Type: commsPb.QoS_BEST_EFFORT,
			UserIdentifier: &commsPb.SendMessageBatchRequest_UserId{
				UserId: entityId1,
			},
			CommunicationList: commsList1,
		}
	)
	type args struct {
		ctx   context.Context
		event *calRoutingEventPb.CallRoutingEvent
	}
	tests := []struct {
		name    string
		mocks   func(mock *mockedDependencies, args args)
		args    args
		want    *caseManagementPb.ProcessCallRoutingEventResponse
		wantErr bool
	}{
		{
			name:  "success with processing skipped since not a risk event",
			mocks: func(mock *mockedDependencies, args args) {},
			args: args{
				ctx:   context.Background(),
				event: nonRiskEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name:  "failed since actor id missing in req",
			mocks: func(mock *mockedDependencies, args args) {},
			args: args{
				ctx:   context.Background(),
				event: eventWithMissingActor,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to error while fetching actor details",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(nil, fmt.Errorf("failed"))
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to error while fetching bank actions",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return(nil, fmt.Errorf("failed"))
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to error while getting comms builder implementation",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(nil, fmt.Errorf("failed"))
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to transient error while builder comms object",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(mock.mockBankActionCommsBuilder, nil)
				mock.mockFormDao.EXPECT().Get(context.Background(), formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
					Kind: &structPb.Value_StringValue{StringValue: actorId1},
				}, 0).Return([]*formPb.Form{
					{
						Id:     "form-id-1",
						Origin: formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
						Status: formPb.Status_STATUS_SENT,
					},
				}, nil)
				mock.mockBankActionCommsBuilder.EXPECT().GetNotifications(context.Background(), bankAction1, "form-id-1", true).Return(nil, fmt.Errorf("failed"))
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to permanent error while builder comms object",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(mock.mockBankActionCommsBuilder, nil)
				mock.mockFormDao.EXPECT().Get(context.Background(), formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
					Kind: &structPb.Value_StringValue{StringValue: actorId1},
				}, 0).Return([]*formPb.Form{
					{
						Id:     "form-id-1",
						Origin: formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
						Status: formPb.Status_STATUS_SENT,
					},
				}, nil)
				mock.mockBankActionCommsBuilder.EXPECT().GetNotifications(context.Background(), bankAction1, "form-id-1", true).Return(nil, epifierrors.ErrPermanent)
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "success since sending comms is not required",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(mock.mockBankActionCommsBuilder, nil)
				mock.mockFormDao.EXPECT().Get(context.Background(), formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
					Kind: &structPb.Value_StringValue{StringValue: actorId1},
				}, 0).Return([]*formPb.Form{
					{
						Id:     "form-id-1",
						Origin: formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
						Status: formPb.Status_STATUS_SENT,
					},
				}, nil)
				mock.mockBankActionCommsBuilder.EXPECT().GetNotifications(context.Background(), bankAction1, "form-id-1", true).Return(nil, comms.ErrCommsNotRequiredForAction)
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "failed due to error in comms service",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(mock.mockBankActionCommsBuilder, nil)
				mock.mockFormDao.EXPECT().Get(context.Background(), formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
					Kind: &structPb.Value_StringValue{StringValue: actorId1},
				}, 0).Return([]*formPb.Form{
					{
						Id:     "form-id-1",
						Origin: formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
						Status: formPb.Status_STATUS_SENT,
					},
				}, nil)
				mock.mockBankActionCommsBuilder.EXPECT().GetNotifications(context.Background(), bankAction1, "form-id-1", true).Return(commsList1, nil)
				mock.mockCommsClient.EXPECT().SendMessageBatch(context.Background(), sendBatchReq1).Return(&commsPb.SendMessageBatchResponse{Status: rpcPb.StatusInternal()}, nil)
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "success",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockActorManager.EXPECT().GetByActorId(context.Background(), actorId1).Return(actor1, nil)
				mock.mockBankActionsDao.EXPECT().GetByActor(context.Background(), actorId1, 1).Return([]*riskPb.RiskBankActions{bankAction1}, nil)
				mock.mockBankActionCommsBuilderFactory.EXPECT().GetBuilderForAction(context.Background(), bankAction1).Return(mock.mockBankActionCommsBuilder, nil)
				mock.mockFormDao.EXPECT().Get(context.Background(), formPb.FormFieldMask_FORM_FIELD_MASK_ACTOR_ID, &structPb.Value{
					Kind: &structPb.Value_StringValue{StringValue: actorId1},
				}, 0).Return([]*formPb.Form{
					{
						Id:     "form-id-1",
						Origin: formPb.FormOrigin_FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE,
						Status: formPb.Status_STATUS_SENT,
					},
				}, nil)
				mock.mockBankActionCommsBuilder.EXPECT().GetNotifications(context.Background(), bankAction1, "form-id-1", true).Return(commsList1, nil)
				mock.mockCommsClient.EXPECT().SendMessageBatch(context.Background(), sendBatchReq1).Return(&commsPb.SendMessageBatchResponse{Status: rpcPb.StatusOk()}, nil)
			},
			args: args{
				ctx:   context.Background(),
				event: validEvent,
			},
			want: &caseManagementPb.ProcessCallRoutingEventResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps, assertMocks := newServiceWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := s.ProcessCallRoutingEvent(tt.args.ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessCallRoutingEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessCallRoutingEvent() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestCaseManagementConsumerService_ProcessFormSubmission(t *testing.T) {
	ctx := context.Background()
	req := &caseManagementPb.FormSubmissionEvent{
		FormId: "formId",
	}
	type args struct {
		event *caseManagementPb.FormSubmissionEvent
	}
	tests := []struct {
		name    string
		mocks   func(mock *mockedDependencies, args args)
		args    args
		want    *caseManagementPb.ProcessFormSubmissionResponse
		wantErr bool
	}{
		{
			name: "permanent failure for for expired",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(formErrors.ErrFormExpired)
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "permanent failure for for already submitted",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(formErrors.ErrFormAlreadySubmitted)
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "permanent failure for for form not found",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(formErrors.ErrFormNotFound)
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "permanent failure for invalid response",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(formErrors.ErrInvalidResponse)
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "transient failure",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(errors.New("failed"))
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "success",
			mocks: func(mock *mockedDependencies, args args) {
				mock.mockFormHandler.EXPECT().SubmitForm(ctx, req.GetFormId(), req.GetQuestionResponses()).
					Return(nil)
			},
			args: args{
				event: req,
			},
			want: &caseManagementPb.ProcessFormSubmissionResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status:         queue.MessageConsumptionStatus_SUCCESS,
					GrpcStatusCode: rpcPb.StatusOk(),
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s, mockedDeps, assertMocks := newServiceWithMocks(t)
			if tt.mocks != nil {
				tt.mocks(mockedDeps, tt.args)
			}
			got, err := s.ProcessFormSubmission(ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessFormSubmission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessFormSubmission() got = %v, want %v", got, tt.want)
			}
			assertMocks()
		})
	}
}

func TestCaseManagementConsumerService_ProcessRiskSignalEvent(t *testing.T) {
	ctx := context.Background()
	actorId := "actor-1"
	accountId := "account-1"
	ruleId := "rule-1"
	externalRuleId := "ext-rule-1"
	batchName := "batch-1"

	// Create RawAlert object instead of Alert
	rawAlert := &caseManagementPb.RawAlert{
		ActorId:     actorId,
		AccountId:   accountId,
		EntityType:  cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
		EntityId:    "entity-1",
		BatchName:   batchName,
		InitiatedAt: timestamppb.Now(),
		Identifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_RuleId{
				RuleId: ruleId,
			},
		},
		MetaDetails: &caseManagementPb.AlertMetaDetails{
			TransactionBlocks: []*caseManagementPb.TransactionBlock{
				{
					Id:      "txn-block-1",
					ActorId: actorId,
				},
			},
		},
	}

	// Create event with the RawAlert
	event := &caseManagementPb.RiskSignalIngestEvent{
		Alert: &caseManagementPb.RiskSignalIngestEvent_Alert{
			RawAlert:   rawAlert,
			Provenance: cmEnumsPb.Provenance_PROVENANCE_DATA_ANALYTICS,
		},
	}

	// Create expected Alert after conversion
	alertAfterConversion := &caseManagementPb.Alert{
		Id:           "alert-1",
		ActorId:      actorId,
		AccountId:    accountId,
		RuleId:       ruleId,
		EntityType:   cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
		EntityId:     "entity-1",
		BatchName:    batchName,
		Verdict:      cmEnumsPb.Verdict_VERDICT_UNSPECIFIED,
		HandlingType: cmEnumsPb.AlertHandlingType_ALERT_HANDLING_TYPE_UNSPECIFIED,
		InitiatedAt:  rawAlert.GetInitiatedAt(),
		MetaDetails:  rawAlert.GetMetaDetails(),
	}

	// Create rule objects for different states
	activeRule := &caseManagementPb.Rule{
		Id:         ruleId,
		ExternalId: externalRuleId,
		State:      caseManagementPb.RuleState_RULE_STATE_ACTIVE,
	}

	inactiveRule := &caseManagementPb.Rule{
		Id:         ruleId,
		ExternalId: externalRuleId,
		State:      caseManagementPb.RuleState_RULE_STATE_INACTIVE,
	}

	shadowRule := &caseManagementPb.Rule{
		Id:         ruleId,
		ExternalId: externalRuleId,
		State:      caseManagementPb.RuleState_RULE_STATE_SHADOW,
	}

	type args struct {
		event *caseManagementPb.RiskSignalIngestEvent
	}
	tests := []struct {
		name    string
		mocks   func(s *CaseManagementConsumerService, ctrl *gomock.Controller)
		args    args
		want    *caseManagementPb.RiskSignalIngestResponse
		wantErr bool
	}{
		{
			name: "rule not found",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return(nil, epifierrors.ErrRecordNotFound)
				s.ruleDao = mockRuleDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "inactive rule",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{inactiveRule}, nil)
				s.ruleDao = mockRuleDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "shadow rule success",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{shadowRule}, nil)
				s.ruleDao = mockRuleDao

				mockAlertDao := daoMocks.NewMockAlertDao(ctrl)
				// Expect alert creation with converted RawAlert
				mockAlertDao.EXPECT().Create(ctx, gomock.Any()).DoAndReturn(
					func(_ context.Context, alert *caseManagementPb.Alert) (*caseManagementPb.Alert, error) {
						// Verify alert was properly converted from RawAlert
						if alert.GetRuleId() != ruleId ||
							alert.GetActorId() != actorId ||
							alert.GetAccountId() != accountId ||
							alert.GetVerdict() != cmEnumsPb.Verdict_VERDICT_UNSPECIFIED ||
							alert.GetHandlingType() != cmEnumsPb.AlertHandlingType_ALERT_HANDLING_TYPE_UNSPECIFIED {
							t.Errorf("Alert conversion incorrect: %v", alert)
						}
						return alertAfterConversion, nil
					})
				s.alertDao = mockAlertDao

				mockTxnBlockDao := daoMocks.NewMockTransactionBlockDao(ctrl)
				// Expect transaction block creation
				mockTxnBlockDao.EXPECT().Create(ctx, gomock.Any()).Return(&caseManagementPb.TransactionBlock{}, nil)
				s.transactionBlockDao = mockTxnBlockDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "shadow rule failure on alert creation",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{shadowRule}, nil)
				s.ruleDao = mockRuleDao

				mockAlertDao := daoMocks.NewMockAlertDao(ctrl)
				// Expect alert creation to fail
				mockAlertDao.EXPECT().Create(ctx, gomock.Any()).Return(nil, errors.New("failed to create alert"))
				s.alertDao = mockAlertDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "active rule with active alert queue enabled - should return success",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{activeRule}, nil)
				s.ruleDao = mockRuleDao

				// Mock celestialClient and expect workflow initiation since ProcessActiveRuleAlerts is enabled by default
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestialPb.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				s.celestialClient = mockCelestialClient
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "active rule with active alert queue disabled - should return success",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{activeRule}, nil)
				s.ruleDao = mockRuleDao

				// Mock celestialClient and expect workflow initiation since ProcessActiveRuleAlerts is enabled by default
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestialPb.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				s.celestialClient = mockCelestialClient
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Load static config
			staticConf, _ := config.Load()
			// Load dynamic genConf like in risk service tests
			confGen, err := genconf.Load()
			if err != nil {
				log.Fatal("failed to load dynamic config", err)
			}

			// Create service instance with loaded configs
			s := &CaseManagementConsumerService{
				cfg:     staticConf,
				genConf: confGen,
			}

			if tt.mocks != nil {
				tt.mocks(s, ctrl)
			}

			// Add missing dependencies to prevent panics
			if s.celestialClient == nil {
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				s.celestialClient = mockCelestialClient
			}
			if s.actorClient == nil {
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				s.actorClient = mockActorClient
			}
			if s.paymentClient == nil {
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient
			}

			got, err := s.ProcessRiskSignalEvent(ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRiskSignalEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRiskSignalEvent() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCaseManagementConsumerService_ProcessRiskAlertEvent(t *testing.T) {
	ctx := context.Background()
	actorId := "actor-1"
	accountId := "account-1"
	ruleId := "rule-1"
	externalRuleId := "ext-rule-1"
	batchName := "batch-1"

	// Create RawAlert object
	rawAlert := &caseManagementPb.RawAlert{
		ActorId:     actorId,
		AccountId:   accountId,
		EntityType:  cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
		EntityId:    "entity-1",
		BatchName:   batchName,
		InitiatedAt: timestamppb.Now(),
		Identifier: &caseManagementPb.RuleIdentifier{
			Identifier: &caseManagementPb.RuleIdentifier_RuleId{
				RuleId: ruleId,
			},
		},
		MetaDetails: &caseManagementPb.AlertMetaDetails{
			TransactionBlocks: []*caseManagementPb.TransactionBlock{
				{
					Id:      "txn-block-1",
					ActorId: actorId,
				},
			},
		},
	}

	// Create event with the RawAlert
	event := &caseManagementPb.RiskSignalIngestEvent{
		Alert: &caseManagementPb.RiskSignalIngestEvent_Alert{
			RawAlert:   rawAlert,
			Provenance: cmEnumsPb.Provenance_PROVENANCE_DATA_ANALYTICS,
		},
	}

	// Create rule objects for different states
	activeRule := &caseManagementPb.Rule{
		Id:         ruleId,
		ExternalId: externalRuleId,
		State:      caseManagementPb.RuleState_RULE_STATE_ACTIVE,
	}

	shadowRule := &caseManagementPb.Rule{
		Id:         ruleId,
		ExternalId: externalRuleId,
		State:      caseManagementPb.RuleState_RULE_STATE_SHADOW,
	}

	type args struct {
		event *caseManagementPb.RiskSignalIngestEvent
	}
	tests := []struct {
		name    string
		mocks   func(s *CaseManagementConsumerService, ctrl *gomock.Controller)
		args    args
		want    *caseManagementPb.RiskSignalIngestResponse
		wantErr bool
	}{
		{
			name: "rule not found",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return(nil, epifierrors.ErrRecordNotFound)
				s.ruleDao = mockRuleDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "shadow rule - should return permanent failure",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{shadowRule}, nil)
				s.ruleDao = mockRuleDao
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_PERMANENT_FAILURE,
				},
			},
			wantErr: false,
		},
		{
			name: "active rule success",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{activeRule}, nil)
				s.ruleDao = mockRuleDao

				// Mock celestialClient and expect workflow initiation
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestialPb.InitiateWorkflowResponse{
					Status: rpcPb.StatusOk(),
				}, nil)
				s.celestialClient = mockCelestialClient
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_SUCCESS,
				},
			},
			wantErr: false,
		},
		{
			name: "active rule workflow initiation failure",
			mocks: func(s *CaseManagementConsumerService, ctrl *gomock.Controller) {
				// Set up actor client for validation
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: actorId,
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: actorId},
				}, nil)
				s.actorClient = mockActorClient

				// Set up payment client for validation
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient

				mockRuleDao := daoMocks.NewMockRuleDao(ctrl)
				mockRuleDao.EXPECT().GetBulkById(ctx, []string{ruleId}).Return([]*caseManagementPb.Rule{activeRule}, nil)
				s.ruleDao = mockRuleDao

				// Mock celestialClient and expect workflow initiation to fail
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				mockCelestialClient.EXPECT().InitiateWorkflow(ctx, gomock.Any()).Return(&celestialPb.InitiateWorkflowResponse{
					Status: rpcPb.StatusInternal(),
				}, nil)
				s.celestialClient = mockCelestialClient
			},
			args: args{
				event: event,
			},
			want: &caseManagementPb.RiskSignalIngestResponse{
				ResponseHeader: &queue.ConsumerResponseHeader{
					Status: queue.MessageConsumptionStatus_TRANSIENT_FAILURE,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			// Load static config
			staticConf, _ := config.Load()
			// Load dynamic genConf like in risk service tests
			confGen, err := genconf.Load()
			if err != nil {
				log.Fatal("failed to load dynamic config", err)
			}

			// Create service instance with loaded configs
			s := &CaseManagementConsumerService{
				cfg:     staticConf,
				genConf: confGen,
			}

			if tt.mocks != nil {
				tt.mocks(s, ctrl)
			}

			// Add missing dependencies to prevent panics
			if s.celestialClient == nil {
				mockCelestialClient := mockCelestial.NewMockCelestialClient(ctrl)
				s.celestialClient = mockCelestialClient
			}
			if s.actorClient == nil {
				mockActorClient := mockActorPb.NewMockActorClient(ctrl)
				s.actorClient = mockActorClient
			}
			if s.paymentClient == nil {
				mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)
				s.paymentClient = mockPaymentClient
			}

			got, err := s.ProcessRiskAlertEvent(ctx, tt.args.event)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProcessRiskAlertEvent() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ProcessRiskAlertEvent() got = %v, want = %v", got, tt.want)
			}
		})
	}
}

func TestCaseManagementConsumerService_validateRawAlertObject(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Create mock clients
	mockActorClient := mockActorPb.NewMockActorClient(ctrl)
	mockPaymentClient := mockPaymentPb.NewMockPaymentClient(ctrl)

	type args struct {
		alert *caseManagementPb.RawAlert
	}
	tests := []struct {
		name       string
		setupMocks func()
		args       args
		wantErr    bool
		errMsg     string
	}{
		{
			name: "valid ENTITY_TYPE_USER with valid actor_id",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "valid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: "valid-actor-id"},
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_USER,
					EntityId:   "valid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid ENTITY_TYPE_USER with non-existent actor_id",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "invalid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusRecordNotFound(),
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_USER,
					EntityId:   "invalid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "invalid actor_id for ENTITY_TYPE_USER",
		},
		{
			name: "valid ENTITY_TYPE_TRANSACTION with valid transaction_id and actor_id",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "valid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: "valid-actor-id"},
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION,
					EntityId:   "valid-txn-id",
					ActorId:    "valid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "valid ENTITY_TYPE_TRANSACTION_BLOCK with valid actor_id and meta details",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "valid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: "valid-actor-id"},
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
					EntityId:   "some-entity-id",
					ActorId:    "valid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
					MetaDetails: &caseManagementPb.AlertMetaDetails{
						TransactionBlocks: []*caseManagementPb.TransactionBlock{
							{
								Id:      "block-1",
								ActorId: "valid-actor-id",
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "invalid ENTITY_TYPE_TRANSACTION_BLOCK missing meta details",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "valid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: "valid-actor-id"},
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
					EntityId:   "some-entity-id",
					ActorId:    "valid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
					MetaDetails: nil,
				},
			},
			wantErr: true,
			errMsg:  "meta details are required for ENTITY_TYPE_TRANSACTION_BLOCK",
		},
		{
			name: "invalid ENTITY_TYPE_TRANSACTION_BLOCK missing transaction blocks",
			setupMocks: func() {
				mockActorClient.EXPECT().GetActorById(ctx, &actorPb.GetActorByIdRequest{
					Id: "valid-actor-id",
				}).Return(&actorPb.GetActorByIdResponse{
					Status: rpcPb.StatusOk(),
					Actor:  &typesPb.Actor{Id: "valid-actor-id"},
				}, nil)
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_TRANSACTION_BLOCK,
					EntityId:   "some-entity-id",
					ActorId:    "valid-actor-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
					MetaDetails: &caseManagementPb.AlertMetaDetails{
						TransactionBlocks: []*caseManagementPb.TransactionBlock{},
					},
				},
			},
			wantErr: true,
			errMsg:  "transaction blocks are required in meta details for ENTITY_TYPE_TRANSACTION_BLOCK",
		},
		{
			name: "basic validation - missing entity type",
			setupMocks: func() {
				// No mocks needed for basic validation
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_UNSPECIFIED,
					EntityId:   "some-id",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "entity type field is mandatory",
		},
		{
			name: "basic validation - missing entity id",
			setupMocks: func() {
				// No mocks needed for basic validation
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_USER,
					EntityId:   "",
					Identifier: &caseManagementPb.RuleIdentifier{
						Identifier: &caseManagementPb.RuleIdentifier_RuleId{
							RuleId: "rule-1",
						},
					},
				},
			},
			wantErr: true,
			errMsg:  "entity id field is mandatory",
		},
		{
			name: "basic validation - missing rule identifier",
			setupMocks: func() {
				// No mocks needed for basic validation
			},
			args: args{
				alert: &caseManagementPb.RawAlert{
					EntityType: cmEnumsPb.EntityType_ENTITY_TYPE_USER,
					EntityId:   "some-id",
					Identifier: nil,
				},
			},
			wantErr: true,
			errMsg:  "invalid rule identifier",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create service with mock clients
			s := &CaseManagementConsumerService{
				actorClient:   mockActorClient,
				paymentClient: mockPaymentClient,
			}

			// Setup mocks
			tt.setupMocks()

			// Execute the validation
			err := s.validateRawAlertObject(ctx, tt.args.alert)

			// Check results
			if tt.wantErr {
				if err == nil {
					t.Errorf("validateRawAlertObject() expected error but got none")
					return
				}
				if tt.errMsg != "" && !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("validateRawAlertObject() error = %v, expected to contain %v", err, tt.errMsg)
				}
			} else if err != nil {
				t.Errorf("validateRawAlertObject() unexpected error = %v", err)
			}
		})
	}
}
