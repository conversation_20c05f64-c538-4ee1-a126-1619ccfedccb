// Code generated by tools/conf_gen/conf_gen.go
package genconf

import (
	"fmt"
	"reflect"
	"strings"
	"sync"
	"sync/atomic"

	time "time"

	questtypes "github.com/epifi/be-common/api/quest/types"
	cfg "github.com/epifi/be-common/pkg/cfg"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	gencfg "github.com/epifi/be-common/pkg/cfg/genconf"
	roarray "github.com/epifi/be-common/pkg/data_structs/roarray"
	syncmap "github.com/epifi/be-common/pkg/syncmap"
	questsdk "github.com/epifi/be-common/quest/sdk"
	helper "github.com/epifi/be-common/tools/conf_gen/helper"
	config "github.com/epifi/gamma/risk/config"
	common "github.com/epifi/gamma/risk/config/common"
)

type Config struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_HunterRollOutPercentage                         int64
	_TestFlag                                        uint32
	_RuleTags                                        roarray.ROArray[string]
	_RuleTagsMutex                                   *sync.RWMutex
	_FeatureFlags                                    *FeatureFlags
	_ProcessRedListUpdateSqsSubscriber               *gencfg.SqsSubscriber
	_ProcessSyncLeaActorsSqsSubscriber               *gencfg.SqsSubscriber
	_ProcessRiskCasesIngestEventBEQueueSubscriber    *gencfg.SqsSubscriber
	_ProcessRiskCasesIngestEventDataQueueSubscriber  *gencfg.SqsSubscriber
	_ProcessRiskAlertIngestEventDSQueueSubscriber    *gencfg.SqsSubscriber
	_ProcessCallRoutingEventSqsSubscriber            *gencfg.SqsSubscriber
	_ProcessDisputeUploadSqsSubscriber               *gencfg.SqsSubscriber
	_ProcessFormSubmissionEventSqsSubscriber         *gencfg.SqsSubscriber
	_OnboardingVelocityConfig                        *OnboardingVelocityConfig
	_RiskAlertIngestionQueueSubscriber               *gencfg.SqsSubscriber
	_RiskBatchRuleEngineEventQueueSubscriber         *gencfg.SqsSubscriber
	_RiskCXTicketUpdateEventQueueSubscriber          *gencfg.SqsSubscriber
	_RiskAccountOperationStatusUpdateQueueSubscriber *gencfg.SqsSubscriber
	_ProcessRiskSignalEventQueueSubscriber           *gencfg.SqsSubscriber
	_ProcessMnrlReportSqsSubscriber                  *gencfg.SqsSubscriber
	_ProcessMnrlSuspectedFlaggedMobileSqsSubscriber  *gencfg.SqsSubscriber
	_ProcessRiskAlertEventQueueSubscriber            *gencfg.SqsSubscriber
	_OrderUpdateTxnMonitoringSubscriber              *gencfg.SqsSubscriber
	_CMConsumerConfig                                *CMConsumerConfig
	_Profile                                         *Profile
	_RedList                                         *RedListConfig
	_CasePriorityQueue                               *CasePriorityQueuesConfig
	_UpiVPANameMatchingCheckConfig                   *UpiVPANameMatchCheckConfig
	_AffluenceCheckConfig                            *AffluenceCheckConfig
	_ScreenerConfig                                  *ScreenerConfig
	_JunkEmailCheckConfig                            *JunkEmailChecksConfig
	_LocationModelCheck                              *LocationModelCheckConfig
	_DynamicElementsConfig                           *DynamicElementsConfig
	_InstalledAppsCheckConfig                        *InstalledAppsCheckConfig
	_CreditReportAffluenceClassConfig                *CreditReportAffluenceClassConfig
	_CrossVideoFacematchCheckConfig                  *CrossVideoFacematchCheckConfig
	_SAUnreviewedAlertsCheckConfig                   *SAUnreviewedAlertsCheckConfig
	_ContactAssociationsCheckConfig                  *ContactAssociationsCheckConfig
	_RuleHitCallbackSubscriber                       *gencfg.SqsSubscriber
	_ProfileBannerForSherlock                        *ProfileBannerForSherlock
	_ValidateSavingsAccountStatusConfig              *ValidateSavingsAccountStatusConfig
	_LowContactCountCheckConfig                      *LowContactCountCheckConfig
	_VPNPresenceCheckConfig                          *VPNPresenceCheckConfig
	_PercentageRolloutReleaseConfig                  *PercentageRolloutReleaseConfig
	_IPAddressRedListCheckConfig                     *IPAddressRedListCheckConfig
	_ContactAssociationAndRiskyProfileCheckConfig    *ContactAssociationAndRiskyProfileCheckConfig
	_Application                                     *config.Application
	_Server                                          *config.Server
	_EpifiDb                                         *cfg.DB
	_FRMDb                                           *cfg.DB
	_FRMPgdb                                         *cfg.DB
	_AWS                                             *config.Aws
	_IsFRMPgdbEnabled                                bool
	_RedListLatLongPrecisions                        []int
	_TxnMonitoringConfig                             *config.TxnMonitoringConfig
	_Secrets                                         *cfg.Secrets
	_Tracing                                         *cfg.Tracing
	_BlockingFlowErrorReportConfig                   *config.BlockingErrorReportConfig
	_LeaActorsPublisher                              *cfg.SqsPublisher
	_LEAComplaint                                    *config.LEAComplaintConfig
	_CaseStore                                       *common.CaseStoreConfig
	_FIProductToSegmentMap                           map[string]string
	_ContactAssociationsConfig                       *config.ContactAssociationsConfig
	_InvestigationEmail                              *common.InvestigationEmailConfig
	_Escalation                                      *config.EscalationConfig
	_RiskBankActionComms                             *common.RiskBankActionComms
	_RiskBankAction                                  *common.RiskBankAction
	_DronapayRuleHitCallbackPublisher                *cfg.SqsPublisher
	_FormSubmissionEventPublisher                    *cfg.SqsPublisher
	_AlertsPublisher                                 *cfg.SqsPublisher
	_BatchRuleEngineS3                               *config.BatchRuleEngineS3Config
	_WebForm                                         *config.WebFormConfig
	_UnifiedLEAComplaintConfig                       *common.UnifiedLeaComplaintConfig
	_WebFormConfig                                   *common.WebFormConfig
}

func (obj *Config) HunterRollOutPercentage() int {
	return int(atomic.LoadInt64(&obj._HunterRollOutPercentage))
}
func (obj *Config) TestFlag() bool {
	if atomic.LoadUint32(&obj._TestFlag) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *Config) RuleTags() roarray.ROArray[string] {
	obj._RuleTagsMutex.RLock()
	defer obj._RuleTagsMutex.RUnlock()
	return obj._RuleTags
}
func (obj *Config) FeatureFlags() *FeatureFlags {
	return obj._FeatureFlags
}
func (obj *Config) ProcessRedListUpdateSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRedListUpdateSqsSubscriber
}
func (obj *Config) ProcessSyncLeaActorsSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessSyncLeaActorsSqsSubscriber
}
func (obj *Config) ProcessRiskCasesIngestEventBEQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRiskCasesIngestEventBEQueueSubscriber
}
func (obj *Config) ProcessRiskCasesIngestEventDataQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRiskCasesIngestEventDataQueueSubscriber
}
func (obj *Config) ProcessRiskAlertIngestEventDSQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRiskAlertIngestEventDSQueueSubscriber
}
func (obj *Config) ProcessCallRoutingEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessCallRoutingEventSqsSubscriber
}
func (obj *Config) ProcessDisputeUploadSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessDisputeUploadSqsSubscriber
}
func (obj *Config) ProcessFormSubmissionEventSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessFormSubmissionEventSqsSubscriber
}
func (obj *Config) OnboardingVelocityConfig() *OnboardingVelocityConfig {
	return obj._OnboardingVelocityConfig
}
func (obj *Config) RiskAlertIngestionQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._RiskAlertIngestionQueueSubscriber
}
func (obj *Config) RiskBatchRuleEngineEventQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._RiskBatchRuleEngineEventQueueSubscriber
}
func (obj *Config) RiskCXTicketUpdateEventQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._RiskCXTicketUpdateEventQueueSubscriber
}
func (obj *Config) RiskAccountOperationStatusUpdateQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._RiskAccountOperationStatusUpdateQueueSubscriber
}
func (obj *Config) ProcessRiskSignalEventQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRiskSignalEventQueueSubscriber
}
func (obj *Config) ProcessMnrlReportSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessMnrlReportSqsSubscriber
}
func (obj *Config) ProcessMnrlSuspectedFlaggedMobileSqsSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessMnrlSuspectedFlaggedMobileSqsSubscriber
}
func (obj *Config) ProcessRiskAlertEventQueueSubscriber() *gencfg.SqsSubscriber {
	return obj._ProcessRiskAlertEventQueueSubscriber
}
func (obj *Config) OrderUpdateTxnMonitoringSubscriber() *gencfg.SqsSubscriber {
	return obj._OrderUpdateTxnMonitoringSubscriber
}
func (obj *Config) CMConsumerConfig() *CMConsumerConfig {
	return obj._CMConsumerConfig
}
func (obj *Config) Profile() *Profile {
	return obj._Profile
}
func (obj *Config) RedList() *RedListConfig {
	return obj._RedList
}
func (obj *Config) CasePriorityQueue() *CasePriorityQueuesConfig {
	return obj._CasePriorityQueue
}
func (obj *Config) UpiVPANameMatchingCheckConfig() *UpiVPANameMatchCheckConfig {
	return obj._UpiVPANameMatchingCheckConfig
}
func (obj *Config) AffluenceCheckConfig() *AffluenceCheckConfig {
	return obj._AffluenceCheckConfig
}
func (obj *Config) ScreenerConfig() *ScreenerConfig {
	return obj._ScreenerConfig
}
func (obj *Config) JunkEmailCheckConfig() *JunkEmailChecksConfig {
	return obj._JunkEmailCheckConfig
}
func (obj *Config) LocationModelCheck() *LocationModelCheckConfig {
	return obj._LocationModelCheck
}
func (obj *Config) DynamicElementsConfig() *DynamicElementsConfig {
	return obj._DynamicElementsConfig
}
func (obj *Config) InstalledAppsCheckConfig() *InstalledAppsCheckConfig {
	return obj._InstalledAppsCheckConfig
}
func (obj *Config) CreditReportAffluenceClassConfig() *CreditReportAffluenceClassConfig {
	return obj._CreditReportAffluenceClassConfig
}
func (obj *Config) CrossVideoFacematchCheckConfig() *CrossVideoFacematchCheckConfig {
	return obj._CrossVideoFacematchCheckConfig
}
func (obj *Config) SAUnreviewedAlertsCheckConfig() *SAUnreviewedAlertsCheckConfig {
	return obj._SAUnreviewedAlertsCheckConfig
}
func (obj *Config) ContactAssociationsCheckConfig() *ContactAssociationsCheckConfig {
	return obj._ContactAssociationsCheckConfig
}
func (obj *Config) RuleHitCallbackSubscriber() *gencfg.SqsSubscriber {
	return obj._RuleHitCallbackSubscriber
}
func (obj *Config) ProfileBannerForSherlock() *ProfileBannerForSherlock {
	return obj._ProfileBannerForSherlock
}
func (obj *Config) ValidateSavingsAccountStatusConfig() *ValidateSavingsAccountStatusConfig {
	return obj._ValidateSavingsAccountStatusConfig
}
func (obj *Config) LowContactCountCheckConfig() *LowContactCountCheckConfig {
	return obj._LowContactCountCheckConfig
}
func (obj *Config) VPNPresenceCheckConfig() *VPNPresenceCheckConfig {
	return obj._VPNPresenceCheckConfig
}
func (obj *Config) PercentageRolloutReleaseConfig() *PercentageRolloutReleaseConfig {
	return obj._PercentageRolloutReleaseConfig
}
func (obj *Config) IPAddressRedListCheckConfig() *IPAddressRedListCheckConfig {
	return obj._IPAddressRedListCheckConfig
}
func (obj *Config) ContactAssociationAndRiskyProfileCheckConfig() *ContactAssociationAndRiskyProfileCheckConfig {
	return obj._ContactAssociationAndRiskyProfileCheckConfig
}
func (obj *Config) Application() *config.Application {
	return obj._Application
}
func (obj *Config) Server() *config.Server {
	return obj._Server
}
func (obj *Config) EpifiDb() *cfg.DB {
	return obj._EpifiDb
}
func (obj *Config) FRMDb() *cfg.DB {
	return obj._FRMDb
}
func (obj *Config) FRMPgdb() *cfg.DB {
	return obj._FRMPgdb
}
func (obj *Config) AWS() *config.Aws {
	return obj._AWS
}
func (obj *Config) IsFRMPgdbEnabled() bool {
	return obj._IsFRMPgdbEnabled
}
func (obj *Config) RedListLatLongPrecisions() []int {
	return obj._RedListLatLongPrecisions
}
func (obj *Config) TxnMonitoringConfig() *config.TxnMonitoringConfig {
	return obj._TxnMonitoringConfig
}
func (obj *Config) Secrets() *cfg.Secrets {
	return obj._Secrets
}
func (obj *Config) Tracing() *cfg.Tracing {
	return obj._Tracing
}
func (obj *Config) BlockingFlowErrorReportConfig() *config.BlockingErrorReportConfig {
	return obj._BlockingFlowErrorReportConfig
}
func (obj *Config) LeaActorsPublisher() *cfg.SqsPublisher {
	return obj._LeaActorsPublisher
}
func (obj *Config) LEAComplaint() *config.LEAComplaintConfig {
	return obj._LEAComplaint
}
func (obj *Config) CaseStore() *common.CaseStoreConfig {
	return obj._CaseStore
}
func (obj *Config) FIProductToSegmentMap() map[string]string {
	return obj._FIProductToSegmentMap
}
func (obj *Config) ContactAssociationsConfig() *config.ContactAssociationsConfig {
	return obj._ContactAssociationsConfig
}
func (obj *Config) InvestigationEmail() *common.InvestigationEmailConfig {
	return obj._InvestigationEmail
}
func (obj *Config) Escalation() *config.EscalationConfig {
	return obj._Escalation
}
func (obj *Config) RiskBankActionComms() *common.RiskBankActionComms {
	return obj._RiskBankActionComms
}
func (obj *Config) RiskBankAction() *common.RiskBankAction {
	return obj._RiskBankAction
}
func (obj *Config) DronapayRuleHitCallbackPublisher() *cfg.SqsPublisher {
	return obj._DronapayRuleHitCallbackPublisher
}
func (obj *Config) FormSubmissionEventPublisher() *cfg.SqsPublisher {
	return obj._FormSubmissionEventPublisher
}
func (obj *Config) AlertsPublisher() *cfg.SqsPublisher {
	return obj._AlertsPublisher
}
func (obj *Config) BatchRuleEngineS3() *config.BatchRuleEngineS3Config {
	return obj._BatchRuleEngineS3
}
func (obj *Config) WebForm() *config.WebFormConfig {
	return obj._WebForm
}
func (obj *Config) UnifiedLEAComplaintConfig() *common.UnifiedLeaComplaintConfig {
	return obj._UnifiedLEAComplaintConfig
}
func (obj *Config) WebFormConfig() *common.WebFormConfig {
	return obj._WebFormConfig
}

type FeatureFlags struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_EnablePGDBForTxnMonitoring uint32
}

func (obj *FeatureFlags) EnablePGDBForTxnMonitoring() bool {
	if atomic.LoadUint32(&obj._EnablePGDBForTxnMonitoring) == 0 {
		return false
	} else {
		return true
	}
}

type OnboardingVelocityConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Threshold          int32
	_D2HThreshold       int32
	_BucketPrecision    int64
	_BucketExpiry       int64
	_QueryRangeDuration int64
}

func (obj *OnboardingVelocityConfig) Threshold() int64 {
	return int64(atomic.LoadInt32(&obj._Threshold))
}
func (obj *OnboardingVelocityConfig) D2HThreshold() int64 {
	return int64(atomic.LoadInt32(&obj._D2HThreshold))
}
func (obj *OnboardingVelocityConfig) BucketPrecision() int {
	return int(atomic.LoadInt64(&obj._BucketPrecision))
}
func (obj *OnboardingVelocityConfig) BucketExpiry() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._BucketExpiry))
}
func (obj *OnboardingVelocityConfig) QueryRangeDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._QueryRangeDuration))
}

type CMConsumerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// ProcessActiveRuleAlerts controls whether active rule alerts are processed
	// If false, active rule alerts will not trigger workflows and will be treated like shadow rules
	_ProcessActiveRuleAlerts uint32
	// CaseManagementRollOutPercentage defines Case management flow roll out percentage for payload types
	// map[Payload Type]RollOut Percentage
	_CaseManagementRollOutPercentage *syncmap.Map[string, uint64]
	_CreateAlertsBatchSize           uint32
}

// ProcessActiveRuleAlerts controls whether active rule alerts are processed
// If false, active rule alerts will not trigger workflows and will be treated like shadow rules
func (obj *CMConsumerConfig) ProcessActiveRuleAlerts() bool {
	if atomic.LoadUint32(&obj._ProcessActiveRuleAlerts) == 0 {
		return false
	} else {
		return true
	}
}

// CaseManagementRollOutPercentage defines Case management flow roll out percentage for payload types
// map[Payload Type]RollOut Percentage
func (obj *CMConsumerConfig) CaseManagementRollOutPercentage() *syncmap.Map[string, uint64] {
	return obj._CaseManagementRollOutPercentage
}
func (obj *CMConsumerConfig) CreateAlertsBatchSize() uint32 {
	return obj._CreateAlertsBatchSize
}

type Profile struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Profile service will not consider pending reviews older than FetchReviewsPeriod (days)
	_FetchReviewsPeriod int64
}

// Profile service will not consider pending reviews older than FetchReviewsPeriod (days)
func (obj *Profile) FetchReviewsPeriod() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._FetchReviewsPeriod))
}

type RedListConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Onboarding state risk score threshold to sync with risk evaluator entity
	_OnboardingStateThreshold uint32
	// risk score threshold for High risk atm list to sync with risk evaluator entity
	_AtmHighRiskThreshold uint32
	// risk score threshold for Medium risk atm list to sync with risk evaluator entity
	_AtmMediumRiskThreshold uint32
}

// Onboarding state risk score threshold to sync with risk evaluator entity
func (obj *RedListConfig) OnboardingStateThreshold() uint32 {
	return uint32(atomic.LoadUint32(&obj._OnboardingStateThreshold))
}

// risk score threshold for High risk atm list to sync with risk evaluator entity
func (obj *RedListConfig) AtmHighRiskThreshold() uint32 {
	return uint32(atomic.LoadUint32(&obj._AtmHighRiskThreshold))
}

// risk score threshold for Medium risk atm list to sync with risk evaluator entity
func (obj *RedListConfig) AtmMediumRiskThreshold() uint32 {
	return uint32(atomic.LoadUint32(&obj._AtmMediumRiskThreshold))
}

type CasePriorityQueuesConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_CachedCaseStaleThreshold     int64
	_QueueCapacityMap             *syncmap.Map[string, int]
	_QueueTTLMap                  *syncmap.Map[string, time.Duration]
	_LiveQueueTags                roarray.ROArray[string]
	_LiveQueueTagsMutex           *sync.RWMutex
	_TransactionQueueTags         roarray.ROArray[string]
	_TransactionQueueTagsMutex    *sync.RWMutex
	_PostOnboardingQueueTags      roarray.ROArray[string]
	_PostOnboardingQueueTagsMutex *sync.RWMutex
	_EscalationQueueTags          roarray.ROArray[string]
	_EscalationQueueTagsMutex     *sync.RWMutex
	_ModelSelectionRates          *ModelSelectionRates
}

func (obj *CasePriorityQueuesConfig) CachedCaseStaleThreshold() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._CachedCaseStaleThreshold))
}
func (obj *CasePriorityQueuesConfig) QueueCapacityMap() *syncmap.Map[string, int] {
	return obj._QueueCapacityMap
}
func (obj *CasePriorityQueuesConfig) QueueTTLMap() *syncmap.Map[string, time.Duration] {
	return obj._QueueTTLMap
}
func (obj *CasePriorityQueuesConfig) LiveQueueTags() roarray.ROArray[string] {
	obj._LiveQueueTagsMutex.RLock()
	defer obj._LiveQueueTagsMutex.RUnlock()
	return obj._LiveQueueTags
}
func (obj *CasePriorityQueuesConfig) TransactionQueueTags() roarray.ROArray[string] {
	obj._TransactionQueueTagsMutex.RLock()
	defer obj._TransactionQueueTagsMutex.RUnlock()
	return obj._TransactionQueueTags
}
func (obj *CasePriorityQueuesConfig) PostOnboardingQueueTags() roarray.ROArray[string] {
	obj._PostOnboardingQueueTagsMutex.RLock()
	defer obj._PostOnboardingQueueTagsMutex.RUnlock()
	return obj._PostOnboardingQueueTags
}
func (obj *CasePriorityQueuesConfig) EscalationQueueTags() roarray.ROArray[string] {
	obj._EscalationQueueTagsMutex.RLock()
	defer obj._EscalationQueueTagsMutex.RUnlock()
	return obj._EscalationQueueTags
}
func (obj *CasePriorityQueuesConfig) ModelSelectionRates() *ModelSelectionRates {
	return obj._ModelSelectionRates
}

type ModelSelectionRates struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Model1Rate               int32
	_Model2Rate               int32
	_CgRate                   int32
	_UseModelRateForSelection uint32
}

func (obj *ModelSelectionRates) Model1Rate() int8 {
	return int8(atomic.LoadInt32(&obj._Model1Rate))
}
func (obj *ModelSelectionRates) Model2Rate() int8 {
	return int8(atomic.LoadInt32(&obj._Model2Rate))
}
func (obj *ModelSelectionRates) CgRate() int8 {
	return int8(atomic.LoadInt32(&obj._CgRate))
}
func (obj *ModelSelectionRates) UseModelRateForSelection() bool {
	if atomic.LoadUint32(&obj._UseModelRateForSelection) == 0 {
		return false
	} else {
		return true
	}
}

type UpiVPANameMatchCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled             uint32
	_CompleteMismatchScore float32
	_FailureScore          float32
}

func (obj *UpiVPANameMatchCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *UpiVPANameMatchCheckConfig) CompleteMismatchScore() float32 {
	return obj._CompleteMismatchScore
}
func (obj *UpiVPANameMatchCheckConfig) FailureScore() float32 {
	return obj._FailureScore
}

type AffluenceCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// if users onboarding ds score is above this affluence check will be failed
	_LowAffluenceClassDSScoreThreshold int64
	// if users is self-employed and  onboarding ds score is above this affluence check will be failed
	_LowAffluenceClassAndSelfEmployedDSScoreThreshold int64
	_IsEnabled                                        uint32
	// Key will be affluence check enum as string and
	// value contains score to be sent back from processor if the given check fails
	// score will be between the range 0 to 100
	_AffluenceCheckScoreMapping *syncmap.Map[string, int]
}

// if users onboarding ds score is above this affluence check will be failed
func (obj *AffluenceCheckConfig) LowAffluenceClassDSScoreThreshold() int {
	return int(atomic.LoadInt64(&obj._LowAffluenceClassDSScoreThreshold))
}

// if users is self-employed and  onboarding ds score is above this affluence check will be failed
func (obj *AffluenceCheckConfig) LowAffluenceClassAndSelfEmployedDSScoreThreshold() int {
	return int(atomic.LoadInt64(&obj._LowAffluenceClassAndSelfEmployedDSScoreThreshold))
}
func (obj *AffluenceCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Key will be affluence check enum as string and
// value contains score to be sent back from processor if the given check fails
// score will be between the range 0 to 100
func (obj *AffluenceCheckConfig) AffluenceCheckScoreMapping() *syncmap.Map[string, int] {
	return obj._AffluenceCheckScoreMapping
}

type ScreenerConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Defines the time threshold for a screener run to finish,
	// if screener attempt status is still in progress post this threshold, it should be considered invalid
	_ScreenerAttemptMaxRunTime int64
	// map contains criteria to risk check config
	// key is screener criteria enum as string and value contains a struct with required config
	_CriteriaToRiskChecksConfigMap *syncmap.Map[string, *RiskChecksConfig]
	// map contains criteria to screener actions threshold config mapping
	// key is screener criteria enum as string and value contains a struct with action thresholds
	_CriteriaToRiskActionThresholdMap *syncmap.Map[string, *ScreenerActionThresholds]
	// map[RiskParam]StoredResultTTL.
	// value is default TTL of stored result and can be overridden at criteria level.
	// A non-positive TTL signifies stored result can't be used for check
	// i.e. check should be reevaluated for each run.
	_RiskCheckStoredResultDefaultTTLMap *syncmap.Map[string, time.Duration]
	_CriteriaToRuleConfigMap            map[string]*config.ScreenerRuleConfig
}

// Defines the time threshold for a screener run to finish,
// if screener attempt status is still in progress post this threshold, it should be considered invalid
func (obj *ScreenerConfig) ScreenerAttemptMaxRunTime() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._ScreenerAttemptMaxRunTime))
}

// map contains criteria to risk check config
// key is screener criteria enum as string and value contains a struct with required config
func (obj *ScreenerConfig) CriteriaToRiskChecksConfigMap() *syncmap.Map[string, *RiskChecksConfig] {
	return obj._CriteriaToRiskChecksConfigMap
}

// map contains criteria to screener actions threshold config mapping
// key is screener criteria enum as string and value contains a struct with action thresholds
func (obj *ScreenerConfig) CriteriaToRiskActionThresholdMap() *syncmap.Map[string, *ScreenerActionThresholds] {
	return obj._CriteriaToRiskActionThresholdMap
}

// map[RiskParam]StoredResultTTL.
// value is default TTL of stored result and can be overridden at criteria level.
// A non-positive TTL signifies stored result can't be used for check
// i.e. check should be reevaluated for each run.
func (obj *ScreenerConfig) RiskCheckStoredResultDefaultTTLMap() *syncmap.Map[string, time.Duration] {
	return obj._RiskCheckStoredResultDefaultTTLMap
}
func (obj *ScreenerConfig) CriteriaToRuleConfigMap() map[string]*config.ScreenerRuleConfig {
	return obj._CriteriaToRuleConfigMap
}

type RiskChecksConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// map contains risk param to bool mapping
	// bool value indicates whether given risk param check is enabled for current entrypoint
	_RiskParamConfigMap *syncmap.Map[string, *RiskParamConfig]
}

// map contains risk param to bool mapping
// bool value indicates whether given risk param check is enabled for current entrypoint
func (obj *RiskChecksConfig) RiskParamConfigMap() *syncmap.Map[string, *RiskParamConfig] {
	return obj._RiskParamConfigMap
}

type RiskParamConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled   uint32
	_IsMandatory uint32
	// Any value other than zero overrides default TTL for check.
	// A negative TTL signifies stored result can't be used for check i.e.
	// check should be reevaluated for each run.
	_StoredResultTTL int64
}

func (obj *RiskParamConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *RiskParamConfig) IsMandatory() bool {
	if atomic.LoadUint32(&obj._IsMandatory) == 0 {
		return false
	} else {
		return true
	}
}

// Any value other than zero overrides default TTL for check.
// A negative TTL signifies stored result can't be used for check i.e.
// check should be reevaluated for each run.
func (obj *RiskParamConfig) StoredResultTTL() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._StoredResultTTL))
}

type ScreenerActionThresholds struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// If any risk checks performed for user has higher score than this, user's screener will be failed with auto-block action
	_AutoBlockThreshold int32
	// If any risk checks performed for user has higher score than this, user's screener will be failed and sent for manual review
	_ManualReviewThreshold                 int32
	_ThresholdToTipManualReviewToAutoBlock *ThresholdToTipManualReviewToAutoBlock
}

// If any risk checks performed for user has higher score than this, user's screener will be failed with auto-block action
func (obj *ScreenerActionThresholds) AutoBlockThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._AutoBlockThreshold))
}

// If any risk checks performed for user has higher score than this, user's screener will be failed and sent for manual review
func (obj *ScreenerActionThresholds) ManualReviewThreshold() int32 {
	return int32(atomic.LoadInt32(&obj._ManualReviewThreshold))
}
func (obj *ScreenerActionThresholds) ThresholdToTipManualReviewToAutoBlock() *ThresholdToTipManualReviewToAutoBlock {
	return obj._ThresholdToTipManualReviewToAutoBlock
}

type ThresholdToTipManualReviewToAutoBlock struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_Threshold                int64
	_IsEnabled                uint32
	_FailureScore             float32
	_ScreenerChecksOfInterest []string
}

func (obj *ThresholdToTipManualReviewToAutoBlock) Threshold() int {
	return int(atomic.LoadInt64(&obj._Threshold))
}
func (obj *ThresholdToTipManualReviewToAutoBlock) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ThresholdToTipManualReviewToAutoBlock) FailureScore() float32 {
	return obj._FailureScore
}
func (obj *ThresholdToTipManualReviewToAutoBlock) ScreenerChecksOfInterest() []string {
	return obj._ScreenerChecksOfInterest
}

type JunkEmailChecksConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
	// Key will be JunkEmail check enum as string and
	// value contains score to be assigned if the given check fails
	// score will be in the range 0 to 100
	_JunkEmailCheckScoreMapping *syncmap.Map[string, int]
}

func (obj *JunkEmailChecksConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Key will be JunkEmail check enum as string and
// value contains score to be assigned if the given check fails
// score will be in the range 0 to 100
func (obj *JunkEmailChecksConfig) JunkEmailCheckScoreMapping() *syncmap.Map[string, int] {
	return obj._JunkEmailCheckScoreMapping
}

type LocationModelCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
	// Key will be risk severity enum as string and
	// value contains score to be sent back from processor if the given check fails
	// score will be between the range 0 to 100
	_RiskSeverityToScoreMap *syncmap.Map[string, int]
}

func (obj *LocationModelCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Key will be risk severity enum as string and
// value contains score to be sent back from processor if the given check fails
// score will be between the range 0 to 100
func (obj *LocationModelCheckConfig) RiskSeverityToScoreMap() *syncmap.Map[string, int] {
	return obj._RiskSeverityToScoreMap
}

type DynamicElementsConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                            uint32
	_FreezeStatusToBannerConfigMap        map[string]*config.BannerConfig
	_OutcallBanner                        *config.BannerConfig
	_DurationToCheckPreviousLEAComplaints time.Duration
}

func (obj *DynamicElementsConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *DynamicElementsConfig) FreezeStatusToBannerConfigMap() map[string]*config.BannerConfig {
	return obj._FreezeStatusToBannerConfigMap
}
func (obj *DynamicElementsConfig) OutcallBanner() *config.BannerConfig {
	return obj._OutcallBanner
}
func (obj *DynamicElementsConfig) DurationToCheckPreviousLEAComplaints() time.Duration {
	return obj._DurationToCheckPreviousLEAComplaints
}

type InstalledAppsCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// Tipping point for less social app count
	_MinRequiredSocialAppCount int32
	// Minimum required count of non system apps
	_MinRequiredNonSystemAppCount int32
	_IsEnabled                    uint32
	// Key will be installed app sub check enum as string and
	// value contains score to be assigned if the given check fails
	// score will be in the range 0 to 100
	_CheckToScoreMap *syncmap.Map[string, int]
	// List of social apps
	_SocialApps                         roarray.ROArray[string]
	_SocialAppsMutex                    *sync.RWMutex
	_DurationToCheckForPreviousFailures time.Duration
}

// Tipping point for less social app count
func (obj *InstalledAppsCheckConfig) MinRequiredSocialAppCount() int32 {
	return int32(atomic.LoadInt32(&obj._MinRequiredSocialAppCount))
}

// Minimum required count of non system apps
func (obj *InstalledAppsCheckConfig) MinRequiredNonSystemAppCount() int32 {
	return int32(atomic.LoadInt32(&obj._MinRequiredNonSystemAppCount))
}
func (obj *InstalledAppsCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

// Key will be installed app sub check enum as string and
// value contains score to be assigned if the given check fails
// score will be in the range 0 to 100
func (obj *InstalledAppsCheckConfig) CheckToScoreMap() *syncmap.Map[string, int] {
	return obj._CheckToScoreMap
}

// List of social apps
func (obj *InstalledAppsCheckConfig) SocialApps() roarray.ROArray[string] {
	obj._SocialAppsMutex.RLock()
	defer obj._SocialAppsMutex.RUnlock()
	return obj._SocialApps
}
func (obj *InstalledAppsCheckConfig) DurationToCheckForPreviousFailures() time.Duration {
	return obj._DurationToCheckForPreviousFailures
}

type CreditReportAffluenceClassConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                         uint32
	_InvalidCreditScoreThreshold       int32
	_FailCreditScoreThreshold          int32
	_PassCreditScoreThreshold          int32
	_FailureScore                      float32
	_BadAffluenceIncomeThreshold       int64
	_SuitFiledWilfulDefault            int
	_SuitFiledWillfulDefaultWrittenOff int
}

func (obj *CreditReportAffluenceClassConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CreditReportAffluenceClassConfig) InvalidCreditScoreThreshold() int32 {
	return obj._InvalidCreditScoreThreshold
}
func (obj *CreditReportAffluenceClassConfig) FailCreditScoreThreshold() int32 {
	return obj._FailCreditScoreThreshold
}
func (obj *CreditReportAffluenceClassConfig) PassCreditScoreThreshold() int32 {
	return obj._PassCreditScoreThreshold
}
func (obj *CreditReportAffluenceClassConfig) FailureScore() float32 {
	return obj._FailureScore
}
func (obj *CreditReportAffluenceClassConfig) BadAffluenceIncomeThreshold() int64 {
	return obj._BadAffluenceIncomeThreshold
}
func (obj *CreditReportAffluenceClassConfig) SuitFiledWilfulDefault() int {
	return obj._SuitFiledWilfulDefault
}
func (obj *CreditReportAffluenceClassConfig) SuitFiledWillfulDefaultWrittenOff() int {
	return obj._SuitFiledWillfulDefaultWrittenOff
}

type CrossVideoFacematchCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled            uint32
	_LivenessScoreCutoff  float32
	_FaceMatchScoreCutoff float32
	_FailureScore         float32
}

func (obj *CrossVideoFacematchCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *CrossVideoFacematchCheckConfig) LivenessScoreCutoff() float32 {
	return obj._LivenessScoreCutoff
}
func (obj *CrossVideoFacematchCheckConfig) FaceMatchScoreCutoff() float32 {
	return obj._FaceMatchScoreCutoff
}
func (obj *CrossVideoFacematchCheckConfig) FailureScore() float32 {
	return obj._FailureScore
}

type SAUnreviewedAlertsCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                    uint32
	_HighPrecisionRuleHitDuration int64
	_LowPrecisionRuleHitDuration  int64
	_HighPrecisionRuleThreshold   float32
}

func (obj *SAUnreviewedAlertsCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *SAUnreviewedAlertsCheckConfig) HighPrecisionRuleHitDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._HighPrecisionRuleHitDuration))
}
func (obj *SAUnreviewedAlertsCheckConfig) LowPrecisionRuleHitDuration() time.Duration {
	return time.Duration(atomic.LoadInt64(&obj._LowPrecisionRuleHitDuration))
}
func (obj *SAUnreviewedAlertsCheckConfig) HighPrecisionRuleThreshold() float32 {
	return obj._HighPrecisionRuleThreshold
}

type ContactAssociationsCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                              uint32
	_IngressMaxAssociationsWithLEAThreshold int
	_IngressManualReviewForLEAThreshold     int
	_IngressManualReviewForBlockedThreshold int
	_EgressMaxAssociationsWithLEAThreshold  int
	_FailedCheckScore                       float32
	_ManualReviewFailCheckScore             float32
}

func (obj *ContactAssociationsCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ContactAssociationsCheckConfig) IngressMaxAssociationsWithLEAThreshold() int {
	return obj._IngressMaxAssociationsWithLEAThreshold
}
func (obj *ContactAssociationsCheckConfig) IngressManualReviewForLEAThreshold() int {
	return obj._IngressManualReviewForLEAThreshold
}
func (obj *ContactAssociationsCheckConfig) IngressManualReviewForBlockedThreshold() int {
	return obj._IngressManualReviewForBlockedThreshold
}
func (obj *ContactAssociationsCheckConfig) EgressMaxAssociationsWithLEAThreshold() int {
	return obj._EgressMaxAssociationsWithLEAThreshold
}
func (obj *ContactAssociationsCheckConfig) FailedCheckScore() float32 {
	return obj._FailedCheckScore
}
func (obj *ContactAssociationsCheckConfig) ManualReviewFailCheckScore() float32 {
	return obj._ManualReviewFailCheckScore
}

type ProfileBannerForSherlock struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled         uint32
	_MaxBannersForType int
}

func (obj *ProfileBannerForSherlock) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ProfileBannerForSherlock) MaxBannersForType() int {
	return obj._MaxBannersForType
}

type ValidateSavingsAccountStatusConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled        uint32
	_FailedCheckScore float32
}

func (obj *ValidateSavingsAccountStatusConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ValidateSavingsAccountStatusConfig) FailedCheckScore() float32 {
	return obj._FailedCheckScore
}

type LowContactCountCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	// If contact count is equal or below this threshold,
	// user will be failed and sent for manual review by the check
	_LowContactCountManualReviewThreshold int64
	// If contact count is equal or below this threshold,
	// user will be passed by the check but a potential risk flag will be returned
	_LowContactCountFlagThreshold int64
}

// If contact count is equal or below this threshold,
// user will be failed and sent for manual review by the check
func (obj *LowContactCountCheckConfig) LowContactCountManualReviewThreshold() int {
	return int(atomic.LoadInt64(&obj._LowContactCountManualReviewThreshold))
}

// If contact count is equal or below this threshold,
// user will be passed by the check but a potential risk flag will be returned
func (obj *LowContactCountCheckConfig) LowContactCountFlagThreshold() int {
	return int(atomic.LoadInt64(&obj._LowContactCountFlagThreshold))
}

type VPNPresenceCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled uint32
}

func (obj *VPNPresenceCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}

type PercentageRolloutReleaseConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_RolloutPercentage int64
	_FeatureName       string
	_FeatureNameMutex  *sync.RWMutex
}

func (obj *PercentageRolloutReleaseConfig) RolloutPercentage() int {
	return int(atomic.LoadInt64(&obj._RolloutPercentage))
}
func (obj *PercentageRolloutReleaseConfig) FeatureName() string {
	obj._FeatureNameMutex.RLock()
	defer obj._FeatureNameMutex.RUnlock()
	return obj._FeatureName
}

type IPAddressRedListCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled                            uint32
	_ManualReviewScore                    float32
	_MinRedListScoreToSendForManualReview float32
}

func (obj *IPAddressRedListCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *IPAddressRedListCheckConfig) ManualReviewScore() float32 {
	return obj._ManualReviewScore
}
func (obj *IPAddressRedListCheckConfig) MinRedListScoreToSendForManualReview() float32 {
	return obj._MinRedListScoreToSendForManualReview
}

type ContactAssociationAndRiskyProfileCheckConfig struct {
	callbacks *syncmap.Map[string, helper.FieldChangeCallback]

	_IsEnabled         uint32
	_ManualReviewScore float32
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) IsEnabled() bool {
	if atomic.LoadUint32(&obj._IsEnabled) == 0 {
		return false
	} else {
		return true
	}
}
func (obj *ContactAssociationAndRiskyProfileCheckConfig) ManualReviewScore() float32 {
	return obj._ManualReviewScore
}

func NewConfig() (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["hunterrolloutpercentage"] = _obj.SetHunterRollOutPercentage
	_setters["testflag"] = _obj.SetTestFlag
	_setters["ruletags"] = _obj.SetRuleTags
	_obj._RuleTagsMutex = &sync.RWMutex{}
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_ProcessRedListUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRedListUpdateSqsSubscriber = _ProcessRedListUpdateSqsSubscriber
	helper.AddFieldSetters("processredlistupdatesqssubscriber", _fieldSetters, _setters)
	_ProcessSyncLeaActorsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSyncLeaActorsSqsSubscriber = _ProcessSyncLeaActorsSqsSubscriber
	helper.AddFieldSetters("processsyncleaactorssqssubscriber", _fieldSetters, _setters)
	_ProcessRiskCasesIngestEventBEQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskCasesIngestEventBEQueueSubscriber = _ProcessRiskCasesIngestEventBEQueueSubscriber
	helper.AddFieldSetters("processriskcasesingesteventbequeuesubscriber", _fieldSetters, _setters)
	_ProcessRiskCasesIngestEventDataQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskCasesIngestEventDataQueueSubscriber = _ProcessRiskCasesIngestEventDataQueueSubscriber
	helper.AddFieldSetters("processriskcasesingesteventdataqueuesubscriber", _fieldSetters, _setters)
	_ProcessRiskAlertIngestEventDSQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskAlertIngestEventDSQueueSubscriber = _ProcessRiskAlertIngestEventDSQueueSubscriber
	helper.AddFieldSetters("processriskalertingesteventdsqueuesubscriber", _fieldSetters, _setters)
	_ProcessCallRoutingEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCallRoutingEventSqsSubscriber = _ProcessCallRoutingEventSqsSubscriber
	helper.AddFieldSetters("processcallroutingeventsqssubscriber", _fieldSetters, _setters)
	_ProcessDisputeUploadSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessDisputeUploadSqsSubscriber = _ProcessDisputeUploadSqsSubscriber
	helper.AddFieldSetters("processdisputeuploadsqssubscriber", _fieldSetters, _setters)
	_ProcessFormSubmissionEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessFormSubmissionEventSqsSubscriber = _ProcessFormSubmissionEventSqsSubscriber
	helper.AddFieldSetters("processformsubmissioneventsqssubscriber", _fieldSetters, _setters)
	_OnboardingVelocityConfig, _fieldSetters := NewOnboardingVelocityConfig()
	_obj._OnboardingVelocityConfig = _OnboardingVelocityConfig
	helper.AddFieldSetters("onboardingvelocityconfig", _fieldSetters, _setters)
	_RiskAlertIngestionQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskAlertIngestionQueueSubscriber = _RiskAlertIngestionQueueSubscriber
	helper.AddFieldSetters("riskalertingestionqueuesubscriber", _fieldSetters, _setters)
	_RiskBatchRuleEngineEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskBatchRuleEngineEventQueueSubscriber = _RiskBatchRuleEngineEventQueueSubscriber
	helper.AddFieldSetters("riskbatchruleengineeventqueuesubscriber", _fieldSetters, _setters)
	_RiskCXTicketUpdateEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskCXTicketUpdateEventQueueSubscriber = _RiskCXTicketUpdateEventQueueSubscriber
	helper.AddFieldSetters("riskcxticketupdateeventqueuesubscriber", _fieldSetters, _setters)
	_RiskAccountOperationStatusUpdateQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskAccountOperationStatusUpdateQueueSubscriber = _RiskAccountOperationStatusUpdateQueueSubscriber
	helper.AddFieldSetters("riskaccountoperationstatusupdatequeuesubscriber", _fieldSetters, _setters)
	_ProcessRiskSignalEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskSignalEventQueueSubscriber = _ProcessRiskSignalEventQueueSubscriber
	helper.AddFieldSetters("processrisksignaleventqueuesubscriber", _fieldSetters, _setters)
	_ProcessMnrlReportSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessMnrlReportSqsSubscriber = _ProcessMnrlReportSqsSubscriber
	helper.AddFieldSetters("processmnrlreportsqssubscriber", _fieldSetters, _setters)
	_ProcessMnrlSuspectedFlaggedMobileSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessMnrlSuspectedFlaggedMobileSqsSubscriber = _ProcessMnrlSuspectedFlaggedMobileSqsSubscriber
	helper.AddFieldSetters("processmnrlsuspectedflaggedmobilesqssubscriber", _fieldSetters, _setters)
	_ProcessRiskAlertEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskAlertEventQueueSubscriber = _ProcessRiskAlertEventQueueSubscriber
	helper.AddFieldSetters("processriskalerteventqueuesubscriber", _fieldSetters, _setters)
	_OrderUpdateTxnMonitoringSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateTxnMonitoringSubscriber = _OrderUpdateTxnMonitoringSubscriber
	helper.AddFieldSetters("orderupdatetxnmonitoringsubscriber", _fieldSetters, _setters)
	_CMConsumerConfig, _fieldSetters := NewCMConsumerConfig()
	_obj._CMConsumerConfig = _CMConsumerConfig
	helper.AddFieldSetters("cmconsumerconfig", _fieldSetters, _setters)
	_Profile, _fieldSetters := NewProfile()
	_obj._Profile = _Profile
	helper.AddFieldSetters("profile", _fieldSetters, _setters)
	_RedList, _fieldSetters := NewRedListConfig()
	_obj._RedList = _RedList
	helper.AddFieldSetters("redlist", _fieldSetters, _setters)
	_CasePriorityQueue, _fieldSetters := NewCasePriorityQueuesConfig()
	_obj._CasePriorityQueue = _CasePriorityQueue
	helper.AddFieldSetters("casepriorityqueue", _fieldSetters, _setters)
	_UpiVPANameMatchingCheckConfig, _fieldSetters := NewUpiVPANameMatchCheckConfig()
	_obj._UpiVPANameMatchingCheckConfig = _UpiVPANameMatchingCheckConfig
	helper.AddFieldSetters("upivpanamematchingcheckconfig", _fieldSetters, _setters)
	_AffluenceCheckConfig, _fieldSetters := NewAffluenceCheckConfig()
	_obj._AffluenceCheckConfig = _AffluenceCheckConfig
	helper.AddFieldSetters("affluencecheckconfig", _fieldSetters, _setters)
	_ScreenerConfig, _fieldSetters := NewScreenerConfig()
	_obj._ScreenerConfig = _ScreenerConfig
	helper.AddFieldSetters("screenerconfig", _fieldSetters, _setters)
	_JunkEmailCheckConfig, _fieldSetters := NewJunkEmailChecksConfig()
	_obj._JunkEmailCheckConfig = _JunkEmailCheckConfig
	helper.AddFieldSetters("junkemailcheckconfig", _fieldSetters, _setters)
	_LocationModelCheck, _fieldSetters := NewLocationModelCheckConfig()
	_obj._LocationModelCheck = _LocationModelCheck
	helper.AddFieldSetters("locationmodelcheck", _fieldSetters, _setters)
	_DynamicElementsConfig, _fieldSetters := NewDynamicElementsConfig()
	_obj._DynamicElementsConfig = _DynamicElementsConfig
	helper.AddFieldSetters("dynamicelementsconfig", _fieldSetters, _setters)
	_InstalledAppsCheckConfig, _fieldSetters := NewInstalledAppsCheckConfig()
	_obj._InstalledAppsCheckConfig = _InstalledAppsCheckConfig
	helper.AddFieldSetters("installedappscheckconfig", _fieldSetters, _setters)
	_CreditReportAffluenceClassConfig, _fieldSetters := NewCreditReportAffluenceClassConfig()
	_obj._CreditReportAffluenceClassConfig = _CreditReportAffluenceClassConfig
	helper.AddFieldSetters("creditreportaffluenceclassconfig", _fieldSetters, _setters)
	_CrossVideoFacematchCheckConfig, _fieldSetters := NewCrossVideoFacematchCheckConfig()
	_obj._CrossVideoFacematchCheckConfig = _CrossVideoFacematchCheckConfig
	helper.AddFieldSetters("crossvideofacematchcheckconfig", _fieldSetters, _setters)
	_SAUnreviewedAlertsCheckConfig, _fieldSetters := NewSAUnreviewedAlertsCheckConfig()
	_obj._SAUnreviewedAlertsCheckConfig = _SAUnreviewedAlertsCheckConfig
	helper.AddFieldSetters("saunreviewedalertscheckconfig", _fieldSetters, _setters)
	_ContactAssociationsCheckConfig, _fieldSetters := NewContactAssociationsCheckConfig()
	_obj._ContactAssociationsCheckConfig = _ContactAssociationsCheckConfig
	helper.AddFieldSetters("contactassociationscheckconfig", _fieldSetters, _setters)
	_RuleHitCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RuleHitCallbackSubscriber = _RuleHitCallbackSubscriber
	helper.AddFieldSetters("rulehitcallbacksubscriber", _fieldSetters, _setters)
	_ProfileBannerForSherlock, _fieldSetters := NewProfileBannerForSherlock()
	_obj._ProfileBannerForSherlock = _ProfileBannerForSherlock
	helper.AddFieldSetters("profilebannerforsherlock", _fieldSetters, _setters)
	_ValidateSavingsAccountStatusConfig, _fieldSetters := NewValidateSavingsAccountStatusConfig()
	_obj._ValidateSavingsAccountStatusConfig = _ValidateSavingsAccountStatusConfig
	helper.AddFieldSetters("validatesavingsaccountstatusconfig", _fieldSetters, _setters)
	_LowContactCountCheckConfig, _fieldSetters := NewLowContactCountCheckConfig()
	_obj._LowContactCountCheckConfig = _LowContactCountCheckConfig
	helper.AddFieldSetters("lowcontactcountcheckconfig", _fieldSetters, _setters)
	_VPNPresenceCheckConfig, _fieldSetters := NewVPNPresenceCheckConfig()
	_obj._VPNPresenceCheckConfig = _VPNPresenceCheckConfig
	helper.AddFieldSetters("vpnpresencecheckconfig", _fieldSetters, _setters)
	_PercentageRolloutReleaseConfig, _fieldSetters := NewPercentageRolloutReleaseConfig()
	_obj._PercentageRolloutReleaseConfig = _PercentageRolloutReleaseConfig
	helper.AddFieldSetters("percentagerolloutreleaseconfig", _fieldSetters, _setters)
	_IPAddressRedListCheckConfig, _fieldSetters := NewIPAddressRedListCheckConfig()
	_obj._IPAddressRedListCheckConfig = _IPAddressRedListCheckConfig
	helper.AddFieldSetters("ipaddressredlistcheckconfig", _fieldSetters, _setters)
	_ContactAssociationAndRiskyProfileCheckConfig, _fieldSetters := NewContactAssociationAndRiskyProfileCheckConfig()
	_obj._ContactAssociationAndRiskyProfileCheckConfig = _ContactAssociationAndRiskyProfileCheckConfig
	helper.AddFieldSetters("contactassociationandriskyprofilecheckconfig", _fieldSetters, _setters)
	return _obj, _setters
}

func NewConfigWithQuest(questFieldPath string) (_obj *Config, _setters map[string]dynconf.SetFunc) {
	_obj = &Config{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}
	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["hunterrolloutpercentage"] = _obj.SetHunterRollOutPercentage
	_setters["testflag"] = _obj.SetTestFlag
	_setters["ruletags"] = _obj.SetRuleTags
	_obj._RuleTagsMutex = &sync.RWMutex{}
	_FeatureFlags, _fieldSetters := NewFeatureFlags()
	_obj._FeatureFlags = _FeatureFlags
	helper.AddFieldSetters("featureflags", _fieldSetters, _setters)
	_ProcessRedListUpdateSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRedListUpdateSqsSubscriber = _ProcessRedListUpdateSqsSubscriber
	helper.AddFieldSetters("processredlistupdatesqssubscriber", _fieldSetters, _setters)
	_ProcessSyncLeaActorsSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessSyncLeaActorsSqsSubscriber = _ProcessSyncLeaActorsSqsSubscriber
	helper.AddFieldSetters("processsyncleaactorssqssubscriber", _fieldSetters, _setters)
	_ProcessRiskCasesIngestEventBEQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskCasesIngestEventBEQueueSubscriber = _ProcessRiskCasesIngestEventBEQueueSubscriber
	helper.AddFieldSetters("processriskcasesingesteventbequeuesubscriber", _fieldSetters, _setters)
	_ProcessRiskCasesIngestEventDataQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskCasesIngestEventDataQueueSubscriber = _ProcessRiskCasesIngestEventDataQueueSubscriber
	helper.AddFieldSetters("processriskcasesingesteventdataqueuesubscriber", _fieldSetters, _setters)
	_ProcessRiskAlertIngestEventDSQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskAlertIngestEventDSQueueSubscriber = _ProcessRiskAlertIngestEventDSQueueSubscriber
	helper.AddFieldSetters("processriskalertingesteventdsqueuesubscriber", _fieldSetters, _setters)
	_ProcessCallRoutingEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessCallRoutingEventSqsSubscriber = _ProcessCallRoutingEventSqsSubscriber
	helper.AddFieldSetters("processcallroutingeventsqssubscriber", _fieldSetters, _setters)
	_ProcessDisputeUploadSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessDisputeUploadSqsSubscriber = _ProcessDisputeUploadSqsSubscriber
	helper.AddFieldSetters("processdisputeuploadsqssubscriber", _fieldSetters, _setters)
	_ProcessFormSubmissionEventSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessFormSubmissionEventSqsSubscriber = _ProcessFormSubmissionEventSqsSubscriber
	helper.AddFieldSetters("processformsubmissioneventsqssubscriber", _fieldSetters, _setters)
	_OnboardingVelocityConfig, _fieldSetters := NewOnboardingVelocityConfig()
	_obj._OnboardingVelocityConfig = _OnboardingVelocityConfig
	helper.AddFieldSetters("onboardingvelocityconfig", _fieldSetters, _setters)
	_RiskAlertIngestionQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskAlertIngestionQueueSubscriber = _RiskAlertIngestionQueueSubscriber
	helper.AddFieldSetters("riskalertingestionqueuesubscriber", _fieldSetters, _setters)
	_RiskBatchRuleEngineEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskBatchRuleEngineEventQueueSubscriber = _RiskBatchRuleEngineEventQueueSubscriber
	helper.AddFieldSetters("riskbatchruleengineeventqueuesubscriber", _fieldSetters, _setters)
	_RiskCXTicketUpdateEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskCXTicketUpdateEventQueueSubscriber = _RiskCXTicketUpdateEventQueueSubscriber
	helper.AddFieldSetters("riskcxticketupdateeventqueuesubscriber", _fieldSetters, _setters)
	_RiskAccountOperationStatusUpdateQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RiskAccountOperationStatusUpdateQueueSubscriber = _RiskAccountOperationStatusUpdateQueueSubscriber
	helper.AddFieldSetters("riskaccountoperationstatusupdatequeuesubscriber", _fieldSetters, _setters)
	_ProcessRiskSignalEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskSignalEventQueueSubscriber = _ProcessRiskSignalEventQueueSubscriber
	helper.AddFieldSetters("processrisksignaleventqueuesubscriber", _fieldSetters, _setters)
	_ProcessMnrlReportSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessMnrlReportSqsSubscriber = _ProcessMnrlReportSqsSubscriber
	helper.AddFieldSetters("processmnrlreportsqssubscriber", _fieldSetters, _setters)
	_ProcessMnrlSuspectedFlaggedMobileSqsSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessMnrlSuspectedFlaggedMobileSqsSubscriber = _ProcessMnrlSuspectedFlaggedMobileSqsSubscriber
	helper.AddFieldSetters("processmnrlsuspectedflaggedmobilesqssubscriber", _fieldSetters, _setters)
	_ProcessRiskAlertEventQueueSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._ProcessRiskAlertEventQueueSubscriber = _ProcessRiskAlertEventQueueSubscriber
	helper.AddFieldSetters("processriskalerteventqueuesubscriber", _fieldSetters, _setters)
	_OrderUpdateTxnMonitoringSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._OrderUpdateTxnMonitoringSubscriber = _OrderUpdateTxnMonitoringSubscriber
	helper.AddFieldSetters("orderupdatetxnmonitoringsubscriber", _fieldSetters, _setters)
	_CMConsumerConfig, _fieldSetters := NewCMConsumerConfig()
	_obj._CMConsumerConfig = _CMConsumerConfig
	helper.AddFieldSetters("cmconsumerconfig", _fieldSetters, _setters)
	_Profile, _fieldSetters := NewProfile()
	_obj._Profile = _Profile
	helper.AddFieldSetters("profile", _fieldSetters, _setters)
	_RedList, _fieldSetters := NewRedListConfig()
	_obj._RedList = _RedList
	helper.AddFieldSetters("redlist", _fieldSetters, _setters)
	_CasePriorityQueue, _fieldSetters := NewCasePriorityQueuesConfig()
	_obj._CasePriorityQueue = _CasePriorityQueue
	helper.AddFieldSetters("casepriorityqueue", _fieldSetters, _setters)
	_UpiVPANameMatchingCheckConfig, _fieldSetters := NewUpiVPANameMatchCheckConfig()
	_obj._UpiVPANameMatchingCheckConfig = _UpiVPANameMatchingCheckConfig
	helper.AddFieldSetters("upivpanamematchingcheckconfig", _fieldSetters, _setters)
	_AffluenceCheckConfig, _fieldSetters := NewAffluenceCheckConfig()
	_obj._AffluenceCheckConfig = _AffluenceCheckConfig
	helper.AddFieldSetters("affluencecheckconfig", _fieldSetters, _setters)
	_ScreenerConfig, _fieldSetters := NewScreenerConfig()
	_obj._ScreenerConfig = _ScreenerConfig
	helper.AddFieldSetters("screenerconfig", _fieldSetters, _setters)
	_JunkEmailCheckConfig, _fieldSetters := NewJunkEmailChecksConfig()
	_obj._JunkEmailCheckConfig = _JunkEmailCheckConfig
	helper.AddFieldSetters("junkemailcheckconfig", _fieldSetters, _setters)
	_LocationModelCheck, _fieldSetters := NewLocationModelCheckConfig()
	_obj._LocationModelCheck = _LocationModelCheck
	helper.AddFieldSetters("locationmodelcheck", _fieldSetters, _setters)
	_DynamicElementsConfig, _fieldSetters := NewDynamicElementsConfig()
	_obj._DynamicElementsConfig = _DynamicElementsConfig
	helper.AddFieldSetters("dynamicelementsconfig", _fieldSetters, _setters)
	_InstalledAppsCheckConfig, _fieldSetters := NewInstalledAppsCheckConfig()
	_obj._InstalledAppsCheckConfig = _InstalledAppsCheckConfig
	helper.AddFieldSetters("installedappscheckconfig", _fieldSetters, _setters)
	_CreditReportAffluenceClassConfig, _fieldSetters := NewCreditReportAffluenceClassConfig()
	_obj._CreditReportAffluenceClassConfig = _CreditReportAffluenceClassConfig
	helper.AddFieldSetters("creditreportaffluenceclassconfig", _fieldSetters, _setters)
	_CrossVideoFacematchCheckConfig, _fieldSetters := NewCrossVideoFacematchCheckConfig()
	_obj._CrossVideoFacematchCheckConfig = _CrossVideoFacematchCheckConfig
	helper.AddFieldSetters("crossvideofacematchcheckconfig", _fieldSetters, _setters)
	_SAUnreviewedAlertsCheckConfig, _fieldSetters := NewSAUnreviewedAlertsCheckConfig()
	_obj._SAUnreviewedAlertsCheckConfig = _SAUnreviewedAlertsCheckConfig
	helper.AddFieldSetters("saunreviewedalertscheckconfig", _fieldSetters, _setters)
	_ContactAssociationsCheckConfig, _fieldSetters := NewContactAssociationsCheckConfig()
	_obj._ContactAssociationsCheckConfig = _ContactAssociationsCheckConfig
	helper.AddFieldSetters("contactassociationscheckconfig", _fieldSetters, _setters)
	_RuleHitCallbackSubscriber, _fieldSetters := gencfg.NewSqsSubscriber()
	_obj._RuleHitCallbackSubscriber = _RuleHitCallbackSubscriber
	helper.AddFieldSetters("rulehitcallbacksubscriber", _fieldSetters, _setters)
	_ProfileBannerForSherlock, _fieldSetters := NewProfileBannerForSherlock()
	_obj._ProfileBannerForSherlock = _ProfileBannerForSherlock
	helper.AddFieldSetters("profilebannerforsherlock", _fieldSetters, _setters)
	_ValidateSavingsAccountStatusConfig, _fieldSetters := NewValidateSavingsAccountStatusConfig()
	_obj._ValidateSavingsAccountStatusConfig = _ValidateSavingsAccountStatusConfig
	helper.AddFieldSetters("validatesavingsaccountstatusconfig", _fieldSetters, _setters)
	_LowContactCountCheckConfig, _fieldSetters := NewLowContactCountCheckConfig()
	_obj._LowContactCountCheckConfig = _LowContactCountCheckConfig
	helper.AddFieldSetters("lowcontactcountcheckconfig", _fieldSetters, _setters)
	_VPNPresenceCheckConfig, _fieldSetters := NewVPNPresenceCheckConfig()
	_obj._VPNPresenceCheckConfig = _VPNPresenceCheckConfig
	helper.AddFieldSetters("vpnpresencecheckconfig", _fieldSetters, _setters)
	_PercentageRolloutReleaseConfig, _fieldSetters := NewPercentageRolloutReleaseConfig()
	_obj._PercentageRolloutReleaseConfig = _PercentageRolloutReleaseConfig
	helper.AddFieldSetters("percentagerolloutreleaseconfig", _fieldSetters, _setters)
	_IPAddressRedListCheckConfig, _fieldSetters := NewIPAddressRedListCheckConfig()
	_obj._IPAddressRedListCheckConfig = _IPAddressRedListCheckConfig
	helper.AddFieldSetters("ipaddressredlistcheckconfig", _fieldSetters, _setters)
	_ContactAssociationAndRiskyProfileCheckConfig, _fieldSetters := NewContactAssociationAndRiskyProfileCheckConfig()
	_obj._ContactAssociationAndRiskyProfileCheckConfig = _ContactAssociationAndRiskyProfileCheckConfig
	helper.AddFieldSetters("contactassociationandriskyprofilecheckconfig", _fieldSetters, _setters)

	return _obj, _setters
}

func (obj *Config) Init() {
	newObj, _ := NewConfig()
	*obj = *newObj
}

func (obj *Config) SetQuestSDK(questSdk questsdk.Client) {
}

func (obj *Config) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Config) GetQuestVariables() (vars []*questtypes.Variable, err error) {
	var (
		v            *questtypes.Variable
		childVars    []*questtypes.Variable
		childVarsErr error
	)
	_ = v
	_ = childVars
	_ = childVarsErr
	return vars, nil
}

func (obj *Config) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Config)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Config) setDynamicField(v *config.Config, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "hunterrolloutpercentage":
		return obj.SetHunterRollOutPercentage(v.HunterRollOutPercentage, true, nil)
	case "testflag":
		return obj.SetTestFlag(v.TestFlag, true, nil)
	case "ruletags":
		return obj.SetRuleTags(v.RuleTags, true, path)
	case "featureflags":
		return obj._FeatureFlags.Set(v.FeatureFlags, true, path)
	case "processredlistupdatesqssubscriber":
		return obj._ProcessRedListUpdateSqsSubscriber.Set(v.ProcessRedListUpdateSqsSubscriber, true, path)
	case "processsyncleaactorssqssubscriber":
		return obj._ProcessSyncLeaActorsSqsSubscriber.Set(v.ProcessSyncLeaActorsSqsSubscriber, true, path)
	case "processriskcasesingesteventbequeuesubscriber":
		return obj._ProcessRiskCasesIngestEventBEQueueSubscriber.Set(v.ProcessRiskCasesIngestEventBEQueueSubscriber, true, path)
	case "processriskcasesingesteventdataqueuesubscriber":
		return obj._ProcessRiskCasesIngestEventDataQueueSubscriber.Set(v.ProcessRiskCasesIngestEventDataQueueSubscriber, true, path)
	case "processriskalertingesteventdsqueuesubscriber":
		return obj._ProcessRiskAlertIngestEventDSQueueSubscriber.Set(v.ProcessRiskAlertIngestEventDSQueueSubscriber, true, path)
	case "processcallroutingeventsqssubscriber":
		return obj._ProcessCallRoutingEventSqsSubscriber.Set(v.ProcessCallRoutingEventSqsSubscriber, true, path)
	case "processdisputeuploadsqssubscriber":
		return obj._ProcessDisputeUploadSqsSubscriber.Set(v.ProcessDisputeUploadSqsSubscriber, true, path)
	case "processformsubmissioneventsqssubscriber":
		return obj._ProcessFormSubmissionEventSqsSubscriber.Set(v.ProcessFormSubmissionEventSqsSubscriber, true, path)
	case "onboardingvelocityconfig":
		return obj._OnboardingVelocityConfig.Set(v.OnboardingVelocityConfig, true, path)
	case "riskalertingestionqueuesubscriber":
		return obj._RiskAlertIngestionQueueSubscriber.Set(v.RiskAlertIngestionQueueSubscriber, true, path)
	case "riskbatchruleengineeventqueuesubscriber":
		return obj._RiskBatchRuleEngineEventQueueSubscriber.Set(v.RiskBatchRuleEngineEventQueueSubscriber, true, path)
	case "riskcxticketupdateeventqueuesubscriber":
		return obj._RiskCXTicketUpdateEventQueueSubscriber.Set(v.RiskCXTicketUpdateEventQueueSubscriber, true, path)
	case "riskaccountoperationstatusupdatequeuesubscriber":
		return obj._RiskAccountOperationStatusUpdateQueueSubscriber.Set(v.RiskAccountOperationStatusUpdateQueueSubscriber, true, path)
	case "processrisksignaleventqueuesubscriber":
		return obj._ProcessRiskSignalEventQueueSubscriber.Set(v.ProcessRiskSignalEventQueueSubscriber, true, path)
	case "processmnrlreportsqssubscriber":
		return obj._ProcessMnrlReportSqsSubscriber.Set(v.ProcessMnrlReportSqsSubscriber, true, path)
	case "processmnrlsuspectedflaggedmobilesqssubscriber":
		return obj._ProcessMnrlSuspectedFlaggedMobileSqsSubscriber.Set(v.ProcessMnrlSuspectedFlaggedMobileSqsSubscriber, true, path)
	case "processriskalerteventqueuesubscriber":
		return obj._ProcessRiskAlertEventQueueSubscriber.Set(v.ProcessRiskAlertEventQueueSubscriber, true, path)
	case "orderupdatetxnmonitoringsubscriber":
		return obj._OrderUpdateTxnMonitoringSubscriber.Set(v.OrderUpdateTxnMonitoringSubscriber, true, path)
	case "cmconsumerconfig":
		return obj._CMConsumerConfig.Set(v.CMConsumerConfig, true, path)
	case "profile":
		return obj._Profile.Set(v.Profile, true, path)
	case "redlist":
		return obj._RedList.Set(v.RedList, true, path)
	case "casepriorityqueue":
		return obj._CasePriorityQueue.Set(v.CasePriorityQueue, true, path)
	case "upivpanamematchingcheckconfig":
		return obj._UpiVPANameMatchingCheckConfig.Set(v.UpiVPANameMatchingCheckConfig, true, path)
	case "affluencecheckconfig":
		return obj._AffluenceCheckConfig.Set(v.AffluenceCheckConfig, true, path)
	case "screenerconfig":
		return obj._ScreenerConfig.Set(v.ScreenerConfig, true, path)
	case "junkemailcheckconfig":
		return obj._JunkEmailCheckConfig.Set(v.JunkEmailCheckConfig, true, path)
	case "locationmodelcheck":
		return obj._LocationModelCheck.Set(v.LocationModelCheck, true, path)
	case "dynamicelementsconfig":
		return obj._DynamicElementsConfig.Set(v.DynamicElementsConfig, true, path)
	case "installedappscheckconfig":
		return obj._InstalledAppsCheckConfig.Set(v.InstalledAppsCheckConfig, true, path)
	case "creditreportaffluenceclassconfig":
		return obj._CreditReportAffluenceClassConfig.Set(v.CreditReportAffluenceClassConfig, true, path)
	case "crossvideofacematchcheckconfig":
		return obj._CrossVideoFacematchCheckConfig.Set(v.CrossVideoFacematchCheckConfig, true, path)
	case "saunreviewedalertscheckconfig":
		return obj._SAUnreviewedAlertsCheckConfig.Set(v.SAUnreviewedAlertsCheckConfig, true, path)
	case "contactassociationscheckconfig":
		return obj._ContactAssociationsCheckConfig.Set(v.ContactAssociationsCheckConfig, true, path)
	case "rulehitcallbacksubscriber":
		return obj._RuleHitCallbackSubscriber.Set(v.RuleHitCallbackSubscriber, true, path)
	case "profilebannerforsherlock":
		return obj._ProfileBannerForSherlock.Set(v.ProfileBannerForSherlock, true, path)
	case "validatesavingsaccountstatusconfig":
		return obj._ValidateSavingsAccountStatusConfig.Set(v.ValidateSavingsAccountStatusConfig, true, path)
	case "lowcontactcountcheckconfig":
		return obj._LowContactCountCheckConfig.Set(v.LowContactCountCheckConfig, true, path)
	case "vpnpresencecheckconfig":
		return obj._VPNPresenceCheckConfig.Set(v.VPNPresenceCheckConfig, true, path)
	case "percentagerolloutreleaseconfig":
		return obj._PercentageRolloutReleaseConfig.Set(v.PercentageRolloutReleaseConfig, true, path)
	case "ipaddressredlistcheckconfig":
		return obj._IPAddressRedListCheckConfig.Set(v.IPAddressRedListCheckConfig, true, path)
	case "contactassociationandriskyprofilecheckconfig":
		return obj._ContactAssociationAndRiskyProfileCheckConfig.Set(v.ContactAssociationAndRiskyProfileCheckConfig, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Config) setDynamicFields(v *config.Config, dynamic bool, path []string) (err error) {

	err = obj.SetHunterRollOutPercentage(v.HunterRollOutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetTestFlag(v.TestFlag, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRuleTags(v.RuleTags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._FeatureFlags.Set(v.FeatureFlags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRedListUpdateSqsSubscriber.Set(v.ProcessRedListUpdateSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessSyncLeaActorsSqsSubscriber.Set(v.ProcessSyncLeaActorsSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRiskCasesIngestEventBEQueueSubscriber.Set(v.ProcessRiskCasesIngestEventBEQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRiskCasesIngestEventDataQueueSubscriber.Set(v.ProcessRiskCasesIngestEventDataQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRiskAlertIngestEventDSQueueSubscriber.Set(v.ProcessRiskAlertIngestEventDSQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessCallRoutingEventSqsSubscriber.Set(v.ProcessCallRoutingEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessDisputeUploadSqsSubscriber.Set(v.ProcessDisputeUploadSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessFormSubmissionEventSqsSubscriber.Set(v.ProcessFormSubmissionEventSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OnboardingVelocityConfig.Set(v.OnboardingVelocityConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskAlertIngestionQueueSubscriber.Set(v.RiskAlertIngestionQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskBatchRuleEngineEventQueueSubscriber.Set(v.RiskBatchRuleEngineEventQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskCXTicketUpdateEventQueueSubscriber.Set(v.RiskCXTicketUpdateEventQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RiskAccountOperationStatusUpdateQueueSubscriber.Set(v.RiskAccountOperationStatusUpdateQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRiskSignalEventQueueSubscriber.Set(v.ProcessRiskSignalEventQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessMnrlReportSqsSubscriber.Set(v.ProcessMnrlReportSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessMnrlSuspectedFlaggedMobileSqsSubscriber.Set(v.ProcessMnrlSuspectedFlaggedMobileSqsSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProcessRiskAlertEventQueueSubscriber.Set(v.ProcessRiskAlertEventQueueSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._OrderUpdateTxnMonitoringSubscriber.Set(v.OrderUpdateTxnMonitoringSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CMConsumerConfig.Set(v.CMConsumerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._Profile.Set(v.Profile, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RedList.Set(v.RedList, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CasePriorityQueue.Set(v.CasePriorityQueue, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._UpiVPANameMatchingCheckConfig.Set(v.UpiVPANameMatchingCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._AffluenceCheckConfig.Set(v.AffluenceCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ScreenerConfig.Set(v.ScreenerConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._JunkEmailCheckConfig.Set(v.JunkEmailCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LocationModelCheck.Set(v.LocationModelCheck, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._DynamicElementsConfig.Set(v.DynamicElementsConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._InstalledAppsCheckConfig.Set(v.InstalledAppsCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CreditReportAffluenceClassConfig.Set(v.CreditReportAffluenceClassConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._CrossVideoFacematchCheckConfig.Set(v.CrossVideoFacematchCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._SAUnreviewedAlertsCheckConfig.Set(v.SAUnreviewedAlertsCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ContactAssociationsCheckConfig.Set(v.ContactAssociationsCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._RuleHitCallbackSubscriber.Set(v.RuleHitCallbackSubscriber, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ProfileBannerForSherlock.Set(v.ProfileBannerForSherlock, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ValidateSavingsAccountStatusConfig.Set(v.ValidateSavingsAccountStatusConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._LowContactCountCheckConfig.Set(v.LowContactCountCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._VPNPresenceCheckConfig.Set(v.VPNPresenceCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._PercentageRolloutReleaseConfig.Set(v.PercentageRolloutReleaseConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._IPAddressRedListCheckConfig.Set(v.IPAddressRedListCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ContactAssociationAndRiskyProfileCheckConfig.Set(v.ContactAssociationAndRiskyProfileCheckConfig, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Config) setStaticFields(v *config.Config) error {

	obj._Application = v.Application
	obj._Server = v.Server
	obj._EpifiDb = v.EpifiDb
	obj._FRMDb = v.FRMDb
	obj._FRMPgdb = v.FRMPgdb
	obj._AWS = v.AWS
	obj._IsFRMPgdbEnabled = v.IsFRMPgdbEnabled
	obj._RedListLatLongPrecisions = v.RedListLatLongPrecisions
	obj._TxnMonitoringConfig = v.TxnMonitoringConfig
	obj._Secrets = v.Secrets
	obj._Tracing = v.Tracing
	obj._BlockingFlowErrorReportConfig = v.BlockingFlowErrorReportConfig
	obj._LeaActorsPublisher = v.LeaActorsPublisher
	obj._LEAComplaint = v.LEAComplaint
	obj._CaseStore = v.CaseStore
	obj._FIProductToSegmentMap = v.FIProductToSegmentMap
	obj._ContactAssociationsConfig = v.ContactAssociationsConfig
	obj._InvestigationEmail = v.InvestigationEmail
	obj._Escalation = v.Escalation
	obj._RiskBankActionComms = v.RiskBankActionComms
	obj._RiskBankAction = v.RiskBankAction
	obj._DronapayRuleHitCallbackPublisher = v.DronapayRuleHitCallbackPublisher
	obj._FormSubmissionEventPublisher = v.FormSubmissionEventPublisher
	obj._AlertsPublisher = v.AlertsPublisher
	obj._BatchRuleEngineS3 = v.BatchRuleEngineS3
	obj._WebForm = v.WebForm
	obj._UnifiedLEAComplaintConfig = v.UnifiedLEAComplaintConfig
	obj._WebFormConfig = v.WebFormConfig
	return nil
}

func (obj *Config) SetHunterRollOutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.HunterRollOutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._HunterRollOutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "HunterRollOutPercentage")
	}
	return nil
}
func (obj *Config) SetTestFlag(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.TestFlag", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._TestFlag, 1)
	} else {
		atomic.StoreUint32(&obj._TestFlag, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "TestFlag")
	}
	return nil
}
func (obj *Config) SetRuleTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *Config.RuleTags", reflect.TypeOf(val))
	}
	obj._RuleTagsMutex.Lock()
	defer obj._RuleTagsMutex.Unlock()
	obj._RuleTags = roarray.New[string](v)
	return nil
}

func NewFeatureFlags() (_obj *FeatureFlags, _setters map[string]dynconf.SetFunc) {
	_obj = &FeatureFlags{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["enablepgdbfortxnmonitoring"] = _obj.SetEnablePGDBForTxnMonitoring
	return _obj, _setters
}

func (obj *FeatureFlags) Init() {
	newObj, _ := NewFeatureFlags()
	*obj = *newObj
}

func (obj *FeatureFlags) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *FeatureFlags) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.FeatureFlags)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *FeatureFlags) setDynamicField(v *config.FeatureFlags, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "enablepgdbfortxnmonitoring":
		return obj.SetEnablePGDBForTxnMonitoring(v.EnablePGDBForTxnMonitoring, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *FeatureFlags) setDynamicFields(v *config.FeatureFlags, dynamic bool, path []string) (err error) {

	err = obj.SetEnablePGDBForTxnMonitoring(v.EnablePGDBForTxnMonitoring, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *FeatureFlags) setStaticFields(v *config.FeatureFlags) error {

	return nil
}

func (obj *FeatureFlags) SetEnablePGDBForTxnMonitoring(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *FeatureFlags.EnablePGDBForTxnMonitoring", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._EnablePGDBForTxnMonitoring, 1)
	} else {
		atomic.StoreUint32(&obj._EnablePGDBForTxnMonitoring, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "EnablePGDBForTxnMonitoring")
	}
	return nil
}

func NewOnboardingVelocityConfig() (_obj *OnboardingVelocityConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &OnboardingVelocityConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["threshold"] = _obj.SetThreshold
	_setters["d2hthreshold"] = _obj.SetD2HThreshold
	_setters["bucketprecision"] = _obj.SetBucketPrecision
	_setters["bucketexpiry"] = _obj.SetBucketExpiry
	_setters["queryrangeduration"] = _obj.SetQueryRangeDuration
	return _obj, _setters
}

func (obj *OnboardingVelocityConfig) Init() {
	newObj, _ := NewOnboardingVelocityConfig()
	*obj = *newObj
}

func (obj *OnboardingVelocityConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *OnboardingVelocityConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.OnboardingVelocityConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *OnboardingVelocityConfig) setDynamicField(v *config.OnboardingVelocityConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "threshold":
		return obj.SetThreshold(v.Threshold, true, nil)
	case "d2hthreshold":
		return obj.SetD2HThreshold(v.D2HThreshold, true, nil)
	case "bucketprecision":
		return obj.SetBucketPrecision(v.BucketPrecision, true, nil)
	case "bucketexpiry":
		return obj.SetBucketExpiry(v.BucketExpiry, true, nil)
	case "queryrangeduration":
		return obj.SetQueryRangeDuration(v.QueryRangeDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *OnboardingVelocityConfig) setDynamicFields(v *config.OnboardingVelocityConfig, dynamic bool, path []string) (err error) {

	err = obj.SetThreshold(v.Threshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetD2HThreshold(v.D2HThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBucketPrecision(v.BucketPrecision, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetBucketExpiry(v.BucketExpiry, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQueryRangeDuration(v.QueryRangeDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *OnboardingVelocityConfig) setStaticFields(v *config.OnboardingVelocityConfig) error {

	return nil
}

func (obj *OnboardingVelocityConfig) SetThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.Threshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Threshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Threshold")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetD2HThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int64)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.D2HThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._D2HThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "D2HThreshold")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetBucketPrecision(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.BucketPrecision", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BucketPrecision, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BucketPrecision")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetBucketExpiry(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.BucketExpiry", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._BucketExpiry, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "BucketExpiry")
	}
	return nil
}
func (obj *OnboardingVelocityConfig) SetQueryRangeDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *OnboardingVelocityConfig.QueryRangeDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._QueryRangeDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "QueryRangeDuration")
	}
	return nil
}

func NewCMConsumerConfig() (_obj *CMConsumerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CMConsumerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["processactiverulealerts"] = _obj.SetProcessActiveRuleAlerts

	_obj._CaseManagementRollOutPercentage = &syncmap.Map[string, uint64]{}
	_setters["casemanagementrolloutpercentage"] = _obj.SetCaseManagementRollOutPercentage
	return _obj, _setters
}

func (obj *CMConsumerConfig) Init() {
	newObj, _ := NewCMConsumerConfig()
	*obj = *newObj
}

func (obj *CMConsumerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CMConsumerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CMConsumerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CMConsumerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CMConsumerConfig) setDynamicField(v *config.CMConsumerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "processactiverulealerts":
		return obj.SetProcessActiveRuleAlerts(v.ProcessActiveRuleAlerts, true, nil)
	case "casemanagementrolloutpercentage":
		return obj.SetCaseManagementRollOutPercentage(v.CaseManagementRollOutPercentage, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CMConsumerConfig) setDynamicFields(v *config.CMConsumerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetProcessActiveRuleAlerts(v.ProcessActiveRuleAlerts, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCaseManagementRollOutPercentage(v.CaseManagementRollOutPercentage, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CMConsumerConfig) setStaticFields(v *config.CMConsumerConfig) error {

	obj._CreateAlertsBatchSize = v.CreateAlertsBatchSize
	return nil
}

func (obj *CMConsumerConfig) SetProcessActiveRuleAlerts(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CMConsumerConfig.ProcessActiveRuleAlerts", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._ProcessActiveRuleAlerts, 1)
	} else {
		atomic.StoreUint32(&obj._ProcessActiveRuleAlerts, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "ProcessActiveRuleAlerts")
	}
	return nil
}
func (obj *CMConsumerConfig) SetCaseManagementRollOutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]uint64)
	if !ok {
		return fmt.Errorf("invalid data type %v *CMConsumerConfig.CaseManagementRollOutPercentage", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._CaseManagementRollOutPercentage, v, path)
}

func NewProfile() (_obj *Profile, _setters map[string]dynconf.SetFunc) {
	_obj = &Profile{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["fetchreviewsperiod"] = _obj.SetFetchReviewsPeriod
	return _obj, _setters
}

func (obj *Profile) Init() {
	newObj, _ := NewProfile()
	*obj = *newObj
}

func (obj *Profile) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *Profile) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.Profile)
	if !ok {
		return fmt.Errorf("invalid data type %v *Profile", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *Profile) setDynamicField(v *config.Profile, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "fetchreviewsperiod":
		return obj.SetFetchReviewsPeriod(v.FetchReviewsPeriod, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *Profile) setDynamicFields(v *config.Profile, dynamic bool, path []string) (err error) {

	err = obj.SetFetchReviewsPeriod(v.FetchReviewsPeriod, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *Profile) setStaticFields(v *config.Profile) error {

	return nil
}

func (obj *Profile) SetFetchReviewsPeriod(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *Profile.FetchReviewsPeriod", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._FetchReviewsPeriod, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "FetchReviewsPeriod")
	}
	return nil
}

func NewRedListConfig() (_obj *RedListConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RedListConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["onboardingstatethreshold"] = _obj.SetOnboardingStateThreshold
	_setters["atmhighriskthreshold"] = _obj.SetAtmHighRiskThreshold
	_setters["atmmediumriskthreshold"] = _obj.SetAtmMediumRiskThreshold
	return _obj, _setters
}

func (obj *RedListConfig) Init() {
	newObj, _ := NewRedListConfig()
	*obj = *newObj
}

func (obj *RedListConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RedListConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RedListConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RedListConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RedListConfig) setDynamicField(v *config.RedListConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "onboardingstatethreshold":
		return obj.SetOnboardingStateThreshold(v.OnboardingStateThreshold, true, nil)
	case "atmhighriskthreshold":
		return obj.SetAtmHighRiskThreshold(v.AtmHighRiskThreshold, true, nil)
	case "atmmediumriskthreshold":
		return obj.SetAtmMediumRiskThreshold(v.AtmMediumRiskThreshold, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RedListConfig) setDynamicFields(v *config.RedListConfig, dynamic bool, path []string) (err error) {

	err = obj.SetOnboardingStateThreshold(v.OnboardingStateThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAtmHighRiskThreshold(v.AtmHighRiskThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAtmMediumRiskThreshold(v.AtmMediumRiskThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RedListConfig) setStaticFields(v *config.RedListConfig) error {

	return nil
}

func (obj *RedListConfig) SetOnboardingStateThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *RedListConfig.OnboardingStateThreshold", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._OnboardingStateThreshold, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "OnboardingStateThreshold")
	}
	return nil
}
func (obj *RedListConfig) SetAtmHighRiskThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *RedListConfig.AtmHighRiskThreshold", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._AtmHighRiskThreshold, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AtmHighRiskThreshold")
	}
	return nil
}
func (obj *RedListConfig) SetAtmMediumRiskThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(uint32)
	if !ok {
		return fmt.Errorf("invalid data type %v *RedListConfig.AtmMediumRiskThreshold", reflect.TypeOf(val))
	}
	atomic.StoreUint32(&obj._AtmMediumRiskThreshold, uint32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AtmMediumRiskThreshold")
	}
	return nil
}

func NewCasePriorityQueuesConfig() (_obj *CasePriorityQueuesConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CasePriorityQueuesConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["cachedcasestalethreshold"] = _obj.SetCachedCaseStaleThreshold

	_obj._QueueCapacityMap = &syncmap.Map[string, int]{}
	_setters["queuecapacitymap"] = _obj.SetQueueCapacityMap

	_obj._QueueTTLMap = &syncmap.Map[string, time.Duration]{}
	_setters["queuettlmap"] = _obj.SetQueueTTLMap
	_setters["livequeuetags"] = _obj.SetLiveQueueTags
	_obj._LiveQueueTagsMutex = &sync.RWMutex{}
	_setters["transactionqueuetags"] = _obj.SetTransactionQueueTags
	_obj._TransactionQueueTagsMutex = &sync.RWMutex{}
	_setters["postonboardingqueuetags"] = _obj.SetPostOnboardingQueueTags
	_obj._PostOnboardingQueueTagsMutex = &sync.RWMutex{}
	_setters["escalationqueuetags"] = _obj.SetEscalationQueueTags
	_obj._EscalationQueueTagsMutex = &sync.RWMutex{}
	_ModelSelectionRates, _fieldSetters := NewModelSelectionRates()
	_obj._ModelSelectionRates = _ModelSelectionRates
	helper.AddFieldSetters("modelselectionrates", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *CasePriorityQueuesConfig) Init() {
	newObj, _ := NewCasePriorityQueuesConfig()
	*obj = *newObj
}

func (obj *CasePriorityQueuesConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CasePriorityQueuesConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CasePriorityQueuesConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CasePriorityQueuesConfig) setDynamicField(v *config.CasePriorityQueuesConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "cachedcasestalethreshold":
		return obj.SetCachedCaseStaleThreshold(v.CachedCaseStaleThreshold, true, nil)
	case "queuecapacitymap":
		return obj.SetQueueCapacityMap(v.QueueCapacityMap, true, path)
	case "queuettlmap":
		return obj.SetQueueTTLMap(v.QueueTTLMap, true, path)
	case "livequeuetags":
		return obj.SetLiveQueueTags(v.LiveQueueTags, true, path)
	case "transactionqueuetags":
		return obj.SetTransactionQueueTags(v.TransactionQueueTags, true, path)
	case "postonboardingqueuetags":
		return obj.SetPostOnboardingQueueTags(v.PostOnboardingQueueTags, true, path)
	case "escalationqueuetags":
		return obj.SetEscalationQueueTags(v.EscalationQueueTags, true, path)
	case "modelselectionrates":
		return obj._ModelSelectionRates.Set(v.ModelSelectionRates, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CasePriorityQueuesConfig) setDynamicFields(v *config.CasePriorityQueuesConfig, dynamic bool, path []string) (err error) {

	err = obj.SetCachedCaseStaleThreshold(v.CachedCaseStaleThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetQueueCapacityMap(v.QueueCapacityMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetQueueTTLMap(v.QueueTTLMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetLiveQueueTags(v.LiveQueueTags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetTransactionQueueTags(v.TransactionQueueTags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetPostOnboardingQueueTags(v.PostOnboardingQueueTags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetEscalationQueueTags(v.EscalationQueueTags, dynamic, path)
	if err != nil {
		return err
	}
	err = obj._ModelSelectionRates.Set(v.ModelSelectionRates, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CasePriorityQueuesConfig) setStaticFields(v *config.CasePriorityQueuesConfig) error {

	return nil
}

func (obj *CasePriorityQueuesConfig) SetCachedCaseStaleThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.CachedCaseStaleThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._CachedCaseStaleThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CachedCaseStaleThreshold")
	}
	return nil
}
func (obj *CasePriorityQueuesConfig) SetQueueCapacityMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.QueueCapacityMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._QueueCapacityMap, v, path)
}
func (obj *CasePriorityQueuesConfig) SetQueueTTLMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.QueueTTLMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._QueueTTLMap, v, path)
}
func (obj *CasePriorityQueuesConfig) SetLiveQueueTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.LiveQueueTags", reflect.TypeOf(val))
	}
	obj._LiveQueueTagsMutex.Lock()
	defer obj._LiveQueueTagsMutex.Unlock()
	obj._LiveQueueTags = roarray.New[string](v)
	return nil
}
func (obj *CasePriorityQueuesConfig) SetTransactionQueueTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.TransactionQueueTags", reflect.TypeOf(val))
	}
	obj._TransactionQueueTagsMutex.Lock()
	defer obj._TransactionQueueTagsMutex.Unlock()
	obj._TransactionQueueTags = roarray.New[string](v)
	return nil
}
func (obj *CasePriorityQueuesConfig) SetPostOnboardingQueueTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.PostOnboardingQueueTags", reflect.TypeOf(val))
	}
	obj._PostOnboardingQueueTagsMutex.Lock()
	defer obj._PostOnboardingQueueTagsMutex.Unlock()
	obj._PostOnboardingQueueTags = roarray.New[string](v)
	return nil
}
func (obj *CasePriorityQueuesConfig) SetEscalationQueueTags(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *CasePriorityQueuesConfig.EscalationQueueTags", reflect.TypeOf(val))
	}
	obj._EscalationQueueTagsMutex.Lock()
	defer obj._EscalationQueueTagsMutex.Unlock()
	obj._EscalationQueueTags = roarray.New[string](v)
	return nil
}

func NewModelSelectionRates() (_obj *ModelSelectionRates, _setters map[string]dynconf.SetFunc) {
	_obj = &ModelSelectionRates{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["model1rate"] = _obj.SetModel1Rate
	_setters["model2rate"] = _obj.SetModel2Rate
	_setters["cgrate"] = _obj.SetCgRate
	_setters["usemodelrateforselection"] = _obj.SetUseModelRateForSelection
	return _obj, _setters
}

func (obj *ModelSelectionRates) Init() {
	newObj, _ := NewModelSelectionRates()
	*obj = *newObj
}

func (obj *ModelSelectionRates) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ModelSelectionRates) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ModelSelectionRates)
	if !ok {
		return fmt.Errorf("invalid data type %v *ModelSelectionRates", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ModelSelectionRates) setDynamicField(v *config.ModelSelectionRates, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "model1rate":
		return obj.SetModel1Rate(v.Model1Rate, true, nil)
	case "model2rate":
		return obj.SetModel2Rate(v.Model2Rate, true, nil)
	case "cgrate":
		return obj.SetCgRate(v.CgRate, true, nil)
	case "usemodelrateforselection":
		return obj.SetUseModelRateForSelection(v.UseModelRateForSelection, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ModelSelectionRates) setDynamicFields(v *config.ModelSelectionRates, dynamic bool, path []string) (err error) {

	err = obj.SetModel1Rate(v.Model1Rate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetModel2Rate(v.Model2Rate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCgRate(v.CgRate, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetUseModelRateForSelection(v.UseModelRateForSelection, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ModelSelectionRates) setStaticFields(v *config.ModelSelectionRates) error {

	return nil
}

func (obj *ModelSelectionRates) SetModel1Rate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int8)
	if !ok {
		return fmt.Errorf("invalid data type %v *ModelSelectionRates.Model1Rate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Model1Rate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Model1Rate")
	}
	return nil
}
func (obj *ModelSelectionRates) SetModel2Rate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int8)
	if !ok {
		return fmt.Errorf("invalid data type %v *ModelSelectionRates.Model2Rate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._Model2Rate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Model2Rate")
	}
	return nil
}
func (obj *ModelSelectionRates) SetCgRate(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int8)
	if !ok {
		return fmt.Errorf("invalid data type %v *ModelSelectionRates.CgRate", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._CgRate, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "CgRate")
	}
	return nil
}
func (obj *ModelSelectionRates) SetUseModelRateForSelection(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ModelSelectionRates.UseModelRateForSelection", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._UseModelRateForSelection, 1)
	} else {
		atomic.StoreUint32(&obj._UseModelRateForSelection, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "UseModelRateForSelection")
	}
	return nil
}

func NewUpiVPANameMatchCheckConfig() (_obj *UpiVPANameMatchCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &UpiVPANameMatchCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *UpiVPANameMatchCheckConfig) Init() {
	newObj, _ := NewUpiVPANameMatchCheckConfig()
	*obj = *newObj
}

func (obj *UpiVPANameMatchCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *UpiVPANameMatchCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.UpiVPANameMatchCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiVPANameMatchCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *UpiVPANameMatchCheckConfig) setDynamicField(v *config.UpiVPANameMatchCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *UpiVPANameMatchCheckConfig) setDynamicFields(v *config.UpiVPANameMatchCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *UpiVPANameMatchCheckConfig) setStaticFields(v *config.UpiVPANameMatchCheckConfig) error {

	obj._CompleteMismatchScore = v.CompleteMismatchScore
	obj._FailureScore = v.FailureScore
	return nil
}

func (obj *UpiVPANameMatchCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *UpiVPANameMatchCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewAffluenceCheckConfig() (_obj *AffluenceCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &AffluenceCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["lowaffluenceclassdsscorethreshold"] = _obj.SetLowAffluenceClassDSScoreThreshold
	_setters["lowaffluenceclassandselfemployeddsscorethreshold"] = _obj.SetLowAffluenceClassAndSelfEmployedDSScoreThreshold
	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._AffluenceCheckScoreMapping = &syncmap.Map[string, int]{}
	_setters["affluencecheckscoremapping"] = _obj.SetAffluenceCheckScoreMapping
	return _obj, _setters
}

func (obj *AffluenceCheckConfig) Init() {
	newObj, _ := NewAffluenceCheckConfig()
	*obj = *newObj
}

func (obj *AffluenceCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *AffluenceCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.AffluenceCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *AffluenceCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *AffluenceCheckConfig) setDynamicField(v *config.AffluenceCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "lowaffluenceclassdsscorethreshold":
		return obj.SetLowAffluenceClassDSScoreThreshold(v.LowAffluenceClassDSScoreThreshold, true, nil)
	case "lowaffluenceclassandselfemployeddsscorethreshold":
		return obj.SetLowAffluenceClassAndSelfEmployedDSScoreThreshold(v.LowAffluenceClassAndSelfEmployedDSScoreThreshold, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "affluencecheckscoremapping":
		return obj.SetAffluenceCheckScoreMapping(v.AffluenceCheckScoreMapping, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *AffluenceCheckConfig) setDynamicFields(v *config.AffluenceCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetLowAffluenceClassDSScoreThreshold(v.LowAffluenceClassDSScoreThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLowAffluenceClassAndSelfEmployedDSScoreThreshold(v.LowAffluenceClassAndSelfEmployedDSScoreThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetAffluenceCheckScoreMapping(v.AffluenceCheckScoreMapping, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *AffluenceCheckConfig) setStaticFields(v *config.AffluenceCheckConfig) error {

	return nil
}

func (obj *AffluenceCheckConfig) SetLowAffluenceClassDSScoreThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AffluenceCheckConfig.LowAffluenceClassDSScoreThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LowAffluenceClassDSScoreThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LowAffluenceClassDSScoreThreshold")
	}
	return nil
}
func (obj *AffluenceCheckConfig) SetLowAffluenceClassAndSelfEmployedDSScoreThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AffluenceCheckConfig.LowAffluenceClassAndSelfEmployedDSScoreThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LowAffluenceClassAndSelfEmployedDSScoreThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LowAffluenceClassAndSelfEmployedDSScoreThreshold")
	}
	return nil
}
func (obj *AffluenceCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *AffluenceCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *AffluenceCheckConfig) SetAffluenceCheckScoreMapping(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *AffluenceCheckConfig.AffluenceCheckScoreMapping", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._AffluenceCheckScoreMapping, v, path)
}

func NewScreenerConfig() (_obj *ScreenerConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ScreenerConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["screenerattemptmaxruntime"] = _obj.SetScreenerAttemptMaxRunTime

	_obj._CriteriaToRiskChecksConfigMap = &syncmap.Map[string, *RiskChecksConfig]{}
	_setters["criteriatoriskchecksconfigmap"] = _obj.SetCriteriaToRiskChecksConfigMap

	_obj._CriteriaToRiskActionThresholdMap = &syncmap.Map[string, *ScreenerActionThresholds]{}
	_setters["criteriatoriskactionthresholdmap"] = _obj.SetCriteriaToRiskActionThresholdMap

	_obj._RiskCheckStoredResultDefaultTTLMap = &syncmap.Map[string, time.Duration]{}
	_setters["riskcheckstoredresultdefaultttlmap"] = _obj.SetRiskCheckStoredResultDefaultTTLMap
	return _obj, _setters
}

func (obj *ScreenerConfig) Init() {
	newObj, _ := NewScreenerConfig()
	*obj = *newObj
}

func (obj *ScreenerConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ScreenerConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ScreenerConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ScreenerConfig) setDynamicField(v *config.ScreenerConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "screenerattemptmaxruntime":
		return obj.SetScreenerAttemptMaxRunTime(v.ScreenerAttemptMaxRunTime, true, nil)
	case "criteriatoriskchecksconfigmap":
		return obj.SetCriteriaToRiskChecksConfigMap(v.CriteriaToRiskChecksConfigMap, true, path)
	case "criteriatoriskactionthresholdmap":
		return obj.SetCriteriaToRiskActionThresholdMap(v.CriteriaToRiskActionThresholdMap, true, path)
	case "riskcheckstoredresultdefaultttlmap":
		return obj.SetRiskCheckStoredResultDefaultTTLMap(v.RiskCheckStoredResultDefaultTTLMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ScreenerConfig) setDynamicFields(v *config.ScreenerConfig, dynamic bool, path []string) (err error) {

	err = obj.SetScreenerAttemptMaxRunTime(v.ScreenerAttemptMaxRunTime, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCriteriaToRiskChecksConfigMap(v.CriteriaToRiskChecksConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetCriteriaToRiskActionThresholdMap(v.CriteriaToRiskActionThresholdMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetRiskCheckStoredResultDefaultTTLMap(v.RiskCheckStoredResultDefaultTTLMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ScreenerConfig) setStaticFields(v *config.ScreenerConfig) error {

	obj._CriteriaToRuleConfigMap = v.CriteriaToRuleConfigMap
	return nil
}

func (obj *ScreenerConfig) SetScreenerAttemptMaxRunTime(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerConfig.ScreenerAttemptMaxRunTime", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._ScreenerAttemptMaxRunTime, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ScreenerAttemptMaxRunTime")
	}
	return nil
}
func (obj *ScreenerConfig) SetCriteriaToRiskChecksConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.RiskChecksConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerConfig.CriteriaToRiskChecksConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._CriteriaToRiskChecksConfigMap, v, dynamic, path)

}
func (obj *ScreenerConfig) SetCriteriaToRiskActionThresholdMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.ScreenerActionThresholds)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerConfig.CriteriaToRiskActionThresholdMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._CriteriaToRiskActionThresholdMap, v, dynamic, path)

}
func (obj *ScreenerConfig) SetRiskCheckStoredResultDefaultTTLMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerConfig.RiskCheckStoredResultDefaultTTLMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._RiskCheckStoredResultDefaultTTLMap, v, path)
}

func NewRiskChecksConfig() (_obj *RiskChecksConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskChecksConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_obj._RiskParamConfigMap = &syncmap.Map[string, *RiskParamConfig]{}
	_setters["riskparamconfigmap"] = _obj.SetRiskParamConfigMap
	return _obj, _setters
}

func (obj *RiskChecksConfig) Init() {
	newObj, _ := NewRiskChecksConfig()
	*obj = *newObj
}

func (obj *RiskChecksConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskChecksConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskChecksConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskChecksConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskChecksConfig) setDynamicField(v *config.RiskChecksConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "riskparamconfigmap":
		return obj.SetRiskParamConfigMap(v.RiskParamConfigMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskChecksConfig) setDynamicFields(v *config.RiskChecksConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRiskParamConfigMap(v.RiskParamConfigMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskChecksConfig) setStaticFields(v *config.RiskChecksConfig) error {

	return nil
}

func (obj *RiskChecksConfig) SetRiskParamConfigMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]*config.RiskParamConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskChecksConfig.RiskParamConfigMap", reflect.TypeOf(val))
	}
	return helper.ApplyDynamicValueMap(obj._RiskParamConfigMap, v, dynamic, path)

}

func NewRiskParamConfig() (_obj *RiskParamConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &RiskParamConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["ismandatory"] = _obj.SetIsMandatory
	_setters["storedresultttl"] = _obj.SetStoredResultTTL
	return _obj, _setters
}

func (obj *RiskParamConfig) Init() {
	newObj, _ := NewRiskParamConfig()
	*obj = *newObj
}

func (obj *RiskParamConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *RiskParamConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.RiskParamConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskParamConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *RiskParamConfig) setDynamicField(v *config.RiskParamConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "ismandatory":
		return obj.SetIsMandatory(v.IsMandatory, true, nil)
	case "storedresultttl":
		return obj.SetStoredResultTTL(v.StoredResultTTL, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *RiskParamConfig) setDynamicFields(v *config.RiskParamConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsMandatory(v.IsMandatory, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetStoredResultTTL(v.StoredResultTTL, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *RiskParamConfig) setStaticFields(v *config.RiskParamConfig) error {

	return nil
}

func (obj *RiskParamConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskParamConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *RiskParamConfig) SetIsMandatory(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskParamConfig.IsMandatory", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsMandatory, 1)
	} else {
		atomic.StoreUint32(&obj._IsMandatory, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsMandatory")
	}
	return nil
}
func (obj *RiskParamConfig) SetStoredResultTTL(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *RiskParamConfig.StoredResultTTL", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._StoredResultTTL, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "StoredResultTTL")
	}
	return nil
}

func NewScreenerActionThresholds() (_obj *ScreenerActionThresholds, _setters map[string]dynconf.SetFunc) {
	_obj = &ScreenerActionThresholds{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["autoblockthreshold"] = _obj.SetAutoBlockThreshold
	_setters["manualreviewthreshold"] = _obj.SetManualReviewThreshold
	_ThresholdToTipManualReviewToAutoBlock, _fieldSetters := NewThresholdToTipManualReviewToAutoBlock()
	_obj._ThresholdToTipManualReviewToAutoBlock = _ThresholdToTipManualReviewToAutoBlock
	helper.AddFieldSetters("thresholdtotipmanualreviewtoautoblock", _fieldSetters, _setters)
	return _obj, _setters
}

func (obj *ScreenerActionThresholds) Init() {
	newObj, _ := NewScreenerActionThresholds()
	*obj = *newObj
}

func (obj *ScreenerActionThresholds) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ScreenerActionThresholds) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ScreenerActionThresholds)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerActionThresholds", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ScreenerActionThresholds) setDynamicField(v *config.ScreenerActionThresholds, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "autoblockthreshold":
		return obj.SetAutoBlockThreshold(v.AutoBlockThreshold, true, nil)
	case "manualreviewthreshold":
		return obj.SetManualReviewThreshold(v.ManualReviewThreshold, true, nil)
	case "thresholdtotipmanualreviewtoautoblock":
		return obj._ThresholdToTipManualReviewToAutoBlock.Set(v.ThresholdToTipManualReviewToAutoBlock, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ScreenerActionThresholds) setDynamicFields(v *config.ScreenerActionThresholds, dynamic bool, path []string) (err error) {

	err = obj.SetAutoBlockThreshold(v.AutoBlockThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetManualReviewThreshold(v.ManualReviewThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj._ThresholdToTipManualReviewToAutoBlock.Set(v.ThresholdToTipManualReviewToAutoBlock, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ScreenerActionThresholds) setStaticFields(v *config.ScreenerActionThresholds) error {

	return nil
}

func (obj *ScreenerActionThresholds) SetAutoBlockThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerActionThresholds.AutoBlockThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._AutoBlockThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "AutoBlockThreshold")
	}
	return nil
}
func (obj *ScreenerActionThresholds) SetManualReviewThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *ScreenerActionThresholds.ManualReviewThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._ManualReviewThreshold, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "ManualReviewThreshold")
	}
	return nil
}

func NewThresholdToTipManualReviewToAutoBlock() (_obj *ThresholdToTipManualReviewToAutoBlock, _setters map[string]dynconf.SetFunc) {
	_obj = &ThresholdToTipManualReviewToAutoBlock{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["threshold"] = _obj.SetThreshold
	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *ThresholdToTipManualReviewToAutoBlock) Init() {
	newObj, _ := NewThresholdToTipManualReviewToAutoBlock()
	*obj = *newObj
}

func (obj *ThresholdToTipManualReviewToAutoBlock) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ThresholdToTipManualReviewToAutoBlock) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ThresholdToTipManualReviewToAutoBlock)
	if !ok {
		return fmt.Errorf("invalid data type %v *ThresholdToTipManualReviewToAutoBlock", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ThresholdToTipManualReviewToAutoBlock) setDynamicField(v *config.ThresholdToTipManualReviewToAutoBlock, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "threshold":
		return obj.SetThreshold(v.Threshold, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ThresholdToTipManualReviewToAutoBlock) setDynamicFields(v *config.ThresholdToTipManualReviewToAutoBlock, dynamic bool, path []string) (err error) {

	err = obj.SetThreshold(v.Threshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ThresholdToTipManualReviewToAutoBlock) setStaticFields(v *config.ThresholdToTipManualReviewToAutoBlock) error {

	obj._FailureScore = v.FailureScore
	obj._ScreenerChecksOfInterest = v.ScreenerChecksOfInterest
	return nil
}

func (obj *ThresholdToTipManualReviewToAutoBlock) SetThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *ThresholdToTipManualReviewToAutoBlock.Threshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._Threshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "Threshold")
	}
	return nil
}
func (obj *ThresholdToTipManualReviewToAutoBlock) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ThresholdToTipManualReviewToAutoBlock.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewJunkEmailChecksConfig() (_obj *JunkEmailChecksConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &JunkEmailChecksConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._JunkEmailCheckScoreMapping = &syncmap.Map[string, int]{}
	_setters["junkemailcheckscoremapping"] = _obj.SetJunkEmailCheckScoreMapping
	return _obj, _setters
}

func (obj *JunkEmailChecksConfig) Init() {
	newObj, _ := NewJunkEmailChecksConfig()
	*obj = *newObj
}

func (obj *JunkEmailChecksConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *JunkEmailChecksConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.JunkEmailChecksConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *JunkEmailChecksConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *JunkEmailChecksConfig) setDynamicField(v *config.JunkEmailChecksConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "junkemailcheckscoremapping":
		return obj.SetJunkEmailCheckScoreMapping(v.JunkEmailCheckScoreMapping, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *JunkEmailChecksConfig) setDynamicFields(v *config.JunkEmailChecksConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetJunkEmailCheckScoreMapping(v.JunkEmailCheckScoreMapping, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *JunkEmailChecksConfig) setStaticFields(v *config.JunkEmailChecksConfig) error {

	return nil
}

func (obj *JunkEmailChecksConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *JunkEmailChecksConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *JunkEmailChecksConfig) SetJunkEmailCheckScoreMapping(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *JunkEmailChecksConfig.JunkEmailCheckScoreMapping", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._JunkEmailCheckScoreMapping, v, path)
}

func NewLocationModelCheckConfig() (_obj *LocationModelCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LocationModelCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._RiskSeverityToScoreMap = &syncmap.Map[string, int]{}
	_setters["riskseveritytoscoremap"] = _obj.SetRiskSeverityToScoreMap
	return _obj, _setters
}

func (obj *LocationModelCheckConfig) Init() {
	newObj, _ := NewLocationModelCheckConfig()
	*obj = *newObj
}

func (obj *LocationModelCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LocationModelCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LocationModelCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LocationModelCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LocationModelCheckConfig) setDynamicField(v *config.LocationModelCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "riskseveritytoscoremap":
		return obj.SetRiskSeverityToScoreMap(v.RiskSeverityToScoreMap, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LocationModelCheckConfig) setDynamicFields(v *config.LocationModelCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetRiskSeverityToScoreMap(v.RiskSeverityToScoreMap, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LocationModelCheckConfig) setStaticFields(v *config.LocationModelCheckConfig) error {

	return nil
}

func (obj *LocationModelCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *LocationModelCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *LocationModelCheckConfig) SetRiskSeverityToScoreMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *LocationModelCheckConfig.RiskSeverityToScoreMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._RiskSeverityToScoreMap, v, path)
}

func NewDynamicElementsConfig() (_obj *DynamicElementsConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &DynamicElementsConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *DynamicElementsConfig) Init() {
	newObj, _ := NewDynamicElementsConfig()
	*obj = *newObj
}

func (obj *DynamicElementsConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *DynamicElementsConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.DynamicElementsConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *DynamicElementsConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *DynamicElementsConfig) setDynamicField(v *config.DynamicElementsConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *DynamicElementsConfig) setDynamicFields(v *config.DynamicElementsConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *DynamicElementsConfig) setStaticFields(v *config.DynamicElementsConfig) error {

	obj._FreezeStatusToBannerConfigMap = v.FreezeStatusToBannerConfigMap
	obj._OutcallBanner = v.OutcallBanner
	obj._DurationToCheckPreviousLEAComplaints = v.DurationToCheckPreviousLEAComplaints
	return nil
}

func (obj *DynamicElementsConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *DynamicElementsConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewInstalledAppsCheckConfig() (_obj *InstalledAppsCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &InstalledAppsCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["minrequiredsocialappcount"] = _obj.SetMinRequiredSocialAppCount
	_setters["minrequirednonsystemappcount"] = _obj.SetMinRequiredNonSystemAppCount
	_setters["isenabled"] = _obj.SetIsEnabled

	_obj._CheckToScoreMap = &syncmap.Map[string, int]{}
	_setters["checktoscoremap"] = _obj.SetCheckToScoreMap
	_setters["socialapps"] = _obj.SetSocialApps
	_obj._SocialAppsMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *InstalledAppsCheckConfig) Init() {
	newObj, _ := NewInstalledAppsCheckConfig()
	*obj = *newObj
}

func (obj *InstalledAppsCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *InstalledAppsCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.InstalledAppsCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *InstalledAppsCheckConfig) setDynamicField(v *config.InstalledAppsCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "minrequiredsocialappcount":
		return obj.SetMinRequiredSocialAppCount(v.MinRequiredSocialAppCount, true, nil)
	case "minrequirednonsystemappcount":
		return obj.SetMinRequiredNonSystemAppCount(v.MinRequiredNonSystemAppCount, true, nil)
	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "checktoscoremap":
		return obj.SetCheckToScoreMap(v.CheckToScoreMap, true, path)
	case "socialapps":
		return obj.SetSocialApps(v.SocialApps, true, path)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *InstalledAppsCheckConfig) setDynamicFields(v *config.InstalledAppsCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetMinRequiredSocialAppCount(v.MinRequiredSocialAppCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetMinRequiredNonSystemAppCount(v.MinRequiredNonSystemAppCount, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetCheckToScoreMap(v.CheckToScoreMap, dynamic, path)
	if err != nil {
		return err
	}
	err = obj.SetSocialApps(v.SocialApps, dynamic, path)
	if err != nil {
		return err
	}
	return nil
}

func (obj *InstalledAppsCheckConfig) setStaticFields(v *config.InstalledAppsCheckConfig) error {

	obj._DurationToCheckForPreviousFailures = v.DurationToCheckForPreviousFailures
	return nil
}

func (obj *InstalledAppsCheckConfig) SetMinRequiredSocialAppCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig.MinRequiredSocialAppCount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinRequiredSocialAppCount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinRequiredSocialAppCount")
	}
	return nil
}
func (obj *InstalledAppsCheckConfig) SetMinRequiredNonSystemAppCount(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int32)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig.MinRequiredNonSystemAppCount", reflect.TypeOf(val))
	}
	atomic.StoreInt32(&obj._MinRequiredNonSystemAppCount, int32(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "MinRequiredNonSystemAppCount")
	}
	return nil
}
func (obj *InstalledAppsCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *InstalledAppsCheckConfig) SetCheckToScoreMap(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(map[string]int)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig.CheckToScoreMap", reflect.TypeOf(val))
	}

	return helper.ApplyStaticValueMap(obj._CheckToScoreMap, v, path)
}
func (obj *InstalledAppsCheckConfig) SetSocialApps(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.([]string)
	if !ok {
		return fmt.Errorf("invalid data type %v *InstalledAppsCheckConfig.SocialApps", reflect.TypeOf(val))
	}
	obj._SocialAppsMutex.Lock()
	defer obj._SocialAppsMutex.Unlock()
	obj._SocialApps = roarray.New[string](v)
	return nil
}

func NewCreditReportAffluenceClassConfig() (_obj *CreditReportAffluenceClassConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CreditReportAffluenceClassConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *CreditReportAffluenceClassConfig) Init() {
	newObj, _ := NewCreditReportAffluenceClassConfig()
	*obj = *newObj
}

func (obj *CreditReportAffluenceClassConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CreditReportAffluenceClassConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CreditReportAffluenceClassConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportAffluenceClassConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CreditReportAffluenceClassConfig) setDynamicField(v *config.CreditReportAffluenceClassConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CreditReportAffluenceClassConfig) setDynamicFields(v *config.CreditReportAffluenceClassConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CreditReportAffluenceClassConfig) setStaticFields(v *config.CreditReportAffluenceClassConfig) error {

	obj._InvalidCreditScoreThreshold = v.InvalidCreditScoreThreshold
	obj._FailCreditScoreThreshold = v.FailCreditScoreThreshold
	obj._PassCreditScoreThreshold = v.PassCreditScoreThreshold
	obj._FailureScore = v.FailureScore
	obj._BadAffluenceIncomeThreshold = v.BadAffluenceIncomeThreshold
	obj._SuitFiledWilfulDefault = v.SuitFiledWilfulDefault
	obj._SuitFiledWillfulDefaultWrittenOff = v.SuitFiledWillfulDefaultWrittenOff
	return nil
}

func (obj *CreditReportAffluenceClassConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CreditReportAffluenceClassConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewCrossVideoFacematchCheckConfig() (_obj *CrossVideoFacematchCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &CrossVideoFacematchCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *CrossVideoFacematchCheckConfig) Init() {
	newObj, _ := NewCrossVideoFacematchCheckConfig()
	*obj = *newObj
}

func (obj *CrossVideoFacematchCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *CrossVideoFacematchCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.CrossVideoFacematchCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *CrossVideoFacematchCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *CrossVideoFacematchCheckConfig) setDynamicField(v *config.CrossVideoFacematchCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *CrossVideoFacematchCheckConfig) setDynamicFields(v *config.CrossVideoFacematchCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *CrossVideoFacematchCheckConfig) setStaticFields(v *config.CrossVideoFacematchCheckConfig) error {

	obj._LivenessScoreCutoff = v.LivenessScoreCutoff
	obj._FaceMatchScoreCutoff = v.FaceMatchScoreCutoff
	obj._FailureScore = v.FailureScore
	return nil
}

func (obj *CrossVideoFacematchCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *CrossVideoFacematchCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewSAUnreviewedAlertsCheckConfig() (_obj *SAUnreviewedAlertsCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &SAUnreviewedAlertsCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	_setters["highprecisionrulehitduration"] = _obj.SetHighPrecisionRuleHitDuration
	_setters["lowprecisionrulehitduration"] = _obj.SetLowPrecisionRuleHitDuration
	return _obj, _setters
}

func (obj *SAUnreviewedAlertsCheckConfig) Init() {
	newObj, _ := NewSAUnreviewedAlertsCheckConfig()
	*obj = *newObj
}

func (obj *SAUnreviewedAlertsCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *SAUnreviewedAlertsCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.SAUnreviewedAlertsCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *SAUnreviewedAlertsCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *SAUnreviewedAlertsCheckConfig) setDynamicField(v *config.SAUnreviewedAlertsCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	case "highprecisionrulehitduration":
		return obj.SetHighPrecisionRuleHitDuration(v.HighPrecisionRuleHitDuration, true, nil)
	case "lowprecisionrulehitduration":
		return obj.SetLowPrecisionRuleHitDuration(v.LowPrecisionRuleHitDuration, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *SAUnreviewedAlertsCheckConfig) setDynamicFields(v *config.SAUnreviewedAlertsCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetHighPrecisionRuleHitDuration(v.HighPrecisionRuleHitDuration, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLowPrecisionRuleHitDuration(v.LowPrecisionRuleHitDuration, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *SAUnreviewedAlertsCheckConfig) setStaticFields(v *config.SAUnreviewedAlertsCheckConfig) error {

	obj._HighPrecisionRuleThreshold = v.HighPrecisionRuleThreshold
	return nil
}

func (obj *SAUnreviewedAlertsCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *SAUnreviewedAlertsCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
func (obj *SAUnreviewedAlertsCheckConfig) SetHighPrecisionRuleHitDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SAUnreviewedAlertsCheckConfig.HighPrecisionRuleHitDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._HighPrecisionRuleHitDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "HighPrecisionRuleHitDuration")
	}
	return nil
}
func (obj *SAUnreviewedAlertsCheckConfig) SetLowPrecisionRuleHitDuration(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(time.Duration)
	if !ok {
		return fmt.Errorf("invalid data type %v *SAUnreviewedAlertsCheckConfig.LowPrecisionRuleHitDuration", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LowPrecisionRuleHitDuration, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LowPrecisionRuleHitDuration")
	}
	return nil
}

func NewContactAssociationsCheckConfig() (_obj *ContactAssociationsCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ContactAssociationsCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *ContactAssociationsCheckConfig) Init() {
	newObj, _ := NewContactAssociationsCheckConfig()
	*obj = *newObj
}

func (obj *ContactAssociationsCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ContactAssociationsCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ContactAssociationsCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactAssociationsCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ContactAssociationsCheckConfig) setDynamicField(v *config.ContactAssociationsCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ContactAssociationsCheckConfig) setDynamicFields(v *config.ContactAssociationsCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ContactAssociationsCheckConfig) setStaticFields(v *config.ContactAssociationsCheckConfig) error {

	obj._IngressMaxAssociationsWithLEAThreshold = v.IngressMaxAssociationsWithLEAThreshold
	obj._IngressManualReviewForLEAThreshold = v.IngressManualReviewForLEAThreshold
	obj._IngressManualReviewForBlockedThreshold = v.IngressManualReviewForBlockedThreshold
	obj._EgressMaxAssociationsWithLEAThreshold = v.EgressMaxAssociationsWithLEAThreshold
	obj._FailedCheckScore = v.FailedCheckScore
	obj._ManualReviewFailCheckScore = v.ManualReviewFailCheckScore
	return nil
}

func (obj *ContactAssociationsCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactAssociationsCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewProfileBannerForSherlock() (_obj *ProfileBannerForSherlock, _setters map[string]dynconf.SetFunc) {
	_obj = &ProfileBannerForSherlock{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *ProfileBannerForSherlock) Init() {
	newObj, _ := NewProfileBannerForSherlock()
	*obj = *newObj
}

func (obj *ProfileBannerForSherlock) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ProfileBannerForSherlock) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ProfileBannerForSherlock)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProfileBannerForSherlock", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ProfileBannerForSherlock) setDynamicField(v *config.ProfileBannerForSherlock, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ProfileBannerForSherlock) setDynamicFields(v *config.ProfileBannerForSherlock, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ProfileBannerForSherlock) setStaticFields(v *config.ProfileBannerForSherlock) error {

	obj._MaxBannersForType = v.MaxBannersForType
	return nil
}

func (obj *ProfileBannerForSherlock) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ProfileBannerForSherlock.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewValidateSavingsAccountStatusConfig() (_obj *ValidateSavingsAccountStatusConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ValidateSavingsAccountStatusConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *ValidateSavingsAccountStatusConfig) Init() {
	newObj, _ := NewValidateSavingsAccountStatusConfig()
	*obj = *newObj
}

func (obj *ValidateSavingsAccountStatusConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ValidateSavingsAccountStatusConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ValidateSavingsAccountStatusConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ValidateSavingsAccountStatusConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ValidateSavingsAccountStatusConfig) setDynamicField(v *config.ValidateSavingsAccountStatusConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ValidateSavingsAccountStatusConfig) setDynamicFields(v *config.ValidateSavingsAccountStatusConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ValidateSavingsAccountStatusConfig) setStaticFields(v *config.ValidateSavingsAccountStatusConfig) error {

	obj._FailedCheckScore = v.FailedCheckScore
	return nil
}

func (obj *ValidateSavingsAccountStatusConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ValidateSavingsAccountStatusConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewLowContactCountCheckConfig() (_obj *LowContactCountCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &LowContactCountCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["lowcontactcountmanualreviewthreshold"] = _obj.SetLowContactCountManualReviewThreshold
	_setters["lowcontactcountflagthreshold"] = _obj.SetLowContactCountFlagThreshold
	return _obj, _setters
}

func (obj *LowContactCountCheckConfig) Init() {
	newObj, _ := NewLowContactCountCheckConfig()
	*obj = *newObj
}

func (obj *LowContactCountCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *LowContactCountCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.LowContactCountCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *LowContactCountCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *LowContactCountCheckConfig) setDynamicField(v *config.LowContactCountCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "lowcontactcountmanualreviewthreshold":
		return obj.SetLowContactCountManualReviewThreshold(v.LowContactCountManualReviewThreshold, true, nil)
	case "lowcontactcountflagthreshold":
		return obj.SetLowContactCountFlagThreshold(v.LowContactCountFlagThreshold, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *LowContactCountCheckConfig) setDynamicFields(v *config.LowContactCountCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetLowContactCountManualReviewThreshold(v.LowContactCountManualReviewThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetLowContactCountFlagThreshold(v.LowContactCountFlagThreshold, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *LowContactCountCheckConfig) setStaticFields(v *config.LowContactCountCheckConfig) error {

	return nil
}

func (obj *LowContactCountCheckConfig) SetLowContactCountManualReviewThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *LowContactCountCheckConfig.LowContactCountManualReviewThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LowContactCountManualReviewThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LowContactCountManualReviewThreshold")
	}
	return nil
}
func (obj *LowContactCountCheckConfig) SetLowContactCountFlagThreshold(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *LowContactCountCheckConfig.LowContactCountFlagThreshold", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._LowContactCountFlagThreshold, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "LowContactCountFlagThreshold")
	}
	return nil
}

func NewVPNPresenceCheckConfig() (_obj *VPNPresenceCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &VPNPresenceCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *VPNPresenceCheckConfig) Init() {
	newObj, _ := NewVPNPresenceCheckConfig()
	*obj = *newObj
}

func (obj *VPNPresenceCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *VPNPresenceCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.VPNPresenceCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *VPNPresenceCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *VPNPresenceCheckConfig) setDynamicField(v *config.VPNPresenceCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *VPNPresenceCheckConfig) setDynamicFields(v *config.VPNPresenceCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *VPNPresenceCheckConfig) setStaticFields(v *config.VPNPresenceCheckConfig) error {

	return nil
}

func (obj *VPNPresenceCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *VPNPresenceCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewPercentageRolloutReleaseConfig() (_obj *PercentageRolloutReleaseConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &PercentageRolloutReleaseConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["rolloutpercentage"] = _obj.SetRolloutPercentage
	_setters["featurename"] = _obj.SetFeatureName
	_obj._FeatureNameMutex = &sync.RWMutex{}
	return _obj, _setters
}

func (obj *PercentageRolloutReleaseConfig) Init() {
	newObj, _ := NewPercentageRolloutReleaseConfig()
	*obj = *newObj
}

func (obj *PercentageRolloutReleaseConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *PercentageRolloutReleaseConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.PercentageRolloutReleaseConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRolloutReleaseConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *PercentageRolloutReleaseConfig) setDynamicField(v *config.PercentageRolloutReleaseConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "rolloutpercentage":
		return obj.SetRolloutPercentage(v.RolloutPercentage, true, nil)
	case "featurename":
		return obj.SetFeatureName(v.FeatureName, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *PercentageRolloutReleaseConfig) setDynamicFields(v *config.PercentageRolloutReleaseConfig, dynamic bool, path []string) (err error) {

	err = obj.SetRolloutPercentage(v.RolloutPercentage, dynamic, nil)
	if err != nil {
		return err
	}
	err = obj.SetFeatureName(v.FeatureName, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *PercentageRolloutReleaseConfig) setStaticFields(v *config.PercentageRolloutReleaseConfig) error {

	return nil
}

func (obj *PercentageRolloutReleaseConfig) SetRolloutPercentage(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(int)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRolloutReleaseConfig.RolloutPercentage", reflect.TypeOf(val))
	}
	atomic.StoreInt64(&obj._RolloutPercentage, int64(v))
	if dynamic {
		helper.RunCallback(obj.callbacks, "RolloutPercentage")
	}
	return nil
}
func (obj *PercentageRolloutReleaseConfig) SetFeatureName(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(string)
	if !ok {
		return fmt.Errorf("invalid data type %v *PercentageRolloutReleaseConfig.FeatureName", reflect.TypeOf(val))
	}
	obj._FeatureNameMutex.Lock()
	defer obj._FeatureNameMutex.Unlock()
	obj._FeatureName = v
	if dynamic {
		helper.RunCallback(obj.callbacks, "FeatureName")
	}
	return nil
}

func NewIPAddressRedListCheckConfig() (_obj *IPAddressRedListCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &IPAddressRedListCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *IPAddressRedListCheckConfig) Init() {
	newObj, _ := NewIPAddressRedListCheckConfig()
	*obj = *newObj
}

func (obj *IPAddressRedListCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *IPAddressRedListCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.IPAddressRedListCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *IPAddressRedListCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *IPAddressRedListCheckConfig) setDynamicField(v *config.IPAddressRedListCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *IPAddressRedListCheckConfig) setDynamicFields(v *config.IPAddressRedListCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *IPAddressRedListCheckConfig) setStaticFields(v *config.IPAddressRedListCheckConfig) error {

	obj._ManualReviewScore = v.ManualReviewScore
	obj._MinRedListScoreToSendForManualReview = v.MinRedListScoreToSendForManualReview
	return nil
}

func (obj *IPAddressRedListCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *IPAddressRedListCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}

func NewContactAssociationAndRiskyProfileCheckConfig() (_obj *ContactAssociationAndRiskyProfileCheckConfig, _setters map[string]dynconf.SetFunc) {
	_obj = &ContactAssociationAndRiskyProfileCheckConfig{
		callbacks: &syncmap.Map[string, helper.FieldChangeCallback]{},
	}

	_setters = make(map[string]dynconf.SetFunc)
	_setters[""] = _obj.Set

	_setters["isenabled"] = _obj.SetIsEnabled
	return _obj, _setters
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) Init() {
	newObj, _ := NewContactAssociationAndRiskyProfileCheckConfig()
	*obj = *newObj
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) SetCallBack(staticFieldName string, fn helper.FieldChangeCallback) error {
	return helper.SetCallback(obj, obj.callbacks, staticFieldName, fn)
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) Set(val interface{}, dynamic bool, path []string) error {
	v, ok := val.(*config.ContactAssociationAndRiskyProfileCheckConfig)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactAssociationAndRiskyProfileCheckConfig", reflect.TypeOf(val))
	}
	if v == nil {
		return nil
	}

	if !dynamic {
		obj.setStaticFields(v)
	}
	if len(path) != 0 {
		return obj.setDynamicField(v, path)
	}
	return obj.setDynamicFields(v, dynamic, path)
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) setDynamicField(v *config.ContactAssociationAndRiskyProfileCheckConfig, path []string) error {
	field := path[0]
	path = path[1:]
	switch strings.ToLower(field) {

	case "isenabled":
		return obj.SetIsEnabled(v.IsEnabled, true, nil)
	default:
		return fmt.Errorf("unknown dynamic field %s", field)
	}
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) setDynamicFields(v *config.ContactAssociationAndRiskyProfileCheckConfig, dynamic bool, path []string) (err error) {

	err = obj.SetIsEnabled(v.IsEnabled, dynamic, nil)
	if err != nil {
		return err
	}
	return nil
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) setStaticFields(v *config.ContactAssociationAndRiskyProfileCheckConfig) error {

	obj._ManualReviewScore = v.ManualReviewScore
	return nil
}

func (obj *ContactAssociationAndRiskyProfileCheckConfig) SetIsEnabled(val interface{}, dynamic bool, path []string) error {
	if val == nil {
		return nil
	}
	v, ok := val.(bool)
	if !ok {
		return fmt.Errorf("invalid data type %v *ContactAssociationAndRiskyProfileCheckConfig.IsEnabled", reflect.TypeOf(val))
	}
	if v {
		atomic.StoreUint32(&obj._IsEnabled, 1)
	} else {
		atomic.StoreUint32(&obj._IsEnabled, 0)
	}
	if dynamic {
		helper.RunCallback(obj.callbacks, "IsEnabled")
	}
	return nil
}
