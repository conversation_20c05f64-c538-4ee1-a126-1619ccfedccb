Application:
  Environment: "prod"
  Name: "risk"

EpifiDb:
  AppName: "risk"
  StatementTimeout: 10s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 15
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FRMDb:
  AppName: "risk"
  StatementTimeout: 5s
  Username: "frm_dev_user"
  Password: ""
  Name: "frm"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/cockroach/ca.crt"
  SSLClientCert: "prod/cockroach/client.frm_dev_user.crt"
  SSLClientKey: "prod/cockroach/client.frm_dev_user.key"
  MaxOpenConn: 10
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

FRMPgdb:
  AppName: "risk"
  StatementTimeout: 5s
  Name: "frm_pgdb"
  EnableDebug: false
  SSLMode: "verify-full"
  SSLRootCert: "prod/rds/rds-ca-root-2061"
  MaxOpenConn: 20
  MaxIdleConn: 5
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "WARN"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: false

IsFRMPgdbEnabled: true

AWS:
  Region: "ap-south-1"
  S3:
    BucketNames:
      DataBucket: "epifi-data-services"
      OnboardingBucket: "epifi-prod-onboarding"
      BAMetricsBucket: "epifi-ba-metrics"

ProcessRedListUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-redlist-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"

LeaActorsPublisher:
  QueueName: "prod-risk-sync-lea-actors-queue"

ProcessSyncLeaActorsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-sync-lea-actors-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 10
      TimeUnit: "Minute"

ProcessRiskCasesIngestEventBEQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-cases-ingestion-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 3
        Period: 1m
    Namespace: "risk"

ProcessRiskSignalEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-data-risk-bq-signal-ingestion-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 60
        Period: 1m
    Namespace: "risk"

ProcessRiskCasesIngestEventDataQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-data-risk-cases-ingestion-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 4
        Period: 1m
    Namespace: "risk"

ProcessRiskAlertIngestEventDSQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-ds-risk-alert-ingestion-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 3
        Period: 1m
    Namespace: "risk"

ProcessDisputeUploadSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-dispute-upload-queue"
  RetryStrategy:
    RegularIntervalWithJitter:
      Interval: 20
      MaxAttempts: 10
      TimeUnit: "Minute"
      Jitter: 0.5
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "risk"

RiskAlertIngestionQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-alert-ingestion-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 4
        Period: 1m
    Namespace: "risk"

RiskBatchRuleEngineEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-batch-rule-engine-event-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 15
      MaxAttempts: 5
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m

AlertsPublisher:
  QueueName: "prod-risk-alert-ingestion-queue"

BatchRuleEngineS3:
  BucketName: "epifi-ba-metrics"

FormSubmissionEventPublisher:
  QueueName: "prod-risk-form-submission-event-queue"

ProcessFormSubmissionEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-form-submission-event-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 15min, 30min, 45min, 60min...
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 15
          MaxAttempts: 7
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "risk"

Profiling:
  StackDriverProfiling:
    ProjectId: "epifi-prod"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-prod-automatic-profiling"
    CPUPercentageLimit: 80
    MemoryPercentageLimit: 80
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

OnboardingVelocityConfig:
  QueryRangeDuration: 24h
  Threshold: 3
  D2HThreshold: 20
  BucketExpiry: 48h
  BucketPrecision: 3

BlockingWorkFlowConfig:
  FromEmail:  "<EMAIL>"

Secrets:
  Ids:
    EncryptorData: "prod/risk/aes/txn-monitoring-aes-encryption-data"
    FRMPgdbUsernamePassword: "prod/rds/epifimetis/frm_pgdb_dev_user"

OrderUpdateTxnMonitoringSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-update-transaction-monitoring-queue"
  Disable: false
  RetryStrategy:
    Hybrid: # Exponential backoff followed by regular interval
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 3
          TimeUnit: "Minute"
      MaxAttempts: 6
      CutOff: 3
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 75
        Period: 1s
    Namespace: "risk-txn"

TxnMonitoringConfig:
  UpdateEntityDate: "2022-11-29T00:00:00.00Z"
  ThresholdForSyncWithoutCategory: "10m"

Tracing:
  Enable: true

HunterRollOutPercentage: 0

CMConsumerConfig:
  CreateAlertsBatchSize: 1000
  CaseManagementRollOutPercentage:
    PAYLOAD_TYPE_USER_REVIEW: 100
  ProcessActiveRuleAlerts: true

Profile:
  FetchReviewsPeriod: "720h" # 30 Days

BlockingFlowErrorReportConfig:
  ReportName: "Blocking flow error report"
  ToEmailId: "<EMAIL>"
  FromEmailId: "<EMAIL>"

AffluenceCheckConfig:
  IsEnabled: false
  LowAffluenceClassDSScoreThreshold: 82
  LowAffluenceClassAndSelfEmployedDSScoreThreshold: 82
  AffluenceCheckScoreMapping:
    AFFLUENCE_CHECK_LOW_CLASS_AND_HIGH_RISK_SCORE: 92
    AFFLUENCE_CHECK_LOW_CLASS_AND_SELF_EMPLOYED_AND_HIGH_RISK_SCORE: 91

CaseStore:
  ListTopCases:
    CreatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    UpdatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    ConfidenceScoreBuckets: [-1,10,20,30,35,40,45,50,55,60,65,70,75,80,85,90,95,100]
    MaxCallsToVendor: 10

CasePriorityQueue:
  LiveQueueTags: ["ONBOARDING_RISK_SCREENER", "ONBOARDING_LIVENESS", "AFU_LIVENESS", "DS_AFU_RISK_MODEL", "LL_PL_ONBOARDING_RISK_SCREENER"]
  CachedCaseStaleThreshold: "1h"
  QueueCapacityMap:
    transaction_review_queue: 50
    live_queue: 50
    post_onboarding_queue: 100
    escalation_review_queue: 50
  QueueTTLMap:
    transaction_review_queue: "3m"
    live_queue: "3m"
    post_onboarding_queue: "3m"
    escalation_review_queue: "3m"
  modelSelectionRates:
    model1Rate: 60
    model2Rate: 30
    cgRate: 10
    UseModelRateForSelection: false

UpiVPANameMatchingCheckConfig:
  IsEnabled: true
  CompleteMismatchScore: 0.0
  FailureScore: 0.7

JunkEmailCheckConfig:
  IsEnabled: true
  JunkEmailCheckScoreMapping:
    JUNK_EMAIL_CHECK_NO_VOWELS: 50 # keeping a score of 50(which will translate to 0.5 screener score) so the cases can be reviewed manually

LocationModelCheck:
  IsEnabled: true
  RiskSeverityToScoreMap:
    RISK_SEVERITY_CRITICAL: 95 # keeping a score of 95(which will translate to 0.95 screener score) user will be stopped from onboarding

CreditReportAffluenceClassConfig:
  IsEnabled: true
  FailCreditScoreThreshold: 600
  InvalidCreditScoreThreshold: 10
  PassCreditScoreThreshold: 650
  FailureScore: 95 #keeping a score of x (which will translate to 0.x screener score)
  BadAffluenceIncomeThreshold: 20000
  SuitFiledWilfulDefault: 0
  SuitFiledWillfulDefaultWrittenOff: 0

InstalledAppsCheckConfig:
  IsEnabled: true
  MinRequiredSocialAppCount: 1
  MinRequiredNonSystemAppCount: 10
  SocialApps: ["com.whatsapp", "com.facebook.katana", "com.facebook.orca", "com.twitter.android", "com.linkedin.android", "org.telegram.messenger", "com.snapchat.android"]
  DurationToCheckForPreviousFailures: "4380h" # 6 months
  CheckToScoreMap:
    INSTALLED_APPS_CHECK_NON_SYSTEM_APPS: 71
    INSTALLED_APPS_CHECK_SOCIAL_APPS: 70
    INSTALLED_APPS_CHECK_MALICIOUS_APPS_ONLY: 95
    HIGH_LIFT_BAD_APPS_LOW_LEA_GOOD_APPS: 94
    InstalledAppsInfo_INSTALLED_APPS_CHECK_HIGH_LIFT_BAD_APPS_WITHOUT_LOW_LEA_GOOD_APPS: 94
    LOW_LIFT_BAD_APPS_HIGH_LEA_GOOD_APPS: 93
    InstalledAppsInfo_INSTALLED_APPS_CHECK_LOW_LIFT_BAD_APPS_WITHOUT_HIGH_LEA_GOOD_APPS: 93
    INSTALLED_APPS_CHECK_PREVIOUS_FAILURES: 96
    INSTALLED_APPS_CHECK_MALICIOUS_APPS_WITH_GOOD_APPS: 72
    INSTALLED_APPS_CHECK_HIGH_LIFT_BAD_APPS_WITH_LOW_LEA_GOOD_APPS: 0
    INSTALLED_APPS_CHECK_LOW_LIFT_BAD_APPS_WITH_HIGH_LEA_GOOD_APPS: 0

FIProductToSegmentMap:
  SA Tier - Salary: "127b7c75-93bc-4d13-a7c4-550266576492"
  SA Tier - Infinite: "851c5b55-8610-46de-8b98-71ed406949ec"

ScreenerConfig:
  CriteriaToRiskChecksConfigMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      RiskParamConfigMap:
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PL_FI_LITE_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INCOME_EMPLOYMENT_DISCREPANCY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_AFFLUENCE_CLASS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CREDIT_SCORE_AFFLUENCE_CLASS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_VPN_PRESENCE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_IP_ADDRESS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION_AND_RISKY_PROFILE:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INCOME_EMPLOYMENT_DISCREPANCY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_AFFLUENCE_CLASS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_REOOBE:
      RiskParamConfigMap:
        RISK_PARAM_AFU_RISK_DETECTION_MODEL:
          IsEnabled: true
          # There are failure responses in ds rpc hence intentionally keeping as false.
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_VPN_PRESENCE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CREDIT_SCORE_AFFLUENCE_CLASS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_IP_ADDRESS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION_AND_RISKY_PROFILE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_LOANS_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOW_CONTACT_COUNT:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_VPN_PRESENCE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_D2H_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
  CriteriaToRiskActionThresholdMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
      ThresholdToTipManualReviewToAutoBlock:
        IsEnabled: true
        Threshold: 2
        FailureScore: 0.92
        ScreenerChecksOfInterest: ["RISK_PARAM_INSTALLED_APPS", "RISK_PARAM_JUNK_EMAIL", "RISK_PARAM_CONTACT_ASSOCIATION"]
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_REOOBE:
      AutoBlockThreshold: 95
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_LOANS_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_D2H_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
  CriteriaToRuleConfigMap:
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "ONBOARDING_RISK_SCREENER_2"
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "ONBOARDING_RISK_SCREENER_2"
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      ExternalRuleIdForManualReview: "FEDERAL_CC_ONBOARDING_RISK_SCREENER_1"
    SCREENER_CRITERIA_REOOBE:
      ExternalRuleIdForManualReview: "DS_AFU_RISK_MODEL_3"
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      ExternalRuleIdForManualReview: "LL_PL_ONBOARDING_RISK_SCREENER_1"
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      ExternalRuleIdForManualReview: "RISK_SCREENER_STOCK_GUARDIAN"
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "ONBOARDING_NR_RISK_SCREENER"
  MaxScreenerAttemptRunTime: "2m"

LEAComplaint:
  ReviewExternalRuleId: "LEA_REVIEW_1"
  AuditDateCutOffForCaseCreation: "7200h"

DynamicElementsConfig:
  IsEnabled: true
  DurationToCheckPreviousLEAComplaints: "2160h" # 90 days
  FreezeBannerReleaseConf:
    FeatureName: "freeze_home_banner"
    RolloutPercentage: 70
  FreezeStatusToBannerConfigMap:
    FREEZE_STATUS_CREDIT_FREEZE:
      FontColor: "#FFFFFF"
      Title: "Credit Freeze enabled on your account by partner bank"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      IndicatorSelectedColor: "#D65779"
      IndicatorDefaultColor: "#FFFFFF"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
        Title: "Your account is temporarily restricted"
        Body: "For now, you can withdraw money from this a/c, but can't add money into it.\n\nTo unfreeze: Check for an <NAME_EMAIL>"
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
        RedirectCTA:
          Title: "Fill Details"
          FontColor: "#E6E9ED"
          BodyColor: "#00B899"
      PopupCTAWithFormsConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account is temporarily restricted"
        Body: "You can withdraw money from your savings account but can't add money into it. To unfreeze, please fill in a few details so our team can review the issue"
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#fce6a9"
        RedirectCTA:
          Title: "Fill Details"
          FontColor: "#E6E9ED"
          BodyColor: "#00B899"
        NotifyLaterCTA:
          Title: "Later"
          FontColor: "#00B899"
          BodyColor: "#E6E9ED"
    ACCOUNT_FREEZE_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: %s applied"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps."
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
    ACCOUNT_LIEN_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: ₹%s frozen"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps."
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
    ACCOUNT_FREEZE_AND_LIEN_BANNER:
      FontColor: "#FFFFFF"
      Title: "LEA Enquiry received: %s applied and ₹%s frozen"
      BodyColor: "#A73F4B"
      ImageURL: "https://epifi-icons.pointz.in/risk/account_freeze_banner_icon.png"
      PopupCTAConfig:
        CTAImageURL: "https://epifi-icons.pointz.in/chevron-right-violet.png"
        Title: "Your account's temporarily restricted"
        Body: "%s \n\nPlease check your email for details and next steps. "
        IconUrl: "https://epifi-icons.pointz.in/risk/freeze_alert_icon.svg"
        BackgroundColor: "#A73F4B"
        DismissCTAText: "Ok, got it"
  OutcallBannerReleaseConf:
    FeatureName: "outcall_home_banner"
    RolloutPercentage: 100
  OutcallBanner:
    FontColor: "#FFFFFF"
    Title: "Unusual activity detected: Fill in some details"
    BodyColor: "#F0BECE"
    ImageURL: "https://epifi-icons.pointz.in/risk/alert.png"
    IndicatorSelectedColor: "#D65779"
    IndicatorDefaultColor: "#FFFFFF"
    PopupCTAConfig:
      CTAImageURL: "https://epifi-icons.pointz.in/risk/alert.png"
      Title: "Unusual activity detected"
      Body: "Fill in a questionnaire with correct information before %s to avoid restrictions on your Federal Bank Savings a/c through Fi."
      IconUrl: "https://epifi-icons.pointz.in/risk/alert.png"
      BackgroundColor: "#E6E9ED"
      NotifyLaterCTA:
        Title: "Later"
        FontColor: "#00B899"
        BodyColor: "#E6E9ED"
      RedirectCTA:
        Title: "Fill Details"
        FontColor: "#E6E9ED"
        BodyColor: "#00B899"

ContactAssociationsCheckConfig:
  IsEnabled: true
  IngressMaxAssociationsWithLEAThreshold: 2
  FailedCheckScore: 0.91
  ManualReviewFailCheckScore: 0.70
  IngressManualReviewForLEAThreshold: 1
  IngressManualReviewForBlockedThreshold: 2
  EgressMaxAssociationsWithLEAThreshold: 2

IPAddressRedListCheckConfig:
  IsEnabled: true
  MinRedListScoreToSendForManualReview: 50
  ManualReviewScore: 0.50

ContactAssociationAndRiskyProfileCheckConfig:
  IsEnabled: true
  ManualReviewScore: 0.50

ValidateSavingsAccountStatusConfig:
  IsEnabled: true
  FailedCheckScore: 0.91

InvestigationEmail:
  FromEmail: "<EMAIL>"

FeatureFlags:
  EnablePGDBForTxnMonitoring: true

ProcessCallRoutingEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-call-routing-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

RiskBankAction:
  ActionWorkflowMaxRuntime: "240h"

DronapayRuleHitCallbackPublisher:
  QueueName: "prod-risk-dp-rule-hit-callback-queue"

RuleHitCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-dp-rule-hit-callback-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

ProfileBannerForSherlock:
  IsEnabled: true
  MaxBannersForType: 1

CrossVideoFacematchCheckConfig:
  IsEnabled: true
  LivenessScoreCutoff: 50
  FaceMatchScoreCutoff: 25
  FailureScore: 0.7 # manual review

SAUnreviewedAlertsCheckConfig:
  IsEnabled: true
  highPrecisionRuleHitDuration: 1440h
  LowPrecisionRuleHitDuration: 720h
  HighPrecisionRuleThreshold: 0.3

WebForm:
  BaseUrl: "https://fi.money/account-info-form"

VPNPresenceCheckConfig:
  IsEnabled: true

RiskCXTicketUpdateEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-cx-ticket-update-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1m
    Namespace: "risk"

RiskAccountOperationStatusUpdateQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "prod-risk-account-oper-status-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "risk"

WebFormConfig:
  BaseUrl: "https://fi.money/account-info-form"
  FormUrlTemplate: "%s?form-id=%s"
  IsFormsEnabled: true

UnifiedLEAComplaintConfig:
  IsWorkflowEnabled: true
  LayerNumberEqualAndAboveNotFraud: 5

RuleTags:
  - "RISK_SIGNAL"
  - "TRANSACTION_BLOCK_TYPE_FIFO"
  - "TRANSACTION_BLOCK_TYPE_UNSPECIFIED"

PrioritiserConfig:
  DSModelPrioritisationRolloutPercentage: 100
  ReprioritisationS3Bucket: "epifi-prod-risk-case-prioritisation"

ProcessMnrlReportSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-mnrl-report-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m
    Namespace: "risk"

ProcessMnrlSuspectedFlaggedMobileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-risk-mnrl-suspected-flagged-mobile-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m
    Namespace: "risk"


ProcessRiskAlertEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "prod-data-risk-bq-alert-ingestion-queue"
  # data-prod account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "risk"
