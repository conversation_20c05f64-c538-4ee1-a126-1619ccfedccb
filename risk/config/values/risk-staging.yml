Application:
  Environment: "staging"
  Name: "risk"

EpifiDb:
  AppName: "risk"
  DbType: "CRDB"
  StatementTimeout: 1s
  Username: "epifi_dev_user"
  Password: ""
  Name: "epifi"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.epifi_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.epifi_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FRMDb:
  AppName: "risk"
  DbType: "CRDB"
  StatementTimeout: 1s
  Username: "frm_dev_user"
  Password: ""
  Name: "frm"
  EnableDebug: true
  SSLMode: "verify-full"
  SSLRootCert: "staging/cockroach/ca.crt"
  SSLClientCert: "staging/cockroach/client.frm_dev_user.crt"
  SSLClientKey: "staging/cockroach/client.frm_dev_user.key"
  MaxOpenConn: 5
  MaxIdleConn: 2
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FRMPgdb:
  AppName: "risk"
  DbType: "PGDB"
  StatementTimeout: 5s
  Name: "frm_pgdb"
  EnableDebug: true
  SSLMode: "disable"
  MaxOpenConn: 20
  MaxIdleConn: 10
  MaxConnTtl: "30m"
  GormV2:
    LogLevelGormV2: "INFO"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

IsFRMPgdbEnabled: true

AWS:
  Region: "ap-south-1"

ProcessRedListUpdateSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-redlist-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 1
      TimeUnit: "Minute"

LeaActorsPublisher:
  QueueName: "staging-risk-sync-lea-actors-queue"

ProcessSyncLeaActorsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-sync-lea-actors-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"

ProcessRiskCasesIngestEventBEQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-cases-ingestion-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

ProcessRiskCasesIngestEventDataQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-data-risk-cases-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

ProcessRiskAlertIngestEventDSQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-ds-risk-alert-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

RiskAlertIngestionQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-alert-ingestion-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

RiskBatchRuleEngineEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-risk-batch-rule-engine-event-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"

AlertsPublisher:
  QueueName: "staging-risk-alert-ingestion-queue"

BatchRuleEngineS3:
  BucketName: "epifi-dp-dev"

ProcessDisputeUploadSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-dispute-upload-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "risk"

FormSubmissionEventPublisher:
  QueueName: "staging-risk-form-submission-event-queue"

ProcessFormSubmissionEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-form-submission-event-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 15min, 30min, 45min, 60min...
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 15
          MaxAttempts: 7
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "risk"

Profiling:
  StackDriverProfiling:
    ProjectId: "gamma-app-babc8"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Secrets:
  Ids:
    EncryptorData: "staging/risk/aes/txn-monitoring-aes-encryption-data"
    FRMPgdbUsernamePassword: "staging/rds/postgres/frm_pgdb_dev_user"

OrderUpdateTxnMonitoringSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-update-transaction-monitoring-queue"
  Disable: false
  RetryStrategy:
    Hybrid: # Exponential backoff followed by regular interval
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 10
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 3
          TimeUnit: "Minute"
      MaxAttempts: 6
      CutOff: 3
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 75
        Period: 1s
    Namespace: "risk-txn"

TxnMonitoringConfig:
  UpdateEntityDate: "2022-11-29T00:00:00.00Z"
  ThresholdForSyncWithoutCategory: "10m"

Tracing:
  Enable: true

CMConsumerConfig:
  CreateAlertsBatchSize: 1000
  CaseManagementRollOutPercentage:
    PAYLOAD_TYPE_USER_REVIEW: 100
  ProcessActiveRuleAlerts: true

Profile:
  FetchReviewsPeriod: "720h" # 30 Days

AffluenceCheckConfig:
  IsEnabled: true
  LowAffluenceClassDSScoreThreshold: 70
  LowAffluenceClassAndSelfEmployedDSScoreThreshold: 60
  AffluenceCheckScoreMapping:
    AFFLUENCE_CHECK_LOW_CLASS_AND_HIGH_RISK_SCORE: 92
    AFFLUENCE_CHECK_LOW_CLASS_AND_SELF_EMPLOYED_AND_HIGH_RISK_SCORE: 91

JunkEmailCheckConfig:
  IsEnabled: true
  JunkEmailCheckScoreMapping:
    JUNK_EMAIL_CHECK_NO_VOWELS: 50 # keeping a score of 50(which will translate to 0.5 screener score) so the cases can be reviewed manually

LocationModelCheck:
  IsEnabled: true
  RiskSeverityToScoreMap:
    RISK_SEVERITY_CRITICAL: 95 # keeping a score of 95(which will translate to 0.95 screener score) user will be stopped from onboarding

ScreenerConfig:
  CriteriaToRiskChecksConfigMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      RiskParamConfigMap:
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PL_FI_LITE_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INCOME_EMPLOYMENT_DISCREPANCY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_AFFLUENCE_CLASS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CREDIT_SCORE_AFFLUENCE_CLASS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_IP_ADDRESS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION_AND_RISKY_PROFILE:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INCOME_EMPLOYMENT_DISCREPANCY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_AFFLUENCE_CLASS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_REOOBE:
      RiskParamConfigMap:
        RISK_PARAM_AFU_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_SA_UNREVIEWED_ALERTS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_LOANS_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOW_CONTACT_COUNT:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_VPN_PRESENCE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_D2H_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
  CriteriaToRiskActionThresholdMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
      ThresholdToTipManualReviewToAutoBlock:
        IsEnabled: true
        Threshold: 2
        FailureScore: 0.92
        ScreenerChecksOfInterest: ["RISK_PARAM_KYC_ADDRESS_PIN_CODE", "RISK_PARAM_GEO_LOCATION_PIN_CODE"]
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_REOOBE:
      AutoBlockThreshold: 99
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_LOANS_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_D2H_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
  CriteriaToRuleConfigMap:
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "Onboarding Risk screener 1"
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "Onboarding Risk screener 1"
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      ExternalRuleIdForManualReview: "LOANS_PL_FI_LITE_LIQUILOANS_SCREENER_1"
    SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING:
      ExternalRuleIdForManualReview: "Onboarding Risk screener 1"
    SCREENER_CRITERIA_REOOBE:
      ExternalRuleIdForManualReview: "DS_AFU_RISK_MODEL_2"
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      ExternalRuleIdForManualReview: "RISK_SCREENER_STOCK_GUARDIAN"
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "ONBOARDING_NR_RISK_SCREENER"
  MaxScreenerAttemptRunTime: "2m"

FeatureFlags:
  EnablePGDBForTxnMonitoring: true

ProcessCallRoutingEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-call-routing-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

DronapayRuleHitCallbackPublisher:
  QueueName: "staging-risk-dp-rule-hit-callback-queue"

RuleHitCallbackSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-dp-rule-hit-callback-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

RuleTags:
  - "RISK_SIGNAL"
  - "TRANSACTION_BLOCK_TYPE_FIFO"
  - "TRANSACTION_BLOCK_TYPE_UNSPECIFIED"

SAUnreviewedAlertsCheckConfig:
  IsEnabled: true
  highPrecisionRuleHitDuration: 24h
  LowPrecisionRuleHitDuration: 12h
  HighPrecisionRuleThreshold: 0.3

Form:
  BaseUrl: "https://web.staging.pointz.in/account-info-form"

RiskCXTicketUpdateEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-cx-ticket-update-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

RiskAccountOperationStatusUpdateQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "staging-risk-account-oper-status-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "risk"

WebFormConfig:
  BaseUrl: "https://web.staging.pointz.in/account-info-form"
  FormUrlTemplate: "%s?form-id=%s"
  IsFormsEnabled: true

ProcessRiskSignalEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-data-risk-bq-signal-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 30
        Period: 1m
    Namespace: "risk"

CaseStore:
  ListTopCases:
    CreatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    UpdatedAtBuckets: ["720h","360h","168h","92h","80h","68h","58h","50h","44h","38h","34h","30h","28h","26h","24h","22h","20h","18h","16h","14h","12h","10h","8h","6h","4h","2h","0"]
    ConfidenceScoreBuckets: [-1,30,60,80,90,95,100]
    MaxCallsToVendor: 10

ProcessMnrlReportSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-mnrl-report-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "risk"

ProcessMnrlSuspectedFlaggedMobileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "staging-risk-mnrl-suspected-flagged-mobile-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "risk"

ProcessRiskAlertEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-data-risk-bq-alert-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "risk"
