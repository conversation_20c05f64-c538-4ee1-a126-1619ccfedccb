Application:
  Environment: "test"
  Name: "risk"

EpifiDb:
  AppName: "risk"
  DbType: "CRDB"
  StatementTimeout: 5m
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "epifi_test"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FRMDb:
  Host: "localhost"
  Port: 26257
  Username: "root"
  Password: ""
  Name: "frm_test"
  DbType: "CRDB"
  EnableDebug: false
  SSLMode: "disable"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

FRMPgdb:
  AppName: "risk"
  DbType: "PGDB"
  StatementTimeout: 5m
  Name: "frm_pgdb_test"
  EnableDebug: true
  SSLMode: "disable"
  DBResolverList:
    - Alias: "frm_pgdb"
      DbDsn:
        DbType: "PGDB"
        Host: "localhost"
        Port: 5432
        Username: "root"
        Name: "frm_pgdb_test"
        SSLMode: "disable"
        SecretName: "{\"username\": \"root\", \"password\": \"\"}"
  GormV2:
    LogLevelGormV2: "ERROR"
    SlowQueryLogThreshold: 200ms
    UseInsecureLog: true

IsFRMPgdbEnabled: true

AWS:
  Region: "ap-south-1"

ProcessRedListUpdateSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "risk-redlist-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 1
      TimeUnit: "Minute"

LeaActorsPublisher:
  QueueName: "risk-sync-lea-actors-queue"

ProcessSyncLeaActorsSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "risk-sync-lea-actors-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"

ProcessRiskCasesIngestEventBEQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-cases-ingestion-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

ProcessRiskCasesIngestEventDataQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-data-risk-cases-ingestion-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

ProcessRiskAlertIngestEventDSQueueSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-ds-risk-alert-ingestion-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

ProcessDisputeUploadSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-dispute-upload-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1m
    Namespace: "risk"

FormSubmissionEventPublisher:
  QueueName: "risk-form-submission-event-queue"

ProcessFormSubmissionEventSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-form-submission-event-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 15min, 30min, 45min, 60min...
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 15
          MaxAttempts: 7
          TimeUnit: "Minute"
      MaxAttempts: 10
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 50
        Period: 1m
    Namespace: "risk"

RiskAlertIngestionQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-alert-ingestion-queue"
  RetryStrategy:
    Hybrid: # Exponential backoff 30s, 60s, 90s, regular interval is followed on 30min, 60min, 90min, 120min.
      RetryStrategy1:
        ExponentialBackOff:
          BaseInterval: 30
          MaxAttempts: 3
          TimeUnit: "Second"
      RetryStrategy2:
        RegularInterval:
          Interval: 30
          MaxAttempts: 4
          TimeUnit: "Minute"
      MaxAttempts: 7
      CutOff: 3
    Namespace: "risk"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 2
        Period: 1m
    Namespace: "risk"

RiskBatchRuleEngineEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-risk-batch-rule-engine-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 2
      TimeUnit: "Minute"

AlertsPublisher:
  QueueName: "risk-alert-ingestion-queue"

BatchRuleEngineS3:
  BucketName: "epifi-dp-dev"

Profiling:
  StackDriverProfiling:
    ProjectId: "test"
    EnableStackDriver: false
  PyroscopeProfiling:
    EnablePyroscope: false
  AutomaticProfiling:
    EnableAutoProfiling: false
    BucketName: "epifi-deploy-automatic-profiling"
    CPUPercentageLimit: 60
    MemoryPercentageLimit: 75
    ProfileInterval: 10s
    XProfilesInLast1Hour: 3
    RefreshFrequency: 30s
  EnableCPU: true
  EnableHeap: true
  EnableGoroutine: true
  EnableMutex: true
  EnableAlloc: true

Secrets:
  Ids:
    EncryptorData: "{\"EncryptorKey\":\"6368616e676520746869732070617474\",\"EncryptorIv\":\"1234567887654321\"}"
    FRMPgdbUsernamePassword: "{\"username\": \"root\", \"password\": \"\"}"

OrderUpdateTxnMonitoringSubscriber:
  StartOnServerStart: false
  NumWorkers: 2
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "risk-update-transaction-monitoring-queue"
  Disable: false
  RetryStrategy:
    RegularInterval:
      Interval: 10
      MaxAttempts: 4
      TimeUnit: "Second"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 100
        Period: 1s
    Namespace: "risk-txn"

TxnMonitoringConfig:
  UpdateEntityDate: "2022-11-29T00:00:00.00Z"
  ThresholdForSyncWithoutCategory: "10m"
HunterRollOutPercentage: 100

CMConsumerConfig:
  CreateAlertsBatchSize: 3
  CaseManagementRollOutPercentage:
    PAYLOAD_TYPE_USER_REVIEW: 100
  ProcessActiveRuleAlerts: true

Profile:
  FetchReviewsPeriod: "720h" # 30 Days

AffluenceCheckConfig:
  IsEnabled: true
  LowAffluenceClassDSScoreThreshold: 70
  LowAffluenceClassAndSelfEmployedDSScoreThreshold: 60
  AffluenceCheckScoreMapping:
    AFFLUENCE_CHECK_LOW_CLASS_AND_HIGH_RISK_SCORE: 82
    AFFLUENCE_CHECK_LOW_CLASS_AND_SELF_EMPLOYED_AND_HIGH_RISK_SCORE: 81

JunkEmailCheckConfig:
  IsEnabled: true
  JunkEmailCheckScoreMapping:
    JUNK_EMAIL_CHECK_NO_VOWELS: 50 # keeping a score of 50(which will translate to 0.5 screener score) so the cases can be reviewed manually

ScreenerConfig:
  CriteriaToRiskChecksConfigMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
          StoredResultTTL: "10m" #10 minutes
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
          StoredResultTTL: -1
        RISK_PARAM_AFFLUENCE_CLASS:
          IsEnabled: false
          IsMandatory: false
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: true
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_REOOBE:
      RiskParamConfigMap:
        RISK_PARAM_AFU_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: false
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_UPI_VPA_NAME_MATCH:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_VPN_PRESENCE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_D2H_ONBOARDING:
      RiskParamConfigMap:
        RISK_PARAM_ONBOARDING_VELOCITY:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_EMAIL_ID:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      RiskParamConfigMap:
        RISK_PARAM_KYC_ADDRESS_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_PIN_CODE:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_GEO_LOCATION_LAT_LONG:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_INSTALLED_APPS:
          IsEnabled: true
          IsMandatory: true
        RISK_PARAM_FINITE_CODE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_RISKY_DEVICE:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_PHONE_NUMBER:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_JUNK_EMAIL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_LOCATION_RISK_MODEL:
          IsEnabled: true
          IsMandatory: false
        RISK_PARAM_CONTACT_ASSOCIATION:
          IsEnabled: true
          IsMandatory: false
  CriteriaToRiskActionThresholdMap:
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 20
      ThresholdToTipManualReviewToAutoBlock:
        IsEnabled: true
        Threshold: 2
        FailureScore: 0.92
        ScreenerChecksOfInterest: ["RISK_PARAM_KYC_ADDRESS_PIN_CODE", "RISK_PARAM_GEO_LOCATION_PIN_CODE"]
    SCREENER_CRITERIA_REOOBE:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS:
      AutoBlockThreshold: 0
      ManualReviewThreshold: -10
    SCREENER_CRITERIA_D2H_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 90
    SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      AutoBlockThreshold: 90
      ManualReviewThreshold: 10
  RiskCheckStoredResultDefaultTTLMap:
    RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL: "20m" #20 minutes
    RISK_PARAM_GEO_LOCATION_LAT_LONG: "60m" #60 minutes
    RISK_PARAM_AFFLUENCE_CLASS: "60m" #60 minutes
  CriteriaToRuleConfigMap:
    SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING:
      ExternalRuleIdForManualReview: "ONBOARDING_RISK_SCREENER_2"
    SCREENER_CRITERIA_FI_LITE_ONBOARDING:
      ExternalRuleIdForAutoBlock: "ext-rule-id-1"
      ExternalRuleIdForManualReview: "ext-rule-id-2"
    SCREENER_CRITERIA_REOOBE:
      ExternalRuleIdForManualReview: "DS_AFU_RISK_MODEL_2"
    SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS:
      ExternalRuleIdForManualReview: "RISK_SCREENER_STOCK_GUARDIAN"
  ScreenerAttemptMaxRunTime: "2m"

LocationModelCheck:
  IsEnabled: true
  RiskSeverityToScoreMap:
    RISK_SEVERITY_CRITICAL: 95 # keeping a score of 95(which will translate to 0.95 screener score) user will be stopped from onboarding

DynamicElementsConfig:
  IsEnabled: true
  DurationToCheckPreviousLEAComplaints: "2160h" # 90 days
  FreezeBannerReleaseConf:
    FeatureName: "freeze_home_banner"
    RolloutPercentage: 100
  FreezeStatusToBannerConfigMap:
    FREEZE_STATUS_CREDIT_FREEZE:
      FontColor: "#FFFFFF"
      Title: "credit-freeze-title"
      BodyColor: "#AC7C44"
      ImageURL: "credit-freeze-image-URL"
      PopupCTAConfig:
        CTAImageURL: "credit-test-url"
        Title: "credit pop up title"
        Body: "test body content \n credit"
        IconUrl: "icon.url"
        BackgroundColor: "#FFFFFF"
        DismissCTAText: "Ok, Got it"
    FREEZE_STATUS_TOTAL_FREEZE:
      FontColor: "#FFFFFF"
      Title: "total-freeze-title"
      BodyColor: "#AC7C44"
      ImageURL: "total-freeze-image-URL"
      PopupCTAConfig:
        CTAImageURL: "total-test-url"
        Title: "total pop up title"
        Body: "test body content \n total"
        IconUrl: "icon.url"
        BackgroundColor: "#FFFFFF"
        DismissCTAText: "Ok, Got it"
  OutcallBannerReleaseConf:
    FeatureName: "outcall_home_banner"
    RolloutPercentage: 100
  OutcallBanner:
    FontColor: "#FFFFFF"
    Title: "Unusual activity detected"
    BodyColor: "#AC7C44"
    ImageURL: "https://epifi-icons.pointz.in/risk/exclamation.png"
    PopupCTAConfig:
      CTAImageURL: "outcall-test-url"
      Title: "outcall pop up title"
      Body: "Fill in a questionnaire with correct information before %s to avoid restrictions on your Federal Bank Savings a/c through Fi."
      IconUrl: "outcall banner icon url"
      BackgroundColor: "#FFFFFF"
      NotifyLaterCTA:
        Title: "Later"
      RedirectCTA:
        Title: "Fill Details"

FIProductToSegmentMap:
  Credit Cards: "segmentId"

FeatureFlags:
  EnablePGDBForTxnMonitoring: false

Escalation:
  QuestionsConf:
    REVIEW_TYPE_USER_REVIEW:
      CommonQuestions:
        - QuestionText: "test question 1"
          FieldType: 1
      EmploymentSpecificQuestions:
        EMPLOYMENT_TYPE_SALARIED:
          - QuestionText: "salaried question 1"
            FieldType: 1
      DefaultEmploymentQuestions:
        - QuestionText: "default employment question 1"
          FieldType: 1
    REVIEW_TYPE_TRANSACTION_REVIEW:
      CommonQuestions:
        - QuestionText: "test question 1"
          FieldType: 1
      EmploymentSpecificQuestions:
        EMPLOYMENT_TYPE_SALARIED:
          - QuestionText: "salary range"
            FieldType: 2
            FieldOptions:
              DropdownFieldOptions:
                Choices:
                  - "1L-5L"
                  - "5L-20L"
      DefaultEmploymentQuestions:
        - QuestionText: "default employment question 1"
          FieldType: 1

ProcessCallRoutingEventSqsSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-call-routing-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

DronapayRuleHitCallbackPublisher:
  QueueName: "risk-dp-rule-hit-callback-queue"

RuleHitCallbackSubscriber:
  StartOnServerStart: false
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "risk-dp-rule-hit-callback-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

RuleTags:
  - "dummyTag1"
  - "dummyTag2"

SAUnreviewedAlertsCheckConfig:
  IsEnabled: true
  highPrecisionRuleHitDuration: 1440h
  LowPrecisionRuleHitDuration: 720h
  HighPrecisionRuleThreshold: 0.3

WebForm:
  BaseUrl: "testurl"

RiskCXTicketUpdateEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-cx-ticket-update-event-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1s
    Namespace: "risk"

RiskAccountOperationStatusUpdateQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "risk-account-oper-status-update-queue"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 5
        Period: 1s
    Namespace: "risk"

WebFormConfig:
  BaseUrl: "https://test"
  FormUrlTemplate: "%s?form-id=%s"
  IsFormsEnabled: true

ProcessRiskSignalEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 5
  QueueName: "dev-data-risk-bq-signal-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    RegularInterval:
      Interval: 5
      MaxAttempts: 3
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 3
        Period: 1m
    Namespace: "risk"

ProcessMnrlReportSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "test-risk-mnrl-report-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m
    Namespace: "risk"

ProcessMnrlSuspectedFlaggedMobileSqsSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "test-risk-mnrl-suspected-flagged-mobile-queue"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 1
        Period: 1m
    Namespace: "risk"


ProcessRiskAlertEventQueueSubscriber:
  StartOnServerStart: true
  NumWorkers: 1
  PollingDuration: 20
  MaxMessages: 10
  QueueName: "dev-data-risk-bq-alert-ingestion-queue"
  # data-dev account owns this queue
  QueueOwnerAccountId: "************"
  RetryStrategy:
    ExponentialBackOff:
      BaseInterval: 1
      MaxAttempts: 8
      TimeUnit: "Minute"
  RateLimitConfig:
    ResourceMap:
      subscriber:
        Rate: 10
        Period: 1m
    Namespace: "risk"
