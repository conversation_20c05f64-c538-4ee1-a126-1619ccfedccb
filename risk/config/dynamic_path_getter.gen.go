// Code generated by tools/conf_gen/dynamic_conf_gen.go
package config

import (
	"fmt"
	"strings"
)

func (obj *Config) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "hunterrolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HunterRollOutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HunterRollOutPercentage, nil
	case "testflag":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TestFlag\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TestFlag, nil
	case "ruletags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.<PERSON><PERSON><PERSON>("invalid path %q for primitive field \"RuleTags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RuleTags, nil
	case "featureflags":
		return obj.FeatureFlags.Get(dynamicFieldPath[1:])
	case "processredlistupdatesqssubscriber":
		return obj.ProcessRedListUpdateSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processsyncleaactorssqssubscriber":
		return obj.ProcessSyncLeaActorsSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processriskcasesingesteventbequeuesubscriber":
		return obj.ProcessRiskCasesIngestEventBEQueueSubscriber.Get(dynamicFieldPath[1:])
	case "processriskcasesingesteventdataqueuesubscriber":
		return obj.ProcessRiskCasesIngestEventDataQueueSubscriber.Get(dynamicFieldPath[1:])
	case "processriskalertingesteventdsqueuesubscriber":
		return obj.ProcessRiskAlertIngestEventDSQueueSubscriber.Get(dynamicFieldPath[1:])
	case "processcallroutingeventsqssubscriber":
		return obj.ProcessCallRoutingEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processdisputeuploadsqssubscriber":
		return obj.ProcessDisputeUploadSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processformsubmissioneventsqssubscriber":
		return obj.ProcessFormSubmissionEventSqsSubscriber.Get(dynamicFieldPath[1:])
	case "onboardingvelocityconfig":
		return obj.OnboardingVelocityConfig.Get(dynamicFieldPath[1:])
	case "riskalertingestionqueuesubscriber":
		return obj.RiskAlertIngestionQueueSubscriber.Get(dynamicFieldPath[1:])
	case "riskbatchruleengineeventqueuesubscriber":
		return obj.RiskBatchRuleEngineEventQueueSubscriber.Get(dynamicFieldPath[1:])
	case "riskcxticketupdateeventqueuesubscriber":
		return obj.RiskCXTicketUpdateEventQueueSubscriber.Get(dynamicFieldPath[1:])
	case "riskaccountoperationstatusupdatequeuesubscriber":
		return obj.RiskAccountOperationStatusUpdateQueueSubscriber.Get(dynamicFieldPath[1:])
	case "processrisksignaleventqueuesubscriber":
		return obj.ProcessRiskSignalEventQueueSubscriber.Get(dynamicFieldPath[1:])
	case "processmnrlreportsqssubscriber":
		return obj.ProcessMnrlReportSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processmnrlsuspectedflaggedmobilesqssubscriber":
		return obj.ProcessMnrlSuspectedFlaggedMobileSqsSubscriber.Get(dynamicFieldPath[1:])
	case "processriskalerteventqueuesubscriber":
		return obj.ProcessRiskAlertEventQueueSubscriber.Get(dynamicFieldPath[1:])
	case "orderupdatetxnmonitoringsubscriber":
		return obj.OrderUpdateTxnMonitoringSubscriber.Get(dynamicFieldPath[1:])
	case "cmconsumerconfig":
		return obj.CMConsumerConfig.Get(dynamicFieldPath[1:])
	case "profile":
		return obj.Profile.Get(dynamicFieldPath[1:])
	case "redlist":
		return obj.RedList.Get(dynamicFieldPath[1:])
	case "casepriorityqueue":
		return obj.CasePriorityQueue.Get(dynamicFieldPath[1:])
	case "upivpanamematchingcheckconfig":
		return obj.UpiVPANameMatchingCheckConfig.Get(dynamicFieldPath[1:])
	case "affluencecheckconfig":
		return obj.AffluenceCheckConfig.Get(dynamicFieldPath[1:])
	case "screenerconfig":
		return obj.ScreenerConfig.Get(dynamicFieldPath[1:])
	case "junkemailcheckconfig":
		return obj.JunkEmailCheckConfig.Get(dynamicFieldPath[1:])
	case "locationmodelcheck":
		return obj.LocationModelCheck.Get(dynamicFieldPath[1:])
	case "dynamicelementsconfig":
		return obj.DynamicElementsConfig.Get(dynamicFieldPath[1:])
	case "installedappscheckconfig":
		return obj.InstalledAppsCheckConfig.Get(dynamicFieldPath[1:])
	case "creditreportaffluenceclassconfig":
		return obj.CreditReportAffluenceClassConfig.Get(dynamicFieldPath[1:])
	case "crossvideofacematchcheckconfig":
		return obj.CrossVideoFacematchCheckConfig.Get(dynamicFieldPath[1:])
	case "saunreviewedalertscheckconfig":
		return obj.SAUnreviewedAlertsCheckConfig.Get(dynamicFieldPath[1:])
	case "contactassociationscheckconfig":
		return obj.ContactAssociationsCheckConfig.Get(dynamicFieldPath[1:])
	case "rulehitcallbacksubscriber":
		return obj.RuleHitCallbackSubscriber.Get(dynamicFieldPath[1:])
	case "profilebannerforsherlock":
		return obj.ProfileBannerForSherlock.Get(dynamicFieldPath[1:])
	case "validatesavingsaccountstatusconfig":
		return obj.ValidateSavingsAccountStatusConfig.Get(dynamicFieldPath[1:])
	case "lowcontactcountcheckconfig":
		return obj.LowContactCountCheckConfig.Get(dynamicFieldPath[1:])
	case "vpnpresencecheckconfig":
		return obj.VPNPresenceCheckConfig.Get(dynamicFieldPath[1:])
	case "percentagerolloutreleaseconfig":
		return obj.PercentageRolloutReleaseConfig.Get(dynamicFieldPath[1:])
	case "ipaddressredlistcheckconfig":
		return obj.IPAddressRedListCheckConfig.Get(dynamicFieldPath[1:])
	case "contactassociationandriskyprofilecheckconfig":
		return obj.ContactAssociationAndRiskyProfileCheckConfig.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Config", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *FeatureFlags) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "enablepgdbfortxnmonitoring":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EnablePGDBForTxnMonitoring\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EnablePGDBForTxnMonitoring, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for FeatureFlags", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *OnboardingVelocityConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "threshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Threshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Threshold, nil
	case "d2hthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"D2HThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.D2HThreshold, nil
	case "bucketprecision":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketPrecision\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketPrecision, nil
	case "bucketexpiry":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"BucketExpiry\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.BucketExpiry, nil
	case "queryrangeduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"QueryRangeDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.QueryRangeDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for OnboardingVelocityConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CMConsumerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "processactiverulealerts":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ProcessActiveRuleAlerts\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ProcessActiveRuleAlerts, nil
	case "casemanagementrolloutpercentage":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CaseManagementRollOutPercentage, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"CaseManagementRollOutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.CaseManagementRollOutPercentage[dynamicFieldPath[1]], nil

		}
		return obj.CaseManagementRollOutPercentage, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CMConsumerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *Profile) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "fetchreviewsperiod":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FetchReviewsPeriod\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FetchReviewsPeriod, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for Profile", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RedListConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "onboardingstatethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"OnboardingStateThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.OnboardingStateThreshold, nil
	case "atmhighriskthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AtmHighRiskThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AtmHighRiskThreshold, nil
	case "atmmediumriskthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AtmMediumRiskThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AtmMediumRiskThreshold, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RedListConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CasePriorityQueuesConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "cachedcasestalethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CachedCaseStaleThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CachedCaseStaleThreshold, nil
	case "queuecapacitymap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.QueueCapacityMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"QueueCapacityMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.QueueCapacityMap[dynamicFieldPath[1]], nil

		}
		return obj.QueueCapacityMap, nil
	case "queuettlmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.QueueTTLMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"QueueTTLMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.QueueTTLMap[dynamicFieldPath[1]], nil

		}
		return obj.QueueTTLMap, nil
	case "livequeuetags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LiveQueueTags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LiveQueueTags, nil
	case "transactionqueuetags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"TransactionQueueTags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.TransactionQueueTags, nil
	case "postonboardingqueuetags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"PostOnboardingQueueTags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.PostOnboardingQueueTags, nil
	case "escalationqueuetags":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"EscalationQueueTags\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.EscalationQueueTags, nil
	case "modelselectionrates":
		return obj.ModelSelectionRates.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CasePriorityQueuesConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ModelSelectionRates) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "model1rate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Model1Rate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Model1Rate, nil
	case "model2rate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Model2Rate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Model2Rate, nil
	case "cgrate":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"CgRate\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.CgRate, nil
	case "usemodelrateforselection":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"UseModelRateForSelection\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.UseModelRateForSelection, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ModelSelectionRates", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *UpiVPANameMatchCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for UpiVPANameMatchCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *AffluenceCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "lowaffluenceclassdsscorethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowAffluenceClassDSScoreThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LowAffluenceClassDSScoreThreshold, nil
	case "lowaffluenceclassandselfemployeddsscorethreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowAffluenceClassAndSelfEmployedDSScoreThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LowAffluenceClassAndSelfEmployedDSScoreThreshold, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "affluencecheckscoremapping":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.AffluenceCheckScoreMapping, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"AffluenceCheckScoreMapping\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.AffluenceCheckScoreMapping[dynamicFieldPath[1]], nil

		}
		return obj.AffluenceCheckScoreMapping, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for AffluenceCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ScreenerConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "screenerattemptmaxruntime":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ScreenerAttemptMaxRunTime\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ScreenerAttemptMaxRunTime, nil
	case "criteriatoriskchecksconfigmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CriteriaToRiskChecksConfigMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.CriteriaToRiskChecksConfigMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.CriteriaToRiskChecksConfigMap, nil
	case "criteriatoriskactionthresholdmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CriteriaToRiskActionThresholdMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.CriteriaToRiskActionThresholdMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.CriteriaToRiskActionThresholdMap, nil
	case "riskcheckstoredresultdefaultttlmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RiskCheckStoredResultDefaultTTLMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"RiskCheckStoredResultDefaultTTLMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.RiskCheckStoredResultDefaultTTLMap[dynamicFieldPath[1]], nil

		}
		return obj.RiskCheckStoredResultDefaultTTLMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ScreenerConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskChecksConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "riskparamconfigmap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RiskParamConfigMap, nil
		case len(dynamicFieldPath) > 1:

			return obj.RiskParamConfigMap[dynamicFieldPath[1]].Get(dynamicFieldPath[2:])

		}
		return obj.RiskParamConfigMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskChecksConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *RiskParamConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "ismandatory":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsMandatory\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsMandatory, nil
	case "storedresultttl":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"StoredResultTTL\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.StoredResultTTL, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for RiskParamConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ScreenerActionThresholds) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "autoblockthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"AutoBlockThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.AutoBlockThreshold, nil
	case "manualreviewthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"ManualReviewThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.ManualReviewThreshold, nil
	case "thresholdtotipmanualreviewtoautoblock":
		return obj.ThresholdToTipManualReviewToAutoBlock.Get(dynamicFieldPath[1:])
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ScreenerActionThresholds", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ThresholdToTipManualReviewToAutoBlock) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "threshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"Threshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.Threshold, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ThresholdToTipManualReviewToAutoBlock", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *JunkEmailChecksConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "junkemailcheckscoremapping":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.JunkEmailCheckScoreMapping, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"JunkEmailCheckScoreMapping\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.JunkEmailCheckScoreMapping[dynamicFieldPath[1]], nil

		}
		return obj.JunkEmailCheckScoreMapping, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for JunkEmailChecksConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LocationModelCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "riskseveritytoscoremap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.RiskSeverityToScoreMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"RiskSeverityToScoreMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.RiskSeverityToScoreMap[dynamicFieldPath[1]], nil

		}
		return obj.RiskSeverityToScoreMap, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LocationModelCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *DynamicElementsConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for DynamicElementsConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *InstalledAppsCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "minrequiredsocialappcount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinRequiredSocialAppCount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinRequiredSocialAppCount, nil
	case "minrequirednonsystemappcount":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"MinRequiredNonSystemAppCount\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.MinRequiredNonSystemAppCount, nil
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "checktoscoremap":
		switch {
		case len(dynamicFieldPath) == 1:
			return obj.CheckToScoreMap, nil
		case len(dynamicFieldPath) > 1:

			if len(dynamicFieldPath) > 2 {
				return nil, fmt.Errorf("invalid path %q for non-dynamic map value field \"CheckToScoreMap\"", strings.Join(dynamicFieldPath[1:], "."))
			}
			return obj.CheckToScoreMap[dynamicFieldPath[1]], nil

		}
		return obj.CheckToScoreMap, nil
	case "socialapps":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"SocialApps\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.SocialApps, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for InstalledAppsCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CreditReportAffluenceClassConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CreditReportAffluenceClassConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *CrossVideoFacematchCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for CrossVideoFacematchCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *SAUnreviewedAlertsCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	case "highprecisionrulehitduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"HighPrecisionRuleHitDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.HighPrecisionRuleHitDuration, nil
	case "lowprecisionrulehitduration":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowPrecisionRuleHitDuration\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LowPrecisionRuleHitDuration, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for SAUnreviewedAlertsCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ContactAssociationsCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ContactAssociationsCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ProfileBannerForSherlock) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ProfileBannerForSherlock", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ValidateSavingsAccountStatusConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ValidateSavingsAccountStatusConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *LowContactCountCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "lowcontactcountmanualreviewthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowContactCountManualReviewThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LowContactCountManualReviewThreshold, nil
	case "lowcontactcountflagthreshold":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"LowContactCountFlagThreshold\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.LowContactCountFlagThreshold, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for LowContactCountCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *VPNPresenceCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for VPNPresenceCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *PercentageRolloutReleaseConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "rolloutpercentage":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"RolloutPercentage\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.RolloutPercentage, nil
	case "featurename":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"FeatureName\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.FeatureName, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for PercentageRolloutReleaseConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *IPAddressRedListCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for IPAddressRedListCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
func (obj *ContactAssociationAndRiskyProfileCheckConfig) Get(dynamicFieldPath []string) (val interface{}, err error) {
	if len(dynamicFieldPath) == 0 {
		return obj, nil
	}
	switch strings.ToLower(dynamicFieldPath[0]) {
	case "isenabled":
		if len(dynamicFieldPath) > 1 {
			return nil, fmt.Errorf("invalid path %q for primitive field \"IsEnabled\"", strings.Join(dynamicFieldPath[1:], "."))
		}
		return obj.IsEnabled, nil
	default:
		return nil, fmt.Errorf("invalid dynamic field path %q for ContactAssociationAndRiskyProfileCheckConfig", strings.Join(dynamicFieldPath, "."))
	}
}
