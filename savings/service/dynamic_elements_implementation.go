//nolint:dupl,funlen
package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"

	operationalStatusEnums "github.com/epifi/gamma/api/accounts/enums"
	"github.com/epifi/gamma/api/accounts/operstatus"
	savingsPb "github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/savings/extacct"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/pkg/savings"

	homeFePb "github.com/epifi/gamma/api/frontend/home"

	onboardingPb "github.com/epifi/gamma/api/user/onboarding"

	"go.uber.org/zap"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"

	dePb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	types "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	onbPkg "github.com/epifi/gamma/pkg/onboarding"
)

func (s *SavingsService) FetchDynamicElements(ctx context.Context, request *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	switch {
	case request.GetClientContext().GetHomeInfo().GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY:
		return s.getPrimaryPromoWidget(ctx, request)
	case request.GetClientContext().GetHomeInfo().GetSection() == dePb.HomeScreenAdditionalInfo_SECTION_TOP_BAR:
		return s.getTopBarComponent(ctx, request)
	default:
	}

	trackBalanceBanner := &dePb.DynamicElement{
		OwnerService:  types.ServiceName_SAVINGS_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING, // TODO: None of the elements are fitting in here
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
		Content: &dePb.ElementContent{Content: &dePb.ElementContent_BannerV2{
			BannerV2: &dePb.BannerElementContentV2{
				Title: &commontypes.Text{DisplayValue: &commontypes.Text_PlainString{PlainString: "Track your balance\nacross the month"},
					FontStyle: &commontypes.Text_StandardFontStyle{StandardFontStyle: commontypes.FontStyle_SUBTITLE_S},
					FontColor: "#FFFFFF"},
				BackgroundColor: &ui.BackgroundColour{Colour: &ui.BackgroundColour_RadialGradient{RadialGradient: &ui.RadialGradient{
					Colours: []string{"#8EE4AE", "#479566"},
				}}},
				Deeplink: &deeplinkPb.Deeplink{Screen: deeplinkPb.Screen_BALANCE_HISTORY_SCREEN},
			},
		}},
	}
	elementsList := []*dePb.DynamicElement{trackBalanceBanner}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elementsList,
	}, nil

}

func (s *SavingsService) DynamicElementCallback(_ context.Context, _ *dePb.DynamicElementCallbackRequest) (*dePb.DynamicElementCallbackResponse, error) {
	return &dePb.DynamicElementCallbackResponse{Status: rpc.StatusOk()}, nil
}

func (s *SavingsService) getTopBarComponent(ctx context.Context, request *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	var elementsList []*dePb.DynamicElement

	closedAccBalTransferTopBar, getClosedAccBalTransferTopBarErr := s.getClosedAccountBalanceTransferTopBar(ctx, request)
	if getClosedAccBalTransferTopBarErr != nil {
		logger.Error(ctx, "error while getting closed account balance transfer top bar", zap.Error(getClosedAccBalTransferTopBarErr))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while getting closed account balance transfer top bar"),
		}, nil
	}
	if closedAccBalTransferTopBar != nil {
		elementsList = append(elementsList, closedAccBalTransferTopBar)
	}

	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elementsList,
	}, nil
}

func (s *SavingsService) getClosedAccountBalanceTransferTopBar(ctx context.Context, request *dePb.FetchDynamicElementsRequest) (*dePb.DynamicElement, error) {
	actorId := request.GetActorId()

	savingsAccounts, getAccErr := s.dao.GetAccountsByActorId(ctx, actorId)
	if getAccErr != nil {
		if errors.Is(getAccErr, epifierrors.ErrRecordNotFound) {
			return nil, nil
		}

		return nil, fmt.Errorf("error while getting savings account for actorId %s: %w", actorId, getAccErr)
	}

	var closedAccount *savingsPb.Account
	for _, account := range savingsAccounts {
		operStatusResp, operStatusErr := s.operStatusClient.GetOperationalStatus(ctx, &operstatus.GetOperationalStatusRequest{
			DataFreshness: operstatus.GetOperationalStatusRequest_DATA_FRESHNESS_LAST_KNOWN,
			AccountIdentifier: &operstatus.GetOperationalStatusRequest_SavingsAccountId{
				SavingsAccountId: account.GetId(),
			},
		})
		if rpcErr := epifigrpc.RPCError(operStatusResp, operStatusErr); rpcErr != nil {
			return nil, fmt.Errorf("error while fetching operational status for account %s: %w", account.GetId(), rpcErr)
		}

		if operStatusResp.GetOperationalStatusInfo().GetOperationalStatus() == operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED {
			closedAccount = account
			break
		}
	}

	if closedAccount == nil {
		// no closed account found for the actor
		return nil, nil
	}

	if !savings.ShouldShowAlternateAccountCollectionForm(user.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, operationalStatusEnums.OperationalStatus_OPERATIONAL_STATUS_CLOSED) {
		return nil, nil
	}

	// initiate StoreClosedAccountBalTransferDataFromStatement rpc call to get the latest closed account balance data
	// StoreClosedAccountBalTransferDataFromStatement method fetches balance data from statement if balance data is not found in table already
	storeCbtResp, storeCbtErr := s.StoreClosedAccountBalTransferDataFromStatement(ctx, &savingsPb.StoreClosedAccountBalTransferDataFromStatementRequest{
		SavingsAccountId: closedAccount.GetId(),
	})
	if rpcErr := epifigrpc.RPCError(storeCbtResp, storeCbtErr); rpcErr != nil {
		return nil, fmt.Errorf("error while storing closed account balance transfer data for account %s: %w", closedAccount.GetId(), rpcErr)
	}

	// get the closed account balance transfer data
	cbts, getCbtErr := s.cbtDao.GetBySavingsAccountId(ctx, closedAccount.GetId())
	if getCbtErr != nil && !errors.Is(getCbtErr, epifierrors.ErrRecordNotFound) {
		return nil, fmt.Errorf("error while getting closed account balance transfer data for account %s: %w", closedAccount.GetId(), getCbtErr)
	}

	baRes, err := s.externalAccountsClient.GetBankAccounts(ctx, &extacct.GetBankAccountsRequest{
		ActorId: actorId,
	})
	if grpcErr := epifigrpc.RPCError(baRes, err); grpcErr != nil {
		return nil, fmt.Errorf("error while fetching bank accounts for actorId %s: %w", actorId, grpcErr)
	}

	// get next action for closed account balance transfer
	// using access revoke reason as unspecified since has app access
	nextActionDeeplink, nextActionErr := savings.GetAcctClosureNextActionFromGatheredData(ctx, actorId, user.AccessRevokeReason_ACCESS_REVOKE_REASON_UNSPECIFIED, closedAccount, cbts, baRes)
	if nextActionErr != nil {
		nextActionDeeplink = savings.ClientErrorAsDeeplink(ctx, nextActionErr)
	}

	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_SAVINGS_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
		Content: &dePb.ElementContent{
			Content: &dePb.ElementContent_BannerV2{
				BannerV2: &dePb.BannerElementContentV2{
					Title: &commontypes.Text{
						FontColor: "#262728",
						DisplayValue: &commontypes.Text_PlainString{
							PlainString: "Savings account permanently closed. Know more",
						},
					},
					Image:           commontypes.GetImageFromUrl("https://epifi-icons.pointz.in/home/<USER>/Danger_4x.png"),
					BackgroundColor: ui.GetBlockColor("#A73F4B"),
					Deeplink:        nextActionDeeplink,
				},
			},
		},
		EndTime: timestamppb.New(datetime.EndOfDay(time.Now())),
	}, nil
}

//nolint:gocritic
func getPromoWidgetForSavingsAccount(ctx context.Context) *dePb.DynamicElement {
	saDeeplik, err := onbPkg.GetSABenefitsScreen(ctx)
	if err != nil {
		logger.Error(ctx, "error while getting deeplink for savings account benefits", zap.Error(err))
		return nil
	}
	var rightHorizontalFlyers []*dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("Get back").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("2% on spends").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/savings-promo-right-1.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("Invest with ease").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("US Stocks, FD, etc").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/savings-promo-right-2.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("International Debit Card").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("0 forex fee*").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/savings-promo-right-3.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_SAVINGS_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
		BizAnalyticsData: map[string]string{
			dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Global Spends",
		},
		Content: &dePb.ElementContent{Content: &dePb.ElementContent_FeatureWidgetWithThreePoints{
			FeatureWidgetWithThreePoints: &dePb.FeatureWidgetWithThreePoints{
				Title:       commontypes.GetTextFromStringFontColourFontStyle("Rewards worth up to ₹1000/month", "#313234", commontypes.FontStyle_HEADLINE_M),
				BorderColor: homeFePb.GetHomeWidgetBorderColor(),
				LeftVerticalFlyer: &dePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
					VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/savings_promo_left.png"),
					Cta: &ui.IconTextComponent{
						Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get it now", "#6294A6", commontypes.FontStyle_BUTTON_S)},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#B2E4F1F5"},
						Deeplink:            saDeeplik,
					},
				},
				RightHorizontalFlyers: rightHorizontalFlyers,
			},
		}},
	}
}

func (s *SavingsService) getPrimaryPromoWidget(ctx context.Context, request *dePb.FetchDynamicElementsRequest) (*dePb.FetchDynamicElementsResponse, error) {
	var elementsList []*dePb.DynamicElement
	actorId := request.GetActorId()
	featureLifecycleResp, featureLifecycleErr := s.onboardingClient.GetFeatureLifecycle(ctx, &onboardingPb.GetFeatureLifecycleRequest{
		ActorId: actorId,
		Features: []onboardingPb.Feature{
			onboardingPb.Feature_FEATURE_SA,
			onboardingPb.Feature_FEATURE_FI_LITE,
		},
		WantCachedData: true,
	})
	if rpcErr := epifigrpc.RPCError(featureLifecycleResp, featureLifecycleErr); rpcErr != nil {
		logger.Error(ctx, "error while fetching feature lifecycle details for actorId", zap.Error(rpcErr), zap.String(logger.ACTOR_ID_V2, actorId))
		return &dePb.FetchDynamicElementsResponse{
			Status: rpc.StatusInternalWithDebugMsg("error while fetching feature lifecycle details"),
		}, nil
	}
	isFiLiteUser := featureLifecycleResp.GetOnboardingDetails().GetFiLiteDetails().GetIsEnabled()
	if isFiLiteUser == commontypes.BooleanEnum_TRUE {
		saFeatureLifeCycle := featureLifecycleResp.GetFeatureLifecycleMap()[onboardingPb.Feature_FEATURE_SA.String()]
		selectedSoftIntents := saFeatureLifeCycle.GetIntentSelectionInfo().GetSelectedSoftIntents()
		var isIntentForEverydayNeeds, isIntentForGlobalSpends bool
		for _, selectedSoftIntent := range selectedSoftIntents {
			if onboardingPb.SoftIntentToCategoryMap[selectedSoftIntent] == onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS {
				isIntentForEverydayNeeds = true
			}
			if onboardingPb.SoftIntentToCategoryMap[selectedSoftIntent] == onboardingPb.OnboardingSoftIntentCategory_ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING {
				isIntentForGlobalSpends = true
			}
		}

		// ordering promo widgets based on intent
		if !isIntentForEverydayNeeds && isIntentForGlobalSpends {
			elementsList = append(elementsList, getPromoWidgetForGlobalSpends(ctx), getPromoWidgetForSavingsAccount(ctx))
		} else {
			elementsList = append(elementsList, getPromoWidgetForSavingsAccount(ctx), getPromoWidgetForGlobalSpends(ctx))
		}
	}
	return &dePb.FetchDynamicElementsResponse{
		Status:       rpc.StatusOk(),
		ElementsList: elementsList,
	}, nil
}

//nolint:gocritic
func getPromoWidgetForGlobalSpends(ctx context.Context) *dePb.DynamicElement {
	saDeeplik, err := onbPkg.GetSABenefitsScreen(ctx)
	if err != nil {
		logger.Error(ctx, "error while getting deeplink for savings account benefits", zap.Error(err))
		return nil
	}

	var rightHorizontalFlyers []*dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("Accepted in").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("+180 countries").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/debit-card-right-1.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("Enjoy").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("0 Forex fees").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/debit-card-right-2.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	rightHorizontalFlyers = append(rightHorizontalFlyers, &dePb.FeatureWidgetWithThreePoints_RightHorizontalFlyer{
		PreText:   commontypes.GetPlainStringText("Global ATM usage").WithFontStyle(commontypes.FontStyle_HEADLINE_S).WithFontColor("#929599"),
		Text:      commontypes.GetPlainStringText("Low fees").WithFontStyle(commontypes.FontStyle_NUMBER_M).WithFontColor("#313234"),
		RightIcon: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/debit-card-right-3.png"),
		BgColour:  ui.GetBlockColor("#FFFFFF"),
		Deeplink:  saDeeplik,
	})

	return &dePb.DynamicElement{
		OwnerService:  types.ServiceName_SAVINGS_SERVICE,
		UtilityType:   dePb.ElementUtilityType_ELEMENT_UTILITY_TYPE_MARKETING,
		StructureType: dePb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS,
		BizAnalyticsData: map[string]string{
			dePb.BizAnalyticsDataKey_BIZ_ANALYTICS_DATA_KEY_AREA.String(): "Global Spends",
		},
		Content: &dePb.ElementContent{Content: &dePb.ElementContent_FeatureWidgetWithThreePoints{
			FeatureWidgetWithThreePoints: &dePb.FeatureWidgetWithThreePoints{
				Title:       commontypes.GetTextFromStringFontColourFontStyle("International Debit Card", "#313234", commontypes.FontStyle_HEADLINE_M),
				BorderColor: homeFePb.GetHomeWidgetBorderColor(),
				LeftVerticalFlyer: &dePb.FeatureWidgetWithThreePoints_LeftVerticalFlyer{
					VisualElement: commontypes.GetVisualElementImageFromUrl("https://epifi-icons.pointz.in/savings/debit-card-promo-left.png"),
					Cta: &ui.IconTextComponent{
						Texts:               []*commontypes.Text{commontypes.GetTextFromStringFontColourFontStyle("Get the card", "#00B899", commontypes.FontStyle_BUTTON_S)},
						ContainerProperties: &ui.IconTextComponent_ContainerProperties{BgColor: "#B2E4F1F5"},
						Deeplink:            saDeeplik,
					},
				},
				RightHorizontalFlyers: rightHorizontalFlyers,
			},
		}},
	}
}
