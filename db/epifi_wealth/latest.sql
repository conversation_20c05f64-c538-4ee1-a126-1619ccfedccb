--> Migration Version: 225 

CREATE TABLE public.schema_lock (
	lock_id INT8 NOT NULL,
	CONSTRAINT schema_lock_pkey PRIMARY KEY (lock_id ASC)
);
CREATE TABLE public.schema_migrations (
	version INT8 NOT NULL,
	dirty BOOL NOT NULL,
	CONSTRAINT schema_migrations_pkey PRIMARY KEY (version ASC)
);
CREATE TABLE public.onboarding_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	status STRING NOT NULL,
	current_step STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	completed_at TIMESTAMPTZ NULL,
	onboarding_type STRING NULL DEFAULT 'ONBOARDING_TYPE_WEALTH':::STRING,
	personal_details JSONB NULL,
	nsdl_data JSONB NULL,
	kra_data JSONB NULL,
	ckyc_data JSONB NULL,
	customer_provided_data JSONB NULL,
	match_data JSONB NULL,
	digilocker_data JSONB NULL,
	current_wealth_flow STRING NULL DEFAULT 'WEALTH_FLOW_UNSPECIFIED':::STRING,
	agent_provided_data JSONB NULL,
	CONSTRAINT onboarding_details_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_actor_id_onboarding_type (actor_id ASC, onboarding_type ASC),
	INDEX current_step_idx (current_step ASC),
	FAMILY frequently_updated (metadata, status, current_step, updated_at, id, actor_id, created_at, onboarding_type, personal_details, nsdl_data, kra_data, ckyc_data, customer_provided_data, match_data, digilocker_data, current_wealth_flow, agent_provided_data),
	FAMILY seldom_updated (completed_at)
);
COMMENT ON TABLE public.onboarding_details IS 'table to all the details related to onboarding';
COMMENT ON COLUMN public.onboarding_details.actor_id IS 'stores actor id who is onboarding';
COMMENT ON COLUMN public.onboarding_details.status IS '{"proto_type":"wealthonboarding.OnboardingStatus", "comment":"stores onboarding status"}';
COMMENT ON COLUMN public.onboarding_details.current_step IS '{"proto_type":"wealthonboarding.OnboardingStep", "comment":"stores current onboarding step"}';
COMMENT ON COLUMN public.onboarding_details.metadata IS '{"proto_type":"wealthonboarding.db.OnboardingMetadata", "comment":"stores mata data while onboarding"}';
COMMENT ON COLUMN public.onboarding_details.completed_at IS 'stores successful onboarding completion time';
COMMENT ON COLUMN public.onboarding_details.personal_details IS '{"proto_type":"wealthonboarding.db.PersonalDetails", "comment":"stores personal details collected while onboarding"}';
COMMENT ON COLUMN public.onboarding_details.nsdl_data IS '{"proto_type":"wealthonboarding.NsdlData", "comment":"stores nsdl related data"}';
COMMENT ON COLUMN public.onboarding_details.kra_data IS '{"proto_type":"wealthonboarding.db.KraData", "comment":"stores kra related data"}';
COMMENT ON COLUMN public.onboarding_details.ckyc_data IS '{"proto_type":"wealthonboarding.CkycData", "comment":"stores ckyc related data"}';
COMMENT ON COLUMN public.onboarding_details.customer_provided_data IS '{"proto_type":"wealthonboarding.CustomerProvidedData", "comment":"stores data collected from customer while onboarding"}';
COMMENT ON COLUMN public.onboarding_details.match_data IS '{"proto_type":"wealthonboarding.db.MatchData", "comment":"stores match data like fm, liveness and name match while onboarding"}';
COMMENT ON COLUMN public.onboarding_details.digilocker_data IS '{"proto_type":"wealthonboarding.DigilockerData", "comment":"stores users data downloaded from digilocker while onboarding"}';
CREATE TABLE public.onboarding_step_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	onboarding_details_id UUID NOT NULL,
	step STRING NOT NULL,
	status STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	completed_at TIMESTAMPTZ NULL,
	sub_status STRING NULL,
	staled_at TIMESTAMPTZ NULL,
	expected_retry_at TIMESTAMPTZ NULL,
	num_attempts INT8 NULL,
	CONSTRAINT onboarding_step_details_pkey PRIMARY KEY (id ASC),
	INDEX index_on_onboarding_details_id (onboarding_details_id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX uc_onboarding_details_id_and_step_and_staled_at (onboarding_details_id ASC, step ASC, staled_at ASC),
	INDEX created_at_idx (created_at DESC),
	INDEX step_idx (step ASC),
	FAMILY frequently_updated (metadata, status, updated_at, id, onboarding_details_id, step, created_at, sub_status, staled_at, expected_retry_at, num_attempts),
	FAMILY seldom_updated (completed_at)
);
COMMENT ON TABLE public.onboarding_step_details IS 'table to all the details related to onboarding';
COMMENT ON COLUMN public.onboarding_step_details.onboarding_details_id IS 'stores ref to onboarding_details table';
COMMENT ON COLUMN public.onboarding_step_details.step IS '{"proto_type":"wealthonboarding.OnboardingStep", "comment":"stores step name"}';
COMMENT ON COLUMN public.onboarding_step_details.status IS '{"proto_type":"wealthonboarding.OnboardingStepStatus", "comment":"stores onboarding status"}';
COMMENT ON COLUMN public.onboarding_step_details.metadata IS '{"proto_type":"wealthonboarding.OnboardingStepMetadata", "comment":"metadata collected in the step"}';
COMMENT ON COLUMN public.onboarding_step_details.completed_at IS 'stores successful step completion time';
COMMENT ON COLUMN public.onboarding_step_details.sub_status IS '{"proto_type":"wealthonboarding.OnboardingStepSubStatus", "comment":"stores onboarding step sub status"}';
COMMENT ON COLUMN public.onboarding_step_details.staled_at IS 'stores timestamp when the step execution was marked stale';
CREATE TABLE public.users (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	scheme_code STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	personal_details JSONB NULL,
	kyc_data JSONB NULL,
	CONSTRAINT users_pkey PRIMARY KEY (id ASC),
	INDEX updated_at_index (updated_at DESC),
	UNIQUE INDEX unique_index_on_actor_id (actor_id ASC),
	FAMILY frequently_updated (metadata, id, actor_id, created_at, updated_at, personal_details, kyc_data),
	FAMILY seldom_updated (scheme_code)
);
COMMENT ON TABLE public.users IS 'table to all the details related to wealth user';
COMMENT ON COLUMN public.users.actor_id IS 'stores fi actor id for which this wealth user is created';
COMMENT ON COLUMN public.users.scheme_code IS '{"proto_type":"wealthonboarding.user.SchemeCode", "comment":"active scheme code for the user"}';
COMMENT ON COLUMN public.users.metadata IS '{"proto_type":"wealthonboarding.user.UserMetadata", "comment":"metadata stored for the user"}';
COMMENT ON COLUMN public.users.personal_details IS '{"proto_type":"wealthonboarding.user.PersonalDetails", "comment":"personal details of the user"';
COMMENT ON COLUMN public.users.kyc_data IS '{"proto_type":"wealthonboarding.user.KycData", "comment":"kyc data collected during onboarding"';
CREATE TABLE public.e_sign_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	txn_id STRING NOT NULL,
	doc_id STRING NULL,
	sign_url STRING NULL,
	txn_state STRING NULL,
	stored_s3_path STRING NULL,
	vendor STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	metadata JSONB NULL,
	signed_at TIMESTAMPTZ NULL,
	CONSTRAINT e_sign_details_pkey PRIMARY KEY (id ASC),
	INDEX txn_id (txn_id ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_txn_id (txn_id ASC)
);
CREATE TABLE public.mutual_funds (
	id STRING NOT NULL,
	name_data JSONB NOT NULL,
	plan_type STRING NOT NULL,
	investment_type STRING NOT NULL,
	option_type STRING NOT NULL,
	div_reinv_option_type STRING NOT NULL,
	asset_class STRING NOT NULL,
	sip_allowed BOOL NOT NULL,
	swp_allowed BOOL NOT NULL,
	stp_allowed BOOL NOT NULL,
	isin_number STRING NULL,
	txn_constraints JSONB NOT NULL DEFAULT '{}':::JSONB,
	nav JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	amc STRING NOT NULL,
	scheme_code STRING NOT NULL,
	internal_status STRING NULL DEFAULT 'AVAILABLE':::STRING,
	amfi_code STRING NOT NULL DEFAULT '':::STRING,
	category_name STRING NOT NULL,
	fundhouse_defined_risk_level STRING NOT NULL,
	fi_score FLOAT8 NOT NULL DEFAULT 0.0:::FLOAT8,
	vendor_metadata JSONB NOT NULL DEFAULT '{}':::JSONB,
	daily_nav_change FLOAT8 NOT NULL DEFAULT 0.0:::FLOAT8,
	benchmark_details JSONB NOT NULL DEFAULT '{}':::JSONB,
	performance_metrics JSONB NOT NULL DEFAULT '{}':::JSONB,
	fund_fundamental_details JSONB NOT NULL DEFAULT '{}':::JSONB,
	fi_content JSONB NOT NULL DEFAULT '{}':::JSONB,
	returns JSONB NOT NULL DEFAULT '{}':::JSONB,
	computed_five_year_avg_return FLOAT8 NOT NULL AS (CASE WHEN (returns->>'avgFundReturnFiveYear':::STRING) IS NOT NULL THEN IFNULL((returns->>'avgFundReturnFiveYear':::STRING)::FLOAT8, 0.0:::FLOAT8) ELSE 0.0:::FLOAT8 END) STORED,
	computed_current_aum FLOAT8 NOT NULL AS (CASE WHEN ((fund_fundamental_details->>'aum':::STRING) IS NOT NULL) AND (((fund_fundamental_details->'aum':::STRING)->>'fundAum':::STRING) IS NOT NULL) THEN IFNULL(((fund_fundamental_details->'aum':::STRING)->>'fundAum':::STRING)::FLOAT8, 0.0:::FLOAT8) ELSE 0.0:::FLOAT8 END) STORED,
	allowed_user_groups STRING[] NULL,
	version_support_info JSONB NULL,
	fi_defined_category STRING NOT NULL DEFAULT 'MutualFundCategoryName_UNSPECIFIED':::STRING,
	computed_current_expense_ratio FLOAT8 NOT NULL AS (CASE WHEN (((fund_fundamental_details->>'expenseRatio':::STRING) IS NOT NULL) AND (((fund_fundamental_details->'expenseRatio':::STRING)->>'fundCurrent':::STRING) IS NOT NULL)) AND (((fund_fundamental_details->'expenseRatio':::STRING)->>'fundCurrent':::STRING)::FLOAT8 != 0.0:::FLOAT8) THEN IFNULL(((fund_fundamental_details->'expenseRatio':::STRING)->>'fundCurrent':::STRING)::FLOAT8, 101.0:::FLOAT8) ELSE 101.0:::FLOAT8 END) STORED,
	fund_investment_status STRING NOT NULL DEFAULT 'FundInvestmentStatus_FUND_INVESTMENT_STATUS_UNSPECIFIED':::STRING,
	computed_mstar_id STRING NULL AS ((vendor_metadata->'morningstarData':::STRING)->>'mstarId':::STRING) STORED,
	historical_returns JSONB NOT NULL DEFAULT '{}':::JSONB,
	computed_min_sip_amount INT8 NOT NULL AS (CASE WHEN (((txn_constraints->'allowedSipFrequencies':::STRING) IS NOT NULL) AND ((txn_constraints->'sipMetadata':::STRING) IS NOT NULL)) AND (((txn_constraints->'sipMetadata':::STRING)->'siDetails':::STRING) IS NOT NULL) THEN CASE WHEN (txn_constraints->'allowedSipFrequencies':::STRING) @> '"DAILY"':::JSONB THEN IFNULL(((((txn_constraints->'sipMetadata':::STRING)->'siDetails':::STRING)->'DAILY':::STRING)->>'minAmount':::STRING)::INT8, 2147483647:::INT8) WHEN (txn_constraints->'allowedSipFrequencies':::STRING) @> '"WEEKLY"':::JSONB THEN IFNULL(((((txn_constraints->'sipMetadata':::STRING)->'siDetails':::STRING)->'WEEKLY':::STRING)->>'minAmount':::STRING)::INT8, 2147483647:::INT8) WHEN (txn_constraints->'allowedSipFrequencies':::STRING) @> '"MONTHLY"':::JSONB THEN IFNULL(((((txn_constraints->'sipMetadata':::STRING)->'siDetails':::STRING)->'MONTHLY':::STRING)->>'minAmount':::STRING)::INT8, 2147483647:::INT8) ELSE 2147483647:::INT8 END ELSE 2147483647:::INT8 END) STORED,
	computed_three_year_avg_return FLOAT8 NOT NULL AS (CASE WHEN (returns->>'avgFundReturnThreeYear':::STRING) IS NOT NULL THEN IFNULL((returns->>'avgFundReturnThreeYear':::STRING)::FLOAT8, 0.0:::FLOAT8) ELSE 0.0:::FLOAT8 END) STORED,
	computed_one_year_avg_return FLOAT8 NOT NULL AS (CASE WHEN (returns->>'avgFundReturnOneYear':::STRING) IS NOT NULL THEN IFNULL((returns->>'avgFundReturnOneYear':::STRING)::FLOAT8, 0.0:::FLOAT8) ELSE 0.0:::FLOAT8 END) STORED,
	obsolete_date TIMESTAMPTZ NULL,
	fields_exempt_from_sync JSONB NULL,
	CONSTRAINT mutual_funds_pkey PRIMARY KEY (id ASC),
	INDEX idx_computed_five_year_avg_return (computed_five_year_avg_return ASC),
	INDEX idx_computed_current_aum (computed_current_aum ASC),
	UNIQUE INDEX unique_idx_isin_number (isin_number ASC),
	INDEX mutual_funds_amc_idx (amc ASC),
	INDEX mf_category_name_idx (category_name ASC),
	UNIQUE INDEX uq_scheme_code_amc (scheme_code ASC, amc ASC),
	INDEX mf_fi_defined_category_idx (fi_defined_category ASC),
	INDEX mutual_funds_updated_at_idx (updated_at ASC),
	INDEX idx_computed_current_expense_ratio (computed_current_expense_ratio ASC),
	INDEX idx_computed_min_sip_amount (computed_min_sip_amount ASC),
	INDEX idx_computed_three_year_avg_return (computed_three_year_avg_return ASC),
	INDEX idx_computed_one_year_avg_return (computed_one_year_avg_return ASC),
	UNIQUE INDEX computed_mstar_id_key (computed_mstar_id ASC) WHERE deleted_at IS NULL,
	INDEX internal_status_deleted_at_computed_current_aum (internal_status ASC, deleted_at ASC, computed_current_aum ASC)
);
COMMENT ON TABLE public.mutual_funds IS 'store house for the catalog of mutual funds offering to the epiFi users';
COMMENT ON COLUMN public.mutual_funds.plan_type IS '{"proto_type":"investment.mutual_fund.PlanType", "comment": "enum to store the plan type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mutual_funds.investment_type IS '{"proto_type":"investment.mutual_fund.InvestmentType", "comment": "enum to store the investment type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mutual_funds.option_type IS '{"proto_type":"investment.mutual_fund.OptionType", "comment": "enum to store the option type offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mutual_funds.div_reinv_option_type IS '{"proto_type":"investment.mutual_fund.DividendReinvestmentOptionType", "comment": "enum to store the DividendReinvestmentOptionType offered", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mutual_funds.txn_constraints IS '{"proto_type":"investment.mutual_fund.TransactionConstraints", "comment": "txn level permissible amount and values", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mutual_funds.nav IS 'the Net Asset Value of the mutual fund scheme';
COMMENT ON COLUMN public.mutual_funds.internal_status IS 'denotes if a fund is available for investment on our App. This is internal to our offering and has nothing to do with the market.';
COMMENT ON COLUMN public.mutual_funds.amfi_code IS 'AMFI code is issued by AMFI (Association of Mutual Funds in India) and it assigned to every share class registered in AMFI';
COMMENT ON COLUMN public.mutual_funds.category_name IS 'The Name of the assigned category';
COMMENT ON COLUMN public.mutual_funds.fundhouse_defined_risk_level IS 'Fund house defined risk level';
COMMENT ON COLUMN public.mutual_funds.fi_score IS 'Fi score is provided by fi content team';
COMMENT ON COLUMN public.mutual_funds.vendor_metadata IS 'Metadata about different vendors like Morningstar';
COMMENT ON COLUMN public.mutual_funds.daily_nav_change IS 'The change of NAV since last NAV';
COMMENT ON COLUMN public.mutual_funds.benchmark_details IS 'details about the benchmark taken for the performance evaluation of a mutual fund';
COMMENT ON COLUMN public.mutual_funds.performance_metrics IS 'matrices to measure performance of a mutual fund like alpha, information ratio etc';
COMMENT ON COLUMN public.mutual_funds.fund_fundamental_details IS 'basic details about a mutual fund like aum, market cap, etc';
COMMENT ON COLUMN public.mutual_funds.fi_content IS 'display only content provided by fi content team';
COMMENT ON COLUMN public.mutual_funds.returns IS 'will have fields {avg Return for 1,3,5,maxperiod Year,avg category return}';
COMMENT ON COLUMN public.mutual_funds.fi_defined_category IS 'The Name of the category for a fund decided by internal team';
COMMENT ON COLUMN public.mutual_funds.computed_current_expense_ratio IS 'computed expense ratio will be 101 if not present in fundamental details, need to update to null once we move to CRDB 22.xx so that we can order null at the end of queries';
COMMENT ON COLUMN public.mutual_funds.fund_investment_status IS 'Fund is available for investment or not; amc may/may not accept new investment in a fund, this can be extended if amc/rta blocks withdrawal from fund';
COMMENT ON COLUMN public.mutual_funds.computed_mstar_id IS 'mstar_id is unique identifier for a fund. mstar_id is specific to Morningstar';
COMMENT ON COLUMN public.mutual_funds.historical_returns IS 'historical_returns is used to store data points required to plot a funds return graph. proto: api.investment.mutualfund.mutual_fund.HistoricalReturns';
COMMENT ON COLUMN public.mutual_funds.computed_min_sip_amount IS 'computed_min_sip_amount column is derived from the txn constraint jsonb column. It identifies the lowest SIP amount allowed for the fund. It would be Max INT 32 value i.e 2147483647 if fund does not support SIP';
COMMENT ON COLUMN public.mutual_funds.fields_exempt_from_sync IS 'represent fields to be exempt during catalog sync';
CREATE TABLE public.auth_attempts (
	id STRING NOT NULL,
	auth_mode STRING NOT NULL,
	auth_status STRING NOT NULL,
	auth_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT auth_attempts_pkey PRIMARY KEY (id ASC)
);
COMMENT ON COLUMN public.auth_attempts.id IS 'primary identifier for the auth attempt.';
COMMENT ON COLUMN public.auth_attempts.auth_mode IS 'possible values: OTP_SMS';
COMMENT ON COLUMN public.auth_attempts.auth_status IS 'possible values: AUTH_SUCCESS, AUTH_PENDING, AUTH_FAILED and AUTH_INITIATED';
COMMENT ON COLUMN public.auth_attempts.auth_info IS '{"proto_type":"investment.auth.AuthInfo", "comment": "Metadata from external auth service like token string etc", "ref": "api/investment/auth/auth.proto"}';
CREATE TABLE public.mf_orders (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	mutual_fund_id STRING NOT NULL,
	reinv_type STRING NOT NULL,
	units FLOAT8 NOT NULL,
	amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	order_type STRING NOT NULL,
	folio_id STRING NULL,
	nav JSONB NULL,
	order_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	client STRING NOT NULL,
	client_order_id STRING NOT NULL,
	rta STRING NOT NULL,
	payment_mode STRING NOT NULL,
	failure_reason STRING NOT NULL,
	payment_info JSONB NULL DEFAULT '{}':::JSONB,
	amc STRING NULL,
	oms_orders JSONB NULL,
	order_confirmation_meta_data JSONB NULL DEFAULT '{}':::JSONB,
	external_order_id STRING NULL,
	vendor_order_id STRING NULL,
	pay_fail_reason STRING NOT NULL DEFAULT 'PAYMENT_FAILURE_REASON_UNSPECIFIED':::STRING,
	rta_confirmed_amount JSONB NULL,
	rta_confirmed_units FLOAT8 NULL,
	order_sub_type STRING NULL DEFAULT 'ORDER_SUB_TYPE_UNSPECIFIED':::STRING,
	auth_attempt_id STRING NULL,
	vendor_generated_ref_number STRING NOT NULL DEFAULT '':::STRING,
	failure_debug_reason STRING NULL,
	sip_registration_number STRING NULL,
	sip_installment_number INT4 NULL,
	sip_installment_number_status VARCHAR NOT NULL DEFAULT 'SIP_NUM_NOT_INCREMENTED':::STRING,
	CONSTRAINT mf_orders_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX uq_client_order_id (client_order_id ASC),
	INDEX updatedat_rta_orderstatus_idx (updated_at ASC, rta ASC, order_status ASC),
	INDEX updatedat_amc_orderstatus_idx (updated_at ASC, amc ASC, order_status ASC),
	INDEX fetch_orders_by_actor_id_mutual_funds (actor_id ASC, mutual_fund_id ASC),
	UNIQUE INDEX mf_orders_external_order_id_unique_idx (external_order_id ASC),
	UNIQUE INDEX mf_orders_vendor_order_id_unique_idx (vendor_order_id ASC),
	INDEX mf_orders_actor_mutualfund_createdat_idx (created_at ASC, actor_id ASC, mutual_fund_id ASC),
	INDEX mutual_fund_and_folio_id_idx (mutual_fund_id ASC, folio_id ASC) WHERE (folio_id != '':::STRING) AND (folio_id IS NOT NULL),
	INDEX mf_orders_updated_at_idx (updated_at ASC),
	INDEX mf_orders_sip_registration_number_idx (sip_registration_number ASC),
	INDEX mf_orders_folio_id_idx (folio_id ASC)
);
COMMENT ON COLUMN public.mf_orders.mutual_fund_id IS 'primary identifier to the mutual fund data model';
COMMENT ON COLUMN public.mf_orders.reinv_type IS 'possible values: PAYOUT_ONLY and REINVESTMENT_ONLY';
COMMENT ON COLUMN public.mf_orders.amount IS 'amount of purchase/redemption';
COMMENT ON COLUMN public.mf_orders.order_type IS '{"proto_type":"investment.mutual_fund.order.OrderType", "comment": "types of order supported", "ref": "api/investment/mutualfund/order.order.proto"}';
COMMENT ON COLUMN public.mf_orders.nav IS 'nav at which the execution was done';
COMMENT ON COLUMN public.mf_orders.order_status IS '{"proto_type":"investment.mutual_fund.order.mf_orderstatus", "comment": "the state of the order", "ref": "api/investment/mutualfund/order.order.proto"}';
COMMENT ON COLUMN public.mf_orders.order_confirmation_meta_data IS 'Contains the meta data sent by the vendor after the order reaches a terminal state';
COMMENT ON COLUMN public.mf_orders.external_order_id IS 'external order id which can be shared with user';
COMMENT ON COLUMN public.mf_orders.vendor_order_id IS 'order identifier we either get or post to the vendor';
COMMENT ON COLUMN public.mf_orders.auth_attempt_id IS 'stores the reference for the latest auth attempt on the order';
COMMENT ON COLUMN public.mf_orders.failure_debug_reason IS 'column to store possible failure reason for unknown order failures. To be used only for debugging';
COMMENT ON COLUMN public.mf_orders.sip_registration_number IS 'A unique registration number will be shared with vendor for every sip order within same folio(s). foreign key for sip ledger info';
COMMENT ON COLUMN public.mf_orders.sip_installment_number IS 'current installment number of the registered SIP';
CREATE TABLE public.mf_file_sequence_generator (
	id UUID NOT NULL,
	day STRING NOT NULL,
	file_type STRING NOT NULL,
	vendor_name STRING NOT NULL,
	sequence_number INT8 NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_file_sequence_generator_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_file_sequence_generator_day_file_type_vendor_name_key (day ASC, file_type ASC, vendor_name ASC),
	INDEX mf_file_sequence_generator_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_file_sequence_generator IS 'table that would help to generate consistent sequential number per vendor, file_type and date';
COMMENT ON COLUMN public.mf_file_sequence_generator.day IS 'date in YYYY-MM-DD string format';
CREATE TABLE public.amc_infos (
	id STRING NOT NULL,
	amc STRING NOT NULL,
	amc_code STRING NOT NULL,
	amc_name STRING NOT NULL,
	credit_account JSONB NOT NULL DEFAULT '{}':::JSONB,
	rta STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	credit_account_pi_id STRING NOT NULL,
	amc_actor_id STRING NOT NULL,
	CONSTRAINT amc_infos_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX amc_unique_idx (amc ASC),
	INDEX amc_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.amc_infos.id IS 'primary identifier to the amc info data model';
COMMENT ON COLUMN public.amc_infos.amc IS '{"proto_type":"investment.mutual_fund.order.Amc", "comment": "the list of AMCs supported", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.amc_infos.amc_code IS 'unique name assigned to an AMC';
COMMENT ON COLUMN public.amc_infos.amc_name IS 'unique code assigned to an AMC';
COMMENT ON COLUMN public.amc_infos.credit_account IS 'bank account details where payment needs to be credited';
COMMENT ON COLUMN public.amc_infos.rta IS 'vendor that processes the order for the given AMC';
CREATE TABLE public.mf_actor_prerequisites (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	vendor_name STRING NOT NULL,
	type STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_actor_prerequisites_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_actor_prerequisites_actor_id_vendor_name_type_key (actor_id ASC, vendor_name ASC, type ASC),
	INDEX mf_actor_prerequisites_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_actor_prerequisites IS 'stores the prerequisite status of actors';
CREATE TABLE public.mf_standing_instructions (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	from_actor_id STRING NOT NULL,
	to_actor_id STRING NOT NULL,
	from_payment_instrument_id STRING NOT NULL,
	to_payment_instrument_id STRING NOT NULL,
	maximum_amount JSONB NOT NULL,
	standing_instruction_id STRING NULL,
	transaction_id STRING NULL,
	recurring_payment_id STRING NULL,
	state STRING NOT NULL,
	partner_bank STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_standing_instructions_pkey PRIMARY KEY (id ASC),
	INDEX mf_si_from_and_to_actor_id (from_actor_id ASC, to_actor_id ASC),
	INDEX mf_standing_instructions_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_standing_instructions IS 'table that would store all the standing instructions created for an actor for the purpose of mutual fund transactions';
COMMENT ON COLUMN public.mf_standing_instructions.from_actor_id IS 'actor id of the actor who is sending the money';
COMMENT ON COLUMN public.mf_standing_instructions.to_actor_id IS 'actor id of the actor who is receiving the money. this actor is generally the amc itself';
COMMENT ON COLUMN public.mf_standing_instructions.standing_instruction_id IS 'an id that uniquely represents and id';
COMMENT ON COLUMN public.mf_standing_instructions.transaction_id IS 'transaction id corresponding to which cred block needs to be generated';
COMMENT ON COLUMN public.mf_standing_instructions.recurring_payment_id IS 'an identifier that is used to execute payments for the given transaction';
CREATE TABLE public.mf_payments (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	payment_id STRING NULL,
	payment_mode STRING NOT NULL,
	attempt_id STRING NOT NULL,
	status STRING NOT NULL,
	is_active BOOL NULL,
	amount JSONB NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	txn_protocol STRING NULL,
	txn_failure_code STRING NULL,
	utr_number STRING NULL,
	txn_completed_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_payments_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_payments_order_id_is_active_key (order_id ASC, is_active ASC),
	UNIQUE INDEX mf_payments_attempt_id_key (attempt_id ASC),
	INDEX mf_payments_payment_id_partial_index_idx (payment_id ASC) WHERE (payment_id != '':::STRING) AND (payment_id IS NOT NULL),
	INDEX mf_payments_updated_at_idx (updated_at ASC),
	INDEX mf_payments_utr_number_partial_index_idx (utr_number ASC) WHERE (utr_number != '':::STRING) AND (utr_number IS NOT NULL),
	INDEX mf_payments_created_at_idx (created_at ASC)
);
COMMENT ON TABLE public.mf_payments IS 'table that tracks the payment status for mutual fund purchases';
COMMENT ON COLUMN public.mf_payments.order_id IS 'unique identifier of a mutual fund order';
COMMENT ON COLUMN public.mf_payments.is_active IS 'activity stare of the payment to ensure that only one payment is present in the active state at a time. True represents active. False or NULL represents inactive';
COMMENT ON COLUMN public.mf_payments.txn_protocol IS 'column to store payment protocol present in transaction';
COMMENT ON COLUMN public.mf_payments.txn_failure_code IS 'column to store payment failure code present in transaction';
COMMENT ON COLUMN public.mf_payments.utr_number IS 'column to store latest known utr for the payment';
CREATE TABLE public.mf_file_generation_attempts (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	file_name STRING NULL,
	status STRING NOT NULL,
	file_type STRING NOT NULL,
	vendor_name STRING NOT NULL,
	client_request_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_file_generation_attempts_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_file_generation_attempts_client_request_id_key (client_request_id ASC),
	INDEX createdat_filetype_status (created_at ASC, file_type ASC, status ASC),
	INDEX mf_file_generation_attempts_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_file_generation_attempts IS 'stores details of a file generated by the file generator service.';
COMMENT ON COLUMN public.mf_file_generation_attempts.file_name IS 'name of the file. file name will be initially null and will be populated after the file contents gets generated';
COMMENT ON COLUMN public.mf_file_generation_attempts.status IS 'denotes the status of the file';
COMMENT ON COLUMN public.mf_file_generation_attempts.vendor_name IS 'denotes the name of the vendor for which the file is created.';
COMMENT ON COLUMN public.mf_file_generation_attempts.created_at IS 'time at which the file is created.';
CREATE TABLE public.mf_entity_file_mapper (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	entity_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	is_active BOOL NULL,
	file_id UUID NOT NULL,
	vendor_name STRING NOT NULL,
	entity_type STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NULL,
	CONSTRAINT mf_entity_file_mapper_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_entity_file_mapper_entity_id_vendor_name_entity_type_is_active_key (entity_id ASC, vendor_name ASC, entity_type ASC, is_active ASC),
	INDEX mf_entity_file_mapper_file_id_idx (file_id ASC),
	INDEX mf_entity_file_mapper_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_entity_file_mapper IS 'stores the details of the file in which an entity is stored. EntityId can be anything that is a unique identifier for a row in a file. ForEg:orderID, actorID etc.';
COMMENT ON COLUMN public.mf_entity_file_mapper.entity_id IS 'unique id assigned for an entity.';
COMMENT ON COLUMN public.mf_entity_file_mapper.is_active IS 'activity status of the entity in the file to which it is mapped to. True represents active. False or NULL represents inactive';
COMMENT ON COLUMN public.mf_entity_file_mapper.entity_type IS 'type of entity that the file represents.';
CREATE TABLE public.mf_folio_ledger (
	id STRING NOT NULL,
	folio_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	mutual_fund_id STRING NOT NULL,
	investment_goal STRING NOT NULL DEFAULT 'NO_GOAL':::STRING,
	balance_units FLOAT8 NOT NULL DEFAULT 0.0:::FLOAT8,
	redeemable_units FLOAT8 NOT NULL DEFAULT 0.0:::FLOAT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	invested_value JSONB NOT NULL DEFAULT '{}':::JSONB,
	avg_purchase_nav JSONB NOT NULL DEFAULT '{}':::JSONB,
	date_wise_available_units JSONB NOT NULL DEFAULT '{}':::JSONB,
	status STRING NULL DEFAULT 'FolioStatus_ACTIVE':::STRING,
	provenance STRING NOT NULL DEFAULT 'FolioCreationProvenance_UNSPECIFIED':::STRING,
	last_transaction_posted_at TIMESTAMPTZ NULL,
	folio_update_provenance VARCHAR NOT NULL DEFAULT 'FUP_UNSPECIFIED':::STRING,
	CONSTRAINT mf_folio_ledger_pkey PRIMARY KEY (id ASC),
	INDEX fetch_folios_by_aso (actor_id ASC, mutual_fund_id ASC, investment_goal ASC),
	INDEX fetch_folios_by_folio_id_actor_aso (folio_id ASC, actor_id ASC, mutual_fund_id ASC),
	INDEX mf_folio_ledger_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (balance_units, redeemable_units, updated_at, id, invested_value, avg_purchase_nav, date_wise_available_units, status, provenance, last_transaction_posted_at, folio_update_provenance),
	FAMILY seldom_updated (folio_id, actor_id, mutual_fund_id, investment_goal, created_at, deleted_at)
);
COMMENT ON TABLE public.mf_folio_ledger IS 'table to maintain the folio ledger for mutual funds';
COMMENT ON COLUMN public.mf_folio_ledger.folio_id IS 'folio number mapping to  "actor + ASO + goal"';
COMMENT ON COLUMN public.mf_folio_ledger.actor_id IS 'stores the fi actor id for whom this Folio is created';
COMMENT ON COLUMN public.mf_folio_ledger.investment_goal IS 'the goal for which the mutual fund investments were made in this folio';
COMMENT ON COLUMN public.mf_folio_ledger.invested_value IS 'Contains the total investment in the folio';
COMMENT ON COLUMN public.mf_folio_ledger.avg_purchase_nav IS 'avg_purchase_nav is the avg price of a single unit in the folio. Calculated using "(avg_purchase_nav * balance_units + currentNav * newUnits)/(balance_units + newUnits)';
COMMENT ON COLUMN public.mf_folio_ledger.date_wise_available_units IS 'date_wise_available_units stores a map where the key is a date and the value is the aggregated units left for the orders done for that date';
CREATE TABLE public.mf_file_state (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	file_name STRING NOT NULL,
	vendor STRING NOT NULL,
	type STRING NOT NULL,
	state STRING NOT NULL,
	is_active BOOL NULL,
	vendor_response JSONB NULL,
	file_details_meta_data JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_file_state_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_file_state_file_name_vendor_type_is_active_key (file_name ASC, vendor ASC, type ASC, is_active ASC),
	INDEX mf_file_state_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_file_state IS 'stores details of a file which is to be send to the vendor';
CREATE TABLE public.mf_prerequisite_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	client_request_id STRING NOT NULL,
	status STRING NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_prerequisite_requests_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_prerequisite_requests_client_request_id_key (client_request_id ASC),
	INDEX mf_prerequisite_requests_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_prerequisite_requests IS 'mf_prerequisite_requests is used to track the status of incoming requests to prerequisite handler';
CREATE TABLE public.mf_batch_order_processing_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_ids JSONB NOT NULL,
	status STRING NOT NULL,
	current_step STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_batch_order_processing_details_pkey PRIMARY KEY (id ASC),
	INDEX mf_batch_order_processing_details_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (metadata, status, current_step, updated_at, id),
	FAMILY seldom_updated (order_ids, created_at, deleted_at)
);
COMMENT ON TABLE public.mf_batch_order_processing_details IS 'table to all the details related to order processing request';
COMMENT ON COLUMN public.mf_batch_order_processing_details.order_ids IS 'the order ids which are part of this request';
COMMENT ON COLUMN public.mf_batch_order_processing_details.status IS '{"proto_type":"api.investment.mutualfund.order.BatchOrderProcessingStatus", "comment":"stores request status"}';
COMMENT ON COLUMN public.mf_batch_order_processing_details.current_step IS '{"proto_type":"api.investment.mutualfund.order.OrderProcessingStep", "comment":"stores current processing step"}';
COMMENT ON COLUMN public.mf_batch_order_processing_details.metadata IS '{"proto_type":"api.investment.mutualfund.order.BatchOrderProcessingMetadata", "comment":"metadata collected across multiple processing steps"}';
CREATE TABLE public.mf_batch_order_processing_step_details (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	batch_id UUID NOT NULL,
	step STRING NOT NULL,
	status STRING NOT NULL,
	metadata JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_batch_order_processing_step_details_pkey PRIMARY KEY (id ASC),
	INDEX index_on_batch_id (batch_id ASC),
	UNIQUE INDEX unique_index_on_batch_id_and_step (batch_id ASC, step ASC),
	INDEX mf_batch_order_processing_step_details_updated_at_idx (updated_at ASC),
	FAMILY frequently_updated (metadata, status, updated_at, id, batch_id, created_at),
	FAMILY seldom_updated (step, deleted_at)
);
COMMENT ON TABLE public.mf_batch_order_processing_step_details IS 'table to all the details related to steps in order processing requests';
COMMENT ON COLUMN public.mf_batch_order_processing_step_details.status IS '{"proto_type":"api.investment.mutualfund.order.OrderProcessingStepStatus", "comment":"stores status of individual step"}';
COMMENT ON COLUMN public.mf_batch_order_processing_step_details.metadata IS '{"proto_type":"api.investment.mutualfund.order.BatchOrderProcessingStepMetadata", "comment":"metadata collected the given processing step"}';
CREATE TABLE public.persistent_queue (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	payload_type STRING NOT NULL,
	payload JSONB NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	priority FLOAT8 NULL,
	CONSTRAINT "primary" PRIMARY KEY (payload_type ASC, deleted_at_unix ASC, created_at DESC, id ASC)
);
COMMENT ON TABLE public.persistent_queue IS 'table to store various types of payload in the persistent queue';
COMMENT ON COLUMN public.persistent_queue.payload_type IS '{"proto_type":"persistentqueue.PayloadType", "comment": "the payload stored is based on this type"}';
COMMENT ON COLUMN public.persistent_queue.payload IS '{"proto_type":"persistentqueue.Payload", "comment": "The payload that needs to be stored in the queue"}';
CREATE TABLE public.mf_order_status_updates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	order_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_orders_status_pkey PRIMARY KEY (id ASC),
	INDEX mf_order_id_index (order_id ASC),
	INDEX mf_order_id_updated_at_index (order_id ASC, updated_at ASC),
	INDEX mf_order_status_updates_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_order_status_updates IS 'table to store all status transitions for an order';
COMMENT ON COLUMN public.mf_order_status_updates.order_id IS 'order_id for which this status transition is. Foreign key of mf_orders.id';
COMMENT ON COLUMN public.mf_order_status_updates.order_status IS 'order_status which the order transitioned to';
COMMENT ON COLUMN public.mf_order_status_updates.updated_at IS 'the time stamp we will use to identify when the status was updated';
CREATE TABLE public.manual_reviews (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	payload JSONB NULL,
	status STRING NOT NULL,
	item_type STRING NOT NULL,
	reviewed_at TIMESTAMPTZ NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT "primary" PRIMARY KEY (id ASC)
);
CREATE TABLE public.mf_order_confirmation_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	rta_transaction_number STRING NULL,
	rta STRING NULL,
	order_confirmation_metadata JSONB NULL DEFAULT '{}':::JSONB,
	payment_confirmation_metadata JSONB NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_order_confirmation_infos_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_order_confirmation_infos_rta_transaction_number_rta_key (rta_transaction_number ASC, rta ASC),
	INDEX mf_order_confirmation_metadata_order_id_idx (order_id ASC),
	INDEX mf_order_confirmation_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON COLUMN public.mf_order_confirmation_infos.rta_transaction_number IS 'transaction number generated by the rta that can represent an order';
COMMENT ON COLUMN public.mf_order_confirmation_infos.order_confirmation_metadata IS 'contains data related to the order generated by the vendor';
COMMENT ON COLUMN public.mf_order_confirmation_infos.payment_confirmation_metadata IS 'contains data related to the payment of an order generated by the vendor';
CREATE TABLE public.mf_order_rejection_infos (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	rejected_at STRING NULL,
	rejection_reason STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	order_rejection_metadata JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT mf_order_rejection_infos_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_order_rejection_infos_order_id_key (order_id ASC),
	INDEX mf_order_rejection_infos_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_order_rejection_infos IS 'stores details of orders rejected by vendor';
COMMENT ON COLUMN public.mf_order_rejection_infos.rejected_at IS 'place where the order was rejected. Generally, either the front office or back office';
COMMENT ON COLUMN public.mf_order_rejection_infos.rejection_reason IS 'reason for the order rejection';
CREATE TABLE public.mf_category_averages (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	category_name STRING NOT NULL,
	returns JSONB NULL,
	expense_ratio JSONB NULL,
	tracking_error JSONB NULL,
	sharpe_ratio JSONB NULL,
	information_ratio JSONB NULL,
	alpha JSONB NULL,
	aum JSONB NULL,
	avg_fund_age FLOAT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	historical_returns JSONB NOT NULL DEFAULT '{}':::JSONB,
	CONSTRAINT mf_category_averages_pkey PRIMARY KEY (id ASC),
	INDEX mf_category_averages_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_category_averages IS 'store house for the category averages for all the categories of the mutual fund';
COMMENT ON COLUMN public.mf_category_averages.category_name IS '{"proto_type":"investment.mutual_fund.MutualFundCategoryName", "comment": "enum to store all the categories", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mf_category_averages.historical_returns IS 'historical_returns is used to store data points required to plot a funds category avg return graph. proto: api.investment.mutualfund.mutual_fund.HistoricalReturns';
CREATE TABLE public.mf_collections (
	id STRING NOT NULL,
	name STRING NOT NULL,
	allowed_screens STRING[] NULL,
	weight INT8 NOT NULL DEFAULT 1:::INT8,
	display_info JSONB NULL,
	allowed_user_groups STRING[] NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	curated BOOL NULL,
	CONSTRAINT mf_collections_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_collections_name_idx (name ASC),
	INDEX mf_collections_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_collections IS '{"proto_type": "investment.mutualfund.catalog.collections.Collection", "comment":"stores all data related to collections which includes display and eligibility information. Use "collection_mutual_funds" to map identify mutual funds present in a collection"}';
COMMENT ON COLUMN public.mf_collections.name IS 'unique and readable identifier for the collection, would be used where id is not present';
COMMENT ON COLUMN public.mf_collections.allowed_screens IS e'{"proto_type": "investment.mutualfund.catalog.collections.CollectionScreen",\n    "comment":"allowed_screens will help in controlling which collection to show on which screen.\n\t if allowed screens is null then collection will not be shown on any screen"';
COMMENT ON COLUMN public.mf_collections.weight IS 'weight will help in ordering tiles on UI, weight will be sorted in ascending order. In case of same weights ordering is not guaranteed';
COMMENT ON COLUMN public.mf_collections.display_info IS '{"proto_type": "investment.mutualfund.catalog.collections.CollectionDisplayInfo", "comment":"Icon, color, display name, description"}';
COMMENT ON COLUMN public.mf_collections.curated IS 'whether a collection is curated by Fi';
CREATE TABLE public.mf_collection_funds_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	mutual_fund_id STRING NOT NULL,
	collection_id STRING NOT NULL,
	display_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_collection_funds_mappings_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX collection_mutual_funds_uniq_collection_mf_idx (collection_id ASC, mutual_fund_id ASC),
	INDEX mf_collection_funds_mappings_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_collection_funds_mappings IS '{"proto_type": "investment.mutualfund.catalog.collections.CollectionMutualFund", "comment":maps the one to many relation between mf_collections and mutual_funds table"}';
COMMENT ON COLUMN public.mf_collection_funds_mappings.display_info IS '{"proto_type": "investment.mutualfund.catalog.collections.CollectionMutualFundDisplayInfo", "comment":Will contains info specific to both a particular mutual fund and a collection"}';
CREATE TABLE public.mf_watch_lists (
	id STRING NOT NULL,
	name STRING NOT NULL,
	actor_id STRING NOT NULL,
	display_info JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_watch_lists_pkey PRIMARY KEY (id ASC),
	INDEX mf_watch_lists_actor_idx (actor_id ASC),
	INDEX mf_watch_lists_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_watch_lists IS '{"proto_type": "investment.mutualfund.catalog.collections.WatchList", "comment":contains display and other info pertaining to user created watchlists. Use mutual_fund_watch_lists to identify all mutual funds present in a watchlist. This separation is done to facilitate a user to create multiple watch lists"}';
COMMENT ON COLUMN public.mf_watch_lists.display_info IS '{"proto_type": "investment.mutualfund.catalog.collections.WatchListDisplayInfo", "comment":Icon, color, display name, description"}';
CREATE TABLE public.mf_watch_list_funds_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	mutual_fund_id STRING NOT NULL,
	watch_list_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	display_info JSONB NULL,
	CONSTRAINT mf_watch_list_funds_mappings_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX watch_list_mutual_funds_deleted_at_unix_unq_idx (watch_list_id ASC, mutual_fund_id ASC, deleted_at_unix ASC),
	INDEX mf_watch_list_funds_mappings_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_watch_list_funds_mappings IS '{"proto_type": "investment.mutualfund.catalog.collections.WatchListMutualFund", "comment":maps the one to many relation between mf_watch_lists and mutual_funds table"}';
CREATE TABLE public.recent_mutual_funds (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	mutual_fund_id STRING NOT NULL,
	actor_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	last_visited_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT recent_mutual_funds_pkey PRIMARY KEY (id ASC),
	INDEX recent_mutual_funds_actor_updated_at (actor_id ASC, updated_at ASC),
	UNIQUE INDEX actor_recent_mutual_fund_uniq_idx (actor_id ASC, mutual_fund_id ASC),
	INDEX recent_mutual_funds_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.recent_mutual_funds IS '{"proto_type": "investment.mutualfund.catalog.collections.RecentMutualFund", "comment":stores all recently visited mutual_funds for an actor"}';
CREATE TABLE public.ckyc_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	type STRING NOT NULL,
	ckyc_no STRING NOT NULL,
	kyc_date TIMESTAMPTZ NULL,
	last_updated_at TIMESTAMPTZ NULL,
	account_type STRING NULL,
	raw_payload JSONB NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT ckyc_data_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX actor_id_and_type_idx (actor_id ASC, type ASC),
	INDEX actor_id (actor_id ASC),
	INDEX updated_at (updated_at ASC)
);
COMMENT ON TABLE public.ckyc_data IS 'stores details about the ckyc data for an actor';
COMMENT ON COLUMN public.ckyc_data.type IS 'this could be CKYC_SEARCH or CKYC_DOWNLOAD';
COMMENT ON COLUMN public.ckyc_data.kyc_date IS 'denotes when the kyc was first recorded';
COMMENT ON COLUMN public.ckyc_data.last_updated_at IS 'denotes when the kyc record was last updated';
COMMENT ON COLUMN public.ckyc_data.account_type IS 'denotes the ckyc account type of the user';
COMMENT ON COLUMN public.ckyc_data.raw_payload IS 'stores the raw payload of the api response';
CREATE TABLE public.mf_filter_groups (
	id STRING NOT NULL,
	type STRING NOT NULL,
	level STRING NOT NULL,
	display_info JSONB NULL,
	weight INT8 NOT NULL DEFAULT 1:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	name STRING NOT NULL DEFAULT '':::STRING,
	enabled BOOL NOT NULL DEFAULT true,
	CONSTRAINT mf_filter_groups_pkey PRIMARY KEY (id ASC),
	INDEX mf_filter_groups_level_idx (level ASC),
	UNIQUE INDEX mf_filter_groups_name_idx (name ASC),
	INDEX mf_filter_groups_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_filter_groups IS 'categories for filtering mutual funds';
COMMENT ON COLUMN public.mf_filter_groups.type IS 'type of filter group';
COMMENT ON COLUMN public.mf_filter_groups.weight IS 'for sorting categories on screen';
COMMENT ON COLUMN public.mf_filter_groups.name IS 'internal human-readable name for a filter group';
CREATE TABLE public.mf_filters (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	name STRING NOT NULL,
	type STRING NOT NULL,
	query_info JSONB NOT NULL,
	display_info JSONB NOT NULL,
	filter_group_id STRING NOT NULL,
	weight INT8 NOT NULL DEFAULT 1:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	child_group_ids STRING[] NULL,
	enabled BOOL NOT NULL DEFAULT true,
	CONSTRAINT mf_filters_pkey PRIMARY KEY (id ASC),
	INDEX mf_filters_filter_group_id_idx (filter_group_id ASC),
	UNIQUE INDEX mf_filters_name_idx (name ASC),
	INDEX mf_filters_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_filters IS 'conditions for filtering mutual funds';
COMMENT ON COLUMN public.mf_filters.name IS 'internal human-readable name for a filter';
COMMENT ON COLUMN public.mf_filters.type IS 'type of filter, e.g. range-based, field-based';
COMMENT ON COLUMN public.mf_filters.query_info IS 'info to create the query when filter is selected';
COMMENT ON COLUMN public.mf_filters.filter_group_id IS 'filter group that filter belongs to';
COMMENT ON COLUMN public.mf_filters.weight IS 'for sorting filters on screen';
COMMENT ON COLUMN public.mf_filters.child_group_ids IS 'child filter groups for a filter, e.g hybrid funds by allocation and by solution for hybrid fund type filter';
CREATE TABLE public.mf_collection_filter_mappings (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	collection_id STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	filter_id STRING NOT NULL,
	CONSTRAINT mf_collection_filter_mappings_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX collection_id_filter_id_uniq_idx (collection_id ASC, filter_id ASC),
	INDEX mf_collection_filter_mappings_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.mf_collection_filter_mappings IS 'one to many relation between mf_collections and mf_filters';
CREATE TABLE public.do_once_tasks (
	task_name STRING NOT NULL,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	CONSTRAINT do_once_tasks_pkey PRIMARY KEY (task_name ASC, deleted_at_unix ASC),
	INDEX do_once_tasks_updated_at_idx (updated_at ASC)
);
COMMENT ON TABLE public.do_once_tasks IS 'to identify tasks by their unique names that should be done exactly once';
COMMENT ON COLUMN public.do_once_tasks.task_name IS 'unique identifier for a task';
COMMENT ON COLUMN public.do_once_tasks.deleted_at_unix IS 'non-zero for soft-deleted tasks so that another task with same name can be created again once previous task is soft-deleted';
CREATE TABLE public.mf_sip_ledger (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	mutual_fund_id STRING NOT NULL,
	sip_registration_number STRING NOT NULL,
	fit_subscription_id STRING NULL,
	sip_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	sip_granularity STRING NULL DEFAULT 'SIP_GRANULARITY_UNSPECIFIED':::STRING,
	sip_status STRING NULL DEFAULT 'SIP_STATUS_UNSPECIFIED':::STRING,
	start_date TIMESTAMPTZ NULL,
	end_date TIMESTAMPTZ NULL,
	total_installments INT8 NULL DEFAULT 0:::INT8,
	successful_installments INT8 NULL DEFAULT 0:::INT8,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_sip_ledger_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX sip_reg_no_uniq_idx (sip_registration_number ASC),
	INDEX actor_id_status_idx (actor_id ASC, sip_status ASC),
	INDEX updated_at (updated_at ASC),
	INDEX fit_id_status_idx (fit_subscription_id ASC, sip_status ASC) WHERE (fit_subscription_id != '':::STRING) AND (fit_subscription_id IS NOT NULL),
	UNIQUE INDEX fit_id_active_status_uniq_idx (fit_subscription_id ASC, sip_status ASC) WHERE ((fit_subscription_id != '':::STRING) AND (fit_subscription_id IS NOT NULL)) AND (sip_status = 'SIP_STATUS_ACTIVE':::STRING),
	INDEX sip_status_registration_number_idx (sip_status ASC, sip_registration_number ASC)
);
COMMENT ON TABLE public.mf_sip_ledger IS 'responsible for storing and maintaining all active and past SIP registrations';
COMMENT ON COLUMN public.mf_sip_ledger.sip_registration_number IS 'unique registration number will be shared with vendor for every sip order within same folio(s), sip number is generated when a new SIP is registered';
COMMENT ON COLUMN public.mf_sip_ledger.fit_subscription_id IS 'corresponding fittt subscription for this SIP';
COMMENT ON COLUMN public.mf_sip_ledger.sip_amount IS 'amount to be invested per installment for this SIP';
COMMENT ON COLUMN public.mf_sip_ledger.sip_granularity IS 'granularity of SIP. Eg: DAILY, WEEKLY and MONTHLY';
COMMENT ON COLUMN public.mf_sip_ledger.sip_status IS 'status of the SIP. Eg: ACTIVE, CANCELLED, INACTIVE';
COMMENT ON COLUMN public.mf_sip_ledger.start_date IS 'start date of the SIP';
COMMENT ON COLUMN public.mf_sip_ledger.end_date IS 'end date of the SIP';
COMMENT ON COLUMN public.mf_sip_ledger.total_installments IS 'Total number of installments that can be placed for this SIP';
COMMENT ON COLUMN public.mf_sip_ledger.successful_installments IS 'Number of successful installment submitted to the vendor. We would be deciding the next installment number based on this';
CREATE TABLE public.mf_filters_v2 (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	filter_name STRING NOT NULL,
	query_info JSONB NOT NULL DEFAULT '{}':::JSONB,
	display_info JSONB NOT NULL DEFAULT '{}':::JSONB,
	data_load_type STRING NOT NULL DEFAULT 'DataLoadType_DATA_LOAD_TYPE_UNSPECIFIED':::STRING,
	filter_group_id STRING NOT NULL,
	child_group_ids STRING[] NULL,
	weight INT8 NOT NULL DEFAULT 1:::INT8,
	enabled BOOL NULL DEFAULT false,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_filters_v2_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX filter_name (filter_name ASC),
	INDEX mf_filters_v2_filter_group_id_idx (filter_group_id ASC)
);
COMMENT ON TABLE public.mf_filters_v2 IS 'conditions for filtering mutual funds';
COMMENT ON COLUMN public.mf_filters_v2.filter_name IS 'internal human-readable name for a filter';
COMMENT ON COLUMN public.mf_filters_v2.query_info IS 'info to create the query when filter is selected';
COMMENT ON COLUMN public.mf_filters_v2.display_info IS 'info to display on screen';
COMMENT ON COLUMN public.mf_filters_v2.data_load_type IS 'enum to determine how to load data';
COMMENT ON COLUMN public.mf_filters_v2.filter_group_id IS 'filter group that filter belongs to';
COMMENT ON COLUMN public.mf_filters_v2.child_group_ids IS 'child filter group for a filter, e.g equity-fund-types for equity filter';
COMMENT ON COLUMN public.mf_filters_v2.weight IS 'for sorting filters on screen';
CREATE TABLE public.actor_notification_status (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	actor_state STRING NOT NULL DEFAULT 'ACTOR_STATE_UNSPECIFIED':::STRING,
	actor_substate STRING NOT NULL DEFAULT 'ACTOR_SUBSTATE_UNSPECIFIED':::STRING,
	in_state_since TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	notification_usecase STRING NOT NULL DEFAULT 'NOTIFICATION_USECASE_UNSPECIFIED':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT actor_notification_status_pkey PRIMARY KEY (id ASC),
	INDEX index_on_in_state_since (in_state_since ASC),
	INDEX updated_at (updated_at DESC),
	UNIQUE INDEX unique_index_on_actor_id_and_notification_usecase_and_deleted_at_unix (actor_id ASC, notification_usecase ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.actor_notification_status IS 'table to store the notification state for an actor';
COMMENT ON COLUMN public.actor_notification_status.actor_id IS 'actor_id of an actor';
COMMENT ON COLUMN public.actor_notification_status.actor_state IS 'enum for current state of an actor';
COMMENT ON COLUMN public.actor_notification_status.actor_substate IS 'enum for current sub-state of an actor';
COMMENT ON COLUMN public.actor_notification_status.in_state_since IS 'time at which the user entered this state';
COMMENT ON COLUMN public.actor_notification_status.notification_usecase IS 'enum for usecase of the notification for this actor';
COMMENT ON COLUMN public.actor_notification_status.deleted_at_unix IS 'non-zero for soft-deleted tasks';
CREATE TABLE public.workflow_requests (
	id STRING NOT NULL,
	actor_id STRING NULL,
	stage STRING NOT NULL,
	status STRING NOT NULL,
	version STRING NOT NULL,
	type STRING NOT NULL,
	payload BYTES NULL,
	client_req_id STRING NOT NULL,
	ownership STRING NOT NULL,
	next_action JSONB NULL,
	created_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	UNIQUE INDEX workflow_requests_client_req_id_key (client_req_id ASC),
	INDEX workflow_requests_updated_at_idx (updated_at ASC),
	INDEX workflow_requests_type_stage_status_created_at_idx (type ASC, stage ASC, status ASC, created_at ASC),
	FAMILY frequently_updated (stage, status, updated_at, next_action),
	FAMILY "primary" (id, actor_id, version, type, payload, client_req_id, ownership, created_at, deleted_at)
);
COMMENT ON TABLE public.workflow_requests IS 'workflow_requests maintains all us stocks orders which went through orchestration';
COMMENT ON COLUMN public.workflow_requests.stage IS 'defines current stage of workflow execution';
COMMENT ON COLUMN public.workflow_requests.status IS 'defines current status of the ongoing stage of workflow execution';
COMMENT ON COLUMN public.workflow_requests.type IS 'defines type of workflow under execution';
COMMENT ON COLUMN public.workflow_requests.payload IS 'json format of request payload';
CREATE TABLE public.workflow_histories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	wf_req_id STRING NOT NULL,
	ext_req_id STRING NULL,
	payload BYTES NULL,
	stage STRING NOT NULL,
	status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	completed_at TIMESTAMPTZ NULL,
	attempts INT8 NOT NULL DEFAULT 1:::INT8,
	failure_description STRING NULL,
	CONSTRAINT workflow_histories_pkey PRIMARY KEY (id ASC),
	INDEX workflow_histories_updated_at_idx (updated_at ASC),
	INDEX workflow_histories_wf_req_id_stage_idx (wf_req_id ASC, stage ASC),
	FAMILY frequently_updated (stage, status, updated_at, attempts, payload, ext_req_id, id),
	FAMILY "primary" (wf_req_id, created_at, deleted_at, completed_at, failure_description)
);
COMMENT ON TABLE public.workflow_histories IS 'workflow_histories tracks status of all stages for a given workflow';
COMMENT ON COLUMN public.workflow_histories.wf_req_id IS 'corresponding workflow Id for which the stage is being executed';
COMMENT ON COLUMN public.workflow_histories.ext_req_id IS 'If a stage is required to raise request with other services, ext_req_id will store the Identifier received. It can be mainly used for reverse lookups';
COMMENT ON COLUMN public.workflow_histories.payload IS 'payload is a json field used to store stage specific payload';
CREATE TABLE public.mf_external_orders (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	isin_number STRING NULL,
	amc STRING NOT NULL,
	folio_id STRING NOT NULL,
	external_order_type STRING NOT NULL DEFAULT 'EXTERNAL_ORDER_TYPE_UNSPECIFIED':::STRING,
	transaction_date TIMESTAMPTZ NOT NULL,
	purchase_price JSONB NOT NULL DEFAULT '{}':::JSONB,
	transaction_amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	transaction_charge JSONB NOT NULL DEFAULT '{}':::JSONB,
	transaction_units FLOAT8 NOT NULL,
	transaction_mode STRING NULL,
	posted_date TIMESTAMPTZ NOT NULL,
	tax JSONB NOT NULL DEFAULT '{}':::JSONB,
	scheme_code STRING NULL,
	scheme_name STRING NULL,
	order_identifier STRING NULL,
	stamp_duty FLOAT8 NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	transaction_description STRING NULL,
	mutual_fund_id STRING NULL,
	CONSTRAINT mf_external_orders_pkey PRIMARY KEY (actor_id ASC, id ASC),
	INDEX updated_at (updated_at DESC),
	INDEX actor_id_and_posted_date (actor_id ASC, posted_date DESC),
	INDEX actor_id_transaction_date_idx (actor_id ASC, transaction_date DESC) WHERE deleted_at IS NULL
);
COMMENT ON TABLE public.mf_external_orders IS 'stores all the external mutual fund orders';
COMMENT ON COLUMN public.mf_external_orders.actor_id IS 'actor id';
COMMENT ON COLUMN public.mf_external_orders.isin_number IS 'isin number of a mutual fund';
COMMENT ON COLUMN public.mf_external_orders.amc IS '{"proto_type":"investment.mutual_fund.order.Amc", "comment": "the list of AMCs supported", "ref": "api/investment/mutualfund/mutual_fund.proto"}';
COMMENT ON COLUMN public.mf_external_orders.folio_id IS 'folio id with appended check digit for that mf order';
COMMENT ON COLUMN public.mf_external_orders.external_order_type IS '{"proto_type":"investment.mutual_fund.external.ExternalOrderType", "comment": "types of order supported", "ref": "api/investment/mutualfund/external/mutual_fund_external_order.proto"}';
COMMENT ON COLUMN public.mf_external_orders.transaction_date IS 'date of transaction for that order';
COMMENT ON COLUMN public.mf_external_orders.purchase_price IS 'purchase price of the order';
COMMENT ON COLUMN public.mf_external_orders.transaction_amount IS 'transaction amount for the order';
COMMENT ON COLUMN public.mf_external_orders.transaction_charge IS 'transaction charge';
COMMENT ON COLUMN public.mf_external_orders.transaction_units IS 'units of transaction for a mutual fund';
COMMENT ON COLUMN public.mf_external_orders.transaction_mode IS 'mode of transaction';
COMMENT ON COLUMN public.mf_external_orders.posted_date IS 'for sorting filters on screen';
COMMENT ON COLUMN public.mf_external_orders.tax IS '{"proto_type":"investment.mutual_fund.external.Tax", "comment": "tax components stt, total tax", "ref": "api/investment/mutualfund/analyser/mutual_fund_external_order.proto"}';
COMMENT ON COLUMN public.mf_external_orders.scheme_code IS 'scheme_code';
COMMENT ON COLUMN public.mf_external_orders.scheme_name IS 'scheme name';
COMMENT ON COLUMN public.mf_external_orders.order_identifier IS 'identifier for a mf_external_order';
COMMENT ON COLUMN public.mf_external_orders.stamp_duty IS 'stamp_duty for the order';
COMMENT ON COLUMN public.mf_external_orders.transaction_description IS 'transaction description for the external order';
CREATE TABLE public.mf_external_holdings_import_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	consent_id STRING NULL,
	transaction_id STRING NULL,
	client_reference_number STRING NOT NULL,
	state STRING NOT NULL DEFAULT 'STATE_UNSPECIFIED':::STRING,
	failure_reason STRING NOT NULL DEFAULT 'FAILURE_REASON_UNSPECIFIED':::STRING,
	vendor STRING NOT NULL DEFAULT 'VENDOR_UNSPECIFIED':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	vendor_data JSONB NULL DEFAULT '{}':::JSONB,
	external_id UUID NOT NULL DEFAULT gen_random_uuid(),
	meta_data JSONB NULL DEFAULT '{}':::JSONB,
	provenance STRING NULL,
	flow_type STRING NULL,
	CONSTRAINT mf_external_holdings_import_requests_pkey PRIMARY KEY (id ASC),
	INDEX updated_at (updated_at DESC),
	INDEX actor_id (actor_id DESC),
	UNIQUE INDEX transaction_id_vendor_key (transaction_id DESC, vendor ASC),
	UNIQUE INDEX client_reference_number_key (client_reference_number DESC),
	UNIQUE INDEX external_id_key (external_id ASC),
	INDEX mf_external_holdings_import_requests_actor_id_updated_at_idx (actor_id ASC, updated_at ASC)
);
COMMENT ON TABLE public.mf_external_holdings_import_requests IS 'table to store and track the external mutual fund holdings fetch and processing';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.actor_id IS 'actor_id of an actor';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.consent_id IS 'consent to the request';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.transaction_id IS 'transaction_id generated in response to the vendor';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.client_reference_number IS 'unique 10 digit identifier which gets verified at the vendor';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.state IS 'current state of the mf holdings request';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.failure_reason IS 'reason for the failure of mf holdings request';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.vendor_data IS 'vendor response for a transaction id';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.external_id IS 'unique identifier for the row that can be shared externally';
COMMENT ON COLUMN public.mf_external_holdings_import_requests.meta_data IS 'json meta data for a request';
CREATE TABLE public.mutual_fund_nav_histories (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	isin STRING NULL,
	nav_date TIMESTAMPTZ NOT NULL,
	nav JSONB NOT NULL,
	vendor STRING NOT NULL,
	nav_source STRING NOT NULL,
	nav_derived_date TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	mutual_fund_id STRING NOT NULL,
	CONSTRAINT mutual_fund_nav_histories_pkey PRIMARY KEY (mutual_fund_id ASC, id ASC),
	UNIQUE INDEX mutual_fund_nav_histories_mutual_fund_id_nav_dates_key (mutual_fund_id ASC, nav_date ASC) WHERE deleted_at IS NULL,
	INDEX mutual_fund_nav_histories_updated_at_idx (updated_at DESC),
	UNIQUE INDEX mutual_fund_nav_histories_isin_nav_date_key (isin ASC, nav_date ASC) WHERE (deleted_at IS NULL) AND (isin IS NOT NULL)
);
COMMENT ON TABLE public.mutual_fund_nav_histories IS 'table to store history nav data for mutual funds';
CREATE TABLE public.mutual_fund_external_holdings_summaries (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	email STRING NULL,
	amc_code STRING NOT NULL,
	amc_name STRING NOT NULL,
	folio STRING NOT NULL,
	scheme_code STRING NOT NULL,
	scheme_name STRING NULL,
	kyc_status STRING NULL,
	broker_code STRING NULL,
	broker_name STRING NULL,
	rta_code STRING NOT NULL,
	opening_balance JSONB NOT NULL,
	closing_balance JSONB NOT NULL,
	market_value JSONB NOT NULL,
	nav JSONB NOT NULL,
	is_demat BOOL NOT NULL,
	asset_type STRING NOT NULL,
	isin STRING NULL,
	last_transaction_date TIMESTAMPTZ NOT NULL,
	last_nav_date TIMESTAMPTZ NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mutual_fund_external_holdings_summaries_pkey PRIMARY KEY (actor_id ASC, id ASC),
	INDEX mutual_fund_external_holdings_summaries_updated_at_idx (updated_at DESC),
	INDEX mutual_fund_external_holdings_summaries_actor_id_last_transaction_date_idx (last_transaction_date DESC) WHERE deleted_at IS NOT NULL
);
COMMENT ON TABLE public.mutual_fund_external_holdings_summaries IS 'table to store mutual fund holdings summaries';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.amc_name IS '{"proto_type":"investment.mutualfund.AMC", "comment":"mutual fund amc"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.opening_balance IS '{"struct_type":"pkg.money.Money", "comment":"opening balance"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.closing_balance IS '{"struct_type":"pkg.money.Money", "comment":"closing balance"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.market_value IS '{"struct_type":"pkg.money.Money", "comment":"market value of the scheme units"}';
COMMENT ON COLUMN public.mutual_fund_external_holdings_summaries.nav IS '{"struct_type":"pkg.money.Money", "comment":"nav of scheme as per last_nav_date"}';
CREATE TABLE public.investment_risk_survey (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	survey_data JSONB NOT NULL DEFAULT '{}':::JSONB,
	survey_status STRING NOT NULL DEFAULT 'SURVEY_STATUS_UNSPECIFIED':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT investment_risk_survey_pkey PRIMARY KEY (id ASC),
	INDEX investment_risk_survey_updated_at_idx (updated_at DESC),
	INDEX investment_risk_survey_actor_id_idx (actor_id DESC),
	INDEX investment_risk_survey_survey_status_idx (survey_status ASC),
	UNIQUE INDEX investment_risk_survey_actor_id_deleted_at_unix_idx (actor_id ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.investment_risk_survey IS 'stores the investment risk survey questions and answers for each actor';
COMMENT ON COLUMN public.investment_risk_survey.survey_data IS 'questions and answers collected from the user';
COMMENT ON COLUMN public.investment_risk_survey.survey_status IS 'status of the survey data collection';
CREATE TABLE public.investment_risk_profile (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	risk_survey_id UUID NOT NULL,
	overall_risk_score DECIMAL(4,2) NOT NULL,
	risk_level STRING NOT NULL DEFAULT 'RISK_LEVEL_UNSPECIFIED':::STRING,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT investment_risk_profile_pkey PRIMARY KEY (id ASC),
	INDEX investment_risk_profile_updated_at_idx (updated_at DESC),
	INDEX investment_risk_profile_actor_id_idx (actor_id DESC),
	UNIQUE INDEX investment_risk_profile_actor_id_deleted_at_unix_idx (actor_id ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.investment_risk_profile IS 'stores the investment strategy data for each actor';
COMMENT ON COLUMN public.investment_risk_profile.risk_survey_id IS 'primary id of investment_risk_survey table';
COMMENT ON COLUMN public.investment_risk_profile.overall_risk_score IS 'overall score assigned to actor based on the risk survey';
COMMENT ON COLUMN public.investment_risk_profile.risk_level IS 'investment risk level assigned to actor';
CREATE TABLE public.mf_order_reconciliation_updates (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	order_id STRING NOT NULL,
	folio_id STRING NOT NULL,
	amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	nav JSONB NOT NULL DEFAULT '{}':::JSONB,
	units FLOAT8 NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_order_reconciliation_updates_pkey PRIMARY KEY (id ASC),
	INDEX mf_order_reconciliation_updates_order_id_idx (order_id ASC)
);
COMMENT ON TABLE public.mf_order_reconciliation_updates IS 'stores all updates that happens on mf_orders table during reconciliation';
COMMENT ON COLUMN public.mf_order_reconciliation_updates.order_id IS 'id of the mutual fund order. This is the primary key id of mf_orders table';
CREATE TABLE public.mf_rta_order_data (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	mutual_fund_id STRING NOT NULL,
	amc STRING NOT NULL,
	scheme_code STRING NOT NULL,
	vendor_order_id STRING NOT NULL,
	folio_id STRING NULL,
	amount JSONB NOT NULL DEFAULT '{}':::JSONB,
	nav JSONB NOT NULL DEFAULT '{}':::JSONB,
	units FLOAT8 NOT NULL,
	order_type STRING NOT NULL,
	status STRING NOT NULL,
	meta_data JSONB NOT NULL DEFAULT '{}':::JSONB,
	order_date TIMESTAMPTZ NOT NULL,
	mapped_mf_order_id STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_rta_order_data_pkey PRIMARY KEY (id ASC),
	INDEX mf_rta_order_data_actor_id_amc_idx (actor_id ASC, amc ASC),
	UNIQUE INDEX mf_rta_order_data_vendor_order_id (vendor_order_id ASC)
);
CREATE TABLE public.dynamic_ui_element (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	variant_name STRING NOT NULL,
	content_json JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT dynamic_ui_element_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX variant_name (variant_name ASC, deleted_at_unix DESC)
);
COMMENT ON TABLE public.dynamic_ui_element IS 'table for storing json of different ui content which can rendered on different screens+ usecases';
COMMENT ON COLUMN public.dynamic_ui_element.variant_name IS 'name for the ui content config';
COMMENT ON COLUMN public.dynamic_ui_element.content_json IS 'json of the ui content config';
CREATE TABLE public.dynamic_ui_element_evaluator_config (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	dynamic_ui_screen STRING NOT NULL DEFAULT 'DYNAMIC_UI_SCREEN_UNSPECIFIED':::STRING,
	dynamic_ui_usecase STRING NOT NULL DEFAULT 'DYNAMIC_UI_USECASE_UNSPECIFIED':::STRING,
	evaluator_expression_config JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at_unix INT8 NOT NULL DEFAULT 0:::INT8,
	CONSTRAINT dynamic_ui_element_evaluator_config_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX screen_usecase_idx (dynamic_ui_screen ASC, dynamic_ui_usecase ASC, deleted_at_unix ASC)
);
COMMENT ON TABLE public.dynamic_ui_element_evaluator_config IS 'table for defining config for controlling defined ui elements on multiple screens';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_config.dynamic_ui_screen IS 'screen for the ui content';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_config.dynamic_ui_usecase IS 'usecase for the ui content';
COMMENT ON COLUMN public.dynamic_ui_element_evaluator_config.evaluator_expression_config IS 'config to evaluate expression and figure out the ui variant to show';
CREATE TABLE public.aa_enriched_transactions (
	id STRING NOT NULL,
	pi_from STRING NOT NULL,
	pi_to STRING NOT NULL,
	utr STRING NOT NULL,
	amount JSONB NOT NULL,
	computed_amount INT8 NOT NULL AS ((IFNULL((amount->>'units':::STRING)::INT8, 0:::INT8) * 100:::INT8) + ((IFNULL((amount->>'nanos':::STRING)::INT8, 0:::INT8) * 100:::INT8) / **********:::INT8)::INT8) STORED,
	payment_protocol STRING NOT NULL,
	payload JSONB NULL,
	transaction_type STRING NOT NULL,
	remarks STRING NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	executed_at TIMESTAMPTZ NOT NULL,
	partner_ref_id STRING NOT NULL,
	bank STRING NOT NULL,
	aa_txn_id STRING NOT NULL,
	computed_account_id STRING NOT NULL AS (payload->>'accountId':::STRING) STORED,
	computed_from_actor_id STRING NOT NULL AS (payload->>'fromActorId':::STRING) STORED,
	computed_to_actor_id STRING NOT NULL AS (payload->>'toActorId':::STRING) STORED,
	template_id STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX transactions_created_at_idx (created_at ASC),
	INDEX aa_transactions_executed_at_idx (executed_at ASC),
	INDEX aa_transactions_updated_at_idx (updated_at ASC),
	UNIQUE INDEX aa_transactions_aa_txn_id (aa_txn_id ASC),
	INDEX aa_transactions_computed_account_id_idx (computed_account_id ASC),
	INDEX aa_transactions_computed_from_actor_id_idx (computed_from_actor_id ASC),
	INDEX aa_transactions_computed_to_actor_id_idx (computed_to_actor_id ASC),
	INDEX aa_transactions_utr_idx (utr ASC),
	INDEX aa_transactions_computed_account_id_executed_at_idx (computed_account_id ASC, executed_at ASC),
	INDEX aa_transactions_pi_from_computed_account_id_idx (pi_from ASC, computed_account_id ASC),
	INDEX aa_transactions_pi_to_computed_account_id_idx (pi_to ASC, computed_account_id ASC),
	FAMILY frequently_updated (updated_at, deleted_at, template_id),
	FAMILY "primary" (id, pi_from, pi_to, amount, computed_amount, payment_protocol, remarks, created_at, transaction_type, payload, utr, executed_at, partner_ref_id, bank, aa_txn_id, computed_account_id, computed_from_actor_id, computed_to_actor_id)
);
COMMENT ON TABLE public.aa_enriched_transactions IS '{"comment":"aa_enriched_transactions stores transactions from connected account."}';
COMMENT ON COLUMN public.aa_enriched_transactions.bank IS '{"proto_type":"api.types.bank", "comment": "connected bank involved in the transaction"}';
COMMENT ON COLUMN public.aa_enriched_transactions.aa_txn_id IS '{"comment": "internal transaction id shared by account aggregator service"}';
COMMENT ON COLUMN public.aa_enriched_transactions.template_id IS '{"proto_type":"string", "comment": "template id of the smart parser used for parsing"}';
CREATE TABLE public.mf_nominee_info (
	id STRING NOT NULL,
	actor_id STRING NOT NULL,
	nominee_id STRING NOT NULL,
	folio_id STRING NOT NULL,
	vendor STRING NOT NULL,
	vendor_request_id STRING NOT NULL,
	update_status STRING NOT NULL,
	failure_reason STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_nominee_info_pkey PRIMARY KEY (id ASC),
	UNIQUE INDEX mf_nominee_info_actor_id_nominee_id_folio_id_key (actor_id ASC, nominee_id ASC, folio_id ASC),
	UNIQUE INDEX mf_nominee_info_vendor_request_id_key (vendor_request_id ASC),
	INDEX updated_at (updated_at DESC)
);
COMMENT ON TABLE public.mf_nominee_info IS 'stores the nominee update status for a given folio id.';
COMMENT ON COLUMN public.mf_nominee_info.vendor_request_id IS 'stores a unique request id used to make idempotent calls to vendor';
COMMENT ON COLUMN public.mf_nominee_info.update_status IS 'stores the status (success/failure) of the nominee update at vendor.';
COMMENT ON COLUMN public.mf_nominee_info.failure_reason IS 'stores the reason for nominee update failure if the nominee update failed. This is only relevant  when update_status represents failure';
CREATE TABLE public.mf_external_nft_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	external_id STRING NOT NULL,
	client_reference_id STRING NOT NULL,
	type STRING NOT NULL,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	vendor STRING NOT NULL,
	provenance STRING NOT NULL,
	details JSONB NOT NULL DEFAULT '{}':::JSONB,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	CONSTRAINT mf_external_nft_requests_id_pkey PRIMARY KEY (id ASC),
	INDEX mf_external_nft_requests_updated_at_idx (updated_at DESC),
	INDEX mf_external_nft_requests_actor_id_created_at_idx (actor_id DESC, created_at DESC),
	UNIQUE INDEX mf_external_nft_requests_external_id_key (external_id DESC),
	UNIQUE INDEX mf_external_nft_requests_client_reference_id_key (client_reference_id DESC)
);
CREATE TABLE public.mf_single_otp_cas_import_requests (
	id UUID NOT NULL DEFAULT gen_random_uuid(),
	actor_id STRING NOT NULL,
	primary_statement_request_id UUID NOT NULL,
	contact_identifier STRING NOT NULL,
	contact_identifier_type STRING NOT NULL,
	statement_type STRING NOT NULL,
	vendor_response JSONB NULL DEFAULT '{}':::JSONB,
	status STRING NOT NULL,
	sub_status STRING NOT NULL,
	created_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	updated_at TIMESTAMPTZ NOT NULL DEFAULT now():::TIMESTAMPTZ,
	deleted_at TIMESTAMPTZ NULL,
	details JSONB NULL DEFAULT '{}':::JSONB,
	CONSTRAINT mf_single_otp_cas_import_requests_pkey PRIMARY KEY (id ASC),
	INDEX mf_single_otp_cas_import_requests_updated_at (updated_at DESC),
	UNIQUE INDEX mf_single_otp_cas_import_requests_primary_stmt_req_id_contact_identifier_key (primary_statement_request_id ASC, contact_identifier ASC),
	INDEX mf_single_otp_cas_import_requests_actor_id_created_at (actor_id ASC, created_at DESC) STORING (contact_identifier, status)
);
COMMENT ON TABLE public.mf_single_otp_cas_import_requests IS 'mf_single_otp_cas_import_requests table is used to maintain details of all additional CAS statements fetched via Single-Otp imports. Single-OTP flow can be used to fetch CAS summary and detailed statements using a single OTP. First statement is fetched using the standard flow. Once the first statement is fetched, other statements can be fetched without any otp verification within the next 10 minutes. MfSingleOtpCasImportRequest can be used to fetch the subsequent statement. It requires details of successful first statement fetch to be present in the mf_external_holdings_import_requests table';
CREATE TABLE public.consents (
	id STRING NOT NULL,
	created_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	updated_at TIMESTAMP NOT NULL DEFAULT now():::TIMESTAMP,
	deleted_at TIMESTAMP NULL,
	actor JSONB NOT NULL,
	device JSONB NOT NULL,
	consent_type STRING NULL,
	version INT8 NULL,
	computed_actor_id STRING NULL AS (actor->>'id':::STRING) STORED,
	expires_at TIMESTAMPTZ NULL,
	actor_id STRING NOT NULL DEFAULT '':::STRING,
	ip_address_token STRING NULL,
	client_req_id STRING NULL,
	provenance STRING NULL,
	CONSTRAINT "primary" PRIMARY KEY (id ASC),
	INDEX computed_actor_id_by_consent_idx (computed_actor_id ASC, consent_type ASC),
	INDEX consents_updated_at_idx (updated_at ASC),
	INDEX actor_id_by_consent_idx (actor_id ASC, consent_type ASC),
	UNIQUE INDEX consents_client_req_id_key (client_req_id ASC)
);
COMMENT ON COLUMN public.consents.provenance IS 'source of the consent';
ALTER TABLE public.onboarding_step_details ADD CONSTRAINT fk_onboarding_id_ref_aa_consent_requests FOREIGN KEY (onboarding_details_id) REFERENCES public.onboarding_details(id);
ALTER TABLE public.mf_orders ADD CONSTRAINT fk_mf_orders_mf_id FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_orders ADD CONSTRAINT fk_auth_id_ref_auth_attempts FOREIGN KEY (auth_attempt_id) REFERENCES public.auth_attempts(id);
ALTER TABLE public.mf_payments ADD CONSTRAINT mf_payments_ref_mf_orders FOREIGN KEY (order_id) REFERENCES public.mf_orders(id);
ALTER TABLE public.mf_entity_file_mapper ADD CONSTRAINT mf_file_generation_attempts_ref_mf_entity_file_mapper FOREIGN KEY (file_id) REFERENCES public.mf_file_generation_attempts(id);
ALTER TABLE public.mf_folio_ledger ADD CONSTRAINT mutual_fund_exists FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_batch_order_processing_step_details ADD CONSTRAINT fk_batch_id_ref_mf_order_processing_request FOREIGN KEY (batch_id) REFERENCES public.mf_batch_order_processing_details(id);
ALTER TABLE public.mf_order_status_updates ADD CONSTRAINT mutual_fund_order_exists FOREIGN KEY (order_id) REFERENCES public.mf_orders(id);
ALTER TABLE public.mf_order_confirmation_infos ADD CONSTRAINT fk_order_id_ref_mf_orders FOREIGN KEY (order_id) REFERENCES public.mf_orders(id);
ALTER TABLE public.mf_order_rejection_infos ADD CONSTRAINT fk_mf_order_rejection_infos_ref_mf_orders FOREIGN KEY (order_id) REFERENCES public.mf_orders(id);
ALTER TABLE public.mf_collection_funds_mappings ADD CONSTRAINT fk_mf_collection_funds_mappings_ref_mutual_funds FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_collection_funds_mappings ADD CONSTRAINT fk_mf_collection_funds_mappings_ref_mf_collections FOREIGN KEY (collection_id) REFERENCES public.mf_collections(id);
ALTER TABLE public.mf_watch_list_funds_mappings ADD CONSTRAINT fk_mf_watch_list_funds_mappings_ref_mutual_funds FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_watch_list_funds_mappings ADD CONSTRAINT fk_mf_watch_list_funds_mappings_ref_mf_watch_lists FOREIGN KEY (watch_list_id) REFERENCES public.mf_watch_lists(id);
ALTER TABLE public.recent_mutual_funds ADD CONSTRAINT fk_recent_mutual_funds_ref_mutual_funds FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_filters ADD CONSTRAINT fk_mf_filters_group_id_ref_mf_filter_groups FOREIGN KEY (filter_group_id) REFERENCES public.mf_filter_groups(name);
ALTER TABLE public.mf_collection_filter_mappings ADD CONSTRAINT fk_mf_collection_filter_mappings_ref_mf_collections FOREIGN KEY (collection_id) REFERENCES public.mf_collections(id);
ALTER TABLE public.mf_collection_filter_mappings ADD CONSTRAINT fk_mf_collection_filter_mappings_ref_mf_filters FOREIGN KEY (filter_id) REFERENCES public.mf_filters(name);
ALTER TABLE public.mf_sip_ledger ADD CONSTRAINT mutual_fund_id_fk_const FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_filters_v2 ADD CONSTRAINT fk_mf_filters_group_id_ref_mf_filter_groups FOREIGN KEY (filter_group_id) REFERENCES public.mf_filter_groups(name);
ALTER TABLE public.workflow_histories ADD CONSTRAINT fk_wf_req_id_workflow_requests FOREIGN KEY (wf_req_id) REFERENCES public.workflow_requests(id);
ALTER TABLE public.mf_order_reconciliation_updates ADD CONSTRAINT mf_order_reconciliation_updates_ref_mf_orders FOREIGN KEY (order_id) REFERENCES public.mf_orders(id);
ALTER TABLE public.mf_rta_order_data ADD CONSTRAINT mf_rta_order_data_ref_mutual_funds FOREIGN KEY (mutual_fund_id) REFERENCES public.mutual_funds(id);
ALTER TABLE public.mf_nominee_info ADD CONSTRAINT fk_folio_ledger_id FOREIGN KEY (folio_id) REFERENCES public.mf_folio_ledger(id);
ALTER TABLE public.mf_single_otp_cas_import_requests ADD CONSTRAINT mf_single_otp_cas_import_requests_primary_statement_request_id_external_id_fkey FOREIGN KEY (primary_statement_request_id) REFERENCES public.mf_external_holdings_import_requests(external_id) ON DELETE CASCADE ON UPDATE CASCADE;
-- Validate foreign key constraints. These can fail if there was unvalidated data during the SHOW CREATE ALL TABLES
ALTER TABLE public.onboarding_step_details VALIDATE CONSTRAINT fk_onboarding_id_ref_aa_consent_requests;
ALTER TABLE public.mf_orders VALIDATE CONSTRAINT fk_mf_orders_mf_id;
ALTER TABLE public.mf_orders VALIDATE CONSTRAINT fk_auth_id_ref_auth_attempts;
ALTER TABLE public.mf_payments VALIDATE CONSTRAINT mf_payments_ref_mf_orders;
ALTER TABLE public.mf_entity_file_mapper VALIDATE CONSTRAINT mf_file_generation_attempts_ref_mf_entity_file_mapper;
ALTER TABLE public.mf_folio_ledger VALIDATE CONSTRAINT mutual_fund_exists;
ALTER TABLE public.mf_batch_order_processing_step_details VALIDATE CONSTRAINT fk_batch_id_ref_mf_order_processing_request;
ALTER TABLE public.mf_order_status_updates VALIDATE CONSTRAINT mutual_fund_order_exists;
ALTER TABLE public.mf_order_confirmation_infos VALIDATE CONSTRAINT fk_order_id_ref_mf_orders;
ALTER TABLE public.mf_order_rejection_infos VALIDATE CONSTRAINT fk_mf_order_rejection_infos_ref_mf_orders;
ALTER TABLE public.mf_collection_funds_mappings VALIDATE CONSTRAINT fk_mf_collection_funds_mappings_ref_mutual_funds;
ALTER TABLE public.mf_collection_funds_mappings VALIDATE CONSTRAINT fk_mf_collection_funds_mappings_ref_mf_collections;
ALTER TABLE public.mf_watch_list_funds_mappings VALIDATE CONSTRAINT fk_mf_watch_list_funds_mappings_ref_mutual_funds;
ALTER TABLE public.mf_watch_list_funds_mappings VALIDATE CONSTRAINT fk_mf_watch_list_funds_mappings_ref_mf_watch_lists;
ALTER TABLE public.recent_mutual_funds VALIDATE CONSTRAINT fk_recent_mutual_funds_ref_mutual_funds;
ALTER TABLE public.mf_filters VALIDATE CONSTRAINT fk_mf_filters_group_id_ref_mf_filter_groups;
ALTER TABLE public.mf_collection_filter_mappings VALIDATE CONSTRAINT fk_mf_collection_filter_mappings_ref_mf_collections;
ALTER TABLE public.mf_collection_filter_mappings VALIDATE CONSTRAINT fk_mf_collection_filter_mappings_ref_mf_filters;
ALTER TABLE public.mf_sip_ledger VALIDATE CONSTRAINT mutual_fund_id_fk_const;
ALTER TABLE public.mf_filters_v2 VALIDATE CONSTRAINT fk_mf_filters_group_id_ref_mf_filter_groups;
ALTER TABLE public.workflow_histories VALIDATE CONSTRAINT fk_wf_req_id_workflow_requests;
ALTER TABLE public.mf_order_reconciliation_updates VALIDATE CONSTRAINT mf_order_reconciliation_updates_ref_mf_orders;
ALTER TABLE public.mf_rta_order_data VALIDATE CONSTRAINT mf_rta_order_data_ref_mutual_funds;
ALTER TABLE public.mf_nominee_info VALIDATE CONSTRAINT fk_folio_ledger_id;
ALTER TABLE public.mf_single_otp_cas_import_requests VALIDATE CONSTRAINT mf_single_otp_cas_import_requests_primary_statement_request_id_external_id_fkey;
