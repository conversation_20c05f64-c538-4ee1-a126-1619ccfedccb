Application:
  WorkerOptions:
    Options:
      MaxConcurrentActivityExecutionSize: 100
      WorkerActivitiesPerSecond: 50.0
      MaxConcurrentLocalActivityExecutionSize: 100
      WorkerLocalActivitiesPerSecond: 100.0
      TaskQueueActivitiesPerSecond: 50.0
      MaxConcurrentActivityTaskPollers: 5
      MaxConcurrentWorkflowTaskExecutionSize: 300
      MaxConcurrentWorkflowTaskPollers: 5
  Flags:
    SkipDeprecatedCelestialActivitiesRegistration: false

Server:
  HttpPort: 9090
  GrpcPort: 9000

AWS:
  Region: "ap-south-1"

DefaultActivityParamsList:
  - ActivityName: "GetWorkflowProcessingParamsV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "GetWorkflowProcessingParams"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "InitiateWorkflowStage"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateWorkflowStageStatus"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateWorkflowStage"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishWorkflowUpdateEvent"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "SendNotification"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "CreateOrderV1"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateOrder"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishOrderUpdate"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "UpdateOrderWorkflowRefID"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishToSQS"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "PublishWorkflowUpdateEventV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "InitiateWorkflowStageV2"
    ScheduleToCloseTimeout: "3h"
    StartToCloseTimeout: "10s"
    RetryParams:
      # Exponential retry strategy that runs for 3 hours with max cap between retries at 30min
      # Retry interval - 4s 8s 16s 32s 1m4s 2m8s 4m16s 8m32s 17m4s 30m0s 30m0s 30m0s 30m0s 30m0s
      ExponentialBackOff:
        BaseInterval: "4s"
        MaxInterval: "30m"
        BackoffCoefficient: 2.0
        MaxAttempts: 14
  - ActivityName: "GetLoanRequest"
    ScheduleToCloseTimeout: "4h"
    StartToCloseTimeout: "5m"
    RetryParams:
      ExponentialBackOff:
        BaseInterval: "1s"
        MaxInterval: "5m"
        BackoffCoefficient: 2
        MaxAttempts: 35
  - ActivityName: "IsFeatureEnabled"
    ScheduleToCloseTimeout: "1h"
    StartToCloseTimeout: "5m"
    RetryParams:
      ExponentialBackOff:
        BaseInterval: "2s"
        MaxInterval: "1m"
        BackoffCoefficient: 2
        MaxAttempts: 65

WorkflowParamsList:
  - WorkflowName: "PreApprovedLoanApplication"
    ActivityParamsList:
      - ActivityName: "ProcessLoanApplication"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "GetOtpVerificationStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "CheckKycEligibility"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckLivenessEligibility"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckFaceMatchEligibility"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "ProcessLivenessVendorReview"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForRisk"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "ProcessLoanAccountCreation"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckKfsStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1500
      - ActivityName: "InitialiseKfsStage"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 1500
      - ActivityName: "CheckProfileValidation"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForLimitChange"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckAuthLiveness"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3000
      - ActivityName: "GenerateKfsDocumentInternally"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 18000
      - ActivityName: "CheckForRiskV2"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForLimit"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300

  - WorkflowName: "KycCheck"
    ActivityParamsList:
      - ActivityName: "ProcessKycCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 1000
  - WorkflowName: "LivenessCheck"
    ActivityParamsList:
      - ActivityName: "ProcessLivenessCheck"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 30 mins with max cap between retries at 2 seconds
          # Retry interval - 1s 1.010s 1.08s 1.110s 1.2s 1.10s ....after around 17 mins 2s 2s 2s 2s 2s
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10s"
            BackoffCoefficient: 1.001
            MaxAttempts: 1000
  - WorkflowName: "FaceMatchCheck"
    ActivityParamsList:
      - ActivityName: "ProcessFaceMatchCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
  - WorkflowName: "ManualReviewCheck"
    ActivityParamsList:
      - ActivityName: "CheckManualReviewStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 1500
      - ActivityName: "CheckManualReviewForLivenessAndFaceMatch"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
  - WorkflowName: "PreApprovedLoanPrePay"
    ActivityParamsList:
      - ActivityName: "ProcessPrePayPayment"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1500
  - WorkflowName: "PreCloseLoanAccount"
    ActivityParamsList:
      - ActivityName: "ProcessPreClosure"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1500
  - WorkflowName: "LoanApplication"
    ActivityParamsList:
      - ActivityName: "AbflRegisterPwaUser"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "IncomeEstimation"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "IncomeEstimationStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "2h"
            BackoffCoefficient: 2
            MaxAttempts: 55
      - ActivityName: "LendenPreBreLoanDataCollectionStatus"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "120s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "AbflPushDataToVendor"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "FetchAndSetAbflPwaJourneyLink"
        ScheduleToCloseTimeout: "720h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5m"
            MaxAttempts: 36000
      - ActivityName: "AbflPwaCreateLoanAccount"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "SgKycAmlStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "60s"
            MaxAttempts: 3600
      - ActivityName: "CheckKycEligibilityV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "SgGetCkycUserVerificationStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateVkyc"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgVkycStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgCheckVkycStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "300s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateEmploymentCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgEmploymentCheckStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgCheckForRiskScreenActor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "ContactabilityCheck"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "SgOnboardingAtVendor"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgKycDocumentDownload"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10m"
            MaxAttempts: 432
      - ActivityName: "SgKycDataVerification"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10m"
            MaxAttempts: 432
      - ActivityName: "SgUpdateUserDetailsAtVendor"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "IsFiCoreUser"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "IsBasicAddressCollected"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "ExecuteOnboardingStage"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 720
      - ActivityName: "UpdateBankingDetailsAtSg"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgVendorBre"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateLivenessAndStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateDrawdown"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgGetDrawdownStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateDisbursal"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgGetDisbursalStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateEsign"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgGetEsignStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgLoanAccountCreation"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgSetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgGetMandateStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "15s"
            MaxAttempts: 3600
      - ActivityName: "ExecuteInSync"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 10
      - ActivityName: "UpdateSyncStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 10
      - ActivityName: "SgInitiateMandate"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiatePennyDrop"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgPennyDropStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgInitiateKyc"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgGetOTPVerificationStatus"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "SgCkycStatusAndSetVerificationScreen"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "ContactabilityVerify"
        ScheduleToCloseTimeout: "55h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 65
      - ActivityName: "ProcessKycCheckV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "CheckAuthLivenessV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "GetFeatureVersion"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "4s"
            MaxAttempts: 2500
      - ActivityName: "FederalPreKfs"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "4s"
            MaxAttempts: 2500
      - ActivityName: "FedRealtimeOfferCheck"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FedRealtimeLoanAccountCreation"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FedRealtimeLoanAccountCreationStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1200
      - ActivityName: "FederalLoanDisbursement"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FederalLoanDisbursementEnquiry"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1200
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FederalProcessLoanAccountCreationV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "CheckKfsStatusV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "FedKfsInSync"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 72
      - ActivityName: "CheckForLimitChangeV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "ProcessLivenessVendorReviewV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "FederalProcessLoanApplicationV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "FederalCheckProfileValidationV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "CheckForRiskV3"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "LLCheckCkyc"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "LLESCheckCkyc"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "LLESSi"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLFetchOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLCreateApplicant"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAddAddressDetailsV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAddEmploymentDetailsV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAddEmploymentDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAddEmploymentDetailsWithSalary"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAddAddressDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLCheckLivenessFacematch"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 9000
      - ActivityName: "LLGetMandateUrl"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLGetMandateStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLGetAddressStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLGetEmploymentStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "LLGetESignAgreementDocument"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLUploadDocument"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLAsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLDrawdown"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLCheckDisbursal"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 120
      - ActivityName: "GetLoanOffer"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 20
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 120
      - ActivityName: "LLCheckApplicantStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 2min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m0s 2m0s ....
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "2m"
            BackoffCoefficient: 2.0
            MaxAttempts: 154
      - ActivityName: "GetBankingStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "AddBankingDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "AddAadhaarDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "AddLeadDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "IdfcGetMandatePage"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcGetMandateStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcValidateOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcLoanStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcPanVerification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcValidatePan"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcPanStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcDobVerification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcCheckCkyc"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcCheckLivenessFacematch"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcCkycStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcOccupationVerification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcDrawdown"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcUploadLivenessFacematchDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcGenerateKfsDocument"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcCheckLoanStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "IdfcDisbursal"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "RefreshLoanScheduleFromVendor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "GenerateKfsDocumentInternally"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 18000
      - ActivityName: "CheckForRiskV2"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForLimit"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "IdfcVkyc"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "InitiateIdfcVkyc"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckVkycStatus"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckForRiskScreenActor"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Regular interval of 2s is followed for the first 60 seconds.
          # Post that another regular interval of 30 seconds is followed for 2 hours (2 hours)
          Hybrid:
            RetryStrategy1:
              RegularInterval:
                Interval: "10s"
                MaxAttempts: 30
            RetryStrategy2:
              RegularInterval:
                Interval: "30s"
                MaxAttempts: 240
            MaxAttempts: 270
            CutOff: 30
      - ActivityName: "PanUniqueCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiftyfinFetchMfPortfolio"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiftyfinFundVerification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiftyfinFundVerificationV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiftyfinWaitForUserAction"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiftyfinWaitForLoanVerifyUserAction"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "2m"
            MaxAttempts: 7500
      - ActivityName: "SetOtpDataForLienMark"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "GetLienMarkOtpStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "ValidateMarkedLien"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinLoanAccountCreation"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinInitLoan"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 2min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m0s 2m0s ....
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "2m"
            BackoffCoefficient: 2.0
            MaxAttempts: 154
      - ActivityName: "FiftyfinCreateLoan"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 2min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m0s 2m0s ....
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "2m"
            BackoffCoefficient: 2.0
            MaxAttempts: 154
      - ActivityName: "FiftyfinLinkBankAccount"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 2min
          # Retry interval - 4s 8s 16s 32s 1m4s 2m0s 2m0s ....
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "2m"
            BackoffCoefficient: 2.0
            MaxAttempts: 154
      - ActivityName: "ValidateLoanAmount"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinGetEsignMandatePage"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinGetAdditionalKycDetailScreen"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "CheckFiftyfinAdditionalKycDetailsInputStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinSubmitAdditionalKycDetail"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinSetDrawdownSuccessScreen"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 50
      - ActivityName: "FiftyfinKyc"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinGetKycStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinKycSetNextAction"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinGetApplicationProcessStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinSendLienMarkSuccessComms"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinSendLoanDisbursedSuccessComms"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinVoidLoan"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinSetLoanResetNextAction"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500
      - ActivityName: "FiftyfinLoanResetLoanWaitForUserAction"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 500

      - ActivityName: "AbflPerformCkyc"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPollCkycStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPerformBre"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPollBreStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "CaptureSelfie"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPerformUkyc"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPollUkycStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPennyDrop"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPennyDropStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflAddCommonDetails"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflLatLong"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetMandateUrl"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetMandateStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetDigilockerUrl"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetDigilockerStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SetAbflKycDetailsScreen"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetVerifyKycStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetAgreementSignStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflUploadEsignDocument"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflRecalculateLoanValues"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGetESignStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPerformDisbursement"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflGenerateKfsDocument"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflPollDisbursementStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "AbflCreateLoanAccount"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      # ======== LENDEN ACTIVITIES : START =========
      #      TODO: check with the QAteam if the retry params are correct
      - ActivityName: "LdcUpdateUserSelectedOffer"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LdcInitKyc"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LdcCheckKycStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having retries that are too close apart
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "LdcCheckMandateStatus"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having retries that are too close apart
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "LdcGenerateKfsDocs"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LDCSignKFSLADocs"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LDCAllowROIModification"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LDCInitiateReKfsLaEsign"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "30m" # around 2hrs
            BackoffCoefficient: 1.5
            MaxAttempts: 160
      - ActivityName: "LdcDisbursal"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      # ======== LENDEN ACTIVITIES : END ===========
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetReferencesAdditionStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "MvValidateConsent"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 10
      - ActivityName: "MvCreateLead"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        # retry for around 15 mins with exponential backoff strategy i.e retry should happen at 2s, 4s, 8s, 10s, 20s, 40s .....
        # intentionally setting small ScheduleToCloseTimeout, since on retry exhaustion we will fail workflow and user will be able to retry again.
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 11
      - ActivityName: "MvFetchOffer"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        # retry for around 15 mins with exponential backoff strategy i.e retry should happen at 2s, 4s, 8s, 10s, 20s, 40s .....
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 11
      - ActivityName: "MvProcessVendorPwaStages"
        ScheduleToCloseTimeout: "720h" # 30 days
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "3h"
            MaxAttempts: 20
      - ActivityName: "MvProcessAccountCreation"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        # retry for around 15 mins with exponential backoff strategy i.e retry should happen at 2s, 4s, 8s, 10s, 20s, 40s .....
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "30m"
            MaxAttempts: 20
      - ActivityName: "CheckPanAadhaarLinkedStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "AsyncLoanStepStateCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "FiftyfinUpdateUserDetails"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "10s"
            BackoffCoefficient: 2
            MaxAttempts: 5
      - ActivityName: "GetExpiredPropertyForStage"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "LLCreateRps"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "AddLeadDetailsV2"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "PartnerLmsUserCreation"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "PartnerLmsLoanCreation"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "PartnerLmsLoanDisbursal"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "LlRtSubvenVendorBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FedInitiatePennyDrop"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FedPennyDropStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FedSetupMandateIntroScreen"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FedSetupPennyDropIntroScreen"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "5m"
        RetryParams:
          # Exponential retry strategy that runs for 24 hours  with max cap between retries at 1 hr
          # Retry interval -  1s 2s 4s 8s 16s ....till 1hr 1hr
          # After around 2 hours, 12 attempts, the interval reaches around 1hr, Post this all intervals will be 1hr
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 35
      - ActivityName: "FedGetMandateStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "VendorHardOfferCreation"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "CheckHardOffer"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30
      - ActivityName: "EstimateSalary"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "AutoPayVerifyAccount"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having aggressive retries
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721
      - ActivityName: "AutoPaySetupMandate"
        ScheduleToCloseTimeout: "72h"
        StartToCloseTimeout: "5m"
        # This activity runs in sync-proxy mode, hence no need for having aggressive retries
        # which is usually the case in exponential backoff.
        RetryParams:
          RegularInterval:
            Interval: "1h"
            MaxAttempts: 721

  - WorkflowName: "ProcessFiEligibleBase"
    ActivityParamsList:
      - ActivityName: "ReadCsvFromS3InBatches"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "3m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 100
  - WorkflowName: "ProcessChildFiEligibleBase"
    ActivityParamsList:
      - ActivityName: "ProcessLOEC"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 150

  - WorkflowName: "LoanPrePay"
    ActivityParamsList:
      - ActivityName: "LDCPaymentStatusCheck"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "5s"
            MaxInterval: "5m"
            BackoffCoefficient: 1.5
            MaxAttempts: 13
      - ActivityName: "ProcessPayment"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1500
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1500
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200

  - WorkflowName: "VendorCallbackProcessor"
    ActivityParamsList:
      - ActivityName: "ProcessCallbackNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56

  - WorkflowName: "ProcessVendorNotification"
    ActivityParamsList:
      - ActivityName: "ProcessCallbackNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "ProcessLamfVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Exponential retry strategy that runs for 5 hours with max cap between retries at 5 min
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s 4m16s 5m 5m 5m 5m 5m
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2.0
            MaxAttempts: 56
      - ActivityName: "MvProcessVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "10m"
            MaxAttempts: 30
      - ActivityName: "SetuProcessVendorNotification"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            BackoffCoefficient: 2.0
            MaxInterval: "10m"
            MaxAttempts: 30

  - WorkflowName: "MutualFundNft"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "UpdateWorkflowStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "PublishWorkflowUpdateEventV2"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "FiftyfinMutualFundNftSetOtpData"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 120
      - ActivityName: "FiftyfinMutualFundNftVerifyOtpStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 720
      - ActivityName: "FiftyfinMutualFundNftVerifyOtpCompletion"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 120
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "UpdateLoanRequestV2"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60

  - WorkflowName: "LoanPreCloseV3"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "UpdateWorkflowStage"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "PublishWorkflowUpdateEventV2"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "10s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "4s"
            MaxInterval: "30m"
            BackoffCoefficient: 2.0
            MaxAttempts: 14
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "30s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 20

  - WorkflowName: "LoanCollectionPaymentAndReconcile"
    ActivityParamsList:
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "ExecuteSi"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LmsRefreshAndSaveApiReconcile"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "RefreshLmsFetchAndEvaluationReconcile"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 50
      - ActivityName: "CreateLoanActivity"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300

  - WorkflowName: "LoanCollection"
    ChildWorkflowParamsList:
      - WorkflowName: "LoanCollectionPaymentAndReconcile"
        WorkflowExecutionTimeout: "24h"
        WorkflowRunTimeout: "24h"
        ParentClosePolicy: 1
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10s"
            BackoffCoefficient: 2.0
            MaxAttempts: 1
    ActivityParamsList:
      - ActivityName: "CreateLoanPaymentRequest"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "GetRepaymentBreakup"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanActivity"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "RefreshLmsAndSaveApiReconcile"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 50
      - ActivityName: "FetchLmsAndRefreshReconcile"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300

  - WorkflowName: "LoanEligibility"
    ActivityParamsList:
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "NameGenderCheck"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CheckPanDobStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreateApplicant"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "PanDobCheck"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2
      - ActivityName: "FlFetchOffer"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "AsyncStepTerminalStatusCheckV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLGetAddressStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FlAddAddressDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "LLGetEmploymentStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FLAddEmploymentDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "CreateLoec"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "5s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 60
      - ActivityName: "LLCheckApplicantStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FlVendorBreCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FetchEpfoData"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FiBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "RtSubvenFiBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "EstimateIncome"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "EstimateIncomeViaItrIntimation"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "IncomeEstimatePreRequisite"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FiPreBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GenerateUserOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "PortfolioFetch"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FiCoreBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FiCorePreBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LLCreateApplicantV2"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LLAddEmploymentDetailsV2"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LLAddAddressDetailsV2"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LLCheckApplicantStatusRealTime"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "LLFetchOfferRealtime"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "UpdateApplicantDetails"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgRtDistFiBreCheck"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgSetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgFetchUserActionStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgCreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "SgCreditReportFetchStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "UpdateLoec"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
      - ActivityName: "FedAddEmploymentDetails"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2
      - ActivityName: "LLCreateApplicant"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "GetSetuAaConsentUrl"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 1200
      - ActivityName: "GetSetuJourneyStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 1200
      - ActivityName: "FedAddAddressDetails"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2
      - ActivityName: "FederalGetSetuUrl"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "30s"
            MaxAttempts: 120
      - ActivityName: "FederalSetuStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 12000
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "AddAddressDetailsV2"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2

  - WorkflowName: "LoanDataCollection"
    ActivityParamsList:
      - ActivityName: "MultiPolicyFiPreBre"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "MultiPolicyFiBre"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoecDataStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "NameGenderCheck"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "NameGenderCheckV2"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CheckPanDobStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "PanDobCheck"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "PanDobCheckV2"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "SetCreditReportFetchScreen"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreditReportFetchStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CreditReportFetchStatusSync"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2
      - ActivityName: "FetchUserActionStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "AsyncStepTerminalStatusCheck"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "AsyncStepTerminalStatusCheckV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "FetchEpfoData"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 7500
      - ActivityName: "GetAddressStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetEmploymentStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "DeactivateLoanOffer"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          # Retry interval - 1s 2s 4s 8s 16s 32s 1m4s 2m8s ... 1h 1h 1h 1h ...
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "AddAddressDetailsV2"
        ScheduleToCloseTimeout: "8s"
        StartToCloseTimeout: "10s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 2
      - ActivityName: "LendenPreBreLoanDataCollectionStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "LendenPreBreConsentStatus"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "OfferCreation"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "CheckDataCompleteness"
        ScheduleToCloseTimeout: "60m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1800
      - ActivityName: "EstimateSalary"
        ScheduleToCloseTimeout: "10h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 900
      - ActivityName: "IncomeEstimation"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1440
      - ActivityName: "IncomeEstimationStatus"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 1440
      - ActivityName: "FiPreBreV2"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FedBreConsentStatus"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "GetAlternateOfferOrFailNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "2s"
            MaxAttempts: 60
      - ActivityName: "ExecuteOnboardingStage"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 720
      - ActivityName: "ExecuteInSync"
        ScheduleToCloseTimeout: "2m"
        StartToCloseTimeout: "30s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 10
      - ActivityName: "UpdateSyncStatus"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "5m"
        RetryParams:
          RegularInterval:
            Interval: "1s"
            MaxAttempts: 10
      - ActivityName: "IsFeatureEnabled"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "WaitForDataCollection"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "CreditReportFetch"
        ScheduleToCloseTimeout: "6h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "2s"
            MaxInterval: "1h"
            BackoffCoefficient: 2
            MaxAttempts: 16
      - ActivityName: "CheckHardOffer"
        ScheduleToCloseTimeout: "4h"
        StartToCloseTimeout: "5m"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "5m"
            BackoffCoefficient: 2
            MaxAttempts: 30

  - WorkflowName: "CreateAllocation"
    ActivityParamsList:
      - ActivityName: "CreateAllocationAtCollection"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
      - ActivityName: "CreateAllocationAtVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200

  - WorkflowName: "UpdatePayment"
    ActivityParamsList:
      - ActivityName: "UpdatePaymentAtVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200

  - WorkflowName: "SyncAllActiveCollectionDetails"
    ActivityParamsList:
      - ActivityName: "FetchBatchActiveLeads"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60
    ChildWorkflowParamsList:
      - WorkflowName: "SyncLeadDetails"
        WorkflowExecutionTimeout: "6h"
        WorkflowRunTimeout: "1h"
        ParentClosePolicy: 2
        RetryParams:
          # Exponential retry strategy that runs for ~20min with max cap between retries at 10min
          # Retry interval - 10s 20s 40s 1min20s 2min40s 5min20s 10min40s
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "3h"
            BackoffCoefficient: 2.0
            MaxAttempts: 7

  - WorkflowName: "SyncLeadDetails"
    ActivityParamsList:
      - ActivityName: "UpdateLeadDetailsAtVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "DownloadLeadDetailsFromVendor"
        ScheduleToCloseTimeout: "5m"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200

  - WorkflowName: "LoanPreClose"
    ActivityParamsList:
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckPrePayStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CloseLoanAccount"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300

  - WorkflowName: "AssetPortfolioFetch"
    ActivityParamsList:
      - ActivityName: "UpdateLoanRequestV2"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "FetchPortfolio"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "VerifyOtpStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "SetOtpData"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "SetUserDetailsScreen"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CheckUserDetailsStatus"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "UpdateUserDetails"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "FiftyfinUpdateUserDetailsInLoanApplicant"
        ScheduleToCloseTimeout: "30m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "GetLoanRequest"
        ScheduleToCloseTimeout: "2h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "IsFeatureEnabled"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 300
      - ActivityName: "FetchMfcPortfolio"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "FetchMfcCasSummaryPortfolio"
        ScheduleToCloseTimeout: "1h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 3600
      - ActivityName: "VerifyOtpSuccess"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "ProcessPortfolioFetchCompletion"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300

  - WorkflowName: "SetupSi"
    ActivityParamsList:
      - ActivityName: "CreateSi"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 15000
      - ActivityName: "UpdateLoanRequest"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "5s"
            MaxAttempts: 300
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLRNextAction"
        ScheduleToCloseTimeout: "120s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 60

  - WorkflowName: "ProcessOffAppRepayment"
    ActivityParamsList:
      - ActivityName: "CreateLoanActivityEntry"
        ScheduleToCloseTimeout: "5h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 40
      - ActivityName: "LLRecordPayment"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 180
      - ActivityName: "IDFCRecordPayment"
        ScheduleToCloseTimeout: "24h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "1s"
            MaxInterval: "10m"
            BackoffCoefficient: 2
            MaxAttempts: 180

  - WorkflowName: "LoanOffAppPrepay"
    ActivityParamsList:
      - ActivityName: "CreatePaymentLoanActivity"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "InitiateLoanAccountClosureAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "CheckLoanAccountClosureStatusAtLender"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200

  - WorkflowName: "LoanAutoPay"
    ActivityParamsList:
      - ActivityName: "CreatePaymentLoanActivity"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "UpdateLoanPaymentRequest"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "CreateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "UpdateLoanStepExecution"
        ScheduleToCloseTimeout: "10m"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 250
      - ActivityName: "EmitEvent"
        ScheduleToCloseTimeout: "60s"
        StartToCloseTimeout: "60s"
        RetryParams:
          RegularInterval:
            Interval: "10s"
            MaxAttempts: 30
      - ActivityName: "RefreshMirroredLmsFromVendor"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "RecordPaymentInPartnerLms"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "ExecuteRecurringPayment"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200
      - ActivityName: "PerformPaymentReconcilation"
        ScheduleToCloseTimeout: "3h"
        StartToCloseTimeout: "60s"
        RetryParams:
          ExponentialBackOff:
            BaseInterval: "10s"
            MaxInterval: "1m"
            BackoffCoefficient: 2
            MaxAttempts: 200


Notification:
  VkycSuccess:
    Title: "Yay, time to move forward ➡️"
    Desc: "Your video KYC was successful! Tap to continue your loan application"
    Icon: ""
  ManualReviewSuccess:
    Title: "You are verified on Fi ✅"
    Desc: "Our bank agents have given you the go-ahead! Tap to continue your loan application"
    Icon: ""
  VkycFailed:
    Title: "There seems to be an issue 🤔"
    Desc: " Our partner bank cannot process your loan application due to a verification issue"
    Icon: ""
  ManualReviewFailed:
    Title: "Loan application cancelled"
    Desc: "According to our partner bank, it was due to a verification issue"
    Icon: ""
  LoanAccountCreation:
    Title: "Yay, good news!"
    Desc: "Your loan account number #accNum is now ready for active use ✅"
    Icon: ""
  LoanAccountClosure:
    Title: "Congrats on wrapping up the loan 🚀"
    Desc: "Your loan account number %v is now closed"
    Icon: ""
  LoanPrePay:
    Title: "Money Received"
    Desc: "You have received %v amount in your loan account %v"
    Icon: ""
  VkycPending:
    Title: "Next steps to get your loan ⬇️"
    Desc: "Complete your vKYC to proceed with the loan application."
    Icon:
  LivenessPending:
    Title: "Complete your loan application ✅"
    Desc: "Finish your liveness check to get your loan"
    Icon: ""
  ESignPending:
    Title: "🏁 Finish line to your loan"
    Desc: "Just sign the dotted line on the agreement to get your loan. Agreement expires soon - Hurry!"
    Icon:
  DropOffComms:
    Enable: true
    BlackoutTimeStart: "00:00"
    BlackoutTimeStop: "06:00"
    # Waiting times in minutes
    VkycWaitingTimes: [ 5, 10, 20 ]
    LivenessWaitingTimes: [ 5, 10, 20 ]
    ESignWaitingTimes: [ 5, 10, 20 ]

EarlySalary:
  Si:
    ToActor: "actor-liquiloans-early-salary"
    ToPiId: "paymentinstrument-liquiloans-early-salary"
    MaximumAllowedTransactions: 90
    AdditionalAmountRs: 0
    ValidityDuration:
      Day: 0
      Month: 3
      Year: 0

# revisit this before going live
Lamf:
  IntermediateMfLinkScreenWaitingTime: 1m
  Si:
    ToActor: "actor-liquiloans-early-salary"
    ToPiId: "paymentinstrument-liquiloans-early-salary"
    # setting max txn to 100 after discussion between Vikas and Diparth
    MaximumAllowedTransactions: 100
    AdditionalAmountRs: 0
    ValidityDuration:
      Day: 0
      Month: 3
      Year: 0
  ExperimentConfig:
    ProcessingFeesConfig:
      FirstSegment:
        Enabled: true
        LoanAmountLowerLimit: 24999
        LoanAmountUpperLimit: 1_00_00_000
        IsPercentage: true
        AbsoluteProcessingFees: 0
        PercentageProcessingFees: "1.25"
        MinProcessingFees: 999
        MaxProcessingFees: 20000
      SecondSegment:
        Enabled: false
      ThirdSegment:
        Enabled: false

AcquireToLend:
  AnalyticsEvents:
    # fix TCs here (preapprovedloan/activity/liquiloans/events_test.go) if you change the event properties
    Buckets:
      # EventAcqPLDisbursedBucketA is triggered when a loan is disbursed in an acquire to lend program and amount
      # disbursed is between 25,000 and 60,000
      - EventName: "AcqPLDisbursedBucketA"
        StartAmountInPaise: 25_000_00 # 25,000 INR
        EndAmountInPaise: 60_000_00 # 60,000 INR
        # EventAcqPLDisbursedBucketB is triggered when a loan is disbursed in an acquire to lend program and amount
        # disbursed is between 60,001 and 100,000
      - EventName: "AcqPLDisbursedBucketB"
        StartAmountInPaise: 60_001_00 # 60,001 INR
        EndAmountInPaise: 100_000_00 # 100,000 INR
        # EventAcqPLDisbursedBucketC is triggered when a loan is disbursed in an acquire to lend program and amount
        # disbursed is between 100,001 and 150,000
      - EventName: "AcqPLDisbursedBucketC"
        StartAmountInPaise: 100_001_00 # 100,001 INR
        EndAmountInPaise: 150_000_00 # 150,000 INR
        # EventAcqPLDisbursedBucketD is triggered when a loan is disbursed in an acquire to lend program and amount
        # disbursed is greater than 150,000
      - EventName: "AcqPLDisbursedBucketD"
        StartAmountInPaise: 150_001_00 # 150,001 INR
        EndAmountInPaise: -1 # No upper limit

Prepay:
  UseIDFCLoanCancellationV2: true
  LenderToPrepayBlackOutConfig:
    LIQUILOANS:
      BlockDurationBeforeEmiDueDateInDays: 2
      BlockDurationAfterEmiDueDateInDays: 3
      BlockDurationBeforeEmiGraceEndDateInDays: 3
      BlockDurationAfterEmiGraceEndDateInDays: 1
    STOCK_GUARDIAN_LSP:
      BlockDurationBeforeEmiDueDateInDays: 0
      BlockDurationAfterEmiDueDateInDays: 1
      BlockDurationBeforeEmiGraceEndDateInDays: 0
      BlockDurationAfterEmiGraceEndDateInDays: 1
  LenderToPreClosureBlackOutConfig:
    FIFTYFIN:
      BlockStartHour: 17
      BlockEndHour: 10

DeeplinkConfig:
  IsInitiateMandateEnrichmentEnabled: false
  IsAlternateAccountFlowEnabled: false
  IsAlternateAccountFlowEnabledForLL: false
  # this config is duplicated in frontend service as well, make sure to change the value at both places whenever needed.
  IsLoanDetailsSelectionV2FlowEnabled: false
  OpenAbflMandateUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternal: true
  OpenAbflDigilockerUrlViaExternalForIos: false
  OpenAbflMandateUrlViaExternalForIos: false
  ChangeButtonTextDetailsPage: false
  # this config is extended version of IsLoanDetailsSelectionV2FlowEnabled, make sure to change the value at both places whenever needed.
  LoanDetailsSelectionV2Flow:
    IsEnabled: true
    EnableLoanPrograms:
      - IDFC
      - FEDERAL_BANK
      - LOAN_PROGRAM_PRE_APPROVED_LOAN
      - LOAN_PROGRAM_FLDG
      - LOAN_PROGRAM_STPL
      - LOAN_PROGRAM_ACQ_TO_LEND
    DefaultAmountPercentage:
      - "LIQUILOANS": 0.95
      - "IDFC": 1
      - "FEDERAL": 1
  OfferDetailsV3Config:
    IsEnabled: true
    AppVersionConstraintConfig:
      MinAndroidVersion: 359
      MinIOSVersion: 506
    VendorLoanProgramMap:
      - "LIQUILOANS:LOAN_PROGRAM_FLDG": true
      - "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "LIQUILOANS:LOAN_PROGRAM_STPL": true
      - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN": true
      - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
      - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION": true
  AbflReferencesAppVersionConstraintConfig:
    MinAndroidVersion: 363
    MinIOSVersion: 2317
  FedKfsExitUrl: "https://fi.money/loans-callback"

QuestSdk:
  Disable: true
CreditReportConfig:
  UseCreditReportV2: true

IdfcPlFlowConfig:
  IsVkycEnabled: false

OfferDeactivationConfig:
  "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_FLDG":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CKYC:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE"
  "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "FIFTYFIN:LOAN_PROGRAM_LAMF":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED"
  "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL"

SecondLookNextOfferConfig:
  "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_REAL_TIME_OFFER_NOT_FOUND"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_OFFER_CHANGED"
  "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_FLDG":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_LIVE_LOAN"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_BAD_PERFORMANCE"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_PREVIOUSLY_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_GATING_NORMS"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED_FLAGGED_ACCOUNT"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CKYC:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_CKYC_REJECTED_PIN_CODE_UNSERVICEABLE"
  "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
      - "LOAN_STEP_EXECUTION_STEP_NAME_FETCH_OFFER:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_DUPLICATE_LEAD_FAILURE"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_AGE_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_CREATE_LEAD:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "FIFTYFIN:LOAN_PROGRAM_LAMF":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_PHONE_ALREADY_USED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_USER_REGISTRATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_EMAIL_ALREADY_USED"
  "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_USER_INCOME_CRITERIA_NOT_SATISFIED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_BRE_REJECTED"
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_UNSPECIFIED"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_VENDOR_BRE:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"
  "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_PROCESS_VENDOR_PWA_STAGES:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_LOAN_VERIFICATION_UNSUCCESSFUL"
  "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
    LoanSteps:
      - "LOAN_STEP_EXECUTION_STEP_NAME_OFFER_CREATION:LOAN_STEP_EXECUTION_STATUS_FAILED:LOAN_STEP_EXECUTION_SUB_STATUS_VENDOR_BRE_REJECTION"

PrePaymentNudgeIdsToExit:
  - "LIQUILOANS:LOAN_PROGRAM_FLDG":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_STPL":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"
  - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      - "7776d321-a2a2-40e7-9c7f-0b28c99f1c7c"
      - "a8553277-f23a-4a73-b843-04b4efb695f2"

Lms:
  EmiPaymentNudgeIdsToExit:
    - "LIQUILOANS:LOAN_PROGRAM_FLDG":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_EARLY_SALARY":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"
    - "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
        - "3cd97988-213b-4082-aaa1-ca6167dbb606"

CibilCreditReportFetchVersions:
  MinIosVersion: 100
  MinAndroidVersion: 100

Tracing:
  Enable: true

FeatureReleaseConfig:
  FeatureConstraints:
    LOANS_IDFC_VKYC_V2:
      AppVersionConstraintConfig:
        MinAndroidVersion: 328
        MinIOSVersion: 473
      StickyPercentageConstraintConfig:
        RolloutPercentageByPlatform:
          RolloutPercentageIOS: 0
          RolloutPercentageAndroid: 0
      UserGroupConstraintConfig:
        AllowedGroups:
          - 1 # INTERNAL
    FEATURE_LOANS_SECOND_LOOK_V1:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
    FEATURE_LOANS_ENABLE_EXPERIAN_PULL:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      UserGroupConstraintConfig:
        AllowedGroups:
    FEATURE_LOANS_PREQUAL_OFFER_FLOW:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
    FEATURE_LOANS_INCOME_ESTIMATION_IN_SYNC:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 100
        MinIOSVersion: 100
    FEATURE_LOANS_AUTO_PAY_FLOW:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0
    FEATURE_LDC_APPLICATION_MOVEMENT:
      StickyPercentageConstraintConfig:
        RolloutPercentage: 100
      AppVersionConstraintConfig:
        MinAndroidVersion: 0
        MinIOSVersion: 0

Collections:
  Allocation:
    IsTotalClaimAmtNewDefEnabled: false
  IsContactDetailsUpdateEnabledInSyncLeadWorkflow: false

LoanStageParams:
  Mandate:
    IsFiAccOperationalStatusCheckEnabled: false

MandateConfig:
  LiquiloansMandateConfig:
    IsMandateCoolOffCheckEnabled: true
    MinCoolOffMinutesBetweenMandateAttempts: 8
    IsMandateRequestBasedCountLogicEnabled: true
    IsPreviousMandateStatusCheckEnabled: true

Flags:
  HideIdfcOffer: false
  IsAbflKfsGenerationV2: true
  IsSgDigilockerEnabled: true
  IsLdcApplicationMovementEnabled: false
  PreferUPIMandateTypeForLDC: false
  MoveMoneyViewFinalBreCallToBlackBox: false
  TransferExperianTraficToCibil: false

# if the loan amount is between MinLoanAmount and MaxLoanAmount, tenure should be between MinTenure and MaxTenure or vice versa
LoanCalculator:
  LoanSelectionConstraint:
    "IDFC":
      - MinTenure: 6
        MaxTenure: 24
        MinLoanAmount: 50000
        MaxLoanAmount: 100000
      - MinTenure: 12
        MaxTenure: 48
        MinLoanAmount: 100001
        MaxLoanAmount: 200000
      - MinTenure: 18
        MaxTenure: 60
        MinLoanAmount: 200001
        MaxLoanAmount: 500000

VendorProgramLevelFeature:
  VendorProgramActiveMap:
    "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "IDFC:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FLDG":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_SUBVENTION":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REALTIME_STPL":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_STPL":
      IsAllowed: true
    "MONEYVIEW:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_PRE_APPROVED_LOAN":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "FEDERAL:LOAN_PROGRAM_FED_REAL_TIME":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_FI_LITE_PL":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_ACQ_TO_LEND":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_SUBVENTION":
      IsAllowed: true
    "LIQUILOANS:LOAN_PROGRAM_NON_FI_CORE_STPL":
      IsAllowed: true
    "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
    "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
      IsAllowed: true
  NonFiCoreVendorProgramActiveMap:
    - "STOCK_GUARDIAN_LSP:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "ABFL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "FEDERAL:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB":
        IsAllowed: true
    - "MONEYVIEW:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true
    - "LENDEN:LOAN_PROGRAM_REAL_TIME_DISTRIBUTION":
        IsAllowed: true

SkipEpfoCheck:
  - "EPIFI_TECH:LOAN_PROGRAM_ELIGIBILITY": true

SgEtbNewEligibilityFlow:
  IsAllowed: true

EligibilityNuggetBotFeature:
  DisableFeature: false
  MinIOSVersion: 1
  MinAndroidVersion: 1
  FallbackToEnableFeature: true

UseLdcSubAnalysisForIncome: true
