package billpay

import (
	"github.com/epifi/be-common/tools/servergen/meta"
	billpaypb "github.com/epifi/gamma/api/billpay"
	billpayDeveloper "github.com/epifi/gamma/api/billpay/developer"

	"github.com/epifi/gamma/billpay/wire"
)

var serviceWireInitializers = []*meta.ServiceInitializer{
	{
		WireInitializer:     wire.InitializeBillpayDevService,
		GRPCRegisterMethods: []any{billpayDeveloper.RegisterDevServer},
	},
	{
		WireInitializer:     wire.InitializeService,
		GRPCRegisterMethods: []any{billpaypb.RegisterBillPayServer},
	},
}
