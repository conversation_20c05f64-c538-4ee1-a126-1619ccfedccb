// code generated by tools/servergen
package server

import (
	context "context"
	"fmt"
	http "net/http"
	strings "strings"
	time "time"

	awssdk "github.com/aws/aws-sdk-go-v2/aws"
	awssqs "github.com/aws/aws-sdk-go-v2/service/sqs"
	prometheus "github.com/prometheus/client_golang/prometheus"
	redis "github.com/redis/go-redis/v9"
	analytics "github.com/rudderlabs/analytics-go"
	zap "go.uber.org/zap"
	grpc "google.golang.org/grpc"
	metadata "google.golang.org/grpc/metadata"
	reflection "google.golang.org/grpc/reflection"
	gorm "gorm.io/gorm"

	celestialpb "github.com/epifi/be-common/api/celestial"
	devcache "github.com/epifi/be-common/api/developer/devcache"
	"github.com/epifi/be-common/pkg/async/goroutine"
	awsconfpkg "github.com/epifi/be-common/pkg/aws/v2/config"
	s3pkg "github.com/epifi/be-common/pkg/aws/v2/s3"
	sns "github.com/epifi/be-common/pkg/aws/v2/sns"
	sqs "github.com/epifi/be-common/pkg/aws/v2/sqs"
	cache "github.com/epifi/be-common/pkg/cache"
	cfg "github.com/epifi/be-common/pkg/cfg"
	consul "github.com/epifi/be-common/pkg/cfg/consul"
	dynconf "github.com/epifi/be-common/pkg/cfg/dynconf"
	commonexplorer "github.com/epifi/be-common/pkg/cfg/explorer"
	config "github.com/epifi/be-common/pkg/cmd/config"
	genconf "github.com/epifi/be-common/pkg/cmd/config/genconf"
	types "github.com/epifi/be-common/pkg/cmd/types"
	epificontext "github.com/epifi/be-common/pkg/epificontext"
	epifigrpc "github.com/epifi/be-common/pkg/epifigrpc"
	commoninterceptors "github.com/epifi/be-common/pkg/epifigrpc/interceptors"
	resolver "github.com/epifi/be-common/pkg/epifigrpc/resolver"
	epifiserver "github.com/epifi/be-common/pkg/epifiserver"
	temporalpkg "github.com/epifi/be-common/pkg/epifitemporal"
	errgroup "github.com/epifi/be-common/pkg/errgroup"
	events "github.com/epifi/be-common/pkg/events"
	logger "github.com/epifi/be-common/pkg/logger"
	profiling "github.com/epifi/be-common/pkg/profiling"
	queue "github.com/epifi/be-common/pkg/queue"
	ratelimiter "github.com/epifi/be-common/pkg/ratelimiter"
	store "github.com/epifi/be-common/pkg/ratelimiter/store"
	storage "github.com/epifi/be-common/pkg/storage"
	storage2 "github.com/epifi/be-common/pkg/storage/v2"
	storagev2analytics "github.com/epifi/be-common/pkg/storage/v2/analytics"
	otel "github.com/epifi/be-common/pkg/tracing/opentelemetry"
	acquisitionconf "github.com/epifi/gamma/acquisition/config"
	genconf3 "github.com/epifi/gamma/acquisition/config/genconf"
	wire2 "github.com/epifi/gamma/acquisition/wire"
	types2 "github.com/epifi/gamma/acquisition/wire/types"
	alfredconf "github.com/epifi/gamma/alfred/config"
	genconf2 "github.com/epifi/gamma/alfred/config/genconf"
	wire "github.com/epifi/gamma/alfred/wire"
	accountbalancepb "github.com/epifi/gamma/api/accounts/balance"
	operationalstatuspb "github.com/epifi/gamma/api/accounts/operstatus"
	acquisitioncrossattachpb "github.com/epifi/gamma/api/acquisition/crossattach"
	actor "github.com/epifi/gamma/api/actor"
	alfredpb "github.com/epifi/gamma/api/alfred"
	alfreddeveloper "github.com/epifi/gamma/api/alfred/developer"
	authpb "github.com/epifi/gamma/api/auth"
	authorizerpb "github.com/epifi/gamma/api/auth/authorizer"
	biometricspb "github.com/epifi/gamma/api/auth/biometrics"
	authconsumerpb "github.com/epifi/gamma/api/auth/consumer"
	authdevpb "github.com/epifi/gamma/api/auth/developer"
	liveness "github.com/epifi/gamma/api/auth/liveness"
	developer18 "github.com/epifi/gamma/api/auth/liveness/developer"
	locationpb "github.com/epifi/gamma/api/auth/location"
	authv2pb "github.com/epifi/gamma/api/auth/orchestrator"
	authorchdevpb "github.com/epifi/gamma/api/auth/orchestrator/developer"
	authpartnersdkpb "github.com/epifi/gamma/api/auth/partnersdk"
	sessionpb "github.com/epifi/gamma/api/auth/session"
	totp2 "github.com/epifi/gamma/api/auth/totp"
	bankcust "github.com/epifi/gamma/api/bankcust"
	compliance2 "github.com/epifi/gamma/api/bankcust/compliance"
	consumer2 "github.com/epifi/gamma/api/bankcust/compliance/consumer"
	bankcustdeveloper "github.com/epifi/gamma/api/bankcust/developer"
	cardpb "github.com/epifi/gamma/api/card/provisioning"
	casbinpb "github.com/epifi/gamma/api/casbin"
	categorizer "github.com/epifi/gamma/api/categorizer"
	comms "github.com/epifi/gamma/api/comms"
	devicetoken "github.com/epifi/gamma/api/comms/device_token"
	tcpb "github.com/epifi/gamma/api/comms/inapptargetedcomms"
	uppb "github.com/epifi/gamma/api/comms/user_preference"
	connectedaccountpb "github.com/epifi/gamma/api/connected_account"
	consentpb "github.com/epifi/gamma/api/consent"
	creditreportv "github.com/epifi/gamma/api/creditreportv2"
	derivedattributespb "github.com/epifi/gamma/api/creditreportv2/derivedattributes"
	cxticketpb "github.com/epifi/gamma/api/cx/ticket"
	watson "github.com/epifi/gamma/api/cx/watson"
	depositpb "github.com/epifi/gamma/api/deposit"
	employment "github.com/epifi/gamma/api/employment"
	empconsumerpb "github.com/epifi/gamma/api/employment/consumer"
	employmentdeveloper "github.com/epifi/gamma/api/employment/developer"
	ffpb "github.com/epifi/gamma/api/firefly"
	ffaccpb "github.com/epifi/gamma/api/firefly/accounting"
	ffv2pb "github.com/epifi/gamma/api/firefly/v2"
	healthengine "github.com/epifi/gamma/api/health_engine"
	inappreferralpb "github.com/epifi/gamma/api/inappreferral"
	inapprefdatacollectorpb "github.com/epifi/gamma/api/inappreferral/datacollector"
	inappreferraldev "github.com/epifi/gamma/api/inappreferral/developer"
	inapprefeligibleevalpb "github.com/epifi/gamma/api/inappreferral/eligibilityevaluator"
	inappreferralnotifpb "github.com/epifi/gamma/api/inappreferral/notification"
	season2 "github.com/epifi/gamma/api/inappreferral/season"
	accessinfo "github.com/epifi/gamma/api/insights/accessinfo"
	emailparserpb "github.com/epifi/gamma/api/insights/emailparser"
	networthpb "github.com/epifi/gamma/api/insights/networth"
	mfpb "github.com/epifi/gamma/api/investment/mutualfund/external"
	statementpb "github.com/epifi/gamma/api/investment/mutualfund/statement"
	kyc "github.com/epifi/gamma/api/kyc"
	agent "github.com/epifi/gamma/api/kyc/agent"
	developer11 "github.com/epifi/gamma/api/kyc/developer"
	kycdocspb "github.com/epifi/gamma/api/kyc/docs"
	kycuqudopb "github.com/epifi/gamma/api/kyc/uqudo"
	vkycpb "github.com/epifi/gamma/api/kyc/vkyc"
	notification3 "github.com/epifi/gamma/api/kyc/vkyc/notification"
	leadspb "github.com/epifi/gamma/api/leads"
	merchant "github.com/epifi/gamma/api/merchant"
	nudge "github.com/epifi/gamma/api/nudge"
	omegle "github.com/epifi/gamma/api/omegle"
	omegleconsumerpb "github.com/epifi/gamma/api/omegle/consumer"
	omegledeveloperpb "github.com/epifi/gamma/api/omegle/developer"
	matcher "github.com/epifi/gamma/api/omegle/matcher"
	ocr2 "github.com/epifi/gamma/api/omegle/ocr"
	orderpb "github.com/epifi/gamma/api/order"
	paymentpb "github.com/epifi/gamma/api/order/payment"
	pan "github.com/epifi/gamma/api/pan"
	consumer7 "github.com/epifi/gamma/api/pan/consumer"
	developer13 "github.com/epifi/gamma/api/pan/developer"
	panconsumer "github.com/epifi/gamma/api/pan/epan/consumer"
	paypb "github.com/epifi/gamma/api/pay"
	pipb "github.com/epifi/gamma/api/paymentinstrument"
	explorerpb "github.com/epifi/gamma/api/pkg/cfg/explorer"
	preapprovedloanpb "github.com/epifi/gamma/api/preapprovedloan"
	lendabilitypb "github.com/epifi/gamma/api/preapprovedloan/lendability"
	productpb "github.com/epifi/gamma/api/product"
	managerpb "github.com/epifi/gamma/api/quest/manager"
	referral2 "github.com/epifi/gamma/api/referral"
	rewards "github.com/epifi/gamma/api/rewards"
	riskpb "github.com/epifi/gamma/api/risk"
	casemanagement "github.com/epifi/gamma/api/risk/case_management"
	riskdeveloper "github.com/epifi/gamma/api/risk/developer"
	leapb "github.com/epifi/gamma/api/risk/lea"
	mnrl "github.com/epifi/gamma/api/risk/mnrl"
	profilepb "github.com/epifi/gamma/api/risk/profile"
	redlist "github.com/epifi/gamma/api/risk/redlist"
	transactionmonitoring "github.com/epifi/gamma/api/risk/transaction_monitoring"
	txnmonitoringdronapaypb "github.com/epifi/gamma/api/risk/transaction_monitoring/dronapay"
	whitelistpb "github.com/epifi/gamma/api/risk/whitelist"
	salaryprogram "github.com/epifi/gamma/api/salaryprogram"
	savingsclientpb "github.com/epifi/gamma/api/savings"
	screener2 "github.com/epifi/gamma/api/screener"
	screenerdeveloper "github.com/epifi/gamma/api/screener/developer"
	search "github.com/epifi/gamma/api/search"
	segmentpb "github.com/epifi/gamma/api/segment"
	shipment2 "github.com/epifi/gamma/api/shipment"
	simauthfederal "github.com/epifi/gamma/api/simulator/openbanking/auth/federal"
	tiering "github.com/epifi/gamma/api/tiering"
	tspuserpb "github.com/epifi/gamma/api/tspuser"
	tspclient "github.com/epifi/gamma/api/tspuser/developer"
	_ "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/screen_option_dependency"
	upipb "github.com/epifi/gamma/api/upi"
	upionbpb "github.com/epifi/gamma/api/upi/onboarding"
	user "github.com/epifi/gamma/api/user"
	accessrevokeconsumerpb "github.com/epifi/gamma/api/user/accessrevoke/consumer"
	usercontactpb "github.com/epifi/gamma/api/user/contact"
	developer9 "github.com/epifi/gamma/api/user/developer"
	event2 "github.com/epifi/gamma/api/user/event"
	usergrouppb "github.com/epifi/gamma/api/user/group"
	userlocation "github.com/epifi/gamma/api/user/location"
	obfuscatorpb "github.com/epifi/gamma/api/user/obfuscator"
	onbpb "github.com/epifi/gamma/api/user/onboarding"
	vkyc "github.com/epifi/gamma/api/user/onboarding/vkyc"
	onboardingwatsonclientpb "github.com/epifi/gamma/api/user/onboarding/watson"
	vkycconsumer "github.com/epifi/gamma/api/user/vkyc_consumer"
	useractions2 "github.com/epifi/gamma/api/useractions"
	userintelpb "github.com/epifi/gamma/api/userintel"
	ussaccountmgpb "github.com/epifi/gamma/api/usstocks/account"
	ussportfoliomanagerpb "github.com/epifi/gamma/api/usstocks/portfolio"
	tax "github.com/epifi/gamma/api/usstocks/tax"
	ippb "github.com/epifi/gamma/api/vendordata/ip"
	seonpb "github.com/epifi/gamma/api/vendorgateway/appscreener/seon"
	ckyc "github.com/epifi/gamma/api/vendorgateway/ckyc"
	vgcrmpb "github.com/epifi/gamma/api/vendorgateway/crm"
	dl "github.com/epifi/gamma/api/vendorgateway/dl"
	vgdocpb "github.com/epifi/gamma/api/vendorgateway/docs"
	vgekycpb "github.com/epifi/gamma/api/vendorgateway/ekyc"
	vgemploymentpb "github.com/epifi/gamma/api/vendorgateway/employment"
	fennelvgpb "github.com/epifi/gamma/api/vendorgateway/fennel"
	vgidfcpb "github.com/epifi/gamma/api/vendorgateway/idfc"
	idvalidate "github.com/epifi/gamma/api/vendorgateway/idvalidate"
	incomeestimator "github.com/epifi/gamma/api/vendorgateway/incomeestimator"
	vgitr "github.com/epifi/gamma/api/vendorgateway/itr"
	vguqudopb "github.com/epifi/gamma/api/vendorgateway/kyc/uqudo"
	vgidfcvkycpb "github.com/epifi/gamma/api/vendorgateway/kyc/vkyc/idfc"
	vglivenesspb "github.com/epifi/gamma/api/vendorgateway/liveness"
	locationvgpb "github.com/epifi/gamma/api/vendorgateway/location"
	vgmoengagepb "github.com/epifi/gamma/api/vendorgateway/moengage"
	vgncpb "github.com/epifi/gamma/api/vendorgateway/namecheck"
	ocr "github.com/epifi/gamma/api/vendorgateway/ocr"
	vgauth "github.com/epifi/gamma/api/vendorgateway/openbanking/auth"
	vgpartnersdkpb "github.com/epifi/gamma/api/vendorgateway/openbanking/auth/partnersdk"
	vgbcpb "github.com/epifi/gamma/api/vendorgateway/openbanking/bank_customer"
	customer "github.com/epifi/gamma/api/vendorgateway/openbanking/customer"
	deposit "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit"
	vgpanpb "github.com/epifi/gamma/api/vendorgateway/pan"
	phonenetwork "github.com/epifi/gamma/api/vendorgateway/phonenetwork"
	profilevalidationpb "github.com/epifi/gamma/api/vendorgateway/profilevalidation"
	vgriskpb "github.com/epifi/gamma/api/vendorgateway/risk"
	vgscienapticpb "github.com/epifi/gamma/api/vendorgateway/scienaptic"
	vgtxnmonitoringpb "github.com/epifi/gamma/api/vendorgateway/transactionmonitoring/dronapay"
	userseg "github.com/epifi/gamma/api/vendorgateway/userseg"
	pb "github.com/epifi/gamma/api/vendorgateway/vkyc"
	vkycvgpb "github.com/epifi/gamma/api/vendorgateway/vkyccall"
	inhouseocr "github.com/epifi/gamma/api/vendorgateway/wealth/inhouseocr"
	nsdlvgpb "github.com/epifi/gamma/api/vendorgateway/wealth/nsdl"
	vmpb "github.com/epifi/gamma/api/vendormapping"
	vkyccallpb "github.com/epifi/gamma/api/vkyccall"
	vkyccalldeveloperpb "github.com/epifi/gamma/api/vkyccall/developer"
	vkyccalltroubleshootpb "github.com/epifi/gamma/api/vkyccall/troubleshoot"
	wealthobpb "github.com/epifi/gamma/api/wealthonboarding"
	wire5 "github.com/epifi/gamma/auth/biometrics/wire"
	authconf "github.com/epifi/gamma/auth/config"
	genconf4 "github.com/epifi/gamma/auth/config/genconf"
	wire4 "github.com/epifi/gamma/auth/orchestrator/wire"
	wire3 "github.com/epifi/gamma/auth/wire"
	wiretypes "github.com/epifi/gamma/auth/wire/types"
	bankcustconf "github.com/epifi/gamma/bankcust/config"
	genconf5 "github.com/epifi/gamma/bankcust/config/genconf"
	wire6 "github.com/epifi/gamma/bankcust/wire"
	hook "github.com/epifi/gamma/cmd/servers/uat/onboarding/hook"
	types3 "github.com/epifi/gamma/comms/wire/types"
	wire14 "github.com/epifi/gamma/consent/wire"
	employmentconf "github.com/epifi/gamma/employment/config"
	genconf6 "github.com/epifi/gamma/employment/config/genconf"
	wire7 "github.com/epifi/gamma/employment/wire"
	wiretypes4 "github.com/epifi/gamma/employment/wire/types"
	inappreferralconf "github.com/epifi/gamma/inappreferral/config"
	genconf7 "github.com/epifi/gamma/inappreferral/config/genconf"
	wire8 "github.com/epifi/gamma/inappreferral/wire"
	inappreferraltypes "github.com/epifi/gamma/inappreferral/wire/types"
	kycconf "github.com/epifi/gamma/kyc/config"
	genconf12 "github.com/epifi/gamma/kyc/config/genconf"
	wire17 "github.com/epifi/gamma/kyc/wire"
	wiretypes7 "github.com/epifi/gamma/kyc/wire/types"
	livenessconf "github.com/epifi/gamma/liveness/config"
	genconf17 "github.com/epifi/gamma/liveness/config/genconf"
	wire24 "github.com/epifi/gamma/liveness/wire"
	omegleconf "github.com/epifi/gamma/omegle/config"
	genconf16 "github.com/epifi/gamma/omegle/config/genconf"
	wire22 "github.com/epifi/gamma/omegle/wire"
	types6 "github.com/epifi/gamma/omegle/wire/types"
	panconf "github.com/epifi/gamma/pan/config"
	genconf13 "github.com/epifi/gamma/pan/config/genconf"
	wire18 "github.com/epifi/gamma/pan/wire"
	types5 "github.com/epifi/gamma/pan/wire/types"
	explorer "github.com/epifi/gamma/pkg/cfg/explorer"
	customdelayqueue "github.com/epifi/gamma/pkg/customdelayqueue"
	customqueuewire "github.com/epifi/gamma/pkg/customdelayqueue/wire"
	questinterceptor "github.com/epifi/gamma/pkg/epifigrpc/interceptors/quest"
	servergenwire "github.com/epifi/gamma/pkg/epifigrpc/interceptors/servergen_wire"
	fepkg "github.com/epifi/gamma/pkg/frontend/msg"
	productconf "github.com/epifi/gamma/product/config"
	genconf8 "github.com/epifi/gamma/product/config/genconf"
	wire10 "github.com/epifi/gamma/product/wire"
	questsdkinit "github.com/epifi/gamma/quest/sdk/init"
	referralconf "github.com/epifi/gamma/referral/config"
	wire9 "github.com/epifi/gamma/referral/wire"
	riskconf "github.com/epifi/gamma/risk/config"
	genconf15 "github.com/epifi/gamma/risk/config/genconf"
	wire21 "github.com/epifi/gamma/risk/wire"
	screenerconf "github.com/epifi/gamma/screener/config"
	genconf9 "github.com/epifi/gamma/screener/config/genconf"
	wire11 "github.com/epifi/gamma/screener/wire"
	shipmentconf "github.com/epifi/gamma/shipment/config"
	genconf14 "github.com/epifi/gamma/shipment/config/genconf"
	wire20 "github.com/epifi/gamma/shipment/wire"
	tspuserconf "github.com/epifi/gamma/tspuser/config"
	wire23 "github.com/epifi/gamma/tspuser/wire"
	types7 "github.com/epifi/gamma/tspuser/wire/types"
	userconf "github.com/epifi/gamma/user/config"
	genconf10 "github.com/epifi/gamma/user/config/genconf"
	wire13 "github.com/epifi/gamma/user/onboarding/wire"
	wire12 "github.com/epifi/gamma/user/wire"
	wiretypes6 "github.com/epifi/gamma/user/wire/types"
	useractionsconf "github.com/epifi/gamma/useractions/config"
	wire15 "github.com/epifi/gamma/useractions/wire"
	userintelconf "github.com/epifi/gamma/userintel/config"
	genconf11 "github.com/epifi/gamma/userintel/config/genconf"
	wire16 "github.com/epifi/gamma/userintel/wire"
	vendordataconf "github.com/epifi/gamma/vendordata/config"
	wire19 "github.com/epifi/gamma/vendordata/ip/wire"
	vkyccallconf "github.com/epifi/gamma/vkyccall/config"
	genconf18 "github.com/epifi/gamma/vkyccall/config/genconf"
	wire25 "github.com/epifi/gamma/vkyccall/wire"
	types8 "github.com/epifi/gamma/vkyccall/wire/types"
)

// nolint: funlen
func Run(ctx context.Context, initNotifier chan<- cfg.ServerName) error {
	// Panic handler for logging panics in standard format
	epifiserver.HandlePanic()
	resolver.RegisterBuilder(resolver.ConsulSchemeName)
	var configNameToConfMap = make(commonexplorer.CfgExplorerMap)

	err := initLogger()
	if err != nil {
		// cannot log error since logger maynot be initialized yet.
		return err
	}
	ctx = epificontext.WithTraceId(ctx, metadata.MD{})
	// Load static configuration
	conf, err := config.Load(cfg.ONBOARDING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load static conf", zap.Error(err))
		return err
	}
	_ = conf
	// Load generated dynamic configuration
	gconf, err := genconf.Load(cfg.ONBOARDING_SERVER)
	if err != nil {
		logger.Error(ctx, "failed to load dynamic conf", zap.Error(err))
		return err
	}
	logger.Info(ctx, "initiating server")

	if gconf.Profiling().AutomaticProfiling().EnableAutoProfiling() {
		profilingErr := profiling.AutomaticProfiling(context.Background(), cfg.ONBOARDING_SERVER, gconf.Profiling(), gconf.Environment(), gconf.AWS().Region)
		if profilingErr != nil {
			logger.Error(ctx, "failed to start automatic profiling", zap.Error(profilingErr))
			return profilingErr
		}
	}

	if gconf.Tracing().Enable {
		_, shutdown, tracingErr := otel.ConfigureOpenTelemetryV2(context.Background(), cfg.ONBOARDING_SERVER, gconf.Environment(), gconf.Profiling().PyroscopeProfiling().EnablePyroscope())
		if tracingErr != nil {
			logger.Error(ctx, "failed to configure open telemetry", zap.Error(tracingErr))
			return err
		}
		defer shutdown()
	}

	err = profiling.PyroscopeProfilingV2(gconf.Name(), gconf.Profiling(), gconf.Environment())
	if err != nil {
		logger.Error(ctx, "error starting pyroscope profiler", zap.Error(err))
	}

	if !cfg.IsLocalEnv(gconf.Environment()) && !cfg.IsRemoteDebugEnabled() && !cfg.IsTestTenantEnabled() && !cfg.IsTeamSpaceTenant() {
		initDisc, _ := consul.GetBool(consul.GetInitDiscoveryKey(gconf.Environment(), string(gconf.Name())))
		queue.SetWorkerInitialization(initDisc)
	}

	rateLimiterRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()[gconf.RedisRateLimiterName()], gconf.Tracing().Enable)
	defer func() { _ = rateLimiterRedisStore.Close() }()

	var (
		crdbResourceMap    *storage2.DBResourceProvider[*gorm.DB]
		crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor]
	)

	var dbConnTeardown func()
	crdbResourceMap, crdbTxnResourceMap, dbConnTeardown, err = storage2.NewDBResourceProviderV2(conf.DBConfigMap.GetOwnershipToDbConfigMap(), conf.Tracing.Enable, gconf.PgdbConns())
	if err != nil {
		logger.Error(ctx, "failed to get db resource provider", zap.Error(err))
		return err
	}
	defer func() {
		dbConnTeardown()
	}()

	epifiCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["EpifiCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	epifiCRDBSqlDb, err := epifiCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "EpifiCRDB"))
		return err
	}
	defer func() { _ = epifiCRDBSqlDb.Close() }()
	fRMCRDB, err := storage2.NewCRDBWithConfig(gconf.Databases()["FRMCRDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FRMCRDB"))
		return err
	}
	fRMCRDBSqlDb, err := fRMCRDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FRMCRDB"))
		return err
	}
	defer func() { _ = fRMCRDBSqlDb.Close() }()

	authPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["AuthPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "AuthPGDB"))
		return err
	}
	authPGDBSqlDb, err := authPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "AuthPGDB"))
		return err
	}
	defer func() { _ = authPGDBSqlDb.Close() }()
	bankCustomerPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["BankCustomerPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "BankCustomerPGDB"))
		return err
	}
	bankCustomerPGDBSqlDb, err := bankCustomerPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "BankCustomerPGDB"))
		return err
	}
	defer func() { _ = bankCustomerPGDBSqlDb.Close() }()
	userPropertiesPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["UserPropertiesPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "UserPropertiesPGDB"))
		return err
	}
	userPropertiesPGDBSqlDb, err := userPropertiesPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "UserPropertiesPGDB"))
		return err
	}
	defer func() { _ = userPropertiesPGDBSqlDb.Close() }()
	nudgePGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["NudgePGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	nudgePGDBSqlDb, err := nudgePGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "NudgePGDB"))
		return err
	}
	defer func() { _ = nudgePGDBSqlDb.Close() }()
	kycPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["KycPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "KycPGDB"))
		return err
	}
	kycPGDBSqlDb, err := kycPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "KycPGDB"))
		return err
	}
	defer func() { _ = kycPGDBSqlDb.Close() }()
	kycNonResidentPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["KycNonResidentPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "KycNonResidentPGDB"))
		return err
	}
	kycNonResidentPGDBSqlDb, err := kycNonResidentPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "KycNonResidentPGDB"))
		return err
	}
	defer func() { _ = kycNonResidentPGDBSqlDb.Close() }()
	vendordataPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["VendordataPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "VendordataPGDB"))
		return err
	}
	vendordataPGDBSqlDb, err := vendordataPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "VendordataPGDB"))
		return err
	}
	defer func() { _ = vendordataPGDBSqlDb.Close() }()
	fRMPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["FRMPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "FRMPGDB"))
		return err
	}
	fRMPGDBSqlDb, err := fRMPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "FRMPGDB"))
		return err
	}
	defer func() { _ = fRMPGDBSqlDb.Close() }()
	verifiPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["VerifiPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "VerifiPGDB"))
		return err
	}
	verifiPGDBSqlDb, err := verifiPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "VerifiPGDB"))
		return err
	}
	defer func() { _ = verifiPGDBSqlDb.Close() }()
	stockGuardianTspPGDB, err := storage2.NewPostgresDBWithConfig(gconf.Databases()["StockGuardianTspPGDB"], gconf.Tracing().Enable)
	if err != nil {
		logger.Error(ctx, "failed to establish database conn", zap.Error(err), zap.Any("db_config", "StockGuardianTspPGDB"))
		return err
	}
	stockGuardianTspPGDBSqlDb, err := stockGuardianTspPGDB.DB()
	if err != nil {
		logger.Error(ctx, "failed to get sql database", zap.Error(err), zap.Any("db_config", "StockGuardianTspPGDB"))
		return err
	}
	defer func() { _ = stockGuardianTspPGDBSqlDb.Close() }()

	onboardingConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.ONBOARDING_SERVER)
	defer epifigrpc.CloseConn(onboardingConn)
	dronapayCallbackClient := txnmonitoringdronapaypb.NewDronapayCallbackClient(onboardingConn)
	universalConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.UNIVERSAL_SERVER)
	defer epifigrpc.CloseConn(universalConn)
	managerClient := managerpb.NewManagerClient(universalConn)
	groupClient := usergrouppb.NewGroupClient(onboardingConn)
	usersClient := user.NewUsersClient(onboardingConn)
	payConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.PAY_SERVER)
	defer epifigrpc.CloseConn(payConn)
	actorClient := actor.NewActorClient(payConn)
	segmentationServiceClient := segmentpb.NewSegmentationServiceClient(universalConn)
	bankCustomerServiceClient := bankcust.NewBankCustomerServiceClient(onboardingConn)
	celestialClient := celestialpb.NewCelestialClient(universalConn)
	savingsClient := savingsclientpb.NewSavingsClient(universalConn)
	vKYCClient := vkycpb.NewVKYCClient(onboardingConn)
	statementServiceClient := statementpb.NewStatementServiceClient(universalConn)
	wealthOnboardingClient := wealthobpb.NewWealthOnboardingClient(universalConn)
	tieringClient := tiering.NewTieringClient(universalConn)
	balanceClient := accountbalancepb.NewBalanceClient(payConn)
	consentClient := consentpb.NewConsentClient(onboardingConn)
	kycClient := kyc.NewKycClient(onboardingConn)
	livenessClient := liveness.NewLivenessClient(onboardingConn)
	vendorgatewayConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.VENDOR_GATEWAY_SERVER)
	defer epifigrpc.CloseConn(vendorgatewayConn)
	uNNameCheckClient := vgncpb.NewUNNameCheckClient(vendorgatewayConn)
	accountManagerClient := ussaccountmgpb.NewAccountManagerClient(universalConn)
	ussTaxServiceClient := tax.NewUssTaxServiceClient(universalConn)
	alfredClient := alfredpb.NewAlfredClient(onboardingConn)
	crossAttachUserAttributesRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["CrossAttachUserAttributesRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = crossAttachUserAttributesRedisStore.Close() }()
	crossAttachUserAttributesCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(crossAttachUserAttributesRedisStore), gconf.RedisClusters()["CrossAttachUserAttributesRedisStore"].HystrixCommand)
	onboardingClient := onbpb.NewOnboardingClient(onboardingConn)
	lendingConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.LENDING_SERVER)
	defer epifigrpc.CloseConn(lendingConn)
	lendabilityClient := lendabilitypb.NewLendabilityClient(lendingConn)
	netWorthClient := networthpb.NewNetWorthClient(universalConn)
	userIntelServiceClient := userintelpb.NewUserIntelServiceClient(onboardingConn)
	connectedAccountClient := connectedaccountpb.NewConnectedAccountClient(universalConn)
	universalConnVar18ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptor := servergenwire.NewAuthRequestClientInterceptor()
	if unaryClientInterceptor != nil {
		universalConnVar18ClientInterceptors = append(universalConnVar18ClientInterceptors, unaryClientInterceptor)
	}
	universalConnVar18 := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar18ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar18)
	commsClient := comms.NewCommsClient(universalConnVar18)
	vendorAuthClient := vgauth.NewVendorAuthClient(vendorgatewayConn)
	cardProvisioningClient := cardpb.NewCardProvisioningClient(payConn)
	authRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authRedisStore.Close() }()
	authTokenRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthTokenRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authTokenRedisStore.Close() }()
	authDeviceIntegrityNonceRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceIntegrityNonceRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceIntegrityNonceRedisStore.Close() }()
	authDeviceIntegrityNonceCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceIntegrityNonceRedisStore), gconf.RedisClusters()["AuthDeviceIntegrityNonceRedisStore"].HystrixCommand)
	authDeviceRegistrationsRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceRegistrationsRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceRegistrationsRedisStore.Close() }()
	authDeviceRegistrationsCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceRegistrationsRedisStore), gconf.RedisClusters()["AuthDeviceRegistrationsRedisStore"].HystrixCommand)
	redListClient := redlist.NewRedListClient(onboardingConn)
	riskClient := riskpb.NewRiskClient(onboardingConn)
	simulatorConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.SIMULATOR_GRPC_SERVER)
	defer epifigrpc.CloseConn(simulatorConn)
	authClient := simauthfederal.NewAuthClient(simulatorConn)
	healthEngineServiceClient := healthengine.NewHealthEngineServiceClient(payConn)
	caseManagementClient := casemanagement.NewCaseManagementClient(onboardingConn)
	kycAgentServiceClient := agent.NewKycAgentServiceClient(onboardingConn)
	universalConnVar20ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar2 := servergenwire.NewAuthRequestClientInterceptor()
	if unaryClientInterceptorVar2 != nil {
		universalConnVar20ClientInterceptors = append(universalConnVar20ClientInterceptors, unaryClientInterceptorVar2)
	}
	universalConnVar20 := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar20ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar20)
	fCMDeviceTokenClient := devicetoken.NewFCMDeviceTokenClient(universalConnVar20)
	operationalStatusServiceClient := operationalstatuspb.NewOperationalStatusServiceClient(payConn)
	productClient := productpb.NewProductClient(onboardingConn)
	payClient := paypb.NewPayClient(payConn)
	authClientVar2 := authpb.NewAuthClient(onboardingConn)
	partnerSDKClient := vgpartnersdkpb.NewPartnerSDKClient(vendorgatewayConn)
	accountingClient := ffaccpb.NewAccountingClient(lendingConn)
	authDeviceLocationRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["AuthDeviceLocationRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = authDeviceLocationRedisStore.Close() }()
	authDeviceLocationCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(authDeviceLocationRedisStore), gconf.RedisClusters()["AuthDeviceLocationRedisStore"].HystrixCommand)
	uPIClient := upipb.NewUPIClient(payConn)
	sessionAuthRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["SessionAuthRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = sessionAuthRedisStore.Close() }()
	commsClientVar2 := comms.NewCommsClient(universalConn)
	customerClient := customer.NewCustomerClient(vendorgatewayConn)
	employmentClient := employment.NewEmploymentClient(onboardingConn)
	bankCustomerClient := vgbcpb.NewBankCustomerClient(vendorgatewayConn)
	bankCustomerRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["BankCustomerRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for BankCustomerRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { bankCustomerRueidisRedisStore.Close() }()
	bankCustomerRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(bankCustomerRueidisRedisStore), gconf.RueidisRedisClients()["BankCustomerRueidisRedisStore"].Hystrix)
	docExtractionClient := kycdocspb.NewDocExtractionClient(onboardingConn)
	preApprovedLoanClient := preapprovedloanpb.NewPreApprovedLoanClient(lendingConn)
	bankCustomerRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["BankCustomerRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = bankCustomerRedisStore.Close() }()
	employmentFeClient := employment.NewEmploymentFeClient(onboardingConn)
	eKYCClient := vgekycpb.NewEKYCClient(vendorgatewayConn)
	pANClient := vgpanpb.NewPANClient(vendorgatewayConn)
	watsonClient := watson.NewWatsonClient(universalConn)
	userRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userRedisStore.Close() }()
	employmentClientVar3 := vgemploymentpb.NewEmploymentClient(vendorgatewayConn)
	actionBarClient := search.NewActionBarClient(universalConn)
	seonClient := seonpb.NewSeonClient(vendorgatewayConn)
	employmentDataRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["EmploymentDataRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for EmploymentDataRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { employmentDataRueidisRedisStore.Close() }()
	employmentDataRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(employmentDataRueidisRedisStore), gconf.RueidisRedisClients()["EmploymentDataRueidisRedisStore"].Hystrix)
	inAppReferralRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["InAppReferralRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = inAppReferralRedisStore.Close() }()
	inAppReferralClient := inappreferralpb.NewInAppReferralClient(onboardingConn)
	nudgeServiceClient := nudge.NewNudgeServiceClient(universalConn)
	orderServiceClient := orderpb.NewOrderServiceClient(payConn)
	rewardsGeneratorClient := rewards.NewRewardsGeneratorClient(universalConn)
	piClient := pipb.NewPiClient(payConn)
	fireflyClient := ffpb.NewFireflyClient(lendingConn)
	upiOnboardingClient := upionbpb.NewUpiOnboardingClient(payConn)
	portfolioManagerClient := ussportfoliomanagerpb.NewPortfolioManagerClient(universalConn)
	depositClient := depositpb.NewDepositClient(universalConn)
	emailParserClient := emailparserpb.NewEmailParserClient(universalConn)
	accessInfoClient := accessinfo.NewAccessInfoClient(universalConn)
	fennelFeatureStoreClient := fennelvgpb.NewFennelFeatureStoreClient(vendorgatewayConn)
	scienapticClient := vgscienapticpb.NewScienapticClient(vendorgatewayConn)
	creditReportManagerClient := creditreportv.NewCreditReportManagerClient(lendingConn)
	screenerClient := screener2.NewScreenerClient(onboardingConn)
	depositClientVar2 := deposit.NewDepositClient(vendorgatewayConn)
	userDevicePropertiesRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserDevicePropertiesRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userDevicePropertiesRedisStore.Close() }()
	minimalUserRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["MinimalUserRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = minimalUserRedisStore.Close() }()
	userRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["UserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for UserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { userRueidisRedisStore.Close() }()
	userRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(userRueidisRedisStore), gconf.RueidisRedisClients()["UserRueidisRedisStore"].Hystrix)
	minimalUserRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["MinimalUserRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for MinimalUserRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { minimalUserRueidisRedisStore.Close() }()
	minimalUserRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(minimalUserRueidisRedisStore), gconf.RueidisRedisClients()["MinimalUserRueidisRedisStore"].Hystrix)
	panClient := pan.NewPanClient(onboardingConn)
	userLeadSvcClient := leadspb.NewUserLeadSvcClient(lendingConn)
	userPreferenceClient := uppb.NewUserPreferenceClient(universalConn)
	locationClient := userlocation.NewLocationClient(onboardingConn)
	obfuscatorClient := obfuscatorpb.NewObfuscatorClient(onboardingConn)
	locationClientVar2 := locationpb.NewLocationClient(onboardingConn)
	derivedAttributesManagerClient := derivedattributespb.NewDerivedAttributesManagerClient(lendingConn)
	onboardingRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["OnboardingRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for OnboardingRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { onboardingRueidisRedisStore.Close() }()
	onboardingRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(onboardingRueidisRedisStore), gconf.RueidisRedisClients()["OnboardingRueidisRedisStore"].Hystrix)
	omegleClient := omegle.NewOmegleClient(onboardingConn)
	docsClient := vgdocpb.NewDocsClient(vendorgatewayConn)
	onboardingMinRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["OnboardingMinRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for OnboardingMinRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { onboardingMinRueidisRedisStore.Close() }()
	onboardingMinRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(onboardingMinRueidisRedisStore), gconf.RueidisRedisClients()["OnboardingMinRueidisRedisStore"].Hystrix)
	troubleshootClient := vkyccalltroubleshootpb.NewTroubleshootClient(onboardingConn)
	livenessClientVar6 := vglivenesspb.NewLivenessClient(vendorgatewayConn)
	questRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["QuestRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = questRedisStore.Close() }()
	questCacheStorage := cache.NewRedisCacheStorageWithHystrix(cache.NewRedisCacheStorage(questRedisStore), gconf.RedisClusters()["QuestRedisStore"].HystrixCommand)
	mFExternalOrdersClient := mfpb.NewMFExternalOrdersClient(universalConn)
	fireflyV2Client := ffv2pb.NewFireflyV2Client(lendingConn)
	locationClientVar3 := locationvgpb.NewLocationClient(vendorgatewayConn)
	ipServiceClient := ippb.NewIpServiceClient(onboardingConn)
	userContactRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["UserContactRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = userContactRedisStore.Close() }()
	userGroupRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["UserGroupRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for UserGroupRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { userGroupRueidisRedisStore.Close() }()
	userGroupRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(userGroupRueidisRedisStore), gconf.RueidisRedisClients()["UserGroupRueidisRedisStore"].Hystrix)
	vendormappingConn := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.VENDORMAPPING_SERVER)
	defer epifigrpc.CloseConn(vendormappingConn)
	vendorMappingServiceClient := vmpb.NewVendorMappingServiceClient(vendormappingConn)
	moEngageClient := vgmoengagepb.NewMoEngageClient(vendorgatewayConn)
	userSegmentationClient := userseg.NewUserSegmentationClient(vendorgatewayConn)
	phoneNetworkClient := phonenetwork.NewPhoneNetworkClient(vendorgatewayConn)
	profileValidationClient := profilevalidationpb.NewProfileValidationClient(vendorgatewayConn)
	incomeEstimatorClient := incomeestimator.NewIncomeEstimatorClient(vendorgatewayConn)
	iTRClient := vgitr.NewITRClient(vendorgatewayConn)
	cKycClient := ckyc.NewCKycClient(vendorgatewayConn)
	dLClient := dl.NewDLClient(vendorgatewayConn)
	idValidateClient := idvalidate.NewIdValidateClient(vendorgatewayConn)
	ocrClient := inhouseocr.NewOcrClient(vendorgatewayConn)
	idfcClient := vgidfcpb.NewIdfcClient(vendorgatewayConn)
	idfcClientVar2 := vgidfcvkycpb.NewIdfcClient(vendorgatewayConn)
	kYCRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["KYCRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = kYCRedisStore.Close() }()
	salaryProgramClient := salaryprogram.NewSalaryProgramClient(universalConn)
	inAppTargetedCommsClient := tcpb.NewInAppTargetedCommsClient(universalConn)
	vkycClient := pb.NewVkycClient(vendorgatewayConn)
	vKYCRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["VKYCRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for VKYCRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { vKYCRueidisRedisStore.Close() }()
	vKYCRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(vKYCRueidisRedisStore), gconf.RueidisRedisClients()["VKYCRueidisRedisStore"].Hystrix)
	matcherClient := matcher.NewMatcherClient(onboardingConn)
	casbinClient := casbinpb.NewCasbinClient(universalConn)
	uqudoClient := kycuqudopb.NewUqudoClient(onboardingConn)
	uqudoClientVar2 := vguqudopb.NewUqudoClient(vendorgatewayConn)
	oCRClient := ocr.NewOCRClient(vendorgatewayConn)
	universalConnVar80ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar3 := servergenwire.NewKycRequestClientInterceptor()
	if unaryClientInterceptorVar3 != nil {
		universalConnVar80ClientInterceptors = append(universalConnVar80ClientInterceptors, unaryClientInterceptorVar3)
	}
	universalConnVar80 := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar80ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar80)
	commsClientVar12 := comms.NewCommsClient(universalConnVar80)
	nsdlClient := nsdlvgpb.NewNsdlClient(vendorgatewayConn)
	panValidationRueidisRedisStore, err := storage2.NewRueidisClientFromConfig(gconf.RueidisRedisClients()["PanValidationRueidisRedisStore"])
	if err != nil {
		logger.Error(ctx, "failed to establish rueidis redis client conn for PanValidationRueidisRedisStore", zap.Error(err))
		return err
	}
	defer func() { panValidationRueidisRedisStore.Close() }()
	panValidationRueidisCacheStorage := cache.NewRueidisCacheStorageWithHystrix(cache.NewRueidisCacheWithOptions(panValidationRueidisRedisStore), gconf.RueidisRedisClients()["PanValidationRueidisRedisStore"].Hystrix)
	universalConnVar86ClientInterceptors := make([]grpc.UnaryClientInterceptor, 0)
	unaryClientInterceptorVar4 := servergenwire.NewKycRequestClientInterceptor()
	if unaryClientInterceptorVar4 != nil {
		universalConnVar86ClientInterceptors = append(universalConnVar86ClientInterceptors, unaryClientInterceptorVar4)
	}
	universalConnVar86 := epifigrpc.NewServerConn(cfg.ONBOARDING_SERVER, cfg.UNIVERSAL_SERVER, universalConnVar86ClientInterceptors...)
	defer epifigrpc.CloseConn(universalConnVar86)
	commsClientVar13 := comms.NewCommsClient(universalConnVar86)
	riskClientVar3 := vgriskpb.NewRiskClient(vendorgatewayConn)
	contactClient := usercontactpb.NewContactClient(onboardingConn)
	transactionRiskDronaPayClient := vgtxnmonitoringpb.NewTransactionRiskDronaPayClient(vendorgatewayConn)
	txnRiskScoreServiceClient := riskpb.NewTxnRiskScoreServiceClient(onboardingConn)
	merchantServiceClient := merchant.NewMerchantServiceClient(payConn)
	txnCategorizerClient := categorizer.NewTxnCategorizerClient(universalConn)
	ticketClient := cxticketpb.NewTicketClient(universalConn)
	paymentClient := paymentpb.NewPaymentClient(payConn)
	profileClient := profilepb.NewProfileClient(onboardingConn)
	cRMClient := vgcrmpb.NewCRMClient(vendorgatewayConn)
	var bigqueryConnTeardown func()
	bigqueryDBResourceProvider, bigqueryConnTeardown, err := storagev2analytics.NewBigqueryDBResourceProvider(ctx, gconf.BigqueryClientsMap().GetOwnerToClientConfigMap())
	if err != nil {
		logger.Error(ctx, "failed to get bigquery conn provider", zap.Error(err))
		return err
	}
	defer bigqueryConnTeardown()
	developerClient := tspclient.NewDeveloperClient(onboardingConn)
	vkycCallRedisStore := storage.NewRedisClientFromConfig(gconf.RedisClusters()["VkycCallRedisStore"], gconf.Tracing().Enable)
	defer func() { _ = vkycCallRedisStore.Close() }()
	vkycCallClient := vkycvgpb.NewVkycCallClient(vendorgatewayConn)
	oCRClientVar4 := ocr2.NewOCRClient(onboardingConn)

	awsConf, awsErr := awsconfpkg.NewAWSConfig(ctx, gconf.AWS().Region, gconf.Tracing().Enable)
	if awsErr != nil {
		logger.Error(ctx, "error in loading aws v2 config", zap.Error(awsErr))
		if !cfg.IsRemoteDebugEnabled() {
			return err
		}
	}

	sqsClient := sqs.InitSQSClient(awsConf)
	_ = sqsClient

	var broker *events.RudderStackBroker

	// load rudder-stack broker for sending events
	rudderClient, err := analytics.NewWithConfig(
		gconf.Secrets().Ids[config.RudderWriteKey], "http://"+cfg.GetServerEndPoint(cfg.RUDDER_SERVER).URL(),
		analytics.Config{
			Callback:  events.NewCallback(),
			Interval:  gconf.RudderStack().IntervalInSec * time.Second, // nolint: durationcheck
			BatchSize: gconf.RudderStack().BatchSize,
			Verbose:   gconf.RudderStack().Verbose,
		})
	if err != nil {
		logger.Error(ctx, "error in connecting rudder", zap.Error(err))
		return err
	}
	broker = events.NewRudderStackBroker(rudderClient)
	defer broker.Close()

	rateLimiter := ratelimiter.NewRateLimiterV2(store.NewSlidingWindowLogWithRedis(rateLimiterRedisStore), gconf.GrpcRatelimiterParams().RateLimitConfig())
	_ = rateLimiter

	var unaryInterceptors []grpc.UnaryServerInterceptor

	unaryServerInterceptor, err := servergenwire.AddRudderEventsUnaryInterceptor(broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc unary interceptor", zap.Error(err))
		return err
	}
	if unaryServerInterceptor != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptor)
	}

	unaryServerInterceptorVar2 := servergenwire.AddQuestUserContextUnaryInterceptor(gconf)
	if unaryServerInterceptorVar2 != nil {
		unaryInterceptors = append(unaryInterceptors, unaryServerInterceptorVar2)
	}

	var streamInterceptors []grpc.StreamServerInterceptor

	histogramBuckets := prometheus.DefBuckets
	if len(gconf.LatencyHistogramBuckets()) > 0 {
		histogramBuckets = epifigrpc.AddGrpcLatencyBuckets(gconf.LatencyHistogramBuckets()...)
	}

	serverOptions := []grpc.ServerOption{}
	if gconf.GrpcServerConfig().MaxRecvMsgSize() != nil && gconf.GrpcServerConfig().MaxRecvMsgSize().Size != 0 {
		serverOptions = append(serverOptions, grpc.MaxRecvMsgSize(gconf.GrpcServerConfig().MaxRecvMsgSize().Size))
	}
	var extractFeRespStatusWithErrMsgFunc commoninterceptors.ExtractFeRespStatusWithErrMsgFunc
	extractFeRespStatusWithErrMsgFunc = fepkg.ExtractFeRespStatusWithErrMsgFunc

	s := epifigrpc.NewSecureServerWithInterceptors(gconf.GrpcServerConfig(), gconf.Flags().TrimDebugMessageFromStatus, string(gconf.Name()), histogramBuckets, unaryInterceptors, streamInterceptors, extractFeRespStatusWithErrMsgFunc, serverOptions...)

	if cfg.IsNonProdEnv(gconf.Environment()) {
		reflection.Register(s)
	}

	httpMux := http.NewServeMux()
	_ = httpMux

	var sqsSubscribers []queue.Subscriber
	var subs []queue.Subscriber
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSubs []*sqs.ExtendedSqsSubscriber

	subs, extSubs, err = setupAlfred(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, bankCustomerServiceClient, celestialClient, savingsClient, actorClient, vKYCClient, statementServiceClient, wealthOnboardingClient, tieringClient, balanceClient, consentClient, kycClient, livenessClient, usersClient, uNNameCheckClient, accountManagerClient, ussTaxServiceClient, alfredClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupAcquisition(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, crossAttachUserAttributesCacheStorage, onboardingClient, lendabilityClient, netWorthClient, usersClient, userIntelServiceClient, connectedAccountClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupAuth(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, authPGDB, commsClient, vendorAuthClient, savingsClient, cardProvisioningClient, authRedisStore, authTokenRedisStore, authDeviceIntegrityNonceCacheStorage, authDeviceRegistrationsCacheStorage, livenessClient, consentClient, bankCustomerServiceClient, redListClient, riskClient, authClient, healthEngineServiceClient, caseManagementClient, kycAgentServiceClient, fCMDeviceTokenClient, operationalStatusServiceClient, productClient, kycClient, onboardingClient, payClient, authClientVar2, partnerSDKClient, accountingClient, authDeviceLocationCacheStorage, celestialClient, uPIClient, sessionAuthRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupBankcust(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, actorClient, usersClient, savingsClient, commsClientVar2, customerClient, employmentClient, kycClient, authClientVar2, celestialClient, bankCustomerClient, vKYCClient, bankCustomerRueidisCacheStorage, groupClient, docExtractionClient, onboardingClient, preApprovedLoanClient, bankCustomerPGDB, operationalStatusServiceClient, bankCustomerServiceClient, bankCustomerRedisStore, employmentFeClient, eKYCClient, pANClient, watsonClient, userRedisStore)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupEmployment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, employmentClientVar3, actorClient, usersClient, actionBarClient, onboardingClient, authClientVar2, seonClient, uNNameCheckClient, groupClient, bankCustomerServiceClient, employmentDataRueidisCacheStorage)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupInappreferral(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, inAppReferralRedisStore, inAppReferralClient, nudgeServiceClient, orderServiceClient, commsClientVar2, onboardingClient, balanceClient, savingsClient, rewardsGeneratorClient, userIntelServiceClient, piClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupReferral(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, commsClientVar2)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupProduct(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, savingsClient, fireflyClient, preApprovedLoanClient, usersClient, onboardingClient, upiOnboardingClient, portfolioManagerClient, depositClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupScreener(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, userIntelServiceClient, employmentClient, emailParserClient, authClientVar2, usersClient, connectedAccountClient, actorClient, groupClient, accessInfoClient, fennelFeatureStoreClient, scienapticClient, consentClient, creditReportManagerClient, screenerClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupUser(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, epifiCRDB, kycClient, customerClient, savingsClient, authClientVar2, vKYCClient, depositClientVar2, commsClientVar2, userRedisStore, userDevicePropertiesRedisStore, minimalUserRedisStore, livenessClient, bankCustomerServiceClient, screenerClient, employmentClient, productClient, userRueidisCacheStorage, minimalUserRueidisCacheStorage, panClient, userPropertiesPGDB, onboardingClient, docExtractionClient, userLeadSvcClient, cardProvisioningClient, consentClient, orderServiceClient, inAppReferralClient, eKYCClient, userPreferenceClient, uNNameCheckClient, locationClient, obfuscatorClient, locationClientVar2, riskClient, employmentFeClient, derivedAttributesManagerClient, userIntelServiceClient, nudgePGDB, watsonClient, operationalStatusServiceClient, balanceClient, fireflyClient, preApprovedLoanClient, pANClient, creditReportManagerClient, onboardingRueidisCacheStorage, omegleClient, docsClient, onboardingMinRueidisCacheStorage, troubleshootClient, tieringClient, livenessClientVar6, questCacheStorage, mFExternalOrdersClient, connectedAccountClient, fireflyV2Client, locationClientVar3, ipServiceClient, userContactRedisStore, userGroupRueidisCacheStorage, vendorMappingServiceClient, moEngageClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupUseractions(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupUserintel(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, usersClient, actorClient, userSegmentationClient, phoneNetworkClient, employmentClientVar3, profileValidationClient, userRedisStore, onboardingClient, incomeEstimatorClient, inAppReferralClient, employmentClient, locationClientVar2, fennelFeatureStoreClient, scienapticClient, iTRClient, creditReportManagerClient, lendabilityClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupKyc(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, cKycClient, uNNameCheckClient, dLClient, idValidateClient, vKYCClient, ocrClient, epifiCRDB, bankCustomerServiceClient, kycPGDB, idfcClient, idfcClientVar2, panClient, eKYCClient, authClientVar2, pANClient, kycNonResidentPGDB, kYCRedisStore, kycClient, salaryProgramClient, locationClientVar2, employmentFeClient, savingsClient, locationClient, inAppTargetedCommsClient, employmentClient, userIntelServiceClient, vkycClient, balanceClient, onboardingClient, vKYCRueidisCacheStorage, omegleClient, docExtractionClient, matcherClient, casbinClient, uqudoClient, uqudoClientVar2, oCRClient, commsClientVar12)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupPan(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vkycClient, usersClient, epifiCRDB, kycPGDB, userIntelServiceClient, celestialClient, pANClient, nsdlClient, obfuscatorClient, uNNameCheckClient, panValidationRueidisCacheStorage, oCRClient, actorClient, groupClient, panClient, kycClient, docsClient, bankCustomerServiceClient, vKYCClient, commsClientVar13)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupVendordata(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vendordataPGDB)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupShipment(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vendordataPGDB)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupRisk(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, fRMCRDB, fRMPGDB, usersClient, actorClient, locationClient, locationClientVar2, authClientVar2, riskClientVar3, inAppReferralClient, livenessClient, kycClient, savingsClient, celestialClient, redListClient, uNNameCheckClient, employmentClient, userIntelServiceClient, creditReportManagerClient, commsClientVar2, derivedAttributesManagerClient, bankCustomerServiceClient, onboardingClient, uPIClient, operationalStatusServiceClient, screenerClient, tieringClient, caseManagementClient, contactClient, userRedisStore, preApprovedLoanClient, obfuscatorClient, payClient, riskClient, transactionRiskDronaPayClient, txnRiskScoreServiceClient, piClient, merchantServiceClient, txnCategorizerClient, ticketClient, paymentClient, profileClient, orderServiceClient, cRMClient, vendorMappingServiceClient, segmentationServiceClient, productClient, salaryProgramClient, bigqueryDBResourceProvider, depositClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupOmegle(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, verifiPGDB, vkycClient, livenessClientVar6, oCRClient, locationClient, uqudoClient, locationClientVar3, locationClientVar2, uNNameCheckClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupTspuser(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, stockGuardianTspPGDB, developerClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupLiveness(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, epifiCRDB, livenessClientVar6, caseManagementClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	subs, extSubs, err = setupVkyccall(ctx, s, conf, gconf, configNameToConfMap, awsConf, crdbResourceMap, crdbTxnResourceMap, rateLimiterRedisStore, sqsClient, broker, vkycCallRedisStore, vkycCallClient, omegleClient, locationClient, locationClientVar2, obfuscatorClient, matcherClient, oCRClientVar4, oCRClient, onboardingClient)
	if err != nil {
		return err
	}
	sqsSubscribers = append(sqsSubscribers, subs...)
	extendedSqsSubscribers = append(extendedSqsSubscribers, extSubs...)

	explorerpb.RegisterConfigExplorerServer(s, explorer.NewServer(configNameToConfMap))

	cleanupFn, err := hook.RegisterProcessRudderEventMethodToSubscriber(epifiCRDB) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "RegisterProcessRudderEventMethodToSubscriber"), zap.Error(err))
		return err
	}
	defer cleanupFn()

	cleanupFnVar2, err := hook.InitKycServer(s) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitKycServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar2()

	cleanupFnVar3, err := hook.InitRiskHttpServer(gconf, httpMux, dronapayCallbackClient) // Hook configured to run before the server start
	if err != nil {
		logger.Error(ctx, "failed to init before grpc server start hooks", zap.String("hook", "InitRiskHttpServer"), zap.Error(err))
		return err
	}
	defer cleanupFnVar3()

	// Load rpc level log sampling rate descriptors.
	epifigrpc.GetRPCLogSamplingRatesMapInstance().Load(s)

	initNotifier <- gconf.Name()

	// TODO(Sundeep): Remove this additional logic for starting the servers inside go-routine, by exposing functions
	// from epifiserver package that take errgroup.Group as parameter.
	grp, _ := errgroup.WithContext(context.Background())

	grp.Go(func() error {
		epifiserver.StartSecureServer(s, gconf.ServerPorts(), string(gconf.Name()))
		return nil
	})

	// Wait for server health before starting SQS workers
	if queue.IsWorkerInitializationEnabled() {
		goroutine.RunWithCtx(ctx, func(ctx context.Context) {
			if epifiserver.IsServerReady(gconf.ServerPorts().HttpPort) {
				logger.Info(ctx, "Server is healthy, starting SQS workers")
				sqs.StartSQSWorkers(ctx, sqsSubscribers, extendedSqsSubscribers)
			} else {
				logger.PanicWithCtx(ctx, "Server health check timed out, not starting SQS workers")
			}
		})
	}
	if err := grp.Wait(); err != nil {
		return err
	}

	return nil
}

// nolint: funlen
func setupAlfred(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	celestialClient celestialpb.CelestialClient,
	savingsClient savingsclientpb.SavingsClient,
	actorClient actor.ActorClient,
	vKYCClient vkycpb.VKYCClient,
	statementServiceClient statementpb.StatementServiceClient,
	wealthOnboardingClient wealthobpb.WealthOnboardingClient,
	tieringClient tiering.TieringClient,
	balanceClient accountbalancepb.BalanceClient,
	consentClient consentpb.ConsentClient,
	kycClient kyc.KycClient,
	livenessClient liveness.LivenessClient,
	usersClient user.UsersClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	accountManagerClient ussaccountmgpb.AccountManagerClient,
	ussTaxServiceClient tax.UssTaxServiceClient,
	alfredClient alfredpb.AlfredClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	alfredConf, err := alfredconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ALFRED_SERVICE))
		return nil, nil, err
	}
	_ = alfredConf

	alfredGenConf, err := dynconf.LoadConfig(alfredconf.Load, genconf2.NewConfig, cfg.ALFRED_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ALFRED_SERVICE))
		return nil, nil, err
	}

	_ = alfredGenConf

	service := wire.InitialiseAlfredService(epifiCRDB, bankCustomerServiceClient, celestialClient, savingsClient, actorClient, vKYCClient, statementServiceClient, wealthOnboardingClient, tieringClient, balanceClient, alfredGenConf, consentClient, kycClient, livenessClient, usersClient, uNNameCheckClient, broker, accountManagerClient, ussTaxServiceClient)

	alfredpb.RegisterAlfredServer(s, service)

	alfredDevService := wire.InitialiseAlfredDevService(epifiCRDB, alfredClient)

	alfreddeveloper.RegisterDeveloperServer(s, alfredDevService)

	configNameToConfMap[cfg.ConfigName(cfg.ALFRED_SERVICE)] = &commonexplorer.Config{StaticConf: &alfredconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupAcquisition(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	crossAttachUserAttributesCacheStorage types2.CrossAttachUserAttributesCacheStorage,
	onboardingClient onbpb.OnboardingClient,
	lendabilityClient lendabilitypb.LendabilityClient,
	netWorthClient networthpb.NetWorthClient,
	usersClient user.UsersClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	acquisitionConf, err := acquisitionconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACQUISITION_SERVICE))
		return nil, nil, err
	}
	_ = acquisitionConf

	acquisitionGenConf, err := dynconf.LoadConfig(acquisitionconf.Load, genconf3.NewConfig, cfg.ACQUISITION_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.ACQUISITION_SERVICE))
		return nil, nil, err
	}

	_ = acquisitionGenConf

	serviceVar2 := wire2.InitializeCrossAttachService(acquisitionGenConf, crossAttachUserAttributesCacheStorage, onboardingClient, lendabilityClient, netWorthClient, usersClient, userIntelServiceClient, connectedAccountClient, broker)

	acquisitioncrossattachpb.RegisterCrossAttachServer(s, serviceVar2)

	configNameToConfMap[cfg.ConfigName(cfg.ACQUISITION_SERVICE)] = &commonexplorer.Config{StaticConf: &acquisitionconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupAuth(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	authPGDB types.AuthPGDB,
	commsClient wiretypes.AuthCommsClientWithInterceptors,
	vendorAuthClient vgauth.VendorAuthClient,
	savingsClient savingsclientpb.SavingsClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	authRedisStore types.AuthRedisStore,
	authTokenRedisStore types.AuthTokenRedisStore,
	authDeviceIntegrityNonceCacheStorage wiretypes.AuthDeviceIntegrityNonceCacheStorage,
	authDeviceRegistrationsCacheStorage wiretypes.AuthDeviceRegistrationsCacheStorage,
	livenessClient liveness.LivenessClient,
	consentClient consentpb.ConsentClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	redListClient redlist.RedListClient,
	riskClient riskpb.RiskClient,
	authClient simauthfederal.AuthClient,
	healthEngineServiceClient healthengine.HealthEngineServiceClient,
	caseManagementClient casemanagement.CaseManagementClient,
	kycAgentServiceClient agent.KycAgentServiceClient,
	fCMDeviceTokenClient types3.FCMDeviceTokenClientWithInterceptors,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	productClient productpb.ProductClient,
	kycClient kyc.KycClient,
	onboardingClient onbpb.OnboardingClient,
	payClient paypb.PayClient,
	authClientVar2 authpb.AuthClient,
	partnerSDKClient vgpartnersdkpb.PartnerSDKClient,
	accountingClient ffaccpb.AccountingClient,
	authDeviceLocationCacheStorage wiretypes.AuthDeviceLocationCacheStorage,
	celestialClient celestialpb.CelestialClient,
	uPIClient upipb.UPIClient,
	sessionAuthRedisStore types.SessionAuthRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	authConf, err := authconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.AUTH_SERVICE))
		return nil, nil, err
	}
	_ = authConf

	authGenConf, err := dynconf.LoadConfigWithQuestConfig(authconf.Load, genconf4.NewConfigWithQuest, cfg.AUTH_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.AUTH_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		authGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: authGenConf, SdkConfig: authGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{authGenConfAppConfig}, string(cfg.ONBOARDING_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = authGenConf

	aFUVendorUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, authGenConf.AFUVendorUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	biometricEventPublisher, err := sqs.NewPublisherWithConfig(ctx, authGenConf.BiometricEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	authFactorUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, authGenConf.AuthFactorUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	authTokenCreationPublisher, err := sns.NewSnsPublisherWithConfig(ctx, authGenConf.AuthTokenCreationPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar3 := wire3.InitializeService(epifiCRDB, authPGDB, commsClient, vendorAuthClient, authConf, authGenConf, usersClient, actorClient, savingsClient, broker, aFUVendorUpdatePublisher, authFactorUpdatePublisher, authTokenCreationPublisher, cardProvisioningClient, authRedisStore, authTokenRedisStore, authDeviceIntegrityNonceCacheStorage, authDeviceRegistrationsCacheStorage, livenessClient, consentClient, bankCustomerServiceClient, redListClient, riskClient, authClient, healthEngineServiceClient, caseManagementClient, kycAgentServiceClient, fCMDeviceTokenClient, operationalStatusServiceClient, productClient, kycClient, onboardingClient, payClient)

	authconsumerpb.RegisterAuthFactorUpdateConsumerServer(s, serviceVar3)

	authpb.RegisterAuthServer(s, serviceVar3)

	authorizerpb.RegisterAuthorizerServer(s, serviceVar3)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.AFUVendorUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessVendorUpdateMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.DeviceReregCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterDeviceReregCallbackConsumerMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.AFUManualReviewNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessAFUManualReviewMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.DeviceRegSMSAckSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessDevRegSMSAcknowledgementMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, authGenConf.ProcessPinAttemptsExceededEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		authconsumerpb.RegisterProcessPinAttemptsExceededEventMethodToSubscriber(subscriber, serviceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	devAuthService := wire3.InitializeDevAuthService(epifiCRDB, authPGDB, authGenConf, authTokenRedisStore, broker)

	authdevpb.RegisterDevAuthServer(s, devAuthService)

	serviceVar4 := wire3.InitializePartnerSDKService(authClientVar2, actorClient, partnerSDKClient, authGenConf, groupClient, authRedisStore, accountingClient)

	authpartnersdkpb.RegisterPartnerSDKServer(s, serviceVar4)

	serviceVar5 := wire3.InitializeLocationService(epifiCRDB, authPGDB, authGenConf, authDeviceLocationCacheStorage, broker)

	locationpb.RegisterLocationServer(s, serviceVar5)

	serviceVar6 := wire4.InitializeService(epifiCRDB, celestialClient, actorClient, savingsClient, uPIClient, authClientVar2, usersClient, livenessClient, authGenConf)

	authv2pb.RegisterOrchestratorServer(s, serviceVar6)

	orchestratorDevEntity := wire4.InitializeOrchestratorDevEntityService(epifiCRDB)

	authorchdevpb.RegisterDevOrchestratorServer(s, orchestratorDevEntity)

	serviceVar7 := wire5.InitializeBiometricsService(authPGDB, biometricEventPublisher)

	biometricspb.RegisterBiometricsServiceServer(s, serviceVar7)

	serviceVar8 := wire3.InitializeTotpService(authGenConf, sessionAuthRedisStore)

	totp2.RegisterTotpServer(s, serviceVar8)

	sessionManager := wire3.InitializeSessionManagerService(authGenConf, authClientVar2, sessionAuthRedisStore)

	sessionpb.RegisterSessionManagerServer(s, sessionManager)

	configNameToConfMap[cfg.ConfigName(cfg.AUTH_SERVICE)] = &commonexplorer.Config{StaticConf: &authconf.Config{}, QuestIntegratedConfig: authGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupBankcust(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	savingsClient savingsclientpb.SavingsClient,
	commsClientVar2 comms.CommsClient,
	customerClient customer.CustomerClient,
	employmentClient employment.EmploymentClient,
	kycClient kyc.KycClient,
	authClientVar2 authpb.AuthClient,
	celestialClient celestialpb.CelestialClient,
	bankCustomerClient vgbcpb.BankCustomerClient,
	vKYCClient vkycpb.VKYCClient,
	bankCustomerRueidisCacheStorage types.BankCustomerRueidisCacheStorage,
	groupClient usergrouppb.GroupClient,
	docExtractionClient kycdocspb.DocExtractionClient,
	onboardingClient onbpb.OnboardingClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	bankCustomerPGDB types.BankCustomerPGDB,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	bankCustomerRedisStore types.BankCustomerRedisStore,
	employmentFeClient employment.EmploymentFeClient,
	eKYCClient vgekycpb.EKYCClient,
	pANClient vgpanpb.PANClient,
	watsonClient watson.WatsonClient,
	userRedisStore types.UserRedisStore) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	bankcustConf, err := bankcustconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BANK_CUSTOMER_SERVICE))
		return nil, nil, err
	}
	_ = bankcustConf

	bankcustGenConf, err := dynconf.LoadConfig(bankcustconf.Load, genconf5.NewConfig, cfg.BANK_CUSTOMER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.BANK_CUSTOMER_SERVICE))
		return nil, nil, err
	}

	_ = bankcustGenConf

	customerCreationPublisher, err := sqs.NewPublisherWithConfig(ctx, bankcustGenConf.CustomerCreationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	signalWorkflowPublisher, err := sqs.NewPublisherWithConfig(ctx, bankcustGenConf.SignalWorkflowPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	bankCustomerUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, bankcustGenConf.BankCustomerUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	kycLevelUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, bankcustGenConf.KycLevelUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	bankCustomerS3Client := s3pkg.NewClient(awsConf, bankcustGenConf.Aws().S3.BankCustomerBucketName)

	bankCustomerService := wire6.InitBankCustomerService(epifiCRDB, actorClient, usersClient, savingsClient, commsClientVar2, customerCreationPublisher, bankCustomerUpdateEventPublisher, broker, customerClient, employmentClient, kycClient, authClientVar2, celestialClient, bankCustomerClient, signalWorkflowPublisher, bankCustomerS3Client, bankcustGenConf, vKYCClient, kycLevelUpdatePublisher, bankCustomerRueidisCacheStorage, groupClient, docExtractionClient, onboardingClient, preApprovedLoanClient)

	bankcust.RegisterBankCustomerServiceServer(s, bankCustomerService)

	kYCComplianceService := wire6.InitializeKYCComplianceService(bankCustomerPGDB, bankcustGenConf, usersClient, authClientVar2, operationalStatusServiceClient, savingsClient, bankCustomerServiceClient, bankCustomerRedisStore, broker, kycClient, employmentFeClient, eKYCClient, pANClient, customerClient, watsonClient)

	compliance2.RegisterComplianceServer(s, kYCComplianceService)

	bankCustomerDevService := wire6.InitializeDevBankCustomerService(epifiCRDB, bankCustomerPGDB, bankcustGenConf, userRedisStore, bankCustomerRueidisCacheStorage)

	bankcustdeveloper.RegisterDevBankCustServer(s, bankCustomerDevService)

	federalBankCustomerConsumer := wire6.InitFederalBankCustomerConsumer(epifiCRDB, actorClient, userRedisStore, usersClient, commsClientVar2, customerCreationPublisher, bankCustomerUpdateEventPublisher, broker, customerClient, employmentClient, kycClient, authClientVar2, bankcustGenConf, vKYCClient, savingsClient, celestialClient, bankCustomerClient, groupClient, bankCustomerRueidisCacheStorage, docExtractionClient, onboardingClient, preApprovedLoanClient, watsonClient)

	bankcust.RegisterFederalBankCustomerConsumerServer(s, federalBankCustomerConsumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.CustomerCreationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessCustomerCreationMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.CustomerCreationCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessCustomerCreationCallbackMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.BKYCUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessBKYCUpdateEventMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.KYCStateChangeEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessKYCStateChangeEventMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.ProcessResidentialStatusUpdateCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessResidentialStatusUpdateCallbackMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.ProcessMobileNumberUpdateCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessMobileNumberUpdateCallbackMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.AuthFactorUpdateNotificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		bankcust.RegisterProcessAuthFactorUpdateEventMethodToSubscriber(subscriber, federalBankCustomerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	eventSubscriber := wire6.InitialiseEventsSubscriberService(bankCustomerPGDB, savingsClient, bankCustomerServiceClient, broker, bankcustGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, bankcustGenConf.ProcessAccountOpsStatusEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer2.RegisterProcessOperationalStatusUpdateEventConsumerMethodToSubscriber(subscriber, eventSubscriber)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.BANK_CUSTOMER_SERVICE)] = &commonexplorer.Config{StaticConf: &bankcustconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupEmployment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	employmentClientVar3 vgemploymentpb.EmploymentClient,
	actorClient actor.ActorClient,
	usersClient user.UsersClient,
	actionBarClient search.ActionBarClient,
	onboardingClient onbpb.OnboardingClient,
	authClientVar2 authpb.AuthClient,
	seonClient seonpb.SeonClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	groupClient usergrouppb.GroupClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	employmentDataRueidisCacheStorage wiretypes4.EmploymentDataRueidisCacheStorage) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	employmentConf, err := employmentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.EMPLOYMENT_SERVICE))
		return nil, nil, err
	}
	_ = employmentConf

	employmentGenConf, err := dynconf.LoadConfig(employmentconf.Load, genconf6.NewConfig, cfg.EMPLOYMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.EMPLOYMENT_SERVICE))
		return nil, nil, err
	}

	_ = employmentGenConf

	employmentVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, employmentGenConf.EmploymentVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	linkedinVerificationPublisher, err := sqs.NewPublisherWithConfig(ctx, employmentGenConf.LinkedinVerificationPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	employerPiMappingUpdateEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, employmentGenConf.EmployerPiMappingUpdateEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	updateEmploymentPublisher, err := sns.NewSnsPublisherWithConfig(ctx, employmentGenConf.UpdateEmploymentPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	incomeUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, employmentGenConf.IncomeUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar9 := wire7.InitializeService(epifiCRDB, employmentClientVar3, employmentGenConf, actorClient, usersClient, employmentVerificationPublisher, broker, actionBarClient, onboardingClient, authClientVar2, seonClient, uNNameCheckClient, linkedinVerificationPublisher, updateEmploymentPublisher, incomeUpdatePublisher, employerPiMappingUpdateEventSqsPublisher, groupClient, bankCustomerServiceClient, employmentDataRueidisCacheStorage)

	employment.RegisterEmploymentServer(s, serviceVar9)

	employment.RegisterEmploymentFeServer(s, serviceVar9)

	empconsumerpb.RegisterEmploymentConsumerServer(s, serviceVar9)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, employmentGenConf.EmploymentVerificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		empconsumerpb.RegisterVerifyEmploymentDetailsMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, employmentGenConf.LinkedinVerificationSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		empconsumerpb.RegisterVerifyUserLinkedinInformationMethodToSubscriber(subscriber, serviceVar9)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	employmentDevService := wire7.InitializeDevEmploymentService(epifiCRDB, employmentGenConf, employmentDataRueidisCacheStorage)

	employmentdeveloper.RegisterDevEmploymentServer(s, employmentDevService)

	configNameToConfMap[cfg.ConfigName(cfg.EMPLOYMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &employmentconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupInappreferral(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	inAppReferralRedisStore inappreferraltypes.InAppReferralRedisStore,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	nudgeServiceClient nudge.NudgeServiceClient,
	orderServiceClient orderpb.OrderServiceClient,
	commsClientVar2 comms.CommsClient,
	onboardingClient onbpb.OnboardingClient,
	balanceClient accountbalancepb.BalanceClient,
	savingsClient savingsclientpb.SavingsClient,
	rewardsGeneratorClient rewards.RewardsGeneratorClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	piClient pipb.PiClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	inappreferralConf, err := inappreferralconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INAPP_REFERRAL_SERVICE))
		return nil, nil, err
	}
	_ = inappreferralConf

	inappreferralGenConf, err := dynconf.LoadConfigWithQuestConfig(inappreferralconf.Load, genconf7.NewConfigWithQuest, cfg.INAPP_REFERRAL_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.INAPP_REFERRAL_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		inappreferralGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: inappreferralGenConf, SdkConfig: inappreferralGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{inappreferralGenConfAppConfig}, string(cfg.ONBOARDING_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = inappreferralGenConf

	referralsNotificationV1Publisher, err := sqs.NewPublisherWithConfig(ctx, inappreferralGenConf.ReferralsNotificationV1Publisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	exitEventSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, inappreferralGenConf.ExitEventSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	referralsEligibilityCollectedDataSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, inappreferralGenConf.ReferralsEligibilityCollectedDataSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	referralNotificationCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		inappreferralGenConf.ReferralNotificationCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(inappreferralGenConf.ReferralNotificationCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	referralsNotificationV1CustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		inappreferralGenConf.ReferralsNotificationV1CustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(inappreferralGenConf.ReferralsNotificationV1CustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	referralsEligibilityCollectedDataSqsCustomDelayPublisher, err := customqueuewire.InitializeSqsCustomDelayQueuePublisher(ctx,
		inappreferralGenConf.ReferralsEligibilityCollectedDataSqsCustomDelayPublisher().OrchestratorSqsPublisher,
		customdelayqueue.QueueName(inappreferralGenConf.ReferralsEligibilityCollectedDataSqsCustomDelayPublisher().GetDestQueueName()),
		sqsClient, queue.NewDefaultMessage(),
	)
	if err != nil {
		logger.Error(ctx, "failed to create custom sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar10 := wire8.InitializeService(epifiCRDB, inAppReferralRedisStore, actorClient, usersClient, inAppReferralClient, nudgeServiceClient, orderServiceClient, commsClientVar2, onboardingClient, segmentationServiceClient, balanceClient, savingsClient, broker, inappreferralConf, referralNotificationCustomDelayPublisher, referralsNotificationV1Publisher, referralsNotificationV1CustomDelayPublisher, exitEventSqsPublisher, inappreferralGenConf, rewardsGeneratorClient, groupClient)

	inappreferralpb.RegisterInAppReferralServer(s, serviceVar10)

	inAppReferralDev := wire8.InitializeDevInappreferralService(epifiCRDB, inAppReferralRedisStore)

	inappreferraldev.RegisterDevInAppReferralServer(s, inAppReferralDev)

	serviceVar11 := wire8.InitializeSeasonService(epifiCRDB)

	season2.RegisterSeasonServiceServer(s, serviceVar11)

	serviceVar12 := wire8.InitializeInappReferralConsumerService(epifiCRDB, inAppReferralRedisStore, commsClientVar2, actorClient, rewardsGeneratorClient, inAppReferralClient, usersClient, onboardingClient, segmentationServiceClient, balanceClient, savingsClient, orderServiceClient, referralsNotificationV1Publisher, referralsNotificationV1CustomDelayPublisher, inappreferralGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.AccountStateUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterUpdateRefereeOnbStatusMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.InAppReferralNotificationsSqsCustomDelaySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterProcessDelayedReferralNotificationEventMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ProcessRewardGenerationEventForSeasonSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterProcessRewardGenerationEventForSeasonMethodToSubscriber(subscriber, serviceVar12)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerService := wire8.InitializeEligibilityEvaluatorConsumerService(epifiCRDB, inAppReferralRedisStore, inAppReferralClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ReferralsEligibilityCollectedDataSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inapprefeligibleevalpb.RegisterProcessReferralsDataCollectorEventMethodToSubscriber(subscriber, consumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar13 := wire8.InitializeDataCollectorService(epifiCRDB, actorClient, userIntelServiceClient, inAppReferralRedisStore, referralsEligibilityCollectedDataSqsPublisher, referralsEligibilityCollectedDataSqsCustomDelayPublisher, broker, inappreferralGenConf, orderServiceClient, onboardingClient, usersClient, piClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ReferralsOrderUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inapprefdatacollectorpb.RegisterProcessOrderUpdateEventForReferralsMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ReferralsOrderUpdateFirstAddFundsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inapprefdatacollectorpb.RegisterProcessOrderUpdateEventForFirstAddFundsMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ReferralsSavingsAccountUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inapprefdatacollectorpb.RegisterProcessSavingsAccountStateUpdateEventForReferralsMethodToSubscriber(subscriber, serviceVar13)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar14 := wire8.InitializeInappReferralConsumerService(epifiCRDB, inAppReferralRedisStore, commsClientVar2, actorClient, rewardsGeneratorClient, inAppReferralClient, usersClient, onboardingClient, segmentationServiceClient, balanceClient, savingsClient, orderServiceClient, referralsNotificationV1Publisher, referralsNotificationV1CustomDelayPublisher, inappreferralGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.AccountStateUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterUpdateRefereeOnbStatusMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.InAppReferralNotificationsSqsCustomDelaySubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterProcessDelayedReferralNotificationEventMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ProcessRewardGenerationEventForSeasonSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralpb.RegisterProcessRewardGenerationEventForSeasonMethodToSubscriber(subscriber, serviceVar14)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar2 := wire8.InitializeNotificationConsumerService(epifiCRDB, inAppReferralRedisStore, actorClient, inAppReferralClient, usersClient, commsClientVar2, onboardingClient, segmentationServiceClient, balanceClient, savingsClient, orderServiceClient, inappreferralGenConf, referralsNotificationV1Publisher, referralsNotificationV1CustomDelayPublisher, rewardsGeneratorClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, inappreferralGenConf.ReferralsNotificationSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		inappreferralnotifpb.RegisterProcessReferralNotificationEventMethodToSubscriber(subscriber, consumerServiceVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.INAPP_REFERRAL_SERVICE)] = &commonexplorer.Config{StaticConf: &inappreferralconf.Config{}, QuestIntegratedConfig: inappreferralGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupReferral(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	commsClientVar2 comms.CommsClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	referralConf, err := referralconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.REFERRAL_SERVICE))
		return nil, nil, err
	}
	_ = referralConf

	serviceVar15 := wire9.InitializeService(referralConf, epifiCRDB, commsClientVar2)

	referral2.RegisterReferralServer(s, serviceVar15)

	configNameToConfMap[cfg.ConfigName(cfg.REFERRAL_SERVICE)] = &commonexplorer.Config{StaticConf: &referralconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupProduct(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	savingsClient savingsclientpb.SavingsClient,
	fireflyClient ffpb.FireflyClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	usersClient user.UsersClient,
	onboardingClient onbpb.OnboardingClient,
	upiOnboardingClient upionbpb.UpiOnboardingClient,
	portfolioManagerClient ussportfoliomanagerpb.PortfolioManagerClient,
	depositClient depositpb.DepositClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	productConf, err := productconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PRODUCT_SERVICE))
		return nil, nil, err
	}
	_ = productConf

	productGenConf, err := dynconf.LoadConfig(productconf.Load, genconf8.NewConfig, cfg.PRODUCT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PRODUCT_SERVICE))
		return nil, nil, err
	}

	_ = productGenConf

	serviceVar16 := wire10.InitialiseProductService(productGenConf, savingsClient, fireflyClient, preApprovedLoanClient, usersClient, onboardingClient, upiOnboardingClient, portfolioManagerClient, depositClient)

	productpb.RegisterProductServer(s, serviceVar16)

	configNameToConfMap[cfg.ConfigName(cfg.PRODUCT_SERVICE)] = &commonexplorer.Config{StaticConf: &productconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupScreener(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	employmentClient employment.EmploymentClient,
	emailParserClient emailparserpb.EmailParserClient,
	authClientVar2 authpb.AuthClient,
	usersClient user.UsersClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient,
	accessInfoClient accessinfo.AccessInfoClient,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	consentClient consentpb.ConsentClient,
	creditReportManagerClient creditreportv.CreditReportManagerClient,
	screenerClient screener2.ScreenerClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	screenerConf, err := screenerconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SCREENER_SERVICE))
		return nil, nil, err
	}
	_ = screenerConf

	screenerGenConf, err := dynconf.LoadConfig(screenerconf.Load, genconf9.NewConfig, cfg.SCREENER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SCREENER_SERVICE))
		return nil, nil, err
	}

	_ = screenerGenConf

	cAPublisher, err := sqs.NewPublisherWithConfig(ctx, screenerGenConf.ConnectedAccountsConfig().SqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	fetchSMSParserDataPublisher, err := sqs.NewPublisherWithConfig(ctx, screenerGenConf.SMSParserConfig().SqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	serviceVar17 := wire11.InitialiseScreenerService(epifiCRDB, screenerConf, screenerGenConf, userIntelServiceClient, employmentClient, emailParserClient, authClientVar2, broker, usersClient, connectedAccountClient, actorClient, groupClient, accessInfoClient, cAPublisher, fetchSMSParserDataPublisher, fennelFeatureStoreClient, scienapticClient, consentClient, creditReportManagerClient)

	screener2.RegisterScreenerServer(s, serviceVar17)

	screenerDevService := wire11.InitialiseScreenerDevService(epifiCRDB)

	screenerdeveloper.RegisterDeveloperServer(s, screenerDevService)

	screenerConsumer := wire11.InitialiseScreenerConsumer(epifiCRDB, screenerGenConf, screenerClient, broker, usersClient)

	screener2.RegisterScreenerConsumerServer(s, screenerConsumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, screenerGenConf.EnquiryConsumerSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		screener2.RegisterGetCACheckDecisionEnquiryMethodToSubscriber(subscriber, screenerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, screenerGenConf.FetchSMSParserDataConsumerSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		screener2.RegisterFetchSMSParserDataMethodToSubscriber(subscriber, screenerConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.SCREENER_SERVICE)] = &commonexplorer.Config{StaticConf: &screenerconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupUser(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	epifiCRDB types.EpifiCRDB,
	kycClient kyc.KycClient,
	customerClient customer.CustomerClient,
	savingsClient savingsclientpb.SavingsClient,
	authClientVar2 authpb.AuthClient,
	vKYCClient vkycpb.VKYCClient,
	depositClientVar2 deposit.DepositClient,
	commsClientVar2 comms.CommsClient,
	userRedisStore types.UserRedisStore,
	userDevicePropertiesRedisStore wiretypes6.UserDevicePropertiesRedisStore,
	minimalUserRedisStore types.MinimalUserRedisStore,
	livenessClient liveness.LivenessClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	screenerClient screener2.ScreenerClient,
	employmentClient employment.EmploymentClient,
	productClient productpb.ProductClient,
	userRueidisCacheStorage types.UserRueidisCacheStorage,
	minimalUserRueidisCacheStorage types.MinimalUserRueidisCacheStorage,
	panClient pan.PanClient,
	userPropertiesPGDB types.UserPropertiesPGDB,
	onboardingClient onbpb.OnboardingClient,
	docExtractionClient kycdocspb.DocExtractionClient,
	userLeadSvcClient leadspb.UserLeadSvcClient,
	cardProvisioningClient cardpb.CardProvisioningClient,
	consentClient consentpb.ConsentClient,
	orderServiceClient orderpb.OrderServiceClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	eKYCClient vgekycpb.EKYCClient,
	userPreferenceClient uppb.UserPreferenceClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	locationClient userlocation.LocationClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	locationClientVar2 locationpb.LocationClient,
	riskClient riskpb.RiskClient,
	employmentFeClient employment.EmploymentFeClient,
	derivedAttributesManagerClient derivedattributespb.DerivedAttributesManagerClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	nudgePGDB types.NudgePGDB,
	watsonClient watson.WatsonClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	balanceClient accountbalancepb.BalanceClient,
	fireflyClient ffpb.FireflyClient,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	pANClient vgpanpb.PANClient,
	creditReportManagerClient creditreportv.CreditReportManagerClient,
	onboardingRueidisCacheStorage types.OnboardingRueidisCacheStorage,
	omegleClient omegle.OmegleClient,
	docsClient vgdocpb.DocsClient,
	onboardingMinRueidisCacheStorage types.OnboardingMinRueidisCacheStorage,
	troubleshootClient vkyccalltroubleshootpb.TroubleshootClient,
	tieringClient tiering.TieringClient,
	livenessClientVar6 vglivenesspb.LivenessClient,
	questCacheStorage types.QuestCacheStorage,
	mFExternalOrdersClient mfpb.MFExternalOrdersClient,
	connectedAccountClient connectedaccountpb.ConnectedAccountClient,
	fireflyV2Client ffv2pb.FireflyV2Client,
	locationClientVar3 locationvgpb.LocationClient,
	ipServiceClient ippb.IpServiceClient,
	userContactRedisStore types.UserContactRedisStore,
	userGroupRueidisCacheStorage types.UserGroupRueidisCacheStorage,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient,
	moEngageClient vgmoengagepb.MoEngageClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	userConf, err := userconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_SERVICE))
		return nil, nil, err
	}
	_ = userConf

	userGenConf, err := dynconf.LoadConfigWithQuestConfig(userconf.Load, genconf10.NewConfigWithQuest, cfg.USER_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		userGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: userGenConf, SdkConfig: userGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{userGenConfAppConfig}, string(cfg.ONBOARDING_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = userGenConf

	shippingAddressUpdatePublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.ShippingAddressUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	processAccessRevokeCooldownDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.ProcessAccessRevokeCooldownPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	syncOnboardingSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.Onboarding().SyncOnboardingSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	afPurchasePublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.AfPurchasePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	eventsCompletedTnCPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.EventsCompletedTnCPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	vpaMigrationConsentPublisher, err := sqs.NewPublisherWithConfig(ctx, userGenConf.VpaMigrationConsentPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	userAccessRevokeUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.UserAccessRevokeUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	userDevicePropertiesUpdatePublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.UserDevicePropertiesUpdatePublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	shippingAddressUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.ShippingAddressUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	deleteUserPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.DeleteUserPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	onboardingStageEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.OnboardingStageEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	consentEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, userGenConf.ConsentEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	usersS3Client := s3pkg.NewClient(awsConf, userGenConf.AWS().S3.UsersBucketName)
	usersS3ClientVar2 := s3pkg.NewClient(awsConf, userGenConf.AWS().S3.UsersBucketName)
	nrS3Client := s3pkg.NewClient(awsConf, userGenConf.Onboarding().NrBucketName())

	serviceVar18 := wire12.InitializeService(epifiCRDB, shippingAddressUpdatePublisher, kycClient, customerClient, savingsClient, usersS3Client, userConf, actorClient, authClientVar2, vKYCClient, depositClientVar2, commsClientVar2, broker, userRedisStore, userDevicePropertiesRedisStore, minimalUserRedisStore, userGenConf, livenessClient, userAccessRevokeUpdatePublisher, userDevicePropertiesUpdatePublisher, bankCustomerServiceClient, shippingAddressUpdateEventPublisher, groupClient, screenerClient, employmentClient, productClient, userRueidisCacheStorage, minimalUserRueidisCacheStorage, panClient, userPropertiesPGDB, deleteUserPublisher, onboardingClient, docExtractionClient, processAccessRevokeCooldownDelayPublisher, userLeadSvcClient)

	user.RegisterUsersServer(s, serviceVar18)

	serviceVar19 := wire12.InitializeAccessRevokeConsumer(userGenConf, epifiCRDB, userRueidisCacheStorage, minimalUserRueidisCacheStorage, processAccessRevokeCooldownDelayPublisher)

	accessrevokeconsumerpb.RegisterConsumerServer(s, serviceVar19)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessAccessRevokeCooldownSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		accessrevokeconsumerpb.RegisterProcessAccessRevokeCooldownMethodToSubscriber(subscriber, serviceVar19)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar20 := wire13.InitializeOnboardingService(userGenConf, epifiCRDB, usersClient, savingsClient, actorClient, cardProvisioningClient, consentClient, authClientVar2, kycClient, broker, vKYCClient, onboardingStageEventPublisher, orderServiceClient, groupClient, syncOnboardingSqsPublisher, commsClientVar2, usersS3ClientVar2, employmentClient, inAppReferralClient, eKYCClient, userPreferenceClient, userConf, uNNameCheckClient, livenessClient, locationClient, obfuscatorClient, locationClientVar2, riskClient, screenerClient, bankCustomerServiceClient, userRedisStore, employmentFeClient, derivedAttributesManagerClient, userIntelServiceClient, nudgePGDB, watsonClient, panClient, operationalStatusServiceClient, balanceClient, fireflyClient, preApprovedLoanClient, productClient, pANClient, creditReportManagerClient, onboardingRueidisCacheStorage, docExtractionClient, omegleClient, docsClient, nrS3Client, onboardingMinRueidisCacheStorage, troubleshootClient, tieringClient, livenessClientVar6, managerClient, questCacheStorage, segmentationServiceClient, mFExternalOrdersClient, connectedAccountClient, userLeadSvcClient, fireflyV2Client)

	onbpb.RegisterOnboardingServer(s, serviceVar20)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.SyncOnboardingSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterSyncOnboardingMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessCardCreationEvent(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessCardCreationEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.UserUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessUserUpdateEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessSavingsAccountUpdateEvent(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessSavingsAccountUpdateEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.LivManualReviewEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessLivManualReviewEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.BankCustomerUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessBankCustomerUpdateEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.CreditReportVerificationEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessCreditReportVerificationEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.InHouseVkycCallCompletedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		onbpb.RegisterProcessInhouseVkycCallCompletedEventMethodToSubscriber(subscriber, serviceVar20)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar21 := wire12.InitializeUserLocationService(userGenConf, epifiCRDB, locationClientVar3, locationClientVar2, obfuscatorClient, ipServiceClient)

	userlocation.RegisterLocationServer(s, serviceVar21)

	serviceVar22 := wire12.InitializeUserObfuscatorService(epifiCRDB)

	obfuscatorpb.RegisterObfuscatorServer(s, serviceVar22)

	userDevService := wire12.InitializeDevUserService(epifiCRDB, kycClient, actorClient, onboardingClient, userRueidisCacheStorage, userDevicePropertiesRedisStore, minimalUserRedisStore, userGenConf, minimalUserRueidisCacheStorage, userPropertiesPGDB, crdbResourceMap)

	developer9.RegisterDevUserServer(s, userDevService)

	serviceVar23 := wire12.InitializeUserContactsService(userGenConf, usersClient, actorClient, userContactRedisStore, userPropertiesPGDB)

	usercontactpb.RegisterContactServer(s, serviceVar23)

	serviceVar24 := wire12.InitializeUserGroupService(userGenConf, epifiCRDB, userGroupRueidisCacheStorage)

	usergrouppb.RegisterGroupServer(s, serviceVar24)

	consumerServiceVar3 := wire12.InitializeEventConsumerService(userConf, broker, afPurchasePublisher, savingsClient, authClientVar2, usersClient, balanceClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EventsAfPurchaseSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		event2.RegisterPublishAfPurchaseMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EventsCompletedTnCSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		event2.RegisterPublishCompletedTnCMethodToSubscriber(subscriber, consumerServiceVar3)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	onboardingUserUpdateVKYCConsumerService := wire13.InitializeOnboardingUserUpdateVKYCConsumerService(usersClient, actorClient, onboardingClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.OnboardingUserUpdateVKYCSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkyc.RegisterOnboardingUserUpdateVKYCConsumerMethodToSubscriber(subscriber, onboardingUserUpdateVKYCConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.VKYCCallCompletedEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkyc.RegisterProcessVKYCCallCompletedEventMethodToSubscriber(subscriber, onboardingUserUpdateVKYCConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	userVKYCUpdateConsumerService := wire12.InitializeUserVKYCUpdateConsumerService(epifiCRDB, savingsClient, commsClientVar2, broker, usersClient, kycClient, vKYCClient, userGenConf, bankCustomerServiceClient, onboardingClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.VKYCUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycconsumer.RegisterUserVKYCUpdateConsumerMethodToSubscriber(subscriber, userVKYCUpdateConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.EKYCSuccessSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycconsumer.RegisterProcessEKYCSuccessEventMethodToSubscriber(subscriber, userVKYCUpdateConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar25 := wire13.InitialiseWatsonClientService(usersClient, onboardingClient)

	onboardingwatsonclientpb.RegisterWatsonServer(s, serviceVar25)

	serviceVar26 := wire12.InitializeDevCacheService(userGenConf, minimalUserRueidisCacheStorage, userRueidisCacheStorage)

	devcache.RegisterDevCacheServer(s, serviceVar26)

	userContactConsumer := wire12.InitializeUserContactConsumer(usersClient, userPropertiesPGDB, vendorMappingServiceClient, moEngageClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessAfuEventUserContactSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessAfuCompletionEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessOnboardingEventUserContactSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessOnboardingCompletionEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, userGenConf.ProcessDeleteUserSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		usercontactpb.RegisterProcessDeleteUserEventMethodToSubscriber(subscriber, userContactConsumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar27 := wire14.InitializeService(crdbResourceMap, userConf, consentEventPublisher, eventsCompletedTnCPublisher, vendorMappingServiceClient, obfuscatorClient, locationClient, vpaMigrationConsentPublisher)

	consentpb.RegisterConsentServer(s, serviceVar27)

	configNameToConfMap[cfg.ConfigName(cfg.USER_SERVICE)] = &commonexplorer.Config{StaticConf: &userconf.Config{}, QuestIntegratedConfig: userGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupUseractions(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	useractionsConf, err := useractionsconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_ACTIONS_SERVICE))
		return nil, nil, err
	}
	_ = useractionsConf

	serviceVar28 := wire15.InitializeUserActionsService()

	useractions2.RegisterUserActionsServer(s, serviceVar28)

	configNameToConfMap[cfg.ConfigName(cfg.USER_ACTIONS_SERVICE)] = &commonexplorer.Config{StaticConf: &useractionsconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupUserintel(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	userSegmentationClient userseg.UserSegmentationClient,
	phoneNetworkClient phonenetwork.PhoneNetworkClient,
	employmentClientVar3 vgemploymentpb.EmploymentClient,
	profileValidationClient profilevalidationpb.ProfileValidationClient,
	userRedisStore types.UserRedisStore,
	onboardingClient onbpb.OnboardingClient,
	incomeEstimatorClient incomeestimator.IncomeEstimatorClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	employmentClient employment.EmploymentClient,
	locationClientVar2 locationpb.LocationClient,
	fennelFeatureStoreClient fennelvgpb.FennelFeatureStoreClient,
	scienapticClient vgscienapticpb.ScienapticClient,
	iTRClient vgitr.ITRClient,
	creditReportManagerClient creditreportv.CreditReportManagerClient,
	lendabilityClient lendabilitypb.LendabilityClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	userintelConf, err := userintelconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_INTEL_SERVICE))
		return nil, nil, err
	}
	_ = userintelConf

	userintelGenConf, err := dynconf.LoadConfig(userintelconf.Load, genconf11.NewConfig, cfg.USER_INTEL_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.USER_INTEL_SERVICE))
		return nil, nil, err
	}

	_ = userintelGenConf

	iTRIntimationS3Client := s3pkg.NewClient(awsConf, userintelGenConf.ITRIntimationBucketName())

	serviceVar29, err := wire16.InitialiseUserIntelService(epifiCRDB, usersClient, actorClient, userSegmentationClient, phoneNetworkClient, employmentClientVar3, profileValidationClient, userRedisStore, onboardingClient, incomeEstimatorClient, inAppReferralClient, employmentClient, locationClientVar2, fennelFeatureStoreClient, scienapticClient, iTRClient, iTRIntimationS3Client, userintelGenConf, creditReportManagerClient, lendabilityClient, broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	userintelpb.RegisterUserIntelServiceServer(s, serviceVar29)

	configNameToConfMap[cfg.ConfigName(cfg.USER_INTEL_SERVICE)] = &commonexplorer.Config{StaticConf: &userintelconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupKyc(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	managerClient managerpb.ManagerClient,
	groupClient usergrouppb.GroupClient,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	cKycClient ckyc.CKycClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	dLClient dl.DLClient,
	idValidateClient idvalidate.IdValidateClient,
	vKYCClient vkycpb.VKYCClient,
	ocrClient inhouseocr.OcrClient,
	epifiCRDB types.EpifiCRDB,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	kycPGDB types.KycPGDB,
	idfcClient vgidfcpb.IdfcClient,
	idfcClientVar2 vgidfcvkycpb.IdfcClient,
	panClient pan.PanClient,
	eKYCClient vgekycpb.EKYCClient,
	authClientVar2 authpb.AuthClient,
	pANClient vgpanpb.PANClient,
	kycNonResidentPGDB wiretypes7.KycNonResidentPGDB,
	kYCRedisStore types.KYCRedisStore,
	kycClient kyc.KycClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	locationClientVar2 locationpb.LocationClient,
	employmentFeClient employment.EmploymentFeClient,
	savingsClient savingsclientpb.SavingsClient,
	locationClient userlocation.LocationClient,
	inAppTargetedCommsClient tcpb.InAppTargetedCommsClient,
	employmentClient employment.EmploymentClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	vkycClient pb.VkycClient,
	balanceClient accountbalancepb.BalanceClient,
	onboardingClient onbpb.OnboardingClient,
	vKYCRueidisCacheStorage wiretypes7.VKYCRueidisCacheStorage,
	omegleClient omegle.OmegleClient,
	docExtractionClient kycdocspb.DocExtractionClient,
	matcherClient matcher.MatcherClient,
	casbinClient casbinpb.CasbinClient,
	uqudoClient kycuqudopb.UqudoClient,
	uqudoClientVar2 vguqudopb.UqudoClient,
	oCRClient ocr.OCRClient,
	commsClientVar12 wiretypes7.KycCommsClientWithInterceptors) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	kycConf, err := kycconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.KYC_SERVICE))
		return nil, nil, err
	}
	_ = kycConf

	kycGenConf, err := dynconf.LoadConfigWithQuestConfig(kycconf.Load, genconf12.NewConfigWithQuest, cfg.KYC_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.KYC_SERVICE))
		return nil, nil, err
	}

	if !conf.QuestSdk.Disable {
		kycGenConfAppConfig := questsdkinit.QuestSdkAppConfig{AppConfig: kycGenConf, SdkConfig: kycGenConf.QuestSdk()}
		questsdkinit.SetupQuestSDK([]questsdkinit.QuestSdkAppConfig{kycGenConfAppConfig}, string(cfg.ONBOARDING_SERVER), managerClient, groupClient, usersClient, actorClient, segmentationServiceClient, broker)
	}

	_ = kycGenConf

	vkycAgentUpdateDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, kycGenConf.VkycAgentUpdatePublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	refreshCallStatusPublisher, err := sqs.NewPublisherWithConfig(ctx, kycGenConf.RefreshCallStatusPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	vKYCUserCommsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, kycGenConf.VKYCUserCommsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	vKYCUserCommsPublisher, err := sqs.NewPublisherWithConfig(ctx, kycGenConf.VKYCUserCommsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	bKYCUpdateEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, kycGenConf.BKYCUpdateEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	vkycCallCompletedEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, kycGenConf.VkycCallCompletedEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	livenessS3Client := s3pkg.NewClient(awsConf, kycGenConf.Aws().S3.BucketNames.BucketLiveness)
	nrS3ClientVar2 := s3pkg.NewClient(awsConf, kycGenConf.NrBucketName())

	kYCService, err := wire17.InitializeService(ctx, kycConf, cKycClient, uNNameCheckClient, kycGenConf, livenessS3Client, broker, dLClient, idValidateClient, vKYCClient, ocrClient, epifiCRDB, bankCustomerServiceClient, usersClient, kycPGDB, idfcClient, idfcClientVar2, panClient, bKYCUpdateEventPublisher, eKYCClient, authClientVar2, actorClient, groupClient, pANClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	kyc.RegisterKycServer(s, kYCService)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.QueueCKYCSearchSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		kyc.RegisterProcessSearchCKYCMethodToSubscriber(subscriber, kYCService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.QueueCKYCDownloadSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		kyc.RegisterProcessDownloadCKYCMethodToSubscriber(subscriber, kYCService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.QueueAccountStateUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		kyc.RegisterProcessAccountStateUpdateEventMethodToSubscriber(subscriber, kYCService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	devKycService := wire17.InitializeDevKycService(ctx, epifiCRDB, kycNonResidentPGDB, kycConf, kYCRedisStore, actorClient, usersClient, kycGenConf, kycClient, salaryProgramClient, vKYCClient, bankCustomerServiceClient, kycPGDB)

	developer11.RegisterDevKYCServer(s, devKycService)

	serviceVar30, err := wire17.InitializeVKYCService(ctx, epifiCRDB, broker, vkycAgentUpdateDelayPublisher, vkycCallCompletedEventPublisher, refreshCallStatusPublisher, kycGenConf, vKYCUserCommsDelayPublisher, vKYCUserCommsPublisher, actorClient, usersClient, kycClient, kYCRedisStore, salaryProgramClient, locationClientVar2, bankCustomerServiceClient, employmentFeClient, savingsClient, locationClient, inAppTargetedCommsClient, kycConf, vKYCClient, panClient, employmentClient, userIntelServiceClient, vkycClient, balanceClient, eKYCClient, onboardingClient, pANClient, groupClient, vKYCRueidisCacheStorage, omegleClient, docExtractionClient, matcherClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vkycpb.RegisterVKYCServer(s, serviceVar30)

	serviceVar31, err := wire17.InitializeVKYCService(ctx, epifiCRDB, broker, vkycAgentUpdateDelayPublisher, vkycCallCompletedEventPublisher, refreshCallStatusPublisher, kycGenConf, vKYCUserCommsDelayPublisher, vKYCUserCommsPublisher, actorClient, usersClient, kycClient, kYCRedisStore, salaryProgramClient, locationClientVar2, bankCustomerServiceClient, employmentFeClient, savingsClient, locationClient, inAppTargetedCommsClient, kycConf, vKYCClient, panClient, employmentClient, userIntelServiceClient, vkycClient, balanceClient, eKYCClient, onboardingClient, pANClient, groupClient, vKYCRueidisCacheStorage, omegleClient, docExtractionClient, matcherClient)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vkycpb.RegisterVKYCFeServer(s, serviceVar31)

	serviceVar32 := wire17.InitialiseKycAgentService(kycGenConf, kycPGDB, actorClient, casbinClient)

	agent.RegisterKycAgentServiceServer(s, serviceVar32)

	docExtractionService := wire17.InitialiseKYCDocExtractionService(kycGenConf, kycNonResidentPGDB, uqudoClient, uqudoClientVar2, oCRClient, onboardingClient, nrS3ClientVar2)

	kycdocspb.RegisterDocExtractionServer(s, docExtractionService)

	serviceVar33 := wire17.InitialiseKYCUqudoService(uqudoClientVar2)

	kycuqudopb.RegisterUqudoServer(s, serviceVar33)

	consumerServiceVar4, err := wire17.InitializeVkycNotificationConsumer(ctx, kycConf, epifiCRDB, actorClient, commsClientVar12, kycGenConf, refreshCallStatusPublisher, vkycClient, vkycAgentUpdateDelayPublisher, kYCRedisStore, salaryProgramClient, vkycCallCompletedEventPublisher, broker, usersClient, vKYCUserCommsDelayPublisher, vKYCUserCommsPublisher, kycClient, bankCustomerServiceClient, onboardingClient, vKYCRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.VKYCUserCommsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		notification3.RegisterProcessNotificationMethodToSubscriber(subscriber, consumerServiceVar4)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	consumerServiceVar5, err := wire17.InitializeKarzaConsumerService(ctx, epifiCRDB, broker, kYCRedisStore, kycClient, salaryProgramClient, vkycCallCompletedEventPublisher, vkycClient, refreshCallStatusPublisher, vkycAgentUpdateDelayPublisher, kycGenConf, kycConf, vKYCUserCommsDelayPublisher, vKYCUserCommsPublisher, actorClient, usersClient, inAppTargetedCommsClient, savingsClient, bankCustomerServiceClient, vKYCClient, onboardingClient, vKYCRueidisCacheStorage)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.FederalVKYCUpdateSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessKYCStateChangeMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.RefreshCallStatusSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterRefreshCallStatusMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.KarzaVKYCCallEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessCallEventMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.KarzaVKYCAgentCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessAgentCallbackMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.KarzaVKYCAuditorCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessAuditorCallbackMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.VKYCAgentUpdateDelaySqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterVKYCAgentUpdateMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.ProcessVKYCOnboardingCompleteEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessVKYCOnboardingCompleteEventMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, kycGenConf.ProcessVKYCTransactionEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		vkycpb.RegisterProcessVKYCTransactionEventMethodToSubscriber(subscriber, consumerServiceVar5)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.KYC_SERVICE)] = &commonexplorer.Config{StaticConf: &kycconf.Config{}, QuestIntegratedConfig: kycGenConf}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupPan(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vkycClient pb.VkycClient,
	usersClient user.UsersClient,
	epifiCRDB types.EpifiCRDB,
	kycPGDB types.KycPGDB,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	celestialClient celestialpb.CelestialClient,
	pANClient vgpanpb.PANClient,
	nsdlClient nsdlvgpb.NsdlClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	panValidationRueidisCacheStorage types.PanValidationRueidisCacheStorage,
	oCRClient ocr.OCRClient,
	actorClient actor.ActorClient,
	groupClient usergrouppb.GroupClient,
	panClient pan.PanClient,
	kycClient kyc.KycClient,
	docsClient vgdocpb.DocsClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	vKYCClient vkycpb.VKYCClient,
	commsClientVar13 types5.PanCommsClientWithInterceptors) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	panConf, err := panconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAN_SERVICE))
		return nil, nil, err
	}
	_ = panConf

	panGenConf, err := dynconf.LoadConfig(panconf.Load, genconf13.NewConfig, cfg.PAN_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.PAN_SERVICE))
		return nil, nil, err
	}

	_ = panGenConf

	ePANCommsDelayPublisher, err := sqs.NewPublisherWithConfig(ctx, panGenConf.EPANCommsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	ePANCommsPublisher, err := sqs.NewPublisherWithConfig(ctx, panGenConf.EPANCommsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	panS3Client := s3pkg.NewClient(awsConf, panGenConf.AWS().S3.PanBucketName)

	serviceVar34 := wire18.InitialisePanService(vkycClient, usersClient, epifiCRDB, kycPGDB, userIntelServiceClient, celestialClient, panS3Client, pANClient, ePANCommsDelayPublisher, ePANCommsPublisher, nsdlClient, obfuscatorClient, uNNameCheckClient, panGenConf, panValidationRueidisCacheStorage, oCRClient, actorClient, groupClient, panConf, panClient, kycClient, docsClient, bankCustomerServiceClient, broker)

	pan.RegisterPanServer(s, serviceVar34)

	panDevService := wire18.InitializePanDevService(epifiCRDB, kycPGDB)

	developer13.RegisterDeveloperServer(s, panDevService)

	serviceVar35 := wire18.InitializePanConsumerService(epifiCRDB, vKYCClient, ePANCommsDelayPublisher, ePANCommsPublisher, actorClient, commsClientVar13, usersClient, panGenConf, kycClient, pANClient, panClient, groupClient, docsClient, bankCustomerServiceClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, panGenConf.EPANCommsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		panconsumer.RegisterProcessEPANCommsMethodToSubscriber(subscriber, serviceVar35)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar36 := wire18.InitializePanConsumerService(epifiCRDB, vKYCClient, ePANCommsDelayPublisher, ePANCommsPublisher, actorClient, commsClientVar13, usersClient, panGenConf, kycClient, pANClient, panClient, groupClient, docsClient, bankCustomerServiceClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, panGenConf.BKYCUpdateEventSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		consumer7.RegisterProcessBKYCUpdateEventMethodToSubscriber(subscriber, serviceVar36)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.PAN_SERVICE)] = &commonexplorer.Config{StaticConf: &panconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupVendordata(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vendordataPGDB types.VendordataPGDB) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	vendordataConf, err := vendordataconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VENDOR_DATA_SERVICE))
		return nil, nil, err
	}
	_ = vendordataConf

	serviceVar37 := wire19.InitialiseIpService(vendordataPGDB)

	ippb.RegisterIpServiceServer(s, serviceVar37)

	configNameToConfMap[cfg.ConfigName(cfg.VENDOR_DATA_SERVICE)] = &commonexplorer.Config{StaticConf: &vendordataconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupShipment(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vendordataPGDB types.VendordataPGDB) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	shipmentConf, err := shipmentconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SHIPMENT_SERVICE))
		return nil, nil, err
	}
	_ = shipmentConf

	shipmentGenConf, err := dynconf.LoadConfig(shipmentconf.Load, genconf14.NewConfig, cfg.SHIPMENT_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.SHIPMENT_SERVICE))
		return nil, nil, err
	}

	_ = shipmentGenConf

	serviceVar38 := wire20.InitialiseShipmentService(shipmentGenConf, vendordataPGDB)

	shipment2.RegisterShipmentServer(s, serviceVar38)

	configNameToConfMap[cfg.ConfigName(cfg.SHIPMENT_SERVICE)] = &commonexplorer.Config{StaticConf: &shipmentconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupRisk(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	fRMCRDB types.FRMCRDB,
	fRMPGDB types.FRMPGDB,
	usersClient user.UsersClient,
	actorClient actor.ActorClient,
	locationClient userlocation.LocationClient,
	locationClientVar2 locationpb.LocationClient,
	authClientVar2 authpb.AuthClient,
	riskClientVar3 vgriskpb.RiskClient,
	inAppReferralClient inappreferralpb.InAppReferralClient,
	livenessClient liveness.LivenessClient,
	kycClient kyc.KycClient,
	savingsClient savingsclientpb.SavingsClient,
	celestialClient celestialpb.CelestialClient,
	redListClient redlist.RedListClient,
	uNNameCheckClient vgncpb.UNNameCheckClient,
	employmentClient employment.EmploymentClient,
	userIntelServiceClient userintelpb.UserIntelServiceClient,
	creditReportManagerClient creditreportv.CreditReportManagerClient,
	commsClientVar2 comms.CommsClient,
	derivedAttributesManagerClient derivedattributespb.DerivedAttributesManagerClient,
	bankCustomerServiceClient bankcust.BankCustomerServiceClient,
	onboardingClient onbpb.OnboardingClient,
	uPIClient upipb.UPIClient,
	operationalStatusServiceClient operationalstatuspb.OperationalStatusServiceClient,
	screenerClient screener2.ScreenerClient,
	tieringClient tiering.TieringClient,
	caseManagementClient casemanagement.CaseManagementClient,
	contactClient usercontactpb.ContactClient,
	userRedisStore types.UserRedisStore,
	preApprovedLoanClient preapprovedloanpb.PreApprovedLoanClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	payClient paypb.PayClient,
	riskClient riskpb.RiskClient,
	transactionRiskDronaPayClient vgtxnmonitoringpb.TransactionRiskDronaPayClient,
	txnRiskScoreServiceClient riskpb.TxnRiskScoreServiceClient,
	piClient pipb.PiClient,
	merchantServiceClient merchant.MerchantServiceClient,
	txnCategorizerClient categorizer.TxnCategorizerClient,
	ticketClient cxticketpb.TicketClient,
	paymentClient paymentpb.PaymentClient,
	profileClient profilepb.ProfileClient,
	orderServiceClient orderpb.OrderServiceClient,
	cRMClient vgcrmpb.CRMClient,
	vendorMappingServiceClient vmpb.VendorMappingServiceClient,
	segmentationServiceClient segmentpb.SegmentationServiceClient,
	productClient productpb.ProductClient,
	salaryProgramClient salaryprogram.SalaryProgramClient,
	bigqueryDBResourceProvider storagev2analytics.BigqueryDBResourceProvider,
	depositClient depositpb.DepositClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	riskConf, err := riskconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RISK_SERVICE))
		return nil, nil, err
	}
	_ = riskConf

	riskGenConf, err := dynconf.LoadConfig(riskconf.Load, genconf15.NewConfig, cfg.RISK_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.RISK_SERVICE))
		return nil, nil, err
	}

	_ = riskGenConf

	leaActorsPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.LeaActorsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	alertsPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.AlertsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	formSubmissionEventPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.FormSubmissionEventPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}
	dronapayRuleHitCallbackPublisher, err := sqs.NewPublisherWithConfig(ctx, riskGenConf.DronapayRuleHitCallbackPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	batchRuleEngineS3Client := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)
	batchRuleEngineS3ClientVar2 := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)
	batchRuleEngineS3ClientVar3 := s3pkg.NewClient(awsConf, riskGenConf.BatchRuleEngineS3().BucketName)

	serviceVar39 := wire21.InitialiseRiskService(epifiCRDB, fRMCRDB, fRMPGDB, usersClient, actorClient, locationClient, locationClientVar2, authClientVar2, riskClientVar3, inAppReferralClient, riskConf, livenessClient, kycClient, savingsClient, celestialClient, redListClient, uNNameCheckClient, employmentClient, userIntelServiceClient, riskGenConf, creditReportManagerClient, commsClientVar2, derivedAttributesManagerClient, bankCustomerServiceClient, onboardingClient, leaActorsPublisher, uPIClient, operationalStatusServiceClient, screenerClient, tieringClient, caseManagementClient, contactClient, userRedisStore, preApprovedLoanClient, obfuscatorClient)

	riskpb.RegisterRiskServer(s, serviceVar39)

	consumer := wire21.InitialiseRiskConsumer(epifiCRDB, fRMPGDB, awsConf, payClient, riskClient, savingsClient, transactionRiskDronaPayClient, txnRiskScoreServiceClient, riskConf, riskGenConf)

	riskpb.RegisterRiskConsumerServer(s, consumer)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRedListUpdateSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		riskpb.RegisterProcessRedListUpdateMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessDisputeUploadSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		riskpb.RegisterProcessDisputeUploadMethodToSubscriber(subscriber, consumer)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar40 := wire21.InitialiseWhiteListService(fRMPGDB)

	whitelistpb.RegisterWhiteListServer(s, serviceVar40)

	serviceVar41 := wire21.InitialiseRedListService(epifiCRDB, fRMPGDB, payClient, riskClient, savingsClient, transactionRiskDronaPayClient, txnRiskScoreServiceClient, riskConf, riskGenConf)

	redlist.RegisterRedListServer(s, serviceVar41)

	riskDevService := wire21.InitializeDevService(epifiCRDB, fRMCRDB, fRMPGDB)

	riskdeveloper.RegisterDeveloperServer(s, riskDevService)

	txnScoreService := wire21.InitialiseTxnRiskScoreService(epifiCRDB, fRMPGDB, riskGenConf)

	riskpb.RegisterTxnRiskScoreServiceServer(s, txnScoreService)

	txnMonitoringService := wire21.InitialiseTxnMonitoringService(epifiCRDB, fRMCRDB, fRMPGDB, locationClientVar2, riskConf, piClient, actorClient, onboardingClient, kycClient, livenessClient, transactionRiskDronaPayClient, usersClient, txnRiskScoreServiceClient, employmentClient, inAppReferralClient, merchantServiceClient, riskGenConf, contactClient, savingsClient, caseManagementClient, bankCustomerServiceClient, txnCategorizerClient, uNNameCheckClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessSyncLeaActorsSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		transactionmonitoring.RegisterSyncLeaActorsMethodToSubscriber(subscriber, txnMonitoringService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	txnMonitoringCallbackService := wire21.InitialiseTxnMonitoringCallbackService(caseManagementClient, riskGenConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RuleHitCallbackSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		transactionmonitoring.RegisterProcessRuleHitCallBackMethodToSubscriber(subscriber, txnMonitoringCallbackService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	caseManagementConsumerService := wire21.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClientVar2, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3Client, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskCasesIngestEventBEQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagement.RegisterAddCasesMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessCallRoutingEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagement.RegisterProcessCallRoutingEventMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessFormSubmissionEventSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagement.RegisterProcessFormSubmissionMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskCXTicketUpdateEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		casemanagement.RegisterProcessCXTicketUpdateEventMethodToSubscriber(subscriber, caseManagementConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	caseManagementConsumerServiceVar2 := wire21.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClientVar2, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3ClientVar2, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskBatchRuleEngineEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterProcessBatchRuleEngineEventMethodToSubscriber(subscriber, caseManagementConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskSignalEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterProcessRiskSignalEventMethodToSubscriber(subscriber, caseManagementConsumerServiceVar2)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	caseManagementConsumerServiceVar3 := wire21.InitialiseCaseManagementConsumer(epifiCRDB, livenessClient, caseManagementClient, riskConf, riskGenConf, commsClientVar2, actorClient, fRMPGDB, fRMCRDB, userRedisStore, alertsPublisher, batchRuleEngineS3ClientVar3, celestialClient, savingsClient, usersClient, ticketClient, paymentClient)

	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskCasesIngestEventDataQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterAddCasesMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskAlertIngestEventDSQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterAddAlertsMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskAlertIngestionQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterAddAlertsMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}
	if func(conf *config.Config) bool {
		return !cfg.IsTestTenantEnabled()
	}(conf) {
		subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessRiskAlertEventQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
			casemanagement.RegisterProcessRiskAlertEventMethodToSubscriber(subscriber, caseManagementConsumerServiceVar3)
		}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
		if err != nil {
			logger.Error(ctx, "consumer init failed", zap.Error(err))
			return nil, nil, err
		}
		if shouldStartWorker {
			sqsSubscribers = append(sqsSubscribers, subs)
		}
	}

	serviceVar42 := wire21.InitialiseProfileService(fRMCRDB, epifiCRDB, riskGenConf, savingsClient, riskConf, operationalStatusServiceClient, contactClient, inAppReferralClient, usersClient, actorClient, celestialClient, fRMPGDB, ticketClient)

	profilepb.RegisterProfileServer(s, serviceVar42)

	profileConsumerService := wire21.InitialiseProfileConsumer(fRMPGDB, epifiCRDB, profileClient, savingsClient, riskClient)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.RiskAccountOperationStatusUpdateQueueSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		profilepb.RegisterProcessAccountOperationStatusUpdateEventMethodToSubscriber(subscriber, profileConsumerService)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	serviceVar43 := wire21.InitialiseCaseManagementService(orderServiceClient, piClient, merchantServiceClient, cRMClient, vendorMappingServiceClient, fRMCRDB, celestialClient, segmentationServiceClient, riskGenConf, userRedisStore, usersClient, productClient, formSubmissionEventPublisher, fRMPGDB, salaryProgramClient, employmentClient, ticketClient, bigqueryDBResourceProvider)

	casemanagement.RegisterCaseManagementServer(s, serviceVar43)

	serviceVar44 := wire21.InitializeDronapayRuleHitCallbackService(dronapayRuleHitCallbackPublisher)

	txnmonitoringdronapaypb.RegisterDronapayCallbackServer(s, serviceVar44)

	serviceVar45 := wire21.InitializeLeaService(fRMPGDB, usersClient, savingsClient, bankCustomerServiceClient, depositClient, commsClientVar2, actorClient, riskGenConf, operationalStatusServiceClient, riskConf, celestialClient)

	leapb.RegisterLeaServer(s, serviceVar45)

	consumerVar2 := wire21.InitialiseMnrlConsumer(fRMPGDB, riskConf)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessMnrlReportSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mnrl.RegisterAddMnrlReportMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, riskGenConf.ProcessMnrlSuspectedFlaggedMobileSqsSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		mnrl.RegisterAddMnrlSuspectedFlaggedMobileMethodToSubscriber(subscriber, consumerVar2)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.RISK_SERVICE)] = &commonexplorer.Config{StaticConf: &riskconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupOmegle(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	verifiPGDB types6.VerifiPGDB,
	vkycClient pb.VkycClient,
	livenessClientVar6 vglivenesspb.LivenessClient,
	oCRClient ocr.OCRClient,
	locationClient userlocation.LocationClient,
	uqudoClient kycuqudopb.UqudoClient,
	locationClientVar3 locationvgpb.LocationClient,
	locationClientVar2 locationpb.LocationClient,
	uNNameCheckClient vgncpb.UNNameCheckClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	omegleConf, err := omegleconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.OMEGLE_SERVICE))
		return nil, nil, err
	}
	_ = omegleConf

	omegleGenConf, err := dynconf.LoadConfig(omegleconf.Load, genconf16.NewConfig, cfg.OMEGLE_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.OMEGLE_SERVICE))
		return nil, nil, err
	}

	_ = omegleGenConf

	agentAndAuditorResponseSqsPublisher, err := sqs.NewPublisherWithConfig(ctx, omegleGenConf.AgentAndAuditorResponseSqsPublisher(), sqsClient, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sqs publisher", zap.Error(err))
		return nil, nil, err
	}

	inHouseVkycCallCompletedEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, omegleGenConf.InHouseVkycCallCompletedEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	callDataStorageS3Client := s3pkg.NewClient(awsConf, omegleGenConf.Aws().S3.CallDataStorageBucketName)
	callDataStorageS3ClientVar2 := s3pkg.NewClient(awsConf, omegleGenConf.Aws().S3.CallDataStorageBucketName)

	serviceVar46 := wire22.InitialiseOmegleService(verifiPGDB, omegleGenConf, callDataStorageS3Client, vkycClient, livenessClientVar6, oCRClient, locationClient, uqudoClient, locationClientVar3, locationClientVar2, agentAndAuditorResponseSqsPublisher, uNNameCheckClient)

	omegle.RegisterOmegleServer(s, serviceVar46)

	matcher.RegisterMatcherServer(s, serviceVar46)

	ocr2.RegisterOCRServer(s, serviceVar46)

	devOmegleService := wire22.InitialiseDevOmegleService(verifiPGDB)

	omegledeveloperpb.RegisterDevOmegleServer(s, devOmegleService)

	serviceVar47 := wire22.InitialiseConsumerService(vkycClient, verifiPGDB, omegleGenConf, callDataStorageS3ClientVar2, inHouseVkycCallCompletedEventPublisher)

	omegleconsumerpb.RegisterConsumerServer(s, serviceVar47)

	subs, shouldStartWorker, err = sqs.NewSubscriberWithGenConfigV1(ctx, omegleGenConf.AgentAndAuditorResponseSubscriber(), sqsClient, queue.NewDefaultMessage(), func(subscriber queue.Subscriber) {
		omegleconsumerpb.RegisterProcessAgentAndAuditorResponseMethodToSubscriber(subscriber, serviceVar47)
	}, redisRateLimiterClient, gconf.GrpcServerConfig(), getSQSConsumerInterceptors()...)
	if err != nil {
		logger.Error(ctx, "consumer init failed", zap.Error(err))
		return nil, nil, err
	}
	if shouldStartWorker {
		sqsSubscribers = append(sqsSubscribers, subs)
	}
	configNameToConfMap[cfg.ConfigName(cfg.OMEGLE_SERVICE)] = &commonexplorer.Config{StaticConf: &omegleconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupTspuser(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	stockGuardianTspPGDB types7.StockGuardianTspPGDB,
	developerClient tspclient.DeveloperClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	tspuserConf, err := tspuserconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.TSP_USER_SERVICE))
		return nil, nil, err
	}
	_ = tspuserConf

	serviceVar48 := wire23.InitialiseTspUserService(stockGuardianTspPGDB)

	tspuserpb.RegisterTspUserServiceServer(s, serviceVar48)

	tspUserDevService := wire23.InitialiseDevTspUserService(stockGuardianTspPGDB, developerClient)

	tspclient.RegisterDeveloperServer(s, tspUserDevService)

	configNameToConfMap[cfg.ConfigName(cfg.TSP_USER_SERVICE)] = &commonexplorer.Config{StaticConf: &tspuserconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupLiveness(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	epifiCRDB types.EpifiCRDB,
	livenessClientVar6 vglivenesspb.LivenessClient,
	caseManagementClient casemanagement.CaseManagementClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	livenessConf, err := livenessconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.LIVENESS_SERVICE))
		return nil, nil, err
	}
	_ = livenessConf

	livenessGenConf, err := dynconf.LoadConfig(livenessconf.Load, genconf17.NewConfig, cfg.LIVENESS_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.LIVENESS_SERVICE))
		return nil, nil, err
	}

	_ = livenessGenConf

	livenessManualReviewPublisher, err := sns.NewSnsPublisherWithConfig(ctx, livenessGenConf.LivenessManualReviewPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}
	livenessSummaryCompletedEventPublisher, err := sns.NewSnsPublisherWithConfig(ctx, livenessGenConf.LivenessSummaryCompletedEventPublisher(), awsConf, queue.NewDefaultMessage())
	if err != nil {
		logger.Error(ctx, "failed to create sns publisher", zap.Error(err))
		return nil, nil, err
	}

	nrS3ClientVar3 := s3pkg.NewClient(awsConf, livenessGenConf.Aws().S3.NrBucketName)

	devLivenessService := wire24.InitializeDevLivenessService(epifiCRDB, livenessConf)

	developer18.RegisterDevLivenessServer(s, devLivenessService)

	serviceVar49 := wire24.InitializeService(broker, epifiCRDB, livenessGenConf, livenessClientVar6, awsConf, livenessManualReviewPublisher, livenessConf, caseManagementClient, livenessSummaryCompletedEventPublisher, nrS3ClientVar3)

	liveness.RegisterLivenessServer(s, serviceVar49)

	configNameToConfMap[cfg.ConfigName(cfg.LIVENESS_SERVICE)] = &commonexplorer.Config{StaticConf: &livenessconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

// nolint: funlen
func setupVkyccall(ctx context.Context, s *grpc.Server, conf *config.Config, gconf *genconf.Config, configNameToConfMap map[cfg.ConfigName]*commonexplorer.Config, awsConf awssdk.Config, crdbResourceMap *storage2.DBResourceProvider[*gorm.DB],
	crdbTxnResourceMap *storage2.DBResourceProvider[storage2.IdempotentTxnExecutor], redisRateLimiterClient *redis.Client, sqsClient *awssqs.Client, broker events.Broker,
	vkycCallRedisStore types8.VkycCallRedisStore,
	vkycCallClient vkycvgpb.VkycCallClient,
	omegleClient omegle.OmegleClient,
	locationClient userlocation.LocationClient,
	locationClientVar2 locationpb.LocationClient,
	obfuscatorClient obfuscatorpb.ObfuscatorClient,
	matcherClient matcher.MatcherClient,
	oCRClientVar4 ocr2.OCRClient,
	oCRClient ocr.OCRClient,
	onboardingClient onbpb.OnboardingClient) ([]queue.Subscriber, []*sqs.ExtendedSqsSubscriber, error) {
	env, err := cfg.GetEnvironment()
	if err != nil {
		logger.Error(ctx, "unable to get env", zap.Error(err))
		return nil, nil, err
	}
	var sqsSubscribers []queue.Subscriber
	var subs queue.Subscriber
	var shouldStartWorker bool
	var extendedSqsSubscribers []*sqs.ExtendedSqsSubscriber
	var extSub *sqs.ExtendedSqsSubscriber
	_, _, _, _, _, _ = sqsSubscribers, subs, shouldStartWorker, extendedSqsSubscribers, extSub, env

	vkyccallConf, err := vkyccallconf.Load()
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VKYC_CALL_SERVICE))
		return nil, nil, err
	}
	_ = vkyccallConf

	vkyccallGenConf, err := dynconf.LoadConfig(vkyccallconf.Load, genconf18.NewConfig, cfg.VKYC_CALL_SERVICE)
	if err != nil {
		logger.Error(ctx, "failed to load config", zap.Error(err), zap.Any("serviceGroup", cfg.VKYC_CALL_SERVICE))
		return nil, nil, err
	}

	_ = vkyccallGenConf

	callDataStorageS3ClientVar3 := s3pkg.NewClient(awsConf, vkyccallGenConf.CallDataStorageBucketName())

	serviceVar50, err := wire25.InitializeVKYCCallService(vkyccallGenConf, vkycCallRedisStore, callDataStorageS3ClientVar3, vkycCallClient, omegleClient, locationClient, locationClientVar2, obfuscatorClient, matcherClient, oCRClientVar4, oCRClient, onboardingClient, broker)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vkyccallpb.RegisterVkycCallServer(s, serviceVar50)

	serviceVar51, err := wire25.InitializeVKYCCallTroubleshootService(vkyccallGenConf, vkycCallRedisStore)
	if err != nil {
		logger.Error(ctx, "failed to init grpc serviceGroup", zap.Error(err))
		return nil, nil, err
	}

	vkyccalltroubleshootpb.RegisterTroubleshootServer(s, serviceVar51)

	devVkycCallService := wire25.InitialiseDevVkycCallService(vkycCallRedisStore, vkyccallGenConf)

	vkyccalldeveloperpb.RegisterDevVkycCallServer(s, devVkycCallService)

	configNameToConfMap[cfg.ConfigName(cfg.VKYC_CALL_SERVICE)] = &commonexplorer.Config{StaticConf: &vkyccallconf.Config{}, QuestIntegratedConfig: nil}

	return sqsSubscribers, extendedSqsSubscribers, nil

}

func initLogger() error {
	// Load configuration
	conf, err := config.Load(cfg.ONBOARDING_SERVER)
	if err != nil {
		// cannot log error since logger is not initialized yet.
		return err
	}

	logger.InitV2(conf.Environment, conf.Logging)
	if conf.SecureLogging != nil {
		logger.InitSecureLoggerV2(conf.SecureLogging)
	}
	return nil
}

func getEnvNamespaceClient(ns temporalpkg.Namespace, env string) string {
	if cfg.IsLocalEnv(env) {
		return strings.ToLower(string(ns))
	}

	return fmt.Sprintf("%s-%s", env, strings.ToLower(string(ns)))
}

func getSQSConsumerInterceptors() []grpc.UnaryServerInterceptor {
	return []grpc.UnaryServerInterceptor{questinterceptor.UserContextInterceptor}
}
