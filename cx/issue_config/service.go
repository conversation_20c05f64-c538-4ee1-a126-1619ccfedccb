package issue_config

import (
	"context"
	"fmt"
	"strings"
	"time"

	s3types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	json "google.golang.org/protobuf/encoding/protojson"

	rpcPb "github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/cache"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"

	icPb "github.com/epifi/gamma/api/cx/issue_config"
	"github.com/epifi/gamma/api/typesv2/webui"
	cxGenConf "github.com/epifi/gamma/cx/config/genconf"
	"github.com/epifi/gamma/cx/issue_category/dao"
	"github.com/epifi/gamma/cx/issue_category/manager"
	dao2 "github.com/epifi/gamma/cx/issue_config/dao"
	cxLogger "github.com/epifi/gamma/cx/logger"
)

const (
	productCategory        = "L1"
	productCategoryDetails = "L2"
	subCategory            = "L3"
	oldValue               = "Old Value"
	newValue               = "New Value"
	updatedBy              = "Updated by"
	approvedBy             = "Approved by"
	updatedOn              = "Updated on"
	dateTimeFormat         = "2006-01-02 15:04:05"
	issueConfigFileName    = "issue_config/config_%s.csv"
)

type IssueConfig struct {
	issueCategoryDao     dao.IIssueCategoryDao
	genConf              *cxGenConf.Config
	issueConfigDao       dao2.IssueConfigDao
	issueCategoryManager manager.IssueCategoryManager
	ConfigFileHelper     ConfigFileHelper
	cxS3Client           s3.S3Client
	cacheStorage         cache.CacheStorage
}

func NewIssueConfig(issueCategoryDao dao.IIssueCategoryDao, genConf *cxGenConf.Config, issueConfigDao dao2.IssueConfigDao,
	issueCategoryManager manager.IssueCategoryManager, configFileHelper ConfigFileHelper, cxS3Client s3.S3Client, cacheStorage cache.CacheStorage) *IssueConfig {
	return &IssueConfig{
		issueCategoryDao:     issueCategoryDao,
		issueCategoryManager: issueCategoryManager,
		genConf:              genConf,
		issueConfigDao:       issueConfigDao,
		ConfigFileHelper:     configFileHelper,
		cxS3Client:           cxS3Client,
		cacheStorage:         cacheStorage,
	}
}

var _ icPb.IssueConfigManagementServer = &IssueConfig{}

func (i *IssueConfig) GetIssueConfig(ctx context.Context, request *icPb.GetIssueConfigRequest) (*icPb.GetIssueConfigResponse, error) {

	switch request.GetResponseType() {
	case icPb.IssueConfigResponseType_ISSUE_CONFIG_RESPONSE_TYPE_TABLE:
		return i.processTableResponse(ctx, request)
	case icPb.IssueConfigResponseType_ISSUE_CONFIG_RESPONSE_TYPE_FILE:
		return i.processFileResponse(ctx, request)
	default:
		cxLogger.Error(ctx, "no response type provided while fetching issue config")
		return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
}

func (i *IssueConfig) processTableResponse(ctx context.Context, request *icPb.GetIssueConfigRequest) (*icPb.GetIssueConfigResponse, error) {
	issueConfigList, pageCtxResp, err := i.fetchPaginatedConfigListByFilter(ctx, request.GetFilter(), request.GetPageContextRequest())
	if err != nil {
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		// Check for validation errors (missing mandatory parameters like product category)
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			cxLogger.Error(ctx, "invalid argument provided while fetching config list by filter", zap.Error(err), zap.Any("filter", request.GetFilter()))
			return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusInvalidArgument()}, nil
		}
		cxLogger.Error(ctx, "failed to fetch config list by filter", zap.Error(err), zap.Any("filter", request.GetFilter()))
		return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusInternal()}, nil
	}

	tableView, err := i.getIssueConfigTable(ctx, issueConfigList)
	if err != nil {
		cxLogger.Error(ctx, "failed to format issue config data into table", zap.Error(err))
		return &icPb.GetIssueConfigResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	return &icPb.GetIssueConfigResponse{
		Status:              rpcPb.StatusOk(),
		PageContextResponse: pageCtxResp,
		IssueConfig: &icPb.GetIssueConfigResponse_Table{
			Table: tableView,
		},
	}, nil
}

func (i *IssueConfig) getIssueConfigTable(ctx context.Context, list []*icPb.IssueConfig) (*webui.Table, error) {
	rows, err := i.getRowsForIssueConfigTable(ctx, list)
	if err != nil {
		return nil, errors.Wrap(err, "failed to fetch table view of issue config")
	}

	return &webui.Table{
		TableHeaders: i.getHeaderForIssueConfigTable(),
		TableRows:    rows,
		TableName:    "Issue config table",
	}, nil
}

func (i *IssueConfig) getRowsForIssueConfigTable(ctx context.Context, list []*icPb.IssueConfig) ([]*webui.TableRow, error) {
	groupedConfigs := make(map[string][]*icPb.IssueConfig)
	for _, config := range list {
		groupedConfigs[config.GetIssueCategoryId()] = append(groupedConfigs[config.GetIssueCategoryId()], config)
	}
	var rows []*webui.TableRow
	for issueCategoryId, configs := range groupedConfigs {
		issueCategory, err := i.issueCategoryManager.GetValueById(ctx, issueCategoryId)
		if err != nil {
			cxLogger.Error(ctx, "failed to fetch L1, L2, L3 for issue category", zap.String(logger.ISSUE_CATEGORY_ID, issueCategoryId), zap.Error(err))
			continue
		}
		currentRow := &webui.TableRow{
			HeaderKeyCellMap: map[string]*webui.TableCell{
				productCategory: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2: &webui.TableCell_StringValue{
						StringValue: issueCategory.GetProductCategory(),
					},
				},
				productCategoryDetails: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2: &webui.TableCell_StringValue{
						StringValue: issueCategory.GetProductCategoryDetails(),
					},
				},
				subCategory: {
					DataType: webui.TableCell_DATA_TYPE_STRING,
					ValueV2: &webui.TableCell_StringValue{
						StringValue: issueCategory.GetSubCategory(),
					},
				},
			},
		}
		for _, c := range configs {
			headerKey := i.genConf.IssueConfigServiceConfig().ConfigTypeMapping().Get(c.GetConfigType().String())
			payloadJson, err := json.Marshal(c.GetConfigPayload())
			if err != nil {
				return nil, errors.Wrap(err, fmt.Sprintf("failed to marshal config payload for %v type in %v issue category id",
					c.GetIssueCategoryId(), c.GetConfigType()))
			}
			currentRow.HeaderKeyCellMap[headerKey] = &webui.TableCell{
				DataType: webui.TableCell_DATA_TYPE_JSON,
				ValueV2: &webui.TableCell_JsonValue{
					JsonValue: string(payloadJson),
				},
			}
		}
		rows = append(rows, currentRow)
	}
	return rows, nil
}

func (i *IssueConfig) getHeaderForIssueConfigTable() []*webui.TableHeader {
	issueConfigTableHeaders := []*webui.TableHeader{
		{
			Label:     productCategory,
			HeaderKey: productCategory,
			IsVisible: true,
		},
		{
			Label:     productCategoryDetails,
			HeaderKey: productCategoryDetails,
			IsVisible: true,
		},
		{
			Label:     subCategory,
			HeaderKey: subCategory,
			IsVisible: true,
		},
	}
	i.genConf.IssueConfigServiceConfig().ConfigTypeMapping().Range(func(key string, value string) (continueRange bool) {
		_, isFound := icPb.ConfigType_value[strings.ToUpper(key)]
		if isFound {
			issueConfigTableHeaders = append(issueConfigTableHeaders, &webui.TableHeader{HeaderKey: value, Label: value, IsVisible: true})
		} else {
			cxLogger.Warn("no mapping found for config type: ", zap.String("ConfigType", strings.ToUpper(key)))
		}
		return true
	})
	return issueConfigTableHeaders
}

func (i *IssueConfig) processFileResponse(ctx context.Context, request *icPb.GetIssueConfigRequest) (*icPb.GetIssueConfigResponse, error) {
	var issueCategoryIds []string
	var err error

	// we support downloading all config values for file response type
	// hence, only apply L1, L2, L3 filter if client has provided L1, L2, L3
	if request.GetFilter() != nil {
		issueCategoryIds, err = i.issueCategoryManager.GetIds(ctx, request.GetFilter().GetCategory(),
			request.GetFilter().GetCategoryDetail(), request.GetFilter().GetSubCategory())
		if err != nil {
			cxLogger.Error(ctx, "failed to fetch issue category ids", zap.Error(err))
			// Check for validation errors (missing mandatory parameters like product category)
			if errors.Is(err, epifierrors.ErrInvalidArgument) {
				return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusInvalidArgument()}, nil
			}
			// as there are no issue category id matching given L1, L2, L3; it implies invalid filters are provided
			if errors.Is(err, epifierrors.ErrRecordNotFound) {
				return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusRecordNotFound()}, nil
			}
			return &icPb.GetIssueConfigResponse{Status: rpcPb.StatusInternal()}, nil
		}
	}

	// passing pageSize as -1 because we don't want any limit on number of responses for file response
	issueConfigList, _, err := i.issueConfigDao.GetConfigByFilters(ctx, issueCategoryIds,
		request.GetFilter().GetConfigType(), nil, -1)
	if err != nil {
		cxLogger.Error(ctx, "failed to fetch issue config", zap.Error(err))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &icPb.GetIssueConfigResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	fileContent, err := i.ConfigFileHelper.GetIssueConfigFilePayload(ctx, issueConfigList)
	if err != nil {
		cxLogger.Error(ctx, "failed to create file from issue config entries", zap.Error(err))
		return &icPb.GetIssueConfigResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	fileName := fmt.Sprintf(issueConfigFileName, time.Now().Format(dateTimeFormat))
	uploadErr := i.cxS3Client.Write(ctx, fileName, []byte(fileContent), string(s3types.ObjectCannedACLBucketOwnerFullControl))
	if uploadErr != nil {
		cxLogger.Error(ctx, "failed to upload file to s3", zap.Error(uploadErr))
		return &icPb.GetIssueConfigResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	fileUrl, err := i.cxS3Client.GetPreSignedUrl(ctx, fileName, 1800*time.Second) // setting expiry to 30 minutes
	if err != nil {
		cxLogger.Error(ctx, "failed to get pre-signed URL for csv file", zap.Error(err))
		return &icPb.GetIssueConfigResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &icPb.GetIssueConfigResponse{
		Status:       rpcPb.StatusOk(),
		ResponseType: icPb.IssueConfigResponseType_ISSUE_CONFIG_RESPONSE_TYPE_FILE,
		IssueConfig:  &icPb.GetIssueConfigResponse_FileUrl{FileUrl: fileUrl},
	}, nil
}

func (i *IssueConfig) UpdateIssueConfig(ctx context.Context, request *icPb.UpdateIssueConfigRequest) (*icPb.UpdateIssueConfigResponse, error) {
	if len(request.GetFileContent()) == 0 || request.GetFileType() != icPb.FileType_FILE_TYPE_CSV {
		cxLogger.Error(ctx, "invalid or no file provided while updating config", zap.String("FileType", request.GetFileType().String()))
		return &icPb.UpdateIssueConfigResponse{Status: rpcPb.StatusInvalidArgument()}, nil
	}
	issueConfigList, err := i.ConfigFileHelper.ReadFile(ctx, request.GetFileContent(), request.GetHeader().GetAgentEmail())
	if err != nil {
		cxLogger.Error(ctx, "failed to process uploaded csv file", zap.Error(err))
		return &icPb.UpdateIssueConfigResponse{
			Status:  rpcPb.StatusInternal(),
			Message: err.Error(),
		}, nil
	}

	// map to maintain config types which are updated by the file upload
	configTypeUpdate := make(map[icPb.ConfigType]bool)
	for _, conf := range issueConfigList {
		_, createErr := i.issueConfigDao.Create(ctx, conf)
		if createErr != nil {
			cxLogger.Error(ctx, "failed to update / create issue config", zap.Error(createErr))
			return &icPb.UpdateIssueConfigResponse{
				Status:  rpcPb.StatusInternal(),
				Message: createErr.Error(),
			}, nil
		}
		configTypeUpdate[conf.GetConfigType()] = true
		time.Sleep(time.Millisecond)
	}

	// success message will contain which all config types are updated
	successMessage := "Issue config updated for config types: "
	for configType, _ := range configTypeUpdate {
		successMessage += i.genConf.IssueConfigServiceConfig().ConfigTypeMapping().Get(configType.String()) + ", "
	}
	return &icPb.UpdateIssueConfigResponse{
		Status:  rpcPb.StatusOk(),
		Message: successMessage,
	}, nil
}

func (i *IssueConfig) GetIssueConfigHistory(ctx context.Context, request *icPb.GetIssueConfigHistoryRequest) (*icPb.GetIssueConfigHistoryResponse, error) {
	if err := validateGetIssueConfigHistoryRequest(request); err != nil {
		cxLogger.Error(ctx, "invalid request provided while fetching issue config issueConfigList", zap.Error(err))
		return &icPb.GetIssueConfigHistoryResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}
	pageToken, err := pagination.GetPageToken(request.GetPageContextRequest())
	if err != nil {
		cxLogger.Error(ctx, "invalid page context request passed", zap.Error(err))
		return &icPb.GetIssueConfigHistoryResponse{
			Status: rpcPb.StatusInvalidArgument(),
		}, nil
	}

	issueCategoryId, err := i.issueCategoryManager.GetId(ctx, request.GetFilter().GetCategory(),
		request.GetFilter().GetCategoryDetail(), request.GetFilter().GetSubCategory())
	if err != nil {
		cxLogger.Error(ctx, "failed to fetch issue category id", zap.Error(err), zap.String("L1", request.GetFilter().GetCategory()),
			zap.String("L2", request.GetFilter().GetCategoryDetail()), zap.String("L3", request.GetFilter().GetSubCategory()))
		// Check for validation errors (missing mandatory parameters like product category)
		if errors.Is(err, epifierrors.ErrInvalidArgument) {
			return &icPb.GetIssueConfigHistoryResponse{
				Status: rpcPb.StatusInvalidArgument(),
			}, nil
		}
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigHistoryResponse{
				Status: rpcPb.StatusRecordNotFound(),
			}, nil
		}
		return &icPb.GetIssueConfigHistoryResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	issueConfigList, pageCtxResp, err := i.issueConfigDao.GetAllVersions(ctx, issueCategoryId, request.GetFilter().GetConfigType(), pageToken, request.GetPageContextRequest().GetPageSize())
	if err != nil {
		cxLogger.Error(ctx, "failed to fetch all version of issue config", zap.Error(err), zap.String(logger.ISSUE_CATEGORY_ID, issueCategoryId))
		if errors.Is(err, epifierrors.ErrRecordNotFound) {
			return &icPb.GetIssueConfigHistoryResponse{Status: rpcPb.StatusRecordNotFound()}, nil
		}
		return &icPb.GetIssueConfigHistoryResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}
	return &icPb.GetIssueConfigHistoryResponse{
		Status:              rpcPb.StatusOk(),
		PageContextResponse: pageCtxResp,
		UpdateHistory:       getHistoryTable(ctx, issueConfigList, request.GetFilter()),
	}, nil
}

func getHistoryTable(ctx context.Context, issueConfigList []*icPb.IssueConfig, configFilter *icPb.IssueConfigFilter) *webui.Table {
	return &webui.Table{
		TableHeaders: getHistoryTableHeader(),
		TableRows:    getHistoryTableRows(ctx, issueConfigList, configFilter),
		TableName:    "Issue Config History",
	}
}

func getHistoryTableHeader() []*webui.TableHeader {
	return []*webui.TableHeader{
		{
			Label:     productCategory,
			HeaderKey: productCategory,
			IsVisible: true,
		},
		{
			Label:     productCategoryDetails,
			HeaderKey: productCategoryDetails,
			IsVisible: true,
		},
		{
			Label:     subCategory,
			HeaderKey: subCategory,
			IsVisible: true,
		},
		{
			Label:     newValue,
			HeaderKey: newValue,
			IsVisible: true,
		},
		{
			Label:     oldValue,
			HeaderKey: oldValue,
			IsVisible: true,
		},
		{
			Label:     updatedBy,
			HeaderKey: updatedBy,
			IsVisible: true,
		},
		{
			Label:     approvedBy,
			HeaderKey: approvedBy,
			IsVisible: true,
		},
		{
			Label:     updatedOn,
			HeaderKey: updatedOn,
			IsVisible: true,
		},
	}
}

//nolint:funlen
func getHistoryTableRows(ctx context.Context, issueConfigList []*icPb.IssueConfig, configFilter *icPb.IssueConfigFilter) []*webui.TableRow {
	var rows []*webui.TableRow
	// we need to populate old value in table, we maintain index for each config type
	// once other version is found for same config type we populate the old value, and also update the map with new index
	lastConfigRowIndex := make(map[icPb.ConfigType]int)
	for _, issueConfig := range issueConfigList {
		jsonPayload, err := json.Marshal(issueConfig.GetConfigPayload())
		// this error is never expected, as before inserting any payload into DB we verify its type
		if err != nil {
			cxLogger.Error(ctx, "invalid payload found in issue config", zap.String(logger.ISSUE_CATEGORY_ID, issueConfig.GetIssueCategoryId()),
				zap.String("ConfigType", issueConfig.GetConfigType().String()))
			continue
		}
		displayTime := issueConfig.GetCreatedAt().AsTime().Format(dateTimeFormat)

		// populate old value if we encountered any version for same config type
		if idx, found := lastConfigRowIndex[issueConfig.GetConfigType()]; found {
			rows[idx].HeaderKeyCellMap[oldValue] = &webui.TableCell{
				DataType: webui.TableCell_DATA_TYPE_JSON,
				ValueV2: &webui.TableCell_JsonValue{
					JsonValue: string(jsonPayload),
				},
			}
		}
		// index for current row will be len(rows) hence updating the same in map
		lastConfigRowIndex[issueConfig.GetConfigType()] = len(rows)

		rows = append(rows,
			&webui.TableRow{
				HeaderKeyCellMap: map[string]*webui.TableCell{
					productCategory: {
						DataType: webui.TableCell_DATA_TYPE_STRING,
						ValueV2: &webui.TableCell_StringValue{
							StringValue: configFilter.GetCategory(),
						},
					},
					productCategoryDetails: {
						DataType: webui.TableCell_DATA_TYPE_STRING,
						ValueV2: &webui.TableCell_StringValue{
							StringValue: configFilter.GetCategoryDetail(),
						},
					},
					subCategory: {
						DataType: webui.TableCell_DATA_TYPE_STRING,
						ValueV2: &webui.TableCell_StringValue{
							StringValue: configFilter.GetSubCategory(),
						},
					},
					newValue: {
						DataType: webui.TableCell_DATA_TYPE_JSON,
						ValueV2: &webui.TableCell_JsonValue{
							JsonValue: string(jsonPayload),
						},
					},
					oldValue: {
						DataType: webui.TableCell_DATA_TYPE_JSON,
						ValueV2: &webui.TableCell_JsonValue{
							JsonValue: string(jsonPayload),
						},
					},
					updatedBy: {
						DataType: webui.TableCell_DATA_TYPE_STRING,
						ValueV2: &webui.TableCell_StringValue{
							StringValue: issueConfig.GetUpdatedBy(),
						},
					},
					updatedOn: {
						DataType: webui.TableCell_DATA_TYPE_STRING,
						ValueV2: &webui.TableCell_StringValue{
							StringValue: displayTime,
						},
					},
				},
			},
		)
	}
	return rows
}

func validateGetIssueConfigHistoryRequest(request *icPb.GetIssueConfigHistoryRequest) error {
	if request.GetFilter().GetCategory() == "" || request.GetFilter().GetCategoryDetail() == "" || request.GetFilter().GetSubCategory() == "" {
		return errors.New("l1, l2, l3 are required fields")
	}
	return nil
}

func (i *IssueConfig) GetIssueConfigFilterOptions(ctx context.Context, request *icPb.GetIssueConfigFilterOptionsRequest) (*icPb.GetIssueConfigFilterOptionsResponse, error) {
	tree, err := i.issueCategoryDao.GetIssueCategoryTree(ctx)
	if err != nil {
		cxLogger.Error(ctx, "failed to generate issue category combination tree", zap.Error(err))
		return &icPb.GetIssueConfigFilterOptionsResponse{
			Status: rpcPb.StatusInternal(),
		}, nil
	}

	configTypeMap := make(map[string]icPb.ConfigType)
	i.genConf.IssueConfigServiceConfig().ConfigTypeMapping().Range(func(key string, value string) (continueRange bool) {
		enum, isFound := icPb.ConfigType_value[strings.ToUpper(key)]
		if isFound {
			configTypeMap[value] = icPb.ConfigType(enum)
		} else {
			cxLogger.Warn("no mapping found for config type: ", zap.String("ConfigType", strings.ToUpper(key)))
		}
		return true
	})

	return &icPb.GetIssueConfigFilterOptionsResponse{
		Status:            rpcPb.StatusOk(),
		IssueCategoryTree: tree,
		ConfigTypeMap:     configTypeMap,
	}, nil
}

func (i *IssueConfig) fetchPaginatedConfigListByFilter(ctx context.Context, filter *icPb.IssueConfigFilter,
	pageCtxReq *rpcPb.PageContextRequest) ([]*icPb.IssueConfig, *rpcPb.PageContextResponse, error) {
	issueCategoryIds, err := i.issueCategoryManager.GetIds(ctx, filter.GetCategory(),
		filter.GetCategoryDetail(), filter.GetSubCategory())
	if err != nil {
		return nil, nil, errors.Wrap(err, "fetch issue category id")
	}

	pageToken, err := pagination.GetPageToken(pageCtxReq)
	if err != nil {
		return nil, nil, errors.Wrap(err, "unmarshal page token")
	}

	issueConfigList, pageCtxResp, err := i.issueConfigDao.GetConfigByFilters(ctx, issueCategoryIds,
		filter.GetConfigType(), pageToken, int64(pageCtxReq.GetPageSize()))
	if err != nil {
		return nil, nil, errors.Wrap(err, "fetch issue config")
	}
	return issueConfigList, pageCtxResp, nil
}
