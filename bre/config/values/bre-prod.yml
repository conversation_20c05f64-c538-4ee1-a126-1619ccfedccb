Application:
  Environment: "prod"
  Name: "bre"
  ServerName: "lending"

Server:
  Ports:
    GrpcPort: 8111
    GrpcSecurePort: 9528
    HttpPort: 9999

AWS:
  Region: "ap-south-1"

Tracing:
  Enable: true

# TODO: Add url once available
Apis:
  LoanDecisioning:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-finalOfferCibilATL"
    AuthToken: "Bearer R2WZ8jdH1smT-a1zLhxeDXd6NLJJh6IKyLKdQRCANO9ACQA0W82d6uffKpkn0UFDmmiXAKuAkHucXEwuRpcaHA=="
  LoanPreScreening:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-preScreenExperianATL"
    AuthToken: "Bearer nyJEG8CRLZo8zCmTHqC4q95XfG5dBOQ2MbbzvvYZjWlCWfkCtm83dp5CyanPdOqBbEnr7D0kubatekImiqAZdw=="
  LoanDecisioningRealtimeSubvention:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-finalOfferCibilPA"
  LoanPreScreeningNonFiCore:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-245882904"
  LoanDecisioningNonFiCore:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-245866728"
  LoanPreBre:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-123564240"
  LoanFinalBre:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-82141344"
  LoanPreBreOffer:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-67c17926f4e8f34e5478863b"
  LoanFinalBreV2:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-67d03026f4e8f34e54788fc2"
  LoanFinalBreV2LDC:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-689c7d01533c3e6c4e3cce38"
  LoanFinalBreV2LDCAndMV:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-689dee7b1406260cef4a0cd7"
  LoanFinalBreV2LDCAndMVAndFed:
    Url: "https://iris.epifi.in/evaluator/flow/v1/flow-68a6ce691406260cef4a1d5d"
  LoanFinalBreV2LDCAndMVAndFedAndSG:
    Url: ""

RawInhouseBreBucketName: "epifi-raw"
InhouseBreBucketName: "epifi-prod-cc-inhouse-bre-responses"

InHouseBreConfig:
  InhouseBreRawS3BucketName: "epifi-raw"
  InhouseBreS3BucketName: "epifi-prod-cc-inhouse-bre-responses"
  InhouseBreRawBucketS3FilePath: "vendor/bre_scienaptic/cc_bre_output/%s/%s-inhouseBreOutputs.csv"
  InhouseBreS3FilePath: "cc/inhouse/bre/%s-inhouseBreResponses.csv"

Secrets:
  Ids:
    BreCredentials: "prod/lending/bre-credentials"


FeatureReleaseConfig:
  FeatureConstraints:
    - FEATURE_LDC_FINAL_BRE_NEW_ENDPOINT:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL
    - FEATURE_LDC_AND_MV_FINAL_BRE_NEW_ENDPOINT:
        StickyPercentageConstraintConfig:
          RolloutPercentage: 100
        UserGroupConstraintConfig:
          AllowedGroups:
            - 1 # INTERNAL

# Rollout Configuration Logic:
  # The BreEndpointRolloutConfig uses a tiered system where users are distributed across different endpoints
  # based on their bucket number (0-99) and threshold values defined in config.
  #
# Example with MONEYVIEW vendor:
  # - LoanFinalBreV2LDCAndMVAndFedAndSG["MV"] = 5  -> users with bucket 0-4 (5% traffic)
  # - LoanFinalBreV2LDCAndMVAndFed["MV"] = 30      -> users with bucket 5-29 (25% traffic)
  # - LoanFinalBreV2LDCAndMV["MV"] = 60            -> users with bucket 30-59 (30% traffic)
  # - LoanFinalBreV2LDC["LDC"] = 80                -> users with bucket 60-79 (20% traffic)
  # - Default LoanFinalBreV2                       -> users with bucket 80-99 (20% traffic)
  #
# The system checks thresholds in descending order of priority:
  # 1. Priority Order of Endpoints - LoanFinalBreV2LDCAndMVAndFedAndSG > LoanFinalBreV2LDCAndMVAndFed > LoanFinalBreV2LDCAndMV > LoanFinalBreV2LDC
  # 2. Next tier endpoint gets users not captured by higher tiers
  # 3. Pattern continues until default endpoint handles remaining traffic
  #
  # This allows gradual rollout of new endpoints with fine-grained traffic control.
BreEndpointRolloutConfig:
  LoanFinalBreV2LDC:
    LDC: 100
  LoanFinalBreV2LDCAndMV:
    LDC: 100
    MV: 100
  LoanFinalBreV2LDCAndMVAndFed:
    LDC: 50
    MV: 50
    FED: 30
  LoanFinalBreV2LDCAndMVAndFedAndSG:
    LDC: 0
    MV: 0
    FED: 0
    SG: 0
