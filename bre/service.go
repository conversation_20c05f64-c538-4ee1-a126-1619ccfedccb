// nolint
package bre

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	awsS3 "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"google.golang.org/protobuf/encoding/protojson"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	commonvgpb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/async/goroutine"
	"github.com/epifi/be-common/pkg/aws/v2/s3"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/events"
	"github.com/epifi/be-common/pkg/logger"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	"github.com/epifi/gamma/api/actor"
	brePb "github.com/epifi/gamma/api/bre"
	ffEnumsPb "github.com/epifi/gamma/api/firefly/enums"
	palPb "github.com/epifi/gamma/api/preapprovedloan"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/user"
	"github.com/epifi/gamma/api/user/group"
	breVgPb "github.com/epifi/gamma/api/vendorgateway/lending/bre"
	breInhousePb "github.com/epifi/gamma/api/vendors/inhouse/bre"
	inhouseBre "github.com/epifi/gamma/api/vendors/inhouse/bre"
	"github.com/epifi/gamma/bre/config"
	breEvents "github.com/epifi/gamma/bre/events"
	"github.com/epifi/gamma/pkg/feature/release"
	httpPkg "github.com/epifi/gamma/pkg/http"
)

type InhouseBreS3Client s3.S3Client
type RawInhouseBreS3Client s3.S3Client

var (
	BreDecisionMap = map[breVgPb.BREDecision]brePb.BREDecision{
		breVgPb.BREDecision_BRE_DECISION_APPROVED:    brePb.BREDecision_BRE_DECISION_APPROVED,
		breVgPb.BREDecision_BRE_DECISION_DECLINED:    brePb.BREDecision_BRE_DECISION_DECLINED,
		breVgPb.BREDecision_BRE_DECISION_UNSPECIFIED: brePb.BREDecision_BRE_DECISION_UNSPECIFIED,
	}
	decisionStringToEnumMap = map[string]brePb.BREDecision{
		"Declined": brePb.BREDecision_BRE_DECISION_DECLINED,
		"Approved": brePb.BREDecision_BRE_DECISION_APPROVED,
	}
)

const (
	inhouseBreCheckResponseCsvHeader = "actor_id,decision,raw_response,failure_reasons,created_at,client_request_id,card_request_stage_id,provenance\n"
	fileNotFound                     = "NoSuchKey"
	creditCardCampaignKey            = "campaign"
	cardIssuer                       = "FEDERAL"
	timestampFormat                  = "2006-01-02T15:04:05.000Z"
	dateFormat                       = "2006-01-02"
	personalLoanProduct              = "PL"
)

type Service struct {
	brePb.UnimplementedBreServer
	httpClient httpPkg.Client
	// HTTP client with TLS verification disabled
	insecureTLSHttpClient httpPkg.Client
	conf                  *config.Config
	breVgClient           breVgPb.BusinessRuleEngineClient
	inhouseBreS3Client    InhouseBreS3Client
	rawInhouseBreS3Client RawInhouseBreS3Client
	userClient            user.UsersClient
	eventBroker           events.Broker
	actorClient           actor.ActorClient
	userGroupClient       group.GroupClient
	releaseEvaluator      *release.Evaluator
}

func NewService(
	httpClient httpPkg.Client,
	conf *config.Config,
	breVgClient breVgPb.BusinessRuleEngineClient,
	inhouseBreS3Client InhouseBreS3Client,
	rawInhouseBreS3Client RawInhouseBreS3Client,
	userClient user.UsersClient,
	eventBroker events.Broker,
	actorClient actor.ActorClient,
	userGroupClient group.GroupClient,
	releaseEvaluator *release.Evaluator,
) *Service {
	return &Service{
		httpClient: httpClient,
		// TODO(mounish): remove this once we have a proper TLS cert for IRIS
		insecureTLSHttpClient: &http.Client{
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
		conf:                  conf,
		breVgClient:           breVgClient,
		inhouseBreS3Client:    inhouseBreS3Client,
		rawInhouseBreS3Client: rawInhouseBreS3Client,
		userClient:            userClient,
		eventBroker:           eventBroker,
		actorClient:           actorClient,
		userGroupClient:       userGroupClient,
		releaseEvaluator:      releaseEvaluator,
	}
}

// TODO(prasoon): move out the http endpoint hitting part similar to how we have for VG so that other RPCs can use the same
// nolint:funlen
func (s *Service) GetLoanDecisioning(ctx context.Context, req *brePb.GetLoanDecisioningRequest) (*brePb.GetLoanDecisioningResponse, error) {
	var res = &brePb.GetLoanDecisioningResponse{}
	pincode, err := strconv.Atoi(req.GetAddress().GetPostalCode())
	if err != nil {
		logger.Error(ctx, "failed to convert postal code from string to int", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var everVkycAttempted float64
	var convErr error
	if req.GetPolicyParams().GetEverVkycAttempted() != "" {
		everVkycAttempted, convErr = strconv.ParseFloat(req.GetPolicyParams().GetEverVkycAttempted(), 64)
		if convErr != nil {
			logger.Error(ctx, "failed to convert everVkycAttempted from string to float64", zap.Error(convErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}

	breReq := &breInhousePb.GetLoanDecisioningRequest{
		Values: &breInhousePb.GetLoanDecisioningRequest_Values{
			Input: &breInhousePb.GetLoanDecisioningRequest_Values_Input{
				ActorId:        req.GetActorId(),
				Name:           req.GetName().ToString(),
				Gender:         getGenderStringByType(req.GetGender()),
				Pan:            req.GetPan(),
				Dob:            fmt.Sprintf("%04d-%02d-%02d", req.GetDob().GetYear(), req.GetDob().GetMonth(), req.GetDob().GetDay()),
				Address:        req.GetAddress().String(),
				Pincode:        int32(pincode),
				EmploymentType: req.GetEmploymentType().String(),
				EmployerName:   req.GetEmployerName(),
				WorkEmail:      req.GetWorkEmail(),
				DeclaredIncome: int32(req.GetMonthlyIncome().GetUnits()),
				LoanProgram:    req.GetLoanProgram().String(),
				SchemeId:       req.GetSchemeId(),
				BatchId:        req.GetBatchId(),
				KycLevel:       req.GetKycLevel().String(),
				PolicyParams: &breInhousePb.GetLoanDecisioningRequest_Values_Input_PolicyParams{
					PdScore:           req.GetPolicyParams().GetPdScore(),
					PricingScheme:     req.GetPolicyParams().GetPricingScheme(),
					EverVkycAttempted: int32(everVkycAttempted),
					PdScoreVersion:    req.GetPolicyParams().GetPdScoreVersion(),
					SchemeId:          req.GetPolicyParams().GetSchemeId(),
					PricingSchemeBre:  req.GetPolicyParams().GetPricingSchemeBre(),
					BatchId:           req.GetPolicyParams().GetBatchId(),
				},
				Vendor: req.GetVendor().String(),
			},
		},
	}
	if req.GetPolicyParams().GetPre() != nil {
		breReq.GetValues().GetInput().GetPolicyParams().Pre = &breInhousePb.GetLoanDecisioningRequest_Values_Input_PolicyParams_Pre{
			PdScore:           req.GetPolicyParams().GetPre().GetPdScore(),
			PdScoreVersion:    req.GetPolicyParams().GetPre().GetPdScoreVersion(),
			SchemeId:          req.GetPolicyParams().GetPre().GetSchemeId(),
			BatchId:           req.GetPolicyParams().GetPre().GetBatchId(),
			EverVkycAttempted: int32(everVkycAttempted),
			PricingScheme:     req.GetPolicyParams().GetPre().GetPricingScheme(),
		}
	}
	if req.GetPolicyParams().GetFinal() != nil {
		breReq.GetValues().GetInput().GetPolicyParams().Final = &breInhousePb.GetLoanDecisioningRequest_Values_Input_PolicyParams_Final{
			SchemeId:         req.GetPolicyParams().GetFinal().GetSchemeId(),
			BatchId:          req.GetPolicyParams().GetFinal().GetBatchId(),
			PricingSchemeBre: req.GetPolicyParams().GetFinal().GetPricingSchemeBre(),
		}
	}

	vendorReq, err := protojson.Marshal(breReq)
	if err != nil {
		logger.Error(ctx, "failed to marshall request into vendor request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	breEndpoint, err := s.getLoanDecisioningEndpoint(req.GetLoanProgram())
	if err != nil {
		logger.Error(ctx, "bre endpoint not configured", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	resp, respErr := s.makeHttpCall(ctx, vendorReq, breEndpoint)
	if respErr != nil {
		logger.Error(ctx, "failed to make http call", zap.Error(respErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	protoRes := breInhousePb.GetLoanDecisioningResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(resp, &protoRes)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall response from inhouse bre", zap.Error(err), zap.Any("breResponse", string(resp)))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if len(protoRes.GetDecision()) < 1 {
		logger.Error(ctx, "decision not received from bre output")
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	decision := protoRes.GetDecision()[0]
	logger.Info(ctx, fmt.Sprintf("got bre decision and rejected reasons: %v, %v", decision.GetAction(), decision.GetReasons()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))

	res.LoanDecision, err = parseDecisionFromString(decision.GetAction())
	if err != nil {
		logger.Error(ctx, "failed to parse decision from string", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	emiDueDate, err := datetime.ParseStringToDateInLocation(dateFormat, decision.GetOfferDetails().GetEmiDueDate(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to parse string to date in location", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	validTill, err := datetime.ParseStringTimestampProtoInLocation(timestampFormat, decision.GetOfferDetails().GetValidTillTimestamp(), datetime.IST)
	if err != nil {
		logger.Error(ctx, "failed to parse string to date in location", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	policyParamsEverVkycAttempted := ""
	if decision.GetPolicyParams().GetEverVkycAttempted() != 0 {
		policyParamsEverVkycAttempted = strconv.Itoa(int(decision.GetPolicyParams().GetEverVkycAttempted()))
	}

	res.ExternalReasons = decision.GetExternalReasons()
	res.OfferDetails = &brePb.GetLoanDecisioningResponse_OfferDetails{
		MinAmount:               moneyPkg.ParseFloat(decision.GetOfferDetails().GetMinAmount(), "INR"),
		MaxAmount:               moneyPkg.ParseFloat(decision.GetOfferDetails().GetMaxAmount(), "INR"),
		MaxEmiAmount:            moneyPkg.ParseFloat(decision.GetOfferDetails().GetMaxEmiAmount(), "INR"),
		InterestPercentage:      decision.GetOfferDetails().GetInterestPercentage(),
		ProcessingFeePercentage: decision.GetOfferDetails().GetProcessingFeePercentage(),
		GstPercentage:           decision.GetOfferDetails().GetGstPercentage(),
		MinTenureInMonths:       decision.GetOfferDetails().GetMinTenureInMonths(),
		MaxTenureInMonths:       decision.GetOfferDetails().GetMaxTenureInMonths(),
		EmiDueDate:              emiDueDate,
		ValidTill:               validTill,
	}
	res.LoanProgram = palPb.LoanProgram(palPb.LoanProgram_value[decision.GetLoanProgram()])
	res.Vendor = palPb.Vendor(palPb.Vendor_value[decision.GetVendor()])
	res.ActorId = decision.GetActorId()
	res.SchemeId = decision.GetSchemeId()
	res.BatchId = decision.GetBatchId()
	res.RawBreResponse = resp
	res.PolicyParams = &palPb.PolicyParams{
		PricingScheme:     decision.GetPolicyParams().GetPricingScheme(),
		EverVkycAttempted: policyParamsEverVkycAttempted,
		PdScore:           decision.GetPolicyParams().GetPdScore(),
		PdScoreVersion:    decision.GetPolicyParams().GetPdScoreVersion(),
		SchemeId:          decision.GetPolicyParams().GetSchemeId(),
		PricingSchemeBre:  decision.GetPolicyParams().GetPricingSchemeBre(),
		BatchId:           decision.GetPolicyParams().GetBatchId(),
	}
	if decision.GetPolicyParams().GetPre() != nil {
		res.PolicyParams.Pre = &palPb.PolicyParams_Pre{
			PdScore:           decision.GetPolicyParams().GetPre().GetPdScore(),
			PdScoreVersion:    decision.GetPolicyParams().GetPre().GetPdScoreVersion(),
			SchemeId:          decision.GetPolicyParams().GetPre().GetSchemeId(),
			BatchId:           decision.GetPolicyParams().GetPre().GetBatchId(),
			EverVkycAttempted: strconv.Itoa(int(decision.GetPolicyParams().GetPre().GetEverVkycAttempted())),
			PricingScheme:     decision.GetPolicyParams().GetPre().GetPricingScheme(),
		}
	}
	if decision.GetPolicyParams().GetFinal() != nil {
		res.PolicyParams.Final = &palPb.PolicyParams_Final{
			SchemeId:         decision.GetPolicyParams().GetFinal().GetSchemeId(),
			BatchId:          decision.GetPolicyParams().GetFinal().GetBatchId(),
			PricingSchemeBre: decision.GetPolicyParams().GetFinal().GetPricingSchemeBre(),
		}
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func parseDecisionFromString(action string) (brePb.Decision, error) {
	switch strings.ToLower(action) {
	case "approved":
		return brePb.Decision_DECISION_APPROVED, nil
	case "declined":
		return brePb.Decision_DECISION_REJECTED, nil
	default:
		return brePb.Decision_DECISION_UNSPECIFIED, errors.New(fmt.Sprintf("action undefined, action: %s", action))
	}
}

func (s *Service) makeHttpCall(ctx context.Context, vendorReq []byte, endpoint *config.Endpoint) ([]byte, error) {
	request, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		endpoint.Url,
		bytes.NewReader(vendorReq))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create http request")
	}
	request.Header.Add("authorization", endpoint.AuthToken)
	request.Header.Add("content-type", "application/json")
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "raw bre request",
		zap.String(logger.PAYLOAD, string(vendorReq)), zap.String(logger.URL, endpoint.Url))
	// TODO(mounish): remove this once we have a proper TLS cert for IRIS and use HttpClient instead
	breRes, err := s.insecureTLSHttpClient.Do(request)
	if err != nil {
		return nil, errors.Wrap(err, "failed to hit internal bre http endpoint")
	}
	defer func() {
		closeErr := breRes.Body.Close()
		if closeErr != nil {
			logger.Error(ctx, "failed to close http res body", zap.Error(closeErr))
		}
	}()
	body, readErr := io.ReadAll(breRes.Body)
	if readErr != nil {
		return nil, errors.Wrap(err, "failed to read response from http response")
	}
	if breRes.StatusCode != 200 {
		logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "bre http api gave non okay response",
			zap.String(logger.URL, endpoint.Url),
			zap.String(logger.REQUEST, string(vendorReq)), zap.String(logger.PAYLOAD, string(body)))
		type breErrorResponse struct {
			Status  int    `json:"status"`
			Error   string `json:"error"`
			Success bool   `json:"success"`
		}
		var breErr breErrorResponse
		if err = json.Unmarshal(body, &breErr); err != nil {
			logger.Error(ctx, "error unmarshalling BRE error response", zap.Error(err),
				zap.String(logger.URL, endpoint.Url), zap.String(logger.REQUEST, string(vendorReq)),
				zap.String(logger.PAYLOAD, string(body)))
		} else {
			logger.Error(ctx, "BRE error", zap.String(logger.URL, endpoint.Url),
				zap.String(logger.REQUEST, string(vendorReq)), zap.String(logger.PAYLOAD, breErr.Error))
		}
		return nil, fmt.Errorf("bre http api gave non okay response, httpStatus: %s, can find exact response in secure logs", breRes.Status)
	}
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "bre response",
		zap.String(logger.PAYLOAD, string(body)), zap.String(logger.URL, endpoint.Url))
	return body, nil
}

func getGenderStringByType(gendor typesPb.Gender) string {
	switch gendor {
	case typesPb.Gender_MALE:
		return "M"
	case typesPb.Gender_FEMALE:
		return "F"
	default:
		return "O"
	}
}

// nolint:funlen
func (s *Service) GetLoanPreScreening(ctx context.Context, req *brePb.GetLoanPreScreeningRequest) (*brePb.GetLoanPreScreeningResponse, error) {
	var res = &brePb.GetLoanPreScreeningResponse{}

	pincode, err := strconv.Atoi(req.GetAddress().GetPostalCode())
	if err != nil {
		logger.Error(ctx, "failed to convert postal code from string to int", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	breReq := &breInhousePb.GetLoanPreScreeningRequest{
		Values: &breInhousePb.GetLoanPreScreeningRequest_Values{
			Input: &breInhousePb.GetLoanPreScreeningRequest_Values_Input{
				ActorId:        req.GetActorId(),
				Name:           req.GetName().ToString(),
				Gender:         getGenderStringByType(req.GetGender()),
				Pan:            req.GetPan(),
				Dob:            fmt.Sprintf("%04d-%02d-%02d", req.GetDob().GetYear(), req.GetDob().GetMonth(), req.GetDob().GetDay()),
				Address:        req.GetAddress().String(),
				Pincode:        int32(pincode),
				EmploymentType: req.GetEmploymentType().String(),
				EmployerName:   req.GetEmployerName(),
				WorkEmail:      req.GetWorkEmail(),
				DeclaredIncome: int32(req.GetMonthlyIncome().GetUnits()),
				LoanProgram:    req.GetLoanProgram().String(),
			},
		},
	}

	vendorReq, err := protojson.Marshal(breReq)
	if err != nil {
		logger.Error(ctx, "failed to marshall request into vendor request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	loanPreScreeningEndpoint, err := s.getPreScreeningEndpoint(req.GetLoanProgram())
	if err != nil {
		logger.Error(ctx, "pre bre endpoint not configured", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	resp, respErr := s.makeHttpCall(ctx, vendorReq, loanPreScreeningEndpoint)
	if respErr != nil {
		logger.Error(ctx, "failed to make http call", zap.Error(respErr), zap.Any("vendor-req", breReq))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	protoRes := breInhousePb.GetLoanPreScreeningResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(resp, &protoRes)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall response from in-house bre", zap.Error(err), zap.Any("resp", resp))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	if len(protoRes.GetDecision()) < 1 {
		logger.Error(ctx, "decision not received from bre output")
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	decision := protoRes.GetDecision()[0]
	logger.Info(ctx, fmt.Sprintf("got pre-screening response: %v, %v", decision.GetAction(), decision.GetReasons()), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
	res.LoanDecision, err = parseDecisionFromString(decision.GetAction())
	if err != nil {
		logger.Error(ctx, "failed to parse decision from string", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	// Logic for EPFO data fetch required or not
	// The contract with credit risk is changed for the new programs, now this epfo requirement will come as part of the GetFinalOfferBreRequirements field
	// this check can be removed once all the programs are migrated to the new contract
	if res.LoanDecision == brePb.Decision_DECISION_APPROVED && req.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION && req.GetLoanProgram() != palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL {
		res.IsEpfoDataNeeded = true
	}
	res.ExternalReasons = decision.GetExternalReasons()
	res.RawBreResponse = resp
	res.SchemeId = decision.GetSchemeId()
	res.BatchId = decision.GetBatchId()
	res.PolicyParams = &palPb.PolicyParams{}
	if decision.GetPolicyParams().GetPre() != nil {
		res.GetPolicyParams().Pre = &palPb.PolicyParams_Pre{
			PdScore:        decision.GetPolicyParams().GetPre().GetPdScore(),
			PdScoreVersion: decision.GetPolicyParams().GetPre().GetPdScoreVersion(),
			SchemeId:       decision.GetPolicyParams().GetPre().GetSchemeId(),
			BatchId:        decision.GetPolicyParams().GetPre().GetBatchId(),
		}
	}
	if decision.GetPolicyParams().GetFinal() != nil {
		res.GetPolicyParams().Final = &palPb.PolicyParams_Final{
			SchemeId:         decision.GetPolicyParams().GetFinal().GetSchemeId(),
			BatchId:          decision.GetPolicyParams().GetFinal().GetBatchId(),
			PricingSchemeBre: decision.GetPolicyParams().GetFinal().GetPricingSchemeBre(),
		}
	}
	if decision.GetFinalOfferBreRequirements() != nil {
		res.FinalOfferBreRequirements = &brePb.GetLoanPreScreeningResponse_FinalOfferBreRequirements{
			Epfo:  decision.GetFinalOfferBreRequirements().GetEpfo(),
			Aa:    decision.GetFinalOfferBreRequirements().GetAa(),
			Cibil: decision.GetFinalOfferBreRequirements().GetCibil(),
		}
	}
	res.Status = rpc.StatusOk()
	return res, nil
}

func (s *Service) InhouseBreCheckForCC(ctx context.Context, req *brePb.InhouseBRECheckForCCRequest) (*brePb.InhouseBRECheckForCCResponse, error) {
	if req.GetActorId() == "" {
		return &brePb.InhouseBRECheckForCCResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty actor id"),
		}, nil
	}

	campaign, err := s.getCampaignNameForActor(ctx, req.GetActorId(), req.GetProvenance())
	if err != nil {
		// making this a non blocking call as it is only for A/B testing
		logger.Error(ctx, "error in getCampaignNameForActor", zap.Error(err))
	}

	vgResp, err := s.breVgClient.InhouseBRECheckForCC(ctx, &breVgPb.InhouseBRECheckForCCRequest{
		Header: &commonvgpb.RequestHeader{
			Vendor: commonvgpb.Vendor_IN_HOUSE,
		},
		ActorId:      req.GetActorId(),
		PinCode:      req.GetPinCode(),
		DateOfBirth:  req.GetDateOfBirth(),
		CampaignName: campaign,
	})
	if grpcErr := epifigrpc.RPCError(vgResp, err); grpcErr != nil {
		logger.Error(ctx, "error in vg response for inhouse bre check", zap.Error(grpcErr), zap.String(logger.ACTOR_ID_V2, req.GetActorId()))
		return &brePb.InhouseBRECheckForCCResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// send acq events for bre stage
	s.sendAcquisitionEventsForBre(ctx, req, vgResp)

	goroutine.RunWithCtx(ctx, func(ctx context.Context) {
		_ = s.writeBreResponseToS3(ctx, vgResp, req.GetActorId(), req.GetClientReqId(), req.GetCardRequestStageId(), req.GetProvenance())
	})

	decision, _ := BreDecisionMap[vgResp.GetResult()]
	return &brePb.InhouseBRECheckForCCResponse{
		Status:        rpc.StatusOk(),
		Result:        decision,
		RawResponse:   vgResp.GetRawResponse(),
		FailureReason: vgResp.GetFailureReason(),
		IsOverride:    vgResp.GetIsOverride(),
	}, nil
}

func (s *Service) GetPreBreEligibilityDetails(ctx context.Context, req *brePb.GetPreBreEligibilityDetailsRequest) (*brePb.GetPreBreEligibilityDetailsResponse, error) {
	if req.GetRequestId() == "" {
		logger.Error(ctx, "empty request id")
		return &brePb.GetPreBreEligibilityDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty request id"),
		}, nil
	}
	cusDetails, err := convertToVendorCustomerDetails(req.GetCustomerDetails())
	if err != nil {
		logger.Error(ctx, "error in converting to vendor customer details", zap.Error(err))
		return &brePb.GetPreBreEligibilityDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	var res = &brePb.GetPreBreEligibilityDetailsResponse{}

	breReq := &breInhousePb.GetPreBreEligibilityDetailsRequest{
		Values: &breInhousePb.GetPreBreEligibilityDetailsRequest_Values{
			Input: &breInhousePb.GetPreBreEligibilityDetailsRequest_Values_Input{
				ActorId:               req.GetActorId(),
				EvaluationRequestTime: time.Now().Format(timestampFormat),
				RequestId:             req.GetRequestId(),
				CustomerDetails:       cusDetails,
				PolicyParams: &breInhousePb.PolicyParams{
					DataInfo: &breInhousePb.DataInfo{
						IsEtbUser: req.GetIsEtbUser(),
					},
				},
			},
		},
	}

	marshalOptions := protojson.MarshalOptions{EmitUnpopulated: false}
	vendorReq, err := marshalOptions.Marshal(breReq)
	if err != nil {
		logger.Error(ctx, "failed to marshall request into vendor request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	preBreEndpoint, err := s.getPreBreEndpoint()
	if err != nil {
		logger.Error(ctx, "pre bre endpoint not configured", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "pre bre request", zap.String(logger.PAYLOAD, string(vendorReq)))

	resp, respErr := s.makeHttpCall(ctx, vendorReq, preBreEndpoint)
	if respErr != nil {
		logger.Error(ctx, "failed to make http call", zap.Error(respErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "pre bre response", zap.String(logger.PAYLOAD, string(resp)))
	breResp := &inhouseBre.GetPreBreEligibilityDetailsResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(resp, breResp)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall response from pre bre", zap.Error(err), zap.Any("breResponse", string(resp)))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var evaluationRunTime, evaluationRequestTime, validTillTime *timestampPb.Timestamp
	var evaluationRunTimeErr, evaluationRequestTimeErr, validTillTimeErr error
	validTillTime, validTillTimeErr = datetime.ParseStringTimeStampProto(timestampFormat, breResp.GetDecision().GetValidTill())
	if validTillTimeErr != nil {
		logger.Error(ctx, "failed to parse string to date in location", zap.Error(validTillTimeErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	evaluationRunTime, evaluationRunTimeErr = datetime.ParseStringTimeStampProto(timestampFormat, breResp.GetDecision().GetEvaluationRunTime())
	if evaluationRunTimeErr != nil {
		// keep it non blocking
		logger.Error(ctx, "failed to parse evaluation run at time", zap.Error(evaluationRunTimeErr))
	}

	evaluationRequestTime, evaluationRequestTimeErr = datetime.ParseStringTimeStampProto(timestampFormat, breResp.GetDecision().GetEvaluationRequestTime())
	if evaluationRequestTimeErr != nil {
		// keep it non blocking
		logger.Error(ctx, "failed to parse evaluation request time", zap.Error(evaluationRequestTimeErr))
	}
	var lenders []palPb.Vendor
	for _, l := range breResp.GetDecision().GetValidLenders() {
		lender, ok := palPb.Vendor_value[l]
		if !ok {
			logger.Error(ctx, "unknown lender", zap.String(logger.VENDOR, l))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		lenders = append(lenders, palPb.Vendor(lender))
	}

	return &brePb.GetPreBreEligibilityDetailsResponse{
		Status: rpc.StatusOk(),
		Decision: &brePb.PreBreDecision{
			ActorId:               breResp.GetDecision().GetActorId(),
			RequestId:             breResp.GetDecision().GetRequestId(),
			EvaluationRunTime:     evaluationRunTime,
			ValidTill:             validTillTime,
			EvaluationRequestTime: evaluationRequestTime,
			NumberValidLenders:    breResp.GetDecision().GetNumberValidLenders(),
			ValidLenders:          lenders,
			PolicyParams:          convertPolicyParamsFromVendor(breResp.GetDecision().GetPolicyParams()),
		},
		RawBreResponse: resp,
	}, nil
}

func (s *Service) GetFinalBreEligibilityDetails(ctx context.Context, req *brePb.GetFinalBreEligibilityDetailsRequest) (*brePb.GetFinalBreEligibilityDetailsResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "empty actor id")
		return &brePb.GetFinalBreEligibilityDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty actor id"),
		}, nil
	}
	if req.GetRequestId() == "" {
		logger.Error(ctx, "empty request id")
		return &brePb.GetFinalBreEligibilityDetailsResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty request id"),
		}, nil
	}

	cusDetails, err := convertToVendorCustomerDetails(req.GetCustomerDetails())
	if err != nil {
		logger.Error(ctx, "error in converting to vendor customer details", zap.Error(err))
		return &brePb.GetFinalBreEligibilityDetailsResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	var res = &brePb.GetFinalBreEligibilityDetailsResponse{}

	breReq := &breInhousePb.GetFinalBreEligibilityDetailsRequest{
		Values: &breInhousePb.GetFinalBreEligibilityDetailsRequest_Values{
			Input: &breInhousePb.GetFinalBreEligibilityDetailsRequest_Values_Input{
				ActorId:               req.GetActorId(),
				RequestId:             req.GetRequestId(),
				EvaluationRequestTime: time.Now().Format(timestampFormat),
				Vendor:                req.GetVendor().String(),
				// TODO: take this as input arg once products are decided
				Product:         req.GetProduct(),
				CustomerDetails: cusDetails,
				DataAvailability: &inhouseBre.DataAvailability{
					Epfo:     convertToVendorDataAvailability(req.GetEpfo()),
					Aa:       convertToVendorDataAvailability(req.GetAa()),
					Cibil:    convertToVendorDataAvailability(req.GetCibil()),
					Experian: convertToVendorDataAvailability(req.GetExperian()),
				},
				PolicyParams: convertPolicyParamsToVendor(req.GetPolicyParams()),
			},
		},
	}
	marshalOptions := protojson.MarshalOptions{EmitUnpopulated: false}
	vendorReq, err := marshalOptions.Marshal(breReq)
	if err != nil {
		logger.Error(ctx, "failed to marshall request into vendor request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	finalBreEndpoint := s.getFinalBreEndpoint(req.GetActorId(), req.GetVendor())
	logger.Info(ctx, "final BRE endpoint in GetFinalBreEligibilityDetails", zap.Any(logger.VENDOR, req.GetVendor().String()), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any(logger.URL, finalBreEndpoint.Url))

	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "final bre request", zap.String(logger.PAYLOAD, string(vendorReq)))
	resp, respErr := s.makeHttpCall(ctx, vendorReq, finalBreEndpoint)
	if respErr != nil {
		logger.Error(ctx, "failed to make http call in GetFinalBreEligibilityDetails to BRE", zap.Any(logger.VENDOR, req.GetVendor().String()), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any(logger.URL, finalBreEndpoint.Url), zap.Error(respErr))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	breResp := &inhouseBre.GetFinalBreEligibilityDetailsResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(resp, breResp)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall response from final bre", zap.Error(err), zap.Any("breResponse", string(resp)))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "final bre response", zap.String(logger.PAYLOAD, string(resp)))

	var evaluationRunAtTime *timestampPb.Timestamp
	var evaluationRunAtTimeErr error
	evaluationRunAtTime, evaluationRunAtTimeErr = datetime.ParseStringTimeStampProto(timestampFormat, breResp.GetEvaluationRunTime())
	if evaluationRunAtTimeErr != nil {
		// keep it non blocking
		logger.Error(ctx, "failed to parse evaluation run at time", zap.Error(evaluationRunAtTimeErr))
	}
	var prioDecision *brePb.FinalBreDecision
	if breResp.GetPrioritizedDecision().GetAction() != "" {
		prioDecision, err = convertDecisionFromVendor(breResp.GetPrioritizedDecision())
		if err != nil {
			logger.Error(ctx, "failed to convert bre decision", zap.Error(err))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
	}
	var decisions []*brePb.FinalBreDecision
	for _, d := range breResp.GetDecision() {
		if d.GetAction() == "" {
			continue
		}
		convDecision, cErr := convertDecisionFromVendor(d)
		if cErr != nil {
			logger.Error(ctx, "failed to convert bre decision", zap.Error(cErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		decisions = append(decisions, convDecision)
	}

	logger.Info(ctx, "Final BRE prioritized decision in GetFinalBreEligibilityDetails", zap.Any(logger.VENDOR, req.GetVendor().String()), zap.Any(logger.ACTOR_ID_V2, req.GetActorId()), zap.Any(logger.URL, finalBreEndpoint.Url), zap.Any(logger.PAYLOAD, prioDecision))
	return &brePb.GetFinalBreEligibilityDetailsResponse{
		Status:                rpc.StatusOk(),
		DataRequirements:      breResp.GetDataRequirements(),
		PolicyParams:          convertPolicyParamsFromVendor(breResp.GetPolicyParams()),
		SubsequentCallAllowed: breResp.GetSubsequentCallAllowed(),
		PrioritizedDecision:   prioDecision,
		Decisions:             decisions,
		EvaluationRunTime:     evaluationRunAtTime,
		RawBreResponse:        resp,
	}, nil
}

func (s *Service) GetPreBreEligibilityOffer(ctx context.Context, req *brePb.GetPreBreEligibilityOfferRequest) (*brePb.GetPreBreEligibilityOfferResponse, error) {
	if req.GetActorId() == "" {
		logger.Error(ctx, "empty actor id")
		return &brePb.GetPreBreEligibilityOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty actor id"),
		}, nil
	}
	if req.GetRequestId() == "" {
		logger.Error(ctx, "empty request id")
		return &brePb.GetPreBreEligibilityOfferResponse{
			Status: rpc.StatusInternalWithDebugMsg("empty request id"),
		}, nil
	}

	cusDetails, err := convertToPreBreEligibilityOfferVendorCustomerDetails(req.GetCustomerDetails())
	if err != nil {
		logger.Error(ctx, "error in converting to vendor customer details", zap.Error(err))
		return &brePb.GetPreBreEligibilityOfferResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	var res = &brePb.GetPreBreEligibilityOfferResponse{}

	breReq := &breInhousePb.GetPreBreEligibilityOfferRequest{
		Values: &breInhousePb.GetPreBreEligibilityOfferRequest_Values{
			Input: &breInhousePb.GetPreBreEligibilityOfferRequest_Values_Input{
				ActorId:               req.GetActorId(),
				RequestId:             req.GetRequestId(),
				EvaluationRequestTime: time.Now().Format(timestampFormat),
				Product:               personalLoanProduct,
				CustomerDetails:       cusDetails,
				DataAvailability: &inhouseBre.DataAvailability{
					Experian: convertToVendorDataAvailability(req.GetExperian()),
				},
				PolicyParams: convertPolicyParamsToVendor(req.GetPolicyParams()),
			},
		},
	}
	marshalOptions := protojson.MarshalOptions{EmitUnpopulated: false}
	vendorReq, err := marshalOptions.Marshal(breReq)
	if err != nil {
		logger.Error(ctx, "failed to marshall request into vendor request", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	preBreOfferEndpoint, err := s.getPreEligibilityBreOfferEndpoint()
	if err != nil {
		logger.Error(ctx, "final bre endpoint not configured", zap.Error(err))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "pre bre eligibility offer request", zap.String(logger.PAYLOAD, string(vendorReq)), zap.String(logger.URL, preBreOfferEndpoint.Url))
	resp, respErr := s.makeHttpCall(ctx, vendorReq, preBreOfferEndpoint)
	if respErr != nil {
		logger.Error(ctx, "failed to make http call", zap.Error(respErr), zap.String(logger.URL, preBreOfferEndpoint.Url))
		res.Status = rpc.StatusInternal()
		return res, nil
	}
	logger.SecureInfo(ctx, commonvgpb.Vendor_VENDOR_UNSPECIFIED, "pre bre eligibility offer response", zap.String(logger.PAYLOAD, string(resp)), zap.String(logger.URL, preBreOfferEndpoint.Url))

	breResp := &inhouseBre.GetPreBreEligibilityOfferResponse{}
	err = protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal(resp, breResp)
	if err != nil {
		logger.Error(ctx, "failed to unmarshall response from final bre", zap.Error(err), zap.Any("breResponse", string(resp)))
		res.Status = rpc.StatusInternal()
		return res, nil
	}

	var evaluationRunAtTime *timestampPb.Timestamp
	var evaluationRunAtTimeErr error
	evaluationRunAtTime, evaluationRunAtTimeErr = datetime.ParseStringTimeStampProto(timestampFormat, breResp.GetEvaluationRunTime())
	if evaluationRunAtTimeErr != nil {
		// keep it non blocking
		logger.Error(ctx, "failed to parse evaluation run at time", zap.Error(evaluationRunAtTimeErr))
	}

	var decisions []*brePb.FinalBreDecision
	for _, d := range breResp.GetDecisions() {
		if d.GetAction() == "" {
			continue
		}
		convDecision, cErr := convertDecisionFromVendor(d)
		if cErr != nil {
			logger.Error(ctx, "failed to convert bre decision", zap.Error(cErr))
			res.Status = rpc.StatusInternal()
			return res, nil
		}
		decisions = append(decisions, convDecision)
	}

	return &brePb.GetPreBreEligibilityOfferResponse{
		Status:                rpc.StatusOk(),
		DataRequirements:      breResp.GetDataRequirements(),
		PolicyParams:          convertPolicyParamsFromVendor(breResp.GetPolicyParams()),
		SubsequentCallAllowed: breResp.GetSubsequentCallAllowed(),
		Decisions:             decisions,
		EvaluationRunTime:     evaluationRunAtTime,
		RawBreResponse:        resp,
	}, nil
}

func (s *Service) getCampaignNameForActor(ctx context.Context, actorId string, provenance brePb.InhouseBreProvenance) (string, error) {
	if provenance != brePb.InhouseBreProvenance_INHOUSE_BRE_PROVENANCE_IN_APP {
		// campaign based A/B testing is required only for app based flows
		return "", nil
	}
	userResp, err := s.userClient.GetUser(ctx, &user.GetUserRequest{
		Identifier: &user.GetUserRequest_ActorId{
			ActorId: actorId,
		},
	})
	if grpcErr := epifigrpc.RPCError(userResp, err); grpcErr != nil {
		logger.Error(ctx, "error in get user", zap.Error(grpcErr))
		return "", grpcErr
	}

	name, ok := userResp.GetUser().GetAcquisitionInfo().GetAttributionDetails().GetAppsflyerAttributionData().AsMap()[creditCardCampaignKey].(string)
	if !ok {
		logger.Info(ctx, "campaign name not found", zap.String(logger.ACTOR_ID_V2, actorId))
		return "", nil
	}

	return name, nil
}

func (s *Service) sendAcquisitionEventsForBre(ctx context.Context, req *brePb.InhouseBRECheckForCCRequest, vgResp *breVgPb.InhouseBRECheckForCCResponse) {
	var (
		originalDescision brePb.BREDecision
		fiLiteProgram     = &typesPb.CardProgram{
			CardProgramVendor: typesPb.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL,
			CardProgramSource: typesPb.CardProgramSource_CARD_PROGRAM_SOURCE_REALTIME_BRE,
			CardProgramType:   typesPb.CardProgramType_CARD_PROGRAM_TYPE_UNSECURED,
			CardProgramOrigin: typesPb.CardProgramOrigin_CARD_PROGRAM_ORIGIN_FI_LITE,
		}
	)
	rawResp := &inhouseBre.InhouseBRECheckForCCResponse{}
	err := protojson.UnmarshalOptions{DiscardUnknown: true, AllowPartial: true}.Unmarshal([]byte(vgResp.GetRawResponse()), rawResp)
	if err != nil {
		// keep it non blocking
		logger.Error(ctx, "error unmarshalling raw response")
	}

	// use original decision if available, else use overridden decision
	if len(rawResp.GetEntities().GetValues().GetStrategy().GetCcEligibilityChecks().GetAction()) > 0 {
		originalDescision = decisionStringToEnumMap[rawResp.GetEntities().GetValues().GetStrategy().GetCcEligibilityChecks().GetAction()]
	} else {
		originalDescision = BreDecisionMap[vgResp.GetResult()]
	}

	if originalDescision == brePb.BREDecision_BRE_DECISION_APPROVED {
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(ctx, breEvents.NewAcqCCEligible(req.GetActorId(), req.GetClientReqId(), "", typesPb.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL.String(), "", ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_IN_HOUSE_BRE_CHECK.String(), fiLiteProgram, vgResp.GetIsOverride().ToBool()))
		})
	}

	if originalDescision == brePb.BREDecision_BRE_DECISION_DECLINED {
		var rejectionReason string
		if len(vgResp.GetFailureReason()) > 0 {
			rejectionReason = vgResp.GetFailureReason()[len(vgResp.GetFailureReason())-1]
		}
		goroutine.Run(ctx, 5*time.Second, func(ctx context.Context) {
			s.eventBroker.AddToBatch(ctx, breEvents.NewAcqCCNotEligible(req.GetActorId(), req.GetClientReqId(), "", typesPb.CardProgramVendor_CARD_PROGRAM_VENDOR_FEDERAL.String(), rejectionReason, ffEnumsPb.CardRequestStageName_CARD_REQUEST_STAGE_NAME_IN_HOUSE_BRE_CHECK.String(), fiLiteProgram, vgResp.GetIsOverride().ToBool()))
		})
	}
}

func (s *Service) writeBreResponseToS3(ctx context.Context, resp *breVgPb.InhouseBRECheckForCCResponse, actorId, clientReqId, cardReqStageId string, provenance brePb.InhouseBreProvenance) error {
	payload, err := serializeBreRespToCsv(resp, clientReqId, actorId, cardReqStageId, provenance)
	if err != nil {
		return err
	}

	if err := s.writeToS3Bucket(ctx, payload, s.conf.InHouseBreConfig.InhouseBreRawBucketS3FilePath, true); err != nil {
		return errors.Wrap(err, "error in writing bre response to raw s3 bucket")
	}

	if err := s.writeToS3Bucket(ctx, payload, s.conf.InHouseBreConfig.InhouseBreS3FilePath, false); err != nil {
		return errors.Wrap(err, "error in writing bre response to s3 bucket")
	}

	return nil
}

func (s *Service) writeToS3Bucket(ctx context.Context, payload string, filePath string, writeInRawBucket bool) error {
	var (
		existingData         []byte
		fileNameWithLocation string
		err                  error
	)

	// Get the current date for the CSV file name
	today := time.Now().Format(dateFormat)

	switch {
	case writeInRawBucket:
		// Create or open the CSV file for the current day in the raw S3 bucket
		fileNameWithLocation = fmt.Sprintf(filePath, today, today)
		existingData, err = s.rawInhouseBreS3Client.ReadObject(ctx, fileNameWithLocation)
	case !writeInRawBucket:
		// Create or open the CSV file for the current day in the main S3 bucket
		fileNameWithLocation = fmt.Sprintf(filePath, today)
		existingData, err = s.inhouseBreS3Client.ReadObject(ctx, fileNameWithLocation)
	}
	if err != nil && !errors.Is(err, epifierrors.ErrRecordNotFound) {
		return fmt.Errorf("failed to read CSV file from S3: %w", err)
	}
	if err != nil && errors.Is(err, epifierrors.ErrRecordNotFound) {
		existingData = []byte(inhouseBreCheckResponseCsvHeader)
	}

	// Append the new data to the existing content
	existingData = append(existingData, []byte(payload)...)

	switch {
	case writeInRawBucket:
		err = s.rawInhouseBreS3Client.Write(ctx, fileNameWithLocation, existingData, string(awsS3.ObjectCannedACLBucketOwnerFullControl))
	case !writeInRawBucket:
		err = s.inhouseBreS3Client.Write(ctx, fileNameWithLocation, existingData, string(awsS3.ObjectCannedACLBucketOwnerFullControl))
	}
	if err != nil {
		return fmt.Errorf("error while writing data to s3 bucket: %w", err)
	}

	return nil
}

func serializeBreRespToCsv(resp *breVgPb.InhouseBRECheckForCCResponse, clientReqId, actorId, cardRequestStageId string, provenance brePb.InhouseBreProvenance) (string, error) {
	var tuple strings.Builder
	writer := csv.NewWriter(&tuple)
	tsString := time.Now().Format("2006-01-02 15:04:05")

	csvHeader := inhouseBreCheckResponseCsvHeader[:len(inhouseBreCheckResponseCsvHeader)-1]

	headerFields := strings.Split(csvHeader, ",")

	fieldValues := make(map[string]string)

	fieldValues["actor_id"] = actorId
	fieldValues["decision"] = resp.GetResult().String()
	fieldValues["raw_response"] = resp.GetRawResponse()
	fieldValues["failure_reasons"] = getFailureReasons(resp.GetFailureReason())
	fieldValues["created_at"] = tsString
	fieldValues["client_request_id"] = clientReqId
	fieldValues["card_request_stage_id"] = cardRequestStageId
	fieldValues["provenance"] = provenance.String()

	row := make([]string, 0)
	for _, field := range headerFields {
		row = append(row, fieldValues[field])
	}

	err := writer.Write(row)
	if err != nil {
		return "", err
	}

	writer.Flush()
	return tuple.String(), nil
}

func getFailureReasons(failures []string) string {
	resp := ""
	for _, failure := range failures {
		resp = resp + failure + "-"
	}
	if len(resp) > 0 {
		resp = resp[0 : len(resp)-1]
	}

	return resp
}

// getLoanDecisioningEndpoint returns the endpoint for a loan program which will be hit to get the fi bre status of the applicant
func (s *Service) getLoanDecisioningEndpoint(program palPb.LoanProgram) (*config.Endpoint, error) {
	switch program {
	case palPb.LoanProgram_LOAN_PROGRAM_REALTIME_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_REALTIME_STPL, palPb.LoanProgram_LOAN_PROGRAM_REAL_TIME_DISTRIBUTION, palPb.LoanProgram_LOAN_PROGRAM_EARLY_SALARY_V2:
		return &config.Endpoint{
			Url:       s.conf.Apis.LoanDecisioningRealtimeSubvention.Url,
			AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningRealtimeSubventionBearerToken,
		}, nil
	case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND, palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return &config.Endpoint{
			Url:       s.conf.Apis.LoanDecisioning.Url,
			AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningBearerToken,
		}, nil
	case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return &config.Endpoint{
			Url:       s.conf.Apis.LoanDecisioningNonFiCore.Url,
			AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningNonFiCoreBearerToken,
		}, nil
	default:
		return nil, fmt.Errorf("bre endpoint not configured for %v", program)
	}
}

// getPreScreeningEndpoint returns the endpoint for a loan program which will be hit to get the fi pre-bre status of the applicant
func (s *Service) getPreScreeningEndpoint(program palPb.LoanProgram) (*config.Endpoint, error) {
	switch program {
	case palPb.LoanProgram_LOAN_PROGRAM_ACQ_TO_LEND, palPb.LoanProgram_LOAN_PROGRAM_FI_LITE_PL:
		return &config.Endpoint{
			Url:       s.conf.Apis.LoanPreScreening.Url,
			AuthToken: "Bearer " + s.conf.BreCredentials.LoanPreScreeningBearerToken,
		}, nil
	case palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_SUBVENTION, palPb.LoanProgram_LOAN_PROGRAM_NON_FI_CORE_STPL:
		return &config.Endpoint{
			Url:       s.conf.Apis.LoanPreScreeningNonFiCore.Url,
			AuthToken: "Bearer " + s.conf.BreCredentials.LoanPreScreeningNonFiCoreBearerToken,
		}, nil
	default:
		return nil, fmt.Errorf("bre endpoint not configured for %v", program)
	}
}

// getPreBreEndpoint returns the endpoint which will be hit to get the fi pre-bre status of the applicant common to all vendors
func (s *Service) getPreBreEndpoint() (*config.Endpoint, error) {
	return &config.Endpoint{
		Url:       s.conf.Apis.LoanPreBre.Url,
		AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningPreBreBearerToken,
	}, nil
}

// getPreEligibilityBreOfferEndpoint returns the endpoint which will be hit to get the fi final-bre status of the applicant specific to vendors
func (s *Service) getPreEligibilityBreOfferEndpoint() (*config.Endpoint, error) {
	return &config.Endpoint{
		Url:       s.conf.Apis.LoanPreBreOffer.Url,
		AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningPreBreOfferBearerToken,
	}, nil
}

// rolloutThreshold represents a rollout configuration with threshold and corresponding endpoint
type rolloutThreshold struct {
	threshold int
	url       string
	token     string
}

// getFinalBreEndpoint returns the endpoint for final BRE based on the vendor using tiered rollout configuration
func (s *Service) getFinalBreEndpoint(actorId string, vendor palPb.Vendor) *config.Endpoint {
	bucketNumber := int(events.GetUserLayerBucket(actorId))

	// Define rollout configurations for each vendor
	vendorConfigs := map[palPb.Vendor][]rolloutThreshold{
		palPb.Vendor_STOCK_GUARDIAN_LSP: {
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFedAndSG["SG"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFedAndSG.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedAndSGBearerToken,
			},
		},
		palPb.Vendor_FEDERAL: {
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFedAndSG["FED"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFedAndSG.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedAndSGBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFed["FED"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFed.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedBearerToken,
			},
		},
		palPb.Vendor_MONEYVIEW: {
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFedAndSG["MV"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFedAndSG.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedAndSGBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFed["MV"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFed.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMV["MV"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMV.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVBearerToken,
			},
		},
		palPb.Vendor_LENDEN: {
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFedAndSG["LDC"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFedAndSG.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedAndSGBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMVAndFed["LDC"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMVAndFed.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVAndFedBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDCAndMV["LDC"],
				url:       s.conf.Apis.LoanFinalBreV2LDCAndMV.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCandMVBearerToken,
			},
			{
				threshold: s.conf.BreEndpointRolloutConfig.LoanFinalBreV2LDC["LDC"],
				url:       s.conf.Apis.LoanFinalBreV2LDC.Url,
				token:     s.conf.BreCredentials.LoanDecisioningFinalBreV2LDCBearerToken,
			},
		},
	}

	// Get rollout configs for the vendor, fallback to empty slice if vendor not found
	rollouts, exists := vendorConfigs[vendor]
	if !exists {
		return s.getDefaultFinalBreEndpoint()
	}

	// Find the first threshold that the bucket number is less than
	for _, rollout := range rollouts {
		if bucketNumber < rollout.threshold {
			return &config.Endpoint{
				Url:       rollout.url,
				AuthToken: "Bearer " + rollout.token,
			}
		}
	}

	// If no threshold matched, return default endpoint
	return s.getDefaultFinalBreEndpoint()
}

// getDefaultFinalBreEndpoint returns the default final BRE endpoint
func (s *Service) getDefaultFinalBreEndpoint() *config.Endpoint {
	return &config.Endpoint{
		Url:       s.conf.Apis.LoanFinalBreV2.Url,
		AuthToken: "Bearer " + s.conf.BreCredentials.LoanDecisioningFinalBreV2BearerToken,
	}
}

func convertToVendorCustomerDetails(customerDetails *brePb.CustomerDetails) (*breInhousePb.CustomerDetails, error) {
	workPincode := 0
	var err error
	if customerDetails.GetEmploymentDetails().GetWorkAddress().GetPostalCode() != "" {
		workPincode, err = strconv.Atoi(customerDetails.GetEmploymentDetails().GetWorkAddress().GetPostalCode())
		if err != nil {
			return nil, errors.Wrap(err, "error in converting work pincode to string")
		}
	}
	pincode := 0
	if customerDetails.GetResidentialAddress().GetPostalCode() != "" {
		pincode, err = strconv.Atoi(customerDetails.GetResidentialAddress().GetPostalCode())
		if err != nil {
			return nil, errors.Wrap(err, "error in converting pincode to string")
		}
	}
	return &breInhousePb.CustomerDetails{
		PersonalDetails: &breInhousePb.PersonalDetails{
			Dob:         datetime.DateToString(customerDetails.GetPersonalDetails().GetDob(), dateFormat, datetime.IST),
			Gender:      getGenderStringByType(customerDetails.GetPersonalDetails().GetGender()),
			Name:        customerDetails.GetPersonalDetails().GetName().ToString(),
			Pan:         customerDetails.GetPersonalDetails().GetPan(),
			PhoneNumber: "",
			Email:       "",
		},
		EmploymentDetails: &inhouseBre.EmploymentDetails{
			EmployerName:          customerDetails.GetEmploymentDetails().GetEmployerName(),
			EmploymentType:        customerDetails.GetEmploymentDetails().GetEmploymentType().String(),
			DeclaredMonthlyIncome: int32(customerDetails.GetEmploymentDetails().GetMonthlyIncome().GetUnits()),
			WorkEmail:             customerDetails.GetEmploymentDetails().GetWorkEmail(),
			WorkAddress:           customerDetails.GetEmploymentDetails().GetWorkAddress().String(),
			WorkPincode:           int32(workPincode),
		},
		AddressDetails: &inhouseBre.AddressDetails{
			Address:     customerDetails.GetResidentialAddress().String(),
			Pincode:     int32(pincode),
			AddressType: "",
		},
		RequestedLoanDetails: &inhouseBre.RequestedLoanDetails{
			DesiredLoanAmount: float64(customerDetails.GetRequestedLoanDetails().GetDesiredLoanAmount().GetUnits()),
		},
	}, nil
}

func convertToPreBreEligibilityOfferVendorCustomerDetails(customerDetails *brePb.CustomerDetails) (*breInhousePb.CustomerDetails, error) {
	pincode, err := strconv.Atoi(customerDetails.GetResidentialAddress().GetPostalCode())
	if err != nil {
		return nil, errors.Wrap(err, "error in converting pincode to string")
	}
	return &breInhousePb.CustomerDetails{
		PersonalDetails: &breInhousePb.PersonalDetails{
			Dob:  datetime.DateToString(customerDetails.GetPersonalDetails().GetDob(), dateFormat, datetime.IST),
			Name: customerDetails.GetPersonalDetails().GetName().ToString(),
			Pan:  customerDetails.GetPersonalDetails().GetPan(),
		},
		EmploymentDetails: &inhouseBre.EmploymentDetails{
			EmploymentType:        customerDetails.GetEmploymentDetails().GetEmploymentType().String(),
			DeclaredMonthlyIncome: int32(customerDetails.GetEmploymentDetails().GetMonthlyIncome().GetUnits()),
		},
		AddressDetails: &inhouseBre.AddressDetails{
			Pincode: int32(pincode),
		},
	}, nil
}

func convertPolicyParamsFromVendor(policyParams *breInhousePb.PolicyParams) *palPb.PolicyParams {
	var pre []*palPb.PolicyParamsDetails
	for _, p := range policyParams.GetExecutionInfo().GetPre() {
		pre = append(pre, convertPolicyParamsDetailsFromVendor(p))
	}
	var final []*palPb.PolicyParamsDetails
	for _, f := range policyParams.GetExecutionInfo().GetFinal() {
		final = append(final, convertPolicyParamsDetailsFromVendor(f))
	}

	return &palPb.PolicyParams{
		ExecutionInfo: &palPb.ExecutionInfo{
			Pre:   pre,
			Final: final,
		},
		DataInfo: &palPb.DataInfo{
			AaData: &palPb.DataInfo_AaData{
				MedianAmountSalaryLast_180Days: policyParams.GetDataInfo().GetAaData().GetMedianAmountSalaryLast_180Days(),
			},
			IsEtbUser:               policyParams.GetDataInfo().GetIsEtbUser(),
			IsB2BSalaryUser:         policyParams.GetDataInfo().GetIsB2BSalaryUser(),
			MonthlyIncome:           policyParams.GetDataInfo().GetMonthlyIncome(),
			SalaryCreditDay:         policyParams.GetDataInfo().GetSalaryCreditDay(),
			MonthsSinceSalaryActive: policyParams.GetDataInfo().GetMonthsSinceSalaryActive(),
		},
	}
}

func convertPolicyParamsToVendor(policyParams *palPb.PolicyParams) *breInhousePb.PolicyParams {
	var pre []*breInhousePb.PolicyPramsDetails
	for _, p := range policyParams.GetExecutionInfo().GetPre() {
		pre = append(pre, convertPolicyParamsDetailsToVendor(p))
	}
	var final []*breInhousePb.PolicyPramsDetails
	for _, f := range policyParams.GetExecutionInfo().GetFinal() {
		final = append(final, convertPolicyParamsDetailsToVendor(f))
	}

	return &breInhousePb.PolicyParams{
		ExecutionInfo: &breInhousePb.ExecutionInfo{
			Pre:   pre,
			Final: final,
		},
		DataInfo: &breInhousePb.DataInfo{
			AaData: &breInhousePb.DataInfo_AaData{
				MedianAmountSalaryLast_180Days: policyParams.GetDataInfo().GetAaData().GetMedianAmountSalaryLast_180Days(),
			},
			IsEtbUser:               policyParams.GetDataInfo().GetIsEtbUser(),
			IsB2BSalaryUser:         policyParams.GetDataInfo().GetIsB2BSalaryUser(),
			MonthlyIncome:           policyParams.GetDataInfo().GetMonthlyIncome(),
			MonthsSinceSalaryActive: policyParams.GetDataInfo().GetMonthsSinceSalaryActive(),
			SalaryCreditDay:         policyParams.GetDataInfo().GetSalaryCreditDay(),
		},
	}
}

func convertPolicyParamsDetailsToVendor(policyParamDetails *palPb.PolicyParamsDetails) *breInhousePb.PolicyPramsDetails {
	return &breInhousePb.PolicyPramsDetails{
		BatchId:           policyParamDetails.GetBatchId(),
		SchemeId:          policyParamDetails.GetSchemeId(),
		LendingProgram:    policyParamDetails.GetLendingProgram(),
		PreBreRequestId:   policyParamDetails.GetPreBreRequestId(),
		FinalBreRequestId: policyParamDetails.GetFinalBreRequestId(),
	}
}

func convertPolicyParamsDetailsFromVendor(policyParamDetails *breInhousePb.PolicyPramsDetails) *palPb.PolicyParamsDetails {
	return &palPb.PolicyParamsDetails{
		BatchId:           policyParamDetails.GetBatchId(),
		SchemeId:          policyParamDetails.GetSchemeId(),
		LendingProgram:    policyParamDetails.GetLendingProgram(),
		PreBreRequestId:   policyParamDetails.GetPreBreRequestId(),
		FinalBreRequestId: policyParamDetails.GetFinalBreRequestId(),
	}
}

func convertToVendorDataAvailability(dataAvailability *brePb.DataAvailability) *breInhousePb.DataAvailability_DataAvailabilityDetails {
	return &inhouseBre.DataAvailability_DataAvailabilityDetails{
		IsAvailable:    dataAvailability.GetIsAvailable(),
		CollectionDate: datetime.DateToString(dataAvailability.GetCollectionDate(), dateFormat, datetime.IST),
	}
}

func convertDecisionFromVendor(vDecision *breInhousePb.FinalBreDecision) (*brePb.FinalBreDecision, error) {
	var decision brePb.Decision
	switch vDecision.GetAction() {
	case "Approved":
		decision = brePb.Decision_DECISION_APPROVED
	case "Declined":
		decision = brePb.Decision_DECISION_REJECTED
	default:
		return nil, fmt.Errorf("unknown decision action: %v", vDecision.GetAction())
	}
	validTill, err := datetime.ParseStringTimeStampProto(timestampFormat, vDecision.GetValidTill())
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse valid till")
	}
	res := &brePb.FinalBreDecision{
		LendingProgram: vDecision.GetLendingProgram(),
		Decision:       decision,
		ValidTill:      validTill,
	}
	if vDecision.GetOfferDetails() != nil {
		res.OfferDetails = &brePb.OfferDetails{
			MaxAmount:               moneyPkg.ParseFloat(vDecision.GetOfferDetails().GetMaxAmount(), "INR"),
			MinAmount:               moneyPkg.ParseFloat(vDecision.GetOfferDetails().GetMinAmount(), "INR"),
			MaxEmiAmount:            moneyPkg.ParseFloat(vDecision.GetOfferDetails().GetMaxEmiAmount(), "INR"),
			MaxTenureInMonths:       vDecision.GetOfferDetails().GetMaxTenureInMonths(),
			MinTenureInMonths:       vDecision.GetOfferDetails().GetMinTenureInMonths(),
			InterestPercentage:      vDecision.GetOfferDetails().GetInterestPercentage(),
			ProcessingFeePercentage: vDecision.GetOfferDetails().GetProcessingFeePercentage(),
			GstPercentage:           vDecision.GetOfferDetails().GetGstPercentage(),
			ValidTill:               validTill,
		}
		if vDecision.GetOfferDetails().GetEmiDueDate() != "" {
			emiDueDate := datetime.DateFromString(vDecision.GetOfferDetails().GetEmiDueDate())
			if emiDueDate == nil {
				return nil, fmt.Errorf("failed to parse emi due date: %v", vDecision.GetOfferDetails().GetEmiDueDate())
			}
			res.OfferDetails.EmiDueDate = emiDueDate
		}
	}
	return res, nil
}
