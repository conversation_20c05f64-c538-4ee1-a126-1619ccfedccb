package stages

import (
	"github.com/pkg/errors"
	"go.temporal.io/sdk/workflow"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	activityPb "github.com/epifi/be-common/api/celestial/activity"
	"github.com/epifi/be-common/pkg/epificontext"
	activityPkg "github.com/epifi/be-common/pkg/epifitemporal/activity"
	preApprovedLoanNs "github.com/epifi/be-common/pkg/epifitemporal/namespace/preapprovedloan"
	palpb "github.com/epifi/gamma/api/preapprovedloan"
	palActivityPb "github.com/epifi/gamma/api/preapprovedloan/activity"
)

type CommonStepProcessor struct {
}

func (s *CommonStepProcessor) PreProcess(ctx workflow.Context, req *PreProcessRequest) (*PreProcessResponse, error) {
	actReq := &palActivityPb.CreateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		StepName:   req.StageName,
		Flow:       req.FlowName,
		GroupStage: req.GroupStage,
	}
	actRes := &palActivityPb.CreateLoanStepExecutionResponse{}
	err := activityPkg.Execute(ctx, preApprovedLoanNs.CreateLoanStepExecution, actRes, actReq)
	if err != nil {
		return nil, err
	}
	// TODO(Brijesh): Discuss if we need other activities like UpdateLRNextAction and EmitEvent
	return &PreProcessResponse{loanStep: actRes.GetLoanStep()}, nil
}

func (s *CommonStepProcessor) PostProcess(ctx workflow.Context, req *PostProcessRequest) (*PostProcessResponse, error) {
	if req.Request.GetLoanStep() == nil {
		return nil, errors.New("loan step is nil")
	}
	if req.Request.GetLoanStep().Status == palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_SUCCESS ||
		req.Request.GetLoanStep().Status == palpb.LoanStepExecutionStatus_LOAN_STEP_EXECUTION_STATUS_FAILED {
		req.Request.GetLoanStep().CompletedAt = timestampPb.Now()
		req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_STATUS)
		req.Request.LseFieldMasks = append(req.Request.GetLseFieldMasks(), palpb.LoanStepExecutionFieldMask_LOAN_STEP_EXECUTION_FIELD_MASK_COMPLETED_AT)
	}
	actReq := &palActivityPb.UpdateLoanStepExecutionRequest{
		RequestHeader: &activityPb.RequestHeader{
			ClientReqId: req.WfProcessingParams.GetClientReqId().GetId(),
			Ownership:   epificontext.OwnershipFromContext(ctx),
		},
		LoanStep:      req.Request.GetLoanStep(),
		LseFieldMasks: req.Request.GetLseFieldMasks(),
	}
	actRes := &palActivityPb.PalActivityResponse{}
	err := activityPkg.Execute(ctx, preApprovedLoanNs.UpdateLoanStepExecution, actRes, actReq)
	if err != nil {
		return nil, errors.Wrap(err, "error updating loan step execution")
	}
	// TODO(Brijesh): Discuss if we need other activities like UpdateLRNextAction, EmitEvent,
	//  GetAlternateOfferOrFailNextAction and DeactivateLoanOffer
	return &PostProcessResponse{}, nil
}
