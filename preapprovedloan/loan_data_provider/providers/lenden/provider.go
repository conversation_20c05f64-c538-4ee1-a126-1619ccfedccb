package lenden

import (
	"context"
	"fmt"
	"sort"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/rpc/code"
	"google.golang.org/genproto/googleapis/type/date"
	moneyPb "google.golang.org/genproto/googleapis/type/money"

	vgPb "github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	moneyPkg "github.com/epifi/be-common/pkg/money"

	palPb "github.com/epifi/gamma/api/preapprovedloan"
	ldcVgPb "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/lenden"
	"github.com/epifi/gamma/preapprovedloan/dao"
	"github.com/epifi/gamma/preapprovedloan/loan_data_provider/providers"
	"github.com/epifi/gamma/preapprovedloan/pkg"
)

type Provider struct {
	ldcVgClient    ldcVgPb.LendenClient
	loanAccountDao dao.LoanAccountsDao
	palClient      palPb.PreApprovedLoanClient
}

func NewProvider(
	ldcVgClient ldcVgPb.LendenClient,
	loanAccountDao dao.LoanAccountsDao,
	palClient palPb.PreApprovedLoanClient,
) *Provider {
	return &Provider{
		ldcVgClient:    ldcVgClient,
		loanAccountDao: loanAccountDao,
		palClient:      palClient,
	}
}

func (p *Provider) FetchLoanScheduleFromVendor(ctx context.Context, req *providers.FetchLoanScheduleRequest) (*providers.FetchLoanScheduleResponse, error) {
	amortizationScheduleRes, err := p.ldcVgClient.GetAmortizationSchedule(ctx, &ldcVgPb.GetAmortizationScheduleRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(amortizationScheduleRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting amortization schedule for loan account: %v", req.LoanAccount.GetId())
	}
	installments, err := convertScheduleToPayouts(req.LoanAccount.GetId(), amortizationScheduleRes.GetAmortizationSchedule())
	if err != nil {
		return nil, errors.Wrap(err, "error converting schedule to payouts")
	}
	loanDetailsRes, err := p.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(loanDetailsRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting loan details for loan account: %v", req.LoanAccount.GetId())
	}
	status, err := getLoanAccountStatus(loanDetailsRes.GetLoanStatus())
	if err != nil {
		return nil, errors.Wrap(err, "error getting loan account status")
	}
	return &providers.FetchLoanScheduleResponse{
		LoanInstallments: installments,
		Status:           status,
	}, nil
}

func convertScheduleToPayouts(loanAccountId string, schedule []*ldcVgPb.AmortizationScheduleItem) ([]*palPb.LoanInstallmentPayout, error) {
	var installments []*palPb.LoanInstallmentPayout
	if len(schedule) == 0 {
		return nil, errors.New("no schedule found")
	}
	// Sort the schedule slice in place by due date ascending, so that callers can assume the schedule is sorted
	// regardless of vendor not being deterministic in their ordering.
	sort.Slice(schedule, func(i, j int) bool {
		return !datetime.IsDateAfter(schedule[i].GetDueDate(), schedule[j].GetDueDate())
	})
	for i := 1; i < len(schedule); i++ {
		if datetime.DateEquals(schedule[i-1].GetDueDate(), schedule[i].GetDueDate()) {
			return nil, errors.Errorf("duplicate due date found in schedule, (i-1)th: %v, ith: %v", schedule[i-1].GetDueDate(), schedule[i].GetDueDate())
		}
	}
	for _, item := range schedule {
		paymentStatus, err := getPaymentStatus(item.GetStatus())
		if err != nil {
			return nil, errors.Wrap(err, "error getting payment status")
		}
		var principal, interest *moneyPb.Money
		for _, component := range item.GetBreakup() {
			switch component.GetPurpose() {
			case ldcVgPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_PRINCIPAL:
				principal = component.GetAmount()
			case ldcVgPb.AmortizationComponentPurpose_AMORTIZATION_COMPONENT_PURPOSE_INTEREST:
				interest = component.GetAmount()
			default:
				return nil, errors.Errorf("unexpected purpose: %s", component.GetPurpose().String())
			}
		}
		amountPaid, err := moneyPkg.Subtract(item.GetDueAmount(), item.GetOutstandingAmount())
		if err != nil {
			return nil, errors.Wrapf(err, "error calculating amount paid, dueAmount: %v, outstandingAmount: %v", item.GetDueAmount(), item.GetOutstandingAmount())
		}
		var payoutDate *date.Date
		if moneyPkg.IsZero(item.GetOutstandingAmount()) {
			// TODO(Brijesh): Discuss and add correct handling after discussing with Vikas
			payoutDate = item.GetDueDate()
		}
		installment := &palPb.LoanInstallmentPayout{
			Amount:              amountPaid,
			DueDate:             item.GetDueDate(),
			PayoutDate:          payoutDate,
			Status:              paymentStatus,
			LoanAccountId:       loanAccountId,
			VendorInstallmentId: datetime.DateToString(item.GetDueDate(), datetime.DATE_LAYOUT_DDMMYYYY, datetime.IST),
			Principal:           principal,
			Interest:            interest,
			DueAmount:           item.GetDueAmount(),
		}
		installments = append(installments, installment)
	}
	return installments, nil
}

func getLoanAccountStatus(status ldcVgPb.LoanStatus) (palPb.LoanAccountStatus, error) {
	switch status {
	case ldcVgPb.LoanStatus_LOAN_STATUS_PROCESSING,
		ldcVgPb.LoanStatus_LOAN_STATUS_SANCTIONED,
		ldcVgPb.LoanStatus_LOAN_STATUS_DISBURSED:
		return palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_ACTIVE, nil
	case ldcVgPb.LoanStatus_LOAN_STATUS_CLOSED,
		ldcVgPb.LoanStatus_LOAN_STATUS_CANCELLED:
		return palPb.LoanAccountStatus_LOAN_ACCOUNT_STATUS_CLOSED, nil
	default:
		return 0, errors.Errorf("unexpected loan status: %s", status.String())
	}
}

func getPaymentStatus(status ldcVgPb.AmortizationScheduleItemStatus) (palPb.LoanInstallmentPayoutStatus, error) {
	// TODO(Brijesh): Discuss if we need to map LDC statuses to other internal statuses like partially failed, partially paid, cancelled, etc.
	switch status {
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_UPCOMING,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_OVERDUE,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_DUE:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PENDING, nil
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PAID_IN_ADVANCE,
		ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_LATE_PAYMENT:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_SUCCESS, nil
	case ldcVgPb.AmortizationScheduleItemStatus_AMORTIZATION_SCHEDULE_ITEM_STATUS_PARTIALLY_PAID:
		return palPb.LoanInstallmentPayoutStatus_LOAN_INSTALLMENT_PAYOUT_STATUS_PARTIALLY_PAID, nil
	default:
		return 0, errors.Errorf("unexpected payment status: %s", status.String())
	}
}

func (p *Provider) GetOutstandingLoanAmount(ctx context.Context, req *providers.GetOutstandingLoanAmountRequest) (*providers.GetOutstandingLoanAmountResponse, error) {
	if req.LoanAccountId == "" {
		return nil, errors.New("loan account id is empty")
	}

	loanAccount, err := p.loanAccountDao.GetById(ctx, req.LoanAccountId)
	if err != nil {
		return nil, fmt.Errorf("unable to get loan account: %w", err)
	}

	loanDetailsRes, err := p.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: loanAccount.GetAccountNumber(),
	})
	if err = epifigrpc.RPCError(loanDetailsRes, err); err != nil {
		return nil, errors.Wrapf(err, "error getting loan details for loan account: %s", req.LoanAccountId)
	}
	return &providers.GetOutstandingLoanAmountResponse{
		OutstandingLoanAmount: pkg.GetTotalOutstandingAmountFromLoanDetailsRes(loanDetailsRes),
	}, nil
}
func (p *Provider) GetPaymentLink(ctx context.Context, req *providers.GetPaymentLinkReq) (*providers.GetPaymentLinkResp, error) {
	paymentLinkResp, err := p.ldcVgClient.GeneratePaymentLink(ctx, &ldcVgPb.GeneratePaymentLinkRequest{
		Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId: req.LoanId,
		Amount: req.TxnAmount,
	})
	if te := epifigrpc.RPCError(paymentLinkResp, err); te != nil {
		return nil, errors.Wrap(te, "error in lenden payment link api")
	}
	return &providers.GetPaymentLinkResp{
		PaymentLink: paymentLinkResp.GetUrl(),
		OrderId:     paymentLinkResp.GetOrderId(),
	}, nil
}

func (p *Provider) FetchLoanCancellationDetailsFromVendor(ctx context.Context, loanAccount *palPb.LoanAccount) (*providers.FetchLoanCancellationDetailsFromVendorResponse, error) {
	res, err := p.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
		Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId:  loanAccount.GetAccountNumber(),
		Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_COOL_OFF,
	})
	if err = epifigrpc.RPCError(res, err); err != nil {
		switch res.GetStatus().GetCode() {
		case uint32(ldcVgPb.GetForeclosureDetailsResponse_INVALID_LOAN_STATUS):
			// Loan is most probably actually closed, hence we need to update the loan re-payment and payment-schedule data
			// using lender's APIs so that the user doesn't see the option to pay on the app anymore.
			var refreshLmsRes *palPb.RefreshLmsScheduleResponse
			refreshLmsRes, err = p.palClient.RefreshLMSSchedule(ctx, &palPb.RefreshLmsScheduleRequest{
				ActorId: loanAccount.GetActorId(),
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: loanAccount.GetLoanProgram(),
					Vendor:      loanAccount.GetVendor(),
				},
			})
			if err = epifigrpc.RPCError(refreshLmsRes, err); err != nil {
				return nil, errors.Wrap(err, "error refreshing LMS details")
			}
			return &providers.FetchLoanCancellationDetailsFromVendorResponse{IsCancellationAllowed: false}, nil
		case uint32(code.Code_FAILED_PRECONDITION):
			return &providers.FetchLoanCancellationDetailsFromVendorResponse{IsCancellationAllowed: false}, nil
		default:
			return nil, errors.Wrap(err, "error getting cool-off details")
		}
	}
	return &providers.FetchLoanCancellationDetailsFromVendorResponse{
		IsCancellationAllowed:  true,
		LoanCancellationAmount: res.GetForeclosureDetails().GetForeclosureAmount(),
	}, nil
}

func (p *Provider) FetchLoanPreClosureDetailsFromVendor(ctx context.Context, loanAccount *palPb.LoanAccount) (*providers.FetchLoanPreClosureDetailsFromVendorResponse, error) {
	foreclosureDetailsRes, err := p.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
		Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
		LoanId:  loanAccount.GetAccountNumber(),
		Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_FORECLOSURE,
	})
	if err = epifigrpc.RPCError(foreclosureDetailsRes, err); err != nil {
		switch foreclosureDetailsRes.GetStatus().GetCode() {
		case uint32(ldcVgPb.GetForeclosureDetailsResponse_INVALID_LOAN_STATUS):
			// Loan is most probably actually closed, hence we need to update the loan re-payment and payment-schedule data
			// using lender's APIs so that the user doesn't see the option to pay on the app anymore.
			var refreshLmsRes *palPb.RefreshLmsScheduleResponse
			refreshLmsRes, err = p.palClient.RefreshLMSSchedule(ctx, &palPb.RefreshLmsScheduleRequest{
				ActorId: loanAccount.GetActorId(),
				LoanHeader: &palPb.LoanHeader{
					LoanProgram: loanAccount.GetLoanProgram(),
					Vendor:      loanAccount.GetVendor(),
				},
			})
			if err = epifigrpc.RPCError(refreshLmsRes, err); err != nil {
				return nil, errors.Wrap(err, "error refreshing LMS details")
			}
			// Sending empty foreclosure details, as loan is already closed at lender's end.
			return nil, nil
		case uint32(code.Code_FAILED_PRECONDITION):
			var coolOffDetailsRes *ldcVgPb.GetForeclosureDetailsResponse
			coolOffDetailsRes, err = p.ldcVgClient.GetForeclosureDetails(ctx, &ldcVgPb.GetForeclosureDetailsRequest{
				Header:  &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
				LoanId:  loanAccount.GetAccountNumber(),
				Purpose: ldcVgPb.GetForeclosureDetailsRequest_PURPOSE_COOL_OFF,
			})
			if err = epifigrpc.RPCError(coolOffDetailsRes, err); err != nil {
				return nil, errors.Wrap(err, "error getting cool-off details even when foreclosure is not allowed")
			}
			return &providers.FetchLoanPreClosureDetailsFromVendorResponse{
				LoanPreCloseAmount:             coolOffDetailsRes.GetForeclosureDetails().GetForeclosureAmount(),
				LoanPreCloseCharges:            coolOffDetailsRes.GetForeclosureDetails().GetForeclosureCharges(),
				LoanPrincipalOutstandingAmount: coolOffDetailsRes.GetForeclosureDetails().GetPrincipalOutstanding(),
				LoanOtherCharges:               coolOffDetailsRes.GetForeclosureDetails().GetDelayChargesOutstanding(),
				LoanFeesAmount:                 coolOffDetailsRes.GetForeclosureDetails().GetLateFeeOutstanding(),
				LoanInterestOutstandingAmount:  coolOffDetailsRes.GetForeclosureDetails().GetInterestOutstanding(),
			}, nil
		case uint32(ldcVgPb.GetForeclosureDetailsResponse_PRINCIPAL_PAID),
			uint32(ldcVgPb.GetForeclosureDetailsResponse_LAST_DUE_DATE_CLOSE):
			var loanDetailsRes *ldcVgPb.GetLoanDetailsResponse
			loanDetailsRes, err = p.ldcVgClient.GetLoanDetails(ctx, &ldcVgPb.GetLoanDetailsRequest{
				Header: &vgPb.RequestHeader{Vendor: vgPb.Vendor_LENDEN},
				LoanId: loanAccount.GetAccountNumber(),
			})
			if err = epifigrpc.RPCError(loanDetailsRes, err); err != nil {
				return nil, errors.Wrap(err, "error getting loan details")
			}
			principalOutstanding := moneyPkg.ParseFloat(loanDetailsRes.GetLoanDetails().GetPrincipalOutstanding(), moneyPkg.RupeeCurrencyCode)
			interestOutstanding := moneyPkg.ParseFloat(loanDetailsRes.GetLoanDetails().GetInterestOutstanding(), moneyPkg.RupeeCurrencyCode)
			delayInterestDue := moneyPkg.ParseFloat(loanDetailsRes.GetLoanDetails().GetDelayInterestDue(), moneyPkg.RupeeCurrencyCode)
			otherChargesDue := moneyPkg.ParseFloat(loanDetailsRes.GetLoanDetails().GetOtherChargesDue(), moneyPkg.RupeeCurrencyCode)
			var closureAmount *moneyPb.Money
			closureAmount, err = moneyPkg.SumMultiple(principalOutstanding, interestOutstanding, delayInterestDue, otherChargesDue)
			if err != nil {
				return nil, errors.Wrap(err, "error summing up amounts to get preclosure amount")
			}
			if moneyPkg.IsZero(closureAmount) {
				return nil, errors.New("preclosure amount is zero when expected to be non-zero")
			}
			return &providers.FetchLoanPreClosureDetailsFromVendorResponse{
				LoanPreCloseAmount:             closureAmount,
				LoanPreCloseCharges:            moneyPkg.ZeroINR().GetPb(),
				LoanPrincipalOutstandingAmount: principalOutstanding,
				LoanOtherCharges:               otherChargesDue,
				LoanFeesAmount:                 delayInterestDue,
				LoanInterestOutstandingAmount:  interestOutstanding,
			}, nil
		default:
			return nil, errors.Wrap(err, "error getting foreclosure details")
		}
	}
	return &providers.FetchLoanPreClosureDetailsFromVendorResponse{
		LoanPreCloseAmount:             foreclosureDetailsRes.GetForeclosureDetails().GetForeclosureAmount(),
		LoanPreCloseCharges:            foreclosureDetailsRes.GetForeclosureDetails().GetForeclosureCharges(),
		LoanPrincipalOutstandingAmount: foreclosureDetailsRes.GetForeclosureDetails().GetPrincipalOutstanding(),
		LoanOtherCharges:               foreclosureDetailsRes.GetForeclosureDetails().GetDelayChargesOutstanding(),
		LoanFeesAmount:                 foreclosureDetailsRes.GetForeclosureDetails().GetLateFeeOutstanding(),
		LoanInterestOutstandingAmount:  foreclosureDetailsRes.GetForeclosureDetails().GetInterestOutstanding(),
	}, nil
}
