package tiering

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"testing"
	"time"

	commontypes "github.com/epifi/be-common/api/typesv2/common"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	mockAccountBalance "github.com/epifi/gamma/api/accounts/balance/mocks"
	savingsPb "github.com/epifi/gamma/api/savings"
	savingsMocks "github.com/epifi/gamma/api/savings/mocks"
	segmentMocks "github.com/epifi/gamma/api/segment/mocks"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/testing/protocmp"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	rpcPb "github.com/epifi/be-common/api/rpc"

	dynamicElementsPb "github.com/epifi/gamma/api/dynamic_elements"
	deeplinkPb "github.com/epifi/gamma/api/frontend/deeplink"
	mockPay "github.com/epifi/gamma/api/pay/mocks"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	salaryprogramMocks "github.com/epifi/gamma/api/salaryprogram/mocks"
	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	typesPb "github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/ui"
	userPb "github.com/epifi/gamma/api/user"
	mocks3 "github.com/epifi/gamma/api/user/mocks"
	onbPb "github.com/epifi/gamma/api/user/onboarding"
	onbMocks "github.com/epifi/gamma/api/user/onboarding/mocks"
	releaseEvaluatorMocks "github.com/epifi/gamma/pkg/feature/release/mocks"
	"github.com/epifi/gamma/tiering/config/genconf"
	"github.com/epifi/gamma/tiering/dao/mocks"
	mocks2 "github.com/epifi/gamma/tiering/test/mocks"
	mockTierOptions "github.com/epifi/gamma/tiering/test/mocks/tier_options"
	"github.com/epifi/gamma/tiering/timeline"
)

var minBalanceMap = map[tieringEnumPb.Tier]*gmoney.Money{
	tieringEnumPb.Tier_TIER_TEN: {
		CurrencyCode: "INR",
	},
	tieringEnumPb.Tier_TIER_ONE_HUNDRED: {
		CurrencyCode: "INR",
		Units:        10000,
	},
	tieringEnumPb.Tier_TIER_ONE_THOUSAND: {
		CurrencyCode: "INR",
		Units:        50000,
	},
	tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED: {
		CurrencyCode: "INR",
	},
	tieringEnumPb.Tier_TIER_TWO_THOUSAND: {
		CurrencyCode: "INR",
	},
}

func TestService_DynamicElementCallback(t *testing.T) {
	type fields struct{}
	type args struct {
		ctx context.Context
		req *dynamicElementsPb.DynamicElementCallbackRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *dynamicElementsPb.DynamicElementCallbackResponse
		wantErr bool
	}{
		{
			name:   "success",
			fields: fields{},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.DynamicElementCallbackRequest{
					ActorId:         "actor-1",
					ElementId:       "element-1",
					CallbackPayload: nil,
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{}
			got, err := s.DynamicElementCallback(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("DynamicElementCallback() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("DynamicElementCallback() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_FetchDynamicElements(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockAtiDao := mocks.NewMockActorTierInfoDao(ctrl)
	mockTimelineManager := mocks2.NewMockTierTimeline(ctrl)
	mockTmhDao := mocks.NewMockTierMovementHistoryDao(ctrl)
	mockDataCollectorFactory := mocks2.NewMockDataCollectorFactory(ctrl)
	mockBalanceDataCollector := mocks2.NewMockDataCollector(ctrl)
	mockTierOptionsManager := mockTierOptions.NewMockManager(ctrl)
	mockSalaryProgramClient := salaryprogramMocks.NewMockSalaryProgramClient(ctrl)
	mockOnbClient := onbMocks.NewMockOnboardingClient(ctrl)
	mockUserClient := mocks3.NewMockUsersClient(ctrl)
	mockTieringDataProcessor := mocks2.NewMockTieringDataProcessor(ctrl)
	mockReleaseEvaluator := releaseEvaluatorMocks.NewMockIEvaluator(ctrl)
	mockPayClient := mockPay.NewMockPayClient(ctrl)
	mockSegmentationClient := segmentMocks.NewMockSegmentationServiceClient(ctrl)
	defer ctrl.Finish()

	type mockStruct struct {
		mockAtiDao               *mocks.MockActorTierInfoDao
		mockTimelineManager      *mocks2.MockTierTimeline
		mockTmhDao               *mocks.MockTierMovementHistoryDao
		mockDataCollectorFactory *mocks2.MockDataCollectorFactory
		mockBalanceDataCollector *mocks2.MockDataCollector
		mockTierOptionsManager   *mockTierOptions.MockManager
		mockSalaryProgramClient  *salaryprogramMocks.MockSalaryProgramClient
		mockOnbClient            *onbMocks.MockOnboardingClient
		mockUserClient           *mocks3.MockUsersClient
		mockTieringDataProcessor *mocks2.MockTieringDataProcessor
		mockReleaseEvaluator     *releaseEvaluatorMocks.MockIEvaluator
		mockPayClient            *mockPay.MockPayClient
		mockSegmentationClient   *segmentMocks.MockSegmentationServiceClient
	}
	curTimestamp := timestampPb.Now()
	graceTimestamp := timestampPb.New(time.Now().Add(time.Hour * 73))
	type fields struct {
		gconf *genconf.Config
	}
	type args struct {
		ctx   context.Context
		req   *dynamicElementsPb.FetchDynamicElementsRequest
		mocks func(mockStruct *mockStruct)
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *dynamicElementsPb.FetchDynamicElementsResponse
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				mocks: func(m *mockStruct) {
					m.mockUserClient.EXPECT().IsNonResidentUser(gomock.Any(), gomock.Any()).Return(&userPb.IsNonResidentUserResponse{
						Status:            rpcPb.StatusOk(),
						IsNonResidentUser: commontypes.BooleanEnum_FALSE,
					}, nil)
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(true, &timeline.GraceDetails{MovementTimestamp: graceTimestamp, GracePeriod: 1296000}, nil)
					m.mockTieringDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil).AnyTimes()
					m.mockTmhDao.EXPECT().GetLatestByActorId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&tieringPb.TierMovementHistory{
							ActorId:   "actor-1",
							FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
							CreatedAt: curTimestamp,
						}, nil)
					m.mockTimelineManager.EXPECT().IsARewardAbuserUser(gomock.Any(), "actor-1").Return(false, nil)
					m.mockSalaryProgramClient.EXPECT().IsEligibleForSalaryPromoWidget(gomock.Any(), gomock.Any()).Return(&salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
						Status:     rpcPb.StatusOk(),
						IsEligible: true,
					}, nil)
					m.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil).MaxTimes(1)
					mockReleaseEvaluator.EXPECT().Evaluate(gomock.Any(), gomock.Any()).Return(false, nil)
					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("some error")).AnyTimes()
					m.mockUserClient.EXPECT().GetB2BSalaryProgramVerificationStatus(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("some error")).AnyTimes()
					m.mockAtiDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
					m.mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{
						Status: rpcPb.StatusOk(),
						UserDevicePropertyList: []*userPb.UserDeviceProperty{
							{
								DeviceProperty: typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
								PropertyValue:  &typesPb.PropertyValue{PropValue: &typesPb.PropertyValue_DeviceId{DeviceId: "device-id"}},
							},
						},
					}, nil).AnyTimes()
				},
			},
			want: &dynamicElementsPb.FetchDynamicElementsResponse{
				Status: rpcPb.StatusOk(),
				ElementsList: []*dynamicElementsPb.DynamicElement{
					{
						OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
						UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
						StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
						Content: &dynamicElementsPb.ElementContent{
							Content: &dynamicElementsPb.ElementContent_BannerV2{
								BannerV2: &dynamicElementsPb.BannerElementContentV2{
									Title: &commontypes.Text{
										FontColor: graceTopBarBannerFontColor,
										DisplayValue: &commontypes.Text_PlainString{
											PlainString: fmt.Sprintf(graceTopBarBannerText, "Plus", "3"),
										},
									},
									Image:           commontypes.GetImageFromUrl(graceTopBarBannerImageUrl),
									BackgroundColor: ui.GetBlockColor(graceTopBarBannerBgColor),
									Deeplink: &deeplinkPb.Deeplink{
										Screen: deeplinkPb.Screen_TRANSFER_IN,
									},
								},
							},
						},
						EndTime: graceTimestamp,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf:                  tt.fields.gconf,
				actorTierInfoDao:       mockAtiDao,
				tierTimelineManager:    mockTimelineManager,
				tierMovementHistoryDao: mockTmhDao,
				dataCollectorFactory:   mockDataCollectorFactory,
				tierOptionsManager:     mockTierOptionsManager,
				salaryProgramClient:    mockSalaryProgramClient,
				onboardingClient:       mockOnbClient,
				userClient:             mockUserClient,
				dataProcessor:          mockTieringDataProcessor,
				releaseEvaluator:       mockReleaseEvaluator,
				payClient:              mockPayClient,
				segmentationClient:     mockSegmentationClient,
			}
			m := &mockStruct{
				mockAtiDao:               mockAtiDao,
				mockTimelineManager:      mockTimelineManager,
				mockTmhDao:               mockTmhDao,
				mockDataCollectorFactory: mockDataCollectorFactory,
				mockTierOptionsManager:   mockTierOptionsManager,
				mockBalanceDataCollector: mockBalanceDataCollector,
				mockSalaryProgramClient:  mockSalaryProgramClient,
				mockOnbClient:            mockOnbClient,
				mockUserClient:           mockUserClient,
				mockTieringDataProcessor: mockTieringDataProcessor,
				mockReleaseEvaluator:     mockReleaseEvaluator,
				mockPayClient:            mockPayClient,
				mockSegmentationClient:   mockSegmentationClient,
			}
			tt.args.mocks(m)
			got, err := s.FetchDynamicElements(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("FetchDynamicElements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("FetchDynamicElements() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_gatherDataForDynamicElements(t *testing.T) {
	// Mock controllers will be created per test case to avoid shared state issues

	type mockStruct struct {
		mockAtiDao               *mocks.MockActorTierInfoDao
		mockTimelineManager      *mocks2.MockTierTimeline
		mockTmhDao               *mocks.MockTierMovementHistoryDao
		mockDataCollectorFactory *mocks2.MockDataCollectorFactory
		mockBalanceDataCollector *mocks2.MockDataCollector
		mockTierOptionsManager   *mockTierOptions.MockManager
		mockSalaryProgramClient  *salaryprogramMocks.MockSalaryProgramClient
		mockOnbClient            *onbMocks.MockOnboardingClient
		mockTieringDataProcessor *mocks2.MockTieringDataProcessor
		mockUserClient           *mocks3.MockUsersClient
	}
	curTimestamp := timestampPb.Now()
	tmhCreationTs := timestampPb.New(time.Now().Add(-time.Hour * 121))

	type fields struct{}
	type args struct {
		ctx     context.Context
		actorId string
		mocks   func(mockStruct *mockStruct)
		toSleep bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *DynamicElementsCollectedData
		wantErr bool
	}{
		{
			name:   "success",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				mocks: func(m *mockStruct) {
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(true, &timeline.GraceDetails{MovementTimestamp: curTimestamp, GracePeriod: 1296000}, nil)
					m.mockTieringDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil).AnyTimes()
					m.mockTmhDao.EXPECT().GetLatestByActorId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&tieringPb.TierMovementHistory{
							ActorId:   "actor-1",
							FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
							CreatedAt: tmhCreationTs,
						}, nil)
					m.mockTimelineManager.EXPECT().IsARewardAbuserUser(gomock.Any(), "actor-1").Return(false, nil)
					m.mockSalaryProgramClient.EXPECT().IsEligibleForSalaryPromoWidget(gomock.Any(), gomock.Any()).Return(&salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
						Status:     rpcPb.StatusOk(),
						IsEligible: true,
					}, nil)
					m.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil).MaxTimes(1)
					m.mockAtiDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
					m.mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{
						Status: rpcPb.StatusOk(),
						UserDevicePropertyList: []*userPb.UserDeviceProperty{
							{
								DeviceProperty: typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
								PropertyValue:  &typesPb.PropertyValue{PropValue: &typesPb.PropertyValue_DeviceId{DeviceId: "device-id"}},
							},
						},
					}, nil).AnyTimes()
				},
			},
			want: &DynamicElementsCollectedData{
				IsUserInGrace:        true,
				GraceTimestamp:       curTimestamp,
				GracePeriod:          1296000,
				CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				IsSalaryPromoEnabled: true,
				TrialDetailsResp: &tieringPb.GetTrialDetailsResponse{
					Status: rpcPb.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name:   "success - abuser user",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				mocks: func(m *mockStruct) {
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(true, &timeline.GraceDetails{MovementTimestamp: curTimestamp, GracePeriod: 1296000}, nil)
					m.mockTieringDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil).AnyTimes()
					m.mockTmhDao.EXPECT().GetLatestByActorId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&tieringPb.TierMovementHistory{
							ActorId:   "actor-1",
							FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
							CreatedAt: tmhCreationTs,
						}, nil)
					m.mockTimelineManager.EXPECT().IsARewardAbuserUser(gomock.Any(), "actor-1").Return(true, nil)
					m.mockSalaryProgramClient.EXPECT().IsEligibleForSalaryPromoWidget(gomock.Any(), gomock.Any()).Return(&salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
						Status:     rpcPb.StatusOk(),
						IsEligible: true,
					}, nil)
					m.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil).MaxTimes(1)
					m.mockAtiDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
					m.mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{
						Status: rpcPb.StatusOk(),
						UserDevicePropertyList: []*userPb.UserDeviceProperty{
							{
								DeviceProperty: typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
								PropertyValue:  &typesPb.PropertyValue{PropValue: &typesPb.PropertyValue_DeviceId{DeviceId: "device-id"}},
							},
						},
					}, nil).AnyTimes()
				},
			},
			want: &DynamicElementsCollectedData{
				IsUserInGrace:        true,
				GraceTimestamp:       curTimestamp,
				GracePeriod:          1296000,
				CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				IsARewardsAbuserUser: true,
				IsSalaryPromoEnabled: true,
				TrialDetailsResp: &tieringPb.GetTrialDetailsResponse{
					Status: rpcPb.StatusOk(),
				},
			},
			wantErr: false,
		},
		{
			name:   "failure, error in checking if user in grace or not",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				mocks: func(m *mockStruct) {
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(false, nil, errors.New("some random error"))
					m.mockTieringDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil).AnyTimes()
					m.mockTmhDao.EXPECT().GetLatestByActorId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(&tieringPb.TierMovementHistory{
							ActorId:   "actor-1",
							FromTier:  tieringEnumPb.Tier_TIER_ONE_HUNDRED,
							CreatedAt: curTimestamp,
						}, nil).AnyTimes()
					m.mockTimelineManager.EXPECT().IsARewardAbuserUser(gomock.Any(), "actor-1").Return(false, nil).AnyTimes()
					m.mockSalaryProgramClient.EXPECT().IsEligibleForSalaryPromoWidget(gomock.Any(), gomock.Any()).Return(&salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
						Status:     rpcPb.StatusOk(),
						IsEligible: true,
					}, nil).AnyTimes()
					m.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil).AnyTimes()
					m.mockAtiDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
					m.mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{
						Status: rpcPb.StatusOk(),
						UserDevicePropertyList: []*userPb.UserDeviceProperty{
							{
								DeviceProperty: typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
								PropertyValue:  &typesPb.PropertyValue{PropValue: &typesPb.PropertyValue_DeviceId{DeviceId: "device-id"}},
							},
						},
					}, nil).AnyTimes()
				},
				toSleep: true,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "failure, error getting tier movement history",
			fields: fields{},
			args: args{
				ctx:     context.Background(),
				actorId: "actor-1",
				mocks: func(m *mockStruct) {
					m.mockTimelineManager.EXPECT().IsUserInGracePeriod(gomock.Any(), gomock.Any()).
						Return(true, &timeline.GraceDetails{MovementTimestamp: curTimestamp, GracePeriod: 1296000}, nil)
					m.mockTieringDataProcessor.EXPECT().GetCurrentTierDefaultToBaseTier(gomock.Any(), gomock.Any()).Return(tieringEnumPb.Tier_TIER_ONE_HUNDRED, nil).AnyTimes()
					m.mockTmhDao.EXPECT().GetLatestByActorId(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
						Return(nil, fmt.Errorf("database error")).AnyTimes()
					m.mockTimelineManager.EXPECT().IsARewardAbuserUser(gomock.Any(), "actor-1").Return(false, nil).AnyTimes()
					m.mockSalaryProgramClient.EXPECT().IsEligibleForSalaryPromoWidget(gomock.Any(), gomock.Any()).Return(&salaryprogramPb.IsEligibleForSalaryPromoWidgetResponse{
						Status:     rpcPb.StatusOk(),
						IsEligible: true,
					}, nil).AnyTimes()
					m.mockOnbClient.EXPECT().GetFeatureDetails(gomock.Any(), gomock.Any()).Return(&onbPb.GetFeatureDetailsResponse{
						Status:       rpcPb.StatusOk(),
						IsFiLiteUser: false,
					}, nil).AnyTimes()
					m.mockAtiDao.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
					m.mockUserClient.EXPECT().GetUserDeviceProperties(gomock.Any(), gomock.Any()).Return(&userPb.GetUserDevicePropertiesResponse{
						Status: rpcPb.StatusOk(),
						UserDevicePropertyList: []*userPb.UserDeviceProperty{
							{
								DeviceProperty: typesPb.DeviceProperty_DEVICE_PROP_DEVICE_ID,
								PropertyValue:  &typesPb.PropertyValue{PropValue: &typesPb.PropertyValue_DeviceId{DeviceId: "device-id"}},
							},
						},
					}, nil).AnyTimes()
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create fresh mocks for each test case to avoid shared state
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockAtiDao := mocks.NewMockActorTierInfoDao(ctrl)
			mockTimelineManager := mocks2.NewMockTierTimeline(ctrl)
			mockTmhDao := mocks.NewMockTierMovementHistoryDao(ctrl)
			mockDataCollectorFactory := mocks2.NewMockDataCollectorFactory(ctrl)
			mockBalanceDataCollector := mocks2.NewMockDataCollector(ctrl)
			mockTierOptionsManager := mockTierOptions.NewMockManager(ctrl)
			mockSalaryProgramClient := salaryprogramMocks.NewMockSalaryProgramClient(ctrl)
			mockOnbClient := onbMocks.NewMockOnboardingClient(ctrl)
			mockDataProcessor := mocks2.NewMockTieringDataProcessor(ctrl)
			mockUserClient := mocks3.NewMockUsersClient(ctrl)

			s := &Service{
				gconf:                  gconf,
				actorTierInfoDao:       mockAtiDao,
				tierTimelineManager:    mockTimelineManager,
				tierMovementHistoryDao: mockTmhDao,
				dataCollectorFactory:   mockDataCollectorFactory,
				tierOptionsManager:     mockTierOptionsManager,
				salaryProgramClient:    mockSalaryProgramClient,
				onboardingClient:       mockOnbClient,
				dataProcessor:          mockDataProcessor,
				userClient:             mockUserClient,
			}
			m := &mockStruct{
				mockAtiDao:               mockAtiDao,
				mockTimelineManager:      mockTimelineManager,
				mockTmhDao:               mockTmhDao,
				mockDataCollectorFactory: mockDataCollectorFactory,
				mockTierOptionsManager:   mockTierOptionsManager,
				mockBalanceDataCollector: mockBalanceDataCollector,
				mockSalaryProgramClient:  mockSalaryProgramClient,
				mockOnbClient:            mockOnbClient,
				mockTieringDataProcessor: mockDataProcessor,
				mockUserClient:           mockUserClient,
			}
			tt.args.mocks(m)
			got, err := s.gatherDataForDynamicElements(tt.args.ctx, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("gatherDataForDynamicElements() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				diff := cmp.Diff(got, tt.want, protocmp.Transform())
				t.Errorf("gatherDataForDynamicElements() diff: %s", diff)
			}
			if tt.args.toSleep {
				time.Sleep(time.Second)
			}
		})
	}
}

func TestService_getDynamicElementsTopBar(t *testing.T) {
	graceTimestamp := timestampPb.New(time.Now().Add(time.Hour * 73))
	graceTimestamp2 := timestampPb.New(time.Now().Add(time.Hour * 24 * 14))
	type fields struct {
		gconf *genconf.Config
	}
	type args struct {
		ctx           context.Context
		req           *dynamicElementsPb.FetchDynamicElementsRequest
		collectedData *DynamicElementsCollectedData
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*dynamicElementsPb.DynamicElement
		wantErr bool
	}{
		{
			name: "success",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        true,
					IsUserDowngraded:     false,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       graceTimestamp,
					GracePeriod:          1296000,
					CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					IsARewardsAbuserUser: false,
					IsSalaryPromoEnabled: true,
				},
			},
			want: []*dynamicElementsPb.DynamicElement{
				{
					OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
					UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
					StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
					Content: &dynamicElementsPb.ElementContent{
						Content: &dynamicElementsPb.ElementContent_BannerV2{
							BannerV2: &dynamicElementsPb.BannerElementContentV2{
								Title: &commontypes.Text{
									FontColor: graceTopBarBannerFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: fmt.Sprintf(graceTopBarBannerText, "Plus", "3"),
									},
								},
								Image:           commontypes.GetImageFromUrl(graceTopBarBannerImageUrl),
								BackgroundColor: ui.GetBlockColor(graceTopBarBannerBgColor),
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_TRANSFER_IN,
								},
							},
						},
					},
					EndTime: graceTimestamp,
				},
			},
			wantErr: false,
		},
		{
			name: "context from primary section",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        true,
					IsUserDowngraded:     false,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       graceTimestamp,
					GracePeriod:          1296000,
					CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					IsARewardsAbuserUser: false,
					IsSalaryPromoEnabled: true,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "success - abuser user",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        false,
					IsUserDowngraded:     true,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       nil,
					GracePeriod:          0,
					CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					IsARewardsAbuserUser: true,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "success, grace banner in initial days of grace",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        true,
					IsUserDowngraded:     false,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       graceTimestamp2,
					GracePeriod:          1296000,
					CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					IsARewardsAbuserUser: false,
				},
			},
			want: []*dynamicElementsPb.DynamicElement{
				{
					OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
					UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
					StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_BANNER_V2,
					Content: &dynamicElementsPb.ElementContent{
						Content: &dynamicElementsPb.ElementContent_BannerV2{
							BannerV2: &dynamicElementsPb.BannerElementContentV2{
								Title: &commontypes.Text{
									FontColor: graceTopBarBannerFontColor,
									DisplayValue: &commontypes.Text_PlainString{
										PlainString: fmt.Sprintf(graceInitialDaysTopBarBannerText, "Plus"),
									},
								},
								Image:           commontypes.GetImageFromUrl(graceTopBarBannerImageUrl),
								BackgroundColor: ui.GetBlockColor(graceTopBarBannerBgColor),
								Deeplink: &deeplinkPb.Deeplink{
									Screen: deeplinkPb.Screen_TRANSFER_IN,
								},
							},
						},
					},
					EndTime: graceTimestamp2,
				},
			},
			wantErr: false,
		},
		{
			name: "neutral, user is not in plus or infinite",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        true,
					IsUserDowngraded:     false,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       graceTimestamp,
					GracePeriod:          1296000,
					CurrentTier:          tieringEnumPb.Tier_TIER_TEN,
					IsARewardsAbuserUser: false,
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "neutral, user not in grace",
			fields: fields{
				gconf: gconf,
			},
			args: args{
				ctx: context.Background(),
				collectedData: &DynamicElementsCollectedData{
					IsUserInGrace:        false,
					IsUserDowngraded:     false,
					DowngradedFrom:       tieringEnumPb.Tier_TIER_UNSPECIFIED,
					GraceTimestamp:       nil,
					GracePeriod:          0,
					CurrentTier:          tieringEnumPb.Tier_TIER_ONE_HUNDRED,
					IsARewardsAbuserUser: false,
				},
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenName: deeplinkPb.Screen_HOME,
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_TOP_BAR,
								Version: dynamicElementsPb.HomeScreenAdditionalInfo_VERSION_V2,
							},
						},
						AppPlatform: commontypes.Platform_ANDROID,
						AppVersion:  10000,
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf: tt.fields.gconf,
			}
			got, err := s.getDynamicElementsTopBar(tt.args.ctx, tt.args.req, tt.args.collectedData)
			if (err != nil) != tt.wantErr {
				t.Errorf("getDynamicElementsTopBar() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				println(cmp.Diff(got, tt.want, protocmp.Transform()))
				t.Errorf("getDynamicElementsTopBar() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getBannerInfoForDowngrade(t *testing.T) {
	type args struct {
		ctx              context.Context
		actorId          string
		downgradedFrom   tieringEnumPb.Tier
		isARewardsAbuser bool
	}
	tests := []struct {
		name    string
		args    args
		want    *BannerInfo
		wantErr bool
	}{
		{
			name: "success",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-1",
				downgradedFrom: tieringEnumPb.Tier_TIER_ONE_HUNDRED,
			},
			want: &BannerInfo{
				ToShowBanner: true,
				Title:        downgradeTopBarBannerTextPlus,
				ImageUrl:     downgradeTopBarBannerImageUrl,
				BgColor:      downgradeTopBarBannerBgColor,
				FontColor:    downgradeTopBarBannerFontColor,
			},
			wantErr: false,
		},
		{
			name: "neutral, not eligible for banner surfacing",
			args: args{
				ctx:            context.Background(),
				actorId:        "actor-1",
				downgradedFrom: tieringEnumPb.Tier_TIER_TEN,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "success",
			args: args{
				ctx:              context.Background(),
				actorId:          "actor-1",
				downgradedFrom:   tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				isARewardsAbuser: true,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getBannerInfoForDowngrade(tt.args.ctx, tt.args.actorId, tt.args.downgradedFrom, tt.args.isARewardsAbuser)
			if (err != nil) != tt.wantErr {
				t.Errorf("getBannerInfoForDowngrade() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getBannerInfoForDowngrade() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getGraceBannerTitle(t *testing.T) {
	curTierString := "Plus"
	type args struct {
		ctx                  context.Context
		curTierString        string
		graceStartTime       time.Time
		graceExpiryTimestamp *timestampPb.Timestamp
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			name: "initial days banner",
			args: args{
				ctx:                  context.Background(),
				curTierString:        curTierString,
				graceStartTime:       time.Now().Add(-time.Hour),
				graceExpiryTimestamp: timestampPb.New(time.Now().Add(time.Hour)),
			},
			want: fmt.Sprintf(graceInitialDaysTopBarBannerText, curTierString),
		},
		{
			name: "expires today",
			args: args{
				ctx:                  context.Background(),
				curTierString:        curTierString,
				graceStartTime:       time.Now().Add(-4 * 24 * time.Hour),
				graceExpiryTimestamp: timestampPb.New(time.Now().Add(time.Hour)),
			},
			want: fmt.Sprintf(graceTopBarBannerTextZeroDays, curTierString),
		},
		{
			name: "expires in 1 day",
			args: args{
				ctx:                  context.Background(),
				curTierString:        curTierString,
				graceStartTime:       time.Now().Add(-4 * 24 * time.Hour),
				graceExpiryTimestamp: timestampPb.New(time.Now().Add(25 * time.Hour)),
			},
			want: fmt.Sprintf(graceTopBarBannerTextOneDay, curTierString, "1"),
		},
		{
			name: "expires in >= 2 days",
			args: args{
				ctx:                  context.Background(),
				curTierString:        curTierString,
				graceStartTime:       time.Now().Add(-4 * 24 * time.Hour),
				graceExpiryTimestamp: timestampPb.New(time.Now().Add(3*24*time.Hour + time.Hour)),
			},
			want: fmt.Sprintf(graceTopBarBannerText, curTierString, "3"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf: gconf,
			}
			if got := s.getGraceTopBannerTitle(tt.args.ctx, tt.args.curTierString, tt.args.graceStartTime, tt.args.graceExpiryTimestamp); got != tt.want {
				t.Errorf("getGraceTopBannerTitle() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getBannerInfoForGrace(t *testing.T) {
	movementTimestamp1 := time.Now().Add(14 * 24 * time.Hour)
	movementTimestamp2 := time.Now().Add(time.Hour)
	movementTimestamp3 := time.Now().Add(25 * time.Hour)
	type args struct {
		ctx                context.Context
		actorId            string
		currentTier        tieringEnumPb.Tier
		graceTimestamp     *timestampPb.Timestamp
		gracePeriodSeconds float64
	}
	tests := []struct {
		name    string
		args    args
		want    *BannerInfo
		wantErr bool
	}{
		{
			name: "initial days of grace",
			args: args{
				ctx:                context.Background(),
				actorId:            "actor",
				currentTier:        tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				graceTimestamp:     timestampPb.New(movementTimestamp1),
				gracePeriodSeconds: 1296000,
			},
			want: &BannerInfo{
				ToShowBanner: true,
				Title:        fmt.Sprintf(graceInitialDaysTopBarBannerText, "Plus"),
				ImageUrl:     graceTopBarBannerImageUrl,
				BgColor:      graceTopBarBannerBgColor,
				FontColor:    graceTopBarBannerFontColor,
			},
			wantErr: false,
		},
		{
			name: "last day of grace",
			args: args{
				ctx:                context.Background(),
				actorId:            "actor",
				currentTier:        tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				graceTimestamp:     timestampPb.New(movementTimestamp2),
				gracePeriodSeconds: 1296000,
			},
			want: &BannerInfo{
				ToShowBanner: true,
				Title:        fmt.Sprintf(graceTopBarBannerTextZeroDays, "Plus"),
				ImageUrl:     graceTopBarBannerImageUrl,
				BgColor:      graceTopBarBannerBgColor,
				FontColor:    graceTopBarBannerFontColor,
			},
			wantErr: false,
		},
		{
			name: "one day for downgrade",
			args: args{
				ctx:                context.Background(),
				actorId:            "actor",
				currentTier:        tieringEnumPb.Tier_TIER_ONE_HUNDRED,
				graceTimestamp:     timestampPb.New(movementTimestamp3),
				gracePeriodSeconds: 1296000,
			},
			want: &BannerInfo{
				ToShowBanner: true,
				Title:        fmt.Sprintf(graceTopBarBannerTextOneDay, "Plus", "1"),
				ImageUrl:     graceTopBarBannerImageUrl,
				BgColor:      graceTopBarBannerBgColor,
				FontColor:    graceTopBarBannerFontColor,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf: gconf,
			}
			got, err := s.getBannerInfoForGrace(tt.args.ctx, tt.args.actorId, tt.args.currentTier, tt.args.graceTimestamp, tt.args.gracePeriodSeconds)
			if (err != nil) != tt.wantErr {
				t.Errorf("getBannerInfoForGrace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				println(cmp.Diff(got, tt.want))
				t.Errorf("getBannerInfoForGrace() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_getPromoWidgetElementsV2(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockDataProcessor := mocks2.NewMockTieringDataProcessor(ctrl)
	mockSavingsClient := savingsMocks.NewMockSavingsClient(ctrl)
	mockAccountBalanceClient := mockAccountBalance.NewMockBalanceClient(ctrl)
	defer ctrl.Finish()

	type fields struct {
		gconf                *genconf.Config
		dataProcessor        *mocks2.MockTieringDataProcessor
		savingsClient        savingsPb.SavingsClient
		accountBalanceClient *mockAccountBalance.MockBalanceClient
	}
	type args struct {
		ctx           context.Context
		req           *dynamicElementsPb.FetchDynamicElementsRequest
		collectedData *DynamicElementsCollectedData
		actorId       string
		mocks         func()
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantLen    int
		wantReason string
		wantErr    bool
	}{
		{
			name: "should pitch for base tier users using promo widget",
			fields: fields{
				gconf:                gconf,
				dataProcessor:        mockDataProcessor,
				savingsClient:        mockSavingsClient,
				accountBalanceClient: mockAccountBalanceClient,
			},
			args: args{
				actorId: "actor-1",
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY,
							},
						},
					},
				},
				collectedData: &DynamicElementsCollectedData{CurrentTier: tieringEnumPb.Tier_TIER_TEN},
				mocks: func() {
					mockDataProcessor.EXPECT().IsMultipleWaysToEnterTierEnabled(gomock.Any(), "actor-1").Return(true, nil)
					mockSavingsClient.EXPECT().GetAccount(gomock.Any(), gomock.Any()).Return(&savingsPb.GetAccountResponse{Status: rpcPb.StatusOk(), Account: &savingsPb.Account{Id: "account-1"}}, nil)
					mockAccountBalanceClient.EXPECT().GetAccountBalance(gomock.Any(), gomock.Any()).Return(&accountBalancePb.GetAccountBalanceResponse{Status: rpcPb.StatusOk(), AvailableBalance: &gmoney.Money{Units: 60000}}, nil)
				},
			},
			wantLen: 1,
		},
		{
			name: "should not pitch for higher tier users using promo widget",
			fields: fields{
				gconf:                gconf,
				dataProcessor:        mockDataProcessor,
				savingsClient:        mockSavingsClient,
				accountBalanceClient: mockAccountBalanceClient,
			},
			args: args{
				actorId: "actor-1",
				req: &dynamicElementsPb.FetchDynamicElementsRequest{
					ActorId: "actor-1",
					ClientContext: &dynamicElementsPb.ClientContext{
						ScreenAdditionalInfo: &dynamicElementsPb.ClientContext_HomeInfo{
							HomeInfo: &dynamicElementsPb.HomeScreenAdditionalInfo{
								Section: dynamicElementsPb.HomeScreenAdditionalInfo_SECTION_FEATURE_PRIMARY,
							},
						},
					},
				},
				collectedData: &DynamicElementsCollectedData{CurrentTier: tieringEnumPb.Tier_TIER_ONE_HUNDRED},
				mocks: func() {
					mockDataProcessor.EXPECT().IsMultipleWaysToEnterTierEnabled(gomock.Any(), "actor-1").Return(true, nil)
				},
			},
			wantLen: 0,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				gconf:                tt.fields.gconf,
				dataProcessor:        tt.fields.dataProcessor,
				savingsClient:        tt.fields.savingsClient,
				accountBalanceClient: tt.fields.accountBalanceClient,
			}
			tt.args.mocks()
			got, gotReason, err := s.getPromoWidgetElementsV2(tt.args.ctx, tt.args.req, tt.args.collectedData, tt.args.actorId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getPromoWidgetElementsV2() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotReason != tt.wantReason {
				t.Errorf("getPromoWidgetElementsV2() gotReason = %v, want %v", gotReason, tt.wantReason)
			}
			if len(got) != tt.wantLen {
				t.Errorf("getPromoWidgetElementsV2() got = %v, wantLen %v", got, tt.wantLen)
			}
		})
	}
}

// func TestService_getReactivationPromoWidget(t *testing.T) {
//	ctrl := gomock.NewController(t)
//	mockPayClient := mockPay.NewMockPayClient(ctrl)
//	mockSegmentationClient := mocks4.NewMockSegmentationServiceClient(ctrl)
//	defer ctrl.Finish()
//
//	type mockStruct struct {
//		mockPayClient          *mockPay.MockPayClient
//		mockSegmentationClient *mocks4.MockSegmentationServiceClient
//	}
//
//	type fields struct {
//		gconf *genconf.Config
//	}
//	type args struct {
//		ctx     context.Context
//		actorId string
//		mocks   func(mockStruct *mockStruct)
//	}
//	tests := []struct {
//		name    string
//		fields  fields
//		args    args
//		want    *dynamicElementsPb.DynamicElement
//		wantErr bool
//	}{
//		{
//			name: "success_0_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 0,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_0),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//		{
//			name: "success_1_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 1,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_1),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//		{
//			name: "success_2_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 2,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_2),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//		{
//			name: "success_3_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 3,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_3),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//		{
//			name: "success_4_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 4,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_4),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Make a payment >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_REWARDS_WAYS_TO_EARN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//		{
//			name: "success_5_txn",
//			fields: fields{
//				gconf: gconf,
//			},
//			args: args{
//				ctx:     context.Background(),
//				actorId: "actor-1",
//				mocks: func(m *mockStruct) {
//					m.mockSegmentationClient.EXPECT().GetSegmentEntryTimeForUser(gomock.Any(), gomock.Any()).Return(&segment.GetSegmentEntryTimeForUserResponse{
//						Status:    rpcPb.StatusOk(),
//						EntryTime: timestampPb.New(time.Now().Add(-time.Hour * 24)),
//					}, nil).AnyTimes()
//					m.mockPayClient.EXPECT().GetTransactionAggregates(gomock.Any(), gomock.Any()).Return(&pay.GetTransactionAggregatesResponse{
//						Status: rpcPb.StatusOk(),
//						TransactionAggregates: &pay.TransactionAggregates{
//							Count: 5,
//						},
//					}, nil)
//				},
//			},
//			want: &dynamicElementsPb.DynamicElement{
//				OwnerService:  typesPb.ServiceName_TIERING_SERVICE,
//				UtilityType:   dynamicElementsPb.ElementUtilityType_ELEMENT_UTILITY_TYPE_ALERT,
//				StructureType: dynamicElementsPb.ElementStructureType_ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS,
//				Content: &dynamicElementsPb.ElementContent{
//					Content: &dynamicElementsPb.ElementContent_FeatureWidgetWithFourPoints{
//						FeatureWidgetWithFourPoints: &dynamicElementsPb.FeatureWidgetWithFourPoints{
//							Card: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard_{
//								FullVisualElementCard: &dynamicElementsPb.FeatureWidgetWithFourPoints_FullVisualElementCard{
//									VisualElement: commontypes.GetVisualElementImageFromUrl(reactivationPromoWidgetImageUrl_5Txn_5),
//									Cta: &ui.IconTextComponent{
//										Texts: []*commontypes.Text{
//											{
//												DisplayValue: &commontypes.Text_PlainString{PlainString: "Claim your ₹250 >"},
//												FontColor:    "#37522A",
//											},
//										},
//										Deeplink: &deeplinkPb.Deeplink{
//											Screen: deeplinkPb.Screen_MY_REWARDS_SCREEN,
//										},
//									},
//									Shadow: &ui.Shadow{
//										Colour: ui.GetBlockColor("#AFD2A2"),
//									},
//								},
//							},
//						},
//					},
//				},
//				BizAnalyticsData: map[string]string{"BIZ_ANALYTICS_DATA_KEY_AREA": "Tiering"},
//			},
//			wantErr: false,
//		},
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			s := &Service{
//				payClient:          mockPayClient,
//				segmentationClient: mockSegmentationClient,
//				gconf:              tt.fields.gconf,
//			}
//			m := &mockStruct{
//				mockPayClient:          mockPayClient,
//				mockSegmentationClient: mockSegmentationClient,
//			}
//			tt.args.mocks(m)
//			got, err := s.getReactivationPromoWidget(tt.args.ctx, tt.args.actorId)
//			if (err != nil) != tt.wantErr {
//				t.Errorf("getReactivationPromoWidget() error = %v, wantErr %v", err, tt.wantErr)
//				return
//			}
//			if !reflect.DeepEqual(got, tt.want) {
//				t.Errorf("getReactivationPromoWidget() got = %v, want %v", got, tt.want)
//			}
//		})
//	}
// }
