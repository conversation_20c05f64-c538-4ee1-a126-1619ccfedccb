package tiering

import (
	"context"
	"fmt"

	cmap "github.com/orcaman/concurrent-map"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	moneyPb "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/vendorgateway"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/errgroup"
	"github.com/epifi/be-common/pkg/logger"
	accountBalancePb "github.com/epifi/gamma/api/accounts/balance"
	"github.com/epifi/gamma/api/accounts/balance/enums"
	"github.com/epifi/gamma/api/savings"
	"github.com/epifi/gamma/api/typesv2"
	"github.com/epifi/gamma/api/typesv2/account"
	"github.com/epifi/gamma/api/user"

	"github.com/epifi/gamma/api/kyc"
	tieringPb "github.com/epifi/gamma/api/tiering"
	criteriaPb "github.com/epifi/gamma/api/tiering/criteria"
	tieringEnumPb "github.com/epifi/gamma/api/tiering/enums"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	tieringErrors "github.com/epifi/gamma/tiering/errors"
	"github.com/epifi/gamma/tiering/tiermappings"
)

func (s *Service) getBalanceForActor(ctx context.Context, actorId string) (*moneyPb.Money, error) {
	// get current balance implementation of the actor
	balanceImpl, getImplErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_BALANCE)
	if getImplErr != nil {
		return nil, errors.Wrap(getImplErr, "error getting implementation for balance collector")
	}
	// collect balance data
	collectedData, collectDataErr := balanceImpl.CollectData(ctx, actorId)
	if collectDataErr != nil {
		return nil, errors.Wrap(collectDataErr, "error collecting balance data")
	}

	return collectedData.GetBalanceData().GetAvailableBalance(), nil
}

func (s *Service) getKycLevelForActor(ctx context.Context, actorId string) (kyc.KYCLevel, error) {
	// get current balance implementation of the actor
	kycImpl, getImplErr := s.dataCollectorFactory.GetImpl(tieringEnumPb.QualifyingCriteriaType_QUALIFYING_CRITERIA_TYPE_KYC)
	if getImplErr != nil {
		return kyc.KYCLevel_UNSPECIFIED, errors.Wrap(getImplErr, "error getting implementation for balance collector")
	}
	// collect balance data
	kycData, collectDataErr := kycImpl.CollectData(ctx, actorId)
	if collectDataErr != nil {
		return kyc.KYCLevel_UNSPECIFIED, errors.Wrap(collectDataErr, "error collecting balance data")
	}

	return kycData.GetKycData().GetKycLevel(), nil
}

// convertFromTierAndToTierToInternlTiers helper to convert from_tier and to_tier from external tiers to internal tiers
func convertFromTierAndToTierToInternlTiers(fromExtTier, toExtTier tieringExtPb.Tier) (tieringEnumPb.Tier, tieringEnumPb.Tier, error) {
	fromTier, fromTierErr := tiermappings.GetInternalTierFromExternalTier(fromExtTier)
	if fromTierErr != nil {
		return tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error converting external tier %s to internal tier, %w", fromExtTier.String(), fromTierErr)
	}
	toTier, toTierErr := tiermappings.GetInternalTierFromExternalTier(toExtTier)
	if toTierErr != nil {
		return tieringEnumPb.Tier_TIER_UNSPECIFIED, tieringEnumPb.Tier_TIER_UNSPECIFIED, fmt.Errorf("error converting external tier %s to internal tier, %w", toExtTier.String(), toTierErr)
	}
	return fromTier, toTier, nil
}

// gatherDataForPitchV2 gathers all the data that is need for pitch details based on actorId
func (s *Service) gatherDataForPitchV2(ctx context.Context, actorId string) (*PitchV2CollectedData, error) {
	data := &PitchV2CollectedData{}
	pitchDetailsErrGroup, _ := errgroup.WithContext(ctx)
	// get the current tier of the actor
	pitchDetailsErrGroup.Go(func() error {
		currentTier, getCurTierErr := s.dataProcessor.GetCurrentTierDefaultToBaseTier(ctx, actorId)
		if getCurTierErr != nil {
			return errors.Wrap(getCurTierErr, "error getting current tier for actor")
		}
		data.currentTier = currentTier
		return nil
	})
	// get tier options map
	pitchDetailsErrGroup.Go(func() error {
		tierOptionsMap, getTierOptionsErr := s.tierOptionsManager.GetTierOptionsMap(ctx, actorId)
		if getTierOptionsErr != nil {
			return errors.Wrap(getTierOptionsErr, "error getting tier options map")
		}
		data.tierOptionsMap = tierOptionsMap
		return nil
	})
	// get latest upgrade details
	var lastUpgradeMovement *tieringPb.TierMovementHistory
	pitchDetailsErrGroup.Go(func() error {
		var getTmhErr error
		lastUpgradeMovement, getTmhErr = s.tierMovementHistoryDao.GetLatestByActorIdAndMovementType(ctx, actorId, tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
		)
		if getTmhErr != nil && !errors.Is(getTmhErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(getTmhErr, "error getching latest upgrade tier movement for actor")
		}
		return nil
	})
	// get latest downgrade details
	var lastDowngradeMovement *tieringPb.TierMovementHistory
	pitchDetailsErrGroup.Go(func() error {
		var getTmhErr error
		lastDowngradeMovement, getTmhErr = s.tierMovementHistoryDao.GetLatestByActorIdAndMovementType(ctx, actorId, tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_FROM_TIER,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_TO_TIER,
			tieringPb.TierMovementHistoryFieldMask_TIER_MOVEMENT_HISTORY_FIELD_MASK_CREATED_AT,
		)
		if getTmhErr != nil && !errors.Is(getTmhErr, epifierrors.ErrRecordNotFound) {
			return errors.Wrap(getTmhErr, "error fetching latest downgrade tier movement for actor")
		}
		return nil
	})

	pitchDetailsErrGroup.Go(func() error {
		actorBaseTier, getBaseTierErr := s.dataProcessor.GetBaseTierForActor(ctx, actorId)
		if getBaseTierErr != nil {
			return errors.Wrap(getBaseTierErr, "error getting base tier for actor")
		}
		data.actorBaseTier = actorBaseTier
		return nil
	})

	pitchDetailsErrGroup.Go(func() error {
		latestDoneEtm, getLatestDoneEtmErr := s.eligibleTierMovementDao.GetLatestByActorIdAndStatus(ctx, actorId, tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_DONE)
		if getLatestDoneEtmErr != nil {
			if errors.Is(getLatestDoneEtmErr, epifierrors.ErrRecordNotFound) {
				return nil
			}
			return errors.Wrap(getLatestDoneEtmErr, "error getting latest done etm")
		}
		latestCriteriaChangeOptionType, latestCriteriaChangeOptionTypes, getEtmsErr := s.getOptionTypeForGivenParentEtm(ctx, latestDoneEtm)
		if getEtmsErr != nil {
			return errors.Wrap(getEtmsErr, "error getting criteria option types for current tier")
		}
		data.currentTierlatestDoneOptionType = latestDoneEtm.GetCriteriaOptionType()
		data.currentTierLatestCriteriaChangeOptionType = latestCriteriaChangeOptionType
		data.currentTierLatestCriteriaChangeOptionTypes = latestCriteriaChangeOptionTypes
		return nil
	})

	var lastUpgradeDoneOptionType, lastUpgradeCriteriaChangeOptionType tieringEnumPb.CriteriaOptionType
	pitchDetailsErrGroup.Go(func() error {
		latestDoneEtm, getLatestDoneEtmErr := s.eligibleTierMovementDao.GetLatestByActorIdMovementTypeStatus(ctx, actorId, tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_UPGRADE, tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_DONE)
		if getLatestDoneEtmErr != nil {
			if errors.Is(getLatestDoneEtmErr, epifierrors.ErrRecordNotFound) {
				return nil
			}
			return errors.Wrap(getLatestDoneEtmErr, "error getting latest done etm")
		}
		latestCriteriaChangeOptionType, _, getEtmsErr := s.getOptionTypeForGivenParentEtm(ctx, latestDoneEtm)
		if getEtmsErr != nil {
			return errors.Wrap(getEtmsErr, "error getting criteria option types for current tier")
		}
		lastUpgradeDoneOptionType = latestDoneEtm.GetCriteriaOptionType()
		lastUpgradeCriteriaChangeOptionType = latestCriteriaChangeOptionType
		return nil
	})

	var lastDowngradeDoneOptionType, lastDowngradeCriteriaChangeOptionType tieringEnumPb.CriteriaOptionType
	pitchDetailsErrGroup.Go(func() error {
		latestDoneEtm, getLatestDoneEtmErr := s.eligibleTierMovementDao.GetLatestByActorIdMovementTypeStatus(ctx, actorId,
			tieringEnumPb.TierMovementType_TIER_MOVEMENT_TYPE_DOWNGRADE,
			tieringEnumPb.EligibleTierMovementStatus_ELIGIBLE_TIER_MOVEMENT_STATUS_DONE)
		if getLatestDoneEtmErr != nil {
			if errors.Is(getLatestDoneEtmErr, epifierrors.ErrRecordNotFound) {
				return nil
			}
			return errors.Wrap(getLatestDoneEtmErr, "error getting latest done etm")
		}
		latestCriteriaChangeOptionType, _, getEtmsErr := s.getOptionTypeForGivenParentEtm(ctx, latestDoneEtm)
		if getEtmsErr != nil {
			return errors.Wrap(getEtmsErr, "error getting criteria option types for current tier")
		}
		lastDowngradeDoneOptionType = latestDoneEtm.GetCriteriaOptionType()
		lastDowngradeCriteriaChangeOptionType = latestCriteriaChangeOptionType
		return nil
	})

	pitchDetailsErr := pitchDetailsErrGroup.Wait()
	if pitchDetailsErr != nil {
		return nil, pitchDetailsErr
	}
	lastUpDetails, lastDownDetails, conversionErr := s.convertToLatestMovementDetails(lastUpgradeMovement, lastDowngradeMovement, lastUpgradeDoneOptionType, lastUpgradeCriteriaChangeOptionType, lastDowngradeDoneOptionType, lastDowngradeCriteriaChangeOptionType)
	if conversionErr != nil {
		return nil, conversionErr
	}
	data.lastUpgradeMovement = lastUpDetails
	data.lastDowngradeMovement = lastDownDetails
	return data, nil
}

func (s *Service) getOptionTypeForGivenParentEtm(ctx context.Context, parentEtm *tieringPb.EligibleTierMovement) (tieringEnumPb.CriteriaOptionType, []tieringEnumPb.CriteriaOptionType, error) {
	etmRefIds := parentEtm.GetDetails().GetCriteriaChangeRefIds()
	if len(etmRefIds) == 0 {
		return parentEtm.GetCriteriaOptionType(), parentEtm.GetEvaluatorMeta().GetSatisfiedCriteriaOptionTypes(), nil
	}
	lastCriteriaChangeEtm := etmRefIds[len(etmRefIds)-1]
	latestCriteriaChangeEtm, getLatestCriteriaChangeEtm := s.eligibleTierMovementDao.Get(ctx, lastCriteriaChangeEtm)
	if getLatestCriteriaChangeEtm != nil {
		if errors.Is(getLatestCriteriaChangeEtm, epifierrors.ErrRecordNotFound) {
			return parentEtm.GetCriteriaOptionType(), parentEtm.GetEvaluatorMeta().GetSatisfiedCriteriaOptionTypes(), nil
		}
		return tieringEnumPb.CriteriaOptionType_CRITERIA_OPTION_TYPE_UNSPECIFIED, nil, errors.Wrap(getLatestCriteriaChangeEtm, "error getting latest criteria change etm")
	}
	return latestCriteriaChangeEtm.GetCriteriaOptionType(), latestCriteriaChangeEtm.GetEvaluatorMeta().GetSatisfiedCriteriaOptionTypes(), nil
}

func (s *Service) convertToLatestMovementDetails(lastUpgradeMovement, lastDowngradeMovement *tieringPb.TierMovementHistory, lastUpgradeDoneOptionType, lastUpgradeCriteriaChangeOptionType, lastDowngradeDoneOptionType, lastDowngradeCriteriaChangeOptionType tieringEnumPb.CriteriaOptionType) (
	*tieringExtPb.LatestMovementDetails, *tieringExtPb.LatestMovementDetails, error) {
	var lastUpDetails, lastDownDetails *tieringExtPb.LatestMovementDetails
	if lastUpgradeMovement != nil {
		upgradeFromTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(lastUpgradeMovement.GetFromTier())
		if conversionErr != nil {
			if lastUpgradeMovement.GetFromTier() == tieringEnumPb.Tier_TIER_UNSPECIFIED {
				var getErr error
				upgradeFromTier, getErr = s.getExternalBaseTierForActor(context.Background(), lastUpgradeMovement.GetActorId())
				if getErr != nil {
					return nil, nil, errors.Wrap(getErr, "error getting base tier for actor")
				}
			} else {
				return nil, nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
			}
		}
		upgradeToTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(lastUpgradeMovement.GetToTier())
		if conversionErr != nil {
			return nil, nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
		}
		lastUpDetails = &tieringExtPb.LatestMovementDetails{
			FromTier:                 upgradeFromTier,
			ToTier:                   upgradeToTier,
			MovementTimestamp:        lastUpgradeMovement.GetCreatedAt(),
			EntryCriteriaOptionType:  lastUpgradeDoneOptionType,
			LatestCriteriaOptionType: lastUpgradeCriteriaChangeOptionType,
		}
	}
	if lastDowngradeMovement != nil {
		downgradeFromTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(lastDowngradeMovement.GetFromTier())
		if conversionErr != nil {
			return nil, nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
		}
		downgradeToTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(lastDowngradeMovement.GetToTier())
		if conversionErr != nil {
			if lastDowngradeMovement.GetToTier() == tieringEnumPb.Tier_TIER_UNSPECIFIED {
				downgradeToTier = tieringExtPb.Tier_TIER_FI_BASIC
			} else {
				return nil, nil, errors.Wrap(conversionErr, "error converting internal tier to BE external tier")
			}
		}
		lastDownDetails = &tieringExtPb.LatestMovementDetails{
			FromTier:                 downgradeFromTier,
			ToTier:                   downgradeToTier,
			MovementTimestamp:        lastDowngradeMovement.GetCreatedAt(),
			EntryCriteriaOptionType:  lastDowngradeDoneOptionType,
			LatestCriteriaOptionType: lastDowngradeCriteriaChangeOptionType,
		}
	}
	return lastUpDetails, lastDownDetails, nil
}

// TODO(sainath), TODO(obed): Combine tier options and tier mappings package to avoid this, asap
func getAllInternalAndExternalTiers(tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option) ([]tieringEnumPb.Tier, []tieringExtPb.Tier, error) {
	_, isSalaryLiteActiveForActor := tierOptionsMap[tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED]
	_, isTwoFiftyActive := tierOptionsMap[tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_FIFTY]
	_, isTwoSeventyActive := tierOptionsMap[tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_SEVENTY]
	_, isTwoNinetyActive := tierOptionsMap[tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY]
	_, isFiveActive := tierOptionsMap[tieringEnumPb.Tier_TIER_FIVE]
	_, isEightHundredActive := tierOptionsMap[tieringEnumPb.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED]

	allInternalTiers := tiermappings.GetAllActiveInternalTiers()
	internalTiers := make([]tieringEnumPb.Tier, 0)
	for _, intTier := range allInternalTiers {
		// only include aa salary tier if AA salary is active for actor
		// AA salary tier is active for user if we get it as option in tierOptionsMap
		if intTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_FIFTY && !isTwoFiftyActive {
			continue
		}
		if intTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_SEVENTY && !isTwoSeventyActive {
			continue
		}
		if intTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_TWO_NINETY && !isTwoNinetyActive {
			continue
		}
		// only include salary lite tier if salary lite is active for actor
		// salary lite tier is active for user if we get it as option in tierOptionsMap
		if intTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_FIVE_HUNDRED && !isSalaryLiteActiveForActor {
			continue
		}
		// only include regular tier if it is active for actor
		if intTier == tieringEnumPb.Tier_TIER_FIVE && !isFiveActive {
			continue
		}
		// only include salary basic tier if it is active for actor
		if intTier == tieringEnumPb.Tier_TIER_ONE_THOUSAND_EIGHT_HUNDRED && !isEightHundredActive {
			continue
		}
		internalTiers = append(internalTiers, intTier)
	}

	allExternalTiers, getAllExtTierErr := tiermappings.GetAllActiveExternalTiers()
	if getAllExtTierErr != nil {
		return nil, nil, errors.Wrap(getAllExtTierErr, "error getting all active external tiers")
	}
	externalTiers := make([]tieringExtPb.Tier, 0)
	for _, extTier := range allExternalTiers {
		if extTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_1 && !isTwoFiftyActive {
			continue
		}
		if extTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2 && !isTwoSeventyActive {
			continue
		}
		if extTier == tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_2 && !isTwoNinetyActive {
			continue
		}
		if extTier == tieringExtPb.Tier_TIER_FI_SALARY_LITE && !isSalaryLiteActiveForActor {
			continue
		}
		if extTier == tieringExtPb.Tier_TIER_FI_REGULAR && !isFiveActive {
			continue
		}
		if extTier == tieringExtPb.Tier_TIER_FI_SALARY_BASIC && !isEightHundredActive {
			continue
		}
		externalTiers = append(externalTiers, extTier)
	}
	return internalTiers, externalTiers, nil
}

// getTierMovementDetails returns tier movement details for all possible tier movements
func (s *Service) getTierMovementDetails(ctx context.Context, actorId string, currentTier tieringEnumPb.Tier,
	tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option) ([]*tieringExtPb.MovementExternalDetails, error) {
	// Since we are doing concurrent calls order is not guaranteed
	// So maintaining a map to collect movement details for each tier and making a slice at the end
	movementExtDetailsMap := cmap.New()
	movementDetailsErrGrp, _ := errgroup.WithContext(ctx)
	internalTiers, allExtTiers, getAllIntAndExtTiersErr := getAllInternalAndExternalTiers(tierOptionsMap)
	if getAllIntAndExtTiersErr != nil {
		return nil, errors.Wrap(getAllIntAndExtTiersErr, "error fetching all internal and external active tiers")
	}
	// higher tier upgrade details
	for _, internalTierUnscoped := range internalTiers {
		internalTier := internalTierUnscoped
		switch {
		case internalTier.Number() < currentTier.Number():
			lowerTierMovementDetails, getLowerTierDetailsErr := s.getLowerTierMovementDetails(currentTier, internalTier, tierOptionsMap)
			if getLowerTierDetailsErr != nil {
				return nil, errors.Wrap(getLowerTierDetailsErr, "error getting lower tier movement details")
			}
			movementExtDetailsMap.Set(lowerTierMovementDetails.GetTierName().String(), lowerTierMovementDetails)
		case internalTier == currentTier:
			// current tier grace details
			movementDetailsErrGrp.Go(func() error {
				curTierMovementDetails, getCurTierDetailsErr := s.getCurrentTierMovementDetails(ctx, actorId, currentTier, tierOptionsMap)
				if getCurTierDetailsErr != nil {
					return errors.Wrap(getCurTierDetailsErr, "error getting current tier movement details")
				}
				movementExtDetailsMap.Set(curTierMovementDetails.GetTierName().String(), curTierMovementDetails)
				return nil
			})
		case internalTier.Number() > currentTier.Number():
			movementDetailsErrGrp.Go(func() error {
				higherTierMovementDetails, getHigherTierDetailsErr := s.getHigherTierMovementDetails(ctx, actorId, currentTier, internalTier, tierOptionsMap)
				if getHigherTierDetailsErr != nil {
					return fmt.Errorf("error getting higher tier movement details, %s: %s, %s: %s,%w",
						logger.FROM_TIER, currentTier, logger.TO_TIER, internalTier, getHigherTierDetailsErr)
				}
				movementExtDetailsMap.Set(higherTierMovementDetails.GetTierName().String(), higherTierMovementDetails)
				return nil
			})
		}
	}
	movementDetailsErr := movementDetailsErrGrp.Wait()
	if movementDetailsErr != nil {
		return nil, movementDetailsErr
	}
	var movementDetails []*tieringExtPb.MovementExternalDetails
	for _, extTier := range allExtTiers {
		extDetails, ok := movementExtDetailsMap.Get(extTier.String())
		if ok {
			movementDetails = append(movementDetails, extDetails.(*tieringExtPb.MovementExternalDetails))
		}
	}
	return movementDetails, nil
}

// getLowerTierMovementDetails returns tier movements details for lower tiers
func (s *Service) getLowerTierMovementDetails(currentTier, lowerTier tieringEnumPb.Tier,
	tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option) (*tieringExtPb.MovementExternalDetails, error) {
	lowerExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(lowerTier)
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error getting lower external tier from internal tier")
	}
	lowerTierOptions, ok := tierOptionsMap[lowerTier]
	if !ok {
		return nil, fmt.Errorf("%s tier has no tier options mapped", lowerTier.String())
	}
	return &tieringExtPb.MovementExternalDetails{
		TierName:          lowerExtTier,
		IsMovementAllowed: false,
		Options:           lowerTierOptions,
	}, nil
}

// getCurrentTierMovementDetails returns tier movements details for current tier
func (s *Service) getCurrentTierMovementDetails(ctx context.Context, actorId string, currentTier tieringEnumPb.Tier,
	tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option) (*tieringExtPb.MovementExternalDetails, error) {
	currentExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(currentTier)
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error getting current external tier from internal tier")
	}
	isUserInGrace, graceDetails, graceCheckErr := s.tierTimelineManager.IsUserInGracePeriod(ctx, actorId)
	if graceCheckErr != nil {
		return nil, errors.Wrap(graceCheckErr, "error checking if user in grace period or not")
	}
	curTierOptions, ok := tierOptionsMap[currentTier]
	if !ok {
		return nil, fmt.Errorf("%s tier has not tier options mapped", currentTier.String())
	}
	var graceTimestamp *timestampPb.Timestamp
	var graceParams *tieringExtPb.GraceParams
	if isUserInGrace {
		graceTimestamp = graceDetails.GetMovementTimestamp()
		graceParams = &tieringExtPb.GraceParams{Period: graceDetails.GetGracePeriod()}
	}
	return &tieringExtPb.MovementExternalDetails{
		TierName:          currentExtTier,
		IsMovementAllowed: !isUserInGrace,
		MovementTimestamp: graceTimestamp,
		Options:           curTierOptions,
		GraceParams:       graceParams,
	}, nil
}

// getHigherTierMovementDetails returns tier movement details for higher tiers
func (s *Service) getHigherTierMovementDetails(ctx context.Context, actorId string, currentTier, higherTier tieringEnumPb.Tier,
	tierOptionsMap map[tieringEnumPb.Tier][]*criteriaPb.Option) (*tieringExtPb.MovementExternalDetails, error) {
	higherExtTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(higherTier)
	if conversionErr != nil {
		return nil, errors.Wrap(conversionErr, "error getting higher external tier from internal tier")
	}
	isEligible, getEligibilityErr := s.movementManager.IsEligibleForTierUpgrade(ctx, actorId, currentTier, higherTier)
	if getEligibilityErr != nil {
		if !(errors.Is(getEligibilityErr, tieringErrors.ErrCooloff) ||
			errors.Is(getEligibilityErr, tieringErrors.ErrEligibleMovementNotMatured)) {
			return nil, fmt.Errorf("error checking if user is eligible for tier upgrade, %s: %s, %s: %s,%w",
				logger.FROM_TIER, currentTier, logger.TO_TIER, higherTier, getEligibilityErr)
		}
		logger.Debug(ctx, "non critical error while checking for eligibility", zap.Error(getEligibilityErr))
		isEligible = false
	}
	higherTierOptions, ok := tierOptionsMap[higherTier]
	if !ok {
		return nil, fmt.Errorf("%s tier has no tier options mapped", higherTier.String())
	}
	var movementTimestamp *timestampPb.Timestamp
	if !isEligible {
		var getUpgradeDetailsErr error
		movementTimestamp, _, getUpgradeDetailsErr = s.tierTimelineManager.GetUpgradeDetails(ctx, actorId)
		if getUpgradeDetailsErr != nil {
			return nil, errors.Wrap(getUpgradeDetailsErr, "error fetching upgrade details for user")
		}
	}
	return &tieringExtPb.MovementExternalDetails{
		TierName:          higherExtTier,
		IsMovementAllowed: isEligible,
		MovementTimestamp: movementTimestamp,
		Options:           higherTierOptions,
	}, nil
}

func (s *Service) getExternalBaseTierForActor(ctx context.Context, actorId string) (tieringExtPb.Tier, error) {
	baseTier, getBaseTierErr := s.dataProcessor.GetBaseTierForActor(ctx, actorId)
	if getBaseTierErr != nil {
		return 0, errors.Wrap(getBaseTierErr, "error getting base tier for actor")
	}

	extBaseTier, conversionErr := tiermappings.GetExternalTierFromInternalTier(baseTier)
	if conversionErr != nil {
		return 0, errors.Wrap(conversionErr, "error converting internal tier to external tier")
	}

	return extBaseTier, nil
}

func (s *Service) getSavingsAccountBalance(ctx context.Context, actorId string) (*moneyPb.Money, error) {
	savingsResp, savingsErr := s.savingsClient.GetSavingsAccountEssentials(ctx, &savings.GetSavingsAccountEssentialsRequest{
		Filter: &savings.GetSavingsAccountEssentialsRequest_ActorUniqueAccountIdentifier{ActorUniqueAccountIdentifier: &savings.ActorUniqueAccountIdentifier{
			ActorId:                actorId,
			AccountProductOffering: account.AccountProductOffering_APO_REGULAR,
			PartnerBank:            vendorgateway.Vendor_FEDERAL_BANK,
		},
		},
	})
	if rpcErr := epifigrpc.RPCError(savingsResp, savingsErr); rpcErr != nil {
		return nil, fmt.Errorf("error getting savings account essentials: %w", rpcErr)
	}

	getBalResp, getBalErr := s.accountBalanceClient.GetAccountBalance(ctx, &accountBalancePb.GetAccountBalanceRequest{
		Identifier: &accountBalancePb.GetAccountBalanceRequest_Id{
			Id: savingsResp.GetAccount().GetId(),
		},
		ActorId:            actorId,
		DataFreshness:      enums.DataFreshness_HISTORICAL,
		ForceBalanceUpdate: accountBalancePb.ForceBalanceUpdate_FORCE_BALANCE_UPDATE_NOT_NEEDED,
	})
	if rpcErr := epifigrpc.RPCError(getBalResp, getBalErr); rpcErr != nil {
		return nil, fmt.Errorf("error getting account balance: %w", rpcErr)
	}

	return getBalResp.GetAvailableBalance(), nil
}

func (s *Service) getDeviceId(ctx context.Context, actorId string) (string, error) {
	userDeviceProp, userDevicePropErr := s.userClient.GetUserDeviceProperties(ctx, &user.GetUserDevicePropertiesRequest{
		ActorId: actorId,
		PropertyTypes: []typesv2.DeviceProperty{
			typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID,
		},
	})
	if rpcErr := epifigrpc.RPCError(userDeviceProp, userDevicePropErr); rpcErr != nil {
		return "", fmt.Errorf("error fetching user device properties: %w", rpcErr)
	}

	for _, prop := range userDeviceProp.GetUserDevicePropertyList() {
		if prop.GetDeviceProperty() == typesv2.DeviceProperty_DEVICE_PROP_DEVICE_ID {
			return prop.GetPropertyValue().GetDeviceId(), nil
		}
	}

	return "", errors.New("device ID not found in GetUserDeviceProperties response")
}
