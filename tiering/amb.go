package tiering

import (
	"context"
	"time"

	"go.uber.org/zap"
	gmoney "google.golang.org/genproto/googleapis/type/money"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/cfg"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/money"

	tieringPb "github.com/epifi/gamma/api/tiering"
	tieringExtPb "github.com/epifi/gamma/api/tiering/external"
	tieringPinotPb "github.com/epifi/gamma/api/tiering/pinot"
	pkgTiering "github.com/epifi/gamma/pkg/tiering"
)

// getAMBDetailsForCx calculates AMB-related data for CX display
func (s *Service) getAMBDetailsForCx(ctx context.Context, actorId string) (string, string, string, []*tieringPb.AmbDetails, error) {
	// Get AMB info using the existing GetAMBInfo method
	ambInfoResp, err := s.GetAMBInfo(ctx, &tieringPb.GetAMBInfoRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(ambInfoResp, err); te != nil {
		logger.Error(ctx, "error getting amb info", zap.Error(te))
	}
	// Get AMB history
	var ambHistory []*tieringPb.AmbDetails
	ambHistory, err = s.getAMBHistory(ctx, actorId)
	if err != nil {
		// log and move on since it's optional
		logger.Error(ctx, "error getting AMB history", zap.Error(err))
	}

	// Format AMB values as strings
	currentAmbStr := money.ToDisplayStringInIndianFormat(ambInfoResp.GetCurrentAmb(), 2, true)
	requiredAmbStr := money.ToDisplayStringInIndianFormat(ambInfoResp.GetTargetAmb(), 2, true)
	shortfallStr := money.ToDisplayStringInIndianFormat(ambInfoResp.GetShortfallAmount(), 2, true)

	return currentAmbStr, requiredAmbStr, shortfallStr, ambHistory, nil
}

// getAMBHistory fetches and formats AMB history data
func (s *Service) getAMBHistory(ctx context.Context, actorId string) ([]*tieringPb.AmbDetails, error) {
	// Get tier time ranges for AMB history - last 3 months
	tierRangesResp, err := s.GetTierTimeRangesForActor(ctx, &tieringPb.GetTierTimeRangesForActorRequest{
		ActorId: actorId,
		Tiers:   []tieringExtPb.Tier{tieringExtPb.Tier_TIER_FI_INFINITE, tieringExtPb.Tier_TIER_FI_BASIC, tieringExtPb.Tier_TIER_FI_PLUS, tieringExtPb.Tier_TIER_FI_REGULAR, tieringExtPb.Tier_TIER_FI_AA_SALARY_BAND_3, tieringExtPb.Tier_TIER_FI_SALARY_BASIC, tieringExtPb.Tier_TIER_FI_SALARY},
		// Start date filter - get periods from last 3 months
		FilterFrom: timestampPb.New(time.Now().AddDate(0, -3, 0)),
	})
	if te := epifigrpc.RPCError(tierRangesResp, err); te != nil {
		logger.Error(ctx, "error getting tier time ranges", zap.Error(te))
		// Continue without history
		return nil, te
	}

	// Use the common function for getting AMB history
	return pkgTiering.GetAMBHistory(ctx, actorId, s.tieringPinotClient, tierRangesResp.GetTierTimeRangesMap())
}

// GetAMBInfo returns the current projected AMB and target AMB for a user
func (s *Service) GetAMBInfo(ctx context.Context, req *tieringPb.GetAMBInfoRequest) (*tieringPb.GetAMBInfoResponse, error) {
	actorId := req.GetActorId()

	// Get the current tier from GetTierAtTime
	tierResp, err := s.GetCurrentTierForActor(ctx, &tieringPb.GetCurrentTierForActorRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(tierResp, err); te != nil {
		logger.Error(ctx, "error getting tier at time info", zap.Error(te))
		return &tieringPb.GetAMBInfoResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	currentTier := tierResp.GetTier()

	// Get tiering pitch response to get tier criteria min values
	tieringPitchResp, err := s.GetTieringPitchV2(ctx, &tieringPb.GetTieringPitchV2Request{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(tieringPitchResp, err); te != nil {
		logger.Error(ctx, "error getting tier pitch", zap.Error(te))
		return &tieringPb.GetAMBInfoResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	trialDetailsResp, err := s.GetTrialDetails(ctx, &tieringPb.GetTrialDetailsRequest{
		ActorId: actorId,
	})
	if te := epifigrpc.RPCError(trialDetailsResp, err); te != nil {
		logger.Error(ctx, "error getting trial details", zap.Error(te))
		return &tieringPb.GetAMBInfoResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	regularTierConfigParams := &tieringPb.RegularTierConfigParams{
		MinBalanceForRegularTier:        &gmoney.Money{CurrencyCode: "INR", Units: int64(s.gconf.MinAvgMonthlyBalanceForRegularTier())},
		MinBalancePenaltyForRegularTier: &gmoney.Money{CurrencyCode: "INR", Units: int64(s.gconf.MinBalancePenaltyForRegularTier())},
	}

	// Get target AMB from tiering pitch
	targetAMB, err := pkgTiering.GetTargetAMBFromTieringPitch(tieringPitchResp, regularTierConfigParams, trialDetailsResp)
	if err != nil {
		logger.Error(ctx, "error getting target AMB", zap.Error(err))
		return &tieringPb.GetAMBInfoResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}

	// Get account balance
	balanceInfo, err := s.getBalanceForActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error getting balance for actor", zap.Error(err))
		return &tieringPb.GetAMBInfoResponse{
			Status: rpc.StatusInternal(),
		}, nil
	}
	currentBalance := money.ToDecimal(balanceInfo).InexactFloat64()

	// Calculate dates for AMB calculation
	currentTime := time.Now().Local()
	currentDay := currentTime.Day()

	// Get the start of the current month
	startOfMonth := datetime.StartOfMonth(currentTime)
	fromDate := startOfMonth

	// Calculate total days in month
	endOfMonth := datetime.EndOfMonth(startOfMonth)
	totalDaysInMonth := endOfMonth.Day()

	// For tracking days so far in the period
	daysSoFar := currentDay

	// For tiers other than REGULAR, use the later of (month start, tier movement timestamp)
	if currentTier != tieringExtPb.Tier_TIER_FI_REGULAR {
		// Get the last movement timestamp from the TierInfo
		lastMovementTime := pkgTiering.GetLatestMovementDetails(tieringPitchResp).GetMovementTimestamp().AsTime()

		// Only use tier movement timestamp if it's after the month start and not in the future
		if !lastMovementTime.IsZero() && lastMovementTime.After(fromDate) && lastMovementTime.Before(currentTime) {
			fromDate = lastMovementTime
			// Update the days so far based on the tier movement date
			daysSoFar = currentDay - fromDate.Day() + 1
		}
	}

	// Get average EOD balance for date range
	avgEODBalance := 0.0
	if daysSoFar > 1 {
		// Create timestamps for the query
		fromTimestamp := timestampPb.New(fromDate)
		// Use yesterday as the end date for historical data
		yesterdayEnd := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day()-1, 23, 59, 59, 0, currentTime.Location())
		toTimestamp := timestampPb.New(yesterdayEnd)

		// Get the average EOD balance for the period
		eodBalanceResp, eodErr := s.tieringPinotClient.GetAverageEODBalanceInDateRange(ctx, &tieringPinotPb.GetAverageEODBalanceInDateRangeRequest{
			ActorId:       actorId,
			FromTimestamp: fromTimestamp,
			ToTimestamp:   toTimestamp,
		})

		if rpcErr := epifigrpc.RPCError(eodBalanceResp, eodErr); rpcErr != nil {
			if cfg.IsStagingEnv(s.gconf.Application().Environment) || cfg.IsQaEnv(s.gconf.Application().Environment) {
				logger.Warn("using dummy values on non prod")
				avgEODBalance = 4892.3
			} else {
				logger.Error(ctx, "error getting average EOD balance", zap.Error(rpcErr))
				return &tieringPb.GetAMBInfoResponse{
					Status: rpc.StatusInternal(),
				}, nil
			}
		} else {
			avgEODBalance = eodBalanceResp.GetAvgBalance()
		}
	}

	// Calculate relevant period parameters
	effectiveEndDate := endOfMonth
	if fromDate.After(effectiveEndDate) {
		fromDate = effectiveEndDate
	}
	totalDaysInRelevantPeriod := effectiveEndDate.Day() - fromDate.Day() + 1

	// Calculate sums for past days and projections for remaining days
	passedDaysSum := 0.0
	if daysSoFar > 1 {
		// Sum of balances for days that have already passed - use real data
		// We subtract 1 from daysSoFar because we don't count today in the passed days
		numberOfPastDaysInPeriod := float64(daysSoFar - 1)
		if numberOfPastDaysInPeriod > 0 {
			passedDaysSum = avgEODBalance * numberOfPastDaysInPeriod
		}
	}

	// Calculate the number of remaining days (including today) until the end of the month
	remainingDays := float64(totalDaysInMonth - currentDay + 1)
	if remainingDays < 1 {
		remainingDays = 1 // Ensure we don't divide by zero
	}

	// Calculate projected EOD sum for remaining days (assuming current balance for all remaining days)
	projectedRemainingDaysSum := currentBalance * remainingDays

	// Calculate total projected sum for the relevant period
	projectedTotalSum := passedDaysSum + projectedRemainingDaysSum

	// Calculate projected AMB for the entire month
	projectedAMB := projectedTotalSum / float64(totalDaysInRelevantPeriod)

	// Calculate shortfall amount
	var shortfallAmount float64 = 0
	if projectedAMB < targetAMB {
		// Calculate the target sum needed for the relevant period
		targetSumRelevantPeriod := targetAMB * float64(totalDaysInRelevantPeriod)

		// Calculate the sum required from today until the end of the month
		sumNeededFromToday := targetSumRelevantPeriod - passedDaysSum

		// Calculate the average balance needed for the remaining days
		avgBalanceNeededForRemainingDays := sumNeededFromToday / remainingDays

		// The amount needed is the difference between the required average daily balance for the remaining days
		// and the current balance. This tells the user how much to add right now to stay on track.
		shortfallAmount = avgBalanceNeededForRemainingDays - currentBalance
		if shortfallAmount < 0 {
			shortfallAmount = 0
		}
	}

	// Prepare response
	response := &tieringPb.GetAMBInfoResponse{
		Status:          rpc.StatusOk(),
		CurrentAmb:      money.ParseFloat(projectedAMB, money.RupeeCurrencyCode),
		TargetAmb:       money.ParseFloat(targetAMB, money.RupeeCurrencyCode),
		CurrentTier:     tierResp.GetTier(),
		ShortfallAmount: money.ParseFloat(shortfallAmount, money.RupeeCurrencyCode),
	}

	return response, nil
}
