package healthinsurance

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	commonvgpb "github.com/epifi/be-common/api/vendorgateway"

	typesPb "github.com/epifi/gamma/api/typesv2"

	"github.com/aws/aws-sdk-go-v2/aws"
	"go.uber.org/zap"
	timestampPb "google.golang.org/protobuf/types/known/timestamppb"

	"github.com/epifi/be-common/api/rpc"
	"github.com/epifi/be-common/pkg/datetime"
	"github.com/epifi/be-common/pkg/epificontext"
	"github.com/epifi/be-common/pkg/epifierrors"
	"github.com/epifi/be-common/pkg/epifigrpc"
	"github.com/epifi/be-common/pkg/idgen"
	"github.com/epifi/be-common/pkg/lock"
	"github.com/epifi/be-common/pkg/logger"
	"github.com/epifi/be-common/pkg/pagination"
	storagev2 "github.com/epifi/be-common/pkg/storage/v2"

	employmentPb "github.com/epifi/gamma/api/employment"
	salaryprogramPb "github.com/epifi/gamma/api/salaryprogram"
	healthinsurancePb "github.com/epifi/gamma/api/salaryprogram/healthinsurance"
	consumerPb "github.com/epifi/gamma/api/salaryprogram/healthinsurance/consumer"
	"github.com/epifi/gamma/salaryprogram/config"
	"github.com/epifi/gamma/salaryprogram/healthinsurance/dao"
	"github.com/epifi/gamma/salaryprogram/healthinsurance/insurancevendor"
	"github.com/epifi/gamma/salaryprogram/helper"
	salaryTypes "github.com/epifi/gamma/salaryprogram/wire/types"
)

// key used for acquiring a distributed lock for handling race conditions in policy request update flows
const policyRequestUpdateFlowLockKeyTemplate = "salaryprogram:healthinsurance:policyrequestupdateflow:%s"
const userOnboardingAlreadyDoneErrorMessage = "Member cant be added to more than one health plan from the same organization"
const userOnboardingAlreadyDoneWithDifferentCredentialsErrorMessage = "self already exists with different details"
const userPolicyAlreadyCancelledErrorMessage = "Invalid employerId or plan"

type Service struct {
	salaryClient                           salaryprogramPb.SalaryProgramClient
	policyIssuanceReqDao                   dao.IPolicyIssuanceRequestDao
	policyDetailsDao                       dao.IPolicyDetailsDao
	vendorFactory                          insurancevendor.IVendorFactory
	pollPolicyPurchaseStatusDelayPublisher salaryTypes.HealthInsurancePollPolicyPurchaseStatusEventSqsPublisher
	distributedLockManager                 lock.ILockManager
	conf                                   *config.Config
	userHelperSvc                          helper.IUserHelperService
	txnExecutor                            storagev2.TxnExecutor
}

func NewService(salaryClient salaryprogramPb.SalaryProgramClient, policyIssuanceReqDao dao.IPolicyIssuanceRequestDao, policyDetailsDao dao.IPolicyDetailsDao, vendorFactory insurancevendor.IVendorFactory,
	pollPolicyPurchaseStatusDelayPublisher salaryTypes.HealthInsurancePollPolicyPurchaseStatusEventSqsPublisher, distributedLockManager lock.ILockManager, conf *config.Config, userHelperSvc helper.IUserHelperService, txnExecutor storagev2.TxnExecutor) *Service {
	return &Service{salaryClient: salaryClient, policyIssuanceReqDao: policyIssuanceReqDao, policyDetailsDao: policyDetailsDao, vendorFactory: vendorFactory, pollPolicyPurchaseStatusDelayPublisher: pollPolicyPurchaseStatusDelayPublisher, distributedLockManager: distributedLockManager, conf: conf,
		userHelperSvc: userHelperSvc, txnExecutor: txnExecutor}
}

// compile time check to ensure Service implements healthinsurancePb.HealthInsuranceServer
var _ healthinsurancePb.HealthInsuranceServer = &Service{}

// InitiatePolicyPurchase rpc is useful to create a request for purchasing a new health insurance policy.
//
//nolint:gocritic
func (s *Service) InitiatePolicyPurchase(ctx context.Context, req *healthinsurancePb.InitiatePolicyPurchaseRequest) (*healthinsurancePb.InitiatePolicyPurchaseResponse, error) {
	// check if new policy purchase is allowed or not.
	isPurchaseAllowed, err := s.isNewPolicyPurchaseAllowed(ctx, req.GetActorId())
	if err != nil {
		logger.Error(ctx, "error checking if policy purchase is allowed or not", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
		return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if !isPurchaseAllowed {
		return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("new policy purchase is not allowed")}, nil
	}

	vendor, err := s.getVendorBasedOnUserEmployerChannelAndPolicyType(ctx, req.GetActorId(), req.GetPolicyType())
	if err != nil {
		return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error finding the vendor for policy issuance, err : %v", err.Error()))}, nil
	}

	policyIssuanceReq := &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
		ActorId:      req.GetActorId(),
		PolicyVendor: vendor,
	}

	switch vendor {
	case commonvgpb.Vendor_RISKCOVRY:
		// create policy issuance request
		policyIssuanceReq, err = s.checkAndCreatePolicyIssuanceRequestWithLock(ctx, req.GetActorId(), req.GetPolicyType())
		if err != nil {
			logger.Error(ctx, "error creating a new policy issuance ", zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.Error(err))
			return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
	default:
	}

	// get redirection url for policy purchase
	vendorFactoryImpl, err := s.vendorFactory.Get(policyIssuanceReq.GetPolicyVendor())
	if err != nil {
		logger.Error(ctx, "error getting vendor processor", zap.String(logger.VENDOR, policyIssuanceReq.GetPolicyVendor().String()), zap.Error(err))
		return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	policyPurchaseInfo, err := vendorFactoryImpl.GetPolicyPurchaseUrl(ctx, policyIssuanceReq)
	if err != nil {
		logger.Error(ctx, "error creating policy purchase redirection url", zap.String(logger.REQUEST_ID, policyIssuanceReq.GetVendorRequestId()), zap.Error(err))
		return &healthinsurancePb.InitiatePolicyPurchaseResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	var initiatePolicyPurchaseRes *healthinsurancePb.InitiatePolicyPurchaseResponse
	switch policyPurchaseInfo.(type) {
	case *insurancevendor.RiskcovryPolicyPurchaseInfo:
		riskcovryPolicyPurchaseInfo := policyPurchaseInfo.(*insurancevendor.RiskcovryPolicyPurchaseInfo)
		logger.SecureInfo(ctx, commonvgpb.Vendor_RISKCOVRY, "generated policy purchase redirection url", zap.String(logger.URL, riskcovryPolicyPurchaseInfo.RedirectionUrl), zap.String(logger.ACTOR_ID_V2, req.GetActorId()), zap.String(logger.REQUEST_ID, policyIssuanceReq.GetVendorRequestId()))
		initiatePolicyPurchaseRes = &healthinsurancePb.InitiatePolicyPurchaseResponse{
			Status:                       rpc.StatusOk(),
			PolicyPurchaseRedirectionUrl: riskcovryPolicyPurchaseInfo.RedirectionUrl,
			PolicyPurchaseRedirectionInfo: &healthinsurancePb.InitiatePolicyPurchaseResponse_RiskcovryInfo{
				RiskcovryInfo: &healthinsurancePb.RiskcovryInfo{
					RedirectionUrl: riskcovryPolicyPurchaseInfo.RedirectionUrl,
				},
			},
		}
	case *insurancevendor.OnsurityPolicyPurchaseInfo:
		onsurityPolicyPurchaseInfo := policyPurchaseInfo.(*insurancevendor.OnsurityPolicyPurchaseInfo)
		initiatePolicyPurchaseRes = &healthinsurancePb.InitiatePolicyPurchaseResponse{
			Status: rpc.StatusOk(),
			PolicyPurchaseRedirectionInfo: &healthinsurancePb.InitiatePolicyPurchaseResponse_OnsurityInfo{
				OnsurityInfo: &healthinsurancePb.OnsurityInfo{
					Name:        onsurityPolicyPurchaseInfo.Name,
					Gender:      onsurityPolicyPurchaseInfo.Gender,
					PhoneNumber: onsurityPolicyPurchaseInfo.PhoneNumber,
					DateOfBirth: onsurityPolicyPurchaseInfo.DateOfBirth,
				},
			},
		}
	default:
		initiatePolicyPurchaseRes = &healthinsurancePb.InitiatePolicyPurchaseResponse{
			Status: rpc.StatusOkWithDebugMsg("invalid vendor received for getting purchase info"),
		}
	}
	return initiatePolicyPurchaseRes, nil
}

// isNewPolicyPurchaseAllowed checks if a new healthinsurance policy purchase is allowed for actor or not.
// new policy purchase is allowed only if all the following conditions are satisfied
// 1. User should be full salaryprogram active currently.
// 2. User should not have any active health insurance policy.
// 3. There shouldn't be any existing in_progress policy purchase request for the user at policy vendor's end.
func (s *Service) isNewPolicyPurchaseAllowed(ctx context.Context, actorId string) (bool, error) {
	// new policy purchase is allowed only for salaryprogram active users
	isFullSalaryProgramActive, err := s.isUserFullSalaryProgramActiveCurrently(ctx, actorId)
	switch {
	case err != nil:
		return false, fmt.Errorf("error checking if user is salaryprogram active or not, err : %w", err)
	case !isFullSalaryProgramActive:
		logger.Info(ctx, "user is not full salaryprogram active so new policy purchase request is not allowed", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	// new policy purchase is allowed only if no policy is already active for the user
	activePolicies, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, actorId, time.Now())
	switch {
	case err != nil:
		return false, fmt.Errorf("error fetching active policies, err : %w", err)
	case len(activePolicies) != 0:
		// added warn log as ideally this rpc shouldn't be called if user already has an active policy.
		logger.Info(ctx, "user already has an active policy so can't initiate a new policy purchase request", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	// new policy purchase is allowed only if no policy issuance request is already in in_progress state at vendor's end.
	inProgressPolicyIssuanceReqs, _, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, actorId, []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS}, nil, nil, 1, healthinsurancePb.SortOrder_DESC, commonvgpb.Vendor_RISKCOVRY)
	switch {
	case err != nil:
		return false, fmt.Errorf("error fetching in_progress policy issuance requests, err : %w", err)
	case len(inProgressPolicyIssuanceReqs) != 0:
		// added warn log as ideally this rpc shouldn't be called if user already has an active policy.
		logger.Info(ctx, "an existing request is already in in_progress state so can't initiate a new one", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}
	return true, nil
}

// checkAndCreatePolicyIssuanceRequestWithLock checks if a policyIssuanceRequest can be created for the actor after acquiring a distributed lock
// and creates a request (or re-uses an existing one if present).
func (s *Service) checkAndCreatePolicyIssuanceRequestWithLock(ctx context.Context, actorId string, policyType healthinsurancePb.HealthInsurancePolicyType) (*healthinsurancePb.HealthInsurancePolicyIssuanceRequest, error) {
	// acquire actor level distributed lock to prevent race conditions in this flow.
	lockKey := fmt.Sprintf(policyRequestUpdateFlowLockKeyTemplate, actorId)
	lock, err := s.distributedLockManager.GetLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("error aquiring distributed lock, err : %w", err)
	}
	defer func() { _ = lock.Release(epificontext.CloneCtx(ctx)) }()

	// check if no active policy exists for user
	activePolicies, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, actorId, time.Now())
	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching active policies, err : %w", err)
	case len(activePolicies) != 0:
		return nil, fmt.Errorf("user already has an active policy so can't create a new policy issuance request")
	}

	// check if no existing request is in in_progress state at vendor's end
	vendorPurchaseInProgressReqs, _, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, actorId, []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS}, nil, nil, 1, healthinsurancePb.SortOrder_DESC, commonvgpb.Vendor_RISKCOVRY)
	switch {
	case err != nil:
		return nil, fmt.Errorf("error fetching in_progress policy issuance requests, err : %w", err)
	case len(vendorPurchaseInProgressReqs) != 0:
		return nil, fmt.Errorf("an existing request is already in in_progress state so can't initiate a new one")
	}

	// if an existing non-expired request exists in created state, then use that otherwise create a new one.
	exitingReqInCreatedState, _, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, actorId, []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED}, aws.Time(time.Now()), nil, 1, healthinsurancePb.SortOrder_DESC, commonvgpb.Vendor_RISKCOVRY)
	if err != nil {
		return nil, fmt.Errorf("error fetching policy issuance request in created state, err : %w", err)
	}
	if len(exitingReqInCreatedState) != 0 {
		return exitingReqInCreatedState[0], nil
	}

	vendorRequestId := "EPI_" + idgen.RandAlphaNumericString(15)
	newPolicyIssuanceReq, err := s.policyIssuanceReqDao.Create(ctx, &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
		ActorId:          actorId,
		PolicyVendor:     commonvgpb.Vendor_RISKCOVRY,
		VendorRequestId:  vendorRequestId,
		PolicyType:       policyType,
		RequestExpiresAt: timestampPb.New(time.Now().Add(s.conf.HealthInsuranceConfig.NewPolicyIssuanceRequestExpiryDuration)),
		RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED,
	})
	if err != nil {
		return nil, fmt.Errorf("error creating a new policy issuance request, err : %w", err)
	}
	return newPolicyIssuanceReq, nil
}

// GetPolicyIssuanceRequestsForActor rpc is useful to get policy issuance requests for the actor
func (s *Service) GetPolicyIssuanceRequestsForActor(ctx context.Context, req *healthinsurancePb.GetPolicyIssuanceRequestsForActorRequest) (*healthinsurancePb.GetPolicyIssuanceRequestsForActorResponse, error) {
	pageToken, err := pagination.GetPageToken(req.GetPageContext())
	if err != nil {
		logger.Error(ctx, "failed to get page token", zap.Any("filters", req.GetPageContext()), zap.Error(err))
		return &healthinsurancePb.GetPolicyIssuanceRequestsForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	policyIssuanceRequests, pageCtxRes, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, req.GetActorId(), req.GetFilters().GetIssuanceReqStatuses(), nil, pageToken, req.GetPageContext().GetPageSize(), req.GetSortOrder(), commonvgpb.Vendor_VENDOR_UNSPECIFIED)
	if err != nil {
		logger.Error(ctx, "error fetching policy issuance requests from db", zap.Error(err))
		return &healthinsurancePb.GetPolicyIssuanceRequestsForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &healthinsurancePb.GetPolicyIssuanceRequestsForActorResponse{
		Status:                 rpc.StatusOk(),
		PolicyIssuanceRequests: policyIssuanceRequests,
		PageContext:            pageCtxRes,
	}, nil
}

// GetIssuedPoliciesRedirectionInfo rpc is useful to get the info for re-directing the user to vendor web-app for viewing their issued policies.
func (s *Service) GetIssuedPoliciesRedirectionInfo(ctx context.Context, request *healthinsurancePb.IssuedPoliciesRedirectionInfoRequest) (*healthinsurancePb.IssuedPoliciesRedirectionInfoResponse, error) {
	policyDetailsList, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, request.GetActorId(), time.Now())
	if err != nil {
		logger.Error(ctx, "error fetching policy details list", zap.Error(err))
		return &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse{Status: rpc.StatusRecordNotFoundWithDebugMsg(err.Error())}, nil
	}
	vendor := commonvgpb.Vendor_RISKCOVRY
	for _, policy := range policyDetailsList {
		if policy.GetPolicyVendor() == commonvgpb.Vendor_ONSURITY {
			vendor = commonvgpb.Vendor_ONSURITY
			break
		}
	}

	vendorFacImpl, err := s.vendorFactory.Get(vendor)
	if err != nil {
		logger.Error(ctx, "error fetching vendor processor from vendor factory", zap.Error(err))
		return &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// get redirection url
	redirectionUrl, err := vendorFacImpl.GetIssuedPoliciesUrl(ctx, request.GetActorId())
	if err != nil {
		logger.Error(ctx, "error fetching re-direction url from vendor processor", zap.Error(err))
		return &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	res := &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse{
		Status:         rpc.StatusOk(),
		RedirectionUrl: redirectionUrl,
	}
	switch vendor {
	case commonvgpb.Vendor_RISKCOVRY:
		res.VendorBasedRedirectionInfo = &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_RiskcovryRedirectionUrl{
			RiskcovryRedirectionUrl: redirectionUrl,
		}
	case commonvgpb.Vendor_ONSURITY:
		res.VendorBasedRedirectionInfo = &healthinsurancePb.IssuedPoliciesRedirectionInfoResponse_OnsurityRedirectionUrl{
			OnsurityRedirectionUrl: redirectionUrl,
		}
	default:

	}
	return res, nil
}

// CancelPolicyAutoRenewal rpc is useful to stop renewing a policy.
func (s *Service) CancelPolicyAutoRenewal(ctx context.Context, req *healthinsurancePb.CancelPolicyAutoRenewalRequest) (*healthinsurancePb.CancelPolicyAutoRenewalResponse, error) {
	// get policy details by id
	policyDetails, err := s.policyDetailsDao.GetById(ctx, req.GetPolicyDetailsId())
	if err != nil {
		logger.Error(ctx, "error fetching policy details from db", zap.String("policyDetailsId", req.GetPolicyDetailsId()), zap.Error(err))
		return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	policyIssuanceReq, getErr := s.policyIssuanceReqDao.GetById(ctx, policyDetails.GetPolicyIssuanceRequestId())
	if getErr != nil {
		logger.Error(ctx, "error fetching policy issuance request from db", zap.String("policyIssuanceRequestId", policyDetails.GetPolicyIssuanceRequestId()), zap.Error(getErr))
		return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(getErr.Error())}, nil
	}

	vendor, err := s.vendorFactory.Get(policyDetails.GetPolicyVendor())
	if err != nil {
		logger.Error(ctx, "error fetching vendor processor", zap.String("policyDetailsId", policyDetails.GetId()), zap.String(logger.VENDOR, policyDetails.GetPolicyVendor().String()), zap.Error(err))
		return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// cancel policy auto-renewal at vendor's end.
	cancellationResp, cancellationErr := vendor.CancelPolicyAutoRenewal(ctx, policyDetails.GetVendorPolicyId(), policyDetails.GetActorId(), policyIssuanceReq.GetPolicyType())
	if cancellationErr != nil {
		if cancellationResp != nil && cancellationResp.PolicyCancelResponseData() != nil && cancellationResp.PolicyCancelResponseData().GetMeta() != nil &&
			cancellationResp.PolicyCancelResponseData().GetMeta().GetStatusCode() == "OSPI_409" &&
			strings.Contains(cancellationResp.PolicyCancelResponseData().GetMeta().GetResponseMessage(), userPolicyAlreadyCancelledErrorMessage) {
			logger.Info(ctx, "policy is already cancelled, removing entries from DB for actor", zap.String("actorId", policyDetails.GetActorId()), zap.String("policyDetailsId", policyDetails.GetId()), zap.String(logger.VENDOR, policyDetails.GetPolicyVendor().String()))
			return s.removeDbEntriesForAlreadyCancelledPolicies(ctx, policyDetails)
		}
	}

	switch policyDetails.GetPolicyVendor() {
	case commonvgpb.Vendor_ONSURITY:
		policyDetails.PolicyActiveTill = timestampPb.New(time.Now())
		policyDetails.PolicyMetadata.PolicyActiveTill = timestampPb.New(time.Now())
		if updateErr := s.policyDetailsDao.Update(ctx, policyDetails, []healthinsurancePb.HealthInsurancePolicyDetailsFieldMask{healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_ACTIVE_TILL, healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_METADATA}); updateErr != nil {
			logger.Error(ctx, "error updating policy_active_till time in policy_details for onsurity", zap.String("policyDetailsId", policyDetails.GetId()), zap.Error(updateErr))
			return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(updateErr.Error())}, nil
		}
	case commonvgpb.Vendor_RISKCOVRY:
		// get the latest policy_active_till time from vendor and persist in the policy details at our end.
		vendorPolicyDetails, err := vendor.GetPolicyPurchaseStatusAndDetails(ctx, "", policyDetails.GetVendorPolicyId())
		if err != nil {
			logger.Error(ctx, "error fetching policy details from vendor", zap.String("policyDetailsId", policyDetails.GetId()), zap.Error(err))
			return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}

		policyDetails.PolicyActiveTill = vendorPolicyDetails.PolicyMetadata.GetPolicyActiveTill()
		policyDetails.PolicyMetadata.PolicyActiveTill = vendorPolicyDetails.PolicyMetadata.GetPolicyActiveTill()
		if updateErr := s.policyDetailsDao.Update(ctx, policyDetails, []healthinsurancePb.HealthInsurancePolicyDetailsFieldMask{healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_ACTIVE_TILL, healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_METADATA}); updateErr != nil {
			logger.Error(ctx, "error updating policy_active_till time in policy_details for riscovry", zap.String("policyDetailsId", policyDetails.GetId()), zap.Error(updateErr))
			return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(updateErr.Error())}, nil
		}
	default:
		return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("invalid vendor received while cancel insurance policy"))}, nil
	}

	return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) removeDbEntriesForAlreadyCancelledPolicies(ctx context.Context, policyDetails *healthinsurancePb.HealthInsurancePolicyDetails) (*healthinsurancePb.CancelPolicyAutoRenewalResponse, error) {
	policyDetails.PolicyActiveTill = timestampPb.Now()
	policyDetails.PolicyMetadata.PolicyActiveTill = timestampPb.Now()
	if updateErr := s.policyDetailsDao.Update(ctx, policyDetails, []healthinsurancePb.HealthInsurancePolicyDetailsFieldMask{healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_ACTIVE_TILL, healthinsurancePb.HealthInsurancePolicyDetailsFieldMask_POLICY_METADATA}); updateErr != nil {
		logger.Error(ctx, "error updating policy_active_till time in policy_details for onsurity", zap.String("policyDetailsId", policyDetails.GetId()), zap.Error(updateErr))
		return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusInternalWithDebugMsg(updateErr.Error())}, nil
	}
	return &healthinsurancePb.CancelPolicyAutoRenewalResponse{Status: rpc.StatusOk()}, nil
}

// GetIssuedPoliciesForActor rpc is useful to get issued policies for an actor.
func (s *Service) GetIssuedPoliciesForActor(ctx context.Context, req *healthinsurancePb.GetIssuedPoliciesForActorRequest) (*healthinsurancePb.GetIssuedPoliciesForActorResponse, error) {
	policyDetailsList, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, req.GetActorId(), datetime.TimestampToTime(req.GetFilters().GetActiveAtTime()))
	if err != nil {
		logger.Error(ctx, "error fetching policy details list", zap.Error(err))
		return &healthinsurancePb.GetIssuedPoliciesForActorResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	return &healthinsurancePb.GetIssuedPoliciesForActorResponse{
		Status:   rpc.StatusOk(),
		Policies: policyDetailsList,
	}, nil
}

func (s *Service) isUserFullSalaryProgramActiveCurrently(ctx context.Context, actorId string) (bool, error) {
	regStatusRes, err := s.salaryClient.GetCurrentRegStatusAndNextRegStage(ctx, &salaryprogramPb.CurrentRegStatusAndNextRegStageRequest{ActorId: actorId,
		FlowType: salaryprogramPb.SalaryProgramRegistrationFlowType_SALARY_PROGRAM_REGISTRATION_FLOW_TYPE_FULL_SALARY_AND_SALARY_LITE})
	if rpcErr := epifigrpc.RPCError(regStatusRes, err); rpcErr != nil {
		return false, fmt.Errorf("salaryClient.GetCurrentRegStatusAndNextRegStage rpc call failed, err : %w", rpcErr)
	}
	// if user is not registered for salaryprogram then they can't be active so returning false.
	if regStatusRes.GetRegistrationStatus() != salaryprogramPb.SalaryProgramRegistrationStatus_REGISTRATION_STATUS_COMPLETED {
		return false, nil
	}
	registrationId := regStatusRes.GetRegistrationId()

	activationDetailsRes, err := s.salaryClient.GetLatestActivationDetailsActiveAtTime(ctx, &salaryprogramPb.LatestActivationDetailsActiveAtTimeRequest{
		RegistrationId: registrationId,
		ActiveAtTime:   timestampPb.Now(),
		// activation_kind is unspecified as latest activation can be used to determine whether an active salary program exists for the user and its activation type.
		ActivationKind: salaryprogramPb.SalaryProgramActivationKind_SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED,
	})
	if err != nil || !activationDetailsRes.GetStatus().IsSuccess() && !activationDetailsRes.GetStatus().IsRecordNotFound() {
		logger.Error(ctx, "salaryClient.GetLatestActivationDetailsActiveAtTime rpc call failed", zap.Any(logger.RPC_STATUS, activationDetailsRes.GetStatus()), zap.Error(err))
		return false, errors.New("salaryClient.GetLatestActivationDetailsActiveAtTime rpc call failed")
	}
	if activationDetailsRes.GetStatus().IsRecordNotFound() || activationDetailsRes.GetActivationType() != salaryprogramPb.SalaryActivationType_FULL_SALARY_ACTIVATION {
		return false, nil
	}
	return true, nil
}

// ProcessPolicyPurchaseVerificationCallback rpc is useful to process policy purchase verification callbacks, it checks and returns whether a given purchase should be allowed or not,
// purchase verification callback is initiated by the policy vendor just before issuing a policy to re-confirm if a purchase should be allowed or not.
// nolint: funlen
func (s *Service) ProcessPolicyPurchaseVerificationCallback(ctx context.Context, req *healthinsurancePb.PolicyPurchaseVerificationCallbackRequest) (*healthinsurancePb.PolicyPurchaseVerificationCallbackResponse, error) {
	// fetch policy issuance request using vendor and requestId.
	policyIssuanceReq, err := s.policyIssuanceReqDao.GetByPolicyVendorAndVendorRequestId(ctx, req.GetPolicyVendor(), req.GetPolicyIssuanceVendorReqId())
	if err != nil {
		logger.Error(ctx, "error fetching policy issuance request from db", zap.String(logger.VENDOR, req.GetPolicyVendor().String()), zap.String(logger.REQUEST_ID, req.GetPolicyIssuanceVendorReqId()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	// policy verification callback should ONLY be processed if policy issuance request is in CREATED state.
	if policyIssuanceReq.GetRequestStatus() != healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED {
		logger.Error(ctx, "policy issuance request is not in CREATED state", zap.String(logger.REQUEST_ID, req.GetPolicyIssuanceVendorReqId()), zap.String("requestStatus", policyIssuanceReq.GetRequestStatus().String()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("policy issuance request is not in CREATED state")}, nil
	}

	// validate the purchase verification callback payload.
	vendor, err := s.vendorFactory.Get(policyIssuanceReq.GetPolicyVendor())
	if err != nil {
		logger.Error(ctx, "error getting vendor processor", zap.String(logger.VENDOR, policyIssuanceReq.GetPolicyVendor().String()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	if validateErr := vendor.ValidatePurchaseVerificationCallback(ctx, policyIssuanceReq, req.GetRequestMetadata()); validateErr != nil {
		logger.Error(ctx, "error in validating the purchase verification callback", zap.String(logger.VENDOR, req.GetPolicyVendor().String()), zap.String(logger.REQUEST_ID, req.GetPolicyIssuanceVendorReqId()), zap.Error(validateErr))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(validateErr.Error())}, nil
	}

	// check if policy purchase is allowed or not

	// check if user is full salaryprogram active
	isUserFullSalaryProgramActive, err := s.isUserFullSalaryProgramActiveCurrently(ctx, policyIssuanceReq.GetActorId())
	switch {
	case err != nil:
		logger.Error(ctx, "error checking if user is salaryprogram active", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	case !isUserFullSalaryProgramActive:
		logger.Info(ctx, "user is not full salaryprogram active so policy purchase is not allowed", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusOk(), IsPurchaseAllowed: false}, nil
	}

	// acquire actor level distributed lock to prevent race conditions in rest of the flow.
	lockKey := fmt.Sprintf(policyRequestUpdateFlowLockKeyTemplate, policyIssuanceReq.GetActorId())
	lock, err := s.distributedLockManager.GetLock(ctx, lockKey, 5*time.Minute)
	if err != nil {
		return nil, fmt.Errorf("error aquiring distributed lock, err : %w", err)
	}
	defer func() { _ = lock.Release(epificontext.CloneCtx(ctx)) }()

	// check if no policy purchase request is in_progress state at vendor's end.
	inProgressPolicyIssuanceReqs, _, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, policyIssuanceReq.GetActorId(), []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS}, nil, nil, 1, healthinsurancePb.SortOrder_DESC, commonvgpb.Vendor_RISKCOVRY)
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching vendor purchase_in_progress policy issuance requests", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	case len(inProgressPolicyIssuanceReqs) != 0:
		logger.Info(ctx, "a purchase request is already in_progress at vendor's end, so can't allow any new purchase request", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusOk(), IsPurchaseAllowed: false}, nil
	}

	// check if no active policies exist for the user.
	activePolicies, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, policyIssuanceReq.GetActorId(), time.Now())
	switch {
	case err != nil:
		logger.Error(ctx, "error fetching active healthinsurance policies", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()), zap.Error(err))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	case len(activePolicies) != 0:
		logger.Info(ctx, "user already has an active policy so new policy purchase is not allowed", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusOk(), IsPurchaseAllowed: false}, nil
	}

	if policyIssuanceReq.GetRequestExpiresAt().AsTime().Before(time.Now()) {
		logger.WarnWithCtx(ctx, "policy issuance request is expired, so new policy purchase is not allowed", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusOk(), IsPurchaseAllowed: false}, nil
	}

	// update request status to VENDOR_PURCHASE_IN_PROGRESS only if request was in CREATED state before.
	// NOTE : this also helps in ensuring that we only allow successful verification once for a given vendor requestId.
	policyIssuanceReq.RequestStatus = healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_IN_PROGRESS
	if updateErr := s.policyIssuanceReqDao.UpdateWithCurrentRequestStatusCheck(ctx, policyIssuanceReq, []healthinsurancePb.HealthInsurancePolicyIssuanceRequestFieldMask{healthinsurancePb.HealthInsurancePolicyIssuanceRequestFieldMask_REQUEST_STATUS}, healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED, aws.Time(time.Now())); updateErr != nil {
		logger.Error(ctx, "error updating request status to VENDOR_PURCHASE_IN_PROGRESS state", zap.String(logger.ACTOR_ID_V2, policyIssuanceReq.GetActorId()), zap.Error(updateErr))
		return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{Status: rpc.StatusInternalWithDebugMsg(updateErr.Error())}, nil
	}

	// todo (utkarsh) : should we instead publish event before request status update to make sure status is polled from vendor once state is updated ?
	// publish event to poll the status of the policy purchase from vendor after a success verification response is sent to the vendor.
	if _, err := s.pollPolicyPurchaseStatusDelayPublisher.PublishWithDelay(ctx, &consumerPb.PollPolicyPurchaseStatusRequest{
		PolicyVendor:              req.GetPolicyVendor(),
		PolicyIssuanceVendorReqId: req.GetPolicyIssuanceVendorReqId(),
	}, 1*time.Second); err != nil {
		logger.Error(ctx, "error publishing poll policy purchase status event", zap.String(logger.VENDOR, req.GetPolicyVendor().String()), zap.String(logger.REQUEST_ID, req.GetPolicyIssuanceVendorReqId()), zap.Error(err))
	}

	return &healthinsurancePb.PolicyPurchaseVerificationCallbackResponse{
		Status:            rpc.StatusOk(),
		IsPurchaseAllowed: true,
	}, nil
}

func (s *Service) getVendorBasedOnUserEmployerChannelAndPolicyType(ctx context.Context, actorId string, policyType healthinsurancePb.HealthInsurancePolicyType) (commonvgpb.Vendor, error) {
	employerInfo, err := s.userHelperSvc.GetCurrentEmployerInfoOfActor(ctx, actorId)
	if err != nil {
		logger.Error(ctx, "error in getting current employer of user", zap.Error(err))
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("error in getting current employer of user")
	}

	switch employerInfo.GetSalaryProgramChannel() {
	case employmentPb.EmployerSalaryProgramChannel_B2C:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("no policy purchase is not allowed for B2C users")
	case employmentPb.EmployerSalaryProgramChannel_B2B:
		switch policyType {
		case healthinsurancePb.HealthInsurancePolicyType_SUPER_TOP_UP_INSURANCE:
			return commonvgpb.Vendor_RISKCOVRY, nil
		case healthinsurancePb.HealthInsurancePolicyType_BASE_HEALTH_INSURANCE, healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_1A,
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A, healthinsurancePb.HealthInsurancePolicyType_ONSURITY_OPD_WELLNESS_2A2C,
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_DIAMOND_PLUS_1A, healthinsurancePb.HealthInsurancePolicyType_ONSURITY_GHI_GPA_RUBY_1A,
			healthinsurancePb.HealthInsurancePolicyType_ONSURITY_GHI_GPA_OPAL_1A:
			return commonvgpb.Vendor_ONSURITY, nil
		default:
			return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unspecified policy type given to user")
		}
	default:
		return commonvgpb.Vendor_VENDOR_UNSPECIFIED, fmt.Errorf("unspecified vendor for of user employer channel")
	}
}

// nolint:funlen
func (s *Service) IssueNewPolicy(ctx context.Context, request *healthinsurancePb.IssueNewPolicyRequest) (*healthinsurancePb.IssueNewPolicyResponse, error) {
	switch request.GetPolicyVendor() {
	case commonvgpb.Vendor_ONSURITY:
		isReqValid := s.isOnsurityNewPolicyReqValid(request)
		if !isReqValid {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("invalid IssueNewPolicyRequest")}, nil
		}
		isPurchaseAllowed, err := s.isOnsurityNewPolicyPurchaseAllowed(ctx, request.GetActorId())
		if err != nil {
			logger.Error(ctx, "error checking if onsurity policy purchase is allowed or not", zap.String(logger.ACTOR_ID_V2, request.GetActorId()), zap.Error(err))
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
		}
		if !isPurchaseAllowed {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusFailedPreconditionWithDebugMsg("new onsurity policy purchase is not allowed")}, nil
		}
	default:
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("invaid vendor : %v", request.GetPolicyVendor().String()))}, nil

	}

	vendor, err := s.vendorFactory.Get(request.GetPolicyVendor())
	if err != nil {
		logger.Error(ctx, "error getting vendor processor", zap.String(logger.VENDOR, request.GetPolicyVendor().String()), zap.Error(err))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}
	issuedPolicyReqDetails, issueNewPolicyErr := vendor.IssueNewPolicy(ctx, request)

	if issueNewPolicyErr != nil {
		if isUserAlreadyOnboardedWithVendor(issuedPolicyReqDetails) {
			logger.Info(ctx, "user is already onboarded with vendor, creating db entries for already issued policies")
			return s.createDBEntriesForAlreadyIssuedPolicies(ctx, request)
		}
		logger.Error(ctx, "error issuing a new policy", zap.Error(issueNewPolicyErr))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(issueNewPolicyErr.Error())}, nil
	}

	switch request.GetPolicyVendor() {
	case commonvgpb.Vendor_ONSURITY:
		newPolicyIssuanceReq, newPolicyIssuanceReqErr := s.policyIssuanceReqDao.Create(ctx, &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
			ActorId:          request.GetActorId(),
			PolicyVendor:     commonvgpb.Vendor_ONSURITY,
			VendorRequestId:  issuedPolicyReqDetails.OnsurityPolicyIssuanceRequestDetails.PolicyIssuanceRequestData.GetData().GetRequestId(),
			PolicyType:       request.GetPolicyType(),
			RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL,
			RequestExpiresAt: timestampPb.New(time.Now().Add(s.conf.HealthInsuranceConfig.NewPolicyIssuanceRequestExpiryDuration)),
		})
		if newPolicyIssuanceReqErr != nil {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("error creating a new onsurity policy issuance request, err : %v", issueNewPolicyErr.Error()))}, nil
		}

		issuedPolicyDetails, issuedPolicyDetailsErr := vendor.GetIssuedPolicyDetails(ctx, request.GetActorId())
		if issuedPolicyDetailsErr != nil || issuedPolicyDetails.PrimaryMemberUserDetails == nil || issuedPolicyDetails.IssuedPolicyDetails == nil {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(issuedPolicyDetailsErr.Error())}, nil
		}

		userPhoneNumber := strconv.FormatUint(issuedPolicyDetails.PrimaryMemberUserDetails.GetPhoneNumber().GetNationalNumber(), 10)
		primaryMemberDetails := issuedPolicyDetails.IssuedPolicyDetails.GetData().GetMembershipDetails()[userPhoneNumber]
		if primaryMemberDetails == nil || len(primaryMemberDetails.GetPrimaryMemberDataValues()) == 0 {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("primary member details are nil for actor: %v", request.GetActorId()))}, nil
		}

		vendorPolicyId := primaryMemberDetails.GetPrimaryMemberDataValues()[0].GetOshsId()
		policyActiveFrom := timestampPb.New(time.Unix(int64(primaryMemberDetails.GetPrimaryMemberDataValues()[0].GetDateOfJoining()), 0))
		// create policy details entry
		if _, createErr := s.policyDetailsDao.Create(ctx, &healthinsurancePb.HealthInsurancePolicyDetails{
			ActorId:                 request.GetActorId(),
			PolicyVendor:            request.GetPolicyVendor(),
			VendorPolicyId:          vendorPolicyId,
			PolicyIssuanceRequestId: newPolicyIssuanceReq.GetId(),
			PolicyRequestSource:     healthinsurancePb.PolicyRequestSource_REQUEST_SOURCE_FI_APP,
			PolicyActiveFrom:        policyActiveFrom,
			PolicyMetadata: &healthinsurancePb.PolicyMetadata{
				VendorPolicyId:   vendorPolicyId,
				PolicyActiveFrom: policyActiveFrom,
			},
		}); createErr != nil {
			return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(createErr.Error())}, nil
		}
	default:
	}
	return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) createDBEntriesForAlreadyIssuedPolicies(ctx context.Context, request *healthinsurancePb.IssueNewPolicyRequest) (*healthinsurancePb.IssueNewPolicyResponse, error) {
	// get the users who have already issued policies
	// for each user, get the policy details from vendor
	// create policy details entry in our db

	vendor, err := s.vendorFactory.Get(request.GetPolicyVendor())
	if err != nil {
		logger.Error(ctx, "error getting vendor processor", zap.String(logger.VENDOR, request.GetPolicyVendor().String()), zap.Error(err))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(err.Error())}, nil
	}

	issuedPolicyDetails, issuedPolicyDetailsErr := vendor.GetIssuedPolicyDetails(ctx, request.GetActorId())
	if issuedPolicyDetailsErr != nil || issuedPolicyDetails.PrimaryMemberUserDetails == nil || issuedPolicyDetails.IssuedPolicyDetails == nil {
		logger.Error(ctx, "error getting issued policy details", zap.Error(err))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(issuedPolicyDetailsErr.Error())}, nil
	}

	userPhoneNumber := strconv.FormatUint(issuedPolicyDetails.PrimaryMemberUserDetails.GetPhoneNumber().GetNationalNumber(), 10)
	primaryMemberDetails := issuedPolicyDetails.IssuedPolicyDetails.GetData().GetMembershipDetails()[userPhoneNumber]
	if primaryMemberDetails == nil || len(primaryMemberDetails.GetPrimaryMemberDataValues()) == 0 {
		logger.Error(ctx, "error getting primaryMemberDetails", zap.Error(err))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(fmt.Sprintf("primary member details are nil for actor: %v", request.GetActorId()))}, nil
	}

	vendorPolicyId := primaryMemberDetails.GetPrimaryMemberDataValues()[0].GetOshsId()
	policyActiveFrom := timestampPb.New(time.Unix(int64(primaryMemberDetails.GetPrimaryMemberDataValues()[0].GetDateOfJoining()), 0))

	if txnErr := s.txnExecutor.RunTxn(ctx, func(txnCtx context.Context) error {
		newPolicyIssuanceReq, newPolicyIssuanceReqErr := s.policyIssuanceReqDao.Create(txnCtx, &healthinsurancePb.HealthInsurancePolicyIssuanceRequest{
			ActorId:          request.GetActorId(),
			PolicyVendor:     commonvgpb.Vendor_ONSURITY,
			PolicyType:       request.GetPolicyType(),
			RequestStatus:    healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_VENDOR_PURCHASE_SUCCESSFUL,
			RequestExpiresAt: timestampPb.New(time.Now().Add(s.conf.HealthInsuranceConfig.NewPolicyIssuanceRequestExpiryDuration)),
		})
		if newPolicyIssuanceReqErr != nil {
			return fmt.Errorf("error creating a new onsurity policy issuance request, err : %w", newPolicyIssuanceReqErr)
		}
		// create policy details entry
		if _, createErr := s.policyDetailsDao.Create(txnCtx, &healthinsurancePb.HealthInsurancePolicyDetails{
			ActorId:                 request.GetActorId(),
			PolicyVendor:            request.GetPolicyVendor(),
			VendorPolicyId:          vendorPolicyId,
			PolicyIssuanceRequestId: newPolicyIssuanceReq.GetId(),
			PolicyRequestSource:     healthinsurancePb.PolicyRequestSource_POLICY_REQUEST_SOURCE_BACKFILLED_AFTER_USER_ACTIVATED_ON_ONSURITY_NOT_ON_FI_APP,
			PolicyActiveFrom:        policyActiveFrom,
			PolicyMetadata: &healthinsurancePb.PolicyMetadata{
				VendorPolicyId:   vendorPolicyId,
				PolicyActiveFrom: policyActiveFrom,
			},
		}); createErr != nil {
			return fmt.Errorf("error creating policy details entry, err : %w", createErr)
		}
		return nil
	}); txnErr != nil {
		logger.Error(ctx, "error in txn while creating db entries for already issued policies", zap.Error(txnErr))
		return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusInternalWithDebugMsg(txnErr.Error())}, nil
	}
	return &healthinsurancePb.IssueNewPolicyResponse{Status: rpc.StatusOk()}, nil
}

func (s *Service) isOnsurityNewPolicyReqValid(request *healthinsurancePb.IssueNewPolicyRequest) bool {
	return !(request.GetPhoneNumber() == nil || len(request.GetName().String()) == 0 || request.GetGender() == typesPb.Gender_GENDER_UNSPECIFIED || request.GetDateOfBirth() == nil)
}

func (s *Service) isOnsurityNewPolicyPurchaseAllowed(ctx context.Context, actorId string) (bool, error) {
	// new policy purchase is allowed only for salaryprogram active users
	isFullSalaryProgramActive, err := s.isUserFullSalaryProgramActiveCurrently(ctx, actorId)
	switch {
	case err != nil:
		return false, fmt.Errorf("error checking if user is salaryprogram active or not, err : %w", err)
	case !isFullSalaryProgramActive:
		logger.Info(ctx, "user is not full salaryprogram active so new onsurity policy purchase request is not allowed", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	// new policy purchase is allowed only if no policy is already active for the user
	activePolicies, err := s.policyDetailsDao.GetPolicyDetailsList(ctx, actorId, time.Now())
	switch {
	case err != nil:
		return false, fmt.Errorf("error fetching active policies, err : %w", err)
	case len(activePolicies) != 0:
		// added warn log as ideally this rpc shouldn't be called if user already has an active policy.
		logger.Info(ctx, "user already has an active policy so can't initiate a new onsurity policy purchase request", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}

	// new onsurity policy purchase is allowed only if no policy issuance request is already in created state at vendor's end.
	inProgressPolicyIssuanceReqs, _, err := s.policyIssuanceReqDao.GetPolicyIssuanceRequestsPaginated(ctx, actorId, []healthinsurancePb.PolicyIssuanceRequestStatus{healthinsurancePb.PolicyIssuanceRequestStatus_REQUEST_STATUS_CREATED}, nil, nil, 1, healthinsurancePb.SortOrder_DESC, commonvgpb.Vendor_ONSURITY)
	switch {
	case err != nil && err != epifierrors.ErrRecordNotFound:
		return false, fmt.Errorf("error fetching created onsurity policy issuance requests, err : %w", err)
	case len(inProgressPolicyIssuanceReqs) != 0:
		// added warn log as ideally this rpc shouldn't be called if user already has an active policy.
		logger.Info(ctx, "an existing request is already in created state so can't initiate a new onsurity policy", zap.String(logger.ACTOR_ID_V2, actorId))
		return false, nil
	}
	return true, nil
}

func isUserAlreadyOnboardedWithVendor(issuedPolicyReqDetails *insurancevendor.NewPolicyIssuanceRequestDetails) bool {
	if issuedPolicyReqDetails == nil {
		return false
	}

	if len(issuedPolicyReqDetails.OnsurityPolicyIssuanceRequestDetails.PolicyIssuanceRequestData.GetMeta().GetErrors()) == 0 {
		return false
	}

	errorMessage := issuedPolicyReqDetails.OnsurityPolicyIssuanceRequestDetails.PolicyIssuanceRequestData.GetMeta().GetErrors()[0].GetMessage()
	// fix for issue where user onboarding is already done on Onsurity with different status code for success and DB entries not getting created for our system
	if strings.Contains(errorMessage, userOnboardingAlreadyDoneErrorMessage) || strings.Contains(errorMessage, userOnboardingAlreadyDoneWithDifferentCredentialsErrorMessage) {
		return true
	}

	return false
}
